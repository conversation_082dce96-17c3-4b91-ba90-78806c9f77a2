﻿<UserControl xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:dxrt="http://schemas.devexpress.com/winfx/2008/xaml/ribbon/themekeys"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm"
             xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
             xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
             xmlns:dxnt="http://schemas.devexpress.com/winfx/2008/xaml/navbar/themekeys"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
             xmlns:dxn="http://schemas.devexpress.com/winfx/2008/xaml/navbar"
             xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"             
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"  
             x:Class="ServoStudio.Views.SoftwareErrorLogView"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type={x:Type ViewModels:SoftwareErrorLogViewModel}}"
             d:DesignHeight="600" d:DesignWidth="900">
    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" CustomNotificationPosition="BottomRight" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields"/>
        <dxmvvm:EventToCommand Command="{Binding SoftwareErrorLogLoadedCommand}"/>
    </dxmvvm:Interaction.Behaviors>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <Grid Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Label Grid.Column="0" Content="起始日期" Style="{StaticResource LabelStyle}" Margin="12,5,5,0"/>
            <dxe:DateEdit Grid.Column="1" Margin="2,5,0,0" Width="140" EditValue="{Binding BeginningDate}" MaskUseAsDisplayFormat="True">
                <dxe:DateEdit.StyleSettings>
                    <dxe:DateEditCalendarStyleSettings/>
                </dxe:DateEdit.StyleSettings>
            </dxe:DateEdit>

            <Label Grid.Column="2" Content="截止日期" Style="{StaticResource LabelStyle}" Margin="12,5,5,0"/>
            <dxe:DateEdit Grid.Column="3" Margin="2,5,0,0" Width="140" EditValue="{Binding EndingDate}" MaskUseAsDisplayFormat="True">
                <dxe:DateEdit.StyleSettings>
                    <dxe:DateEditCalendarStyleSettings/>
                </dxe:DateEdit.StyleSettings>
            </dxe:DateEdit>

            <dx:SimpleButton Grid.Column="4" Command="{Binding GetSoftwareErrorDataCommand}"  Style="{StaticResource ButtonStyle}" Width="80" Margin="12,5,0,0" Glyph="{dx:DXImage Image=MarqueeZoom_16x16.png}">
                <Label Content="查  询" Style="{StaticResource LabelStyle}" Margin="0"/>
            </dx:SimpleButton>
        </Grid>

        <dxg:GridControl Name="gridControl" Grid.Row="1" Margin="5" SelectionMode="Cell" AutoGenerateColumns="AddNew" ItemsSource="{Binding SoftwareErrorLog}" EnableSmartColumnsGeneration="True" >
            <dxg:GridControl.View>
                <dxg:TableView AllowEditing="False" AutoWidth="False">
                    <dxg:TableView.FormatConditions>
                        <dxg:FormatCondition Expression="[Level] = '1'" FieldName="Level">
                            <dx:Format Background="#FFF1F743"/>
                        </dxg:FormatCondition>
                        <dxg:FormatCondition Expression="[Level] = '2'" FieldName="Level">
                            <dx:Format Background="#FFFC973F"/>
                        </dxg:FormatCondition>
                        <dxg:FormatCondition Expression="[Level] = '3'" FieldName="Level">
                            <dx:Format Background="#FFFF2200"/>
                        </dxg:FormatCondition>
                    </dxg:TableView.FormatConditions>
                </dxg:TableView>
            </dxg:GridControl.View>
          
            <dxg:GridColumn FieldName="Code" Header="错误码" IsSmart="True" Width="0.7*"/>
            <dxg:GridColumn FieldName="Function" Header="异常函数" IsSmart="True" Width="1.6*"/>
            <dxg:GridColumn FieldName="Content" Header="异常内容" IsSmart="True" Width="3.4*"/>
            <dxg:GridColumn FieldName="Level" Header="错误等级" IsSmart="True" Width="0.7*"/>
            <dxg:GridColumn FieldName="DateTime" Header="时间" IsSmart="True" Width="1.2*"/>
        </dxg:GridControl>
    </Grid>
</UserControl>