﻿using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace ServoStudio.GlobalPthread
{
    public class MicrosecondsOscilloscopeDrawingPthread
    {
        #region Kernel32 方法声明
        [DllImport("Kernel32.dll")]
        private static extern bool QueryPerformanceCounter(out long lpPerformanceCount);//获取当前系统性能计数
        [DllImport("Kernel32.dll")]
        private static extern bool QueryPerformanceFrequency(out long lpFrequency);//获取当前系统性能频率
        [DllImport("kernel32.dll")]
        private static extern UIntPtr SetThreadAffinityMask(IntPtr hThread, UIntPtr dwThreadAffinityMask);//指定某一特定线程运行在指定的CPU核心
        [DllImport("kernel32.dll")]
        private static extern IntPtr GetCurrentThread();//获取当前线程的Handler
        #endregion

        #region 事件
        public event CheckAllThreadClosed evtCheckAllThreadClosed;
        #endregion

        #region 属性
        public bool PthreadSwitch { get; set; }//是否正在运行定时器
        public bool PthreadPause { get; set; }//是否中断
        public bool PthreadDispose { get; set; }//是否销毁定时器
        public bool PthreadWorking { get; set; }//是否工作中
        public int PthreadCallBackTimes { get; set; }//线程CallBack次数
        #endregion

        #region 字段
        /// <param name="sender">事件的发起者，即定时器对象</param>
        /// <param name="JumpPeriod">上次调用和本次调用跳跃的周期数</param>
        /// <param name="interval">上次调用和本次调用之间的间隔时间（微秒）</param>
        public delegate void OnTickHandle(object sender, long JumpPeriod, long interval);//定时器事件的委托定义
        private OnTickHandle _tick = null;//回调函数定义      
        private int _points = 0;//需要采样的个数     
        private int _delay = 0;//首次启动延时（微秒）
        private long _period = 0;//定时器周期（微秒）
        private byte _cpuIndex = 1;//定时器运行时独占的CPU核心索引序号
        private long _freq = 0;//系统性能计数频率（每秒）
        private long _freqmms = 0;//系统性能计数频率（每微秒）
        #endregion

        //*************************************************************************
        //函数名称：Run
        //函数功能：线程运行
        //
        //输入参数：object obj   线程池传输参数
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.21&2023.04.19
        //*************************************************************************
        public void Run(object obj)
        {
            try
            {
                Initializer();

                Initializer_ForFaultAcquisition();//由Lilbert于2023.04.19添加故障数据采集初始化

                RunTimer();
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PTHREAD_MICROSECONDS_OSCILLOSCOPE_DRAWING, "MicrosecondsOscilloscopeDrawingPthread.Run", ex);
            }
            finally
            {
                PthreadWorking = false;

                if (OthersHelper.CheckIsAllThreadClosed() && evtCheckAllThreadClosed != null)
                {
                    evtCheckAllThreadClosed();
                }                 
             }
        }

        //*************************************************************************
        //函数名称：Initializer()
        //函数功能：时钟信息初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.02
        //*************************************************************************
        private void Initializer()
        {
            try
            {
                long lFrequency = 0;
                QueryPerformanceFrequency(out lFrequency);

                if (lFrequency > 0)
                {
                    _points = 20000;
                    _freq = lFrequency;
                    _freqmms = lFrequency / 1000000;
                    _tick = ViewModelSet.OscilloscopeView.TimerCallBack;
                    _delay = 100;
                    _period = 20000;
                    _cpuIndex = Oscilloscope.CUPCore;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MICROSECONDHELPER_CONSTRUCTOR, "MicrosecondHelper", ex);
            }
        }

        //*************************************************************************
        //函数名称：Initializer_ForFaultAcquisition()
        //函数功能：时钟信息初始化-故障数据采集
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.19
        //*************************************************************************
        private void Initializer_ForFaultAcquisition()
        {
            try
            {
                long lFrequency = 0;
                QueryPerformanceFrequency(out lFrequency);

                if (lFrequency > 0)
                {
                    _points = 20000;
                    _freq = lFrequency;
                    _freqmms = lFrequency / 1000000;
                    _tick = ViewModelSet.FaultDataOscilloscopeView.TimerCallBack;
                    _delay = 100;
                    _period = 20000;
                    _cpuIndex = FaultDataOscilloscope.CUPCore;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MICROSECONDHELPER_CONSTRUCTOR, "MicrosecondHelper", ex);
            }
        }

        //*************************************************************************
        //函数名称：RunTimer
        //函数功能：时钟运行
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.02
        //*************************************************************************
        private void RunTimer()
        {
            int iPoints = 0;//采样个数
            long q1, q2;

            try
            {
                if (_cpuIndex != 0)
                {
                    UIntPtr up = SetThreadAffinityMask(GetCurrentThread(), new UIntPtr(GetCpuID(_cpuIndex)));
                    if (up == UIntPtr.Zero) return; //为定时器分配CPU核心时失败
                }

                QueryPerformanceCounter(out q1);
                QueryPerformanceCounter(out q2);

                if (_delay > 0)
                {
                    while (q2 < q1 + _delay * _freqmms)
                    {
                        QueryPerformanceCounter(out q2);
                    }
                }

                QueryPerformanceCounter(out q1);
                QueryPerformanceCounter(out q2);

                while (PthreadSwitch)
                {
                    if (!PthreadPause)
                    {
                        QueryPerformanceCounter(out q2);

                        if (q2 > q1 + _freqmms * _period)
                        {
                            if ((!PthreadDispose) && (iPoints <= _points))
                            {
                                _tick(this, (q2 - q1) / (_freqmms * _period), (q2 - q1) / _freqmms);
                            }
                            else
                            {
                                return;
                            }

                            q1 = q2;
                            iPoints++;
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MICROSECONDHELPER_RUN_TIMER, "MicrosecondHelper.RunTimer", ex);
            }
            finally
            {
                //回调函数，告知前台数采完毕
                //if (evtThreadAbortHint != null)
                //    evtThreadAbortHint();
            }
        }

        //*************************************************************************
        //函数名称：GetCpuID
        //函数功能：根据CPU的索引序号获取CPU的标识序号
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.02
        //*************************************************************************
        private ulong GetCpuID(int idx)
        {
            ulong cpuid = 0;

            if (idx < 0 || idx >= System.Environment.ProcessorCount)
            {
                idx = 0;
            }

            cpuid |= 1UL << idx;

            return cpuid;
        }
    }
}
