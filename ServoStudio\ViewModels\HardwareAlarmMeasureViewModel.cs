﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Windows.Threading;
using System.Collections.Generic;
using System.Linq;
using ServoStudio.Models;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class HardwareAlarmMeasureViewModel
    {
        #region 属性
        public virtual ObservableCollection<MeasureSet> HardwareAlarmMeasure { get; set; }//报警集合   
        #endregion

        #region 构造函数
        public HardwareAlarmMeasureViewModel()
        {
            ViewModelSet.HardwareAlarmMeasure = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.HARDWAREALARMMEASURE;
        }
        #endregion

        #region 方法
        //*************************************************************************
        //函数名称：HardwareAlarmMeasureLoaded
        //函数功能：载入
        //
        //输入参数：
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.05
        //*************************************************************************
        public void HardwareAlarmMeasureLoaded()
        {
            HardwareAlarmMeasure = new ObservableCollection<MeasureSet>();

            for (int i = 0; i < GlobalParameterSet.dt_HardwareExplanation.Rows.Count; i++)
            {
                MeasureSet measureSet = new MeasureSet();
                measureSet.Content = GlobalParameterSet.dt_HardwareExplanation.Rows[i]["Content"].ToString();
                measureSet.Reason = Convert.ToString(GlobalParameterSet.dt_HardwareExplanation.Rows[i]["Reason"]).Replace("\n", " ");
                measureSet.Measure = Convert.ToString(GlobalParameterSet.dt_HardwareExplanation.Rows[i]["Measure"]);

                measureSet.Code = Convert.ToString(GlobalParameterSet.dt_HardwareExplanation.Rows[i]["Code"]).Replace("0x", "");
                if (measureSet.Code == "0000")
                {
                    continue;
                }

                switch (GlobalParameterSet.dt_HardwareExplanation.Rows[i]["Level"].ToString())
                {
                    case "3":
                        measureSet.Level = "高级";
                        break;
                    case "2":
                        measureSet.Level = "中级";
                        break;
                    case "1":
                        measureSet.Level = "低级";
                        break;
                    default:
                        break;
                }

                HardwareAlarmMeasure.Add(measureSet);
            }
        }          
        #endregion
    }

    public class MeasureSet
    {
        public string Code { get; set; }//故障编号
        public string Content { get; set; }//故障内容
        public string Level { get; set; }//故障等级
        public string Reason { get; set; }//故障原因
        public string Measure { get; set; }//解除故障措施
    }
}