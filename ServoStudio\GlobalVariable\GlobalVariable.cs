﻿using ServoStudio.GlobalMethod;
using ServoStudio.GlobalPthread;
using ServoStudio.Models;
using ServoStudio.ViewModels;
using ServoStudio.Views;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServoStudio
{
    //委托
    public delegate int ClosingHint();//关闭系统提示
    public delegate void EvabluationParamterReadAndWrite(string strName);//赋值参数读写
    public delegate void GetOtherViewModelProperty();//获取其他ViewModel属性值
    public delegate void EvaluationParamterReadAndWrite(string strName);//赋值参数读写
    public delegate void EvaluationTestCommunication(string strReceiving);//赋值回送测试
    public delegate void EvaluationSerialPortWorkProcess(string strProcess);//串口工作状态进展
    public delegate void EvaluationSerialPortConnectState(string strState);//串口连接状态
    public delegate void CheckAllThreadClosed();//判断线程是否关闭
    public delegate void ShowNotification(int iType);//信息提示
    public delegate void ControlEnabled();//控件使能
    public delegate void EvaluationHardwareAlarm();//赋值硬件报警
    public delegate void EvaluationAxisAddress(string strAddress);//赋值轴地址
    public delegate void EvaluationSystemStatus(string strStatus);//赋值系统状态
    public delegate void GetMotorParameterIdentification();//获取电机识别参数
    public delegate void CheckIdentificationComplete();//电机识别完成


    //软件当前状态参数集合
    public static class SoftwareStateParameterSet
    {
        public static bool IsRetransmission = false;//是否重传     //由Lilbert于2023.04.13添加重传机制
        public static bool IsFaultUpload = false;//是否故障上传     //由Lilbert于2023.04.13添加重传机制
        public static bool IsClearAcquisitionFault = false;//是否清除故障数据采集     //由Lilbert于2023.04.18添加

        public static int OscilloscopeTabControlIndex;//示波器波形界面TabControl检索号
        public static bool IsConnected;//串口通信是否连接
        public static bool CloseConnectionFlag = false;//串口是否关闭
        public static bool OpenConnectionFlag = false;//串口是否打开
        public static bool IsFirstEchoTest;//是否连接后初次回送测试
        public static int DisconnectionTimes;//串口中断次数
        public static string CurrentPageName;//当前页面名称
        public static string ImportConfigurationAddress;//当前展开的配置文件地址  
        public static string ServoStatus;//伺服当前状态  
        public static List<byte> lstMessage = new List<byte>();//接收数据

        //public static string StationID;//从站ID
        public static string SlaveID;//从站ID
        public static string AxisID;//转轴ID
        //public static string LastStationID;//上一次从站ID
        public static string LastSlaveID;//上一次从站ID
        public static string LastAxisID;//上一次轴ID

        public static int iBatchIndex = 0;//参数批处理写入集合索引号

        public static int iBatchIndex_ForImportConfig = 0;//参数批处理写入集合索引号,为参数导入        

        public static List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo = new List<List<TransmitingDataInfoSet>>();//参数批处理写入集合   
        public static List<List<TransmitingDataInfoSet>> lstReceivingDataInfo = new List<List<TransmitingDataInfoSet>>();//参数批处理读取集合  

        public static List<string> SelectedDIFunction = new List<string>();//选中的DI功能集合   

        public static bool IsAlarmAutoExpand = true;//是否报警自动弹出
        public static bool IsOneKeyShortCut = false;//是否设置一键快捷键
        public static bool PutAcquisitionStateBottom = false;

        public static int AxisIndex = 0;//轴地址索引号
        public static List<AxisSet> lstAxisInfo = new List<AxisSet>();//轴地址集合

        public static Dictionary<string, string> dicAcquisitionUint = new Dictionary<string, string>();//采样单位字典
        public static Dictionary<string, string> dicAcquisitionUint_FaultAcquisition = new Dictionary<string, string>();//采样单位字典_故障采集     由Lilbert于2023.04.26添加

        public static string ServoSoftwareVersion;//伺服软件版本号
        public static string ServoHardwareVersion;//伺服硬件版本号
        public static string ServoName;//伺服名称  

        public static string ServoSoftwareVersion_For_Excel;//伺服软件版本号   
        public static string ServoHardwareVersion_For_Excel;//伺服软件版本号 
        public static string ServoNameVersion_For_Excel;//伺服软件版本号 

        public static string ABSEncoderSingleTurnBit;//编码器单圈分辨率
    }

    //固件升级集合
    public static class FirmwareUpdateSet
    {
        public static bool IsUpdate;//固件升级标志位
        public static string Process;//固件升级进程

        public static string FileNameAA = "Servo_Axis_H743";       //由Lilbert添加ST芯片文件名称
        public static string FileNameBB = "servo_6axis_rzt";       //由Lilbert添加瑞萨芯片文件名称
        public static string ServoNameAA = "RS60E";               //由Lilbert添加ST芯片文件名称对应伺服名称
        public static string ServoNameBB = "Black Tiger";       //由Lilbert添加瑞萨芯片文件名称对应伺服名称

        public static string FileName;//文件名称
        public static int FileSize;//文件大小
        public static byte[] FileContent;//文件内容

        public static int Offset;//数组地址索引
        public static int PackageNumber;//数据包个数
        public static int DataSize;//数据大小

        public static StringBuilder ProcessNotification = new StringBuilder();//升级进程提示

        public static List<int> ARM = new List<int>();//需要升级的ARM编号集合
        public static int ARMIndex;//ARM编号

        public static int ReceivingTimes;//接收次数
        public static int SendingAgainTimes;//重新发送次数        
    }

    //当前单位信息集合
    public static class CurrentUnit
    {
        public static bool bInitialized;//是否获取初始配置文件单位
        public static string EncodeType;//编码器类型

        public static string Position;//位置单位
        public static string Torque;//转矩单位
        public static string Speed;//速度单位
        public static string Acceleration;//加速度单位

        public static List<UnitExchangedSet> ListAbsolute = new List<UnitExchangedSet>();//绝对式
        public static List<UnitExchangedSet> ListIncremental = new List<UnitExchangedSet>();//增量式
    }

    //选中的单位信息集合
    public static class SelectUnit
    {
        public static string Position;//位置单位
        public static string Torque;//转矩单位
        public static string Speed;//速度单位
        public static string Acceleration;//加速度单位
    }

    //单位换算集合
    public class UnitExchangedSet
    {
        public string Content;
        public double Value;
    }

    //控制字集合
    public static class ControlWordSet
    {
        public static bool WriteSwitch = false;//是否写入控制字
        public static List<int> ListValue = new List<int>();//写入的值
        public static int IndexOfList = 0;//List的检索号
        public static string TaskName;//任务名称
        public static bool IsExistTaskAfterControlWord = false;//控制字后是否有新的任务
    }

    //示波器采样信息集合
    public static class AcquisitionInfoSet
    {
        public static bool AcquisitionSwitch;//采样开关
        public static bool IsExistTask;//当前是否有采样任务
        public static string CurrentProcess;//当前采样任务进展-数采任务下达、采集状态询问、数据上传、采集停止

        public static bool IsContinuous;//是否连续采样
        public static int ContinuousAcquisitionTimes = 0;//连续采样次数
        public static bool IsDrawingCompleted = true;//连续采样画图是否完成
        public static int OscilloscopeDisplayMethod;//连续采样示波器展示方式

        // 通道映射列表，用于解决数据错位问题
        // 存储按ID排序后的UI通道索引，与硬件返回数据顺序对应
        public static List<int> lstChannelMapping = new List<int>();

        // UI通道到数据索引的映射，用于UI显示时获取正确的单位信息
        // 例如：UI通道1对应数据索引2，则lstUiToDataMapping[0] = 2
        public static List<int> lstUiToDataMapping = new List<int> { -1, -1, -1, -1 };

        public static List<string> lstChannel = new List<string>();//选中的通道
        public static int ChannelNumberOfCurrentMessage;//当前上传数据的通道数
        public static int CurrentMessageNumber;//当前是第几帧
        public static List<List<int>> lstReceiving = new List<List<int>>();//接收数据上传集合
        public static List<string> lstUnit = new List<string>();//选中的采样项目单位
        public static List<double> lstExchangeValue = new List<double>();//单位转换系数
    }

    //示波器采样数据
    public static class AcquisitionData
    {
        public static List<int> Channel1 = new List<int>();
        public static List<int> Channel2 = new List<int>();
        public static List<int> Channel3 = new List<int>();
        public static List<int> Channel4 = new List<int>();
    }

    //故障数据采样信息集合
    public static class FaultAcquisitionInfoSet
    {
        public static bool AcquisitionSwitch;//采样开关
        public static bool IsExistTask;//当前是否有采样任务
        public static string CurrentProcess;//当前采样任务进展-数采任务下达、采集状态询问、数据上传、采集停止

        public static bool IsContinuous;//是否连续采样
        public static int ContinuousAcquisitionTimes = 0;//连续采样次数 
        public static bool IsDrawingCompleted = true;//连续采样画图是否完成
        public static int OscilloscopeDisplayMethod;//连续采样示波器展示方式

        public static List<string> lstChannel = new List<string>();//选中的通道
        public static int ChannelNumberOfCurrentMessage;//当前上传数据的通道数
        public static int CurrentMessageNumber;//当前是第几帧
        public static List<List<int>> lstReceiving = new List<List<int>>();//接收数据上传集合
        public static List<string> lstUnit = new List<string>();//选中的采样项目单位
        public static List<double> lstExchangeValue = new List<double>();//单位转换系数
    }

    //故障数据采样数据
    public static class FaultAcquisitionData
    {
        public static List<int> Channel1 = new List<int>();
        public static List<int> Channel2 = new List<int>();
        public static List<int> Channel3 = new List<int>();
        public static List<int> Channel4 = new List<int>();
        public static List<int> Channel5 = new List<int>();
        public static List<int> Channel6 = new List<int>();
        public static List<int> Channel7 = new List<int>();
        public static List<int> Channel8 = new List<int>();
    }

    //当前通信信息集合
    public static class CommunicationSet
    {
        public static SerialPort SerialPortInfo = new SerialPort();//串口信息

        //public static string StationID;//从站ID  
        public static string SlaveID;//从站ID
        public static string AxisID;//转轴ID

        public static byte[] Transmiting;//发送帧
        public static byte[] Receiving;//接收帧
        public static string TaskName;//任务名称            
        public static string FunctionCode;//功能码
        public static string AcquisitionExecutedCode;//数据采集执行事项
        public static string CurrentPageName;//当前页面名称

        public static List<TransmitingDataInfoSet> TransmittingDataInfo = new List<TransmitingDataInfoSet>();// 发送数据信息集合
        public static Dictionary<string, string> CurrentPointValue_AxisA = new Dictionary<string, string>();//当前点的数值
        public static Dictionary<string, string> CurrentPointValue_AxisB = new Dictionary<string, string>();//当前点的数值
        public static List<HardwareAlarm> HardwareAlarmValue = new List<HardwareAlarm>();//硬件报警数值
    }

    //串口任务全局集合
    public static class SerialPortTask
    {
        public static List<TaskManagementSet> TaskManagement = new List<TaskManagementSet>();
    }

    //串口任务管理集合
    public class TaskManagementSet
    {
        public int ExecutionState = 0;//0：没有执行，1：正在执行，2：执行完成
        public string TaskName;//任务名称
        //public string StationID;//从站ID 
        public string SlaveID;//从站ID 
        public string AxisID;//转轴ID      
        public string FunctionCode;//功能码
        public string AcquisitionExecutedCode;//数据采集执行事项 01：采集，02：问询，03：上传，04：停止
        public int ExecuteTimes = 0;//执行次数
        public string CurrentPageName;//当前页面名称
        public List<TransmitingDataInfoSet> TransmittingDataInfo = new List<TransmitingDataInfoSet>();//发送数据信息集合
    }

    public static class ConfigServo
    {
        public static int SelectConfigID;
        public static string SelectConfigServoName;
        public static int SelectConfigSlaveID;
        public static int SelectConfigAxisID;
        public static string SelectConfigParameter;

        public static List<ServoConfigsModel> ServoConfigs = new List<ServoConfigsModel>();//伺服配置信息集合
        public static List<string> ConfigServoName = new List<string>();//配置伺服名称集合
        public static List<ServoConfigsModel> SelectServoConfigs = new List<ServoConfigsModel>();//选择的伺服配置信息集合
    }

    // 发送的数据信息集合
    public class TransmitingDataInfoSet
    {
        public string Address;//地址
        public string DataType;//数据类型
        public string Content;//发送的内容
        public string Value;//对应地址的数值
    }

    //轴信息集合
    public class AxisSet
    {
        public string StationID;
        public string SlaveID;
        public string AxisID;
    }

    //ARM对应的Axis状态
    public static class ARMStatus
    {
        public static string AxisValueA;
        public static string AxisValueB;

        public static bool IsFirmUpdate;
    }

    //硬件故障集合
    public class HardwareAlarm
    {
        public string Index;
        public UInt32 DateTime;
    }

    //窗体集合
    public static class WindowSet
    {
        public static MainWindow clsMainWindow;
        public static FirmwareUpdateView clsFirmwareUpdate;     
    }

    //Jog集合
    public static class JogSet
    {
        public static string Direction;
        public static bool IsContinuous;
    }

    public static class JogDirectionSet
    {
        public static string Direction;
        public static bool IsContinuous;
    }

    public static class ProgramJogSet
    {
        public static string Direction;
        public static bool IsContinuous;
    }

    public static class OfflineInertiaIdentificationSet
    {
        public static bool IsContinuous;
        public static bool IsModified;//是否替换负载惯量比
    }

    public static class MotorParameterIdentificationSet
    {
        public static string WindingResistance;//额定功率     
        public static string WindingInductance;//额定功率     
        public static string LineUVWSequence;//额定功率    
        public static string AbsEncoderSingleTurnBit;//多圈值分辨率位数   
        public static string MotorPolePairsNumber;//极对数  
        public static string AbsEncoderOffset;//绝对式编码器偏置
        public static bool IsModified;//是否替换   
        public static bool IsCallBackOn;//监听回调开关
        public static bool IsIndentification; //是否完成参数辨识     
    }

    public static class MotorParameterAutoLearnIdentificationSet
    {
        public static string MotorWindingResistance;//电机电阻     
        public static string MotorInductanceLd;//D轴电机电感 
        public static string MotorInductanceLq;//Q轴电机电感    
        public static string LineUVWSequence;//电机动力线相序    
        public static string AbsEncoderSingleTurnBit;//单圈值分辨率位数   
        public static string MotorPolePairsNumber;//极对数  
        public static string AbsEncoderOffset;//绝对式编码器偏置
        public static bool IsModified_AutoLearn;//是否替换   
        public static bool IsCallBackOn_AutoLearn;//监听回调开关
        public static bool IsIndentification_AutoLearn; //是否完成参数辨识 

        public static bool IsCallBackOn_MotorLineUVWSequenceIdentification;//电机动力线相序辨识监听回调开关
        public static bool IsIndentification_MotorLineUVWSequenceIdentification; //电机动力线相序辨识是否完成参数辨识 

        public static bool IsCallBackOn_AbsEncoderOffsetIdentification;//绝对值编码器偏置辨识监听回调开关
        public static bool IsIndentification_AbsEncoderOffsetIdentification; //绝对值编码器偏置辨识是否完成参数辨识     
    }

    public static class MotorParameterMotorLineUVWSequenceAbsEncoderOffsetIdentificationSet
    {
        public static string MotorWindingResistance;//电机电阻     
        public static string MotorInductanceLd;//D轴电机电感 
        public static string MotorInductanceLq;//Q轴电机电感    
        //public static string MotorLineUVWSequence;//电机动力线相序
        public static string SelectedMotorLineUVWSequenceType;//电机动力线相序    
        public static string AbsEncoderSingleTurnBit;//单圈值分辨率位数   
        public static string MotorPolePairsNumber;//极对数  
        public static string AbsEncoderOffset;//绝对式编码器偏置
        public static bool IsModified_AutoLearn;//是否替换   
        public static bool IsCallBackOn_AutoLearn;//监听回调开关
        public static bool IsIndentification_AutoLearn; //是否完成参数辨识 

        public static bool IsCallBackOn_MotorLineUVWSequenceIdentification;//电机动力线相序辨识监听回调开关
        public static bool IsIndentification_MotorLineUVWSequenceIdentification; //电机动力线相序辨识是否完成参数辨识 

        public static bool IsCallBackOn_AbsEncoderOffsetIdentification;//绝对值编码器偏置辨识监听回调开关
        public static bool IsIndentification_AbsEncoderOffsetIdentification; //绝对值编码器偏置辨识是否完成参数辨识     
    }

    public static class MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet
    {
        public static string InertiaIdetificationStartValue;//电机负载惯量辨识起始值     
        public static string LoadInertiaRatio;//负载惯量比 

        //public static string AutoTunningMode;//自整定应用模式  
        public static string SelectedAutoTunningModeType;//选择的自整定应用模式 
        public static string AutoTunningDistance;//自整定移动距离    
        public static string SelectedAutoTunningRigidityLevelType;//自整定刚性等级   
        public static string PositionCompletionWidth;//定位完成宽度  

        public static string PositionLoopGain;//位置环增益 
        public static string SpeedLoopGain;//速度环增益    
        public static string SpeedLoopTimeConstant;//速度环积分时间常数   
        public static string FirstTrqcmdFilterTime;//第一转矩指令滤波时间参数
        public static string SelectedVibrationSuppressionOption;//A型抑振控制选择 
        public static string VibsupFreq;//A型抑振频率    
        public static string VibsupGainComp;//A型抑振增益补偿   
        public static string VibsupDampingGain;//A型抑振阻尼增益 
        public static string SelectedModelFollowingControlSwitch;//模型追踪控制开关 
        public static string ModelFollowingControlGain;//模型追踪控制增益    
        public static string MFCGainCorrection;//模型追踪控制增益补偿   
        public static string VibrationSuppressionFrequencyA;//振动抑制1频率A         

        public static bool IsCallBackOn_InertiaIdentification;//电机负载惯量辨识监听回调开关
        public static bool IsIndentification_InertiaIdentification; //电机负载惯量辨识是否完成参数辨识 

        public static bool IsCallBackOn_ParameterSelfTunning;//参数自整定辨识监听回调开关
        public static bool IsIndentification_ParameterSelfTunning; //参数自整定辨识是否完成参数辨识     
    }

    //ViewModel集合
    public static class ViewModelSet
    {
        public static CurrentLoopViewModel CurrentLoop;
        public static DigitalIOViewModel Digital;
        public static LimitAmplitudeViewModel LimitAmplitude;
        public static MotorFeedbackViewModel MotorFeedback;
        public static MotorFeedbackAutoLearnViewModel MotorFeedbackAutoLearn;
        public static MotorLineUVWSequenceAbsEncoderOffsetViewModel MotorLineUVWSequenceAbsEncoderOffset;
        public static InertiaIdentificationParameterSelfTunningViewModel InertiaIdentificationParameterSelfTunning;
        public static OscilloscopeViewModel Oscilloscope;
        public static OscilloscopeView OscilloscopeView;
        public static FaultDataOscilloscopeViewModel FaultDataOscilloscope;
        public static FaultDataOscilloscopeView FaultDataOscilloscopeView;
        public static PositionLoopViewModel PositionLoop;
        public static SeekZeroViewModel SeekZero;
        public static SpeedLoopViewModel SpeedLoop;
        public static MainWindowViewModel Main;       
        public static ParameterMonitorViewModel ParameterMonitor;
        public static ParameterReadWriteViewModel ParameterReadWrite;
        public static CommunicationSetViewModel CommunicationSet;
        public static NormalSettingViewModel NormalSetting;
        public static SwitchAxisViewModel SwitchAxis;
        public static MotorParameterIdentificationViewModel MotorParameterIdentification;
        public static MotorParameterIdentificationViewModel MotorParameterSelfLearning;
        public static OfflineInertiaIdentificationViewModel OfflineInertiaIdentification;
        public static FirmwareUpdateViewModel FirmwareUpdate;
        public static AdministratorViewModel Administrator;
        public static HardwareAlarmHistoryViewModel HardwareAlarmHistory;
        public static HardwareAlarmMeasureViewModel HardwareAlarmMeasure;
        public static UnitViewModel Unit;
        public static JogViewModel Jog;
        public static MotorDriectionJogViewModel JogDriection;
        public static ProgramJogViewModel ProgramJog;
        public static ModifyPasswordViewModel ModifyPassword;
        public static MotorLibraryViewModel MotorLibrary;
        public static MotorLibraryDetailsViewModel MotorLibraryDetails;
        public static AddSlaveAxisIDViewModel AddSlaveAxisID;

        public static ParameterImportViewModel ParameterImport;//由Lilbert于2023.05.17添加
        public static FaultDataConfigViewModel FaultDataConfig;//由Lilbert于2023.06.09添加
        public static AdvancedFeedbackViewModel AdvancedFeedback;//由Lilbert于2023.11.14添加

        public static SplashScreenViewModel SplashScreen;
    }

    //参数集合
    public static class GlobalParameterSet
    {
        public static DataTable dt_Diff = new DataTable();//自动或手动导入配置参数的差异
        public static DataTable Out_dt_Diff = new DataTable();//自动或手动导入配置参数的差异
        public static DataTable dt_Import = new DataTable();//自动或手动导入的配置参数
        public static DataTable dt_Import_SoftVersion = new DataTable();//自动或手动导入的配置参数
        public static DataTable dt_Export_SoftVersion = new DataTable();//用于导出的配置参数
        public static DataTable dt_Base = new DataTable();//自动或手动导入的配置参数
        public static DataTable Out_dtImport = new DataTable();//自动或手动导入配置参数的差异
        public static DataTable dt_Export_ForCompare = new DataTable();//用于导出的配置参数

        public static Dictionary<string, string> CurrentPointValue_AxisA = new Dictionary<string, string>();//当前点的数值
        public static Dictionary<string, string> CurrentPointValue_AxisB = new Dictionary<string, string>();//当前点的数值

        public static DataTable dt = new DataTable();//自动或手动导入的配置参数
        public static DataTable dt_Export = new DataTable();//用于导出的配置参数
        public static DataTable dt_Monitor = new DataTable();//用于导入的配置监控参数
        public static DataTable dt_HardwareExplanation = new DataTable();//用于导入报警信息解释
        public static List<AlarmInfoSet> lstHardwareAlarm = new List<AlarmInfoSet>();//用于硬件报警 

        public static DataTable dt_ConfigServo = new DataTable();//伺服配置参数     由Lilbert于2023年10月16日添加     
    }

    //报警信息集合
    public class AlarmInfoSet
    {
        public string Bit;//位
        public string Code;//编号
        public string Level;//等级
        public string Content;//解释
        public string Reason;//原因
        public string Measure;//处理方法
    }

    //异常数据集合
    public static class GlobalErrorSet
    {
        public static DataTable dtSoftware = new DataTable();
        public static DataTable dtHardware = new DataTable();
    }

    //当前输入的配置参数
    public static class GlobalCurrentInput
    {
        #region 通信配置
        public static string SelectedSerialPortNum;//选中串口号
        public static string SelectedBaudRate;//选中波特率
        public static string SelectedDataBit;//选中数据位
        public static string SelectedCheckBit;//选中校验位
        public static string SelectedEndBit;//选中停止位
        public static string SelectedAxisID;//选中的转轴ID
        public static string SelectedStationID;//选中的从站ID
        public static string SelectedSlaveID;//选中的从站ID

        public static string SelectedServoName;//选中的伺服驱动器名称
        #endregion

        #region 电机反馈
        public static string SelectedMotorType;//电机类型
        public static string SelectedMotorID;//电机编号

        public static string MotorRatedPower;//额定功率
        public static string MotorRatedFrequency;//额定功率
        //public static string MotorRatedVoltage;//额定电压
        public static string SelectedMotorRatedVoltage;//额定电压
        public static string MotorRatedCurrent;//额定电流
        public static string MotorRatedTorque;//额定转矩
        public static string MotorRatedSpeed;//额定转速

        public static string MotorMaxCurrent;//最大电流
        public static string MotorMaxTorque;//最大转矩
        public static string MotorMaxSpeed;//最大转速

        //public static string MotorLineUVWSequence;//电机动力线相序
        public static string SelectedMotorLineUVWSequenceType;//电机动力线相序
        public static string AbsEncoderOffset;//绝对式编码器偏置

        public static string MotorPolePairsNumber;//极对数
        public static string MotorWindingResistance;//线电阻
        public static string MotorWindingInductance;//线电感
        public static string MotorRotorInertia;//转动惯量
        public static string MotorBackEMF;//电机反电势常数
        public static string MotorTorqueConstant;//转矩系数
        public static string MotorMechanicalConstant;//机械系数

        public static string LinearMotorPitch;//直线电机节距
        public static string OverSpeedValue;//超速预警阈值
        public static string MotorEncoderType;//编码器类型
        public static string MotorInductanceLd;//D轴线电感
        public static string MotorInductanceLq;//Q轴线电感

        public static string SelectedEncoderType;//编码器类型
        public static string ABSEncoderSingleTurnBit;//单圈分辨率
        public static string ABSEncoderMultiTurnBit;//多圈分辨率
        public static string ABSEncoderOffset;//绝对式编码器偏置
        public static string ABZEncoderPulses;//ABZ编码器脉冲数
        public static string BissCEncoderLength;//Biss-C总位置长度
        #endregion

        #region 惯量辨识和参数自整定
        public static string InertiaIdetificationStartValue;//惯量辨识起始值
        public static string AutoTunningDistance;//自整定移动距离
        //public static string AutoTunningRigidityLevel;//自整定刚性等级
        public static string PositionCompletionWidth;//定位完成宽度
        public static string SelectedAutoTunningModeType;//自整定应用类型   
        public static string SelectedAutoTunningRigidityLevelType;//自整定刚性等级  

        public static string SelectedVibrationSuppressionOption;//A型抑振控制选择
        public static string VibsupFreq;//A型抑振频率
        public static string VibsupGainComp;//A型抑振增益补偿
        public static string VibsupDampingGain;//A型抑振阻尼增益
        public static string SelectedModelFollowingControlSwitch;//模型追踪控制开关   
        public static string ModelFollowingControlGain;//模型追踪控制增益
        public static string MFCGainCorrection;//模型追踪控制增益补偿
        public static string VibrationSuppressionFrequencyA;//振动抑制1频率A         
        #endregion

        #region 控制限幅
        public static string ForwardInternalTorqueLimit;//正转内部转矩限制值
        public static string ReverseInternalTorqueLimit;//反转内部转矩限制值
        public static string ForwardExternalTorqueLimit;//正转外部转矩限制值
        public static string ReverseExternalTorqueLimit;//反转外部转矩限制值
        public static string EmergencyStopTorqueLimit;//紧急停止转矩限制值

        public static string ServoOnSpeedLimit;//使能时速度限制值
        public static string TrqctrlSpeedLimit;//转矩控制时速度限制值
        public static string MaxProfileVelocity;//最大轮廓速度
        public static string MaxAcceleration;//最大加速度
        public static string MaxDeceleration;//最大减速度
        public static string VelocityWindow;//速度到达阈值
        public static string VelocityWindowTime;//速度到达窗口时间

        public static string PosErrWarnLevel;//位置偏差过大警告值
        public static string PosErrAlarmLevel;//位置偏差过大报警值
        public static string SvonPosErrWarnLevel;//使能时位置偏差过大警告值
        public static string SvonPosErrAlarmLevel;//使能时位置偏差过大报警值
        public static string FollowingErrorWindow;//位置跟踪误差阈值
        public static string FollowingErrorTimeout;//位置跟踪误差过大判定时间
        public static string PositionWindow;//位置到达阈值
        public static string PositionWindowTime;//位置到达窗口时间
        public static string MinSoftwarePositionLimit;//软件限位最小值
        public static string MaxSoftwarePositionLimit;//软件限位最大值

        public static string VdcOvLevel;//母线电压过压报警值
        public static string VdcDischargeLevel;//母线电压泄放阈值
        public static string VdcUvLevel;//母线电压欠压报警值
        public static string VdcUvFilter;//母线电压欠压滤波时间常数
        public static string VdcUvWarnLevel;//母线电压欠压警告值
        #endregion

        #region 数字IO
        public static string SelectedDI1Logic;//DI1输入方式
        public static string SelectedDI2Logic;//DI2输入方式
        public static string SelectedDI3Logic;//DI3输入方式
        public static string SelectedDI4Logic;//DI4输入方式
        public static string SelectedDI5Logic;//DI5输入方式
        public static string SelectedDI6Logic;//DI6输入方式

        public static string SelectedDI1Function;//DI1功能
        public static string SelectedDI2Function;//DI2功能
        public static string SelectedDI3Function;//DI3功能
        public static string SelectedDI4Function;//DI4功能
        public static string SelectedDI5Function;//DI5功能
        public static string SelectedDI6Function;//DI6功能

        public static string SelectedDO1Logic;//DO1输入方式
        public static string SelectedDO2Logic;//DO2输入方式
        public static string SelectedDO3Logic;//DO3输入方式
        public static string SelectedDO4Logic;//DO4输入方式
        public static string SelectedDO5Logic;//DO5输入方式
        public static string SelectedDO6Logic;//DO6输入方式

        public static string SelectedDO1Function;//DO1功能
        public static string SelectedDO2Function;//DO2功能
        public static string SelectedDO3Function;//DO3功能
        public static string SelectedDO4Function;//DO4功能
        public static string SelectedDO5Function;//DO5功能
        public static string SelectedDO6Function;//DO6功能
        #endregion

        #region 抱闸制动
        public static string BrakeActivationVelocity;//使能抱闸的速度阈值
        public static string BrakeDisengageTime;//松闸延迟时间
        public static string BrakeEngageTime;//抱闸延迟时间
        public static string BrakeEnable;//使能抱闸
        public static string BrakeDisengageSoftwareDelayTime;//松闸前软件延迟时间
        public static string BrakeEngageSoftwareDelayTime;//抱闸前软件延迟时间
        public static string BrakeActivationDelayTime;//故障时使能抱闸的时间阈值
        #endregion

        #region 控制滤波
        public static string PositionLoopKp;//位置环Kp
        public static string VelocityLoopKp;//速度环Kp
        public static string VelocityLoopKi;//速度环Ki
        public static string CurrentLoopKp;//电流环Kp
        public static string CurrentLoopKi;//电流环Ki
        public static string SelectedSpeedFilterTypeIndex;//速度反馈滤波器类型
        public static string SpeedFilterFrequency;//速度反馈滤波器滤波频率
        public static string SpeedFilterQualityFactor;//速度反馈滤波器品质因数
        public static string SelectedSpeedErrorFilterTypeIndex;//速度误差滤波器类型
        public static string SpeedErrorFilterFrequency;//速度误差滤波器滤波频率
        public static string SpeedErrorFilterQualityFactor;//速度误差滤波器品质因数
        public static string SelectedSpeedErrorFilterType2Index;//速度误差滤波器2类型
        public static string SpeedErrorFilterFrequency2;//速度误差滤波器2滤波频率
        public static string SpeedErrorFilterQualityFactor2;//速度误差滤波器2品质因数
        #endregion

        #region 函数发生器与三环调试
        public static string InnerSourceFrequency;//信号频率
        public static string InnerSourceAmplitude;//位置幅值
        public static string SelectedInnerSourceEffectIndex;//作用对象
        public static string SelectedInnerSourceTypeIndex;//函数类型
        public static string InnerSourceNumber;//产生个数
        public static string InnerSourceGradient;//信号斜率
        public static string AmplitudeUnit;//幅值单位

        public static string SelectedLoopMode;//选中模式
        public static string PositionLoopGain;//位置环增益
        public static string SpeedLoopGain;//速度环增益
        public static string SpeedLoopTimeConstant;//速度环积分时间常数
        public static string CurrentLoopGain;//电流环增益
        public static string CurrentLoopTimeConstant;//电流环积分时间常数
        #endregion

        #region 示波器
        public static string SelectedOscilloscopePreset;//示波器预设置
        public static string SelectedOscilloscopePresetID;//选中的示波器ID
        public static string SelectedSamplingPeriod;//采样周期
        public static string SelectedSamplingDuration;//采样时长
        public static string SelectedContinuousSampling;//连续采样

        public static int SelectedSampleChannel1Index;//采用通道1
        public static int SelectedSampleChannel2Index;//采用通道2
        public static int SelectedSampleChannel3Index;//采样通道3
        public static int SelectedSampleChannel4Index;//采样通道4

        public static string SelectedTriggerClockEdge;//触发边沿 
        public static string SelectedTriggerChannel;//触发通道
        public static string SelectedPreTrigger;//预触发
        public static string TriggerLevel;//触发水平
        public static string SelectedDisplayMethod;//展示方式

        public static int TriggerChannelIndex;//触发通道检索号
        public static int SamplingDurationIndex;//采样时长检索号

        public static string[] DoublingChannel = new string[4];//倍乘通道
        #endregion

        #region 故障数据示波器
        //public static int SelectedOscilloscopePresetIndex;//示波器预设置
        //public static string SelectedOscilloscopePresetID;//选中的示波器ID
        public static string SelectedSamplingPeriod_Fault;//采样周期
        public static string SelectedSamplingDuration_Fault;//采样时长
        //public static string SelectedContinuousSampling_Fault;//连续采样
        public static string SelectedSamplingPeriodConfig;//采样周期
        public static string SelectedSamplingDurationConfig;//采样时长
        public static int SamplingDurationIndexConfig;//采样时长检索号

        public static string SelectedSampleChannel1Index_Fault;//采用通道1
        public static string SelectedSampleChannel2Index_Fault;//采用通道2
        public static string SelectedSampleChannel3Index_Fault;//采样通道3
        public static string SelectedSampleChannel4Index_Fault;//采样通道4
        public static string SelectedSampleChannel5Index_Fault;//采用通道5
        public static string SelectedSampleChannel6Index_Fault;//采用通道6
        public static string SelectedSampleChannel7Index_Fault;//采样通道7
        public static string SelectedSampleChannel8Index_Fault;//采样通道8
        public static int SelectedSampleChannel1IndexConfig;//采用通道1
        public static int SelectedSampleChannel2IndexConfig;//采用通道2
        public static int SelectedSampleChannel3IndexConfig;//采样通道3
        public static int SelectedSampleChannel4IndexConfig;//采样通道4
        public static int SelectedSampleChannel5IndexConfig;//采用通道5
        public static int SelectedSampleChannel6IndexConfig;//采用通道6
        public static int SelectedSampleChannel7IndexConfig;//采样通道7
        public static int SelectedSampleChannel8IndexConfig;//采样通道8

        //public static string SelectedTriggerClockEdge_Fault;//触发边沿 
        //public static string SelectedTriggerChannel_Fault;//触发通道
        //public static string SelectedPreTrigger_Fault;//预触发
        //public static string TriggerLevel_Fault;//触发水平
        public static string SelectedDisplayMethod_Fault;//展示方式

        public static int TriggerChannelIndex_Fault;//触发通道检索号
        //public static int SamplingDurationIndex_Fault;//采样时长检索号

        public static string[] DoublingChannel_Fault = new string[8];//倍乘通道
        #endregion

        #region 电流环调试
        public static string FirstTrqcmdFilterTime;//第一转矩指令滤波时间参数
        public static string SecondTrqcmdFilterFreq;//第二转矩指令滤波器频率
        public static string SecondTrqcmdFilterQ;//第二转矩指令滤波器Q值
        public static string NotchFilterConfig;//陷波滤波器配置
        public static string NotchFilterFrequency1;//第1段陷波滤波器频率
        public static string NotchFilterQFactor1;//第1段陷波滤波器Q值
        public static string NotchFilterDepth1;//第1段陷波滤波器深度
        public static string NotchFilterFrequency2;//第2段陷波滤波器频率
        public static string NotchFilterQFactor2;//第2段陷波滤波器Q值
        public static string NotchFilterDepth2;//第2段陷波滤波器深度
        public static string NotchFilterFrequency3;//第3段陷波滤波器频率
        public static string NotchFilterQFactor3;//第3段陷波滤波器Q值
        public static string NotchFilterDepth3;//第3段陷波滤波器深度
        public static string NotchFilterFrequency4;//第4段陷波滤波器频率
        public static string NotchFilterQFactor4;//第4段陷波滤波器Q值
        public static string NotchFilterDepth4;//第4段陷波滤波器深度
        public static string ForwardInternalTorqueLimit_CurrentLoop;//正转内部转矩限制值
        public static string ReverseInternalTorqueLimit_CurrentLoop;//反转内部转矩限制值
        public static string ForwardExternalTorqueLimit_CurrentLoop;//正转外部转矩限制值
        public static string ReverseExternalTorqueLimit_CurrentLoop;//反转外部转矩限制值
        #endregion

        #region 速度环调试
        public static string SpeedLoopGain_SpeedLoop;//速度环增益
        public static string SpeedLoopTimeConstant_SpeedLoop;//速度环积分时间常数
        public static string LoadInertiaRatio;//负载惯量比
        public static string SelectedTrqffControlSelectIndex;//转矩前馈选择
        public static string TrqffFilterFimeConstant;//转矩前馈滤波时间常数
        public static string TrqffGain;//转矩前馈增益
        public static string SelectedSpeedAverageFilterConfigIndex;//速度反馈平均滤波配置
        public static string SpeedObserverGain;//速度观测增益
        public static string SpeedObserverPosCompensationGain;//速度观测补偿增益
        public static string SpeedFeedbackLPFTime;//低通滤波器时间参数
        public static string TorqueFeedForwardMaFilterTime;//转矩前馈平均滤波时间
        public static string Rigidity;//刚性
        #endregion

        #region 位置环调试
        public static string PositionLoopGain_PositionLoop;//位置环增益
        public static string SelectedSpdFFControlSelectIndex;//速度前馈选择
        public static string SpdFFFilterFimeConstant;//速度前馈滤波时间常数
        public static string SpdFFGain;//速度前馈增益
        public static string PositionReferenceMaFilterTime;//位置指令平均滤波时间
        public static string PositionReferenceHighFilterRatio;//位置指令平滑比
        public static string SpeedFeedForwardMaFilterTime;//速度前馈平均滤波时间
        #endregion

        #region 高级功能参数
        public static string GainChangeTimeOne;//增益切换时间1
        public static string GainChangeTimeTwo;//增益切换时间2
        public static string GainChangeWaitTimeOne;//增益切换等待时间1
        public static string GainChangeWaitTimeTwo;//增益切换等待时间2
        public static string SelectedGainSwitch;//增益切换开关[0-3]bit
        public static string SelectedGainSwitchCondition;//增益切换开关[4-8]bit
        public static string SelectedSpeedModeSwitch;//速度模式开关设置
        public static string ModeSwitchTorqueValue;//模式开关（转矩指令）
        public static string ModeSwitchSpeedValue;//模式开关（速度指令）
        public static string ModeSwitchAccValue;//模式开关（加速度）

        public static string SelectedVibrationSuppressionASwitch;//A型抑振控制选择[0-3]bit
        public static string SelectedSelfTuningSet;//A型抑振控制选择[4-7]bit
        public static string VibsupFreq_Advanced;//A型抑振频率
        public static string VibsupGainComp_Advanced;//A型抑振增益补偿
        public static string VibsupDampingGain_Advanced;//A型抑振阻尼增益
        public static string SelectedSpeedObserverSwitch;//高级应用开关[0-3]bit
        public static string SelectedDisturbanceObserverSwitch;//高级应用开关[4-7]bit
        public static string DisturbanceObserverGainOne;//摩擦补偿增益
        public static string DisturbanceObserverGainTwo;//摩擦补偿增益2
        public static string DisturbanceObserverCoefficient;//摩擦补偿系数
        public static string DisturbanceObserverFreqCorrection;//摩擦补偿频率补偿
        public static string DisturbanceObserverGainCorrection;//摩擦补偿增益补偿
        public static string SpeedObserverGain_Advanced;//速度观测增益
        public static string SpeedObserverPosCompensationGain_Advanced;//速度观测补偿增益
        public static string SelectedEndVibrationSuppressionOption;//末端抖动抑制控制选择
        public static string EndVibrationSuppressionFrequency;//末端抖动抑制频率
        public static string EndVibrationSuppressionCompensation;//末端抖动抑制补偿

        public static string SelectedModelFollowingSwitch;//模型追踪控制开关[0-3]bit
        public static string SelectedModelFollowingVibrationSuppressionSwitch;//模型追踪控制开关[4-7]bit
        public static string ModelFollowingControlGain_Advanced;//模型追踪控制增益
        public static string MFCGainCorrection_Advanced;//模型追踪控制增益补偿
        public static string MFCForwardBias;//模型追踪控制增益偏置(正向)
        public static string MFCReverseBias;//模型追踪控制增益偏置(反向)
        public static string VibrationSuppressionFrequencyA_Advanced;//振动抑制1频率A
        public static string VibrationSuppressionFrequencyB;//振动抑制1频率B
        public static string MFCVelocityFeedforwardCompensation;//模型追踪控制速度前馈补偿
        public static string MFCGainTwo;//第2模型追踪控制增益
        public static string MFCGainCorrectionTwo;//第2模型追踪控制增益补偿

        public static string WeakFieldControlGain;//弱磁控制电压反馈增益
        public static string WeakFieldControlTimeConstant;//弱磁控制电压反馈时间常数
        public static string WeakFieldMaxSpeedCorrespondingToIdRef;//弱磁最大速度对应的Id指令
        public static string SelectedWeakFieldControlSwitch;//弱磁控制开关       
        #endregion

        #region 一般设置
        public static string MotorRevolutions;//电机分辨率
        public static string LoadShaftRevolutions;//负载轴分辨率

        public static string BrakeReleaseDelayTime;//抱闸释放延时时间
        public static string BrakeActiveDelayTime;//抱闸制动延时时间
        public static string BrakeActiveVelocity;//抱闸制动速度门限
        public static string BrakeActiveAllowedDelayTime;//抱闸制动允许延时时间

        public static string SelectedOverTravelStopMode;//超程停机方式
        public static string SelectedForceStopMode;//强制停机方式
        public static string SelectedTwoFaultStopMode;//报警停机方式
        public static string SelectedOneFaultStopMode;//故障NO.1 停机方式

        public static bool IsCWChecked;//是否CW为正转
        public static bool IsCCWChecked;//是否CCW为正转
        public static bool IsJogIntermittentModeChecked;//Jog点动模式
        public static bool IsJogContinuousModeChecked;//Jog连续模式
        public static bool IsBrakeChecked;//是否抱闸使用
        public static string JogSpeed;//Jog速度
        public static string JogAccelerationTime;//Jog点动加速时间
        public static string JogDecelerationTime;//Jog点动减速时间
        public static bool IsJogModeChecked;//Jog运动模式
        public static string JogTime;//Jog运动时间
        #endregion

        #region 运动模式
        public static string TargetPosition;//目标位置
        public static string ProfileVelocity;//轮廓运行速度
        public static string EndProfileVelocity;//轮廓结尾速度
        public static string ProfileAcceleration;//轮廓加速度
        public static string ProfileDeceleration;//轮廓减速度
        public static string ModesOfOperation;//伺服模式选择

        public static string TargetVelocity;//目标速度
        public static string ProfileJerk1;//轮廓加加速度1
        public static string ProfileJerk2;//轮廓加加速度2
        public static string ProfileJerk3;//轮廓加加速度3
        public static string ProfileJerk4;//轮廓加加速度4

        public static string TargetTorque;//目标转矩
        public static string TorqueSlope;//转矩斜坡
        public static string SelectedActionMode;//选中的运动模式
        public static string SelectedPositionMode;//选中的位置指令
        public static string SelectedProfileMode;//选中的规划曲线

        public static string PositionUnit;//位置单位
        public static string TorqueUnit;//转矩单位
        public static string SpeedUnit;//速度单位
        public static string AccelerationUnit;//加速单位

        //public static string ProgramJogMovingDistance;//程序Jog移动距离
        //public static string ProgramJogMovingSpeed;//程序Jog移动速度
        //public static string ProgramJogAccDecTime;//程序Jog加减速时间
        //public static string ProgramJogWaitTime;//程序Jog等待时间
        //public static string ProgramJogMovingNumber;//程序Jog移动次数
        #endregion

        #region 参数调优模式
        public static string SelectedParameterTunningMode;//选中的参数调优模式

        public static string ParameterTunningJogSpeed;//Jog速度
        public static string ParameterTunningJogAccelerationTime;//Jog点动加速时间
        public static string ParameterTunningJogDecelerationTime;//Jog点动减速时间
        public static string ParameterTunningJogTime;//Jog运动时间

        public static string ParameterTunningProgramJogMovingDistance;//程序Jog移动距离
        public static string ParameterTunningProgramJogMovingSpeed;//程序Jog移动速度
        public static string ParameterTunningProgramJogAccDecTime;//程序Jog加减速时间
        public static string ParameterTunningProgramJogWaitTime;//程序Jog等待时间
        public static string ParameterTunningProgramJogMovingNumber;//程序Jog移动次数
        #endregion

        #region 回零模式
        public static string FastHomingSpeed;//搜索原点高速
        public static string SlowHomingSpeed;//搜索原点低速
        public static string HomingAcceleration;//回零加速度
        public static string HomeOffset;//原点偏移量
        public static string SelectedHomingMethod;//原点复归方法
        #endregion

        #region 报警编号
        public static string LastAlarmIndex = "0";//报警标号
        #endregion
    }

    //公司信息
    public static class GlobalCompanyInfo
    {
        #region 公司信息
        public static string CompanyDepartment;//公司及部门
        public static string CompanyDepartment1;//公司及部门
        public static string CompanyWeb;//公司网站
        public static string CompanyName;//公司名称       
        #endregion
    }

    //线程实例集合
    public static class PthreadStatement
    {
        public static SerialPortTransmitingPthread SerialPortTransmiting = new SerialPortTransmitingPthread();
        public static MicrosecondsOscilloscopeDrawingPthread MicrosecondsOscilloscopeDrawing = new MicrosecondsOscilloscopeDrawingPthread();
    }

    //示波器参数集合
    public class OscilloscopeParameterSet
    {
        public string OscilloscopePreset;//示波器预设置
        public string AxisID;//轴ID

        public string Period;//周期
        public string Duration;//时长
        public string IsContinuous;//是否连续
        public string Date;//时间

        public string Channel1Name;//通道1名称
        public string Channel2Name;//通道2名称
        public string Channel3Name;//通道3名称
        public string Channel4Name;//通道4名称

        public string TriggerMode;//触发模式
        public string TriggerChannel;//触发通道
        public string PreTrigger;//预触发
        public string TriggerLevel;//触发水平

        public List<int> lstChannel1;//通道1数值
        public List<int> lstChannel2;//通道2数值
        public List<int> lstChannel3;//通道3数值
        public List<int> lstChannel4;//通道4数值

        public string Channel1Doubling;//通道1倍乘
        public string Channel2Doubling;//通道2倍乘
        public string Channel3Doubling;//通道3倍乘
        public string Channel4Doubling;//通道4倍乘

        public List<string> lstUnit;//单位
        public List<double> lstExchangeValue;//单位换算
    }

    public static class ConfigOscilloscopePreset
    {
        public static int SelectConfigID;
        public static string SelectConfigServoName;
        public static int SelectConfigSlaveID;
        public static int SelectConfigAxisID;
        public static string SelectConfigParameter;

        public static List<OscilloscopePresetModel> OscilloscopePresetConfigs = new List<OscilloscopePresetModel>();//示波器预配置信息集合
        //public static List<string> ConfigServoName = new List<string>();//配置伺服名称集合
        //public static List<ServoConfigsModel> SelectServoConfigs = new List<ServoConfigsModel>();//选择的伺服配置信息集合
    }

    //示波器离线数据导入导出
    public static class OfflineOscilloscope
    {
        public static OscilloscopeParameterSet Import = new OscilloscopeParameterSet();
        public static OscilloscopeParameterSet Export = new OscilloscopeParameterSet();
        public static OscilloscopeParameterSet Last = new OscilloscopeParameterSet();
    }

    //故障数据参数集合
    public class FaultAcquisitionParameterSet
    {
        //public string OscilloscopePreset;//示波器预设置
        public string AxisID;//轴ID

        public string Period;//周期
        public string Duration;//时长
        //public string IsContinuous;//是否连续
        public string Date;//时间

        public string Channel1Name;//通道1名称
        public string Channel2Name;//通道2名称
        public string Channel3Name;//通道3名称
        public string Channel4Name;//通道4名称
        public string Channel5Name;//通道5名称
        public string Channel6Name;//通道6名称
        public string Channel7Name;//通道7名称
        public string Channel8Name;//通道8名称

        //public string TriggerMode;//触发模式
        //public string TriggerChannel;//触发通道
        //public string PreTrigger;//预触发
        //public string TriggerLevel;//触发水平

        public List<int> lstChannel1;//通道1数值
        public List<int> lstChannel2;//通道2数值
        public List<int> lstChannel3;//通道3数值
        public List<int> lstChannel4;//通道4数值
        public List<int> lstChannel5;//通道5数值
        public List<int> lstChannel6;//通道6数值
        public List<int> lstChannel7;//通道7数值
        public List<int> lstChannel8;//通道8数值

        public string Channel1Doubling;//通道1倍乘
        public string Channel2Doubling;//通道2倍乘
        public string Channel3Doubling;//通道3倍乘
        public string Channel4Doubling;//通道4倍乘
        public string Channel5Doubling;//通道5倍乘
        public string Channel6Doubling;//通道6倍乘
        public string Channel7Doubling;//通道7倍乘
        public string Channel8Doubling;//通道8倍乘

        public List<string> lstUnit;//单位
        public List<double> lstExchangeValue;//单位换算
    }

    //故障数据采集离线数据导入导出
    public static class OfflineFaultAcquisition
    {
        public static FaultAcquisitionParameterSet Import = new FaultAcquisitionParameterSet();
        public static FaultAcquisitionParameterSet Export = new FaultAcquisitionParameterSet();
        public static FaultAcquisitionParameterSet Last = new FaultAcquisitionParameterSet();
    }

    //函数发生器参数
    public class FunctionGeneratorSet
    {
        public string InnerSourceNumber;//信号个数
        public string InnerSourceFrequency;//信号频率
        public string InnerSourceAmplitude;//信号幅值
        public string InnerSourceEffectIndex;//作用对象
        public string InnerSourceTypeIndex;//信号类型
        public string InnerSourceGradient;//信号斜率
        public string InnerSourceSwitch;//发生开关
    }

    public class AlarmBitAndTime
    {
        public int Bit;
        public DateTime AlarmTime;
    }

    public static class HardwareAlarmInfoSet
    {
        public static int AlarmNum;//报警个数
        public static bool IsExistAlarm;//是否有报警
        public static bool IsAlreadyPrompt = true;//是否提示过
        public static string LastAlarmSet;
        public static string CurrentAlarmSet;
        public static List<AlarmBitAndTime> LastAlarmBitList = new List<AlarmBitAndTime>();
        public static List<AlarmBitAndTime> CurrentAlarmBitList = new List<AlarmBitAndTime>();
        public static List<string> SlideAlarmState = new List<string>();//侧边栏报警展开收起状态
    }

    public static class MotorLibraryDetailSet
    {
        public static string FileName;//文件名
        public static string Action;//操作名

        public static string Author;
        public static string MotorType;
        public static string EncoderType;
        public static string Comment;
    }

    public static class AddSlaveAxisIDSet
    {
        public static string FileName;//文件名
        public static string Action;//操作名

        public static bool Abc = false;//是否有报警

        public static ObservableCollection<string> GetSlaveIDList { get; set; }

    }
   
}
