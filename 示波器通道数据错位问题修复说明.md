# ServoStudio 示波器通道数据错位问题修复说明

## 🐛 问题描述

在ServoStudio的示波器数据采集功能中，当选择多个通道进行采集时，会出现数据错位的问题：

### 问题现象
- **Debug参数4** (ID=41, Address="29") 会采集到 **Debug参数5** 的值
- **Debug参数5** (ID=42, Address="2A") 采集不正确
- **位置增量的增量** (ID=43, Address="2B") 采集正常

### 配置示例
```xml
<Channel GroupName="Debug (4)" ID="41" ItemName="Debug参数4" Address="29" />
<Channel GroupName="Debug (4)" ID="42" ItemName="Debug参数5" Address="2A" />
<Channel GroupName="位置 (5)" ID="43" ItemName="位置增量的增量" Address="2B" />
```

## 🔍 根本原因分析

### 问题根源
1. **发送端排序**：在`GetTransmittingContent`方法中，通道ID被排序后发送给硬件
   ```csharp
   channelIds.Sort(); // 按ID排序：41, 42, 43
   strChannel = string.Concat(channelIds.Select(id => GetSampleAddressByIndex(id)));
   // 发送地址顺序：29, 2A, 2B
   ```

2. **接收端固定映射**：在`ReceivingMessage_ForUploading`方法中，数据按固定顺序分配
   ```csharp
   switch (AcquisitionInfoSet.ChannelNumberOfCurrentMessage)
   {
       case 0: // 第一个数据 → Channel1
       case 1: // 第二个数据 → Channel2  
       case 2: // 第三个数据 → Channel3
   }
   ```

3. **映射错位**：硬件按地址顺序返回数据，但软件按固定通道索引分配，导致数据错位

## 🛠️ 修复方案

### 核心思路
建立通道ID与UI通道索引的正确映射关系，确保数据按照正确的通道分配。

### 修复内容

#### 1. 添加通道映射字段
在`AcquisitionInfoSet`类中添加新字段：
```csharp
// 通道映射列表，用于解决数据错位问题
// 存储按ID排序后的UI通道索引，与硬件返回数据顺序对应
public static List<int> lstChannelMapping = new List<int>();
```

#### 2. 修改RefreshAcquisitionList方法
创建并存储通道映射关系：
```csharp
// 创建通道ID到UI通道索引的映射
List<(int channelId, int uiChannelIndex)> channelMappings = new List<(int, int)>();

if (SelectedSampleChannel1Index != 0)
    channelMappings.Add((SelectedSampleChannel1Index, 1));
if (SelectedSampleChannel2Index != 0)
    channelMappings.Add((SelectedSampleChannel2Index, 2));
// ... 其他通道类似

// 按照通道ID排序，与GetTransmittingContent中的排序保持一致
channelMappings.Sort((x, y) => x.channelId.CompareTo(y.channelId));

// 存储映射关系
AcquisitionInfoSet.lstChannelMapping = channelMappings.Select(x => x.uiChannelIndex).ToList();
```

#### 3. 修改数据接收逻辑
在`HexHelper.ReceivingMessage_ForUploading`方法中使用映射关系：
```csharp
// 使用通道映射来正确分配数据到对应的UI通道
int currentDataIndex = AcquisitionInfoSet.ChannelNumberOfCurrentMessage;
if (currentDataIndex < AcquisitionInfoSet.lstChannelMapping.Count)
{
    int uiChannelIndex = AcquisitionInfoSet.lstChannelMapping[currentDataIndex];
    
    switch (uiChannelIndex)
    {
        case 1: // 分配到UI通道1
            AcquisitionData.Channel1 = new List<int>();
            AcquisitionInfoSet.lstReceiving[currentDataIndex].ForEach(item => AcquisitionData.Channel1.Add(item));
            break;
        case 2: // 分配到UI通道2
            AcquisitionData.Channel2 = new List<int>();
            AcquisitionInfoSet.lstReceiving[currentDataIndex].ForEach(item => AcquisitionData.Channel2.Add(item));
            break;
        // ... 其他通道类似
    }
}
```

## 📋 修复文件清单

### 修改的文件
1. **ServoStudio/GlobalVariable/GlobalVariable.cs**
   - 在`AcquisitionInfoSet`类中添加`lstChannelMapping`字段

2. **ServoStudio/ViewModels/OscilloscopeViewModel.cs**
   - 修改`RefreshAcquisitionList`方法，建立通道映射关系

3. **ServoStudio/GlobalMethod/HexHelper.cs**
   - 修改`ReceivingMessage_ForUploading`方法，使用映射关系分配数据

## 🧪 测试验证

### 测试场景
使用以下配置进行测试：
```xml
<Channel GroupName="Debug (4)" ID="41" ItemName="Debug参数4" Address="29" />
<Channel GroupName="Debug (4)" ID="42" ItemName="Debug参数5" Address="2A" />
<Channel GroupName="位置 (5)" ID="43" ItemName="位置增量的增量" Address="2B" />
```

### 预期结果
- **Debug参数4** 显示正确的数据（地址29的数据）
- **Debug参数5** 显示正确的数据（地址2A的数据）
- **位置增量的增量** 继续正常显示（地址2B的数据）

### 数据流验证
1. **发送阶段**：地址按ID排序发送 → 29, 2A, 2B
2. **接收阶段**：数据按地址顺序返回 → data[29], data[2A], data[2B]
3. **分配阶段**：根据映射关系正确分配
   - data[29] → UI通道1 (Debug参数4)
   - data[2A] → UI通道2 (Debug参数5)
   - data[2B] → UI通道3 (位置增量的增量)

## ✅ 修复优势

1. **向后兼容**：不影响现有的正常工作的通道配置
2. **逻辑清晰**：明确的映射关系，便于理解和维护
3. **扩展性好**：支持任意通道ID和地址的组合
4. **性能影响小**：只增加了少量的映射逻辑，不影响整体性能

## 🔧 使用说明

修复后，用户可以：
1. 在XML配置文件中任意配置通道ID和地址
2. 选择任意通道组合进行采集
3. 数据将正确显示在对应的UI通道中

无需额外的配置或操作，修复对用户完全透明。
