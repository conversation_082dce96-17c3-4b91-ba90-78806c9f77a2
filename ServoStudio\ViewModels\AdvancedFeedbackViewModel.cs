﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Collections.Generic;
using ServoStudio.Models;
using System.Data;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class AdvancedFeedbackViewModel
    {
        #region 私有字段
        private static bool IsInitialized = true;
        private static bool IsEvaluationAll = true;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 服务      
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 属性
        public virtual string SelectedTabIndex { get; set; }

        #region 增益切换参数
        public virtual string GainChangeTimeOne { get; set; }//增益切换时间1
        public virtual string GainChangeTimeTwo { get; set; }//增益切换时间2
        public virtual string GainChangeWaitTimeOne { get; set; }//增益切换等待时间1
        public virtual string GainChangeWaitTimeTwo { get; set; }//增益切换等待时间2
        public virtual ObservableCollection<string> GainSwitch { get; set; }//增益切换开关[0-3]bit
        public virtual string SelectedGainSwitch { get; set; }//选中的增益切换开关[0-3]bit
        public virtual ObservableCollection<string> GainSwitchCondition { get; set; }//增益切换开关[4-8]bit
        public virtual string SelectedGainSwitchCondition { get; set; }//选中的增益切换开关[4-8]bit 
        #endregion

        #region 模式切换参数
        public virtual ObservableCollection<string> SpeedModeSwitch { get; set; }//速度模式开关设置
        public virtual string SelectedSpeedModeSwitch { get; set; }//选中的速度模式开关设置
        public virtual string ModeSwitchTorqueValue { get; set; }//模式开关(转矩指令)
        public virtual string ModeSwitchSpeedValue { get; set; }//模式开关(速度指令)
        public virtual string ModeSwitchAccValue { get; set; }//模式开关(加速度)               
        #endregion

        #region 增益切换参数
        public virtual ObservableCollection<string> VibrationSuppressionASwitch { get; set; }//A型抑振控制选择[0-3]bit
        public virtual string SelectedVibrationSuppressionASwitch { get; set; }//选中的A型抑振控制选择[0-3]bit
        public virtual ObservableCollection<string> SelfTuningSet { get; set; }//A型抑振控制选择[4-7]bit
        public virtual string SelectedSelfTuningSet { get; set; }//选中的A型抑振控制选择[4-7]bit
        public virtual string VibsupFreq { get; set; }//A型抑振频率
        public virtual string VibsupGainComp { get; set; }//A型抑振增益补偿
        public virtual string VibsupDampingGain { get; set; }//A型抑振阻尼增益     
        #endregion

        #region 摩擦补偿参数
        public virtual ObservableCollection<string> SpeedObserverSwitch { get; set; }//高级应用开关[0-3]bit
        public virtual string SelectedSpeedObserverSwitch { get; set; }//选中的高级应用开关[0-3]bit
        public virtual ObservableCollection<string> DisturbanceObserverSwitch { get; set; }//高级应用开关[4-7]bit
        public virtual string SelectedDisturbanceObserverSwitch { get; set; }//选中的高级应用开关[4-7]bit
        public virtual string DisturbanceObserverGainOne { get; set; }//摩擦补偿增益
        public virtual string DisturbanceObserverGainTwo { get; set; }//摩擦补偿增益2
        public virtual string DisturbanceObserverCoefficient { get; set; }//摩擦补偿系数
        public virtual string DisturbanceObserverFreqCorrection { get; set; }//摩擦补偿频率补偿
        public virtual string DisturbanceObserverGainCorrection { get; set; }//摩擦补偿增益补偿
        public virtual string SpeedObserverGain { get; set; }//速度观测增益
        public virtual string SpeedObserverPosCompensationGain { get; set; }//速度观测补偿增益       
        #endregion

        #region 末端抖动抑制参数
        public virtual ObservableCollection<string> EndVibrationSuppressionOption { get; set; }//末端抖动抑制控制选择
        public virtual string SelectedEndVibrationSuppressionOption { get; set; }//选中的末端抖动抑制控制选择
        public virtual string EndVibrationSuppressionFrequency { get; set; }//末端抖动抑制频率
        public virtual string EndVibrationSuppressionCompensation { get; set; }//末端抖动抑制补偿        
        #endregion

        #region 模型追踪参数
        public virtual ObservableCollection<string> ModelFollowingSwitch { get; set; }//模型追踪控制开关[0-3]bit
        public virtual string SelectedModelFollowingSwitch { get; set; }//选中的模型追踪控制开关[0-3]bit
        public virtual ObservableCollection<string> ModelFollowingVibrationSuppressionSwitch { get; set; }//模型追踪控制开关[4-7]bit
        public virtual string SelectedModelFollowingVibrationSuppressionSwitch { get; set; }//选中的模型追踪控制开关[4-7]bit
        public virtual string ModelFollowingControlGain { get; set; }//模型追踪控制增益
        public virtual string MFCGainCorrection { get; set; }//模型追踪控制增益补偿
        public virtual string MFCForwardBias { get; set; }//模型追踪控制增益偏置(正向)
        public virtual string MFCReverseBias { get; set; }//模型追踪控制增益偏置(反向)
        public virtual string VibrationSuppressionFrequencyA { get; set; }//振动抑制1频率A
        public virtual string VibrationSuppressionFrequencyB { get; set; }//振动抑制1频率B
        public virtual string MFCVelocityFeedforwardCompensation { get; set; }//模型追踪控制速度前馈补偿     
        public virtual string MFCGainTwo { get; set; }//第2模型追踪控制增益
        public virtual string MFCGainCorrectionTwo { get; set; }//第2模型追踪控制增益补偿 
        #endregion

        #region 弱磁控制参数
        public virtual ObservableCollection<string> WeakFieldControlSwitch { get; set; }//弱磁控制开关
        public virtual string SelectedWeakFieldControlSwitch { get; set; }//选中的弱磁控制开关
        public virtual string WeakFieldControlGain { get; set; }//弱磁控制电压反馈增益
        public virtual string WeakFieldControlTimeConstant { get; set; }//弱磁控制电压反馈时间常数     
        public virtual string WeakFieldMaxSpeedCorrespondingToIdRef { get; set; }//弱磁最大速度对应的Id指令   
        #endregion
        
        #endregion

        #region 构造函数
        public AdvancedFeedbackViewModel()
        {
            ViewModelSet.AdvancedFeedback = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.ADVANCEDFEEDBACK;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：AdvancedFeedbackLoaded
        //函数功能：高级功能界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.15
        //*************************************************************************
        public void AdvancedFeedbackLoaded()
        {
            int iRet = -1;

            try
            {
                //背景颜色初始化
                //BackgroundInitialize();
                ComboBoxInitialize();

                if (IsInitialized == true)
                {
                    //标志位更新
                    IsInitialized = false;
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadAdvancedFeedbackParameter("All");
                    }
                    else
                    {
                        GetDefaultAdvancedFeedbackParameter("All");
                    }
                }
                else
                {
                    //标志位更新
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadAdvancedFeedbackParameter("All");
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_LOADED, "AdvancedFeedbackLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：WriteAdvancedFeedbackParameter
        //函数功能：写高级功能参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.16
        //*************************************************************************
        public void WriteAdvancedFeedbackParameter(string strCategory)
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary_ForWrite(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.ADVANCEDFEEDBACK, TaskName.AdvancedFeedback, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_WRITE_PARAMETER, "WriteAdvancedFeedbackParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadAdvancedFeedbackParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.15
        //*************************************************************************
        public void ReadAdvancedFeedbackParameter(string strCategory)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.ADVANCEDFEEDBACK, TaskName.AdvancedFeedback, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_READ_PARAMETER, "ReadAdvancedFeedbackParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultAdvancedFeedbackParameter
        //函数功能：获取高级功能参数的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.15
        //*************************************************************************
        public void GetDefaultAdvancedFeedbackParameter(string strCategory)
        {
            string strGainChangeSwitch = null;
            string strGainSwitch = null;
            string strGainSwitchCondition = null;

            string strSpeedModeSwitch = null;

            string strVibrationSuppressionOption = null;
            string strVibrationSuppressionASwitch = null;
            string strSelfTuningSet = null;

            string strAdvancedApplicationSwitch = null;
            string strSpeedObserverSwitch = null;
            string strDisturbanceObserverSwitch = null;

            string strEndVibrationSuppressionOption = null;

            string strModelFollowingControlSwitch = null;
            string strModelFollowingSwitch = null;
            string strModelFollowingVibrationSuppressionSwitch = null;

            string strWeakFieldControlSwitch = null;

            try
            {
                switch (strCategory)
                {
                    case "0":
                        GainChangeTimeOne = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Time 1", "Default");
                        GainChangeTimeTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Time 2", "Default");
                        GainChangeWaitTimeOne = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Wait Time 1", "Default");
                        GainChangeWaitTimeTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Wait Time 2", "Default");
                        strGainChangeSwitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Switch", "Default");
                        if (strGainChangeSwitch == "1")
                        {
                            strGainSwitchCondition = "0";
                            strGainSwitch = "1";                            
                        }
                        else if (strGainChangeSwitch == "2")
                        {
                            strGainSwitchCondition = "0";
                            strGainSwitch = "2";
                        }
                        else if (strGainChangeSwitch == "17")
                        {
                            strGainSwitchCondition = "1";
                            strGainSwitch = "1";
                        }
                        else if (strGainChangeSwitch == "18")
                        {
                            strGainSwitchCondition = "1";
                            strGainSwitch = "2";
                        }
                        else if (strGainChangeSwitch == "33")
                        {
                            strGainSwitchCondition = "2";
                            strGainSwitch = "1";
                        }
                        else if (strGainChangeSwitch == "34")
                        {
                            strGainSwitchCondition = "2";
                            strGainSwitch = "2";
                        }
                        else if (strGainChangeSwitch == "49")
                        {
                            strGainSwitchCondition = "3";
                            strGainSwitch = "1";
                        }
                        else if (strGainChangeSwitch == "50")
                        {
                            strGainSwitchCondition = "3";
                            strGainSwitch = "2";
                        }
                        else if (strGainChangeSwitch == "65")
                        {
                            strGainSwitchCondition = "4";
                            strGainSwitch = "1";
                        }
                        else if (strGainChangeSwitch == "66")
                        {
                            strGainSwitchCondition = "4";
                            strGainSwitch = "2";
                        }
                        else if (strGainChangeSwitch == "81")
                        {
                            strGainSwitchCondition = "5";
                            strGainSwitch = "1";
                        }
                        else if (strGainChangeSwitch == "82")
                        {
                            strGainSwitchCondition = "5";
                            strGainSwitch = "2";
                        }

                        if (strGainSwitch == "1")
                        {
                            SelectedGainSwitch = "关闭增益切换";
                        }
                        else if (strGainSwitch == "2")
                        {
                            SelectedGainSwitch = "开启增益切换";
                        }

                        if (strGainSwitchCondition == "0")
                        {
                            SelectedGainSwitchCondition = "定位完成信号On";
                        }
                        else if (strGainSwitchCondition == "1")
                        {
                            SelectedGainSwitchCondition = "定位完成信号Off";
                        }
                        else if (strGainSwitchCondition == "2")
                        {
                            SelectedGainSwitchCondition = "定位接近信号On";
                        }
                        else if (strGainSwitchCondition == "3")
                        {
                            SelectedGainSwitchCondition = "定位接近信号Off";
                        }
                        else if (strGainSwitchCondition == "4")
                        {
                            SelectedGainSwitchCondition = "位置指令输入Off且位置指令滤波输出为0";
                        }
                        else if (strGainSwitchCondition == "5")
                        {
                            SelectedGainSwitchCondition = "位置指令输入On";
                        }

                        strSpeedModeSwitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Mode Switch", "Default");
                        if (strSpeedModeSwitch == "0")
                        {
                            SelectedSpeedModeSwitch = "以内部转矩指令为条件";
                        }
                        else if (strSpeedModeSwitch == "1")
                        {
                            SelectedSpeedModeSwitch = "以速度指令为条件";
                        }
                        else if (strSpeedModeSwitch == "2")
                        {
                            SelectedSpeedModeSwitch = "以加速度为条件";
                        }
                        else if (strSpeedModeSwitch == "3")
                        {
                            SelectedSpeedModeSwitch = "以位置偏差为条件";
                        }
                        else if (strSpeedModeSwitch == "4")
                        {
                            SelectedSpeedModeSwitch = "无模式开关";
                        }

                        ModeSwitchTorqueValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Torque Value", "Default");
                        ModeSwitchSpeedValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Speed Value", "Default");
                        ModeSwitchAccValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Acc Value", "Default");                        
                        break;
                    case "1":
                        strVibrationSuppressionOption = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Default");
                        if (strVibrationSuppressionOption == "0")
                        {
                            strSelfTuningSet = "0";
                            strVibrationSuppressionASwitch = "0";
                        }
                        else if (strVibrationSuppressionOption == "1")
                        {
                            strSelfTuningSet = "0";
                            strVibrationSuppressionASwitch = "1";
                        }
                        else if (strVibrationSuppressionOption == "16")
                        {
                            strSelfTuningSet = "1";
                            strVibrationSuppressionASwitch = "0";
                        }
                        else if (strVibrationSuppressionOption == "17")
                        {
                            strSelfTuningSet = "1";
                            strVibrationSuppressionASwitch = "1";
                        }

                        if (strVibrationSuppressionASwitch == "0")
                        {
                            SelectedVibrationSuppressionASwitch = "关闭A型抑振";
                        }
                        else if (strVibrationSuppressionASwitch == "1")
                        {
                            SelectedVibrationSuppressionASwitch = "开启A型抑振";
                        }

                        if (strSelfTuningSet == "0")
                        {
                            SelectedSelfTuningSet = "自整定可设置";
                        }
                        else if (strSelfTuningSet == "1")
                        {
                            SelectedSelfTuningSet = "自整定不可设置";
                        }

                        VibsupFreq = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Default");
                        VibsupGainComp = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Default");
                        VibsupDampingGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Default");

                        strAdvancedApplicationSwitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Advanced Application Switch", "Default");
                        if (strAdvancedApplicationSwitch == "0")
                        {
                            strDisturbanceObserverSwitch = "0";
                            strSpeedObserverSwitch = "0";
                        }
                        else if (strAdvancedApplicationSwitch == "1")
                        {
                            strDisturbanceObserverSwitch = "0";
                            strSpeedObserverSwitch = "1";
                        }
                        else if (strAdvancedApplicationSwitch == "16")
                        {
                            strDisturbanceObserverSwitch = "1";
                            strSpeedObserverSwitch = "0";
                        }
                        else if (strAdvancedApplicationSwitch == "17")
                        {
                            strDisturbanceObserverSwitch = "1";
                            strSpeedObserverSwitch = "1";
                        }

                        if (strSpeedObserverSwitch == "0")
                        {
                            SelectedSpeedObserverSwitch = "关闭速度观测";
                        }
                        else if (strSpeedObserverSwitch == "1")
                        {
                            SelectedSpeedObserverSwitch = "开启速度观测";
                        }

                        if (strDisturbanceObserverSwitch == "0")
                        {
                            SelectedDisturbanceObserverSwitch = "关闭摩擦补偿";
                        }
                        else if (strDisturbanceObserverSwitch == "1")
                        {
                            SelectedDisturbanceObserverSwitch = "开启摩擦补偿";
                        }

                        DisturbanceObserverGainOne = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain", "Default");
                        DisturbanceObserverGainTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain 2", "Default");
                        DisturbanceObserverCoefficient = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Coefficient", "Default");
                        DisturbanceObserverFreqCorrection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Freq Correction", "Default");
                        DisturbanceObserverGainCorrection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain Correction", "Default");
                        SpeedObserverGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Gain", "Default");
                        SpeedObserverPosCompensationGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Pos Compensation Gain", "Default");

                        strEndVibrationSuppressionOption = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Option", "Default");
                        if (strEndVibrationSuppressionOption == "0")
                        {
                            SelectedEndVibrationSuppressionOption = "不进行末端抖动抑制";
                        }
                        else if (strEndVibrationSuppressionOption == "1")
                        {
                            SelectedEndVibrationSuppressionOption = "进行末端抖动抑制";
                        }

                        EndVibrationSuppressionFrequency = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Frequency", "Default");
                        EndVibrationSuppressionCompensation = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Compensation", "Default");
                        break;
                    case "2":
                        strModelFollowingControlSwitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Default");
                        if (strModelFollowingControlSwitch == "0")
                        {
                            strModelFollowingVibrationSuppressionSwitch = "0";
                            strModelFollowingSwitch = "0";
                        }
                        else if (strModelFollowingControlSwitch == "1")
                        {
                            strModelFollowingVibrationSuppressionSwitch = "0";
                            strModelFollowingSwitch = "1";
                        }
                        else if (strModelFollowingControlSwitch == "16")
                        {
                            strModelFollowingVibrationSuppressionSwitch = "1";
                            strModelFollowingSwitch = "0";
                        }
                        else if (strModelFollowingControlSwitch == "17")
                        {
                            strModelFollowingVibrationSuppressionSwitch = "1";
                            strModelFollowingSwitch = "1";
                        }

                        if (strModelFollowingSwitch == "0")
                        {
                            SelectedModelFollowingSwitch = "关闭模型追踪";
                        }
                        else if (strModelFollowingSwitch == "1")
                        {
                            SelectedModelFollowingSwitch = "开启模型追踪";
                        }

                        if (strModelFollowingVibrationSuppressionSwitch == "0")
                        {
                            SelectedModelFollowingVibrationSuppressionSwitch = "关闭模型抑振";
                        }
                        else if (strModelFollowingVibrationSuppressionSwitch == "1")
                        {
                            SelectedModelFollowingVibrationSuppressionSwitch = "开启模型抑振";
                        }

                        ModelFollowingControlGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Default");
                        MFCGainCorrection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Default");
                        MFCForwardBias = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Forward Bias", "Default");
                        MFCReverseBias = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Reverse Bias", "Default");
                        VibrationSuppressionFrequencyA = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Default");
                        VibrationSuppressionFrequencyB = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency B", "Default");
                        MFCVelocityFeedforwardCompensation = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Velocity Feedforward Compensation", "Default");
                        MFCGainTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain 2", "Default");
                        MFCGainCorrectionTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction 2", "Default");                        
                        break;
                    case "3":
                        WeakFieldControlGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Gain", "Default");
                        WeakFieldControlTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Time Constant", "Default");
                        WeakFieldMaxSpeedCorrespondingToIdRef = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Max Speed Corresponding To IdRef", "Default");
                        strWeakFieldControlSwitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Switch", "Default");
                        if (strWeakFieldControlSwitch == "0")
                        {
                            SelectedWeakFieldControlSwitch = "关闭弱磁";
                        }
                        else if (strWeakFieldControlSwitch == "1")
                        {
                            SelectedWeakFieldControlSwitch = "开启弱磁";
                        }                                                                                              
                        break;
                    default:
                        GainChangeTimeOne = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Time 1", "Default");
                        GainChangeTimeTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Time 2", "Default");
                        GainChangeWaitTimeOne = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Wait Time 1", "Default");
                        GainChangeWaitTimeTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Wait Time 2", "Default");
                        strGainChangeSwitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Switch", "Default");
                        if (strGainChangeSwitch == "1")
                        {
                            strGainSwitchCondition = "0";
                            strGainSwitch = "1";
                        }
                        else if (strGainChangeSwitch == "2")
                        {
                            strGainSwitchCondition = "0";
                            strGainSwitch = "2";
                        }
                        else if (strGainChangeSwitch == "17")
                        {
                            strGainSwitchCondition = "1";
                            strGainSwitch = "1";
                        }
                        else if (strGainChangeSwitch == "18")
                        {
                            strGainSwitchCondition = "1";
                            strGainSwitch = "2";
                        }
                        else if (strGainChangeSwitch == "33")
                        {
                            strGainSwitchCondition = "2";
                            strGainSwitch = "1";
                        }
                        else if (strGainChangeSwitch == "34")
                        {
                            strGainSwitchCondition = "2";
                            strGainSwitch = "2";
                        }
                        else if (strGainChangeSwitch == "49")
                        {
                            strGainSwitchCondition = "3";
                            strGainSwitch = "1";
                        }
                        else if (strGainChangeSwitch == "50")
                        {
                            strGainSwitchCondition = "3";
                            strGainSwitch = "2";
                        }
                        else if (strGainChangeSwitch == "65")
                        {
                            strGainSwitchCondition = "4";
                            strGainSwitch = "1";
                        }
                        else if (strGainChangeSwitch == "66")
                        {
                            strGainSwitchCondition = "4";
                            strGainSwitch = "2";
                        }
                        else if (strGainChangeSwitch == "81")
                        {
                            strGainSwitchCondition = "5";
                            strGainSwitch = "1";
                        }
                        else if (strGainChangeSwitch == "82")
                        {
                            strGainSwitchCondition = "5";
                            strGainSwitch = "2";
                        }

                        if (strGainSwitch == "1")
                        {
                            SelectedGainSwitch = "关闭增益切换";
                        }
                        else if (strGainSwitch == "2")
                        {
                            SelectedGainSwitch = "开启增益切换";
                        }

                        if (strGainSwitchCondition == "0")
                        {
                            SelectedGainSwitchCondition = "定位完成信号On";
                        }
                        else if (strGainSwitchCondition == "1")
                        {
                            SelectedGainSwitchCondition = "定位完成信号Off";
                        }
                        else if (strGainSwitchCondition == "2")
                        {
                            SelectedGainSwitchCondition = "定位接近信号On";
                        }
                        else if (strGainSwitchCondition == "3")
                        {
                            SelectedGainSwitchCondition = "定位接近信号Off";
                        }
                        else if (strGainSwitchCondition == "4")
                        {
                            SelectedGainSwitchCondition = "位置指令输入Off且位置指令滤波输出为0";
                        }
                        else if (strGainSwitchCondition == "5")
                        {
                            SelectedGainSwitchCondition = "位置指令输入On";
                        }

                        strSpeedModeSwitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Mode Switch", "Default");
                        if (strSpeedModeSwitch == "0")
                        {
                            SelectedSpeedModeSwitch = "以内部转矩指令为条件";
                        }
                        else if (strSpeedModeSwitch == "1")
                        {
                            SelectedSpeedModeSwitch = "以速度指令为条件";
                        }
                        else if (strSpeedModeSwitch == "2")
                        {
                            SelectedSpeedModeSwitch = "以加速度为条件";
                        }
                        else if (strSpeedModeSwitch == "3")
                        {
                            SelectedSpeedModeSwitch = "以位置偏差为条件";
                        }
                        else if (strSpeedModeSwitch == "4")
                        {
                            SelectedSpeedModeSwitch = "无模式开关";
                        }

                        ModeSwitchTorqueValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Torque Value", "Default");
                        ModeSwitchSpeedValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Speed Value", "Default");
                        ModeSwitchAccValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Acc Value", "Default");


                        strVibrationSuppressionOption = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Default");
                        if (strVibrationSuppressionOption == "0")
                        {
                            strSelfTuningSet = "0";
                            strVibrationSuppressionASwitch = "0";
                        }
                        else if (strVibrationSuppressionOption == "1")
                        {
                            strSelfTuningSet = "0";
                            strVibrationSuppressionASwitch = "1";
                        }
                        else if (strVibrationSuppressionOption == "16")
                        {
                            strSelfTuningSet = "1";
                            strVibrationSuppressionASwitch = "0";
                        }
                        else if (strVibrationSuppressionOption == "17")
                        {
                            strSelfTuningSet = "1";
                            strVibrationSuppressionASwitch = "1";
                        }

                        if (strVibrationSuppressionASwitch == "0")
                        {
                            SelectedVibrationSuppressionASwitch = "关闭A型抑振";
                        }
                        else if (strVibrationSuppressionASwitch == "1")
                        {
                            SelectedVibrationSuppressionASwitch = "开启A型抑振";
                        }

                        if (strSelfTuningSet == "0")
                        {
                            SelectedSelfTuningSet = "自整定可设置";
                        }
                        else if (strSelfTuningSet == "1")
                        {
                            SelectedSelfTuningSet = "自整定不可设置";
                        }

                        VibsupFreq = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Default");
                        VibsupGainComp = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Default");
                        VibsupDampingGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Default");

                        strAdvancedApplicationSwitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Advanced Application Switch", "Default");
                        if (strAdvancedApplicationSwitch == "0")
                        {
                            strDisturbanceObserverSwitch = "0";
                            strSpeedObserverSwitch = "0";
                        }
                        else if (strAdvancedApplicationSwitch == "1")
                        {
                            strDisturbanceObserverSwitch = "0";
                            strSpeedObserverSwitch = "1";
                        }
                        else if (strAdvancedApplicationSwitch == "16")
                        {
                            strDisturbanceObserverSwitch = "1";
                            strSpeedObserverSwitch = "0";
                        }
                        else if (strAdvancedApplicationSwitch == "17")
                        {
                            strDisturbanceObserverSwitch = "1";
                            strSpeedObserverSwitch = "1";
                        }

                        if (strSpeedObserverSwitch == "0")
                        {
                            SelectedSpeedObserverSwitch = "关闭速度观测";
                        }
                        else if (strSpeedObserverSwitch == "1")
                        {
                            SelectedSpeedObserverSwitch = "开启速度观测";
                        }

                        if (strDisturbanceObserverSwitch == "0")
                        {
                            SelectedDisturbanceObserverSwitch = "关闭摩擦补偿";
                        }
                        else if (strDisturbanceObserverSwitch == "1")
                        {
                            SelectedDisturbanceObserverSwitch = "开启摩擦补偿";
                        }

                        DisturbanceObserverGainOne = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain", "Default");
                        DisturbanceObserverGainTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain 2", "Default");
                        DisturbanceObserverCoefficient = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Coefficient", "Default");
                        DisturbanceObserverFreqCorrection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Freq Correction", "Default");
                        DisturbanceObserverGainCorrection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain Correction", "Default");
                        SpeedObserverGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Gain", "Default");
                        SpeedObserverPosCompensationGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Pos Compensation Gain", "Default");

                        strEndVibrationSuppressionOption = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Option", "Default");
                        if (strEndVibrationSuppressionOption == "0")
                        {
                            SelectedEndVibrationSuppressionOption = "不进行末端抖动抑制";
                        }
                        else if (strEndVibrationSuppressionOption == "1")
                        {
                            SelectedEndVibrationSuppressionOption = "进行末端抖动抑制";
                        }

                        EndVibrationSuppressionFrequency = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Frequency", "Default");
                        EndVibrationSuppressionCompensation = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Compensation", "Default");


                        strModelFollowingControlSwitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Default");
                        if (strModelFollowingControlSwitch == "0")
                        {
                            strModelFollowingVibrationSuppressionSwitch = "0";
                            strModelFollowingSwitch = "0";
                        }
                        else if (strModelFollowingControlSwitch == "1")
                        {
                            strModelFollowingVibrationSuppressionSwitch = "0";
                            strModelFollowingSwitch = "1";
                        }
                        else if (strModelFollowingControlSwitch == "16")
                        {
                            strModelFollowingVibrationSuppressionSwitch = "1";
                            strModelFollowingSwitch = "0";
                        }
                        else if (strModelFollowingControlSwitch == "17")
                        {
                            strModelFollowingVibrationSuppressionSwitch = "1";
                            strModelFollowingSwitch = "1";
                        }

                        if (strModelFollowingSwitch == "0")
                        {
                            SelectedModelFollowingSwitch = "关闭模型追踪";
                        }
                        else if (strModelFollowingSwitch == "1")
                        {
                            SelectedModelFollowingSwitch = "开启模型追踪";
                        }

                        if (strModelFollowingVibrationSuppressionSwitch == "0")
                        {
                            SelectedModelFollowingVibrationSuppressionSwitch = "关闭模型抑振";
                        }
                        else if (strModelFollowingVibrationSuppressionSwitch == "1")
                        {
                            SelectedModelFollowingVibrationSuppressionSwitch = "开启模型抑振";
                        }

                        ModelFollowingControlGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Default");
                        MFCGainCorrection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Default");
                        MFCForwardBias = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Forward Bias", "Default");
                        MFCReverseBias = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Reverse Bias", "Default");
                        VibrationSuppressionFrequencyA = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Default");
                        VibrationSuppressionFrequencyB = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency B", "Default");
                        MFCVelocityFeedforwardCompensation = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Velocity Feedforward Compensation", "Default");
                        MFCGainTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain 2", "Default");
                        MFCGainCorrectionTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction 2", "Default");


                        WeakFieldControlGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Gain", "Default");
                        WeakFieldControlTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Time Constant", "Default");
                        WeakFieldMaxSpeedCorrespondingToIdRef = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Max Speed Corresponding To IdRef", "Default");
                        strWeakFieldControlSwitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Switch", "Default");
                        if (strWeakFieldControlSwitch == "0")
                        {
                            SelectedWeakFieldControlSwitch = "关闭弱磁";
                        }
                        else if (strWeakFieldControlSwitch == "1")
                        {
                            SelectedWeakFieldControlSwitch = "开启弱磁";
                        }
                        break;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_GET_DEFAULT_PARAMETER, "GetDefaultAdvancedFeedbackParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：SaveAdvancedFeedbackConfigFile
        //函数功能：保存高级功能配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.16
        //*************************************************************************
        public void SaveAdvancedFeedbackConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                //选择配置文件存放位置
                iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.AdvancedFeedback);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //生成配置文件
                iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetAdvancedFeedbackConfigToDataTable(), ExcelType.AdvancedFeedback);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2002);
                }
                else
                {
                    ShowNotification(2003);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_SAVE_CONFIG_FILE, "SaveAdvancedFeedbackConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetAdvancedFeedbackConfigFile
        //函数功能：获取高级功能配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.16
        //*************************************************************************
        public void GetAdvancedFeedbackConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            DataTable dt = null;

            try
            {
                //获取配置文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(strFilePath, ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //判断是否导入正确的配置文件
                if (dt.Columns[3].ColumnName != FileInterface.ADVANCED)
                {
                    ShowNotification(2001);
                    return;
                }

                //导入参数
                iRet = GetAdvancedFeedbackConfigFromDataTable(dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //是否下载
                OthersHelper.GetWindowsStartupPosition();
                if (MessageBoxService.ShowMessage("是否下载当前导入的电流环参数...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    WriteAdvancedFeedbackParameter("All");
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_GET_CONFIG_FILE, "GetAdvancedFeedbackConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationAdvancedFeedbackParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.16
        //*************************************************************************
        public void EvaluationAdvancedFeedbackParameter()
        {
            string strGainChangeSwitch = null;
            string strGainSwitch = null;
            string strGainSwitchCondition = null;

            string strSpeedModeSwitch = null;

            string strVibrationSuppressionOption = null;
            string strVibrationSuppressionASwitch = null;
            string strSelfTuningSet = null;

            string strAdvancedApplicationSwitch = null;
            string strSpeedObserverSwitch = null;
            string strDisturbanceObserverSwitch = null;

            string strEndVibrationSuppressionOption = null;

            string strModelFollowingControlSwitch = null;
            string strModelFollowingSwitch = null;
            string strModelFollowingVibrationSuppressionSwitch = null;

            string strWeakFieldControlSwitch = null; 

            if (IsEvaluationAll)
            {
                //标志位更新
                IsEvaluationAll = false;

                //赋值
                #region 模式切换
                GainChangeTimeOne = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Time 1", "Index"));//增益切换时间1
                GainChangeTimeTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Time 2", "Index"));//增益切换时间2
                GainChangeWaitTimeOne = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Wait Time 1", "Index"));//增益切换等待时间1
                GainChangeWaitTimeTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Wait Time 2", "Index"));//增益切换等待时间2
                strGainChangeSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Switch", "Index"));//增益切换开关
                if (strGainChangeSwitch == "1")
                {
                    strGainSwitchCondition = "0";
                    strGainSwitch = "1";
                }
                else if (strGainChangeSwitch == "2")
                {
                    strGainSwitchCondition = "0";
                    strGainSwitch = "2";
                }
                else if (strGainChangeSwitch == "17")
                {
                    strGainSwitchCondition = "1";
                    strGainSwitch = "1";
                }
                else if (strGainChangeSwitch == "18")
                {
                    strGainSwitchCondition = "1";
                    strGainSwitch = "2";
                }
                else if (strGainChangeSwitch == "33")
                {
                    strGainSwitchCondition = "2";
                    strGainSwitch = "1";
                }
                else if (strGainChangeSwitch == "34")
                {
                    strGainSwitchCondition = "2";
                    strGainSwitch = "2";
                }
                else if (strGainChangeSwitch == "49")
                {
                    strGainSwitchCondition = "3";
                    strGainSwitch = "1";
                }
                else if (strGainChangeSwitch == "50")
                {
                    strGainSwitchCondition = "3";
                    strGainSwitch = "2";
                }
                else if (strGainChangeSwitch == "65")
                {
                    strGainSwitchCondition = "4";
                    strGainSwitch = "1";
                }
                else if (strGainChangeSwitch == "66")
                {
                    strGainSwitchCondition = "4";
                    strGainSwitch = "2";
                }
                else if (strGainChangeSwitch == "81")
                {
                    strGainSwitchCondition = "5";
                    strGainSwitch = "1";
                }
                else if (strGainChangeSwitch == "82")
                {
                    strGainSwitchCondition = "5";
                    strGainSwitch = "2";
                }

                switch (strGainSwitch)
                {
                    case "1":
                        SelectedGainSwitch = "关闭增益切换";
                        break;
                    case "2":
                        SelectedGainSwitch = "开启增益切换";
                        break;
                    default:
                        break;
                }

                switch (strGainSwitchCondition)
                {
                    case "0":
                        SelectedGainSwitchCondition = "定位完成信号On";
                        break;
                    case "1":
                        SelectedGainSwitchCondition = "定位完成信号Off";
                        break;
                    case "2":
                        SelectedGainSwitchCondition = "定位接近信号On";
                        break;
                    case "3":
                        SelectedGainSwitchCondition = "定位接近信号Off";
                        break;
                    case "4":
                        SelectedGainSwitchCondition = "位置指令输入Off且位置指令滤波输出为0";
                        break;
                    case "5":
                        SelectedGainSwitchCondition = "位置指令输入On";
                        break;
                    default:
                        break;
                }

                strSpeedModeSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Mode Switch", "Index"));//速度模式开关设置                
                switch (strSpeedModeSwitch)
                {
                    case "0":
                        SelectedSpeedModeSwitch = "以内部转矩指令为条件";
                        break;
                    case "1":
                        SelectedSpeedModeSwitch = "以速度指令为条件";
                        break;
                    case "2":
                        SelectedSpeedModeSwitch = "以加速度为条件";
                        break;
                    case "3":
                        SelectedSpeedModeSwitch = "以位置偏差为条件";
                        break;
                    case "4":
                        SelectedSpeedModeSwitch = "无模式开关";
                        break;
                    default:
                        break;
                }

                ModeSwitchTorqueValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Torque Value", "Index"));//模式开关（转矩指令）
                ModeSwitchSpeedValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Speed Value", "Index"));//模式开关（速度指令）
                ModeSwitchAccValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Acc Value", "Index"));//模式开关（加速度） 
                #endregion

                #region 振动抑制
                strVibrationSuppressionOption = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Index"));//A型抑振控制选择
                if (strVibrationSuppressionOption == "0")
                {
                    strSelfTuningSet = "0";
                    strVibrationSuppressionASwitch = "0";
                }
                else if (strVibrationSuppressionOption == "1")
                {
                    strSelfTuningSet = "0";
                    strVibrationSuppressionASwitch = "1";
                }
                else if (strVibrationSuppressionOption == "16")
                {
                    strSelfTuningSet = "1";
                    strVibrationSuppressionASwitch = "0";
                }
                else if (strVibrationSuppressionOption == "17")
                {
                    strSelfTuningSet = "1";
                    strVibrationSuppressionASwitch = "1";
                }

                switch (strVibrationSuppressionASwitch)
                {
                    case "0":
                        SelectedVibrationSuppressionASwitch = "关闭A型抑振";
                        break;
                    case "1":
                        SelectedVibrationSuppressionASwitch = "开启A型抑振";
                        break;
                    default:
                        break;
                }

                switch (strSelfTuningSet)
                {
                    case "0":
                        SelectedSelfTuningSet = "自整定可设置";
                        break;
                    case "1":
                        SelectedSelfTuningSet = "自整定不可设置";
                        break;
                    default:
                        break;
                }

                VibsupFreq = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Index"));//A型抑振频率
                VibsupGainComp = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Index"));//A型抑振增益补偿
                VibsupDampingGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Index"));//A型抑振阻尼增益

                strAdvancedApplicationSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Advanced Application Switch", "Index"));//高级应用开关
                if (strAdvancedApplicationSwitch == "0")
                {
                    strDisturbanceObserverSwitch = "0";
                    strSpeedObserverSwitch = "0";
                }
                else if (strAdvancedApplicationSwitch == "1")
                {
                    strDisturbanceObserverSwitch = "0";
                    strSpeedObserverSwitch = "1";
                }
                else if (strAdvancedApplicationSwitch == "16")
                {
                    strDisturbanceObserverSwitch = "1";
                    strSpeedObserverSwitch = "0";
                }
                else if (strAdvancedApplicationSwitch == "17")
                {
                    strDisturbanceObserverSwitch = "1";
                    strSpeedObserverSwitch = "1";
                }

                switch (strSpeedObserverSwitch)
                {
                    case "0":
                        SelectedSpeedObserverSwitch = "关闭速度观测";
                        break;
                    case "1":
                        SelectedSpeedObserverSwitch = "开启速度观测";
                        break;
                    default:
                        break;
                }

                switch (strDisturbanceObserverSwitch)
                {
                    case "0":
                        SelectedDisturbanceObserverSwitch = "关闭摩擦补偿";
                        break;
                    case "1":
                        SelectedDisturbanceObserverSwitch = "开启摩擦补偿";
                        break;
                    default:
                        break;
                }

                DisturbanceObserverGainOne = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain", "Index"));//摩擦补偿增益
                DisturbanceObserverGainTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain 2", "Index"));//摩擦补偿增益2
                DisturbanceObserverCoefficient = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Coefficient", "Index"));//摩擦补偿系数
                DisturbanceObserverFreqCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Freq Correction", "Index"));//摩擦补偿频率补偿
                DisturbanceObserverGainCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain Correction", "Index"));//摩擦补偿增益补偿
                SpeedObserverGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Gain", "Index"));//速度观测增益
                SpeedObserverPosCompensationGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Pos Compensation Gain", "Index"));//速度观测补偿增益

                strEndVibrationSuppressionOption = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Option", "Index"));//末端抖动抑制控制选择
                switch (strEndVibrationSuppressionOption)
                {
                    case "0":
                        SelectedEndVibrationSuppressionOption = "不进行末端抖动抑制";
                        break;
                    case "1":
                        SelectedEndVibrationSuppressionOption = "进行末端抖动抑制";
                        break;
                    default:
                        break;
                }

                EndVibrationSuppressionFrequency = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Frequency", "Index"));//末端抖动抑制频率
                EndVibrationSuppressionCompensation = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Compensation", "Index"));//末端抖动抑制补偿 
                #endregion

                #region 模型追踪
                strModelFollowingControlSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Index"));//模型追踪控制开关
                if (strModelFollowingControlSwitch == "0")
                {
                    strModelFollowingVibrationSuppressionSwitch = "0";
                    strModelFollowingSwitch = "0";
                }
                else if (strModelFollowingControlSwitch == "1")
                {
                    strModelFollowingVibrationSuppressionSwitch = "0";
                    strModelFollowingSwitch = "1";
                }
                else if (strModelFollowingControlSwitch == "16")
                {
                    strModelFollowingVibrationSuppressionSwitch = "1";
                    strModelFollowingSwitch = "0";
                }
                else if (strModelFollowingControlSwitch == "17")
                {
                    strModelFollowingVibrationSuppressionSwitch = "1";
                    strModelFollowingSwitch = "1";
                }

                switch (strModelFollowingSwitch)
                {
                    case "0":
                        SelectedModelFollowingSwitch = "关闭模型追踪";
                        break;
                    case "1":
                        SelectedModelFollowingSwitch = "开启模型追踪";
                        break;
                    default:
                        break;
                }

                switch (strModelFollowingVibrationSuppressionSwitch)
                {
                    case "0":
                        SelectedModelFollowingVibrationSuppressionSwitch = "关闭模型抑振";
                        break;
                    case "1":
                        SelectedModelFollowingVibrationSuppressionSwitch = "开启模型抑振";
                        break;
                    default:
                        break;
                }

                ModelFollowingControlGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Index"));//模型追踪控制增益
                MFCGainCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Index"));//模型追踪控制增益补偿
                MFCForwardBias = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Forward Bias", "Index"));//模型追踪控制增益偏置（正向）
                MFCReverseBias = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Reverse Bias", "Index"));//模型追踪控制增益偏置（反向）
                VibrationSuppressionFrequencyA = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Index"));//振动抑制1频率A
                VibrationSuppressionFrequencyB = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency B", "Index"));//振动抑制1频率B
                MFCVelocityFeedforwardCompensation = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Velocity Feedforward Compensation", "Index"));//模型追踪控制速度前馈补偿
                MFCGainTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain 2", "Index"));//第2模型追踪控制增益
                MFCGainCorrectionTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction 2", "Index"));//第2模型追踪控制增益补偿 
                #endregion

                #region 弱磁控制
                WeakFieldControlGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Gain", "Index"));//弱磁控制电压反馈增益
                WeakFieldControlTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Time Constant", "Index"));//弱磁控制电压反馈时间常数
                WeakFieldMaxSpeedCorrespondingToIdRef = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Max Speed Corresponding To IdRef", "Index"));//弱磁最大速度对应的Id指令
                strWeakFieldControlSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Switch", "Index"));//弱磁控制开关
                switch (strWeakFieldControlSwitch)
                {
                    case "0":
                        SelectedWeakFieldControlSwitch = "关闭弱磁";
                        break;
                    case "1":
                        SelectedWeakFieldControlSwitch = "开启弱磁";
                        break;
                    default:
                        break;
                } 
                #endregion

            }
            else
            {
                if (SelectedTabIndex == "0")
                {
                    #region 模式切换
                    GainChangeTimeOne = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Time 1", "Index"));//增益切换时间1
                    GainChangeTimeTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Time 2", "Index"));//增益切换时间2
                    GainChangeWaitTimeOne = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Wait Time 1", "Index"));//增益切换等待时间1
                    GainChangeWaitTimeTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Wait Time 2", "Index"));//增益切换等待时间2
                    strGainChangeSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Gain Change Switch", "Index"));//增益切换开关
                    if (strGainChangeSwitch == "1")
                    {
                        strGainSwitchCondition = "0";
                        strGainSwitch = "1";
                    }
                    else if (strGainChangeSwitch == "2")
                    {
                        strGainSwitchCondition = "0";
                        strGainSwitch = "2";
                    }
                    else if (strGainChangeSwitch == "17")
                    {
                        strGainSwitchCondition = "1";
                        strGainSwitch = "1";
                    }
                    else if (strGainChangeSwitch == "18")
                    {
                        strGainSwitchCondition = "1";
                        strGainSwitch = "2";
                    }
                    else if (strGainChangeSwitch == "33")
                    {
                        strGainSwitchCondition = "2";
                        strGainSwitch = "1";
                    }
                    else if (strGainChangeSwitch == "34")
                    {
                        strGainSwitchCondition = "2";
                        strGainSwitch = "2";
                    }
                    else if (strGainChangeSwitch == "49")
                    {
                        strGainSwitchCondition = "3";
                        strGainSwitch = "1";
                    }
                    else if (strGainChangeSwitch == "50")
                    {
                        strGainSwitchCondition = "3";
                        strGainSwitch = "2";
                    }
                    else if (strGainChangeSwitch == "65")
                    {
                        strGainSwitchCondition = "4";
                        strGainSwitch = "1";
                    }
                    else if (strGainChangeSwitch == "66")
                    {
                        strGainSwitchCondition = "4";
                        strGainSwitch = "2";
                    }
                    else if (strGainChangeSwitch == "81")
                    {
                        strGainSwitchCondition = "5";
                        strGainSwitch = "1";
                    }
                    else if (strGainChangeSwitch == "82")
                    {
                        strGainSwitchCondition = "5";
                        strGainSwitch = "2";
                    }

                    switch (strGainSwitch)
                    {
                        case "1":
                            SelectedGainSwitch = "关闭增益切换";
                            break;
                        case "2":
                            SelectedGainSwitch = "开启增益切换";
                            break;
                        default:
                            break;
                    }

                    switch (strGainSwitchCondition)
                    {
                        case "0":
                            SelectedGainSwitchCondition = "定位完成信号On";
                            break;
                        case "1":
                            SelectedGainSwitchCondition = "定位完成信号Off";
                            break;
                        case "2":
                            SelectedGainSwitchCondition = "定位接近信号On";
                            break;
                        case "3":
                            SelectedGainSwitchCondition = "定位接近信号Off";
                            break;
                        case "4":
                            SelectedGainSwitchCondition = "位置指令输入Off且位置指令滤波输出为0";
                            break;
                        case "5":
                            SelectedGainSwitchCondition = "位置指令输入On";
                            break;
                        default:
                            break;
                    }

                    strSpeedModeSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Mode Switch", "Index"));//速度模式开关设置                
                    switch (strSpeedModeSwitch)
                    {
                        case "0":
                            SelectedSpeedModeSwitch = "以内部转矩指令为条件";
                            break;
                        case "1":
                            SelectedSpeedModeSwitch = "以速度指令为条件";
                            break;
                        case "2":
                            SelectedSpeedModeSwitch = "以加速度为条件";
                            break;
                        case "3":
                            SelectedSpeedModeSwitch = "以位置偏差为条件";
                            break;
                        case "4":
                            SelectedSpeedModeSwitch = "无模式开关";
                            break;
                        default:
                            break;
                    }

                    ModeSwitchTorqueValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Torque Value", "Index"));//模式开关（转矩指令）
                    ModeSwitchSpeedValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Speed Value", "Index"));//模式开关（速度指令）
                    ModeSwitchAccValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Mode Switch Acc Value", "Index"));//模式开关（加速度） 
                    #endregion
                }
                else if (SelectedTabIndex == "1")
                {
                    #region 振动抑制
                    strVibrationSuppressionOption = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Index"));//A型抑振控制选择
                    if (strVibrationSuppressionOption == "0")
                    {
                        strSelfTuningSet = "0";
                        strVibrationSuppressionASwitch = "0";
                    }
                    else if (strVibrationSuppressionOption == "1")
                    {
                        strSelfTuningSet = "0";
                        strVibrationSuppressionASwitch = "1";
                    }
                    else if (strVibrationSuppressionOption == "16")
                    {
                        strSelfTuningSet = "1";
                        strVibrationSuppressionASwitch = "0";
                    }
                    else if (strVibrationSuppressionOption == "17")
                    {
                        strSelfTuningSet = "1";
                        strVibrationSuppressionASwitch = "1";
                    }

                    switch (strVibrationSuppressionASwitch)
                    {
                        case "0":
                            SelectedVibrationSuppressionASwitch = "关闭A型抑振";
                            break;
                        case "1":
                            SelectedVibrationSuppressionASwitch = "开启A型抑振";
                            break;
                        default:
                            break;
                    }

                    switch (strSelfTuningSet)
                    {
                        case "0":
                            SelectedSelfTuningSet = "自整定可设置";
                            break;
                        case "1":
                            SelectedSelfTuningSet = "自整定不可设置";
                            break;
                        default:
                            break;
                    }

                    VibsupFreq = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Index"));//A型抑振频率
                    VibsupGainComp = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Index"));//A型抑振增益补偿
                    VibsupDampingGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Index"));//A型抑振阻尼增益

                    strAdvancedApplicationSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Advanced Application Switch", "Index"));//高级应用开关
                    if (strAdvancedApplicationSwitch == "0")
                    {
                        strDisturbanceObserverSwitch = "0";
                        strSpeedObserverSwitch = "0";
                    }
                    else if (strAdvancedApplicationSwitch == "1")
                    {
                        strDisturbanceObserverSwitch = "0";
                        strSpeedObserverSwitch = "1";
                    }
                    else if (strAdvancedApplicationSwitch == "16")
                    {
                        strDisturbanceObserverSwitch = "1";
                        strSpeedObserverSwitch = "0";
                    }
                    else if (strAdvancedApplicationSwitch == "17")
                    {
                        strDisturbanceObserverSwitch = "1";
                        strSpeedObserverSwitch = "1";
                    }

                    switch (strSpeedObserverSwitch)
                    {
                        case "0":
                            SelectedSpeedObserverSwitch = "关闭速度观测";
                            break;
                        case "1":
                            SelectedSpeedObserverSwitch = "开启速度观测";
                            break;
                        default:
                            break;
                    }

                    switch (strDisturbanceObserverSwitch)
                    {
                        case "0":
                            SelectedDisturbanceObserverSwitch = "关闭摩擦补偿";
                            break;
                        case "1":
                            SelectedDisturbanceObserverSwitch = "开启摩擦补偿";
                            break;
                        default:
                            break;
                    }

                    DisturbanceObserverGainOne = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain", "Index"));//摩擦补偿增益
                    DisturbanceObserverGainTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain 2", "Index"));//摩擦补偿增益2
                    DisturbanceObserverCoefficient = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Coefficient", "Index"));//摩擦补偿系数
                    DisturbanceObserverFreqCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Freq Correction", "Index"));//摩擦补偿频率补偿
                    DisturbanceObserverGainCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Disturbance Observer Gain Correction", "Index"));//摩擦补偿增益补偿
                    SpeedObserverGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Gain", "Index"));//速度观测增益
                    SpeedObserverPosCompensationGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Pos Compensation Gain", "Index"));//速度观测补偿增益

                    strEndVibrationSuppressionOption = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Option", "Index"));//末端抖动抑制控制选择
                    switch (strEndVibrationSuppressionOption)
                    {
                        case "0":
                            SelectedEndVibrationSuppressionOption = "不进行末端抖动抑制";
                            break;
                        case "1":
                            SelectedEndVibrationSuppressionOption = "进行末端抖动抑制";
                            break;
                        default:
                            break;
                    }

                    EndVibrationSuppressionFrequency = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Frequency", "Index"));//末端抖动抑制频率
                    EndVibrationSuppressionCompensation = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Vibration Suppression Compensation", "Index"));//末端抖动抑制补偿 
                    #endregion
                }
                else if (SelectedTabIndex == "2")
                {
                    #region 模型追踪
                    strModelFollowingControlSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Index"));//模型追踪控制开关
                    if (strModelFollowingControlSwitch == "0")
                    {
                        strModelFollowingVibrationSuppressionSwitch = "0";
                        strModelFollowingSwitch = "0";
                    }
                    else if (strModelFollowingControlSwitch == "1")
                    {
                        strModelFollowingVibrationSuppressionSwitch = "0";
                        strModelFollowingSwitch = "1";
                    }
                    else if (strModelFollowingControlSwitch == "16")
                    {
                        strModelFollowingVibrationSuppressionSwitch = "1";
                        strModelFollowingSwitch = "0";
                    }
                    else if (strModelFollowingControlSwitch == "17")
                    {
                        strModelFollowingVibrationSuppressionSwitch = "1";
                        strModelFollowingSwitch = "1";
                    }

                    switch (strModelFollowingSwitch)
                    {
                        case "0":
                            SelectedModelFollowingSwitch = "关闭模型追踪";
                            break;
                        case "1":
                            SelectedModelFollowingSwitch = "开启模型追踪";
                            break;
                        default:
                            break;
                    }

                    switch (strModelFollowingVibrationSuppressionSwitch)
                    {
                        case "0":
                            SelectedModelFollowingVibrationSuppressionSwitch = "关闭模型抑振";
                            break;
                        case "1":
                            SelectedModelFollowingVibrationSuppressionSwitch = "开启模型抑振";
                            break;
                        default:
                            break;
                    }

                    ModelFollowingControlGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Index"));//模型追踪控制增益
                    MFCGainCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Index"));//模型追踪控制增益补偿
                    MFCForwardBias = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Forward Bias", "Index"));//模型追踪控制增益偏置（正向）
                    MFCReverseBias = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Reverse Bias", "Index"));//模型追踪控制增益偏置（反向）
                    VibrationSuppressionFrequencyA = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Index"));//振动抑制1频率A
                    VibrationSuppressionFrequencyB = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency B", "Index"));//振动抑制1频率B
                    MFCVelocityFeedforwardCompensation = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Velocity Feedforward Compensation", "Index"));//模型追踪控制速度前馈补偿
                    MFCGainTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain 2", "Index"));//第2模型追踪控制增益
                    MFCGainCorrectionTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction 2", "Index"));//第2模型追踪控制增益补偿 
                    #endregion
                }
                else if (SelectedTabIndex == "3")
                {
                    #region 弱磁控制
                    WeakFieldControlGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Gain", "Index"));//弱磁控制电压反馈增益
                    WeakFieldControlTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Time Constant", "Index"));//弱磁控制电压反馈时间常数
                    WeakFieldMaxSpeedCorrespondingToIdRef = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Max Speed Corresponding To IdRef", "Index"));//弱磁最大速度对应的Id指令
                    strWeakFieldControlSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Weak Field Control Switch", "Index"));//弱磁控制开关
                    switch (strWeakFieldControlSwitch)
                    {
                        case "0":
                            SelectedWeakFieldControlSwitch = "关闭弱磁";
                            break;
                        case "1":
                            SelectedWeakFieldControlSwitch = "开启弱磁";
                            break;
                        default:
                            break;
                    } 
                    #endregion
                }
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.16
        //*************************************************************************
        public void OnGainChangeTimeOneChanged() { GlobalCurrentInput.GainChangeTimeOne = GainChangeTimeOne; }//增益切换时间1
        public void OnGainChangeTimeTwoChanged() { GlobalCurrentInput.GainChangeTimeTwo = GainChangeTimeTwo; }//增益切换时间2
        public void OnGainChangeWaitTimeOneChanged() { GlobalCurrentInput.GainChangeWaitTimeOne = GainChangeWaitTimeOne; }//增益切换等待时间1
        public void OnGainChangeWaitTimeTwoChanged() { GlobalCurrentInput.GainChangeWaitTimeTwo = GainChangeWaitTimeTwo; }//增益切换等待时间2        
        public void OnSelectedGainSwitchChanged() { GlobalCurrentInput.SelectedGainSwitch = SelectedGainSwitch; }//增益切换开关[0-3]bit
        public void OnSelectedGainSwitchConditionChanged() { GlobalCurrentInput.SelectedGainSwitchCondition = SelectedGainSwitchCondition; }//增益切换开关[4-8]bit

        public void OnSelectedSpeedModeSwitchChanged() { GlobalCurrentInput.SelectedSpeedModeSwitch = SelectedSpeedModeSwitch; }//速度模式开关设置
        public void OnModeSwitchTorqueValueChanged() { GlobalCurrentInput.ModeSwitchTorqueValue = ModeSwitchTorqueValue; }//模式开关（转矩指令）
        public void OnModeSwitchSpeedValueChanged() { GlobalCurrentInput.ModeSwitchSpeedValue = ModeSwitchSpeedValue; }//模式开关（速度指令）
        public void OnModeSwitchAccValueChanged() { GlobalCurrentInput.ModeSwitchAccValue = ModeSwitchAccValue; }//模式开关（加速度）

        public void OnSelectedVibrationSuppressionASwitchChanged() { GlobalCurrentInput.SelectedVibrationSuppressionASwitch = SelectedVibrationSuppressionASwitch; }//A型抑振控制选择[0-3]bit
        public void OnSelectedSelfTuningSetChanged() { GlobalCurrentInput.SelectedSelfTuningSet = SelectedSelfTuningSet; }//A型抑振控制选择[4-7]bit
        public void OnVibsupFreqChanged() { GlobalCurrentInput.VibsupFreq_Advanced = VibsupFreq; }//A型抑振频率
        public void OnVibsupGainCompChanged() { GlobalCurrentInput.VibsupGainComp_Advanced = VibsupGainComp; }//A型抑振增益补偿
        public void OnVibsupDampingGainChanged() { GlobalCurrentInput.VibsupDampingGain_Advanced = VibsupDampingGain; }//A型抑振阻尼增益
        public void OnSelectedSpeedObserverSwitchChanged() { GlobalCurrentInput.SelectedSpeedObserverSwitch = SelectedSpeedObserverSwitch; }//高级应用开关[0-3]bit
        public void OnSelectedDisturbanceObserverSwitchChanged() { GlobalCurrentInput.SelectedDisturbanceObserverSwitch = SelectedDisturbanceObserverSwitch; }//高级应用开关[4-7]bit
        public void OnDisturbanceObserverGainOneChanged() { GlobalCurrentInput.DisturbanceObserverGainOne = DisturbanceObserverGainOne; }//摩擦补偿增益
        public void OnDisturbanceObserverGainTwoChanged() { GlobalCurrentInput.DisturbanceObserverGainTwo = DisturbanceObserverGainTwo; }//摩擦补偿增益2
        public void OnDisturbanceObserverCoefficientChanged() { GlobalCurrentInput.DisturbanceObserverCoefficient = DisturbanceObserverCoefficient; }//摩擦补偿系数
        public void OnDisturbanceObserverFreqCorrectionChanged() { GlobalCurrentInput.DisturbanceObserverFreqCorrection = DisturbanceObserverFreqCorrection; }//摩擦补偿频率补偿
        public void OnDisturbanceObserverGainCorrectionChanged() { GlobalCurrentInput.DisturbanceObserverGainCorrection = DisturbanceObserverGainCorrection; }//摩擦补偿增益补偿
        public void OnSpeedObserverGainChanged() { GlobalCurrentInput.SpeedObserverGain_Advanced = SpeedObserverGain; }//速度观测增益
        public void OnSpeedObserverPosCompensationGainChanged() { GlobalCurrentInput.SpeedObserverPosCompensationGain_Advanced = SpeedObserverPosCompensationGain; }//速度观测补偿增益
        public void OnSelectedEndVibrationSuppressionOptionChanged() { GlobalCurrentInput.SelectedEndVibrationSuppressionOption = SelectedEndVibrationSuppressionOption; }//末端抖动抑制控制选择
        public void OnEndVibrationSuppressionFrequencyChanged() { GlobalCurrentInput.EndVibrationSuppressionFrequency = EndVibrationSuppressionFrequency; }//末端抖动抑制频率
        public void OnEndVibrationSuppressionCompensationChanged() { GlobalCurrentInput.EndVibrationSuppressionCompensation = EndVibrationSuppressionCompensation; }//末端抖动抑制补偿

        public void OnSelectedModelFollowingSwitchChanged() { GlobalCurrentInput.SelectedModelFollowingSwitch = SelectedModelFollowingSwitch; }//模型追踪控制开关[0-3]bit
        public void OnSelectedModelFollowingVibrationSuppressionSwitchChanged() { GlobalCurrentInput.SelectedModelFollowingVibrationSuppressionSwitch = SelectedModelFollowingVibrationSuppressionSwitch; }//模型追踪控制开关[4-7]bit
        public void OnModelFollowingControlGainChanged() { GlobalCurrentInput.ModelFollowingControlGain_Advanced = ModelFollowingControlGain; }//模型追踪控制增益
        public void OnMFCGainCorrectionChanged() { GlobalCurrentInput.MFCGainCorrection_Advanced = MFCGainCorrection; }//模型追踪控制增益补偿
        public void OnMFCForwardBiasChanged() { GlobalCurrentInput.MFCForwardBias = MFCForwardBias; }//模型追踪控制增益偏置(正向)
        public void OnMFCReverseBiasChanged() { GlobalCurrentInput.MFCReverseBias = MFCReverseBias; }//模型追踪控制增益偏置（反向）
        public void OnVibrationSuppressionFrequencyAChanged() { GlobalCurrentInput.VibrationSuppressionFrequencyA_Advanced = VibrationSuppressionFrequencyA; }//振动抑制1频率A
        public void OnVibrationSuppressionFrequencyBChanged() { GlobalCurrentInput.VibrationSuppressionFrequencyB = VibrationSuppressionFrequencyB; }//振动抑制1频率B
        public void OnMFCVelocityFeedforwardCompensationChanged() { GlobalCurrentInput.MFCVelocityFeedforwardCompensation = MFCVelocityFeedforwardCompensation; }//模型追踪控制速度前馈补偿
        public void OnMFCGainTwoChanged() { GlobalCurrentInput.MFCGainTwo = MFCGainTwo; }//第2模型追踪控制增益
        public void OnMFCGainCorrectionTwoChanged() { GlobalCurrentInput.MFCGainCorrectionTwo = MFCGainCorrectionTwo; }//第2模型追踪控制增益补偿
        public void OnWeakFieldControlGainChanged() { GlobalCurrentInput.WeakFieldControlGain = WeakFieldControlGain; }//弱磁控制电压反馈增益
        public void OnWeakFieldControlTimeConstantChanged() { GlobalCurrentInput.WeakFieldControlTimeConstant = WeakFieldControlTimeConstant; }//弱磁控制电压反馈时间常数
        public void OnWeakFieldMaxSpeedCorrespondingToIdRefChanged() { GlobalCurrentInput.WeakFieldMaxSpeedCorrespondingToIdRef = WeakFieldMaxSpeedCorrespondingToIdRef; }//弱磁最大速度对应的Id指令
        public void OnSelectedWeakFieldControlSwitchChanged() { GlobalCurrentInput.SelectedWeakFieldControlSwitch = SelectedWeakFieldControlSwitch; }//弱磁控制开关
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：BackgroundInitialize
        //函数功能：背景初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.16
        //*************************************************************************
        //private void BackgroundInitialize()
        //{
        //    FirstTrqcmdFilterTimeBackground = BackgroundState.Selected;//第一转矩指令滤波时间参数
        //    SecondTrqcmdFilterFreqBackground = BackgroundState.NotSelected;//第二转矩指令滤波器频率
        //    SecondTrqcmdFilterQBackground = BackgroundState.NotSelected;//第二转矩指令滤波器Q值
        //}

        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：属性初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.15
        //*************************************************************************
        private void ComboBoxInitialize()
        {
            try
            {
                GainSwitch = new ObservableCollection<string>() { "关闭增益切换", "开启增益切换" };
                SelectedGainSwitch = "关闭增益切换";

                GainSwitchCondition = new ObservableCollection<string>() { "定位完成信号On", "定位完成信号Off", "定位接近信号On", "定位接近信号Off", "位置指令输入Off且位置指令滤波输出为0", "位置指令输入On" };
                SelectedGainSwitchCondition = "定位完成信号On";

                VibrationSuppressionASwitch = new ObservableCollection<string>() { "关闭A型抑振", "开启A型抑振" };
                SelectedVibrationSuppressionASwitch = "开启A型抑振";

                SelfTuningSet = new ObservableCollection<string>() { "自整定可设置", "自整定不可设置" };
                SelectedSelfTuningSet = "自整定不可设置";

                SpeedObserverSwitch = new ObservableCollection<string>() { "关闭速度观测", "开启速度观测" };
                SelectedSpeedObserverSwitch = "关闭速度观测";

                DisturbanceObserverSwitch = new ObservableCollection<string>() { "关闭摩擦补偿", "开启摩擦补偿" };
                SelectedDisturbanceObserverSwitch = "关闭摩擦补偿";

                EndVibrationSuppressionOption = new ObservableCollection<string>() { "不进行末端抖动抑制", "进行末端抖动抑制" };
                SelectedEndVibrationSuppressionOption = "进行末端抖动抑制";

                ModelFollowingSwitch = new ObservableCollection<string>() { "关闭模型追踪", "开启模型追踪" };
                SelectedModelFollowingSwitch = "开启模型追踪";

                ModelFollowingVibrationSuppressionSwitch = new ObservableCollection<string>() { "关闭模型抑振", "开启模型抑振" };
                SelectedModelFollowingVibrationSuppressionSwitch = "关闭模型抑振";


                SpeedModeSwitch = new ObservableCollection<string>() { "以内部转矩指令为条件", "以速度指令为条件", "以加速度为条件", "以位置偏差为条件", "无模式开关" };
                SelectedSpeedModeSwitch = "以内部转矩指令为条件";

                WeakFieldControlSwitch = new ObservableCollection<string>() { "关闭弱磁", "开启弱磁" };
                SelectedWeakFieldControlSwitch = "开启弱磁";
                
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_PROPERTY_INITIALIZE, "ComboBoxInitialize", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetAdvancedFeedbackConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.16
        //*************************************************************************
        private DataTable GetAdvancedFeedbackConfigToDataTable()
        {
            DataTable dt = new DataTable();

            try
            {
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Gain Change Time 1", "增益切换时间1", GainChangeTimeOne, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Gain Change Time 2", "增益切换时间2", GainChangeTimeTwo, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Gain Change Wait Time 1 ", "增益切换等待时间1", GainChangeWaitTimeOne, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Gain Change Wait Time 2", "增益切换等待时间2", GainChangeWaitTimeTwo, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Gain Change Switch", "增益切换开关", SelectedGainSwitchCondition + SelectedGainSwitch, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Speed Mode Switch", "速度模式开关设置", SelectedSpeedModeSwitch, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Mode Switch Torque Value", "模式开关（转矩指令）", ModeSwitchTorqueValue, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Mode Switch Speed Value", "模式开关（速度指令）", ModeSwitchSpeedValue, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Mode Switch Acc Value", "模式开关（加速度）", ModeSwitchAccValue, ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Vibration Suppression Option", "A型抑振控制选择", SelectedSelfTuningSet + SelectedVibrationSuppressionASwitch, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Vibsup Freq", "A型抑振频率", VibsupFreq, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Vibsup Gain Comp", "A型抑振增益补偿", VibsupGainComp, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Vibsup Damping Gain", "A型抑振阻尼增益", VibsupDampingGain, ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Advanced Application Switch", "高级应用开关", SelectedDisturbanceObserverSwitch + SelectedSpeedObserverSwitch, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Disturbance Observer Gain", "摩擦补偿增益", DisturbanceObserverGainOne, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Disturbance Observer Gain 2", "摩擦补偿增益2", DisturbanceObserverGainTwo, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Disturbance Observer Coefficient", "摩擦补偿系数", DisturbanceObserverCoefficient, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Disturbance Observer Freq Correction", "摩擦补偿频率补偿", DisturbanceObserverFreqCorrection, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Disturbance Observer Gain Correction", "摩擦补偿增益补偿", DisturbanceObserverGainCorrection, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Speed Observer Gain", "速度观测增益", SpeedObserverGain, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Speed Observer Pos Compensation Gain", "速度观测补偿增益", SpeedObserverPosCompensationGain, ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "End Vibration Suppression Option", "末端抖动抑制控制选择", SelectedEndVibrationSuppressionOption, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "End Vibration Suppression Frequency", "末端抖动抑制频率", EndVibrationSuppressionFrequency, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "End Vibration Suppression Compensation", "末端抖动抑制补偿", EndVibrationSuppressionCompensation, ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Model Following Control Switch", "模型追踪控制开关", SelectedModelFollowingVibrationSuppressionSwitch + SelectedModelFollowingSwitch, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Model Following Control Gain", "模型追踪控制增益", ModelFollowingControlGain, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "MFC Gain Correction", "模型追踪控制增益补偿", MFCGainCorrection, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "MFC Forward Bias", "模型追踪控制增益偏置（正向）", MFCForwardBias, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "MFC Reverse Bias", "模型追踪控制增益偏置（反向）", MFCReverseBias, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Vibration Suppression 1 Frequency A", "振动抑制1频率A", VibrationSuppressionFrequencyA, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Vibration Suppression 1 Frequency B", "振动抑制1频率B", VibrationSuppressionFrequencyB, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "MFC Velocity Feedforward Compensation", "模型追踪控制速度前馈补偿", MFCVelocityFeedforwardCompensation, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "MFC Gain 2", "第2模型追踪控制增益", MFCGainTwo, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "MFC Gain Correction 2", "第2模型追踪控制增益补偿", MFCGainCorrectionTwo, ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Weak Field Control Gain", "弱磁控制电压反馈增益", WeakFieldControlGain, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Weak Field Control Time Constant", "弱磁控制电压反馈时间常数", WeakFieldControlTimeConstant, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Weak Field Max Speed Corresponding To IdRef", "弱磁最大速度对应的Id指令", WeakFieldMaxSpeedCorrespondingToIdRef, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.ADVANCED, "Weak Field Control Switch", "弱磁控制开关", SelectedWeakFieldControlSwitch, ref dt);

                return dt;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_GET_CONFIG_TO_DATATABLE, "GetAdvancedFeedbackConfigToDataTable", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetCurrentLoopConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.16
        //*************************************************************************
        private int GetAdvancedFeedbackConfigFromDataTable(DataTable dt)
        {
            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                GainChangeTimeOne = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Gain Change Time 1", "Default");
                GainChangeTimeTwo = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Gain Change Time 2", "Default");
                GainChangeWaitTimeOne = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Gain Change Wait Time 1", "Default");
                GainChangeWaitTimeTwo = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Gain Change Wait Time 2", "Default");

                SelectedGainSwitch = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Gain Change Switch", "Default");
                SelectedGainSwitchCondition = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Gain Change Switch", "Default");

                SelectedSpeedModeSwitch = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Mode Switch", "Default");
                ModeSwitchTorqueValue = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Mode Switch Torque Value", "Default");
                ModeSwitchSpeedValue = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Mode Switch Speed Value", "Default");
                ModeSwitchAccValue = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Mode Switch Acc Value", "Default");

                SelectedVibrationSuppressionASwitch = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibration Suppression Option", "Default");
                SelectedSelfTuningSet = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibration Suppression Option", "Default");
                VibsupFreq = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibsup Freq", "Default");
                VibsupGainComp = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibsup Gain Comp", "Default");
                VibsupDampingGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibsup Damping Gain", "Default");

                SelectedSpeedObserverSwitch = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Advanced Application Switch", "Default");
                SelectedDisturbanceObserverSwitch = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Advanced Application Switch", "Default");
                DisturbanceObserverGainOne = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Disturbance Observer Gain", "Default");
                DisturbanceObserverGainTwo = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Disturbance Observer Gain 2", "Default");
                DisturbanceObserverCoefficient = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Disturbance Observer Coefficient", "Default");
                DisturbanceObserverFreqCorrection = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Disturbance Observer Freq Correction", "Default");
                DisturbanceObserverGainCorrection = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Disturbance Observer Gain Correction", "Default");
                SpeedObserverGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Observer Gain", "Default");
                SpeedObserverPosCompensationGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Observer Pos Compensation Gain", "Default");

                SelectedEndVibrationSuppressionOption = OthersHelper.GetCellValueFromDataTable(dt, "Name", "End Vibration Suppression Option", "Default");
                EndVibrationSuppressionFrequency = OthersHelper.GetCellValueFromDataTable(dt, "Name", "End Vibration Suppression Frequency", "Default");
                EndVibrationSuppressionCompensation = OthersHelper.GetCellValueFromDataTable(dt, "Name", "End Vibration Suppression Compensation", "Default");

                SelectedModelFollowingSwitch = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Model Following Control Switch", "Default");
                SelectedModelFollowingVibrationSuppressionSwitch = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Model Following Control Switch", "Default");
                ModelFollowingControlGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Model Following Control Gain", "Default");
                MFCGainCorrection = OthersHelper.GetCellValueFromDataTable(dt, "Name", "MFC Gain Correction", "Default");
                MFCForwardBias = OthersHelper.GetCellValueFromDataTable(dt, "Name", "MFC Forward Bias", "Default");
                MFCReverseBias = OthersHelper.GetCellValueFromDataTable(dt, "Name", "MFC Reverse Bias", "Default");
                VibrationSuppressionFrequencyA = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibration Suppression 1 Frequency A", "Default");
                VibrationSuppressionFrequencyB = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibration Suppression 1 Frequency B", "Default");
                MFCVelocityFeedforwardCompensation = OthersHelper.GetCellValueFromDataTable(dt, "Name", "MFC Velocity Feedforward Compensation", "Default");
                MFCGainTwo = OthersHelper.GetCellValueFromDataTable(dt, "Name", "MFC Gain 2", "Default");
                MFCGainCorrectionTwo = OthersHelper.GetCellValueFromDataTable(dt, "Name", "MFC Gain Correction 2", "Default");

                WeakFieldControlGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Weak Field Control Gain", "Default");
                WeakFieldControlTimeConstant = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Weak Field Control Time Constant", "Default");
                WeakFieldMaxSpeedCorrespondingToIdRef = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Weak Field Max Speed Corresponding To IdRef", "Default");
                SelectedWeakFieldControlSwitch = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Weak Field Control Switch", "Default");

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_GET_CONFIG_FROM_DATATABLE, "GetAdvancedFeedbackConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.16
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                GainChangeTimeOne = GlobalCurrentInput.GainChangeTimeOne;//增益切换时间1
                GainChangeTimeTwo = GlobalCurrentInput.GainChangeTimeTwo;//增益切换时间2
                GainChangeWaitTimeOne = GlobalCurrentInput.GainChangeWaitTimeOne;//增益切换等待时间1
                GainChangeWaitTimeTwo = GlobalCurrentInput.GainChangeWaitTimeTwo;//增益切换等待时间2
                SelectedGainSwitch = GlobalCurrentInput.SelectedGainSwitch;//增益切换开关[0-3]bit
                SelectedGainSwitchCondition = GlobalCurrentInput.SelectedGainSwitchCondition;//增益切换开关[4-8]bit
                SelectedSpeedModeSwitch = GlobalCurrentInput.SelectedSpeedModeSwitch;//速度模式开关设置
                ModeSwitchTorqueValue = GlobalCurrentInput.ModeSwitchTorqueValue;//模式开关（转矩指令）
                ModeSwitchSpeedValue = GlobalCurrentInput.ModeSwitchSpeedValue;//模式开关（速度指令）
                ModeSwitchAccValue = GlobalCurrentInput.ModeSwitchAccValue;//模式开关（加速度）

                SelectedVibrationSuppressionASwitch = GlobalCurrentInput.SelectedVibrationSuppressionASwitch;//A型抑振控制选择[0-3]bit
                SelectedSelfTuningSet = GlobalCurrentInput.SelectedSelfTuningSet;//A型抑振控制选择[4-7]bit
                VibsupFreq = GlobalCurrentInput.VibsupFreq_Advanced;//A型抑振频率
                VibsupGainComp = GlobalCurrentInput.VibsupGainComp_Advanced;//A型抑振增益补偿
                VibsupDampingGain = GlobalCurrentInput.VibsupDampingGain_Advanced;//A型抑振阻尼增益
                SelectedSpeedObserverSwitch = GlobalCurrentInput.SelectedSpeedObserverSwitch;//高级应用开关[0-3]bit
                SelectedDisturbanceObserverSwitch = GlobalCurrentInput.SelectedDisturbanceObserverSwitch;//高级应用开关[4-7]bit
                DisturbanceObserverGainOne = GlobalCurrentInput.DisturbanceObserverGainOne;//摩擦补偿增益
                DisturbanceObserverGainTwo = GlobalCurrentInput.DisturbanceObserverGainTwo;//摩擦补偿增益2
                DisturbanceObserverCoefficient = GlobalCurrentInput.DisturbanceObserverCoefficient;//摩擦补偿系数
                DisturbanceObserverFreqCorrection = GlobalCurrentInput.DisturbanceObserverFreqCorrection;//摩擦补偿频率补偿
                DisturbanceObserverGainCorrection = GlobalCurrentInput.DisturbanceObserverGainCorrection;//摩擦补偿增益补偿
                SpeedObserverGain = GlobalCurrentInput.SpeedObserverGain_Advanced;//速度观测增益
                SpeedObserverPosCompensationGain = GlobalCurrentInput.SpeedObserverPosCompensationGain_Advanced;//速度观测补偿增益
                SelectedEndVibrationSuppressionOption = GlobalCurrentInput.SelectedEndVibrationSuppressionOption;//末端抖动抑制控制选择
                EndVibrationSuppressionFrequency = GlobalCurrentInput.EndVibrationSuppressionFrequency;//末端抖动抑制频率
                EndVibrationSuppressionCompensation = GlobalCurrentInput.EndVibrationSuppressionCompensation;//末端抖动抑制补偿

                SelectedModelFollowingSwitch = GlobalCurrentInput.SelectedModelFollowingSwitch;//模型追踪控制开关[0-3]bit
                SelectedModelFollowingVibrationSuppressionSwitch = GlobalCurrentInput.SelectedModelFollowingVibrationSuppressionSwitch;//模型追踪控制开关[4-7]bit
                ModelFollowingControlGain = GlobalCurrentInput.ModelFollowingControlGain_Advanced;//模型追踪控制增益
                MFCGainCorrection = GlobalCurrentInput.MFCGainCorrection_Advanced;//模型追踪控制增益补偿
                MFCForwardBias = GlobalCurrentInput.MFCForwardBias;//模型追踪控制增益偏置(正向)
                MFCReverseBias = GlobalCurrentInput.MFCReverseBias;//模型追踪控制增益偏置(反向)
                VibrationSuppressionFrequencyA = GlobalCurrentInput.VibrationSuppressionFrequencyA_Advanced;//振动抑制1频率A
                VibrationSuppressionFrequencyB = GlobalCurrentInput.VibrationSuppressionFrequencyB;//振动抑制1频率B
                MFCVelocityFeedforwardCompensation = GlobalCurrentInput.MFCVelocityFeedforwardCompensation;//模型追踪控制速度前馈补偿
                MFCGainTwo = GlobalCurrentInput.MFCGainTwo;//第2模型追踪控制增益
                MFCGainCorrectionTwo = GlobalCurrentInput.MFCGainCorrectionTwo;//第2模型追踪控制增益补偿

                WeakFieldControlGain = GlobalCurrentInput.WeakFieldControlGain;//弱磁控制电压反馈增益
                WeakFieldControlTimeConstant = GlobalCurrentInput.WeakFieldControlTimeConstant;//弱磁控制电压反馈时间常数
                WeakFieldMaxSpeedCorrespondingToIdRef = GlobalCurrentInput.WeakFieldMaxSpeedCorrespondingToIdRef;//弱磁最大速度对应的Id指令
                SelectedWeakFieldControlSwitch = GlobalCurrentInput.SelectedWeakFieldControlSwitch;//弱磁控制开关

            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.15
        //*************************************************************************
        private int AddParameterInfoDictionary(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            string strGainChangeSwitch = null;
            string strGainSwitch = null;
            string strGainSwitchCondition = null;

            string strSpeedModeSwitch = null;

            string strVibrationSuppressionOption = null;
            string strVibrationSuppressionASwitch = null;
            string strSelfTuningSet = null;

            string strAdvancedApplicationSwitch = null;
            string strSpeedObserverSwitch = null;
            string strDisturbanceObserverSwitch = null;

            string strEndVibrationSuppressionOption = null;

            string strModelFollowingControlSwitch = null;
            string strModelFollowingSwitch = null;
            string strModelFollowingVibrationSuppressionSwitch = null;

            string strWeakFieldControlSwitch = null;

            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                //增益切换开关
                switch (SelectedGainSwitch)
                {
                    case "关闭增益切换":
                        strGainSwitch = "1";
                        break;
                    case "开启增益切换":
                        strGainSwitch = "2";
                        break;
                    default:
                        break;
                }
                switch (SelectedGainSwitchCondition)
                {
                    case "定位完成信号On":
                        strGainSwitchCondition = "0";
                        break;
                    case "定位完成信号Off":
                        strGainSwitchCondition = "1";
                        break;
                    case "定位接近信号On":
                        strGainSwitchCondition = "2";
                        break;
                    case "定位接近信号Off":
                        strGainSwitchCondition = "3";
                        break;
                    case "位置指令输入Off且位置指令滤波输出为0":
                        strGainSwitchCondition = "4";
                        break;
                    case "位置指令输入On":
                        strGainSwitchCondition = "5";
                        break;
                    default:
                        break;
                }
                if (strGainSwitchCondition == "0" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "1";
                }
                else if (strGainSwitchCondition == "0" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "2";
                }
                else if (strGainSwitchCondition == "1" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "17";
                }
                else if (strGainSwitchCondition == "1" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "18";
                }
                else if (strGainSwitchCondition == "2" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "33";
                }
                else if (strGainSwitchCondition == "2" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "34";
                }
                else if (strGainSwitchCondition == "3" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "49";
                }
                else if (strGainSwitchCondition == "3" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "50";
                }
                else if (strGainSwitchCondition == "4" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "65";
                }
                else if (strGainSwitchCondition == "4" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "66";
                }
                else if (strGainSwitchCondition == "5" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "81";
                }
                else if (strGainSwitchCondition == "5" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "82";
                }
                //strGainChangeSwitch = strGainSwitchCondition + strGainSwitch;

                switch (SelectedSpeedModeSwitch)
                {
                    case "以内部转矩指令为条件":
                        strSpeedModeSwitch = "0";
                        break;
                    case "以速度指令为条件":
                        strSpeedModeSwitch = "1";
                        break;
                    case "以加速度为条件":
                        strSpeedModeSwitch = "2";
                        break;
                    case "以位置偏差为条件":
                        strSpeedModeSwitch = "3";
                        break;
                    case "无模式开关":
                        strSpeedModeSwitch = "4";
                        break;                    
                    default:
                        break;
                }

                //A型抑振控制选择
                switch (SelectedVibrationSuppressionASwitch)
                {
                    case "关闭A型抑振":
                        strVibrationSuppressionASwitch = "0";
                        break;
                    case "开启A型抑振":
                        strVibrationSuppressionASwitch = "1";
                        break;
                    default:
                        break;
                }
                switch (SelectedSelfTuningSet)
                {
                    case "自整定可设置":
                        strSelfTuningSet = "0";
                        break;
                    case "自整定不可设置":
                        strSelfTuningSet = "1";
                        break;                    
                    default:
                        break;
                }
                if (strSelfTuningSet == "0" && strVibrationSuppressionASwitch == "0")
                {
                    strVibrationSuppressionOption = "0";
                }
                else if (strSelfTuningSet == "0" && strVibrationSuppressionASwitch == "1")
                {
                    strVibrationSuppressionOption = "1";
                }
                else if (strSelfTuningSet == "1" && strVibrationSuppressionASwitch == "0")
                {
                    strVibrationSuppressionOption = "16";
                }
                else if (strSelfTuningSet == "1" && strVibrationSuppressionASwitch == "1")
                {
                    strVibrationSuppressionOption = "17";
                }
                //strVibrationSuppressionOption = strVibrationSuppressionASwitch + strSelfTuningSet;

                //高级应用开关
                switch (SelectedSpeedObserverSwitch)
                {
                    case "关闭速度观测":
                        strSpeedObserverSwitch = "0";
                        break;
                    case "开启速度观测":
                        strSpeedObserverSwitch = "1";
                        break;
                    default:
                        break;
                }
                switch (SelectedDisturbanceObserverSwitch)
                {
                    case "关闭摩擦补偿":
                        strDisturbanceObserverSwitch = "0";
                        break;
                    case "开启摩擦补偿":
                        strDisturbanceObserverSwitch = "1";
                        break;
                    default:
                        break;
                }
                if (strDisturbanceObserverSwitch == "0" && strSpeedObserverSwitch == "0")
                {
                    strAdvancedApplicationSwitch = "0";
                }
                else if (strDisturbanceObserverSwitch == "0" && strSpeedObserverSwitch == "1")
                {
                    strAdvancedApplicationSwitch = "1";
                }
                else if (strDisturbanceObserverSwitch == "1" && strSpeedObserverSwitch == "0")
                {
                    strAdvancedApplicationSwitch = "16";
                }
                else if (strDisturbanceObserverSwitch == "1" && strSpeedObserverSwitch == "1")
                {
                    strAdvancedApplicationSwitch = "17";
                }
                //strAdvancedApplicationSwitch = strDisturbanceObserverSwitch + strSpeedObserverSwitch;

                switch (SelectedEndVibrationSuppressionOption)
                {
                    case "不进行末端抖动抑制":
                        strEndVibrationSuppressionOption = "0";
                        break;
                    case "进行末端抖动抑制":
                        strEndVibrationSuppressionOption = "1";
                        break;                    
                    default:
                        break;
                }

                //模型追踪控制开关
                switch (SelectedModelFollowingSwitch)
                {
                    case "关闭模型追踪":
                        strModelFollowingSwitch = "0";
                        break;
                    case "开启模型追踪":
                        strModelFollowingSwitch = "1";
                        break;
                    default:
                        break;
                }
                switch (SelectedModelFollowingVibrationSuppressionSwitch)
                {
                    case "关闭模型抑振":
                        strModelFollowingVibrationSuppressionSwitch = "0";
                        break;
                    case "开启模型抑振":
                        strModelFollowingVibrationSuppressionSwitch = "1";
                        break;
                    default:
                        break;
                }
                if (strModelFollowingVibrationSuppressionSwitch == "0" && strModelFollowingSwitch == "0")
                {
                    strModelFollowingControlSwitch = "0";
                }
                else if (strModelFollowingVibrationSuppressionSwitch == "0" && strModelFollowingSwitch == "1")
                {
                    strModelFollowingControlSwitch = "1";
                }
                else if (strModelFollowingVibrationSuppressionSwitch == "1" && strModelFollowingSwitch == "0")
                {
                    strModelFollowingControlSwitch = "16";
                }
                else if (strModelFollowingVibrationSuppressionSwitch == "1" && strModelFollowingSwitch == "1")
                {
                    strModelFollowingControlSwitch = "17";
                }
                //strModelFollowingControlSwitch = strModelFollowingSwitch + strModelFollowingVibrationSuppressionSwitch;

                switch (SelectedWeakFieldControlSwitch)
                {
                    case "关闭弱磁":
                        strWeakFieldControlSwitch = "0";
                        break;
                    case "开启弱磁":
                        strWeakFieldControlSwitch = "1";
                        break;
                    default:
                        break;
                }


                switch (strCategory)
                {
                    case "0":
                        dicParameterInfo.Add("Gain Change Time 1", GainChangeTimeOne);
                        dicParameterInfo.Add("Gain Change Time 2", GainChangeTimeTwo);
                        dicParameterInfo.Add("Gain Change Wait Time 1", GainChangeWaitTimeOne);
                        dicParameterInfo.Add("Gain Change Wait Time 2", GainChangeWaitTimeTwo);
                        dicParameterInfo.Add("Gain Change Switch", strGainChangeSwitch);

                        dicParameterInfo.Add("Speed Mode Switch", strSpeedModeSwitch);
                        dicParameterInfo.Add("Mode Switch Torque Value", ModeSwitchTorqueValue);
                        dicParameterInfo.Add("Mode Switch Speed Value", ModeSwitchSpeedValue);
                        dicParameterInfo.Add("Mode Switch Acc Value", ModeSwitchAccValue);
                        break;
                    case "1":
                        dicParameterInfo.Add("Vibration Suppression Option", strVibrationSuppressionOption);
                        dicParameterInfo.Add("Vibsup Freq", VibsupFreq);
                        dicParameterInfo.Add("Vibsup Gain Comp", VibsupGainComp);
                        dicParameterInfo.Add("Vibsup Damping Gain", VibsupDampingGain);

                        dicParameterInfo.Add("Advanced Application Switch", strAdvancedApplicationSwitch);
                        dicParameterInfo.Add("Disturbance Observer Gain", DisturbanceObserverGainOne);
                        dicParameterInfo.Add("Disturbance Observer Gain 2", DisturbanceObserverGainTwo);
                        dicParameterInfo.Add("Disturbance Observer Coefficient", DisturbanceObserverCoefficient);
                        dicParameterInfo.Add("Disturbance Observer Freq Correction", DisturbanceObserverFreqCorrection);
                        dicParameterInfo.Add("Disturbance Observer Gain Correction", DisturbanceObserverGainCorrection);
                        dicParameterInfo.Add("Speed Observer Gain", SpeedObserverGain);
                        dicParameterInfo.Add("Speed Observer Pos Compensation Gain", SpeedObserverPosCompensationGain);

                        dicParameterInfo.Add("End Vibration Suppression Option", strEndVibrationSuppressionOption);
                        dicParameterInfo.Add("End Vibration Suppression Frequency", EndVibrationSuppressionFrequency);
                        dicParameterInfo.Add("End Vibration Suppression Compensation", EndVibrationSuppressionCompensation);
                        break;
                    case "2":
                        dicParameterInfo.Add("Model Following Control Switch", strModelFollowingControlSwitch);
                        dicParameterInfo.Add("Model Following Control Gain", ModelFollowingControlGain);
                        dicParameterInfo.Add("MFC Gain Correction", MFCGainCorrection);
                        dicParameterInfo.Add("MFC Forward Bias", MFCForwardBias);
                        dicParameterInfo.Add("MFC Reverse Bias", MFCReverseBias);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency A", VibrationSuppressionFrequencyA);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency B", VibrationSuppressionFrequencyB);
                        dicParameterInfo.Add("MFC Velocity Feedforward Compensation", MFCVelocityFeedforwardCompensation);
                        dicParameterInfo.Add("MFC Gain 2", MFCGainTwo);
                        dicParameterInfo.Add("MFC Gain Correction 2", MFCGainCorrectionTwo);
                        break;
                    case "3":
                        dicParameterInfo.Add("Weak Field Control Gain", WeakFieldControlGain);
                        dicParameterInfo.Add("Weak Field Control Time Constant", WeakFieldControlTimeConstant);
                        dicParameterInfo.Add("Weak Field Max Speed Corresponding To IdRef", WeakFieldMaxSpeedCorrespondingToIdRef);
                        dicParameterInfo.Add("Weak Field Control Switch", strWeakFieldControlSwitch);                    
                        break;
                    default:
                        dicParameterInfo.Add("Gain Change Time 1", GainChangeTimeOne);
                        dicParameterInfo.Add("Gain Change Time 2", GainChangeTimeTwo);
                        dicParameterInfo.Add("Gain Change Wait Time 1", GainChangeWaitTimeOne);
                        dicParameterInfo.Add("Gain Change Wait Time 2", GainChangeWaitTimeTwo);
                        dicParameterInfo.Add("Gain Change Switch", strGainChangeSwitch);

                        dicParameterInfo.Add("Speed Mode Switch", strSpeedModeSwitch);
                        dicParameterInfo.Add("Mode Switch Torque Value", ModeSwitchTorqueValue);
                        dicParameterInfo.Add("Mode Switch Speed Value", ModeSwitchSpeedValue);
                        dicParameterInfo.Add("Mode Switch Acc Value", ModeSwitchAccValue);

                        dicParameterInfo.Add("Vibration Suppression Option", strVibrationSuppressionOption);
                        dicParameterInfo.Add("Vibsup Freq", VibsupFreq);
                        dicParameterInfo.Add("Vibsup Gain Comp", VibsupGainComp);
                        dicParameterInfo.Add("Vibsup Damping Gain", VibsupDampingGain);

                        dicParameterInfo.Add("Advanced Application Switch", strAdvancedApplicationSwitch);
                        dicParameterInfo.Add("Disturbance Observer Gain", DisturbanceObserverGainOne);
                        dicParameterInfo.Add("Disturbance Observer Gain 2", DisturbanceObserverGainTwo);
                        dicParameterInfo.Add("Disturbance Observer Coefficient", DisturbanceObserverCoefficient);
                        dicParameterInfo.Add("Disturbance Observer Freq Correction", DisturbanceObserverFreqCorrection);
                        dicParameterInfo.Add("Disturbance Observer Gain Correction", DisturbanceObserverGainCorrection);
                        dicParameterInfo.Add("Speed Observer Gain", SpeedObserverGain);
                        dicParameterInfo.Add("Speed Observer Pos Compensation Gain", SpeedObserverPosCompensationGain);

                        dicParameterInfo.Add("End Vibration Suppression Option", strEndVibrationSuppressionOption);
                        dicParameterInfo.Add("End Vibration Suppression Frequency", EndVibrationSuppressionFrequency);
                        dicParameterInfo.Add("End Vibration Suppression Compensation", EndVibrationSuppressionCompensation);

                        dicParameterInfo.Add("Model Following Control Switch", strModelFollowingControlSwitch);
                        dicParameterInfo.Add("Model Following Control Gain", ModelFollowingControlGain);
                        dicParameterInfo.Add("MFC Gain Correction", MFCGainCorrection);
                        dicParameterInfo.Add("MFC Forward Bias", MFCForwardBias);
                        dicParameterInfo.Add("MFC Reverse Bias", MFCReverseBias);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency A", VibrationSuppressionFrequencyA);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency B", VibrationSuppressionFrequencyB);
                        dicParameterInfo.Add("MFC Velocity Feedforward Compensation", MFCVelocityFeedforwardCompensation);
                        dicParameterInfo.Add("MFC Gain 2", MFCGainTwo);
                        dicParameterInfo.Add("MFC Gain Correction 2", MFCGainCorrectionTwo);

                        dicParameterInfo.Add("Weak Field Control Gain", WeakFieldControlGain);
                        dicParameterInfo.Add("Weak Field Control Time Constant", WeakFieldControlTimeConstant);
                        dicParameterInfo.Add("Weak Field Max Speed Corresponding To IdRef", WeakFieldMaxSpeedCorrespondingToIdRef);
                        dicParameterInfo.Add("Weak Field Control Switch", strWeakFieldControlSwitch);
                        
                        break;
                }
               
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.15
        //*************************************************************************
        private int AddParameterInfoDictionary_ForWrite(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            string strGainChangeSwitch = null;
            string strGainSwitch = null;
            string strGainSwitchCondition = null;

            string strSpeedModeSwitch = null;

            string strVibrationSuppressionOption = null;
            string strVibrationSuppressionASwitch = null;
            string strSelfTuningSet = null;

            string strAdvancedApplicationSwitch = null;
            string strSpeedObserverSwitch = null;
            string strDisturbanceObserverSwitch = null;

            string strEndVibrationSuppressionOption = null;

            string strModelFollowingControlSwitch = null;
            string strModelFollowingSwitch = null;
            string strModelFollowingVibrationSuppressionSwitch = null;

            string strWeakFieldControlSwitch = null;

            dicParameterInfo = new Dictionary<string, string>();

            try
            {

                //增益切换开关
                switch (SelectedGainSwitch)
                {
                    case "关闭增益切换":
                        strGainSwitch = "1";
                        break;
                    case "开启增益切换":
                        strGainSwitch = "2";
                        break;
                    default:
                        break;
                }
                switch (SelectedGainSwitchCondition)
                {
                    case "定位完成信号On":
                        strGainSwitchCondition = "0";
                        break;
                    case "定位完成信号Off":
                        strGainSwitchCondition = "1";
                        break;
                    case "定位接近信号On":
                        strGainSwitchCondition = "2";
                        break;
                    case "定位接近信号Off":
                        strGainSwitchCondition = "3";
                        break;
                    case "位置指令输入Off且位置指令滤波输出为0":
                        strGainSwitchCondition = "4";
                        break;
                    case "位置指令输入On":
                        strGainSwitchCondition = "5";
                        break;
                    default:
                        break;
                }
                if (strGainSwitchCondition == "0" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "1";
                }
                else if (strGainSwitchCondition == "0" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "2";
                }
                else if (strGainSwitchCondition == "1" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "17";
                }
                else if (strGainSwitchCondition == "1" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "18";
                }
                else if (strGainSwitchCondition == "2" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "33";
                }
                else if (strGainSwitchCondition == "2" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "34";
                }
                else if (strGainSwitchCondition == "3" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "49";
                }
                else if (strGainSwitchCondition == "3" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "50";
                }
                else if (strGainSwitchCondition == "4" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "65";
                }
                else if (strGainSwitchCondition == "4" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "66";
                }
                else if (strGainSwitchCondition == "5" && strGainSwitch == "1")
                {
                    strGainChangeSwitch = "81";
                }
                else if (strGainSwitchCondition == "5" && strGainSwitch == "2")
                {
                    strGainChangeSwitch = "82";
                }
                //strGainChangeSwitch = strGainSwitchCondition + strGainSwitch;

                switch (SelectedSpeedModeSwitch)
                {
                    case "以内部转矩指令为条件":
                        strSpeedModeSwitch = "0";
                        break;
                    case "以速度指令为条件":
                        strSpeedModeSwitch = "1";
                        break;
                    case "以加速度为条件":
                        strSpeedModeSwitch = "2";
                        break;
                    case "以位置偏差为条件":
                        strSpeedModeSwitch = "3";
                        break;
                    case "无模式开关":
                        strSpeedModeSwitch = "4";
                        break;
                    default:
                        break;
                }

                //A型抑振控制选择
                switch (SelectedVibrationSuppressionASwitch)
                {
                    case "关闭A型抑振":
                        strVibrationSuppressionASwitch = "0";
                        break;
                    case "开启A型抑振":
                        strVibrationSuppressionASwitch = "1";
                        break;
                    default:
                        break;
                }
                switch (SelectedSelfTuningSet)
                {
                    case "自整定可设置":
                        strSelfTuningSet = "0";
                        break;
                    case "自整定不可设置":
                        strSelfTuningSet = "1";
                        break;
                    default:
                        break;
                }
                if (strSelfTuningSet == "0" && strVibrationSuppressionASwitch == "0")
                {
                    strVibrationSuppressionOption = "0";
                }
                else if (strSelfTuningSet == "0" && strVibrationSuppressionASwitch == "1")
                {
                    strVibrationSuppressionOption = "1";
                }
                else if (strSelfTuningSet == "1" && strVibrationSuppressionASwitch == "0")
                {
                    strVibrationSuppressionOption = "16";
                }
                else if (strSelfTuningSet == "1" && strVibrationSuppressionASwitch == "1")
                {
                    strVibrationSuppressionOption = "17";
                }
                //strVibrationSuppressionOption = strVibrationSuppressionASwitch + strSelfTuningSet;

                //高级应用开关
                switch (SelectedSpeedObserverSwitch)
                {
                    case "关闭速度观测":
                        strSpeedObserverSwitch = "0";
                        break;
                    case "开启速度观测":
                        strSpeedObserverSwitch = "1";
                        break;
                    default:
                        break;
                }
                switch (SelectedDisturbanceObserverSwitch)
                {
                    case "关闭摩擦补偿":
                        strDisturbanceObserverSwitch = "0";
                        break;
                    case "开启摩擦补偿":
                        strDisturbanceObserverSwitch = "1";
                        break;
                    default:
                        break;
                }
                if (strDisturbanceObserverSwitch == "0" && strSpeedObserverSwitch == "0")
                {
                    strAdvancedApplicationSwitch = "0";
                }
                else if (strDisturbanceObserverSwitch == "0" && strSpeedObserverSwitch == "1")
                {
                    strAdvancedApplicationSwitch = "1";
                }
                else if (strDisturbanceObserverSwitch == "1" && strSpeedObserverSwitch == "0")
                {
                    strAdvancedApplicationSwitch = "16";
                }
                else if (strDisturbanceObserverSwitch == "1" && strSpeedObserverSwitch == "1")
                {
                    strAdvancedApplicationSwitch = "17";
                }
                //strAdvancedApplicationSwitch = strDisturbanceObserverSwitch + strSpeedObserverSwitch;

                switch (SelectedEndVibrationSuppressionOption)
                {
                    case "不进行末端抖动抑制":
                        strEndVibrationSuppressionOption = "0";
                        break;
                    case "进行末端抖动抑制":
                        strEndVibrationSuppressionOption = "1";
                        break;
                    default:
                        break;
                }

                //模型追踪控制开关
                switch (SelectedModelFollowingSwitch)
                {
                    case "关闭模型追踪":
                        strModelFollowingSwitch = "0";
                        break;
                    case "开启模型追踪":
                        strModelFollowingSwitch = "1";
                        break;
                    default:
                        break;
                }
                switch (SelectedModelFollowingVibrationSuppressionSwitch)
                {
                    case "关闭模型抑振":
                        strModelFollowingVibrationSuppressionSwitch = "0";
                        break;
                    case "开启模型抑振":
                        strModelFollowingVibrationSuppressionSwitch = "1";
                        break;
                    default:
                        break;
                }
                if (strModelFollowingVibrationSuppressionSwitch == "0" && strModelFollowingSwitch == "0")
                {
                    strModelFollowingControlSwitch = "0";
                }
                else if (strModelFollowingVibrationSuppressionSwitch == "0" && strModelFollowingSwitch == "1")
                {
                    strModelFollowingControlSwitch = "1";
                }
                else if (strModelFollowingVibrationSuppressionSwitch == "1" && strModelFollowingSwitch == "0")
                {
                    strModelFollowingControlSwitch = "16";
                }
                else if (strModelFollowingVibrationSuppressionSwitch == "1" && strModelFollowingSwitch == "1")
                {
                    strModelFollowingControlSwitch = "17";
                }
                //strModelFollowingControlSwitch = strModelFollowingSwitch + strModelFollowingVibrationSuppressionSwitch;

                switch (SelectedWeakFieldControlSwitch)
                {
                    case "关闭弱磁":
                        strWeakFieldControlSwitch = "0";
                        break;
                    case "开启弱磁":
                        strWeakFieldControlSwitch = "1";
                        break;
                    default:
                        break;
                }


                switch (strCategory)
                {
                    case "0":
                        dicParameterInfo.Add("Gain Change Time 1", GainChangeTimeOne);
                        dicParameterInfo.Add("Gain Change Time 2", GainChangeTimeTwo);
                        dicParameterInfo.Add("Gain Change Wait Time 1", GainChangeWaitTimeOne);
                        dicParameterInfo.Add("Gain Change Wait Time 2", GainChangeWaitTimeTwo);
                        dicParameterInfo.Add("Gain Change Switch", strGainChangeSwitch);

                        dicParameterInfo.Add("Speed Mode Switch", strSpeedModeSwitch);
                        dicParameterInfo.Add("Mode Switch Torque Value", ModeSwitchTorqueValue);
                        dicParameterInfo.Add("Mode Switch Speed Value", ModeSwitchSpeedValue);
                        dicParameterInfo.Add("Mode Switch Acc Value", ModeSwitchAccValue);
                        break;
                    case "1":
                        dicParameterInfo.Add("Vibration Suppression Option", strVibrationSuppressionOption);
                        dicParameterInfo.Add("Vibsup Freq", VibsupFreq);
                        dicParameterInfo.Add("Vibsup Gain Comp", VibsupGainComp);
                        dicParameterInfo.Add("Vibsup Damping Gain", VibsupDampingGain);

                        dicParameterInfo.Add("Advanced Application Switch", strAdvancedApplicationSwitch);
                        dicParameterInfo.Add("Disturbance Observer Gain", DisturbanceObserverGainOne);
                        dicParameterInfo.Add("Disturbance Observer Gain 2", DisturbanceObserverGainTwo);
                        dicParameterInfo.Add("Disturbance Observer Coefficient", DisturbanceObserverCoefficient);
                        dicParameterInfo.Add("Disturbance Observer Freq Correction", DisturbanceObserverFreqCorrection);
                        dicParameterInfo.Add("Disturbance Observer Gain Correction", DisturbanceObserverGainCorrection);
                        dicParameterInfo.Add("Speed Observer Gain", SpeedObserverGain);
                        dicParameterInfo.Add("Speed Observer Pos Compensation Gain", SpeedObserverPosCompensationGain);

                        dicParameterInfo.Add("End Vibration Suppression Option", strEndVibrationSuppressionOption);
                        dicParameterInfo.Add("End Vibration Suppression Frequency", EndVibrationSuppressionFrequency);
                        dicParameterInfo.Add("End Vibration Suppression Compensation", EndVibrationSuppressionCompensation);
                        break;
                    case "2":

                        if (Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x200317")) > 4095)
                        {
                            //使用前馈模型
                        }

                        if (Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x200317")) >= 256 && Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x200317")) <= 4095)
                        {
                            ViewModelSet.Main?.ShowHintInfo("模型追踪控制开关仅读取...");
                        }

                        if (Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x200317")) >= 0 && Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x200317")) <= 255)
                        {
                            dicParameterInfo.Add("Model Following Control Switch", strModelFollowingControlSwitch);
                        }
                        
                        dicParameterInfo.Add("Model Following Control Gain", ModelFollowingControlGain);
                        dicParameterInfo.Add("MFC Gain Correction", MFCGainCorrection);
                        dicParameterInfo.Add("MFC Forward Bias", MFCForwardBias);
                        dicParameterInfo.Add("MFC Reverse Bias", MFCReverseBias);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency A", VibrationSuppressionFrequencyA);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency B", VibrationSuppressionFrequencyB);
                        dicParameterInfo.Add("MFC Velocity Feedforward Compensation", MFCVelocityFeedforwardCompensation);
                        dicParameterInfo.Add("MFC Gain 2", MFCGainTwo);
                        dicParameterInfo.Add("MFC Gain Correction 2", MFCGainCorrectionTwo);
                        break;
                    case "3":
                        dicParameterInfo.Add("Weak Field Control Gain", WeakFieldControlGain);
                        dicParameterInfo.Add("Weak Field Control Time Constant", WeakFieldControlTimeConstant);
                        dicParameterInfo.Add("Weak Field Max Speed Corresponding To IdRef", WeakFieldMaxSpeedCorrespondingToIdRef);
                        dicParameterInfo.Add("Weak Field Control Switch", strWeakFieldControlSwitch);
                        break;
                    default:
                        dicParameterInfo.Add("Gain Change Time 1", GainChangeTimeOne);
                        dicParameterInfo.Add("Gain Change Time 2", GainChangeTimeTwo);
                        dicParameterInfo.Add("Gain Change Wait Time 1", GainChangeWaitTimeOne);
                        dicParameterInfo.Add("Gain Change Wait Time 2", GainChangeWaitTimeTwo);
                        dicParameterInfo.Add("Gain Change Switch", strGainChangeSwitch);

                        dicParameterInfo.Add("Speed Mode Switch", strSpeedModeSwitch);
                        dicParameterInfo.Add("Mode Switch Torque Value", ModeSwitchTorqueValue);
                        dicParameterInfo.Add("Mode Switch Speed Value", ModeSwitchSpeedValue);
                        dicParameterInfo.Add("Mode Switch Acc Value", ModeSwitchAccValue);

                        dicParameterInfo.Add("Vibration Suppression Option", strVibrationSuppressionOption);
                        dicParameterInfo.Add("Vibsup Freq", VibsupFreq);
                        dicParameterInfo.Add("Vibsup Gain Comp", VibsupGainComp);
                        dicParameterInfo.Add("Vibsup Damping Gain", VibsupDampingGain);

                        dicParameterInfo.Add("Advanced Application Switch", strAdvancedApplicationSwitch);
                        dicParameterInfo.Add("Disturbance Observer Gain", DisturbanceObserverGainOne);
                        dicParameterInfo.Add("Disturbance Observer Gain 2", DisturbanceObserverGainTwo);
                        dicParameterInfo.Add("Disturbance Observer Coefficient", DisturbanceObserverCoefficient);
                        dicParameterInfo.Add("Disturbance Observer Freq Correction", DisturbanceObserverFreqCorrection);
                        dicParameterInfo.Add("Disturbance Observer Gain Correction", DisturbanceObserverGainCorrection);
                        dicParameterInfo.Add("Speed Observer Gain", SpeedObserverGain);
                        dicParameterInfo.Add("Speed Observer Pos Compensation Gain", SpeedObserverPosCompensationGain);

                        dicParameterInfo.Add("End Vibration Suppression Option", strEndVibrationSuppressionOption);
                        dicParameterInfo.Add("End Vibration Suppression Frequency", EndVibrationSuppressionFrequency);
                        dicParameterInfo.Add("End Vibration Suppression Compensation", EndVibrationSuppressionCompensation);

                        dicParameterInfo.Add("Model Following Control Switch", strModelFollowingControlSwitch);
                        dicParameterInfo.Add("Model Following Control Gain", ModelFollowingControlGain);
                        dicParameterInfo.Add("MFC Gain Correction", MFCGainCorrection);
                        dicParameterInfo.Add("MFC Forward Bias", MFCForwardBias);
                        dicParameterInfo.Add("MFC Reverse Bias", MFCReverseBias);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency A", VibrationSuppressionFrequencyA);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency B", VibrationSuppressionFrequencyB);
                        dicParameterInfo.Add("MFC Velocity Feedforward Compensation", MFCVelocityFeedforwardCompensation);
                        dicParameterInfo.Add("MFC Gain 2", MFCGainTwo);
                        dicParameterInfo.Add("MFC Gain Correction 2", MFCGainCorrectionTwo);

                        dicParameterInfo.Add("Weak Field Control Gain", WeakFieldControlGain);
                        dicParameterInfo.Add("Weak Field Control Time Constant", WeakFieldControlTimeConstant);
                        dicParameterInfo.Add("Weak Field Max Speed Corresponding To IdRef", WeakFieldMaxSpeedCorrespondingToIdRef);
                        dicParameterInfo.Add("Weak Field Control Switch", strWeakFieldControlSwitch);

                        break;
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.ADVANCEDFEEDBACK_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }
}