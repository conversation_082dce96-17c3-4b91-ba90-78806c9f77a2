﻿using DevExpress.Mvvm;
using GalaSoft.MvvmLight.Command;
using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using ServoStudio.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Windows;

namespace ServoStudio.ViewModels
{
    public class AddSlaveAxisIDViewModel : ViewModelBase
    {
        LocalDB localDb;

        private static bool ScanSlaveAxisIDFlag = false;

        public virtual ObservableCollection<string> SlaveID { get; set; }
        public virtual string SelectedSlaveID { get; set; }
        public virtual ObservableCollection<string> AxisID { get; set; }
        public virtual string SelectedAxisID { get; set; }
        public virtual bool IsAddSlaveAxisIDEnabled { get; set; }//添加按钮属性
        public virtual bool IsScanSlaveAxisIDEnabled { get; set; }//扫描按钮属性
        public virtual bool ReadOnly { get; set; }//只读属性

        public AddSlaveAxisIDViewModel()
        {
            localDb = new LocalDB();

            AddSlaveAxisIDLibraryDetailsLoaded();

            AddSlaveAxisIDCommand = new RelayCommand(AddSlaveAxisID);

            ScanSlaveAxisIDCommand = new RelayCommand(ScanSlaveAxisID);

            DelSlaveAxisIDCommand = new RelayCommand<int>(t => DelSlaveAxisID(t));
        }

        private string search = string.Empty;

        public string Search
        {
            get { return search; }
            set
            {
                search = value;
                RaisePropertyChanged();
            }
        }

        private ObservableCollection<SlaveAxisAddress> addSlaveAxisIDList;

        public ObservableCollection<SlaveAxisAddress> AddSlaveAxisIDList
        {
            get { return addSlaveAxisIDList; }
            set
            {
                addSlaveAxisIDList = value;
                RaisePropertyChanged();
            }
        }

        #region Command
        public RelayCommand AddSlaveAxisIDCommand { get; set; }
        public RelayCommand ScanSlaveAxisIDCommand { get; set; }

        public RelayCommand<int> DelSlaveAxisIDCommand { get; set; }
        #endregion

        //*************************************************************************
        //函数名称：AddSlaveAxisIDLibraryDetailsLoaded
        //函数功能：载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.02.02
        //*************************************************************************
        public void AddSlaveAxisIDLibraryDetailsLoaded()
        {           
            RefreshAddScanFlag();

            //只读属性与控件初始化
            ComboBoxInitialize();
            ControlReadOnlyProperty();
            
        }

        //*************************************************************************
        //函数名称：RefreshAddScanFlag
        //函数功能：刷新添加扫描标志
        //
        //输入参数：None
        //         
        //输出参数：None
        //         
        //        
        //编码作者：Lilbert
        //更新时间：2023.02.02
        //*************************************************************************
        public void RefreshAddScanFlag()
        {

            if (AddSlaveAxisIDSet.Action == "AddSlaveAxisID")
            {
                IsAddSlaveAxisIDEnabled = true;
                IsScanSlaveAxisIDEnabled = false;
            }
            else
            {
                IsAddSlaveAxisIDEnabled = false;
                IsScanSlaveAxisIDEnabled = true;
            }
        }

        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：属性初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.02.02
        //*************************************************************************
        private void ComboBoxInitialize()
        {
            if (AddSlaveAxisIDSet.Action == "AddSlaveAxisID")
            {
                SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15",  "SLAVE-16",
                                                              "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22", "SLAVE-23", "SLAVE-24", "SLAVE-25", "SLAVE-26", "SLAVE-27", "SLAVE-28", "SLAVE-29", "SLAVE-30", "SLAVE-30",  "SLAVE-32",
                                                              "SLAVE-33", "SLAVE-34", "SLAVE-35", "SLAVE-36", "SLAVE-37", "SLAVE-38", "SLAVE-39", "SLAVE-80", "SLAVE-41", "SLAVE-42", "SLAVE-43", "SLAVE-44", "SLAVE-45", "SLAVE-46", "SLAVE-47",  "SLAVE-48",
                                                              "SLAVE-49", "SLAVE-50", "SLAVE-51", "SLAVE-52", "SLAVE-53", "SLAVE-54", "SLAVE-55", "SLAVE-56", "SLAVE-57", "SLAVE-58", "SLAVE-59", "SLAVE-60", "SLAVE-61", "SLAVE-62", "SLAVE-63",  "SLAVE-64",
                                                              "SLAVE-65", "SLAVE-66", "SLAVE-67", "SLAVE-68", "SLAVE-69", "SLAVE-70", "SLAVE-71", "SLAVE-72", "SLAVE-73", "SLAVE-74", "SLAVE-75", "SLAVE-76", "SLAVE-77", "SLAVE-78", "SLAVE-79",  "SLAVE-80",
                                                              "SLAVE-81", "SLAVE-82", "SLAVE-83", "SLAVE-84", "SLAVE-85", "SLAVE-86", "SLAVE-87", "SLAVE-88", "SLAVE-89", "SLAVE-90", "SLAVE-91", "SLAVE-92", "SLAVE-93", "SLAVE-94", "SLAVE-95",  "SLAVE-96",
                                                              "SLAVE-97", "SLAVE-98", "SLAVE-99", "SLAVE-100", "SLAVE-101", "SLAVE-102", "SLAVE-103", "SLAVE-104", "SLAVE-105", "SLAVE-106", "SLAVE-107", "SLAVE-108", "SLAVE-109", "SLAVE-110", "SLAVE-111",  "SLAVE-112",
                                                              "SLAVE-113", "SLAVE-114", "SLAVE-115", "SLAVE-116", "SLAVE-117", "SLAVE-118", "SLAVE-119", "SLAVE-120", "SLAVE-121", "SLAVE-122", "SLAVE-123", "SLAVE-124", "SLAVE-125", "SLAVE-126", "SLAVE-127",  "SLAVE-128",
                                                              "SLAVE-129", "SLAVE-130", "SLAVE-131", "SLAVE-132", "SLAVE-133", "SLAVE-134", "SLAVE-135", "SLAVE-136", "SLAVE-137", "SLAVE-138", "SLAVE-139", "SLAVE-140", "SLAVE-141", "SLAVE-142", "SLAVE-143",  "SLAVE-144",
                                                              "SLAVE-145", "SLAVE-146", "SLAVE-147", "SLAVE-148", "SLAVE-149", "SLAVE-150", "SLAVE-151", "SLAVE-152", "SLAVE-153", "SLAVE-154", "SLAVE-155", "SLAVE-156", "SLAVE-157", "SLAVE-158", "SLAVE-159",  "SLAVE-160",
                                                              "SLAVE-161", "SLAVE-162", "SLAVE-163", "SLAVE-164", "SLAVE-165", "SLAVE-166", "SLAVE-167", "SLAVE-168", "SLAVE-169", "SLAVE-170", "SLAVE-171", "SLAVE-172", "SLAVE-173", "SLAVE-174", "SLAVE-175",  "SLAVE-176",
                                                              "SLAVE-177", "SLAVE-178", "SLAVE-179", "SLAVE-180", "SLAVE-181", "SLAVE-182", "SLAVE-183", "SLAVE-184", "SLAVE-185", "SLAVE-186", "SLAVE-187", "SLAVE-188", "SLAVE-189", "SLAVE-190", "SLAVE-191",  "SLAVE-192",
                                                              "SLAVE-193", "SLAVE-194", "SLAVE-195", "SLAVE-196", "SLAVE-197", "SLAVE-198", "SLAVE-199", "SLAVE-200", "SLAVE-201", "SLAVE-202", "SLAVE-203", "SLAVE-204", "SLAVE-205", "SLAVE-206", "SLAVE-207",  "SLAVE-208",
                                                              "SLAVE-209", "SLAVE-210", "SLAVE-211", "SLAVE-212", "SLAVE-213", "SLAVE-214", "SLAVE-215", "SLAVE-216", "SLAVE-217", "SLAVE-218", "SLAVE-219", "SLAVE-220", "SLAVE-221", "SLAVE-222", "SLAVE-223",  "SLAVE-224",
                                                              "SLAVE-225", "SLAVE-226", "SLAVE-227", "SLAVE-228", "SLAVE-229", "SLAVE-230", "SLAVE-231", "SLAVE-232", "SLAVE-233", "SLAVE-234", "SLAVE-235", "SLAVE-236", "SLAVE-237", "SLAVE-238", "SLAVE-239",  "SLAVE-240",
                                                              "SLAVE-241", "SLAVE-242", "SLAVE-243", "SLAVE-244", "SLAVE-245", "SLAVE-246", "SLAVE-247", "SLAVE-248", "SLAVE-249", "SLAVE-250", "SLAVE-251", "SLAVE-252", "SLAVE-253", "SLAVE-254", "SLAVE-255",  
                                                              };
                SelectedSlaveID = "SLAVE-1";

                AxisID = new ObservableCollection<string>() { "AXIS-1" };
                SelectedAxisID = "AXIS-1";
            }
            else
            {
                SlaveID = new ObservableCollection<string>() { "" };
                SelectedSlaveID = "";

                AxisID = new ObservableCollection<string>() { "" };
                SelectedAxisID = "";
            }
        }

        //*************************************************************************
        //函数名称：ControlReadOnlyProperty
        //函数功能：属性初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.02.02
        //*************************************************************************
        private void ControlReadOnlyProperty()
        {
            if (MotorLibraryDetailSet.Action == "Select" || MotorLibraryDetailSet.Action == "Download" || MotorLibraryDetailSet.Action == "Delete")
            {
                ReadOnly = true;
            }
            else
            {
                ReadOnly = false;
            }
        }

        public void OnSelectedSlaveIDChanged() { GlobalCurrentInput.SelectedSlaveID = SelectedSlaveID; }//从站ID

        public void OnSelectedAxisIDChanged() { GlobalCurrentInput.SelectedAxisID = SelectedAxisID; }//轴地址

        public void Query()
        {
            //var models = localDb.GetStudents();
            var models = localDb.GetSlaveAxisAddressesBySlaveID(Search);
            AddSlaveAxisIDList = new ObservableCollection<SlaveAxisAddress>();
            if (models != null)
            {
                models.ForEach(arg =>
                {
                    AddSlaveAxisIDList.Add(arg);
                });
            }
        }

        public void AddSlaveAxisID()
        {
            SlaveAxisAddress slaveAxisAddress = new SlaveAxisAddress();           

            if (AddSlaveAxisIDList.Count == 0)
            {
                slaveAxisAddress.Id = 1;

                slaveAxisAddress.SlaveID = SelectedSlaveID;

                slaveAxisAddress.AxisID = SelectedAxisID;

                localDb.AddSlaveAxisAddress(slaveAxisAddress);
                this.Query();
            }
            else
            {                
                bool a = AddSlaveAxisIDList.Select(q => q.SlaveID).ToArray().Contains(SelectedSlaveID);

                if (!a)
                {
                    slaveAxisAddress.Id = AddSlaveAxisIDList.Max(t => t.Id) + 1;

                    slaveAxisAddress.SlaveID = SelectedSlaveID;

                    slaveAxisAddress.AxisID = SelectedAxisID;

                    localDb.AddSlaveAxisAddress(slaveAxisAddress);
                    this.Query();
                }
                else
                {
                    ViewModelSet.Main?.ShowHintInfo("从站ID已经存在，请选择不同从站ID...");
                }
            }

            AddSlaveAxisIDSet.GetSlaveIDList = new ObservableCollection<string>(AddSlaveAxisIDList.Select(q => q.SlaveID).ToArray());           

            //a = AddSlaveAxisIDs.Select(t => t.Name).ToArray();

            //SlaveID = new ObservableCollection<string>(a);

        }       

        public void ScanSlaveAxisID()
        {
            int iRet = -1;
            
            SlaveAxisAddress slaveAxisAddress = new SlaveAxisAddress();

            localDb.ScanSlaveAxisAddress();

            this.Query();

            ////串口参数设置
            //iRet = ViewModelSet.CommunicationSet.SerialPortParameterSet_For_ScanSlaveAxisID();
            //if (iRet != RET.SUCCEEDED)
            //{
            //    ViewModelSet.Main?.ShowHintInfo("系统异常，请关闭系统后重试...");
            //    return;
            //}

            ////建立连接，接收事件注册
            //iRet = ViewModelSet.CommunicationSet.OpenSerialPortConnection_For_ScanSlaveAxisID();
            //if (iRet == RET.SUCCEEDED)
            //{
            //    SoftwareStateParameterSet.OpenConnectionFlag = true;
            //    for (int i = 0; i < 3; i++)
            //    {
            //        //回送测试
            //        EchoTest_For_ScanSlaveAxisID(i.ToString("X2"));

            //        if (ScanSlaveAxisIDFlag)
            //        {
            //            localDb.ScanSlaveAxisAddress(i);


            //            //if (AddSlaveAxisIDList.Count == 0)
            //            //{
            //            //    slaveAxisAddress.Id = 1;
            //            //}
            //            //else
            //            //{
            //            //    slaveAxisAddress.Id = AddSlaveAxisIDList.Max(t => t.Id) + 1;
            //            //}

            //            //slaveAxisAddress.SlaveID = $"SlaveID-{i + 1}";

            //            //slaveAxisAddress.AxisID = "AxisID-1";

            //            //localDb.AddSlaveAxisAddress(slaveAxisAddress);
            //            //this.Query();
            //            ScanSlaveAxisIDFlag = false;
            //        }
            //        //localDb.AddSlaveAxisAddress(slaveAxisAddress);
            //        //this.Query();
            //    }
            //    this.Query();

            //    AddSlaveAxisIDSet.GetSlaveIDList = new ObservableCollection<string>(AddSlaveAxisIDList.Select(q => q.SlaveID).ToArray());


            //}
            //else if (iRet == RET.ERROR)
            //{
            //    ViewModelSet.Main?.ShowHintInfo("串口通信连接失败，端口可能被占用...");
            //}
        }

        public void DelSlaveAxisID(int id)
        {
            var model = localDb.GetSlaveAxisAddressById(id);
            if (model != null)
            {
                var r = MessageBox.Show($"确认删除当前用户:{model.SlaveID}?", "操作提示", MessageBoxButton.OK, MessageBoxImage.Question);
                if (r == MessageBoxResult.OK)
                    localDb.DelSlaveAxisAddress(model.Id);
                this.Query();
            }
        }

        //*************************************************************************
        //函数名称：EchoTest
        //函数功能：回传测试
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.11.04
        //*************************************************************************
        //public void EchoTest_For_ScanSlaveAxisID(string slaveID)
        //{
        //    int iRet = -1;
        //    List<TransmitingDataInfoSet> TransmittingDataInfo = new List<TransmitingDataInfoSet>();
        //    TransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = "9999" });

        //    //if (IsFirstTest == "true")
        //    //{
        //    //    SoftwareStateParameterSet.IsFirstEchoTest = true;
        //    //}
        //    //else
        //    //{
        //    //    SoftwareStateParameterSet.IsFirstEchoTest = false;
        //    //}

        //    //判断串口状态
        //    iRet = HexHelper.CheckSerialPortStatus();
        //    if (iRet != RET.SUCCEEDED)
        //    {
        //        ViewModelSet.Main?.ShowHintInfo("串口未开启，请稍后重试...");
        //        return;
        //    }

        //    //添加串口任务
        //    //iRet = HexHelper.AddSerialPortTask(PageName.COMMUNICATIONSET, SoftwareStateParameterSet.StationID, SoftwareStateParameterSet.AxisID, TaskName.Test, FunctionCode.TEST_COMMUNICATION, null, TransmittingDataInfo);
        //    if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
        //    {
        //        iRet = HexHelper.AddSerialPortTask(PageName.COMMUNICATIONSET, slaveID, SoftwareStateParameterSet.AxisID, TaskName.Test, FunctionCode.TEST_COMMUNICATION, null, TransmittingDataInfo);
        //    }
        //    else
        //    {
        //        iRet = HexHelper.AddSerialPortTask(PageName.COMMUNICATIONSET, slaveID, SoftwareStateParameterSet.AxisID, TaskName.Test1, FunctionCode.TEST_COMMUNICATION, null, TransmittingDataInfo);
        //    }
        //    if (iRet != RET.SUCCEEDED)
        //    {
        //        ViewModelSet.Main?.ShowHintInfo("系统异常，请关闭系统后重试...");
        //        return;
        //    }

        //    //开启任务管理与发送线程
        //    if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
        //    {
        //        ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
        //    }
        //    else
        //    {
        //        ScanSlaveAxisIDFlag = true;
        //    }
        //}
    }
}
