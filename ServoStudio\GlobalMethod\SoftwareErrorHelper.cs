﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Diagnostics;
using ServoStudio.GlobalConstant;

namespace ServoStudio.GlobalMethod
{
    public static class SoftwareErrorHelper
    {       
        //*************************************************************************
        //函数名称：CatchDispose
        //函数功能：异常处理
        //
        //输入参数：string In_strErrorCode       错误码
        //         string In_strErrorFunction   异常函数
        //         Exception In_clException     异常原因
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.17
        //*************************************************************************
        public static void CatchDispose(string In_strErrorCode, string In_strErrorFunction, System.Exception In_clException)
        {           
            try
            {
                //记录数据库
                RecordtoExcel(In_strErrorCode, In_strErrorFunction, In_clException);

                //记录ErrorLog
                LogHelper.ErrorLog(In_strErrorFunction, In_clException);
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("SoftwareErrorHelper.CatchDispose", ex);
            }
        }

        //*************************************************************************
        //函数名称：RecordtoExcel
        //函数功能：记录软件异常到数据库
        //
        //输入参数：string In_strErrorCode       错误码
        //         string In_strErrorFunction   异常函数
        //         Exception In_clException     异常原因
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.17
        //*************************************************************************
        private static void RecordtoExcel(string In_strErrorCode, string In_strErrorFunction, Exception In_clException)
        {
            int iRet = -1;           
            string strErrorContent = null;

            try
            {
                //获取异常内容
                strErrorContent = "[Type]" + In_clException.GetType().Name + ".  [Message]" + In_clException.Message;
                strErrorContent = strErrorContent.Replace("'", "");
                if (strErrorContent.Length > 200)
                {
                    strErrorContent = strErrorContent.Substring(0, 200);
                }
               
                //查询Excel数据
                DataTable clsDataTable = new DataTable();
                DataColumnCollection columns = clsDataTable.Columns;
                columns.Add("Code", typeof(String));
                columns.Add("Function", typeof(String));
                columns.Add("Content", typeof(String));
                columns.Add("Level", typeof(Int32));
                columns.Add("DateTime", typeof(String));

                DataRow clsDataRow = clsDataTable.NewRow();

                clsDataRow["Code"] = Convert.ToString(In_strErrorCode);
                clsDataRow["Function"] = Convert.ToString(In_strErrorFunction);
                clsDataRow["Content"] = Convert.ToString(strErrorContent);
                clsDataRow["Level"] = Convert.ToInt32(In_strErrorCode.Substring(2, 2));
                clsDataRow["DateTime"] = DateTime.Now.ToString();

                clsDataTable.Rows.Add(clsDataRow);

                //更新Excel配置文件
                iRet = ExcelHelper.WriteIntoExcel(FilePath.SoftwareErrorLog, clsDataTable);
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("SoftwareErrorHelper.RecordtoExcel", ex);
            }
        }      
    }
}


