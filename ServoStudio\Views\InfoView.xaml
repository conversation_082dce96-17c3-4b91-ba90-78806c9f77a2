﻿<UserControl
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             xmlns:local="clr-namespace:ServoStudio.Views"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui" 
             xmlns:Views="clr-namespace:ServoStudio.Views"
             x:Class="ServoStudio.Views.InfoView"
             DataContext="{dxmvvm:ViewModelSource Type={x:Type ViewModels:InfoViewModel}}"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1100">
    <dxmvvm:Interaction.Behaviors>
        <dxwui:WinUIDialogService x:Name="Administrator" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:AdministratorView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
        <Grid>
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <StackPanel>
                    <TextBlock Text="Information" FontWeight="SemiBold" FontSize="30" FontFamily="Agency FB" Foreground="Gray" Margin="0,0,0,-8"/>

                    <Border Grid.Column="1" BorderBrush="#FFD5D5D5" Background="#FFD5D5D5" Height="1" Margin="0,10" />

                    <dxb:GalleryControl Background="Transparent">
                        <dxb:Gallery ColCount="1" Background="Transparent" IsGroupCaptionVisible="False" AllowFilter="False" ItemDescriptionHorizontalAlignment="Left" ItemGlyphMargin="0,10" ItemDescriptionVerticalAlignment="Top">

                            <dxb:Gallery.ItemDescriptionTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding}" TextWrapping="WrapWithOverflow" MaxWidth="230" />
                                </DataTemplate>
                            </dxb:Gallery.ItemDescriptionTemplate>

                            <dxb:GalleryItemGroup>
                                <!--<dxb:GalleryItem Command="{Binding ShowContactUsCommand}" Caption="了解季华实验室" Description="详细介绍可以浏览官方网站..." Glyph="/ServoStudio;component/Resource/Code_Central.png"/>-->

                                <dxb:GalleryItem Command="{Binding ShowContactUsCommand}" Caption="{Binding CompanyName}" Description="详细介绍可以浏览官方网站..." Glyph="/ServoStudio;component/Resource/Code_Central.png"/>

                                <dxb:GalleryItem Command="{Binding ShowHelpCommand}" Caption="用户手册" Description="使用前请阅读使用说明..." Glyph="/ServoStudio;component/Resource/Online_Help.png" />

                                <dxb:GalleryItem Command="{Binding ShowAdministratorKeyCommand}" Caption="管理员模式" Description="开启管理员权限..." Glyph="/ServoStudio;component/Resource/BO_Task_Large.png" />

                                <dxb:GalleryItem Command="{Binding ServoMaintenanceManualCommand}" Caption="伺服维护手册" Description="使用前请阅读使用说明..." Glyph="/ServoStudio;component/Resource/Online_Help.png" />
                            </dxb:GalleryItemGroup>
                        </dxb:Gallery>
                    </dxb:GalleryControl>
                </StackPanel>

                <Border Grid.Column="1" BorderBrush="#FFD5D5D5" Background="#FFD5D5D5" Width="1" Margin="30,0,0,0" />
                <Grid Grid.Column="2" Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="0.5*"/>
                        <RowDefinition Height="530"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="800"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Rectangle Grid.Row="1" Grid.Column="1" StrokeThickness="1" Stroke="#FFD5D5D5"/>
                    <Grid Grid.Row="1" Grid.Column="1" Margin="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="350"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Canvas Grid.Row="0" Background="#FF107EBD" Margin="15">
                            <Label Content="Servo Studio" Canvas.Left="10" Canvas.Top="187" FontSize="90" FontFamily="Agency FB" Foreground="White"/>
                            <Label Content="Released" Height="40" Width="179" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Canvas.Left="591" Foreground="White" Background="Red" Canvas.Top="47" FontFamily="Microsoft YaHei" FontSize="21.333" FontWeight="Bold"/>
                        </Canvas>
                        <Label Grid.Row="1" Margin="15,0" Content="{Binding Version}" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" FontSize="16" />
                        <Label Grid.Row="2" Margin="15,0" Content="{Binding ReleasedDate}" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" FontSize="16"/>

                        <Grid Grid.Row="3" Margin="15,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <DockPanel Grid.Row="0" Margin="15,0">

                                <!--<TextBlock TextWrapping="Wrap" Text="&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;季华实验室-机器人中心&#x0a;Copyright © 2019-2021    All Rights Reserved" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Center" VerticalAlignment="Center"/>-->
                                <TextBlock TextWrapping="Wrap" Text="{Binding CompanyDepartment}" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </DockPanel>

                            <DockPanel Grid.Row="1" Margin="15,0">

                                <!--<TextBlock TextWrapping="Wrap" Text="&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;季华实验室-机器人中心&#x0a;Copyright © 2019-2021    All Rights Reserved" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Center" VerticalAlignment="Center"/>-->
                                <TextBlock TextWrapping="Wrap" Text="Copyright © 2019-2023    All Rights Reserved" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </DockPanel>

                        </Grid>

                        <!--<DockPanel Grid.Row="3" Margin="15,0">
                        <TextBlock TextWrapping="Wrap" Text="&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;季华实验室-机器人中心&#x0a;Copyright © 2019-2021    All Rights Reserved" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </DockPanel>-->
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    </ScrollViewer>
    
</UserControl>
