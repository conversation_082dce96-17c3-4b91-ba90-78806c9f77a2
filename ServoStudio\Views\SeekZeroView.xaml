﻿<UserControl x:Class="ServoStudio.Views.SeekZeroView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:Views="clr-namespace:ServoStudio.Views"
             mc:Ignorable="d"
             xmlns:converter="clr-namespace:Converter"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:SeekZeroViewModel}"
             d:DesignHeight="800" d:DesignWidth="1300">
    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding SeekZeroLoadedCommand}"/>
        <dxmvvm:EventToCommand EventName="Unloaded" Command="{Binding SeekZeroUnloadedCommand}"/>
    </dxmvvm:Interaction.Behaviors>

    <UserControl.Resources>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>
    </UserControl.Resources>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>


            <Label Grid.Row="0" Margin="3,10" Style="{StaticResource LabelStyle}" Content="回零方法" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>
            <Grid Grid.Row ="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="260"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Label Grid.Column="1" Margin="10,15" Content="回零方法设置" Style="{StaticResource LabelStyle}" />
                <dxe:ComboBoxEdit Grid.Column="2" Margin="10,15" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding HomingMethod}" SelectedItem="{Binding SelectedHomingMethod, Mode=TwoWay}"/>
            </Grid>

            <Label Grid.Row="2" Margin="3,10" Style="{StaticResource LabelStyle}" Content="回零图示" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold" Visibility="{Binding HeaderVisibility,Converter={StaticResource VisibilityConverter}}"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/1.png" Visibility="{Binding Image1Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/2.png" Visibility="{Binding Image2Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/3.png" Visibility="{Binding Image3Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/4.png" Visibility="{Binding Image4Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/5.png" Visibility="{Binding Image5Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/6.png" Visibility="{Binding Image6Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/7.png" Visibility="{Binding Image7Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/8.png" Visibility="{Binding Image8Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/9.png" Visibility="{Binding Image9Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/10.png" Visibility="{Binding Image10Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/11.png" Visibility="{Binding Image11Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/12.png" Visibility="{Binding Image12Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/13.png" Visibility="{Binding Image13Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/14.png" Visibility="{Binding Image14Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/17.png" Visibility="{Binding Image17Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/18.png" Visibility="{Binding Image18Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/19.png" Visibility="{Binding Image19Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/20.png" Visibility="{Binding Image20Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/21.png" Visibility="{Binding Image21Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/22.png" Visibility="{Binding Image22Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/23.png" Visibility="{Binding Image23Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/24.png" Visibility="{Binding Image24Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/25.png" Visibility="{Binding Image25Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/26.png" Visibility="{Binding Image26Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/27.png" Visibility="{Binding Image27Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/28.png" Visibility="{Binding Image28Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/29.png" Visibility="{Binding Image29Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/30.png" Visibility="{Binding Image30Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/33.png" Visibility="{Binding Image33Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False"/>
            <dxe:ImageEdit Source="pack://application:,,,/ServoStudio;component/Resource/34.png" Visibility="{Binding Image34Visibility,Converter={StaticResource VisibilityConverter}}" Grid.Row="3" Margin="10,9" Width="420" Opacity="0.7" HorizontalAlignment="Left" ShowMenu="False" ShowBorder="False" />

            <Label Grid.Row="4" Margin="3,10" Style="{StaticResource LabelStyle}" Content="回零参数设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Grid Grid.Row="5">
                <Grid.RowDefinitions>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="20"/>

                    <ColumnDefinition Width="92"/>
                    <ColumnDefinition Width="180"/>
                    <ColumnDefinition Width="80"/>

                    <ColumnDefinition Width="100"/>

                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="180"/>
                    <ColumnDefinition Width="80"/>

                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Label Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="2" Foreground="Green" Margin="10,9" Content="{Binding HomingStatus}" ContentStringFormat="回零状态：{0}" Style="{StaticResource LabelStyle}"/>

                <Label   Grid.Row="1" Grid.Column="1" Margin="10,15" Content="回零加速度" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,15" Style="{StaticResource TextBoxStyle}" Text="{Binding HomingAcceleration,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding AccelerationUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                <Label   Grid.Row="1" Grid.Column="5" Margin="10,15" Content="回零偏置" Style="{StaticResource LabelStyle}" />
                <TextBox Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,15" Style="{StaticResource TextBoxStyle}" Text="{Binding HomeOffset,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="1" Grid.Column="7" Text="{Binding PositionUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                <Label   Grid.Row="2" Grid.Column="1" Margin="10,15" Content="回零快速度" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,15" Style="{StaticResource TextBoxStyle}" Text="{Binding FastHomingSpeed,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="2" Grid.Column="3" Text="{Binding SpeedUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                <Label   Grid.Row="2" Grid.Column="5" Margin="10,15" Content="回零慢速度" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,15" Style="{StaticResource TextBoxStyle}" Text="{Binding SlowHomingSpeed,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="2" Grid.Column="7" Text="{Binding SpeedUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>
            </Grid>

            <Label Grid.Row="6" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <StackPanel Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="9" HorizontalAlignment="Right" Orientation="Horizontal">
                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" Command="{Binding StartSeekZeroCommand}">
                    <Label Content="回零开始" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/Close_16x16.png" Command="{Binding StopSeekZeroCommand}">
                    <Label Content="回零关闭" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadSeekZeroParameterCommand}">
                    <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultSeekZeroParameterCommand}">
                    <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>


            </StackPanel>

            <Grid Grid.Row="9">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>

                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="12" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetSeekZeroConfigFileCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Row="1" Grid.Column="13" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" />

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="14" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SaveSeekZeroConfigFileCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Row="1" Grid.Column="16" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}"/>
            </Grid>
        </Grid>

    </ScrollViewer>
       
</UserControl>
