﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Data;
using ServoStudio.Models;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Data;
using System.Linq;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class FaultDataConfigViewModel
    {
        #region 私有字段
        private static bool IsPositionSet = false;
        private static bool IsTorqueSet = false;
        private static bool IsVoltageSet = false;
        private static bool IsInitialized = true;
        private static bool IsEvaluationAll = true;
        private static bool IsLoadedAgain = false;//是否是多次载入  
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        public string SelectedSamplingChannel1;
        public string SelectedSamplingChannel2;
        public string SelectedSamplingChannel3;
        public string SelectedSamplingChannel4;
        public string SelectedSamplingChannel5;
        public string SelectedSamplingChannel6;
        public string SelectedSamplingChannel7;
        public string SelectedSamplingChannel8;

        #region 服务
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 属性

        #region 采样通道 
        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo1 { get; set; }
        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo1_Display { get; set; }//由Lilbert添加SampleChannelInfo1_Display用于展示
        public ICollectionView GroupedSampleChannelInfo1 { get; set; }
        public virtual int SelectedSampleChannel1IndexConfig { get; set; }

        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo2 { get; set; }
        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo2_Display { get; set; }//由Lilbert添加SampleChannelInfo2_Display用于展示
        public ICollectionView GroupedSampleChannelInfo2 { get; set; }
        public virtual int SelectedSampleChannel2IndexConfig { get; set; }

        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo3 { get; set; }
        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo3_Display { get; set; }//由Lilbert添加SampleChannelInfo3_Display用于展示
        public ICollectionView GroupedSampleChannelInfo3 { get; set; }
        public virtual int SelectedSampleChannel3IndexConfig { get; set; }

        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo4 { get; set; }
        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo4_Display { get; set; }//由Lilbert添加SampleChannelInfo4_Display用于展示
        public ICollectionView GroupedSampleChannelInfo4 { get; set; }
        public virtual int SelectedSampleChannel4IndexConfig { get; set; }

        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo5 { get; set; }
        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo5_Display { get; set; }//由Lilbert添加SampleChannelInfo5_Display用于展示
        public ICollectionView GroupedSampleChannelInfo5 { get; set; }
        public virtual int SelectedSampleChannel5IndexConfig { get; set; }

        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo6 { get; set; }
        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo6_Display { get; set; }//由Lilbert添加SampleChannelInfo6_Display用于展示
        public ICollectionView GroupedSampleChannelInfo6 { get; set; }
        public virtual int SelectedSampleChannel6IndexConfig { get; set; }

        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo7 { get; set; }
        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo7_Display { get; set; }//由Lilbert添加SampleChannelInfo7_Display用于展示
        public ICollectionView GroupedSampleChannelInfo7 { get; set; }
        public virtual int SelectedSampleChannel7IndexConfig { get; set; }

        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo8 { get; set; }
        public virtual List<SampleChannelConfigInfoSet> SampleChannelInfo8_Display { get; set; }//由Lilbert添加SampleChannelInfo8_Display用于展示
        public ICollectionView GroupedSampleChannelInfo8 { get; set; }
        public virtual int SelectedSampleChannel8IndexConfig { get; set; }
        #endregion

        #region 采样参数
        //public virtual string Unit { get; set; }//采样单位
        public virtual ObservableCollection<string> SamplingPeriodConfig { get; set; }//采样周期
        public virtual string SelectedSamplingPeriodConfig { get; set; }
        public virtual ObservableCollection<string> SamplingDurationConfig { get; set; }//采样时长
        public virtual string SelectedSamplingDurationConfig { get; set; }
        public virtual int SelectedSamplingDurationIndexConfig { get; set; }
        public virtual string SamplingDurationUnit { get; set; }//采样时长单位
        //public virtual ObservableCollection<string> ContinuousSampling { get; set; }//是否连续采样
        //public virtual string SelectedContinuousSampling { get; set; }
        #endregion


        public virtual bool Channel2Enabled { get; set; }//通道2使能
        public virtual bool Channel3Enabled { get; set; }//通道3使能
        public virtual bool Channel4Enabled { get; set; }//通道4使能
        public virtual bool Channel5Enabled { get; set; }//通道5使能
        public virtual bool Channel6Enabled { get; set; }//通道6使能
        public virtual bool Channel7Enabled { get; set; }//通道7使能
        public virtual bool Channel8Enabled { get; set; }//通道8使能

        public virtual string SelectedTabIndex { get; set; }//选中的TabItem
        //转矩
        public virtual string ForwardInternalTorqueLimit { get; set; }//正转内部转矩限制值
        public virtual string ReverseInternalTorqueLimit { get; set; }//反转内部转矩限制值
        public virtual string ForwardExternalTorqueLimit { get; set; }//正转外部转矩限制值
        public virtual string ReverseExternalTorqueLimit { get; set; }//反转外部转矩限制值
        public virtual string EmergencyStopTorqueLimit { get; set; }//紧急停止转矩限制值

        //速度
        public virtual string ServoOnSpeedLimit { get; set; }//使能时速度限制值
        public virtual string TrqctrlSpeedLimit { get; set; }//转矩控制时速度限制值
        public virtual string MaxProfileVelocity { get; set; }//最大轮廓速度
        public virtual string MaxAcceleration { get; set; }//最大加速度
        public virtual string MaxDeceleration { get; set; }//最大减速度
        public virtual string VelocityWindow { get; set; }//速度到达阈值
        public virtual string VelocityWindowTime { get; set; }//速度到达窗口时间

        //位置
        public virtual string PosErrWarnLevel { get; set; }//位置偏差过大警告值
        public virtual string PosErrAlarmLevel { get; set; }//位置偏差过大报警值
        public virtual string SvonPosErrWarnLevel { get; set; }//使能时位置偏差过大警告值
        public virtual string SvonPosErrAlarmLevel { get; set; }//使能时位置偏差过大报警值
        public virtual string FollowingErrorWindow { get; set; }//位置跟踪误差阈值
        public virtual string FollowingErrorTimeout { get; set; }//位置跟踪误差过大判定时间
        public virtual string PositionWindow { get; set; }//位置到达阈值
        public virtual string PositionWindowTime { get; set; }//位置到达窗口时间
        public virtual string MinSoftwarePositionLimit { get; set; }//软件限位最小值
        public virtual string MaxSoftwarePositionLimit { get; set; }//软件限位最大值

        //单位
        public virtual string PositionUnit { get; set; }//位置单位
        public virtual string SpeedUnit { get; set; }//速度单位
        public virtual string AccelerateUnit { get; set; }//加速度单位

        //故障数据配置参数
        public virtual string AlarmCacheChennalSettingOne { get; set; }//故障数据配置参数1
        public virtual string AlarmCacheChennalSettingTwo { get; set; }//故障数据配置参数2
        public virtual string AlarmCacheChennalTimeSetting { get; set; }//故障数据配置通道时间
        #endregion

        #region 构造函数
        public FaultDataConfigViewModel()
        {
            ViewModelSet.FaultDataConfig = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.FAULTDATACONFIG;
            //采样通道分组初始化
            GroupSampleChannelInitialize();
        }
        #endregion       

        #region 公有方法
        //*************************************************************************
        //函数名称：FaultDataConfigLoaded
        //函数功能：FaultDataConfig界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.09
        //*************************************************************************
        public void FaultDataConfigLoaded()
        {
            int iRet = -1;

            try
            {
                //ObservableCollection初始化
                ObservableCollectionInitialize();

                if (IsInitialized == true)
                {
                    //标志位更新
                    IsInitialized = false;
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadAlarmCacheSettingParameter("All");
                    }
                    else
                    {
                        GetDefaultAlarmCacheSettingParameter("All");
                    }
                    
                    //GetDefaultFaultOscilloscopeSetParameter();
                }
                else
                {
                    //标志位更新
                    IsEvaluationAll = true;
                    IsLoadedAgain = true;

                    //InterfaceEvaluationFromGlobalVariable();

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadAlarmCacheSettingParameter("All");
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }

                    IsLoadedAgain = false;

                    if (SamplingDurationConfig != null)
                    {
                        if (GlobalCurrentInput.SamplingDurationIndexConfig < SamplingDurationConfig.Count && GlobalCurrentInput.SamplingDurationIndexConfig >= 0)
                        {
                            SelectedSamplingDurationConfig = SamplingDurationConfig[GlobalCurrentInput.SamplingDurationIndexConfig];
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_LOADED, "LimitAmplitudeLoaded", ex);
            }
        }
    
        //*************************************************************************
        //函数名称：SaveFaultDataConfigFile
        //函数功能：保存故障数据配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.12
        //*************************************************************************
        public void SaveFaultDataConfigFile(string strCategory)
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                #region 写入配置文件
                //生成配置文件
                iRet = ExcelHelper.WriteIntoExcel(FilePath.FaultDataLibrary + "配置文件.xlsx", GetFaultDataConfigToDataTable(), ExcelType.FaultDataConfig);
                if (iRet == RET.SUCCEEDED)
                {
                    //ShowNotification(2002);
                    ViewModelSet.Main?.ShowHintInfo("故障采集通道配置保存成功...");
                }
                else
                {
                    //ShowNotification(2003);
                    ViewModelSet.Main?.ShowHintInfo("故障采集通道配置保存失败，请确认文件是否被占用...");
                }
                #endregion

                #region 写入伺服
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取要写入的数据字典
                //iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                iRet = AddParameterInfoDictionary_ForWrite(strCategory, ref dicParameterInfo);                
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                //iRet = OthersHelper.CheckInputParametersCorrected_For_MotorFeedback(ref lstParameterInfo_ForWrite);     //由Lilbert于2022.11.24更改提示信息方式
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.FAULTDATACONFIG, TaskName.FaultDataConfig, lstTransmittingDataInfo); 
                #endregion
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULTDATA_SAVE_FAULTDATA_CONFIG_FILE, "SaveFaultDataConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadAlarmCacheSettingParameter
        //函数功能：读故障数据配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2024.03.12
        //*************************************************************************
        public void ReadAlarmCacheSettingParameter(string strCategory)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.FAULTDATACONFIG, TaskName.FaultDataConfig, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULTDATA_READ_ALARM_CACHE_SETTING_PARAMETER, "ReadAlarmCacheSettingParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultAlarmCacheSettingParameter
        //函数功能：获取故障数据参数配置的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.03.12
        //*************************************************************************
        public void GetDefaultAlarmCacheSettingParameter(string strCategory)
        {
            try
            {
                if (strCategory == "0")
                {
                    AlarmCacheChennalSettingOne = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 1 setting", "Default");//故障数据配置参数1
                    AlarmCacheChennalSettingTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 2 setting", "Default");//故障数据配置参数2

                    AlarmCacheChennalTimeSetting = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal Time setting", "Default");//故障数据配置通道时间                                     
                }
                else
                {
                    AlarmCacheChennalSettingOne = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 1 setting", "Default");//故障数据配置参数1
                    AlarmCacheChennalSettingTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 2 setting", "Default");//故障数据配置参数2

                    AlarmCacheChennalTimeSetting = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal Time setting", "Default");//故障数据配置通道时间 

                    GetDefaultFaultOscilloscopeSetParameter();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_GET_DEFAULT_PARAMETER, "GetDefaultMotorFeedbackParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationAlarmCacheSettingParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2024.03.12
        //*************************************************************************
        public void EvaluationAlarmCacheSettingParameter()
        {
            if (IsEvaluationAll)
            {
                //标志位更新
                IsEvaluationAll = false;

                //赋值               
                AlarmCacheChennalSettingOne = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 1 setting", "Index"));//故障数据配置参数1   
                AlarmCacheChennalSettingTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 2 setting", "Index"));//故障数据配置参数2         
                AlarmCacheChennalTimeSetting = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal Time setting", "Index"));//故障数据配置通道时间
            }
            else
            {
                AlarmCacheChennalSettingOne = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 1 setting", "Index"));//故障数据配置参数1   
                AlarmCacheChennalSettingTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 2 setting", "Index"));//故障数据配置参数2        
                AlarmCacheChennalTimeSetting = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal Time setting", "Index"));//故障数据配置通道时间
            }
            GetDefaultFaultOscilloscopeSetParameter();
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void OnSelectedSamplingPeriodConfigChanged()
        {
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDurationConfig);
            GlobalCurrentInput.SelectedSamplingPeriodConfig = SelectedSamplingPeriodConfig;
        }
        public void OnSelectedSamplingDurationConfigChanged()
        {
            GlobalCurrentInput.SelectedSamplingDurationConfig = SelectedSamplingDurationConfig;
            GetSamplingDurationUnit(SelectedSamplingDurationConfig);

            if (!IsLoadedAgain)
            {
                GlobalCurrentInput.SamplingDurationIndexConfig = SamplingDurationConfig.FindIndex(item => item == SelectedSamplingDurationConfig);
            }
        }
        public void OnSelectedSamplingDurationIndexConfigChanged()
        {
            if (SelectedSamplingDurationIndexConfig >= 5)
            {
                ViewModelSet.Main?.ShowHintInfo("采样量较大不能设置连续采样");
            }
            else
            {
                //ContinuousAcquisitionEnabled = true;
            }
        }

        public void OnSelectedSampleChannel1IndexConfigChanged()
        {
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDurationConfig);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel1IndexConfig = SelectedSampleChannel1IndexConfig;

            //获取采样名称
            SelectedSamplingChannel1 = GetSampleNameByIndex(SelectedSampleChannel1IndexConfig);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent(0);
        }

        public void OnSelectedSampleChannel2IndexConfigChanged()
        {
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDurationConfig);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel2IndexConfig = SelectedSampleChannel2IndexConfig;

            //获取采样名称
            SelectedSamplingChannel2 = GetSampleNameByIndex(SelectedSampleChannel2IndexConfig);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent(0);
        }
        public void OnSelectedSampleChannel3IndexConfigChanged()
        {
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDurationConfig);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel3IndexConfig = SelectedSampleChannel3IndexConfig;

            //获取采样名称
            SelectedSamplingChannel3 = GetSampleNameByIndex(SelectedSampleChannel3IndexConfig);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent(0);
        }
        public void OnSelectedSampleChannel4IndexConfigChanged()
        {
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDurationConfig);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel4IndexConfig = SelectedSampleChannel4IndexConfig;

            //获取采样名称
            SelectedSamplingChannel4 = GetSampleNameByIndex(SelectedSampleChannel4IndexConfig);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent(0);
        }
        public void OnSelectedSampleChannel5IndexConfigChanged()
        {
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDurationConfig);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel5IndexConfig = SelectedSampleChannel5IndexConfig;

            //获取采样名称
            SelectedSamplingChannel5 = GetSampleNameByIndex(SelectedSampleChannel5IndexConfig);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent(0);
        }
        public void OnSelectedSampleChannel6IndexConfigChanged()
        {
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDurationConfig);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel6IndexConfig = SelectedSampleChannel6IndexConfig;

            //获取采样名称
            SelectedSamplingChannel6 = GetSampleNameByIndex(SelectedSampleChannel6IndexConfig);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent(0);
        }
        public void OnSelectedSampleChannel7IndexConfigChanged()
        {
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDurationConfig);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel7IndexConfig = SelectedSampleChannel7IndexConfig;

            //获取采样名称
            SelectedSamplingChannel7 = GetSampleNameByIndex(SelectedSampleChannel7IndexConfig);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent(0);
        }
        public void OnSelectedSampleChannel8IndexConfigChanged()
        {
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDurationConfig);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel8IndexConfig = SelectedSampleChannel8IndexConfig;

            //获取采样名称
            SelectedSamplingChannel8 = GetSampleNameByIndex(SelectedSampleChannel8IndexConfig);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent(0);
        }
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：下拉列表初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.12.02&2021.07.05
        //*************************************************************************
        private void ObservableCollectionInitialize()
        {
            try
            {
                #region 示波器
                SamplingDurationConfig = new ObservableCollection<string>();
                SamplingPeriodConfig = new ObservableCollection<string>() { "62.5μs", "125μs", "250μs", "500μs", "1ms", "2ms", "4ms", "8ms" };
                #endregion
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_COMBOBOX_INITIALIZE, "ComboBoxInitialize", ex);
            }
        }

        //*************************************************************************
        //函数名称：GroupSampleChannelInitialize
        //函数功能：ComboBox分组初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.09
        //*************************************************************************
        private void GroupSampleChannelInitialize()
        {
            SampleChannelInfo1_Display = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                //new SampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},           //由Lilbert在通道后面添加单位，并去掉示波器标签显示
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},              //由lilbert于2022年8月5日添加位置增量
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo1 = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},                //由lilbert于2022年8月5日添加位置增量
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo2_Display = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo2 = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo3_Display = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo3 = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo4_Display = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo4 = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo5_Display = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},           //由Lilbert在通道后面添加单位，并去掉示波器标签显示
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},              //由lilbert于2022年8月5日添加位置增量
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo5 = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},                //由lilbert于2022年8月5日添加位置增量
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo6_Display = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo6 = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo7_Display = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo7 = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo8_Display = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo8 = new List<SampleChannelConfigInfoSet>()
            {
                new SampleChannelConfigInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new SampleChannelConfigInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new SampleChannelConfigInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new SampleChannelConfigInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new SampleChannelConfigInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new SampleChannelConfigInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new SampleChannelConfigInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new SampleChannelConfigInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            GroupedSampleChannelInfo1 = CollectionViewSource.GetDefaultView(SampleChannelInfo1_Display);     //由Lilbert添加SampleChannelInfo1_Display由于展示
            GroupedSampleChannelInfo1.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo2 = CollectionViewSource.GetDefaultView(SampleChannelInfo2_Display);
            GroupedSampleChannelInfo2.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo3 = CollectionViewSource.GetDefaultView(SampleChannelInfo3_Display);
            GroupedSampleChannelInfo3.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo4 = CollectionViewSource.GetDefaultView(SampleChannelInfo4_Display);
            GroupedSampleChannelInfo4.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo5 = CollectionViewSource.GetDefaultView(SampleChannelInfo5_Display);     //由Lilbert添加SampleChannelInfo1_Display由于展示
            GroupedSampleChannelInfo5.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo6 = CollectionViewSource.GetDefaultView(SampleChannelInfo6_Display);
            GroupedSampleChannelInfo6.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo7 = CollectionViewSource.GetDefaultView(SampleChannelInfo7_Display);
            GroupedSampleChannelInfo7.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo8 = CollectionViewSource.GetDefaultView(SampleChannelInfo8_Display);
            GroupedSampleChannelInfo8.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            SelectedSampleChannel1IndexConfig = 0;
            SelectedSampleChannel2IndexConfig = 0;
            SelectedSampleChannel3IndexConfig = 0;
            SelectedSampleChannel4IndexConfig = 0;
            SelectedSampleChannel5IndexConfig = 0;
            SelectedSampleChannel6IndexConfig = 0;
            SelectedSampleChannel7IndexConfig = 0;
            SelectedSampleChannel8IndexConfig = 0;

            SelectedSamplingChannel1 = GetSampleNameByIndex(SelectedSampleChannel1IndexConfig);
            SelectedSamplingChannel2 = GetSampleNameByIndex(SelectedSampleChannel2IndexConfig);
            SelectedSamplingChannel3 = GetSampleNameByIndex(SelectedSampleChannel3IndexConfig);
            SelectedSamplingChannel4 = GetSampleNameByIndex(SelectedSampleChannel4IndexConfig);
            SelectedSamplingChannel5 = GetSampleNameByIndex(SelectedSampleChannel5IndexConfig);
            SelectedSamplingChannel6 = GetSampleNameByIndex(SelectedSampleChannel6IndexConfig);
            SelectedSamplingChannel7 = GetSampleNameByIndex(SelectedSampleChannel7IndexConfig);
            SelectedSamplingChannel8 = GetSampleNameByIndex(SelectedSampleChannel8IndexConfig);
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2024.03.12
        //*************************************************************************
        private int AddParameterInfoDictionary(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                dicParameterInfo.Add("Alarm Cache chennal 1 setting", AlarmCacheChennalSettingOne);//故障数据配置参数1
                dicParameterInfo.Add("Alarm Cache chennal 2 setting", AlarmCacheChennalSettingTwo);//故障数据配置参数2
                dicParameterInfo.Add("Alarm Cache chennal Time setting", AlarmCacheChennalTimeSetting);//故障数据配置通道时间

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary_ForWrite
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2024.03.12
        //*************************************************************************
        private int AddParameterInfoDictionary_ForWrite(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            string strAlarmCacheChennalSettingOne = null;
            string strAlarmCacheChennalSettingTwo = null;
            string strAlarmCacheChennalTimeSetting = null;
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                strAlarmCacheChennalSettingOne = "0x" + GetSampleAddressByID(SelectedSampleChannel4IndexConfig) + GetSampleAddressByID(SelectedSampleChannel3IndexConfig) + GetSampleAddressByID(SelectedSampleChannel2IndexConfig) + GetSampleAddressByID(SelectedSampleChannel1IndexConfig);

                dicParameterInfo.Add("Alarm Cache chennal 1 setting", strAlarmCacheChennalSettingOne);//故障数据配置参数1

                strAlarmCacheChennalSettingTwo = "0x" + GetSampleAddressByID(SelectedSampleChannel8IndexConfig) + GetSampleAddressByID(SelectedSampleChannel7IndexConfig) + GetSampleAddressByID(SelectedSampleChannel6IndexConfig) + GetSampleAddressByID(SelectedSampleChannel5IndexConfig);

                dicParameterInfo.Add("Alarm Cache chennal 2 setting", strAlarmCacheChennalSettingTwo);//故障数据配置参数2

                if (SelectedSamplingPeriodConfig.Contains("μ"))
                {
                    strAlarmCacheChennalTimeSetting = Convert.ToString(Convert.ToInt32(SelectedSamplingPeriodConfig.Substring(0, SelectedSamplingPeriodConfig.IndexOf("μ"))) / 62.5);
                }
                else
                {
                    strAlarmCacheChennalTimeSetting = Convert.ToString(Convert.ToInt32(SelectedSamplingPeriodConfig.Substring(0, SelectedSamplingPeriodConfig.IndexOf("m"))) * 1000 / 62.5);
                }
                dicParameterInfo.Add("Alarm Cache chennal Time setting", strAlarmCacheChennalTimeSetting);//故障数据配置通道时间

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary_ForWrite", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：RefreshSampleChanelState_TriggerChannelContent
        //函数功能：更新触发通道下拉列表和采集通道下拉列表使能状态
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.26
        //*************************************************************************
        private void RefreshSampleChanelState_TriggerChannelContent(int i)
        {
            //TriggerChannel = new ObservableCollection<string>();

            #region 通道1
            if (SelectedSampleChannel1IndexConfig == 0)
            {
                Channel2Enabled = false;
                Channel3Enabled = false;
                Channel4Enabled = false;
                Channel5Enabled = false;
                Channel6Enabled = false;
                Channel7Enabled = false;
                Channel8Enabled = false;

                SelectedSampleChannel2IndexConfig = 0;
                SelectedSampleChannel3IndexConfig = 0;
                SelectedSampleChannel4IndexConfig = 0;
                SelectedSampleChannel5IndexConfig = 0;
                SelectedSampleChannel6IndexConfig = 0;
                SelectedSampleChannel7IndexConfig = 0;
                SelectedSampleChannel8IndexConfig = 0;
                
                return;
            }
            else
            {
                Channel2Enabled = true;

                //获取采样名称
                SelectedSamplingChannel2 = GetSampleNameByIndex(SelectedSampleChannel2IndexConfig);
                SelectedSamplingChannel3 = GetSampleNameByIndex(SelectedSampleChannel3IndexConfig);
                SelectedSamplingChannel4 = GetSampleNameByIndex(SelectedSampleChannel4IndexConfig);
                SelectedSamplingChannel5 = GetSampleNameByIndex(SelectedSampleChannel5IndexConfig);
                SelectedSamplingChannel6 = GetSampleNameByIndex(SelectedSampleChannel6IndexConfig);
                SelectedSamplingChannel7 = GetSampleNameByIndex(SelectedSampleChannel7IndexConfig);
                SelectedSamplingChannel8 = GetSampleNameByIndex(SelectedSampleChannel8IndexConfig);
            }
            #endregion

            #region 通道2
            if (SelectedSampleChannel2IndexConfig == 0)
            {
                Channel3Enabled = false;
                Channel4Enabled = false;
                Channel5Enabled = false;
                Channel6Enabled = false;
                Channel7Enabled = false;
                Channel8Enabled = false;

                SelectedSampleChannel3IndexConfig = 0;
                SelectedSampleChannel4IndexConfig = 0;
                SelectedSampleChannel5IndexConfig = 0;
                SelectedSampleChannel6IndexConfig = 0;
                SelectedSampleChannel7IndexConfig = 0;
                SelectedSampleChannel8IndexConfig = 0;
                return;
            }
            else
            {
                Channel3Enabled = true;

                SelectedSamplingChannel3 = GetSampleNameByIndex(SelectedSampleChannel3IndexConfig);
                SelectedSamplingChannel4 = GetSampleNameByIndex(SelectedSampleChannel4IndexConfig);
                SelectedSamplingChannel5 = GetSampleNameByIndex(SelectedSampleChannel5IndexConfig);
                SelectedSamplingChannel6 = GetSampleNameByIndex(SelectedSampleChannel6IndexConfig);
                SelectedSamplingChannel7 = GetSampleNameByIndex(SelectedSampleChannel7IndexConfig);
                SelectedSamplingChannel8 = GetSampleNameByIndex(SelectedSampleChannel8IndexConfig);
            }
            #endregion

            #region 通道3
            if (SelectedSampleChannel3IndexConfig == 0)
            {
                Channel4Enabled = false;
                Channel5Enabled = false;
                Channel6Enabled = false;
                Channel7Enabled = false;
                Channel8Enabled = false;
                SelectedSampleChannel4IndexConfig = 0;
                SelectedSampleChannel5IndexConfig = 0;
                SelectedSampleChannel6IndexConfig = 0;
                SelectedSampleChannel7IndexConfig = 0;
                SelectedSampleChannel8IndexConfig = 0;
                return;
            }
            else
            {
                Channel4Enabled = true;

                SelectedSamplingChannel4 = GetSampleNameByIndex(SelectedSampleChannel4IndexConfig);
                SelectedSamplingChannel5 = GetSampleNameByIndex(SelectedSampleChannel5IndexConfig);
                SelectedSamplingChannel6 = GetSampleNameByIndex(SelectedSampleChannel6IndexConfig);
                SelectedSamplingChannel7 = GetSampleNameByIndex(SelectedSampleChannel7IndexConfig);
                SelectedSamplingChannel8 = GetSampleNameByIndex(SelectedSampleChannel8IndexConfig);
            }
            #endregion

            #region 通道4
            if (SelectedSampleChannel4IndexConfig == 0)
            {
                Channel5Enabled = false;
                Channel6Enabled = false;
                Channel7Enabled = false;
                Channel8Enabled = false;

                SelectedSampleChannel5IndexConfig = 0;
                SelectedSampleChannel6IndexConfig = 0;
                SelectedSampleChannel7IndexConfig = 0;
                SelectedSampleChannel8IndexConfig = 0;
                return;
            }
            else
            {
                Channel5Enabled = true;

                SelectedSamplingChannel5 = GetSampleNameByIndex(SelectedSampleChannel5IndexConfig);
                SelectedSamplingChannel6 = GetSampleNameByIndex(SelectedSampleChannel6IndexConfig);
                SelectedSamplingChannel7 = GetSampleNameByIndex(SelectedSampleChannel7IndexConfig);
                SelectedSamplingChannel8 = GetSampleNameByIndex(SelectedSampleChannel8IndexConfig);
            }
            #endregion          

            #region 通道5
            if (SelectedSampleChannel5IndexConfig == 0)
            {
                Channel6Enabled = false;
                Channel7Enabled = false;
                Channel8Enabled = false;

                SelectedSampleChannel6IndexConfig = 0;
                SelectedSampleChannel7IndexConfig = 0;
                SelectedSampleChannel8IndexConfig = 0;
                return;
            }
            else
            {
                Channel6Enabled = true;

                SelectedSamplingChannel6 = GetSampleNameByIndex(SelectedSampleChannel6IndexConfig);
                SelectedSamplingChannel7 = GetSampleNameByIndex(SelectedSampleChannel7IndexConfig);
                SelectedSamplingChannel8 = GetSampleNameByIndex(SelectedSampleChannel8IndexConfig);
            }
            #endregion

            #region 通道6
            if (SelectedSampleChannel6IndexConfig == 0)
            {
                Channel7Enabled = false;
                Channel8Enabled = false;

                SelectedSampleChannel7IndexConfig = 0;
                SelectedSampleChannel8IndexConfig = 0;
                return;
            }
            else
            {
                Channel7Enabled = true;

                SelectedSamplingChannel7 = GetSampleNameByIndex(SelectedSampleChannel7IndexConfig);
                SelectedSamplingChannel8 = GetSampleNameByIndex(SelectedSampleChannel8IndexConfig);
            }
            #endregion

            #region 通道7
            if (SelectedSampleChannel7IndexConfig == 0)
            {
                Channel8Enabled = false;

                SelectedSampleChannel8IndexConfig = 0;
                return;
            }
            else
            {
                Channel8Enabled = true;

                SelectedSamplingChannel8 = GetSampleNameByIndex(SelectedSampleChannel8IndexConfig);
            }
            #endregion

            #region 通道8
            if (SelectedSampleChannel8IndexConfig != 0)
            {                
            }
            
            #endregion                            
        }

        //*************************************************************************
        //函数名称：RefreshSamplingDurationComboBox
        //函数功能：更新触发总长
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.12
        //*************************************************************************
        private void RefreshSamplingDuration(string strLastDuration)
        {
            int iChannelNumber = 0;//通道数
            int iInterval = 0;//间隔数
            double dSamplingDuration = 0;
            double dBiggestTimes = 0;
            SamplingDurationConfig = new ObservableCollection<string>();

            try
            {
                if (SelectedSampleChannel1IndexConfig != 0) iChannelNumber++;
                if (SelectedSampleChannel2IndexConfig != 0) iChannelNumber++;
                if (SelectedSampleChannel3IndexConfig != 0) iChannelNumber++;
                if (SelectedSampleChannel4IndexConfig != 0) iChannelNumber++;
                if (SelectedSampleChannel5IndexConfig != 0) iChannelNumber++;
                if (SelectedSampleChannel6IndexConfig != 0) iChannelNumber++;
                if (SelectedSampleChannel7IndexConfig != 0) iChannelNumber++;
                if (SelectedSampleChannel8IndexConfig != 0) iChannelNumber++;
                if (iChannelNumber == 0) return;

                //获取采样间隔
                switch (iChannelNumber)
                {
                    case 1:
                        dBiggestTimes = 4000;
                        iInterval = Convert.ToInt32(Math.Floor(dBiggestTimes / 10));
                        break;
                    case 2:
                        dBiggestTimes = 2000;
                        iInterval = Convert.ToInt32(Math.Floor(dBiggestTimes / 10));
                        break;
                    case 3:
                        dBiggestTimes = 1300;
                        iInterval = Convert.ToInt32(Math.Floor(dBiggestTimes / 10));
                        break;
                    case 4:
                        dBiggestTimes = 1000;
                        iInterval = Convert.ToInt32(Math.Floor(dBiggestTimes / 10));
                        break;
                    default:
                        break;
                }

                //前10个采集时长  
                for (int iNumber = 1; iNumber <= 13; iNumber++)
                {
                    //获取采样时长，单位转换成ms
                    if (SelectedSamplingPeriodConfig.IndexOf("μ") == -1)
                    {
                        dSamplingDuration = iInterval * iNumber * Convert.ToDouble(SelectedSamplingPeriodConfig.Replace("ms", ""));
                    }
                    else
                    {
                        dSamplingDuration = iInterval * iNumber * Convert.ToDouble(SelectedSamplingPeriodConfig.Replace("μs", "")) / 1000;
                    }

                    SamplingDurationConfig.Add(dSamplingDuration.ToString() + "ms");
                }

                //后20个采集时长
                //for (int iNumber = 11; iNumber <= 20; iNumber++)
                //{
                //    //获取采样时长，单位转换成ms
                //    if (SelectedSamplingPeriod.IndexOf("μ") == -1)
                //    {
                //        dSamplingDuration = iInterval * iNumber * Convert.ToDouble(SelectedSamplingPeriod.Replace("ms", ""));
                //    }
                //    else
                //    {
                //        dSamplingDuration = iInterval * iNumber * Convert.ToDouble(SelectedSamplingPeriod.Replace("μs", "")) / 1000;
                //    }

                //    if (iNumber % 3 == 0)
                //    {
                //        SamplingDuration.Add(dSamplingDuration.ToString() + "ms");
                //    }
                //}

                //选中的采集时长
                if (SamplingDurationConfig.Count > 0)
                {
                    if (string.IsNullOrEmpty(strLastDuration))
                    {
                        SelectedSamplingDurationConfig = SamplingDurationConfig[0];
                    }
                    else
                    {
                        if (SamplingDurationConfig.Contains(strLastDuration))
                        {
                            SelectedSamplingDurationConfig = strLastDuration;
                        }
                        else
                        {
                            SelectedSamplingDurationConfig = SamplingDurationConfig[0];
                        }
                    }
                }
                else
                {
                    SelectedSamplingDurationConfig = "0ms";
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_REFRESH_SAMPLING_DURATION_COMBO_BOX, "RefreshSamplingDurationComboBox", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetSamplingDurationUnit
        //函数功能：获取单位信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.12
        //*************************************************************************
        private void GetSamplingDurationUnit(string strDaration)
        {
            if (string.IsNullOrEmpty(strDaration))
            {
                SamplingDurationUnit = "采样时长";
            }
            else
            {
                if (strDaration.IndexOf("μ") != -1)
                {
                    SamplingDurationUnit = "采样时长" + "   " + "单位：μs";
                }
                else if (strDaration.IndexOf("m") != -1)
                {
                    SamplingDurationUnit = "采样时长" + "   " + "单位：ms";
                }
                else
                {
                    SamplingDurationUnit = "采样时长" + "   " + "单位：s";
                }
            }
        }

        //*************************************************************************
        //函数名称：GetSampleNameByIndex
        //函数功能：通过索引号获取采样名称
        //
        //输入参数：int Index   索引号
        //         
        //输出参数：string Name  采样名称
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.12
        //*************************************************************************
        private string GetSampleNameByIndex(int Index)
        {
            var query = SampleChannelInfo1_Display.Where(o => o.ID == Index).FirstOrDefault<SampleChannelConfigInfoSet>();
            if (query != null)
            {
                return Convert.ToString(query.ItemName);
            }
            else
            {
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultFaultOscilloscopeSetParameter
        //函数功能：获取默认故障示波器配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.12
        //*************************************************************************
        private void GetDefaultFaultOscilloscopeSetParameter()
        {
            //示波器
            if (int.Parse(AlarmCacheChennalTimeSetting) * 62.5 < 1000)
            {
                SelectedSamplingPeriodConfig = (int.Parse(AlarmCacheChennalTimeSetting) * 62.5).ToString() + "μs";
            }
            else
            {
                SelectedSamplingPeriodConfig = ((int.Parse(AlarmCacheChennalTimeSetting) * 62.5) / 1000).ToString() + "ms";
            }            

            //SelectedSamplingPeriodConfig = "62.5μs";
            SelectedSamplingDurationConfig = "0ms";            

            SelectedSampleChannel1IndexConfig = GetSampleIndexByAddress(int.Parse(AlarmCacheChennalSettingOne).ToString("X8").Substring(6, 2));
            SelectedSampleChannel2IndexConfig = GetSampleIndexByAddress(int.Parse(AlarmCacheChennalSettingOne).ToString("X8").Substring(4, 2));
            SelectedSampleChannel3IndexConfig = GetSampleIndexByAddress(int.Parse(AlarmCacheChennalSettingOne).ToString("X8").Substring(2, 2));
            SelectedSampleChannel4IndexConfig = GetSampleIndexByAddress(int.Parse(AlarmCacheChennalSettingOne).ToString("X8").Substring(0, 2));

            SelectedSampleChannel5IndexConfig = GetSampleIndexByAddress(int.Parse(AlarmCacheChennalSettingTwo).ToString("X8").Substring(6, 2));
            SelectedSampleChannel6IndexConfig = GetSampleIndexByAddress(int.Parse(AlarmCacheChennalSettingTwo).ToString("X8").Substring(4, 2));
            SelectedSampleChannel7IndexConfig = GetSampleIndexByAddress(int.Parse(AlarmCacheChennalSettingTwo).ToString("X8").Substring(2, 2));
            SelectedSampleChannel8IndexConfig = GetSampleIndexByAddress(int.Parse(AlarmCacheChennalSettingTwo).ToString("X8").Substring(0, 2));

            //SelectedSampleChannel1IndexConfig = 0;
            //SelectedSampleChannel2IndexConfig = 0;
            //SelectedSampleChannel3IndexConfig = 0;
            //SelectedSampleChannel4IndexConfig = 0;
            //SelectedSampleChannel5IndexConfig = 0;
            //SelectedSampleChannel6IndexConfig = 0;
            //SelectedSampleChannel7IndexConfig = 0;
            //SelectedSampleChannel8IndexConfig = 0;            
        }

        //*************************************************************************
        //函数名称：GetSampleIndexByAddress
        //函数功能：通过地址获取采样索引号
        //
        //输入参数：string Address  采样地址
        //         
        //输出参数：int Index    采样索引号
        //        
        //编码作者：Lilbert
        //更新时间：2024.03.12
        //*************************************************************************
        private int GetSampleIndexByAddress(string Address)
        {
            var query = SampleChannelInfo1.Where(o => o.Address == Address).FirstOrDefault<SampleChannelConfigInfoSet>();
            if (query != null)
            {
                return Convert.ToInt32(query.ID);
            }
            else
            {
                return 0;
            }
        }

        //*************************************************************************
        //函数名称：GetSampleIndexByName
        //函数功能：通过地址获取采样索引号
        //
        //输入参数：string Address  采样地址
        //         
        //输出参数：int Index    采样索引号
        //        
        //编码作者：Ryan
        //更新时间：2021.01.15
        //*************************************************************************
        private string GetSampleAddressByID(int ID)
        {
            var query = SampleChannelInfo1.Where(o => o.ID == ID).FirstOrDefault<SampleChannelConfigInfoSet>();
            if (query != null)
            {
                return Convert.ToString(query.Address);
            }
            else
            {
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetAmplitudeConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.12
        //*************************************************************************
        private DataTable GetFaultDataConfigToDataTable()
        {
            DataTable dt = new DataTable();

            try
            {
                OthersHelper.MakeValueToDataTable(FileInterface.FAULTDATACONFIG, "FaultDataConfig", 
                                                  "SelectedSampleChannel1IndexConfig", SelectedSamplingChannel1, 
                                                  "SelectedSampleChannel2IndexConfig", SelectedSamplingChannel2, 
                                                  "SelectedSampleChannel3IndexConfig", SelectedSamplingChannel3, 
                                                  "SelectedSampleChannel4IndexConfig", SelectedSamplingChannel4, 
                                                  "SelectedSampleChannel5IndexConfig", SelectedSamplingChannel5,
                                                  "SelectedSampleChannel6IndexConfig", SelectedSamplingChannel6,
                                                  "SelectedSampleChannel7IndexConfig", SelectedSamplingChannel7,
                                                  "SelectedSampleChannel8IndexConfig", SelectedSamplingChannel8,
                                                  "SelectedSamplingPeriodConfig", SelectedSamplingPeriodConfig, ref dt);
                
                return dt;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULTDATA_GET_FAULTDATA_CONFIG_TO_DATATABLE, "GetFaultDataConfigToDataTable", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetAmplitudeConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int GetAmplitudeConfigFromDataTable(DataTable dt)
        {
            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                SpeedUnit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Profile Velocity", "Unit");
                AccelerateUnit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Acceleration", "Unit");
                PositionUnit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Software Position Limit", "Unit");
                CurrentUnit.Speed = SpeedUnit;
                CurrentUnit.Acceleration = AccelerateUnit;
                CurrentUnit.Position = PositionUnit;

                ForwardInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Forward Internal Torque Limit", "Default");
                ReverseInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Reverse Internal Torque Limit", "Default");
                ForwardExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Forward External Torque Limit", "Default");
                ReverseExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Reverse External Torque Limit", "Default");
                EmergencyStopTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Emergency Stop Torque Limit", "Default");

                ServoOnSpeedLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Servo On Speed Limit", "Default");
                TrqctrlSpeedLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Trqctrl Speed Limit", "Default");
                MaxProfileVelocity = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Profile Velocity", "Default");
                MaxAcceleration = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Acceleration", "Default");
                MaxDeceleration = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Deceleration", "Default");
                VelocityWindow = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Velocity Window", "Default");
                VelocityWindowTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Velocity Window Time", "Default");

                PosErrWarnLevel = OthersHelper.GetCellValueFromDataTable(dt, "Name", "PosErr Warn Level", "Default");
                PosErrAlarmLevel = OthersHelper.GetCellValueFromDataTable(dt, "Name", "PosErr Alarm Level", "Default");
                SvonPosErrWarnLevel = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Svon PosErr Warn Level", "Default");
                SvonPosErrAlarmLevel = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Svon PosErr Alarm Level", "Default");
                FollowingErrorWindow = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Following Error Window", "Default");
                FollowingErrorTimeout = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Following Error Timeout", "Default");
                PositionWindow = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Position Window", "Default");
                PositionWindowTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Position Window Time", "Default");
                MinSoftwarePositionLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Min Software Position Limit", "Default");
                MaxSoftwarePositionLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Software Position Limit", "Default");

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_GET_AMPLITUDE_CONFIG_FROM_DATATABLE, "GetAmplitudeConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                SelectedSamplingPeriodConfig = GlobalCurrentInput.SelectedSamplingPeriodConfig;
                SelectedSamplingDurationConfig = GlobalCurrentInput.SelectedSamplingDurationConfig;

                SelectedSampleChannel1IndexConfig = GlobalCurrentInput.SelectedSampleChannel1IndexConfig;
                SelectedSampleChannel2IndexConfig = GlobalCurrentInput.SelectedSampleChannel2IndexConfig;
                SelectedSampleChannel3IndexConfig = GlobalCurrentInput.SelectedSampleChannel3IndexConfig;
                SelectedSampleChannel4IndexConfig = GlobalCurrentInput.SelectedSampleChannel4IndexConfig;
                SelectedSampleChannel5IndexConfig = GlobalCurrentInput.SelectedSampleChannel5IndexConfig;
                SelectedSampleChannel6IndexConfig = GlobalCurrentInput.SelectedSampleChannel6IndexConfig;
                SelectedSampleChannel7IndexConfig = GlobalCurrentInput.SelectedSampleChannel7IndexConfig;
                SelectedSampleChannel8IndexConfig = GlobalCurrentInput.SelectedSampleChannel8IndexConfig;


                ForwardInternalTorqueLimit = GlobalCurrentInput.ForwardInternalTorqueLimit;//正转内部转矩限制值
                ReverseInternalTorqueLimit = GlobalCurrentInput.ReverseInternalTorqueLimit;//反转内部转矩限制值
                ForwardExternalTorqueLimit = GlobalCurrentInput.ForwardExternalTorqueLimit;//正转外部转矩限制值
                ReverseExternalTorqueLimit = GlobalCurrentInput.ReverseExternalTorqueLimit;//反转外部转矩限制值
                EmergencyStopTorqueLimit = GlobalCurrentInput.EmergencyStopTorqueLimit;//紧急停止转矩限制值

                ServoOnSpeedLimit = GlobalCurrentInput.ServoOnSpeedLimit;//使能时速度限制值
                TrqctrlSpeedLimit = GlobalCurrentInput.TrqctrlSpeedLimit;//转矩控制时速度限制值
                MaxProfileVelocity = GlobalCurrentInput.MaxProfileVelocity;//最大轮廓速度
                MaxAcceleration = GlobalCurrentInput.MaxAcceleration;//最大加速度
                MaxDeceleration = GlobalCurrentInput.MaxDeceleration;//最大减速度
                VelocityWindow = GlobalCurrentInput.VelocityWindow;//速度到达阈值
                VelocityWindowTime = GlobalCurrentInput.VelocityWindowTime;//速度到达窗口时间

                PosErrWarnLevel = GlobalCurrentInput.PosErrWarnLevel;//位置偏差过大警告值
                PosErrAlarmLevel = GlobalCurrentInput.PosErrAlarmLevel;//位置偏差过大报警值
                SvonPosErrWarnLevel = GlobalCurrentInput.SvonPosErrWarnLevel;//使能时位置偏差过大警告值
                SvonPosErrAlarmLevel = GlobalCurrentInput.SvonPosErrAlarmLevel;//使能时位置偏差过大报警值
                FollowingErrorWindow = GlobalCurrentInput.FollowingErrorWindow;//位置跟踪误差阈值
                FollowingErrorTimeout = GlobalCurrentInput.FollowingErrorTimeout;//位置跟踪误差过大判定时间
                PositionWindow = GlobalCurrentInput.PositionWindow;//位置到达阈值
                PositionWindowTime = GlobalCurrentInput.PositionWindowTime;//位置到达窗口时间
                MinSoftwarePositionLimit = GlobalCurrentInput.MinSoftwarePositionLimit;//软件限位最小值
                MaxSoftwarePositionLimit = GlobalCurrentInput.MaxSoftwarePositionLimit;//软件限位最大值

                SpeedUnit = GlobalCurrentInput.SpeedUnit;
                AccelerateUnit = GlobalCurrentInput.AccelerationUnit;
                PositionUnit = GlobalCurrentInput.PositionUnit;

                CurrentUnit.Speed = SpeedUnit;
                CurrentUnit.Acceleration = AccelerateUnit;
                CurrentUnit.Position = PositionUnit;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowHintInfo(string strInfo)
        {
            ViewModelSet.Main?.ShowHintInfo(strInfo);
        }
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion

        public class SampleChannelConfigInfoSet
        {
            public string GroupName { get; set; }
            public int ID { get; set; }
            public string ItemName { get; set; }
            public string Address { get; set; }
        }
    }
}