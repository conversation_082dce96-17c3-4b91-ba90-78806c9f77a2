﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Data;
using ServoStudio.Models;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class LimitAmplitudeViewModel
    {
        #region 私有字段
        private static bool IsPositionSet = false;
        private static bool IsTorqueSet = false;
        private static bool IsVoltageSet = false;
        private static bool IsInitialized = true;
        private static bool IsEvaluationAll = true;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 服务
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 属性
        public virtual string SelectedTabIndex { get; set; }//选中的TabItem
        //转矩
        public virtual string ForwardInternalTorqueLimit { get; set; }//正转内部转矩限制值
        public virtual string ReverseInternalTorqueLimit { get; set; }//反转内部转矩限制值
        public virtual string ForwardExternalTorqueLimit { get; set; }//正转外部转矩限制值
        public virtual string ReverseExternalTorqueLimit { get; set; }//反转外部转矩限制值
        public virtual string EmergencyStopTorqueLimit { get; set; }//紧急停止转矩限制值

        //速度
        public virtual string ServoOnSpeedLimit { get; set; }//使能时速度限制值
        public virtual string TrqctrlSpeedLimit { get; set; }//转矩控制时速度限制值
        public virtual string MaxProfileVelocity { get; set; }//最大轮廓速度
        public virtual string MaxAcceleration { get; set; }//最大加速度
        public virtual string MaxDeceleration { get; set; }//最大减速度
        public virtual string VelocityWindow { get; set; }//速度到达阈值
        public virtual string VelocityWindowTime { get; set; }//速度到达窗口时间

        //位置
        public virtual string PosErrWarnLevel { get; set; }//位置偏差过大警告值
        public virtual string PosErrAlarmLevel { get; set; }//位置偏差过大报警值
        public virtual string SvonPosErrWarnLevel { get; set; }//使能时位置偏差过大警告值
        public virtual string SvonPosErrAlarmLevel { get; set; }//使能时位置偏差过大报警值
        public virtual string FollowingErrorWindow { get; set; }//位置跟踪误差阈值
        public virtual string FollowingErrorTimeout { get; set; }//位置跟踪误差过大判定时间
        public virtual string PositionWindow { get; set; }//位置到达阈值
        public virtual string PositionWindowTime { get; set; }//位置到达窗口时间
        public virtual string MinSoftwarePositionLimit { get; set; }//软件限位最小值
        public virtual string MaxSoftwarePositionLimit { get; set; }//软件限位最大值

        //单位
        public virtual string PositionUnit { get; set; }//位置单位
        public virtual string SpeedUnit { get; set; }//速度单位
        public virtual string AccelerateUnit { get; set; }//加速度单位
        #endregion

        #region 构造函数
        public LimitAmplitudeViewModel()
        {
            ViewModelSet.LimitAmplitude = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.LIMITAMPLITUDE;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：LimitAmplitudeLoaded
        //函数功能：LimitAmplitude界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void LimitAmplitudeLoaded()
        {
            int iRet = -1;

            try
            {
                if (IsInitialized == true)
                {
                    //标志位更新
                    IsInitialized = false;
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadLimitAmplitudeParameter("All");
                    }
                    else
                    {
                        GetDefaultLimitAmplitudeParameter("All");
                    }
                }
                else
                {
                    //标志位更新
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadLimitAmplitudeParameter("All");
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_LOADED, "LimitAmplitudeLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：WriteLimitAmplitudeParameter
        //函数功能：写电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void WriteLimitAmplitudeParameter(string strCategory)
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.LIMITAMPLITUDE, TaskName.LimitAmplitude, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_WRITE_PARAMETER, "WriteLimitAmplitudeParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadLimitAmplitudeParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void ReadLimitAmplitudeParameter(string strCategory)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.LIMITAMPLITUDE, TaskName.LimitAmplitude, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_READ_MOTOR_FEEDBACK_PARAMETER, "ReadLimitAmplitudeParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultLimitAmplitudeParameter
        //函数功能：获取限幅保护的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.15
        //*************************************************************************
        public void GetDefaultLimitAmplitudeParameter(string strCategory)
        {
            SpeedUnit = DefaultUnit.SpeedUnit;       
            AccelerateUnit = DefaultUnit.AccelerationUnit;       
            PositionUnit = DefaultUnit.PositionUnit;

            CurrentUnit.Speed = SpeedUnit;
            CurrentUnit.Acceleration = AccelerateUnit;
            CurrentUnit.Position = PositionUnit;

            try
            {
                if (strCategory == "0")
                {
                    ServoOnSpeedLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Servo On Speed Limit", "Default");
                    TrqctrlSpeedLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Trqctrl Speed Limit", "Default");
                    MaxProfileVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Profile Velocity", "Default");
                    MaxAcceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Acceleration", "Default");
                    MaxDeceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Deceleration", "Default");
                    VelocityWindow = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Velocity Window", "Default");
                    VelocityWindowTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Velocity Window Time", "Default");
                }
                else if (strCategory == "1")
                {
                    PosErrWarnLevel = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "PosErr Warn Level", "Default");
                    PosErrAlarmLevel = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "PosErr Alarm Level", "Default");
                    SvonPosErrWarnLevel = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Svon PosErr Warn Level", "Default");
                    SvonPosErrAlarmLevel = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Svon PosErr Alarm Level", "Default");
                    FollowingErrorWindow = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Following Error Window", "Default");
                    FollowingErrorTimeout = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Following Error Timeout", "Default");
                    PositionWindow = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Window", "Default");
                    PositionWindowTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Window Time", "Default");
                    MinSoftwarePositionLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Min Software Position Limit", "Default");
                    MaxSoftwarePositionLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Software Position Limit", "Default");
                }
                else if (strCategory == "2")
                {
                    ForwardInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward Internal Torque Limit", "Default");
                    ReverseInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse Internal Torque Limit", "Default");
                    ForwardExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward External Torque Limit", "Default");
                    ReverseExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse External Torque Limit", "Default");
                    EmergencyStopTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Emergency Stop Torque Limit", "Default");
                }      
                else
                {
                    ForwardInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward Internal Torque Limit", "Default");
                    ReverseInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse Internal Torque Limit", "Default");
                    ForwardExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward External Torque Limit", "Default");
                    ReverseExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse External Torque Limit", "Default");
                    EmergencyStopTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Emergency Stop Torque Limit", "Default");

                    ServoOnSpeedLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Servo On Speed Limit", "Default");
                    TrqctrlSpeedLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Trqctrl Speed Limit", "Default");
                    MaxProfileVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Profile Velocity", "Default");
                    MaxAcceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Acceleration", "Default");
                    MaxDeceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Deceleration", "Default");
                    VelocityWindow = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Velocity Window", "Default");
                    VelocityWindowTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Velocity Window Time", "Default");

                    PosErrWarnLevel = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "PosErr Warn Level", "Default");
                    PosErrAlarmLevel = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "PosErr Alarm Level", "Default");
                    SvonPosErrWarnLevel = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Svon PosErr Warn Level", "Default");
                    SvonPosErrAlarmLevel = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Svon PosErr Alarm Level", "Default");
                    FollowingErrorWindow = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Following Error Window", "Default");
                    FollowingErrorTimeout = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Following Error Timeout", "Default");
                    PositionWindow = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Window", "Default");
                    PositionWindowTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Window Time", "Default");
                    MinSoftwarePositionLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Min Software Position Limit", "Default");
                    MaxSoftwarePositionLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Software Position Limit", "Default");                
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_GET_DEFAULT_PARAMETER, "GetDefaultLimitAmplitudeParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：SaveLimitAmplitudeConfigFile
        //函数功能：保存限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void SaveLimitAmplitudeConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                //选择配置文件存放位置
                iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.AmplitudeConfig);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //生成配置文件
                iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetAmplitudeConfigToDataTable(), ExcelType.AmplitudeConfig);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2002);
                }
                else
                {
                    ShowNotification(2003);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_SAVE_AMPLITUDE_CONFIG_FILE, "SaveLimitAmplitudeConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetLimitAmplitudeConfigFile
        //函数功能：获取限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void GetLimitAmplitudeConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            DataTable dt = null;

            try
            {
                //获取配置文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(strFilePath, ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //判断是否导入正确的配置文件
                if (dt.Columns[3].ColumnName != FileInterface.LIMITAMPLITUDE)
                {
                    ShowNotification(2001);
                    return;
                }

                //导入参数         
                iRet = GetAmplitudeConfigFromDataTable(dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //是否下载
                OthersHelper.GetWindowsStartupPosition();
                if (MessageBoxService.ShowMessage("是否下载当前导入的全部限幅保护参数...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    WriteLimitAmplitudeParameter("All");
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_GET_AMPLITUDE_CONFIG_FILE, "GetLimitAmplitudeConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationLimitAmplitudeParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void EvaluationLimitAmplitudeParameter()
        {
            //获取当前的单位
            SpeedUnit = SelectUnit.Speed;
            AccelerateUnit = SelectUnit.Acceleration;
            PositionUnit = SelectUnit.Position;

            CurrentUnit.Speed = SpeedUnit;
            CurrentUnit.Acceleration = AccelerateUnit;
            CurrentUnit.Position = PositionUnit;

            if (IsEvaluationAll)
            {
                //更新标志位
                IsEvaluationAll = false;

                //赋值
                ForwardInternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward Internal Torque Limit", "Index"));//正转内部转矩限制值
                ReverseInternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse Internal Torque Limit", "Index"));//反转内部转矩限制值
                ForwardExternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward External Torque Limit", "Index"));//正转外部转矩限制值
                ReverseExternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse External Torque Limit", "Index"));//反转外部转矩限制值
                EmergencyStopTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Emergency Stop Torque Limit", "Index"));//紧急停止转矩限制值

                ServoOnSpeedLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Servo On Speed Limit", "Index"));//使能时速度限制值
                TrqctrlSpeedLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Trqctrl Speed Limit", "Index"));//转矩控制时速度限制值
                VelocityWindowTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Velocity Window Time", "Index"));//速度到达窗口时间

                MaxProfileVelocity = OthersHelper.ExchangeUnit("Max Profile Velocity", DefaultUnit.SpeedUnit + "-" + SpeedUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Profile Velocity", "Index")));//最大轮廓速度
                MaxAcceleration = OthersHelper.ExchangeUnit("Max Acceleration", DefaultUnit.AccelerationUnit + "-" + AccelerateUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Acceleration", "Index")));//最大加速度
                MaxDeceleration = OthersHelper.ExchangeUnit("Max Deceleration", DefaultUnit.AccelerationUnit + "-" + AccelerateUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Deceleration", "Index")));//最大减速度
                VelocityWindow = OthersHelper.ExchangeUnit("Velocity Window", DefaultUnit.SpeedUnit + "-" + SpeedUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Velocity Window", "Index")));//速度到达阈值

                PosErrWarnLevel = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "PosErr Warn Level", "Index"));//位置偏差过大警告值
                SvonPosErrWarnLevel = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Svon PosErr Warn Level", "Index"));//使能时位置偏差过大警告值
                FollowingErrorTimeout = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Following Error Timeout", "Index"));//位置跟踪误差过大判定时间
                PositionWindowTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Window Time", "Index"));//位置到达窗口时间

                PositionWindow = OthersHelper.ExchangeUnit("Position Window", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Window", "Index")));//位置到达阈值
                FollowingErrorWindow = OthersHelper.ExchangeUnit("Following Error Window", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Following Error Window", "Index")));//位置跟踪误差阈值
                PosErrAlarmLevel = OthersHelper.ExchangeUnit("PosErr Alarm Level", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "PosErr Alarm Level", "Index")));//位置偏差过大报警值
                SvonPosErrAlarmLevel = OthersHelper.ExchangeUnit("Svon PosErr Alarm Level", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Svon PosErr Alarm Level", "Index")));//使能时位置偏差过大报警值
                MinSoftwarePositionLimit = OthersHelper.ExchangeUnit("Min Software Position Limit", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Min Software Position Limit", "Index")));//软件限位最小值
                MaxSoftwarePositionLimit = OthersHelper.ExchangeUnit("Max Software Position Limit", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Software Position Limit", "Index")));//软件限位最大值            
            }
            else
            {
                if (SelectedTabIndex == "0")
                {
                    ServoOnSpeedLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Servo On Speed Limit", "Index"));//使能时速度限制值
                    TrqctrlSpeedLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Trqctrl Speed Limit", "Index"));//转矩控制时速度限制值
                    VelocityWindowTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Velocity Window Time", "Index"));//速度到达窗口时间

                    MaxProfileVelocity = OthersHelper.ExchangeUnit("Max Profile Velocity", DefaultUnit.SpeedUnit + "-" + SpeedUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Profile Velocity", "Index")));//最大轮廓速度
                    MaxAcceleration = OthersHelper.ExchangeUnit("Max Acceleration", DefaultUnit.AccelerationUnit + "-" + AccelerateUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Acceleration", "Index")));//最大加速度
                    MaxDeceleration = OthersHelper.ExchangeUnit("Max Deceleration", DefaultUnit.AccelerationUnit + "-" + AccelerateUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Deceleration", "Index")));//最大减速度
                    VelocityWindow = OthersHelper.ExchangeUnit("Velocity Window", DefaultUnit.SpeedUnit + "-" + SpeedUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Velocity Window", "Index")));//速度到达阈值

                }
                else if (SelectedTabIndex == "1")
                {
                    PosErrWarnLevel = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "PosErr Warn Level", "Index"));//位置偏差过大警告值
                    SvonPosErrWarnLevel = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Svon PosErr Warn Level", "Index"));//使能时位置偏差过大警告值
                    FollowingErrorTimeout = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Following Error Timeout", "Index"));//位置跟踪误差过大判定时间
                    PositionWindowTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Window Time", "Index"));//位置到达窗口时间

                    PositionWindow = OthersHelper.ExchangeUnit("Position Window", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Window", "Index")));//位置到达阈值
                    FollowingErrorWindow = OthersHelper.ExchangeUnit("Following Error Window", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Following Error Window", "Index")));//位置跟踪误差阈值
                    PosErrAlarmLevel = OthersHelper.ExchangeUnit("PosErr Alarm Level", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "PosErr Alarm Level", "Index")));//位置偏差过大报警值
                    SvonPosErrAlarmLevel = OthersHelper.ExchangeUnit("Svon PosErr Alarm Level", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Svon PosErr Alarm Level", "Index")));//使能时位置偏差过大报警值
                    MinSoftwarePositionLimit = OthersHelper.ExchangeUnit("Min Software Position Limit", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Min Software Position Limit", "Index")));//软件限位最小值
                    MaxSoftwarePositionLimit = OthersHelper.ExchangeUnit("Max Software Position Limit", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Max Software Position Limit", "Index")));//软件限位最大值

                }
                else if (SelectedTabIndex == "2")
                {
                    ForwardInternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward Internal Torque Limit", "Index"));//正转内部转矩限制值
                    ReverseInternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse Internal Torque Limit", "Index"));//反转内部转矩限制值
                    ForwardExternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward External Torque Limit", "Index"));//正转外部转矩限制值
                    ReverseExternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse External Torque Limit", "Index"));//反转外部转矩限制值
                    EmergencyStopTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Emergency Stop Torque Limit", "Index"));//紧急停止转矩限制值
                }            
            }           
        }

        //*************************************************************************
        //函数名称：MotorFeedbackNavigation
        //函数功能：电机反馈配置导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void UnitNavigation()
        {
            if (ViewModelSet.Main != null)
            {            
                SoftwareStateParameterSet.CurrentPageName = PageName.UNIT;
                NavigationService.Navigate("UnitView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：PositionAlreadySet
        //函数功能：位置限定已经设置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.24
        //*************************************************************************
        public void PositionAlreadySet()
        {
            IsPositionSet = true;
        }

        //*************************************************************************
        //函数名称：TorqueAlreadySet
        //函数功能：转矩限定已经设置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.24
        //*************************************************************************
        public void TorqueAlreadySet()
        {
            IsTorqueSet = true;
        }

        //*************************************************************************
        //函数名称：VoltageAlreadySet
        //函数功能：电压限定已经设置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.24
        //*************************************************************************
        public void VoltageAlreadySet()
        {
            IsVoltageSet = true;
        }

        //*************************************************************************
        //函数名称：NormalSettingNavigation
        //函数功能：一般参数设置导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.03
        //*************************************************************************
        public void NormalSettingNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                if (IsPositionSet && IsTorqueSet && !IsVoltageSet)
                {
                    ShowHintInfo("请留意【电压限定】配置");
                }
                else if (IsPositionSet && !IsTorqueSet && IsVoltageSet)
                {
                    ShowHintInfo("请留意【转矩限定】配置");
                }
                else if (IsPositionSet && !IsTorqueSet && !IsVoltageSet)
                {
                    ShowHintInfo("请留意【转矩、电压限定】配置");
                }
                else if (!IsPositionSet && IsTorqueSet && IsVoltageSet)
                {
                    ShowHintInfo("请留意【位置限定】配置");
                }
                else if (!IsPositionSet && IsTorqueSet && !IsVoltageSet)
                {
                    ShowHintInfo("请留意【位置、电压限定】配置");
                }
                else if (!IsPositionSet && !IsTorqueSet && IsVoltageSet)
                {
                    ShowHintInfo("请留意【位置、转矩限定】配置");
                }
                else if (!IsPositionSet && !IsTorqueSet && !IsVoltageSet)
                {
                    ShowHintInfo("请留意【位置、转矩、电压限定】配置");
                }

                IsPositionSet = true;
                IsTorqueSet = true;
                IsVoltageSet = true;

                SoftwareStateParameterSet.CurrentPageName = PageName.NORMALSET;
                NavigationService.Navigate("NormalSettingView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void OnForwardInternalTorqueLimitChanged() { GlobalCurrentInput.ForwardInternalTorqueLimit = ForwardInternalTorqueLimit; }//正转内部转矩限制值
        public void OnReverseInternalTorqueLimitChanged() { GlobalCurrentInput.ReverseInternalTorqueLimit = ReverseInternalTorqueLimit; }//反转内部转矩限制值
        public void OnForwardExternalTorqueLimitChanged() { GlobalCurrentInput.ForwardExternalTorqueLimit = ForwardExternalTorqueLimit; }//正转外部转矩限制值
        public void OnReverseExternalTorqueLimitChanged() { GlobalCurrentInput.ReverseExternalTorqueLimit = ReverseExternalTorqueLimit; }//反转外部转矩限制值
        public void OnEmergencyStopTorqueLimitChanged() { GlobalCurrentInput.EmergencyStopTorqueLimit = EmergencyStopTorqueLimit; }//紧急停止转矩限制值

        public void OnServoOnSpeedLimitChanged() { GlobalCurrentInput.ServoOnSpeedLimit = ServoOnSpeedLimit; }//使能时速度限制值
        public void OnTrqctrlSpeedLimitChanged() { GlobalCurrentInput.TrqctrlSpeedLimit = TrqctrlSpeedLimit; }//转矩控制时速度限制值
        public void OnMaxProfileVelocityChanged() { GlobalCurrentInput.MaxProfileVelocity = MaxProfileVelocity; }//最大轮廓速度
        public void OnMaxAccelerationChanged() { GlobalCurrentInput.MaxAcceleration = MaxAcceleration; }//最大加速度
        public void OnMaxDecelerationChanged() { GlobalCurrentInput.MaxDeceleration = MaxDeceleration; }//最大减速度
        public void OnVelocityWindowChanged() { GlobalCurrentInput.VelocityWindow = VelocityWindow; }//速度到达阈值
        public void OnVelocityWindowTimeChanged() { GlobalCurrentInput.VelocityWindowTime = VelocityWindowTime; }//速度到达窗口时间

        public void OnPosErrWarnLevelChanged() { GlobalCurrentInput.PosErrWarnLevel = PosErrWarnLevel; }//位置偏差过大警告值
        public void OnPosErrAlarmLevelChanged() { GlobalCurrentInput.PosErrAlarmLevel = PosErrAlarmLevel; }//位置偏差过大报警值
        public void OnSvonPosErrWarnLevelChanged() { GlobalCurrentInput.SvonPosErrWarnLevel = SvonPosErrWarnLevel; }//使能时位置偏差过大警告值
        public void OnSvonPosErrAlarmLevelChanged() { GlobalCurrentInput.SvonPosErrAlarmLevel = SvonPosErrAlarmLevel; }//使能时位置偏差过大报警值
        public void OnFollowingErrorWindowChanged() { GlobalCurrentInput.FollowingErrorWindow = FollowingErrorWindow; }//位置跟踪误差阈值
        public void OnFollowingErrorTimeoutChanged() { GlobalCurrentInput.FollowingErrorTimeout = FollowingErrorTimeout; }//位置跟踪误差过大判定时间
        public void OnPositionWindowChanged() { GlobalCurrentInput.PositionWindow = PositionWindow; }//位置到达阈值
        public void OnPositionWindowTimeChanged() { GlobalCurrentInput.PositionWindowTime = PositionWindowTime; }//位置到达窗口时间
        public void OnMinSoftwarePositionLimitChanged() { GlobalCurrentInput.MinSoftwarePositionLimit = MinSoftwarePositionLimit; }//软件限位最小值
        public void OnMaxSoftwarePositionLimitChanged() { GlobalCurrentInput.MaxSoftwarePositionLimit = MaxSoftwarePositionLimit; }//软件限位最大值
        public void OnSpeedUnitChanged() { GlobalCurrentInput.SpeedUnit = SpeedUnit; }//速度单位
        public void OnAccelerateUnitChanged() { GlobalCurrentInput.AccelerationUnit = AccelerateUnit; }//加速度单位
        public void OnPositionUnitChanged() { GlobalCurrentInput.PositionUnit = PositionUnit; }//位置单位
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：GetAmplitudeConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private DataTable GetAmplitudeConfigToDataTable()
        {
            DataTable dt = new DataTable();

            try
            {   
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Servo On Speed Limit", "使能时速度限制值", ServoOnSpeedLimit, "rpm", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Trqctrl Speed Limit", "转矩控制时速度限制值", TrqctrlSpeedLimit, "rpm", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Max Profile Velocity", "最大轮廓速度", MaxProfileVelocity, SpeedUnit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Max Acceleration", "最大加速度", MaxAcceleration, AccelerateUnit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Max Deceleration", "最大减速度", MaxDeceleration, AccelerateUnit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Velocity Window", "速度到达阈值", VelocityWindow, SpeedUnit ,ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Velocity Window Time", "速度到达窗口时间", VelocityWindowTime, "ms",ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"PosErr Warn Level", "位置偏差过大警告值", PosErrWarnLevel, "0.01",ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"PosErr Alarm Level", "位置偏差过大报警值", PosErrAlarmLevel, PositionUnit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Svon PosErr Warn Level", "使能时位置偏差过大警告值", SvonPosErrWarnLevel, "0.01", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Svon PosErr Alarm Level", "使能时位置偏差过大报警值", SvonPosErrAlarmLevel, PositionUnit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Following Error Window", "位置跟踪误差阈值", FollowingErrorWindow, PositionUnit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Following Error Timeout", "位置跟踪误差过大判定时间", FollowingErrorTimeout, "ms", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Position Window", "位置到达阈值", PositionWindow,PositionUnit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Position Window Time", "位置到达窗口时间", PositionWindowTime,"ms", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Min Software Position Limit", "软件限位最小值", MinSoftwarePositionLimit,PositionUnit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE,"Max Software Position Limit", "软件限位最大值", MaxSoftwarePositionLimit,PositionUnit, ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE, "Forward Internal Torque Limit", "正转内部转矩限制值", ForwardInternalTorqueLimit, "0.01", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE, "Reverse Internal Torque Limit", "反转内部转矩限制值", ReverseInternalTorqueLimit, "0.01", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE, "Forward External Torque Limit", "正转外部转矩限制值", ForwardExternalTorqueLimit, "0.01", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE, "Reverse External Torque Limit", "反转外部转矩限制值", ReverseExternalTorqueLimit, "0.01", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.LIMITAMPLITUDE, "Emergency Stop Torque Limit", "紧急停止转矩限制值", EmergencyStopTorqueLimit, "0.01", ref dt);

                return dt;             
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_GET_AMPLITUDE_CONFIG_TO_DATATABLE, "GetAmplitudeConfigToDataTable", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetAmplitudeConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int GetAmplitudeConfigFromDataTable(DataTable dt)
        {
            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                SpeedUnit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Profile Velocity", "Unit");
                AccelerateUnit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Acceleration", "Unit");
                PositionUnit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Software Position Limit", "Unit");
                CurrentUnit.Speed = SpeedUnit;
                CurrentUnit.Acceleration = AccelerateUnit;
                CurrentUnit.Position = PositionUnit;

                ForwardInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Forward Internal Torque Limit", "Default");
                ReverseInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Reverse Internal Torque Limit", "Default");
                ForwardExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Forward External Torque Limit", "Default");
                ReverseExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Reverse External Torque Limit", "Default");
                EmergencyStopTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Emergency Stop Torque Limit", "Default");

                ServoOnSpeedLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Servo On Speed Limit", "Default");
                TrqctrlSpeedLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Trqctrl Speed Limit", "Default");
                MaxProfileVelocity = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Profile Velocity", "Default");
                MaxAcceleration = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Acceleration", "Default");
                MaxDeceleration = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Deceleration", "Default");
                VelocityWindow = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Velocity Window", "Default");
                VelocityWindowTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Velocity Window Time", "Default");

                PosErrWarnLevel = OthersHelper.GetCellValueFromDataTable(dt, "Name", "PosErr Warn Level", "Default");
                PosErrAlarmLevel = OthersHelper.GetCellValueFromDataTable(dt, "Name", "PosErr Alarm Level", "Default");
                SvonPosErrWarnLevel = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Svon PosErr Warn Level", "Default");
                SvonPosErrAlarmLevel = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Svon PosErr Alarm Level", "Default");
                FollowingErrorWindow = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Following Error Window", "Default");
                FollowingErrorTimeout = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Following Error Timeout", "Default");
                PositionWindow = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Position Window", "Default");
                PositionWindowTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Position Window Time", "Default");
                MinSoftwarePositionLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Min Software Position Limit", "Default");
                MaxSoftwarePositionLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Max Software Position Limit", "Default");

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_GET_AMPLITUDE_CONFIG_FROM_DATATABLE, "GetAmplitudeConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }
     
        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                ForwardInternalTorqueLimit = GlobalCurrentInput.ForwardInternalTorqueLimit;//正转内部转矩限制值
                ReverseInternalTorqueLimit = GlobalCurrentInput.ReverseInternalTorqueLimit;//反转内部转矩限制值
                ForwardExternalTorqueLimit = GlobalCurrentInput.ForwardExternalTorqueLimit;//正转外部转矩限制值
                ReverseExternalTorqueLimit = GlobalCurrentInput.ReverseExternalTorqueLimit;//反转外部转矩限制值
                EmergencyStopTorqueLimit = GlobalCurrentInput.EmergencyStopTorqueLimit;//紧急停止转矩限制值

                ServoOnSpeedLimit = GlobalCurrentInput.ServoOnSpeedLimit;//使能时速度限制值
                TrqctrlSpeedLimit = GlobalCurrentInput.TrqctrlSpeedLimit;//转矩控制时速度限制值
                MaxProfileVelocity = GlobalCurrentInput.MaxProfileVelocity;//最大轮廓速度
                MaxAcceleration = GlobalCurrentInput.MaxAcceleration;//最大加速度
                MaxDeceleration = GlobalCurrentInput.MaxDeceleration;//最大减速度
                VelocityWindow = GlobalCurrentInput.VelocityWindow;//速度到达阈值
                VelocityWindowTime = GlobalCurrentInput.VelocityWindowTime;//速度到达窗口时间

                PosErrWarnLevel = GlobalCurrentInput.PosErrWarnLevel;//位置偏差过大警告值
                PosErrAlarmLevel = GlobalCurrentInput.PosErrAlarmLevel;//位置偏差过大报警值
                SvonPosErrWarnLevel = GlobalCurrentInput.SvonPosErrWarnLevel;//使能时位置偏差过大警告值
                SvonPosErrAlarmLevel = GlobalCurrentInput.SvonPosErrAlarmLevel;//使能时位置偏差过大报警值
                FollowingErrorWindow = GlobalCurrentInput.FollowingErrorWindow;//位置跟踪误差阈值
                FollowingErrorTimeout = GlobalCurrentInput.FollowingErrorTimeout;//位置跟踪误差过大判定时间
                PositionWindow = GlobalCurrentInput.PositionWindow;//位置到达阈值
                PositionWindowTime = GlobalCurrentInput.PositionWindowTime;//位置到达窗口时间
                MinSoftwarePositionLimit = GlobalCurrentInput.MinSoftwarePositionLimit;//软件限位最小值
                MaxSoftwarePositionLimit = GlobalCurrentInput.MaxSoftwarePositionLimit;//软件限位最大值
      
                SpeedUnit = GlobalCurrentInput.SpeedUnit;
                AccelerateUnit = GlobalCurrentInput.AccelerationUnit;
                PositionUnit = GlobalCurrentInput.PositionUnit;

                CurrentUnit.Speed = SpeedUnit;
                CurrentUnit.Acceleration = AccelerateUnit;
                CurrentUnit.Position = PositionUnit;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int AddParameterInfoDictionary(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                switch (strCategory)
                {
                    case "0":
                        dicParameterInfo.Add("Servo On Speed Limit", ServoOnSpeedLimit);
                        dicParameterInfo.Add("Trqctrl Speed Limit", TrqctrlSpeedLimit);
                        dicParameterInfo.Add("Velocity Window Time", VelocityWindowTime);

                        dicParameterInfo.Add("Max Profile Velocity", OthersHelper.ExchangeUnit("Max Profile Velocity", SpeedUnit + "-" + DefaultUnit.SpeedUnit, MaxProfileVelocity));
                        dicParameterInfo.Add("Max Acceleration", OthersHelper.ExchangeUnit("Max Acceleration", AccelerateUnit + "-" + DefaultUnit.AccelerationUnit, MaxAcceleration));
                        dicParameterInfo.Add("Max Deceleration", OthersHelper.ExchangeUnit("Max Deceleration", AccelerateUnit + "-" + DefaultUnit.AccelerationUnit, MaxDeceleration));
                        dicParameterInfo.Add("Velocity Window", OthersHelper.ExchangeUnit("Velocity Window", SpeedUnit + "-" + DefaultUnit.SpeedUnit, VelocityWindow));
                        break;
                    case "1":
                        dicParameterInfo.Add("PosErr Warn Level", PosErrWarnLevel);
                        dicParameterInfo.Add("Svon PosErr Warn Level", SvonPosErrWarnLevel);                    
                        dicParameterInfo.Add("Following Error Timeout", FollowingErrorTimeout);
                        dicParameterInfo.Add("Position Window Time", PositionWindowTime);

                        dicParameterInfo.Add("Following Error Window", OthersHelper.ExchangeUnit("Following Error Window", PositionUnit + "-" + DefaultUnit.PositionUnit, FollowingErrorWindow));
                        dicParameterInfo.Add("Position Window", OthersHelper.ExchangeUnit("Position Window", PositionUnit + "-" + DefaultUnit.PositionUnit, PositionWindow));
                        dicParameterInfo.Add("PosErr Alarm Level", OthersHelper.ExchangeUnit("PosErr Alarm Level", PositionUnit + "-" + DefaultUnit.PositionUnit, PosErrAlarmLevel));
                        dicParameterInfo.Add("Svon PosErr Alarm Level", OthersHelper.ExchangeUnit("Svon PosErr Alarm Level", PositionUnit + "-" + DefaultUnit.PositionUnit, SvonPosErrAlarmLevel));
                        dicParameterInfo.Add("Min Software Position Limit", OthersHelper.ExchangeUnit("Min Software Position Limit", PositionUnit + "-" + DefaultUnit.PositionUnit, MinSoftwarePositionLimit));
                        dicParameterInfo.Add("Max Software Position Limit", OthersHelper.ExchangeUnit("Max Software Position Limit", PositionUnit + "-" + DefaultUnit.PositionUnit, MaxSoftwarePositionLimit));
                        break;
                    case "2":
                        dicParameterInfo.Add("Forward Internal Torque Limit", ForwardInternalTorqueLimit);
                        dicParameterInfo.Add("Reverse Internal Torque Limit", ReverseInternalTorqueLimit);
                        dicParameterInfo.Add("Forward External Torque Limit", ForwardExternalTorqueLimit);
                        dicParameterInfo.Add("Reverse External Torque Limit", ReverseExternalTorqueLimit);
                        dicParameterInfo.Add("Emergency Stop Torque Limit", EmergencyStopTorqueLimit);
                        break;         
                    default:
                        dicParameterInfo.Add("Servo On Speed Limit", ServoOnSpeedLimit);
                        dicParameterInfo.Add("Trqctrl Speed Limit", TrqctrlSpeedLimit);
                        dicParameterInfo.Add("Velocity Window Time", VelocityWindowTime);

                        dicParameterInfo.Add("Max Profile Velocity", OthersHelper.ExchangeUnit("Max Profile Velocity", SpeedUnit + "-" + DefaultUnit.SpeedUnit, MaxProfileVelocity));
                        dicParameterInfo.Add("Max Acceleration", OthersHelper.ExchangeUnit("Max Acceleration", AccelerateUnit + "-" + DefaultUnit.AccelerationUnit, MaxAcceleration));
                        dicParameterInfo.Add("Max Deceleration", OthersHelper.ExchangeUnit("Max Deceleration", AccelerateUnit + "-" + DefaultUnit.AccelerationUnit, MaxDeceleration));
                        dicParameterInfo.Add("Velocity Window", OthersHelper.ExchangeUnit("Velocity Window", SpeedUnit + "-" + DefaultUnit.SpeedUnit, VelocityWindow));

                        dicParameterInfo.Add("PosErr Warn Level", PosErrWarnLevel);
                        dicParameterInfo.Add("Svon PosErr Warn Level", SvonPosErrWarnLevel);
                        dicParameterInfo.Add("Following Error Timeout", FollowingErrorTimeout);
                        dicParameterInfo.Add("Position Window Time", PositionWindowTime);

                        dicParameterInfo.Add("Following Error Window", OthersHelper.ExchangeUnit("Following Error Window", PositionUnit + "-" + DefaultUnit.PositionUnit, FollowingErrorWindow));
                        dicParameterInfo.Add("Position Window", OthersHelper.ExchangeUnit("Position Window", PositionUnit + "-" + DefaultUnit.PositionUnit, PositionWindow));
                        dicParameterInfo.Add("PosErr Alarm Level", OthersHelper.ExchangeUnit("PosErr Alarm Level", PositionUnit + "-" + DefaultUnit.PositionUnit, PosErrAlarmLevel));
                        dicParameterInfo.Add("Svon PosErr Alarm Level", OthersHelper.ExchangeUnit("Svon PosErr Alarm Level", PositionUnit + "-" + DefaultUnit.PositionUnit, SvonPosErrAlarmLevel));
                        dicParameterInfo.Add("Min Software Position Limit", OthersHelper.ExchangeUnit("Min Software Position Limit", PositionUnit + "-" + DefaultUnit.PositionUnit, MinSoftwarePositionLimit));
                        dicParameterInfo.Add("Max Software Position Limit", OthersHelper.ExchangeUnit("Max Software Position Limit", PositionUnit + "-" + DefaultUnit.PositionUnit, MaxSoftwarePositionLimit));

                        dicParameterInfo.Add("Forward Internal Torque Limit", ForwardInternalTorqueLimit);
                        dicParameterInfo.Add("Reverse Internal Torque Limit", ReverseInternalTorqueLimit);
                        dicParameterInfo.Add("Forward External Torque Limit", ForwardExternalTorqueLimit);
                        dicParameterInfo.Add("Reverse External Torque Limit", ReverseExternalTorqueLimit);
                        dicParameterInfo.Add("Emergency Stop Torque Limit", EmergencyStopTorqueLimit);
                        break;
                }
                           
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }
      
        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowHintInfo(string strInfo)
        {
            ViewModelSet.Main?.ShowHintInfo(strInfo);
        }
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }
}