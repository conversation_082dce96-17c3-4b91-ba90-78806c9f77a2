﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using DevExpress.Mvvm.POCO;
using ServoStudio.Models;
using System.Data;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.ComponentModel;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class SoftwareErrorLogViewModel
    {
        #region 私有字段
        private ObservableCollection<SoftwareErrorSet> obsSoftwareErrorLog = new ObservableCollection<SoftwareErrorSet>();
        #endregion

        #region 服务
        [ServiceProperty(Key = "ServiceWithDefaultNotifications")]
        protected virtual INotificationService DefaultNotificationService { get { return null; } }                    
        #endregion

        #region 属性
        public virtual string BeginningDate { get; set; }//起始时间
        public virtual string EndingDate { get; set; }//截止时间
        public virtual ObservableCollection<SoftwareErrorSet> SoftwareErrorLog { get; set; }//软件错误集合   
        #endregion

        #region 构造函数
        public SoftwareErrorLogViewModel()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.SOFTWAREERROR;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：SoftwareErrorLogLoaded
        //函数功能：检索软件错误日志
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.29
        //*************************************************************************
        public void SoftwareErrorLogLoaded()
        {          
            BeginningDate = DateTime.Now.ToShortDateString();
            EndingDate = DateTime.Now.ToShortDateString();
            SoftwareErrorLog = new ObservableCollection<SoftwareErrorSet>();           
        }

        //*************************************************************************
        //函数名称：GetSoftwareErrorData
        //函数功能：检索软件错误日志
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.29
        //*************************************************************************
        public void GetSoftwareErrorData()
        {            
            int iRet = -1;
            string strBeginningDate = BeginningDate;
            string strEndingDate = EndingDate;

            try
            {
                //获取错误日志信息
                iRet = ExcelHelper.ReadFromExcel(FilePath.SoftwareErrorLog, ref GlobalErrorSet.dtSoftware);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取检索信息
                iRet = OthersHelper.RetrieveErrorLogByDatetime(strBeginningDate, strEndingDate, ref obsSoftwareErrorLog);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(RET.NO_EFFECT);
                }

                //赋值
                SoftwareErrorLog.Clear();
                foreach (var item in obsSoftwareErrorLog)
                {
                    SoftwareErrorLog.Add(new SoftwareErrorSet() { DateTime = item.DateTime, Code = item.Code, Function = item.Function, Content = item.Content, Level = item.Level });
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SOFTWAREERRORLOG_GET_SOFTWARE_ERROR_DATA, "GetSoftwareErrorData", ex);
            }          
        }

        //*************************************************************************
        //函数名称：ShowNotification
        //函数功能：信息提示
        //
        //输入参数：int In_iIndex
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.30
        //*************************************************************************
        public void ShowNotification(int In_iIndex)
        {
            INotification notification;

            try
            {
                switch (In_iIndex)
                {
                    case RET.ERROR:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "查询日志时系统异常。请确认日志是否存在，并关闭系统重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case RET.NO_EFFECT:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "没有查询到数据...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    default:
                        return;
                }

                notification.ShowAsync();
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SOFTWAREERRORLOG_SHOW_NOTIFICATION, "SoftwareErrorLogViewModel.ShowNotification", ex);
            }          
        }
        #endregion
    }

    public class SoftwareErrorSet : INotifyPropertyChanged
    {
        private string function;
        public string Function
        {
            get { return function; }
            set
            {
                function = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Function"));
                }
            }
        }

        private string datetime;
        public string DateTime
        {
            get { return datetime; }
            set
            {
                datetime = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("DateTime"));
                }
            }
        }

        private string code;
        public string Code
        {
            get { return code; }
            set
            {
                code = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Code"));
                }
            }
        }

        private string level;
        public string Level
        {
            get { return level; }
            set
            {
                level = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Level"));
                }
            }
        }

        private string content;
        public string Content
        {
            get { return content; }
            set
            {
                content = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Content"));
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
    }
}