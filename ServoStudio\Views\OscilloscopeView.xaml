﻿<UserControl x:Class="ServoStudio.Views.OscilloscopeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:d3="http://research.microsoft.com/DynamicDataDisplay/1.0"             
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dxnt="http://schemas.devexpress.com/winfx/2008/xaml/navbar/themekeys"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
             xmlns:dxn="http://schemas.devexpress.com/winfx/2008/xaml/navbar"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"              
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:Views="clr-namespace:ServoStudio.Views"
             xmlns:converter="clr-namespace:Converter"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:OscilloscopeViewModel}"
             d:DesignHeight="800" d:DesignWidth="1300">
    
    <dxmvvm:Interaction.Behaviors>
        <dxwui:WinUIDialogService  x:Name="TorqueLegend" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:TorqueView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService  x:Name="PositionLegend" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:PositionView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService  x:Name="SlopeLegend" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:SpeedView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService  x:Name="JerkFreeLegend" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:JerkFreeView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService  x:Name="SquareLegend" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:SquareView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService  x:Name="SinLegend" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:SinView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService  x:Name="StepLegend" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:StepView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService  x:Name="SlopeAscLegend" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:SlopeView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <!--<dxmvvm:EventToCommand Command="{Binding OscilloscopeLoadedCommand}"/>-->
        <dxmvvm:EventToCommand Command="{Binding OscilloscopeLoadedCommand}" EventName="Loaded"/>
        <dxmvvm:EventToCommand Command="{Binding OscilloscopeUnloadedCommand}" EventName="Unloaded"/>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields" UseWin8NotificationsIfAvailable="True"/>
    </dxmvvm:Interaction.Behaviors>

    <UserControl.Resources>

        <converter:BackgroundConverter x:Key="ColorConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>

        <Style x:Key="myTabItem" TargetType="dx:DXTabItem">
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Trigger.Setters>
                        <Setter Property="FontStyle" Value="Italic"/>
                        <Setter Property="Foreground" Value="Green"/>
                    </Trigger.Setters>
                </Trigger>
            </Style.Triggers>
        </Style>

        <DataTemplate x:Key="gridDataTemplate_Text">
            <Grid>
                <dxe:TextEdit Text="{Binding RowData.Row.Doubling, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ChangeWaveControlPropertyCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>
            </Grid>
        </DataTemplate>

        <DataTemplate x:Key="gridDataTemplate_CheckBox">
            <Grid>
                <dxe:CheckEdit VerticalAlignment="Center" HorizontalAlignment="Center" IsChecked="{Binding RowData.Row.IsHidden, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="Unchecked" Command="{Binding Path=DataContext.ChangeWaveControlProperty_For_IsHiddenCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}"/>
                        <dxmvvm:EventToCommand EventName="Checked" Command="{Binding Path=DataContext.ChangeWaveControlProperty_For_IsHiddenCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:CheckEdit>
            </Grid>
        </DataTemplate>
    </UserControl.Resources>
  
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="270"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <Grid Grid.Column="0" Margin="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="5"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="5"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="5"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="5"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!--由Lilbert于2022.08.24添加示波器预设置-->
            <lc:GroupBox Grid.Row="0" Margin="0" Header="预设置" Style="{StaticResource GroupBoxStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="140"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Label Content="预设置" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}" Foreground="Red"/>
                    <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="1" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding OscilloscopePreset}" SelectedItem="{Binding SelectedOscilloscopePreset, Mode=TwoWay}" AllowCollectionView="True" Margin="13,3,-6,3" Grid.ColumnSpan="3" Width="Auto"/>

                    <dxb:ToolBarControl AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False" Grid.Column="3" Margin="12,4,-36,0" Height="26">
                        <dxb:BarButtonItem  IsEnabled="{Binding OthersButtonEnabled}" Command="{Binding OscilloscopeConfigExportCommand}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/SaveAs_16x16.png" BarItemDisplayMode="ContentAndGlyph"/>
                    </dxb:ToolBarControl>

                    <dxb:ToolBarControl AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False" Grid.Column="3" Margin="44,4,-68,0" Height="26">
                        <dxb:BarButtonItem  IsEnabled="{Binding OthersButtonEnabled}" Command="{Binding OscilloscopeConfigDeleteCommand}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/Delete_16x16.png" BarItemDisplayMode="ContentAndGlyph"/>
                    </dxb:ToolBarControl>
                </Grid>
            </lc:GroupBox>

            <lc:GroupBox Grid.Row="2" Margin="0" Header="通道设置" Style="{StaticResource GroupBoxStyle}">
                <Grid>
                    <Grid.Resources>
                        <DataTemplate x:Key="headerTemplate1">
                            <TextBlock Text="{Binding}" Foreground="Red" TextDecorations="Underline"/>
                        </DataTemplate>
                        <DataTemplate x:Key="headerTemplate2">
                            <TextBlock Text="{Binding}" Foreground="Orange" TextDecorations="Underline"/>
                        </DataTemplate>
                        <DataTemplate x:Key="headerTemplate3">
                            <TextBlock Text="{Binding}" Foreground="Blue" TextDecorations="Underline"/>
                        </DataTemplate>
                        <DataTemplate x:Key="headerTemplate4">
                            <TextBlock Text="{Binding}" Foreground="Green" TextDecorations="Underline"/>
                        </DataTemplate>
                    </Grid.Resources>

                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="54"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Label Content="通道-1" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}" Foreground="Red"/>
                    <!--<dxe:ComboBoxEdit Grid.Row="0" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding SamplingChannel1}" SelectedItem="{Binding SelectedSamplingChannel1,Mode=TwoWay}"/>-->
                    <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo1}"  SelectedIndex="{Binding SelectedSampleChannel1Index,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"  AllowCollectionView="True"  DisplayMember="ItemName" >
                        <dxe:ComboBoxEdit.GroupStyle>
                            <GroupStyle HidesIfEmpty="True">
                                <GroupStyle.ContainerStyle>
                                    <Style TargetType="{x:Type GroupItem}">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate1}" IsExpanded="False" Margin="5,2">
                                                        <ItemsPresenter Margin="25,0,0,0"/>
                                                    </Expander>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </GroupStyle.ContainerStyle>
                            </GroupStyle>
                        </dxe:ComboBoxEdit.GroupStyle>
                    </dxe:ComboBoxEdit>

                    <Label Content="通道-2" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}" Foreground="Orange"/>
                    <!--<dxe:ComboBoxEdit Grid.Row="1" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding SamplingChannel2}" SelectedItem="{Binding SelectedSamplingChannel2,Mode=TwoWay}" IsEnabled="{Binding Channel2Enabled}"/>-->
                    <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo2}" SelectedIndex="{Binding SelectedSampleChannel2Index,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding Channel2Enabled}" AllowCollectionView="True"  DisplayMember="ItemName" >
                        <dxe:ComboBoxEdit.GroupStyle>
                            <GroupStyle HidesIfEmpty="True">
                                <GroupStyle.ContainerStyle>
                                    <Style TargetType="{x:Type GroupItem}">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate2}" IsExpanded="False" Margin="5,2">
                                                        <ItemsPresenter Margin="25,0,0,0"/>
                                                    </Expander>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </GroupStyle.ContainerStyle>
                            </GroupStyle>
                        </dxe:ComboBoxEdit.GroupStyle>
                    </dxe:ComboBoxEdit>


                    <Label Content="通道-3" Grid.Row="2" Grid.Column="0" Style="{StaticResource LabelStyle}" Foreground="Blue"/>
                    <!--<dxe:ComboBoxEdit Grid.Row="2" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding SamplingChannel3}" SelectedItem="{Binding SelectedSamplingChannel3,Mode=TwoWay}" IsEnabled="{Binding Channel3Enabled}"/>-->
                    <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo3}" SelectedIndex="{Binding SelectedSampleChannel3Index,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding Channel3Enabled}" AllowCollectionView="True"  DisplayMember="ItemName" >
                        <dxe:ComboBoxEdit.GroupStyle>
                            <GroupStyle HidesIfEmpty="True">
                                <GroupStyle.ContainerStyle>
                                    <Style TargetType="{x:Type GroupItem}">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate3}" IsExpanded="False" Margin="5,2">
                                                        <ItemsPresenter Margin="25,0,0,0"/>
                                                    </Expander>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </GroupStyle.ContainerStyle>
                            </GroupStyle>
                        </dxe:ComboBoxEdit.GroupStyle>
                    </dxe:ComboBoxEdit>


                    <Label Content="通道-4" Grid.Row="3" Grid.Column="0" Style="{StaticResource LabelStyle}" Foreground="Green"/>
                    <!--<dxe:ComboBoxEdit Grid.Row="3" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding SamplingChannel4}" SelectedItem="{Binding SelectedSamplingChannel4,Mode=TwoWay}" IsEnabled="{Binding Channel4Enabled}"/>-->
                    <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo4}" SelectedIndex="{Binding SelectedSampleChannel4Index,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding Channel4Enabled}" AllowCollectionView="True"  DisplayMember="ItemName" >
                        <dxe:ComboBoxEdit.GroupStyle>
                            <GroupStyle HidesIfEmpty="True">
                                <GroupStyle.ContainerStyle>
                                    <Style TargetType="{x:Type GroupItem}">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate>
                                                    <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate4}" IsExpanded="False" Margin="5,2">
                                                        <ItemsPresenter Margin="25,0,0,0"/>
                                                    </Expander>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </GroupStyle.ContainerStyle>
                            </GroupStyle>
                        </dxe:ComboBoxEdit.GroupStyle>
                    </dxe:ComboBoxEdit>

                </Grid>
            </lc:GroupBox>

            <lc:GroupBox Grid.Row="4" Margin="0" Header="采样设置" Style="{StaticResource GroupBoxStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="54"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Label Content="采样周期" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                    <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding SamplingPeriod}" SelectedItem="{Binding SelectedSamplingPeriod,Mode=TwoWay}"/>

                    <Label Content="采样时长" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                    <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding SamplingDuration}" SelectedItem="{Binding SelectedSamplingDuration,Mode=TwoWay}" SelectedIndex="{Binding SelectedSamplingDurationIndex}"/>

                    <Label Content="连续采样" Grid.Row="2" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                    <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding ContinuousSampling}" SelectedItem="{Binding SelectedContinuousSampling,Mode=TwoWay}" IsEnabled="{Binding ContinuousAcquisitionEnabled}"/>

                    <!--<Label Content="波形展示" Grid.Row="3" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                    <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="1" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding DisplayMethod}" SelectedItem="{Binding SelectedDisplayMethod,Mode=TwoWay}" IsEnabled="{Binding ContinuousAcquisitionEnabled}"/>-->
                </Grid>
            </lc:GroupBox>

            <lc:GroupBox Grid.Row="6" Margin="0" Header="触发设置" Style="{StaticResource GroupBoxStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Label Content="触发模式" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                    <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="2" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding TriggerClockEdge}" SelectedItem="{Binding SelectedTriggerClockEdge,Mode=TwoWay}"/>

                    <!--Label Content="触发通道" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}"/-->
                    <!--dxe:ComboBoxEdit Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding TriggerChannel,Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" SelectedItem="{Binding SelectedTriggerChannel,UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}"/-->
                    <Label Content="触发通道" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                    <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding TriggerChannel,Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" SelectedItem="{Binding SelectedTriggerChannel,UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}"/>

                    <Label Content="预先触发" Grid.Row="2" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                    <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding PreTrigger}"  SelectedItem="{Binding SelectedPreTrigger,Mode=TwoWay}"/>

                    <Label Content="触发水平" Grid.Row="3" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                    <TextBox Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Text="{Binding TriggerLevel,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="3" Grid.Column="2" Text="{Binding Unit}" Style="{StaticResource TextBoxStyle_Unit}" Height="Auto" Margin="4,0"/>
                </Grid>
            </lc:GroupBox>

            <lc:GroupBox Grid.Row="8" Margin="0" Padding="0"  Header="波形控制" Style="{StaticResource GroupBoxStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="500"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="1">
                        <dxg:GridControl SelectionMode="Row" ItemsSource="{Binding OscilloscopeProperty}" FontStyle="Normal" ShowBorder="False" IsFilterEnabled="False">
                            <dxg:GridControl.OpacityMask>
                                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                    <GradientStop Color="Black" Offset="0"/>
                                    <GradientStop Color="White" Offset="1"/>
                                </LinearGradientBrush>
                            </dxg:GridControl.OpacityMask>
                            <dxg:GridControl.View>
                                <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="27" HeaderPanelMinHeight="27">
                                    <dxg:TableView.FormatConditions>
                                        <dxg:FormatCondition ApplyToRow="True" Expression="[ChannelNumber] = '通道-1'" FieldName="ChannelNumber">
                                            <dx:Format Foreground="Red" TextDecorations="{x:Null}"/>
                                        </dxg:FormatCondition>
                                        <dxg:FormatCondition ApplyToRow="True" Expression="[ChannelNumber] = '通道-2'" FieldName="ChannelNumber">
                                            <dx:Format Foreground="Orange" TextDecorations="{x:Null}"/>
                                        </dxg:FormatCondition>
                                        <dxg:FormatCondition ApplyToRow="True" Expression="[ChannelNumber] = '通道-3'" FieldName="ChannelNumber">
                                            <dx:Format Foreground="Blue" TextDecorations="{x:Null}"/>
                                        </dxg:FormatCondition>
                                        <dxg:FormatCondition ApplyToRow="True" Expression="[ChannelNumber] = '通道-4'" FieldName="ChannelNumber">
                                            <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                        </dxg:FormatCondition>
                                    </dxg:TableView.FormatConditions>
                                </dxg:TableView>
                            </dxg:GridControl.View>

                            <dxg:GridColumn FieldName="ChannelNumber" Header="采样通道" IsSmart="True" Width="0.8*" ReadOnly="True"/>
                            <dxg:GridColumn FieldName="Doubling" Header="波形倍乘" IsSmart="True" Width="1.2*" ReadOnly="False" CellTemplate="{StaticResource gridDataTemplate_Text}"/>
                            <dxg:GridColumn FieldName="IsHidden" Header="是否隐藏" IsSmart="True" Width="0.8*" ReadOnly="False" CellTemplate="{StaticResource gridDataTemplate_CheckBox}"/>
                        </dxg:GridControl>
                    </Grid>
                </Grid>
            </lc:GroupBox>
        </Grid>

        <Grid Grid.Column="1" Margin="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="5"/>
                <RowDefinition Height="224"/>
            </Grid.RowDefinitions>

            <lc:GroupBox Grid.Row="0" Margin="0" Padding="0" Header="示波器显示" Style="{StaticResource GroupBoxStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <dxb:BarContainerControl Grid.Row="0">
                        <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                            <dxb:BarButtonItem Content="  开启数采  " Command="{Binding ParameterAcquisitionStartCommand}" IsEnabled="{Binding AcquisitionStartButtonEnabled}" Glyph="{dx:DXImageOffice2013 Image=RangeArea_16x16.png}" BarItemDisplayMode="ContentAndGlyph" />
                        </dxb:ToolBarControl>

                        <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                            <dxb:BarButtonItem Content="  停止数采  " Command="{Binding ParameterAcquisitionStopCommand}" Glyph="{dx:DXImageOffice2013 Image=HighlightActiveElements_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                        </dxb:ToolBarControl>

                        <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                            <dxb:BarButtonItem Content="  显示波形  " IsEnabled="{Binding OthersButtonEnabled}" Command="{Binding ParameterAcquisitionLastCommand}"  Glyph="{dx:DXImageOffice2013 Image=Area_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                        </dxb:ToolBarControl>

                        <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                            <dxb:BarButtonItem Content="  清除波形  " IsEnabled="{Binding OthersButtonEnabled}" Command="{Binding ParameterAcquisitionClearCommand}" Glyph="{dx:DXImageOffice2013 Image=Clear_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                        </dxb:ToolBarControl>

                        <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                            <dxb:BarButtonItem Content="  保存波形  " IsEnabled="{Binding OthersButtonEnabled}" Command="{Binding ParameterAcquisitionExportCommand}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/SaveAs_16x16.png" BarItemDisplayMode="ContentAndGlyph"/>
                        </dxb:ToolBarControl>

                        <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                            <dxb:BarButtonItem Content="  载入波形  " IsEnabled="{Binding OthersButtonEnabled}" Command="{Binding ParameterAcquisitionImportCommand}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/Export_16x16.png" BarItemDisplayMode="ContentAndGlyph"/>
                        </dxb:ToolBarControl>

                        <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                            <dxb:BarButtonItem Content="  扩大显示  " Command="{Binding ExpandDisplayCommand}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/FitToPage_16x16.png" BarItemDisplayMode="ContentAndGlyph"/>
                        </dxb:ToolBarControl>

                        <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                            <dxb:BarButtonItem Content ="  默认显示  " Command="{Binding NormalDisplayCommand}" Glyph="{dx:DXImageOffice2013 Image=Squeeze_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                        </dxb:ToolBarControl>
                    </dxb:BarContainerControl>

                    <d3:ChartPlotter Grid.Row="1" Name="plotter" NewLegendVisible="False" LegendVisibility="Collapsed" MouseLeftButtonUp="plotter_MouseLeftButtonUp" Background="#FFE8E7E7" BorderBrush="Gray">
                        <d3:HorizontalAxisTitle TextBlock.FontSize="9"/>
                        <d3:CursorCoordinateGraph AutoHide="True" LineStroke="#FF17A7EA"/>

                        <d3:DataFollowChart Name="dataFollowChart_Channel1">
                            <DataTemplate>
                                <Grid d3:ViewportPanel.ScreenOffsetY="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <Rectangle RadiusX="3" RadiusY="3" Stroke="LightGray" Fill="#99FFFFFF" Grid.Row="0" Grid.RowSpan="2"/>
                                    <Ellipse Width="10" Height="10" Stroke="Red" Grid.Row="0" Fill="#FFFFA200"/>
                                    <TextBlock Margin="2,15,2,0" Grid.Row="1">
                                        <TextBlock Text="{Binding Position.X, StringFormat={}{0:F2}}"/>;
                                        <TextBlock Text="{Binding Position.Y, StringFormat={}{0:F2}}"/>
                                     </TextBlock>
                                </Grid>
                            </DataTemplate>
                        </d3:DataFollowChart>

                        <d3:DataFollowChart Name="dataFollowChart_Channel2">
                            <DataTemplate>
                                <Grid d3:ViewportPanel.ScreenOffsetY="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <Rectangle RadiusX="3" RadiusY="3" Stroke="LightGray" Fill="#99FFFFFF" Grid.Row="0" Grid.RowSpan="2"/>
                                    <Ellipse Width="10" Height="10" Fill="Yellow" Stroke="Orange" Grid.Row="0"/>
                                    <TextBlock Margin="2,15,2,0" Grid.Row="1">
                                        <TextBlock Text="{Binding Position.X, StringFormat={}{0:F2}}"/>;
                                        <TextBlock Text="{Binding Position.Y, StringFormat={}{0:F2}}"/>
                                    </TextBlock>
                                </Grid>
                            </DataTemplate>
                        </d3:DataFollowChart>

                        <d3:DataFollowChart Name="dataFollowChart_Channel3">
                            <DataTemplate>
                                <Grid d3:ViewportPanel.ScreenOffsetY="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <Rectangle RadiusX="3" RadiusY="3" Stroke="LightGray" Fill="#99FFFFFF" Grid.Row="0" Grid.RowSpan="2"/>
                                    <Ellipse Width="10" Height="10" Fill="LightBlue" Stroke="Blue" Grid.Row="0"/>
                                    <TextBlock Margin="2,15,2,0" Grid.Row="1">
                                        <TextBlock Text="{Binding Position.X, StringFormat={}{0:F2}}"/>;
                                        <TextBlock Text="{Binding Position.Y, StringFormat={}{0:F2}}"/>
                                    </TextBlock>
                                </Grid>
                            </DataTemplate>
                        </d3:DataFollowChart>

                        <d3:DataFollowChart Name="dataFollowChart_Channel4">
                            <DataTemplate>
                                <Grid d3:ViewportPanel.ScreenOffsetY="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <Rectangle RadiusX="3" RadiusY="3" Stroke="LightGray" Fill="#99FFFFFF" Grid.Row="0" Grid.RowSpan="2"/>
                                    <Ellipse Width="10" Height="10" Fill="LightGreen" Stroke="Green" Grid.Row="0"/>
                                    <TextBlock Margin="2,15,2,0" Grid.Row="1">
                                        <TextBlock Text="{Binding Position.X, StringFormat={}{0:F2}}"/>;
                                        <TextBlock Text="{Binding Position.Y, StringFormat={}{0:F2}}"/>
                                    </TextBlock>
                                </Grid>
                            </DataTemplate>
                        </d3:DataFollowChart>
                    </d3:ChartPlotter>
                    
                </Grid>
            </lc:GroupBox>

            <dx:DXTabControl Grid.Row ="2" Margin="0" Padding="0" SelectedIndex="{Binding SelectedTabIndex}">
                <dx:DXTabItem Header=" 波形计算 " Style="{StaticResource myTabItem}" TabIndex="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <dxg:GridControl Grid.Row="1" SelectionMode="Row" ItemsSource="{Binding OsilloscopeCalculate}" AutoGenerateColumns="AddNew" FontStyle="Normal" ShowBorder="False">
                            <dxg:GridControl.View>
                                <dxg:TableView AllowEditing="False" AutoWidth="True" ShowGroupPanel="False" RowMinHeight="22" HeaderPanelMinHeight="22">
                                    <dxg:TableView.FormatConditions>
                                        <dxg:FormatCondition Expression="[Channel1Value] Is Not Null" FieldName="Channel1Value">
                                            <dx:Format Foreground="Red" TextDecorations="{x:Null}"/>
                                        </dxg:FormatCondition>

                                        <dxg:FormatCondition Expression="[Channel2Value] Is Not Null" FieldName="Channel2Value">
                                            <dx:Format Foreground="Orange" TextDecorations="{x:Null}"/>
                                        </dxg:FormatCondition>

                                        <dxg:FormatCondition Expression="[Channel3Value] Is Not Null" FieldName="Channel3Value">
                                            <dx:Format Foreground="Blue" TextDecorations="{x:Null}"/>
                                        </dxg:FormatCondition>

                                        <dxg:FormatCondition Expression="[Channel4Value] Is Not Null" FieldName="Channel4Value">
                                            <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                        </dxg:FormatCondition>
                                    </dxg:TableView.FormatConditions>
                                </dxg:TableView>
                            </dxg:GridControl.View>

                            <dxg:GridColumn FieldName="Title" Header="项目" IsSmart="True" ReadOnly="True" />
                            <dxg:GridColumn FieldName="Number" Header="点位数" IsSmart="True" ReadOnly="True" />
                            <dxg:GridColumn FieldName="AcquisitionTime" Header="采样时间[ms]" IsSmart="True" ReadOnly="True"/>
                            <dxg:GridColumn FieldName="Channel1Value" Header="通道-1" IsSmart="True" ReadOnly="True" />
                            <dxg:GridColumn FieldName="Channel2Value" Header="通道-2" IsSmart="True" ReadOnly="True" />
                            <dxg:GridColumn FieldName="Channel3Value" Header="通道-3" IsSmart="True" ReadOnly="True" />
                            <dxg:GridColumn FieldName="Channel4Value" Header="通道-4" IsSmart="True" ReadOnly="True" />
                        </dxg:GridControl>
                    </Grid>
                </dx:DXTabItem>

                <dx:DXTabItem Header=" 三环调试 " Style="{StaticResource myTabItem}" TabIndex="1">
                    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <dxb:BarContainerControl Grid.Row="0">
                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  刷新参数  " Command="{Binding ReadAdjustmentParameterCommand}" CommandParameter="{Binding SelectedTabIndex}" Glyph="{dx:DXImageOffice2013 Image=Palette_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>
                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  默认参数  " Command="{Binding GetDefaultAdjustmentParameterCommand}"  CommandParameter="{Binding SelectedTabIndex}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>
                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content= "  下载参数  " Command="{Binding WriteLoopParameterCommand}" CommandParameter="{Binding SelectedLoopMode}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>
                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content= "  下载EEPROM参数  " Command="{Binding SaveRAMtoEEPROMCommand}" CommandParameter="{Binding SelectedLoopMode}" Glyph="{dx:DXImageOffice2013 Image=MoveUp_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>
                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content= "  加载刚性  " Command="{Binding LoadRigidityCommand}" CommandParameter="{Binding SelectedLoopMode}" Glyph="{dx:DXImageOffice2013 Image=Download_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>
                            </dxb:BarContainerControl>

                            <Grid Grid.Row="1" Margin="10,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="20"/>

                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="80"/>

                                    <ColumnDefinition Width="40"/>

                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="150"/>
                                    <ColumnDefinition Width="80"/>

                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <Label  Grid.Row="0" Grid.Column="1" Margin="10" Content="模式选择" Style="{StaticResource LabelStyle}" FontStyle="Normal"/>
                                <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="2" Grid.ColumnSpan="2" Margin="10" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding LoopMode}" SelectedItem="{Binding SelectedLoopMode,Mode=TwoWay}" FontStyle="Normal"/>

                                <Label   Grid.Row="0" Grid.Column="5" Margin="10,7" Content="刚性" Visibility="{Binding IsPositionPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource LabelStyle}" FontStyle="Normal"/>
                                <TextBox Grid.Row="0" Grid.Column="6" Grid.ColumnSpan="2" Visibility="{Binding IsPositionPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding Rigidity,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontStyle="Normal"/>

                                <Border  Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="9" BorderThickness="0,1,0,0" BorderBrush="Green"/>

                                <Label   Grid.Row="2" Grid.Column="1" Margin="10,7" Content="电流环增益" Visibility="{Binding IsCurrentPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource LabelStyle}" FontStyle="Normal"/>
                                <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Visibility="{Binding IsCurrentPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding CurrentLoopGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontStyle="Normal"/>
                                <TextBox Grid.Row="2" Grid.Column="3" Visibility="{Binding IsCurrentPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle_Unit}" Text="Hz" />

                                <Label   Grid.Row="2" Grid.Column="5" Margin="10,7" Content="时间积分" Visibility="{Binding IsCurrentPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource LabelStyle}" FontStyle="Normal"/>
                                <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Visibility="{Binding IsCurrentPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding CurrentLoopTimeConstant,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontStyle="Normal"/>
                                <TextBox Grid.Row="2" Grid.Column="7" Visibility="{Binding IsCurrentPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle_Unit}" Text="0.01ms" />

                                <Label   Grid.Row="3" Grid.Column="1" Margin="10,7" Content="位置环增益" Visibility="{Binding IsPositionPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource LabelStyle}" FontStyle="Normal" />
                                <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Visibility="{Binding IsPositionPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding PositionLoopGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontStyle="Normal"/>
                                <TextBox Grid.Row="3" Grid.Column="3" Visibility="{Binding IsPositionPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle_Unit}" Text="0.1/s" />

                                <Label   Grid.Row="3" Grid.Column="5" Margin="10,7" Content="负载惯量比" Visibility="{Binding IsPositionPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource LabelStyle}" FontStyle="Normal" />
                                <TextBox Grid.Row="3" Grid.Column="6" Grid.ColumnSpan="2" Visibility="{Binding IsPositionPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding LoadInertiaRatio,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontStyle="Normal"/>
                                <TextBox Grid.Row="3" Grid.Column="7" Visibility="{Binding IsPositionPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle_Unit}" Text="0.01" />

                                <Label   Grid.Row="4" Grid.Column="1" Margin="10,7" Content="速度环增益" Visibility="{Binding IsSpeedPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource LabelStyle}" FontStyle="Normal"/>
                                <TextBox Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Visibility="{Binding IsSpeedPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding SpeedLoopGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontStyle="Normal"/>
                                <TextBox Grid.Row="4" Grid.Column="3" Visibility="{Binding IsSpeedPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle_Unit}" Text="0.1Hz"/>

                                <Label   Grid.Row="4" Grid.Column="5" Margin="10,7" Content="时间积分" Visibility="{Binding IsSpeedPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource LabelStyle}" FontStyle="Normal"/>
                                <TextBox Grid.Row="4" Grid.Column="6" Grid.ColumnSpan="2" Visibility="{Binding IsSpeedPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding SpeedLoopTimeConstant,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontStyle="Normal"/>
                                <TextBox Grid.Row="4" Grid.Column="7" Visibility="{Binding IsSpeedPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle_Unit}" Text="0.01ms"/>

                                <Label   Grid.Row="5" Grid.Column="1" Margin="10,7" Content="第一转矩滤波时间" Visibility="{Binding IsSpeedPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource LabelStyle}" FontStyle="Normal"/>
                                <TextBox Grid.Row="5" Grid.Column="2" Grid.ColumnSpan="2" Visibility="{Binding IsSpeedPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding FirstTrqcmdFilterTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontStyle="Normal"/>
                                <TextBox Grid.Row="5" Grid.Column="3" Visibility="{Binding IsSpeedPageEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle_Unit}" Text="0.01ms"/>
                            </Grid>
                        </Grid>
                    </ScrollViewer>
                    
                </dx:DXTabItem>

                <dx:DXTabItem Header=" 函数发生器 " Style="{StaticResource myTabItem}" TabIndex="2">
                    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <dxb:BarContainerControl Grid.Row="0">
                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  刷新参数  " Command="{Binding ReadAdjustmentParameterCommand}" CommandParameter="{Binding SelectedTabIndex}" Glyph="{dx:DXImageOffice2013 Image=Palette_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>
                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  默认参数  " Command="{Binding GetDefaultAdjustmentParameterCommand}" CommandParameter="{Binding SelectedTabIndex}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>

                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  设置图示  " Command="{Binding FunctionLegendDisplayCommand}" CommandParameter="{Binding SelectedInnerSourceTypeIndex}" Glyph="{dx:DXImageOffice2013 Image=Image_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>

                                <!--<dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                <dxb:BarButtonItem Content="  启动数采  " Command="{Binding ParameterAcquisitionStartCommand}" IsEnabled="{Binding AcquisitionStartButtonEnabled}" Glyph="{dx:DXImageOffice2013 Image=Next_16x16.png}" BarItemDisplayMode="ContentAndGlyph" />
                            </dxb:ToolBarControl>

                            <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                <dxb:BarButtonItem Content="  停止数采  " Command="{Binding ParameterAcquisitionStopCommand}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/Close_16x16.png" BarItemDisplayMode="ContentAndGlyph"/>
                            </dxb:ToolBarControl>-->

                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  启动函数发生器  " Command="{Binding CreateFunctionGeneratorCommand}" Glyph="{dx:DXImageOffice2013 Image=Next_16x16.png}" BarItemDisplayMode="ContentAndGlyph" />
                                </dxb:ToolBarControl>

                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  停止函数发生器  " Command="{Binding ControlFunctionGeneratorCommand}" CommandParameter ="0" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/Close_16x16.png" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>
                            </dxb:BarContainerControl>

                            <Grid Grid.Row="1" Margin="10,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="20"/>

                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="110"/>
                                    <ColumnDefinition Width="120"/>

                                    <ColumnDefinition Width="40"/>

                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="110"/>
                                    <ColumnDefinition Width="120"/>

                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <Label  Grid.Row="0" Grid.Column="1" Margin="10" Content="作用对象" Style="{StaticResource LabelStyle}"/>
                                <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="2" Grid.ColumnSpan="2" Margin="10" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding InnerSourceEffect}" SelectedIndex="{Binding SelectedInnerSourceEffectIndex,Mode=TwoWay}"/>

                                <Label  Grid.Row="0" Grid.Column="5" Margin="10" Content="函数类型"  Style="{StaticResource LabelStyle}"/>
                                <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="6" Grid.ColumnSpan="2" Margin="10" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding InnerSourceType}"  SelectedIndex="{Binding SelectedInnerSourceTypeIndex,Mode=TwoWay}"/>

                                <Border  Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="9" BorderThickness="0,1,0,0" BorderBrush="Green"/>

                                <Label   Grid.Row="2" Grid.Column="1" Margin="10,7" Content="指令幅值" Style="{StaticResource LabelStyle}"/>
                                <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding InnerSourceAmplitude,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="2" Grid.Column="3" Text="{Binding AmplitudeUnit,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}"/>

                                <Label   Grid.Row="2" Grid.Column="5" Margin="10,7" Content="信号频率" Style="{StaticResource LabelStyle}" />
                                <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding InnerSourceFrequency,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="2" Grid.Column="7" Text="0.1Hz"  Style="{StaticResource TextBoxStyle_Unit}"/>

                                <Label   Grid.Row="3" Grid.Column="1" Margin="10,7" Content="产生数量" Style="{StaticResource LabelStyle}" FontStyle="Normal"/>
                                <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding InnerSourceNumber,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding InnerSourceNumberEnabled}"/>
                                <TextBox Grid.Row="3" Grid.Column="3" Text="个" Style="{StaticResource TextBoxStyle_Unit}"/>

                                <Label   Grid.Row="3" Grid.Column="5" Margin="10,7" Content="加减速度" Style="{StaticResource LabelStyle}" FontStyle="Normal" Visibility="{Binding InnerSourceGradientVisibility,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="3" Grid.Column="6" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,7" Text="{Binding InnerSourceGradient,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding InnerSourceGradientVisibility,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="3" Grid.Column="7" Text="{Binding AmplitudeUnit, StringFormat={}{0}/s,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding InnerSourceGradientVisibility,Converter={StaticResource VisibilityConverter}}"/>
                            </Grid>
                        </Grid>
                    </ScrollViewer>
                    
                </dx:DXTabItem>

                <dx:DXTabItem Header=" 运动调试 " Style="{StaticResource myTabItem}" TabIndex="3" Height="21" VerticalAlignment="Top" >
                    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <dxb:BarContainerControl Grid.Row="0">
                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  刷新参数  " Command="{Binding ReadAdjustmentParameterCommand}" CommandParameter="{Binding SelectedTabIndex}" Glyph="{dx:DXImageOffice2013 Image=Palette_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>

                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  默认参数  " Command="{Binding GetDefaultAdjustmentParameterCommand}" CommandParameter="{Binding SelectedTabIndex}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>

                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  设置图示  " Command="{Binding LegendDisplayCommand}" CommandParameter="{Binding SelectedActionMode}"  Glyph="{dx:DXImageOffice2013 Image=Image_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>

                                <!--<dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                <dxb:BarButtonItem Content="  启动数采  " Command="{Binding ParameterAcquisitionStartCommand}" IsEnabled="{Binding AcquisitionStartButtonEnabled}" Glyph="{dx:DXImageOffice2013 Image=Next_16x16.png}" BarItemDisplayMode="ContentAndGlyph" />
                            </dxb:ToolBarControl>

                            <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                <dxb:BarButtonItem Content="  停止数采  " Command="{Binding ParameterAcquisitionStopCommand}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/Close_16x16.png" BarItemDisplayMode="ContentAndGlyph"/>
                            </dxb:ToolBarControl>-->

                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  启动运动 " Command="{Binding StartActionCommand}" CommandParameter="{Binding SelectedActionMode}" Glyph="{dx:DXImageOffice2013 Image=Next_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>

                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  停止运动  " Command="{Binding StopActionCommand}" CommandParameter="{Binding SelectedActionMode}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/Close_16x16.png" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>
                            </dxb:BarContainerControl>

                            <Grid Grid.Row="1" Margin="10,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="20"/>

                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="90"/>
                                    <ColumnDefinition Width="120"/>

                                    <ColumnDefinition Width="15"/>

                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="90"/>
                                    <ColumnDefinition Width="120"/>

                                    <ColumnDefinition Width="15"/>

                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="90"/>
                                    <ColumnDefinition Width="120"/>

                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <Label Grid.Row="0" Grid.Column="1" Margin="10" Content="模式选择" Style="{StaticResource LabelStyle}" FontStyle="Normal"/>
                                <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="2" Grid.ColumnSpan="2" Margin="10" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding ActionMode}" SelectedItem="{Binding SelectedActionMode,Mode=TwoWay}" FontStyle="Normal"/>

                                <Label Grid.Row="0" Grid.Column="5" Margin="10" Content="位置指令类型" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="6" Grid.ColumnSpan="2" Margin="10" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding PositionMode}" SelectedItem="{Binding SelectedPositionMode,Mode=TwoWay}" FontStyle="Normal"/>

                                <Label Grid.Row="0" Grid.Column="5" Margin="10" Content="规划曲线类型" Style="{StaticResource LabelStyle}" Visibility="{Binding IsSlopeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="6" Grid.ColumnSpan="2" Margin="10" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding ProfileMode}" SelectedItem="{Binding SelectedProfileMode,Mode=TwoWay}" FontStyle="Normal" Visibility="{Binding IsSlopeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Border Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="13" BorderThickness="0,1,0,0" BorderBrush="Green"/>

                                <Label   Grid.Row="2" Grid.Column="1" Margin="10,7" Content="目标位置" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,7" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}" Style="{StaticResource TextBoxStyle}" Text="{Binding TargetPosition,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="2" Grid.Column="3" Text="{Binding PositionUnit,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="2" Grid.Column="5" Margin="10,7" Content="轮廓运行速度" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,7" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding ProfileVelocity,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="2" Grid.Column="7" Text="{Binding SpeedUnit,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="2" Grid.Column="9" Margin="10,7" Content="轮廓结尾速度" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="2" Grid.Column="10" Grid.ColumnSpan="2" Margin="10,7" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding EndProfileVelocity,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="2" Grid.Column="11" Text="{Binding SpeedUnit,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="3" Grid.Column="1" Margin="10,7" Content="轮廓加速度" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,7" Style="{StaticResource TextBoxStyle}"  Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding ProfileAcceleration,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="3" Grid.Column="3" Text="{Binding AccelerationUnit,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="3" Grid.Column="5" Margin="10,7" Content="轮廓减速度" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="3" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,7" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding ProfileDeceleration,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="3" Grid.Column="7" Text="{Binding AccelerationUnit,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsPositionActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="4" Grid.Column="1" Margin="10,6" Content="目标速度" Style="{StaticResource LabelStyle}" Visibility="{Binding IsSlopeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,6" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsSlopeActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding TargetVelocity,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="4" Grid.Column="3" Text="{Binding SpeedUnit,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsSlopeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="4" Grid.Column="5" Margin="10,6" Content="轮廓加速度" Style="{StaticResource LabelStyle}" Visibility="{Binding IsSlopeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="4" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,6" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsSlopeActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding ProfileAcceleration,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="4" Grid.Column="7" Text="{Binding AccelerationUnit,UpdateSourceTrigger=PropertyChanged }"  Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsSlopeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="4" Grid.Column="9" Margin="10,6" Content="轮廓减速度" Style="{StaticResource LabelStyle}" Visibility="{Binding IsSlopeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="4" Grid.Column="10" Grid.ColumnSpan="2" Margin="10,6" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsSlopeActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding ProfileDeceleration,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="4" Grid.Column="11" Text="{Binding AccelerationUnit,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsSlopeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="5" Grid.Column="1" Margin="10,2" Content="轮廓加加&#x0a;速度时间1" Style="{StaticResource LabelStyle}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="5" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding ProfileJerk1,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="5" Grid.Column="3" Text="ms" Margin="12,2" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="5" Grid.Column="5" Margin="10,2" Content="轮廓加加&#x0a;速度时间2" Style="{StaticResource LabelStyle}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="5" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding ProfileJerk2,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="5" Grid.Column="7" Text="ms" Margin="12,2" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="5" Grid.Column="9" Margin="10,2" Content="轮廓加加&#x0a;速度时间3" Style="{StaticResource LabelStyle}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="5" Grid.Column="10" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding ProfileJerk3,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="5" Grid.Column="11" Text="ms" Margin="12,2" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="6" Grid.Column="1" Margin="10,2" Content="轮廓加加&#x0a;速度时间4" Style="{StaticResource LabelStyle}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="6" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding ProfileJerk4,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="6" Grid.Column="3" Text="ms" Margin="12,2" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsJerkFreeActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="7" Grid.Column="1" Margin="10,7" Content="目标转矩" Style="{StaticResource LabelStyle}" Visibility="{Binding IsTorqueActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="7" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,7" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsTorqueActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding TargetTorque,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="7" Grid.Column="3" Text="{Binding TorqueUnit,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsTorqueActionEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                <Label   Grid.Row="7" Grid.Column="5" Margin="10,7" Content="转矩斜坡" Style="{StaticResource LabelStyle}" Visibility="{Binding IsTorqueActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <TextBox Grid.Row="7" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,7" Style="{StaticResource TextBoxStyle}" Visibility="{Binding IsTorqueActionEnabled,Converter={StaticResource VisibilityConverter}}" Text="{Binding TorqueSlope,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                                <TextBox Grid.Row="7" Grid.Column="7" Text="{Binding TorqueUnit, StringFormat={}{0}/s , UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsTorqueActionEnabled,Converter={StaticResource VisibilityConverter}}"/>
                            </Grid>
                        </Grid>
                    </ScrollViewer>
                    
                </dx:DXTabItem>

                <dx:DXTabItem Header=" 参数调优 " Style="{StaticResource myTabItem}" TabIndex="4" Height="21" VerticalAlignment="Top">
                    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,1">
                                <dxb:BarContainerControl Grid.Row="0">
                                    <!--<dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  刷新参数  " Command="{Binding ReadAdjustmentParameterCommand}" CommandParameter="{Binding SelectedTabIndex}" Glyph="{dx:DXImageOffice2013 Image=Palette_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>-->

                                    <!--<dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  默认参数  " Command="{Binding GetDefaultAdjustmentParameterCommand}" CommandParameter="{Binding SelectedTabIndex}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>-->

                                    <!--<dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                    <dxb:BarButtonItem Content="  设置图示  " Command="{Binding LegendDisplayCommand}" CommandParameter="{Binding SelectedActionMode}"  Glyph="{dx:DXImageOffice2013 Image=Image_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>-->

                                    <!--<dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                <dxb:BarButtonItem Content="  启动数采  " Command="{Binding ParameterAcquisitionStartCommand}" IsEnabled="{Binding AcquisitionStartButtonEnabled}" Glyph="{dx:DXImageOffice2013 Image=Next_16x16.png}" BarItemDisplayMode="ContentAndGlyph" />
                                </dxb:ToolBarControl>

                                <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                <dxb:BarButtonItem Content="  停止数采  " Command="{Binding ParameterAcquisitionStopCommand}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/Close_16x16.png" BarItemDisplayMode="ContentAndGlyph"/>
                                </dxb:ToolBarControl>-->

                                    <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                        <dxb:BarButtonItem Content="  启动运动 " Command="{Binding StartParameterTunningActionCommand}" CommandParameter="{Binding SelectedParameterTunningMode}" Glyph="{dx:DXImageOffice2013 Image=Next_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                                    </dxb:ToolBarControl>

                                    <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                        <dxb:BarButtonItem Content="  停止运动  " Command="{Binding StopParameterTunningActionCommand}" CommandParameter="{Binding SelectedParameterTunningMode}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/Close_16x16.png" BarItemDisplayMode="ContentAndGlyph"/>
                                    </dxb:ToolBarControl>
                                </dxb:BarContainerControl>
                                <!--<Label Margin="6,0" Content="程序JOG模式" Style="{StaticResource LabelStyle}"/>
                            <dxe:ComboBoxEdit Margin="0,0,6,0" Style="{StaticResource SettingComboBoxStyle}" Width="150" ItemsSource="{Binding ProgramJogSwitch}" SelectedIndex="{Binding SelectedProgramJogSwitchIndex,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>-->

                                <!--<dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Separator_16x16.png}" Command="{Binding ProgramJogModelSwitchCommand}" Width="Auto">
                                <Label Content="{Binding ProgramJogSwitchHint}" Style="{StaticResource LabelStyle}" Margin="0" />
                            </dx:SimpleButton>

                            <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Reset_16x16.png}" Width="Auto" IsEnabled="{Binding IsProgramJogButtonEnabled}">
                                <dxmvvm:Interaction.Behaviors>
                                    <dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding ProgramJogRunCommand}"/>
                                    <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding ProgramJogStopCommand}"/>
                                </dxmvvm:Interaction.Behaviors>
                                <Label Content="电机转动" Style="{StaticResource LabelStyle}" Margin="0" />
                            </dx:SimpleButton>-->

                                <!--<dxb:ToolBarControl Width="95" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                <dxb:BarButtonItem Content="{Binding ProgramJogSwitchHint}"  Command="{Binding ProgramJogModelSwitchCommand}" CommandParameter="{Binding SelectedTabIndex}" Glyph="{dx:DXImageOffice2013 Image=Separator_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                            </dxb:ToolBarControl>
                            <dxb:ToolBarControl Width="95" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                                <dxmvvm:Interaction.Behaviors>
                                    <dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding ProgramJogRunCommand}"/>
                                    <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding ProgramJogStopCommand}"/>
                                </dxmvvm:Interaction.Behaviors>
                                <dxb:BarButtonItem Content="  电机转动  "  CommandParameter="{Binding SelectedTabIndex}" Glyph="{dx:DXImageOffice2013 Image=Reset_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
                            </dxb:ToolBarControl>-->
                            </StackPanel>

                            <StackPanel Grid.Row="1" Orientation="Vertical" Margin="0,0,0,1">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="80"/>

                                        <ColumnDefinition Width="180"/>
                                        <ColumnDefinition Width="30"/>

                                        <ColumnDefinition Width="100"/>

                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="60"/>

                                        <ColumnDefinition Width="10"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="60"/>

                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Label Grid.Row="0" Grid.Column="0" Margin="10" Content="模式选择" Style="{StaticResource LabelStyle}" FontStyle="Normal"/>
                                    <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="1" Margin="10" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding ParameterTunningMode}" SelectedItem="{Binding SelectedParameterTunningMode,Mode=TwoWay}" FontStyle="Normal"/>

                                    <!--<Label  Grid.Row="0" Grid.Column="3" Margin="6,0" Content="程序JOG模式" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionParameterTuningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="4" Margin="0,0,6,0" Style="{StaticResource SettingComboBoxStyle}" Width="140" ItemsSource="{Binding ProgramJogSwitch}" SelectedIndex="{Binding SelectedProgramJogSwitchIndex,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsPositionParameterTuningEnabled,Converter={StaticResource VisibilityConverter}}"/>-->

                                    <Border  Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="12" BorderThickness="0,1,0,0" BorderBrush="Green"/>
                                </Grid>
                            </StackPanel>

                            <Grid Grid.Row="2" Margin="5,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <Grid Grid.Column="0" Margin="1,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="5"/>

                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="140"/>
                                        <ColumnDefinition Width="60"/>

                                        <ColumnDefinition Width="10"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="130"/>
                                        <ColumnDefinition Width="60"/>

                                        <ColumnDefinition Width="5"/>
                                    </Grid.ColumnDefinitions>

                                    <Label  Grid.Row="0" Grid.Column="1" Margin="0,9,5,14" Content="程序JOG模式" FontStyle="Normal" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="2" Margin="2,6,5,12" Width="134" FontStyle="Normal" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding ProgramJogSwitch}" SelectedIndex="{Binding SelectedProgramJogSwitchIndex,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                    <Label Grid.Row="0" Grid.Column="5" Margin="0,9,5,14" Content="移动距离" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="0" Grid.Column="6" Grid.ColumnSpan="2" Width="120" Style="{StaticResource TextBoxStyle}" Margin="0,6,56,12" Text="{Binding ParameterTunningProgramJogMovingDistance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="0" Grid.Column="6" Text="{Binding PositionUnit,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}" Margin="88,10,6,15" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                    <Label Grid.Row="0" Grid.Column="1" Margin="5,9,5,14" Content="运动时间" Style="{StaticResource LabelStyle}" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="0" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,6,10,12" Text="{Binding ParameterTunningJogTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="0" Grid.Column="3" Text="0.1s" Style="{StaticResource TextBoxStyle_Unit}" Margin="12,9,12,16" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                    <Label Grid.Row="1" Grid.Column="1" Margin="0,8,5,15" Content="移动速度" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="1" Grid.Column="2" Width="135" Style="{StaticResource TextBoxStyle}" Margin="0,6,0,12" Text="{Binding ParameterTunningProgramJogMovingSpeed,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="1" Grid.Column="2" Text="rpm" Style="{StaticResource TextBoxStyle_Unit}" Margin="88,10,6,15" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                    <Label Grid.Row="0" Grid.Column="5" Margin="5,9,5,14" Content="JOG速度" Style="{StaticResource LabelStyle}" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="0" Grid.Column="6" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,6,10,12" Text="{Binding ParameterTunningJogSpeed,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="0" Grid.Column="7" Text="rpm" Style="{StaticResource TextBoxStyle_Unit}" Margin="12,10,12,15" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                    <Label Grid.Row="1" Grid.Column="5" Margin="0,9,5,14" Content="加减速时间" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Width="120" Style="{StaticResource TextBoxStyle}" Margin="0,6,56,12" Text="{Binding ParameterTunningProgramJogAccDecTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="1" Grid.Column="6" Text="ms" Style="{StaticResource TextBoxStyle_Unit}" Margin="66,10,4,15" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                    <Label Grid.Row="1" Grid.Column="1" Margin="5,8,5,15" Content="JOG加速时间" Style="{StaticResource LabelStyle}" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,6,10,12" Text="{Binding ParameterTunningJogAccelerationTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="1" Grid.Column="3" Text="ms" Style="{StaticResource TextBoxStyle_Unit}" Margin="12,10,12,15" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                    <Label Grid.Row="2" Grid.Column="1" Margin="0,8,5,15" Content="等待时间" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="2" Grid.Column="2" Width="135" Style="{StaticResource TextBoxStyle}" Margin="0,6,0,12" Text="{Binding ParameterTunningProgramJogWaitTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="2" Grid.Column="2" Text="ms" Style="{StaticResource TextBoxStyle_Unit}" Margin="75,12,5,13" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                    <Label Grid.Row="1" Grid.Column="5" Margin="5,9,5,14" Content="JOG减速时间" Style="{StaticResource LabelStyle}" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,6,10,12" Text="{Binding ParameterTunningJogDecelerationTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="1" Grid.Column="7" Text="ms" Style="{StaticResource TextBoxStyle_Unit}" Margin="12,9,12,16" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                    <Label Grid.Row="2" Grid.Column="5" Margin="0,9,5,14" Content="移动次数" Style="{StaticResource LabelStyle}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Width="120" Style="{StaticResource TextBoxStyle}" Margin="0,6,56,12" Text="{Binding ParameterTunningProgramJogMovingNumber,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                    <TextBox Grid.Row="2" Grid.Column="6" Text="次" Style="{StaticResource TextBoxStyle_Unit}" Margin="66,10,4,15" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>

                                </Grid>

                                <Grid Grid.Column="1" Margin="0,0">

                                    <dxe:ImageEdit Height="Auto" Width="410" ShowMenu="False" Source="{Binding LegendAddress,UpdateSourceTrigger=PropertyChanged}" ShowBorder="False" Opacity="0.7" HorizontalAlignment="Center" VerticalAlignment="Center" Visibility="{Binding IsPositionParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}" />

                                    <dxe:ImageEdit Height="120" Width="Auto" HorizontalAlignment="Left" VerticalAlignment="Center" ShowMenu="False" Source="pack://application:,,,/ServoStudio;component/Resource/JOG.png" ShowBorder="False" Opacity="0.65" Visibility="{Binding IsSpeedParameterTunningEnabled,Converter={StaticResource VisibilityConverter}}"/>
                                </Grid>
                            </Grid>
                        </Grid>
                    </ScrollViewer>
                    
                </dx:DXTabItem>
            </dx:DXTabControl>
        </Grid>
    </Grid>
</UserControl>
