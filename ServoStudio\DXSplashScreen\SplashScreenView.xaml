﻿<UserControl
    x:Class="ServoStudio.SplashScreenView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
    xmlns:local="clr-namespace:ServoStudio.Views"
    xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
    xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm"
    xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
    xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui" 
    xmlns:Views="clr-namespace:ServoStudio.Views"
    DataContext="{dxmvvm:ViewModelSource Type={x:Type ViewModels:SplashScreenViewModel}}"
    
    mc:Ignorable="d"
    d:DataContext="{x:Static dx:SplashScreenViewModel.DesignTimeData}"

    
    
    >

    <Grid x:Name="LayoutRoot">
        <Grid x:Name="Splash" Width="450" HorizontalAlignment="Center" VerticalAlignment="Top" Margin="0">
            <Grid x:Name="Back">
                <Border Background="Black" CornerRadius="3" Opacity="0.15"/>
                <Border CornerRadius="2" Margin="1" Background="White"/>
            </Grid>
            <Grid x:Name="Content_Area" Margin="12">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Image Source="Image.png" Stretch="Uniform"/>
                <Canvas  Background="#FF107EBD">
                    <Label Content="Servo Studio" Canvas.Left="10" Canvas.Top="109" FontSize="55" FontFamily="Agency FB" Foreground="White"/>
                </Canvas>

                <TextBlock x:Name="Info" TextWrapping="Wrap" Text="{Binding State}" Grid.Row="1" Margin="12,12,12,0" Foreground="#FF2D2D2D"/>
                <ProgressBar x:Name="progressBar"
                             Height="12"
                             Grid.Row="2"
                             Margin="12"
                             IsIndeterminate="False"
                             Value="{Binding Progress}"
                             Maximum="{Binding MaxProgress}"/>

                <Grid Grid.Row="3" Margin="12,2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1.5*"/>
                        <ColumnDefinition Width="0.5*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <DockPanel Grid.Column="0">
                    
                        <TextBlock x:Name="Footer_Text" TextWrapping="Wrap" Text="Copyright © 2019-2024" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Left" VerticalAlignment="Center"/>
                        <!--<Image Source="pack://application:,,,/ServoStudio;component/Resource/JHL.png" Stretch="Uniform" HorizontalAlignment="Right" Height="12" Margin="105,0,0,0"/>-->
                        <!--<TextBlock x:Name="Footer_Belong" TextWrapping="Wrap" Text="季华实验室-机器人中心" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                        <Image Source="{Binding CompanyLogo}" Stretch="Uniform" HorizontalAlignment="Right" Height="12" Margin="105,0,0,0"/>-->
                        <!--<TextBlock TextWrapping="Wrap" Width="Auto" Text="{Binding CompanyDepartment}" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Right" VerticalAlignment="Center"/>-->
                    
                    </DockPanel>

                    <DockPanel Grid.Column="1">

                        <!--<TextBlock x:Name="Footer_Text" TextWrapping="Wrap" Text="Copyright © 2019-2023" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Left" VerticalAlignment="Center"/>-->
                        <!--<Image Source="pack://application:,,,/ServoStudio;component/Resource/JHL.png" Stretch="Uniform" HorizontalAlignment="Right" Height="12" Margin="105,0,0,0"/>-->
                        <!--<TextBlock x:Name="Footer_Belong" TextWrapping="Wrap" Text="季华实验室-机器人中心" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                        <Image Source="{Binding CompanyLogo}" Stretch="Uniform" HorizontalAlignment="Right" Height="12" Margin="105,0,0,0"/>-->
                        <!--<TextBlock TextWrapping="Wrap" Width="Auto" Text="{Binding CompanyDepartment}" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Right" VerticalAlignment="Center"/>-->

                    </DockPanel>

                    <DockPanel Grid.Column="2">

                        <!--<TextBlock x:Name="Footer_Text" TextWrapping="Wrap" Text="Copyright © 2019-2023" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Left" VerticalAlignment="Center"/>-->
                        <!--<Image Source="pack://application:,,,/ServoStudio;component/Resource/JHL.png" Stretch="Uniform" HorizontalAlignment="Right" Height="12" Margin="105,0,0,0"/>-->
                        <!--<TextBlock x:Name="Footer_Belong" TextWrapping="Wrap" Text="季华实验室-机器人中心" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                        <Image Source="{Binding CompanyLogo}" Stretch="Uniform" HorizontalAlignment="Right" Height="12" Margin="105,0,0,0"/>-->
                        <!--<TextBlock TextWrapping="Wrap" Text="{Binding CompanyDepartment}" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Right" VerticalAlignment="Center"/>-->
                        <TextBlock TextWrapping="Wrap" Text="{Binding CompanyDepartment, Mode=TwoWay}" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Right" VerticalAlignment="Center"/>

                    </DockPanel>

                </Grid>
                
                <!--<DockPanel x:Name="Footer" Grid.Row="3" Margin="12,2">
                    --><!--<TextBlock x:Name="Footer_Text" TextWrapping="Wrap" Text="Copyright © 2019-2023" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Left" VerticalAlignment="Center"/>-->
                    <!--<Image Source="pack://application:,,,/ServoStudio;component/Resource/JHL.png" Stretch="Uniform" HorizontalAlignment="Right" Height="12" Margin="105,0,0,0"/>-->
                    <!--<TextBlock x:Name="Footer_Belong" TextWrapping="Wrap" Text="季华实验室-机器人中心" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Right" VerticalAlignment="Center"/>-->
                    <!--<Image Source="{Binding CompanyLogo}" Stretch="Uniform" HorizontalAlignment="Right" Height="12" Margin="105,0,0,0"/>-->
                    <!--<TextBlock TextWrapping="Wrap" Width="Auto" Text="{Binding CompanyDepartment}" Opacity="0.5" Foreground="#FF2D2D2D" HorizontalAlignment="Right" VerticalAlignment="Center"/>--><!--
                </DockPanel>-->
            </Grid>
        </Grid>
    </Grid>
</UserControl>
