﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Collections.ObjectModel;
using DevExpress.Mvvm.POCO;
using System.Data;
using System.Collections.Generic;
using ServoStudio.Models;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class PositionLoopViewModel
    {
        #region 私有字段
        private static bool IsInitialized = true;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 服务
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 属性
        public virtual ObservableCollection<string> SpdFFControlSelect { get; set; }//速度前馈选择
        public virtual string SelectedSpdFFControlSelectIndex { get; set; }//速度前馈选择
        public virtual string PositionLoopGain { get; set; }//位置环增益
        public virtual string SpdFFFilterFimeConstant { get; set; }//速度前馈滤波时间常数
        public virtual string SpdFFGain { get; set; }//速度前馈增益
        public virtual string PositionReferenceMaFilterTime { get; set; }//位置指令平均滤波时间
        public virtual string PositionReferenceHighFilterRatio { get; set; }//位置指令平滑比
        public virtual string SpeedFeedForwardMaFilterTime { get; set; }//速度前馈平均滤波时间

        //背景
        public virtual int PositionLoopGainBackground { get; set; }
        public virtual int SpdFFControlSelectBackground { get; set; }
        public virtual int SpdFFFilterFimeConstantBackground { get; set; }
        public virtual int SpdFFGainBackground { get; set; }
        public virtual int PositionReferenceMaFilterTimeBackground { get; set; }//位置指令平均滤波时间
        public virtual int PositionReferenceHighFilterRatioBackground { get; set; }//位置指令平滑比
        public virtual int SpeedFeedForwardMaFilterTimeBackground { get; set; }//速度前馈平均滤波时间
        #endregion

        #region 构造函数
        public PositionLoopViewModel()
        {
            ViewModelSet.PositionLoop = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.POSITIONLOOP;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：PositionLoopLoaded
        //函数功能：PositionLoop界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void PositionLoopLoaded()
        {
            int iRet = -1;

            try
            {
                //背景初始化
                BackgroundInitialize();

                //ComboBox初始化
                ComboBoxInitialize();

                //赋值
                if (IsInitialized == true)
                {                  
                    IsInitialized = false;

                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadPositionLoopParameter();
                    }
                    else
                    {
                        GetDefaultPositionLoopParameter();
                    }
                }
                else
                {
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadPositionLoopParameter();
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }                 
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.POSITIONLOOP_LOADED, "PositionLoopLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：WritePositionLoopParameter
        //函数功能：写电流环参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void WritePositionLoopParameter()
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary(ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MAIN, TaskName.PositionLoop, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.POSITIONLOOP_WRITE_PARAMETER, "WritePositionLoopParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadPositionLoopParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void ReadPositionLoopParameter()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.PositionLoop, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.POSITIONLOOP_READ_PARAMETER, "ReadPositionLoopParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultPositionLoopParameter
        //函数功能：获取限幅保护的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void GetDefaultPositionLoopParameter()
        {
            try
            {
                PositionLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Default");
                SelectedSpdFFControlSelectIndex = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "SpdFF Control Select", "Default");
                SpdFFFilterFimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "SpdFF Filter Fime Constant", "Default");
                SpdFFGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "SpdFF Gain", "Default");
                PositionReferenceMaFilterTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Reference Ma Filter Time", "Default");
                PositionReferenceHighFilterRatio = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Reference High Filter Ratio", "Default");
                SpeedFeedForwardMaFilterTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Feed Forward Ma Filter Time", "Default");         
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.POSITIONLOOP_GET_DEFAULT_PARAMETER, "GetDefaultPositionLoopParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：SavePositionLoopConfigFile
        //函数功能：保存限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void SavePositionLoopConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                //选择配置文件存放位置
                iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.PositionLoop);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //生成配置文件
                iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetPositionLoopConfigToDataTable(), ExcelType.PositionLoop);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2002);
                }
                else
                {
                    ShowNotification(2003);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.POSITIONLOOP_SAVE_CONFIG_FILE, "SavePositionLoopConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetPositionLoopConfigFile
        //函数功能：获取限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void GetPositionLoopConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            DataTable dt = null;

            try
            {
                //获取配置文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(strFilePath, ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //判断是否导入正确的配置文件
                if (dt.Columns[3].ColumnName != FileInterface.POSITIONLOOP)
                {
                    ShowNotification(2001);
                    return;
                }

                //导入参数  
                iRet = GetPositionLoopConfigFromDataTable(dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //是否下载
                OthersHelper.GetWindowsStartupPosition();
                if (MessageBoxService.ShowMessage("是否下载当前导入的位置环参数...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    WritePositionLoopParameter();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.POSITIONLOOP_GET_CONFIG_FILE, "GetPositionLoopConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationPositionLoopParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void EvaluationPositionLoopParameter()
        {
            PositionLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//位置环增益
            SelectedSpdFFControlSelectIndex = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "SpdFF Control Select", "Index"));//速度前馈选择
            SpdFFFilterFimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "SpdFF Filter Fime Constant", "Index"));//速度前馈滤波时间常数
            SpdFFGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "SpdFF Gain", "Index"));//速度前馈增益

            PositionReferenceMaFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Reference Ma Filter Time", "Index"));//位置指令平均滤波时间
            PositionReferenceHighFilterRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Reference High Filter Ratio", "Index"));//位置指令平滑比
            SpeedFeedForwardMaFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Feed Forward Ma Filter Time", "Index"));//速度前馈平均滤波时间
        }       

        //*************************************************************************
        //函数名称：ChangeBackground
        //函数功能：更换背景
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.16
        //*************************************************************************
        public void ChangeBackground(string strName)
        {
            if (strName == "位置环增益")
            {
                PositionLoopGainBackground = BackgroundState.Selected;
                SpdFFControlSelectBackground = BackgroundState.NotSelected;
                SpdFFFilterFimeConstantBackground = BackgroundState.NotSelected;
                SpdFFGainBackground = BackgroundState.NotSelected;
                PositionReferenceMaFilterTimeBackground = BackgroundState.NotSelected;
                PositionReferenceHighFilterRatioBackground = BackgroundState.NotSelected;
                SpeedFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
             }
            else if (strName == "前馈选择")
            {
                PositionLoopGainBackground = BackgroundState.NotSelected;
                SpdFFControlSelectBackground = BackgroundState.Selected;
                SpdFFFilterFimeConstantBackground = BackgroundState.NotSelected;
                SpdFFGainBackground = BackgroundState.NotSelected;
                PositionReferenceMaFilterTimeBackground = BackgroundState.NotSelected;
                PositionReferenceHighFilterRatioBackground = BackgroundState.NotSelected;
                SpeedFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }
            else if (strName == "平滑常数")
            {
                PositionLoopGainBackground = BackgroundState.NotSelected;
                SpdFFControlSelectBackground = BackgroundState.NotSelected;
                SpdFFFilterFimeConstantBackground = BackgroundState.Selected;
                SpdFFGainBackground = BackgroundState.NotSelected;
                PositionReferenceMaFilterTimeBackground = BackgroundState.NotSelected;
                PositionReferenceHighFilterRatioBackground = BackgroundState.NotSelected;
                SpeedFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }
            else if (strName == "前馈增益")
            {
                PositionLoopGainBackground = BackgroundState.NotSelected;
                SpdFFControlSelectBackground = BackgroundState.NotSelected;
                SpdFFFilterFimeConstantBackground = BackgroundState.NotSelected;
                SpdFFGainBackground = BackgroundState.Selected;
                PositionReferenceMaFilterTimeBackground = BackgroundState.NotSelected;
                PositionReferenceHighFilterRatioBackground = BackgroundState.NotSelected;
                SpeedFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }   
            else if (strName == "位置指令滤波")
            {
                PositionLoopGainBackground = BackgroundState.NotSelected;
                SpdFFControlSelectBackground = BackgroundState.NotSelected;
                SpdFFFilterFimeConstantBackground = BackgroundState.NotSelected;
                SpdFFGainBackground = BackgroundState.NotSelected;
                PositionReferenceMaFilterTimeBackground = BackgroundState.Selected;
                PositionReferenceHighFilterRatioBackground = BackgroundState.Selected;
                SpeedFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }   
            else if (strName == "平均值滤波")
            {
                PositionLoopGainBackground = BackgroundState.NotSelected;
                SpdFFControlSelectBackground = BackgroundState.NotSelected;
                SpdFFFilterFimeConstantBackground = BackgroundState.NotSelected;
                SpdFFGainBackground = BackgroundState.NotSelected;
                PositionReferenceMaFilterTimeBackground = BackgroundState.NotSelected;
                PositionReferenceHighFilterRatioBackground = BackgroundState.NotSelected;
                SpeedFeedForwardMaFilterTimeBackground = BackgroundState.Selected;
            }    
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void OnPositionLoopGainChanged() { GlobalCurrentInput.PositionLoopGain_PositionLoop = PositionLoopGain; }//位置环增益
        public void OnSelectedSpdFFControlSelectIndexChanged() { GlobalCurrentInput.SelectedSpdFFControlSelectIndex = SelectedSpdFFControlSelectIndex; }//速度前馈选择
        public void OnSpdFFFilterFimeConstantChanged() { GlobalCurrentInput.SpdFFFilterFimeConstant = SpdFFFilterFimeConstant; }//速度前馈滤波时间常数
        public void OnSpdFFGainChanged() { GlobalCurrentInput.SpdFFGain = SpdFFGain; }//速度前馈增益

        public void OnPositionReferenceMaFilterTimeChanged() { GlobalCurrentInput.PositionReferenceMaFilterTime = PositionReferenceMaFilterTime; }
        public void OnPositionReferenceHighFilterRatioChanged() { GlobalCurrentInput.PositionReferenceHighFilterRatio = PositionReferenceHighFilterRatio; }
        public void OnSpeedFeedForwardMaFilterTimeChanged() { GlobalCurrentInput.SpeedFeedForwardMaFilterTime = SpeedFeedForwardMaFilterTime; }
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：BackgroundInitialize
        //函数功能：背景初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.16
        //*************************************************************************
        private void BackgroundInitialize()
        {
            PositionLoopGainBackground = BackgroundState.NotSelected;
            SpdFFControlSelectBackground = BackgroundState.NotSelected;
            SpdFFFilterFimeConstantBackground = BackgroundState.NotSelected;
            SpdFFGainBackground = BackgroundState.NotSelected;
            PositionReferenceMaFilterTimeBackground = BackgroundState.NotSelected;
            PositionReferenceHighFilterRatioBackground = BackgroundState.NotSelected;
            SpeedFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
    }

        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：下拉列表初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.16
        //*************************************************************************
        private void ComboBoxInitialize()
        {
            SpdFFControlSelect = new ObservableCollection<string>() { "无前馈", "内部前馈", "外部给定" };
        }

        //*************************************************************************
        //函数名称：GetPositionLoopConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private DataTable GetPositionLoopConfigToDataTable()
        {
            DataTable dt = new DataTable();

            try
            {
                OthersHelper.MakeValueToDataTable(FileInterface.POSITIONLOOP, "Position Loop Gain", "位置环增益", PositionLoopGain, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.POSITIONLOOP, "SpdFF Control Select", "速度前馈选择", SelectedSpdFFControlSelectIndex, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.POSITIONLOOP, "SpdFF Filter Fime Constant", "速度前馈滤波时间常数", SpdFFFilterFimeConstant, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.POSITIONLOOP, "SpdFF Gain", "速度前馈增益", SpdFFGain, ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.POSITIONLOOP, "Position Reference Ma Filter Time", "位置指令平均滤波时间", PositionReferenceMaFilterTime, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.POSITIONLOOP, "Position Reference High Filter Ratio", "位置指令平滑比", PositionReferenceHighFilterRatio, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.POSITIONLOOP, "Speed Feed Forward Ma Filter Time", "速度前馈平均滤波时间", SpeedFeedForwardMaFilterTime, ref dt);

                return dt;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.POSITIONLOOP_GET_CONFIG_TO_DATATABLE, "GetPositionLoopConfigToDataTable", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetPositionLoopConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int GetPositionLoopConfigFromDataTable(DataTable dt)
        {
            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                PositionLoopGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Position Loop Gain", "Default");
                SelectedSpdFFControlSelectIndex = OthersHelper.GetCellValueFromDataTable(dt, "Name", "SpdFF Control Select", "Default");
                SpdFFFilterFimeConstant = OthersHelper.GetCellValueFromDataTable(dt, "Name", "SpdFF Filter Fime Constant", "Default");
                SpdFFGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "SpdFF Gain", "Default");

                PositionReferenceMaFilterTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Position Reference Ma Filter Time", "Default");
                PositionReferenceHighFilterRatio = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Position Reference High Filter Ratio", "Default");
                SpeedFeedForwardMaFilterTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Feed Forward Ma Filter Time", "Default");
   
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.POSITIONLOOP_GET_CONFIG_FROM_DATATABLE, "GetPositionLoopConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                PositionLoopGain = GlobalCurrentInput.PositionLoopGain_PositionLoop;//位置环增益
                SelectedSpdFFControlSelectIndex = GlobalCurrentInput.SelectedSpdFFControlSelectIndex;//速度前馈选择
                SpdFFFilterFimeConstant = GlobalCurrentInput.SpdFFFilterFimeConstant;//速度前馈滤波时间常数
                SpdFFGain = GlobalCurrentInput.SpdFFGain;//速度前馈增益

                PositionReferenceMaFilterTime = GlobalCurrentInput.PositionReferenceMaFilterTime;
                PositionReferenceHighFilterRatio = GlobalCurrentInput.PositionReferenceHighFilterRatio;
                SpeedFeedForwardMaFilterTime = GlobalCurrentInput.SpeedFeedForwardMaFilterTime;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.POSITIONLOOP_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int AddParameterInfoDictionary(ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                dicParameterInfo.Add("Position Loop Gain", PositionLoopGain);
                dicParameterInfo.Add("SpdFF Control Select", SelectedSpdFFControlSelectIndex);
                dicParameterInfo.Add("SpdFF Filter Fime Constant", SpdFFFilterFimeConstant);
                dicParameterInfo.Add("SpdFF Gain", SpdFFGain);

                dicParameterInfo.Add("Position Reference Ma Filter Time", PositionReferenceMaFilterTime);
                dicParameterInfo.Add("Position Reference High Filter Ratio", PositionReferenceHighFilterRatio);
                dicParameterInfo.Add("Speed Feed Forward Ma Filter Time", SpeedFeedForwardMaFilterTime);

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.POSITIONLOOP_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }
}