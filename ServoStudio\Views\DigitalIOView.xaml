﻿<UserControl x:Class="ServoStudio.Views.DigitalIOView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:DigitalIOViewModel}"
             d:DesignHeight="850" d:DesignWidth="1400">
    
    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand EventName="Loaded" Command="{Binding DigitalIOLoadedCommand}"/>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields" UseWin8NotificationsIfAvailable="True"/>
    </dxmvvm:Interaction.Behaviors>

    <UserControl.Resources>
        <Style x:Key="LabelContentCenterStyle" TargetType="Label">
            <Setter Property="FontFamily" Value="Microsoft YaHei"/>
            <Setter Property="FontSize" Value="9pt"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="Margin" Value="3"/>
        </Style>

        <Style x:Key="ComboBoxCenterStyle" TargetType="dxe:ComboBoxEdit">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="IsTextEditable" Value="False"/>
            <Setter Property="Margin" Value="8,3,8,3"/>
            <Setter Property="SelectedIndex" Value="0"/>
            <Setter Property="Height" Value="27"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
        
        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="268*"/>
                <ColumnDefinition Width="268*"/>
                <ColumnDefinition Width="60"/>
                <ColumnDefinition Width="10"/>

                <ColumnDefinition Width="27*"/>
                <ColumnDefinition Width="1"/>
                <ColumnDefinition Width="27*"/>

                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="268*"/>
                <ColumnDefinition Width="268*"/>
                <ColumnDefinition Width="60"/>
                <ColumnDefinition Width="10"/>
            </Grid.ColumnDefinitions>

            <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="6" Margin="3,10" Style="{StaticResource LabelStyle}" Content="数字输入" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Label Grid.Row="1" Grid.Column="1" Content="端口号" Margin="10,0" Style="{StaticResource LabelContentCenterStyle}"/>
            <Label Grid.Row="1" Grid.Column="2" Content="逻辑选择" Margin="10,0" Style="{StaticResource LabelContentCenterStyle}"/>
            <Label Grid.Row="1" Grid.Column="3" Content="功能选择" Margin="10,0" Style="{StaticResource LabelContentCenterStyle}"/>
            <Label Grid.Row="1" Grid.Column="4" Content="状态" Margin="10,0" Style="{StaticResource LabelContentCenterStyle}"/>

            <Label Grid.Row="2" Grid.Column="1" Content="DI1" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="2" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI1Logic}" SelectedItem="{Binding SelectedDI1Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="3" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI1Function}" SelectedItem="{Binding SelectedDI1Function,Mode=TwoWay}"/>
            <Label Grid.Row="2" Grid.Column="4" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="3" Grid.Column="1" Content="DI2" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="2" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI2Logic}" SelectedItem="{Binding SelectedDI2Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="3" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI2Function}" SelectedItem="{Binding SelectedDI2Function,Mode=TwoWay}"/>
            <Label Grid.Row="3" Grid.Column="4" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="4" Grid.Column="1" Content="DI3" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="4" Grid.Column="2" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI3Logic}" SelectedItem="{Binding SelectedDI3Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="4" Grid.Column="3" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI3Function}" SelectedItem="{Binding SelectedDI3Function,Mode=TwoWay}"/>
            <Label Grid.Row="4" Grid.Column="4" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="5" Grid.Column="1" Content="DI4" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="5" Grid.Column="2" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI4Logic}" SelectedItem="{Binding SelectedDI4Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="5" Grid.Column="3" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI4Function}" SelectedItem="{Binding SelectedDI4Function,Mode=TwoWay}"/>
            <Label Grid.Row="5" Grid.Column="4" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="6" Grid.Column="1" Content="DI5" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="6" Grid.Column="2" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI5Logic}" SelectedItem="{Binding SelectedDI5Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="6" Grid.Column="3" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI5Function}" SelectedItem="{Binding SelectedDI5Function,Mode=TwoWay}"/>
            <Label Grid.Row="6" Grid.Column="4" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="7" Grid.Column="1" Content="DI6" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="7" Grid.Column="2" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI6Logic}" SelectedItem="{Binding SelectedDI6Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="7" Grid.Column="3" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DI6Function}" SelectedItem="{Binding SelectedDI6Function,Mode=TwoWay}"/>
            <Label Grid.Row="7" Grid.Column="4" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="0" Grid.Column="9" Grid.ColumnSpan="6" Margin="3,10" Style="{StaticResource LabelStyle}" Content="数字输出" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Label Grid.Row="1" Grid.Column="10" Content="端口号" Margin="10,0" Style="{StaticResource LabelContentCenterStyle}"/>
            <Label Grid.Row="1" Grid.Column="11" Content="逻辑选择" Margin="10,0" Style="{StaticResource LabelContentCenterStyle}"/>
            <Label Grid.Row="1" Grid.Column="12" Content="功能选择" Margin="10,0" Style="{StaticResource LabelContentCenterStyle}"/>
            <Label Grid.Row="1" Grid.Column="13" Content="状态" Margin="10,0" Style="{StaticResource LabelContentCenterStyle}"/>

            <Label Grid.Row="2" Grid.Column="10" Content="DO1" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="11" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO1Logic}" SelectedItem="{Binding SelectedDO1Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="12" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO1Function}" SelectedItem="{Binding SelectedDO1Function,Mode=TwoWay}"/>
            <Label Grid.Row="2" Grid.Column="13" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="3" Grid.Column="10" Content="DO2" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="11" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO2Logic}" SelectedItem="{Binding SelectedDO2Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="12" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO2Function}" SelectedItem="{Binding SelectedDO2Function,Mode=TwoWay}"/>
            <Label Grid.Row="3" Grid.Column="13" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="4" Grid.Column="10" Content="DO3" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="4" Grid.Column="11" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO3Logic}" SelectedItem="{Binding SelectedDO3Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="4" Grid.Column="12" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO3Function}" SelectedItem="{Binding SelectedDO3Function,Mode=TwoWay}"/>
            <Label Grid.Row="4" Grid.Column="13" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="5" Grid.Column="10" Content="DO4" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="5" Grid.Column="11" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO4Logic}" SelectedItem="{Binding SelectedDO4Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="5" Grid.Column="12" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO4Function}" SelectedItem="{Binding SelectedDO4Function,Mode=TwoWay}"/>
            <Label Grid.Row="5" Grid.Column="13" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="6" Grid.Column="10" Content="DO5" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="6" Grid.Column="11" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO5Logic}" SelectedItem="{Binding SelectedDO5Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="6" Grid.Column="12" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO5Function}" SelectedItem="{Binding SelectedDO5Function,Mode=TwoWay}"/>
            <Label Grid.Row="6" Grid.Column="13" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="7" Grid.Column="10" Content="DO6" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" />
            <dxe:ComboBoxEdit Grid.Row="7" Grid.Column="11" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO6Logic}" SelectedItem="{Binding SelectedDO6Logic,Mode=TwoWay}"/>
            <dxe:ComboBoxEdit Grid.Row="7" Grid.Column="12" Margin="10,9" Style="{StaticResource ComboBoxCenterStyle}" ItemsSource="{Binding DO6Function}" SelectedItem="{Binding SelectedDO6Function,Mode=TwoWay}"/>
            <Label Grid.Row="7" Grid.Column="13" Content="正常" Margin="10,9" Style="{StaticResource LabelContentCenterStyle}" Background="LightGreen"/>

            <Label Grid.Row="8" Grid.Column="0" Grid.ColumnSpan="15" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <StackPanel Grid.Row="9" Grid.Column="0" Grid.ColumnSpan="15" HorizontalAlignment="Right" Orientation="Horizontal">
                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadDigitalIOParameterCommand}">
                    <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultDigitalIOParameterCommand}">
                    <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Download_16x16.png}" Command="{Binding WriteDigitalIOParameterCommand}">
                    <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>
            </StackPanel>

            <Grid Grid.Row="11" Grid.Column="0" Grid.ColumnSpan="15">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>

                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="0" Content="1.单位设置" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="2" Content="2.限幅保护" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="4" Content="3.一般设定" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="6" Content="4.数字IO" Foreground="Gray" Style="{StaticResource LabelStyle}"/>

                <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="2" Content="2.电机反馈" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="4" Content="3.限幅保护" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="6" Content="4.一般设定" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="8" Content="5.数字IO" Style="{StaticResource LabelStyle}"/>-->

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="12" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetIOConfigFileCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="13" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2" />

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="14" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SaveIOConfigFileCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="16" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="17" Margin="20,0,5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Backward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding NormalSettingNavigationCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="18" FontSize="12" Content="上页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                <!--<dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="19" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Forward_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding OscilloscopeNavigationCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="20" FontSize="12" Content="下页" Style="{StaticResource LabelStyle}"/>-->

            </Grid>
        </Grid>
        
    </ScrollViewer>

</UserControl>
