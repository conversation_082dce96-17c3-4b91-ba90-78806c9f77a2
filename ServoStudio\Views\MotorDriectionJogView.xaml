﻿<UserControl x:Class="ServoStudio.Views.MotorDriectionJogView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking" 
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dxga="http://schemas.devexpress.com/winfx/2008/xaml/gauges"
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"  
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:converter="clr-namespace:Converter"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:MotorDriectionJogViewModel}">

    <UserControl.Resources>
        <converter:BackgroundConverter x:Key="ColorConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>
        <Style TargetType="dxe:CheckEdit">
            <Style.Triggers>
                <Trigger Property="IsChecked" Value="True">
                    <Trigger.Setters>
                        <Setter Property="Foreground" Value="Green"/>
                    </Trigger.Setters>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding JogDriectionLoadedCommand}" EventName="Loaded"/>
        <dxmvvm:EventToCommand Command="{Binding JogDriectionUnloadedCommand}" EventName="Unloaded"/>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields" UseWin8NotificationsIfAvailable="True"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0" Margin="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="6" Margin="3,5" Style="{StaticResource LabelStyle}" Content="正反转设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Label Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="2" Margin="5,4" Foreground="Red" Content="{Binding Hint_RotateDirection}" Style="{StaticResource LabelStyle}" />
                        <dxe:CheckEdit Grid.Row="1" Grid.Column="1" Margin="5,4" IsChecked="{Binding IsCCWChecked,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Content="以CCW方向为正转方向"/>
                        <dxe:CheckEdit Grid.Row="1" Grid.Column="2" Margin="5,4" IsChecked="{Binding IsCWChecked,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Content="以CW方向为正转方向"/>
                    </Grid>

                    <Label Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="8" Margin="3,5" Style="{StaticResource LabelStyle}" Content="正反转图示" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>
                    <dxe:ImageEdit Grid.Row="3" Height="115" Width="Auto" HorizontalAlignment="Left" Margin="10,8,0,3" ShowMenu="False" Source="pack://application:,,,/ServoStudio;component/Resource/Rotate.png" ShowBorder="False" Opacity="0.8"/>

                    <!--<Label Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="6" Margin="3,5" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>-->

                    <!--<StackPanel Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="6" HorizontalAlignment="Right" Orientation="Horizontal">
                    <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadNormalSettingParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                        <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                    </dx:SimpleButton>

                    <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultNormalSettingParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                        <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                    </dx:SimpleButton>

                    <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Download_16x16.png}" Command="{Binding WriteNormalSettingParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                        <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                    </dx:SimpleButton>
                </StackPanel>-->
                </Grid>
            </Grid>

            <Grid Grid.Row="1" Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="6" Margin="3,3" Style="{StaticResource LabelStyle}" Content="Jog运动操作区" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                <!--<dxe:CheckEdit Grid.Row="1" Grid.Column="1" Margin="7,4" IsChecked="{Binding IsJogModeChecked, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" Content="{Binding Hint_JogModeEnable}"/>-->

                <!--<Grid Grid.Row="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <dxe:CheckEdit Grid.Row="1" Grid.Column="1" Margin="7,4" IsChecked="{Binding IsJogIntermittentModeChecked,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Content="点动模式"/>
                <dxe:CheckEdit Grid.Row="1" Grid.Column="2" Margin="7,4" IsChecked="{Binding IsJogContinuousModeChecked,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Content="连续模式"/>
            </Grid>-->

                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,3,0,10">
                    <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Separator_16x16.png}" Command="{Binding JogDriectionSwitchCommand}" Margin="0,0,6,0" Width="95">
                        <Label Content="{Binding JogSwitchHint}" Style="{StaticResource LabelStyle}" Margin="0" />
                    </dx:SimpleButton>

                    <!--<dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Redo_16x16.png}" Command="{Binding JogCorotationRunCommand}" Margin="0,0,6,0" Width="95" IsEnabled="{Binding IsJogButtonEnabled}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding JogRunCommand}" CommandParameter="2"/>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding JogStopCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                    <Label Content="电机正转" Style="{StaticResource LabelStyle}" Margin="0" />
                </dx:SimpleButton>-->

                    <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Redo_16x16.png}" Command="{Binding JogCorotationRunCommand}" Margin="0,0,6,0" Width="95" IsEnabled="{Binding IsJogButtonEnabled}">
                        <!--<dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding JogRunCommand}" CommandParameter="2"/>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding JogStopCommand}"/>
                    </dxmvvm:Interaction.Behaviors>-->
                        <Label Content="电机正转" Style="{StaticResource LabelStyle}" Margin="0" />
                    </dx:SimpleButton>

                    <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Undo_16x16.png}" Command="{Binding JogReversalRunCommand}" Margin="0,0,6,0" Width="95" IsEnabled="{Binding IsJogButtonEnabled}">
                        <!--<dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding JogRunCommand}" CommandParameter="3"/>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding JogStopCommand}"/>
                    </dxmvvm:Interaction.Behaviors>-->
                        <Label Content="电机反转" Style="{StaticResource LabelStyle}" Margin="0" />
                    </dx:SimpleButton>

                    <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImage Image=Error_16x16.png}" Margin="0,0,6,0" Width="95" Command="{Binding JogDriectionStopCommand}" IsEnabled="{Binding IsJogButtonEnabled}">
                        <!--<dxmvvm:Interaction.Behaviors>
                        -->
                        <!--<dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding JogRunCommand}" CommandParameter="3"/>-->
                        <!--
                        <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding JogDriectionStopCommand}"/>
                    </dxmvvm:Interaction.Behaviors>-->
                        <Label Content="Jog停止" Style="{StaticResource LabelStyle}" Margin="0" />
                    </dx:SimpleButton>
                </StackPanel>

                <Border Grid.Row="3" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

                <Grid Grid.Row="4">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="50"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="180"/>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="10"/>
                    </Grid.ColumnDefinitions>

                    <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="3"  Grid.Column="1" Height="150" Width="Auto" Margin="0,8,0,2" HorizontalAlignment="Left" ShowMenu="False" Source="pack://application:,,,/ServoStudio;component/Resource/JOG.png" ShowBorder="False" Opacity="0.65"/>

                    <Label Grid.Row="0" Grid.Column="3" Margin="10,9" Content="JOG运动时间" Style="{StaticResource LabelStyle}"/>
                    <TextBox Grid.Row="0" Grid.Column="4" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding JogTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="0" Grid.Column="5" Text="0.1s" Style="{StaticResource TextBoxStyle_Unit}"/>

                    <Label Grid.Row="1" Grid.Column="3" Margin="10,9" Content="JOG速度" Style="{StaticResource LabelStyle}"/>
                    <TextBox Grid.Row="1" Grid.Column="4" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding JogSpeed,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="1" Grid.Column="5" Text="rpm" Style="{StaticResource TextBoxStyle_Unit}"/>

                    <Label Grid.Row="2" Grid.Column="3" Margin="10,9" Content="JOG加速时间" Style="{StaticResource LabelStyle}"/>
                    <TextBox Grid.Row="2" Grid.Column="4" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding JogAccelerationTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="2" Grid.Column="5" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"/>

                    <Label Grid.Row="3" Grid.Column="3" Margin="10,9" Content="JOG减速时间" Style="{StaticResource LabelStyle}"/>
                    <TextBox Grid.Row="3" Grid.Column="4" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding JogDecelerationTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="3" Grid.Column="5" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"/>

                </Grid>

                <Border Grid.Row="5" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

                <!--<StackPanel Grid.Row="6" Orientation="Horizontal" HorizontalAlignment="Right">
                <Label Style="{StaticResource LabelStyle}" Content="提示：鼠标右键长按  [电机正转/电机反转]  按钮，电机转动&#x0a;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;按键松开，随即电机停止" Margin="10,9" Foreground="Red" HorizontalContentAlignment="Right"/>
            </StackPanel>-->

            </Grid>

            <Grid Grid.Row="2" VerticalAlignment="Bottom">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>

                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="2" Content="2.电机参数" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="4" Content="3.电机反馈" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="6" Content="4.参数学习" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="8" Content="5.参数辨识" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="9" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="10" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="10" Content="6.JOG调试" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="11" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="12" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="12" Content="7.辨识整定" Foreground="Gray" Style="{StaticResource LabelStyle}"/>-->


                <dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

                <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="2" Content="2.电机参数" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>-->

                <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="2" Content="2.电机反馈" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="4" Content="3.参数学习" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="6" Content="4.参数辨识" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="8" Content="5.JOG调试" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="9" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="10" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="10" Content="6.辨识整定" Foreground="Gray" Style="{StaticResource LabelStyle}"/>

                <!--<dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="12" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=SortAsc_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding MotorLibraryNavigationCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Column="13" FontSize="12" Content="参数库" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2" />-->

                <!--<dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="14" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetMotorConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Column="15" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2" />-->

                <!--<dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="16" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SaveMotorConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Column="17" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>-->

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="18" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Backward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding MotorParameterIdentification_AddInterfaceCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="19" FontSize="12" Content="上页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="20" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Forward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding InertiaIdentificationParameterSelf_Tunning_For_AddInterfaceCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="21" FontSize="12" Content="下页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>
            </Grid>
        </Grid>

    </ScrollViewer>
   
    <!--<Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>       
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
            <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Separator_16x16.png}" Command="{Binding JogSwitchCommand}" Margin="0,0,6,0" Width="95">
                <Label Content="{Binding JogSwitchHint}" Style="{StaticResource LabelStyle}" Margin="0" />
            </dx:SimpleButton>

            <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Redo_16x16.png}" Margin="0,0,6,0" Width="95" IsEnabled="{Binding IsJogButtonEnabled}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding JogRunCommand}" CommandParameter="2"/>
                    <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding JogStopCommand}"/>
                </dxmvvm:Interaction.Behaviors>
                <Label Content="电机正转" Style="{StaticResource LabelStyle}" Margin="0" />
            </dx:SimpleButton>

            <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Undo_16x16.png}" Margin="0,0,6,0" Width="95" IsEnabled="{Binding IsJogButtonEnabled}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding JogRunCommand}" CommandParameter="3"/>
                    <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding JogStopCommand}"/>
                </dxmvvm:Interaction.Behaviors>
                <Label Content="电机反转" Style="{StaticResource LabelStyle}" Margin="0" />
            </dx:SimpleButton>
        </StackPanel>

        <Border Grid.Row="1" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="10"/>               
                <ColumnDefinition Width="Auto"/>             
                <ColumnDefinition Width="50"/>  
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="180"/>
                <ColumnDefinition Width="60"/>
                <ColumnDefinition Width="10"/>
            </Grid.ColumnDefinitions>

            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="3"  Grid.Column="1" Height="150" Width="Auto" Margin="0,8,0,2" HorizontalAlignment="Left" ShowMenu="False" Source="pack://application:,,,/ServoStudio;component/Resource/JOG.png" ShowBorder="False" Opacity="0.65"/>

            <Label Grid.Row="0" Grid.Column="3" Margin="10,9" Content="JOG速度" Style="{StaticResource LabelStyle}"/>
            <TextBox Grid.Row="0" Grid.Column="4" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding JogSpeed,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
            <TextBox Grid.Row="0" Grid.Column="5" Text="rpm" Style="{StaticResource TextBoxStyle_Unit}"/>

            <Label Grid.Row="1" Grid.Column="3" Margin="10,9" Content="JOG加速时间" Style="{StaticResource LabelStyle}"/>
            <TextBox Grid.Row="1" Grid.Column="4" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding JogAccelerationTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
            <TextBox Grid.Row="1" Grid.Column="5" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"/>

            <Label Grid.Row="2" Grid.Column="3" Margin="10,9" Content="JOG减速时间" Style="{StaticResource LabelStyle}"/>
            <TextBox Grid.Row="2" Grid.Column="4" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding JogDecelerationTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
            <TextBox Grid.Row="2" Grid.Column="5" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"/>
        </Grid>

        <Border Grid.Row="3" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right">
            <Label Style="{StaticResource LabelStyle}" Content="提示：鼠标右键长按  [电机正转/电机反转]  按钮，电机转动&#x0a;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;按键松开，随即电机停止" Margin="10,9" Foreground="Red" HorizontalContentAlignment="Right"/>
        </StackPanel>

    </Grid>-->
</UserControl>
