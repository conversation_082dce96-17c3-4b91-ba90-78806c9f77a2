﻿using Microsoft.Research.DynamicDataDisplay;
using Microsoft.Research.DynamicDataDisplay.Common;
using Microsoft.Research.DynamicDataDisplay.DataSources;
using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using ServoStudio.Models;
using ServoStudio.ViewModels;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace ServoStudio.Views
{   
    public partial class FaultDataOscilloscopeView : UserControl
    {
        #region 公有字段
        public LineGraph lineGraph_Channel1 = new LineGraph();
        public LineGraph lineGraph_Channel2 = new LineGraph();
        public LineGraph lineGraph_Channel3 = new LineGraph();
        public LineGraph lineGraph_Channel4 = new LineGraph();
        public LineGraph lineGraph_Channel5 = new LineGraph();
        public LineGraph lineGraph_Channel6 = new LineGraph();
        public LineGraph lineGraph_Channel7 = new LineGraph();
        public LineGraph lineGraph_Channel8 = new LineGraph();
        #endregion

        #region 私有字段
        private List<FaultDataChannelInfo> lstChannelInfo = null;//通道信息
        private double dX_DynamicStart = 0;//x轴动态起点坐标
        private double dY_DynamicMin = 0;//y轴动态最小数据坐标
        private double dY_DynamicMax = 0;//y轴动态最大数据坐标          
        #endregion

        #region 构造函数
        public FaultDataOscilloscopeView()
        {
            InitializeComponent();
            ViewModelSet.FaultDataOscilloscopeView = this;
           
        }
        #endregion

        #region 方法
        //*************************************************************************
        //函数名称：ClearOscilloscopeData
        //函数功能：清除示波器数据
        //
        //输入参数：NONE        
        //       
        //输出参数：0:NO_EFFECT
        //         1:SUCCEED
        //        -1:WRONG
        //
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public int ClearOscilloscopeData()
        {          
            try
            {
                dX_DynamicStart = 0;//x轴动态起点坐标
                dY_DynamicMax = 0;//y轴动态最大数据坐标

                if (lstChannelInfo != null)
                {
                    if (lstChannelInfo.Count != 0)
                    {
                        if (lstChannelInfo[1].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel1))
                                plotter.Children.Remove(lineGraph_Channel1);

                            if (plotter.Children.Contains(dataFollowChart_Channel1))
                                plotter.Children.Remove(dataFollowChart_Channel1);
                        }

                        if (lstChannelInfo[2].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel2))
                                plotter.Children.Remove(lineGraph_Channel2);

                            if (plotter.Children.Contains(dataFollowChart_Channel2))
                                plotter.Children.Remove(dataFollowChart_Channel2);
                        }

                        if (lstChannelInfo[3].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel3))
                                plotter.Children.Remove(lineGraph_Channel3);

                            if (plotter.Children.Contains(dataFollowChart_Channel3))
                                plotter.Children.Remove(dataFollowChart_Channel3);
                        }

                        if (lstChannelInfo[4].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel4))
                                plotter.Children.Remove(lineGraph_Channel4);

                            if (plotter.Children.Contains(dataFollowChart_Channel4))
                                plotter.Children.Remove(dataFollowChart_Channel4);
                        }

                        if (lstChannelInfo[5].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel5))
                                plotter.Children.Remove(lineGraph_Channel5);

                            if (plotter.Children.Contains(dataFollowChart_Channel5))
                                plotter.Children.Remove(dataFollowChart_Channel5);
                        }

                        if (lstChannelInfo[6].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel6))
                                plotter.Children.Remove(lineGraph_Channel6);

                            if (plotter.Children.Contains(dataFollowChart_Channel6))
                                plotter.Children.Remove(dataFollowChart_Channel6);
                        }

                        if (lstChannelInfo[7].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel7))
                                plotter.Children.Remove(lineGraph_Channel7);

                            if (plotter.Children.Contains(dataFollowChart_Channel7))
                                plotter.Children.Remove(dataFollowChart_Channel7);
                        }

                        if (lstChannelInfo[8].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel8))
                                plotter.Children.Remove(lineGraph_Channel8);

                            if (plotter.Children.Contains(dataFollowChart_Channel8))
                                plotter.Children.Remove(dataFollowChart_Channel8);
                        }
                    }
                }
             
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_CLEAR_DATA, "ClearOscilloscopeData", ex);
                return RET.ERROR;
            }
        }
    
        //*************************************************************************
        //函数名称：OscilloscopeDataBinding
        //函数功能：示波器数据绑定
        //
        //输入参数：NONE        
        //       
        //输出参数：0:NO_EFFECT
        //         1:SUCCEED
        //        -1:WRONG
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        private int OscilloscopeDataBinding()
        {
            int iRet = RET.NO_EFFECT;
            lstChannelInfo = new List<FaultDataChannelInfo>();

            try
            {
                if (ViewModelSet.FaultDataOscilloscope == null)
                {
                    return RET.NO_EFFECT;
                }
        
                for (int i = 0; i <= FaultDataOscilloscope.ChannelNumber; i++)
                {
                    FaultDataChannelInfo channelInfo = new FaultDataChannelInfo();
                    lstChannelInfo.Add(channelInfo);
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel1 != "停用")
                {
                    lstChannelInfo[1].IsUsed = true;

                    var ds_Channel1 = new EnumerableDataSource<FaultDataOscilloscopePoint>(lstChannelInfo[1].FaultDataOscilloscope);
                    ds_Channel1.SetXMapping(x => x.Time);
                    ds_Channel1.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel1))
                    {
                        string strDiscription = ViewModelSet.FaultDataOscilloscope?.SelectedSamplingChannel1 + " " + FaultAcquisitionInfoSet.lstUnit[0];
                        lineGraph_Channel1 = plotter.AddLineGraph(ds_Channel1, Colors.Red, 2, strDiscription);
                        lineGraph_Channel1.DataSource = ds_Channel1;                                          
                    }
                    else
                    {
                        lineGraph_Channel1.DataSource = ds_Channel1;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel1))
                    {   
                        plotter.Children.Add(dataFollowChart_Channel1);
                        dataFollowChart_Channel1.PointSource = lineGraph_Channel1;
                    }
                    else
                    {
                        dataFollowChart_Channel1.PointSource = lineGraph_Channel1;
                    }

                    iRet = RET.SUCCEEDED;
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel2 != "停用")
                {
                    lstChannelInfo[2].IsUsed = true;

                    var ds_Channel2 = new EnumerableDataSource<FaultDataOscilloscopePoint>(lstChannelInfo[2].FaultDataOscilloscope);
                    ds_Channel2.SetXMapping(x => x.Time);
                    ds_Channel2.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel2))
                    {
                        string strDiscription = ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel2 + " " + FaultAcquisitionInfoSet.lstUnit[1];                    
                        lineGraph_Channel2 = plotter.AddLineGraph(ds_Channel2, Colors.Orange, 2, strDiscription);
                        lineGraph_Channel2.DataSource = ds_Channel2;                                            
                    }
                    else
                    {
                        lineGraph_Channel2.DataSource = ds_Channel2;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel2))
                    {
                        plotter.Children.Add(dataFollowChart_Channel2);
                        dataFollowChart_Channel2.PointSource = lineGraph_Channel2;
                    }
                    else
                    {
                        dataFollowChart_Channel2.PointSource = lineGraph_Channel2;
                    }

                    iRet = RET.SUCCEEDED;
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel3 != "停用")
                {
                    lstChannelInfo[3].IsUsed = true;

                    var ds_Channel3 = new EnumerableDataSource<FaultDataOscilloscopePoint>(lstChannelInfo[3].FaultDataOscilloscope);
                    ds_Channel3.SetXMapping(x => x.Time);
                    ds_Channel3.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel3))
                    {
                        string strDiscription = ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel3 + " " + FaultAcquisitionInfoSet.lstUnit[2];
                        lineGraph_Channel3 = plotter.AddLineGraph(ds_Channel3, Colors.Blue, 2, strDiscription);
                        lineGraph_Channel3.DataSource = ds_Channel3;                      
                    }
                    else
                    {
                        lineGraph_Channel3.DataSource = ds_Channel3;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel3))
                    {
                        plotter.Children.Add(dataFollowChart_Channel3);
                        dataFollowChart_Channel3.PointSource = lineGraph_Channel3;
                    }
                    else
                    {
                        dataFollowChart_Channel3.PointSource = lineGraph_Channel3;
                    }

                    iRet = RET.SUCCEEDED;
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel4 != "停用")
                {
                    lstChannelInfo[4].IsUsed = true;

                    var ds_Channel4 = new EnumerableDataSource<FaultDataOscilloscopePoint>(lstChannelInfo[4].FaultDataOscilloscope);
                    ds_Channel4.SetXMapping(x => x.Time);
                    ds_Channel4.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel4))
                    {
                        string strDiscription = ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel4 + " " + FaultAcquisitionInfoSet.lstUnit[3];                   
                        lineGraph_Channel4 = plotter.AddLineGraph(ds_Channel4, Colors.Green, 2, strDiscription);
                        lineGraph_Channel4.DataSource = ds_Channel4;                             
                    }
                    else
                    {
                        lineGraph_Channel4.DataSource = ds_Channel4;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel4))
                    {
                        plotter.Children.Add(dataFollowChart_Channel4);
                        dataFollowChart_Channel4.PointSource = lineGraph_Channel4;
                    }
                    else
                    {
                        dataFollowChart_Channel4.PointSource = lineGraph_Channel4;
                    }

                    iRet = RET.SUCCEEDED;
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel5 != "停用")
                {
                    lstChannelInfo[5].IsUsed = true;

                    var ds_Channel5 = new EnumerableDataSource<FaultDataOscilloscopePoint>(lstChannelInfo[5].FaultDataOscilloscope);
                    ds_Channel5.SetXMapping(x => x.Time);
                    ds_Channel5.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel5))
                    {
                        string strDiscription = ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel5 + " " + FaultAcquisitionInfoSet.lstUnit[4];
                        lineGraph_Channel5 = plotter.AddLineGraph(ds_Channel5, Colors.Black, 2, strDiscription);
                        lineGraph_Channel5.DataSource = ds_Channel5;
                    }
                    else
                    {
                        lineGraph_Channel5.DataSource = ds_Channel5;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel5))
                    {
                        plotter.Children.Add(dataFollowChart_Channel5);
                        dataFollowChart_Channel5.PointSource = lineGraph_Channel5;
                    }
                    else
                    {
                        dataFollowChart_Channel5.PointSource = lineGraph_Channel5;
                    }

                    iRet = RET.SUCCEEDED;
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel6 != "停用")
                {
                    lstChannelInfo[6].IsUsed = true;

                    var ds_Channel6 = new EnumerableDataSource<FaultDataOscilloscopePoint>(lstChannelInfo[6].FaultDataOscilloscope);
                    ds_Channel6.SetXMapping(x => x.Time);
                    ds_Channel6.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel6))
                    {
                        string strDiscription = ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel6 + " " + FaultAcquisitionInfoSet.lstUnit[5];
                        lineGraph_Channel6 = plotter.AddLineGraph(ds_Channel6, Colors.Purple, 2, strDiscription);
                        lineGraph_Channel6.DataSource = ds_Channel6;
                    }
                    else
                    {
                        lineGraph_Channel6.DataSource = ds_Channel6;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel6))
                    {
                        plotter.Children.Add(dataFollowChart_Channel6);
                        dataFollowChart_Channel6.PointSource = lineGraph_Channel6;
                    }
                    else
                    {
                        dataFollowChart_Channel6.PointSource = lineGraph_Channel6;
                    }

                    iRet = RET.SUCCEEDED;
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel7 != "停用")
                {
                    lstChannelInfo[7].IsUsed = true;

                    var ds_Channel7 = new EnumerableDataSource<FaultDataOscilloscopePoint>(lstChannelInfo[7].FaultDataOscilloscope);
                    ds_Channel7.SetXMapping(x => x.Time);
                    ds_Channel7.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel7))
                    {
                        string strDiscription = ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel7 + " " + FaultAcquisitionInfoSet.lstUnit[6];
                        lineGraph_Channel7 = plotter.AddLineGraph(ds_Channel7, Colors.Brown, 2, strDiscription);
                        lineGraph_Channel7.DataSource = ds_Channel7;
                    }
                    else
                    {
                        lineGraph_Channel7.DataSource = ds_Channel7;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel7))
                    {
                        plotter.Children.Add(dataFollowChart_Channel7);
                        dataFollowChart_Channel7.PointSource = lineGraph_Channel7;
                    }
                    else
                    {
                        dataFollowChart_Channel7.PointSource = lineGraph_Channel7;
                    }

                    iRet = RET.SUCCEEDED;
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel8 != "停用")
                {
                    lstChannelInfo[8].IsUsed = true;

                    var ds_Channel8 = new EnumerableDataSource<FaultDataOscilloscopePoint>(lstChannelInfo[8].FaultDataOscilloscope);
                    ds_Channel8.SetXMapping(x => x.Time);
                    ds_Channel8.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel8))
                    {
                        string strDiscription = ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel8 + " " + FaultAcquisitionInfoSet.lstUnit[7];
                        lineGraph_Channel8 = plotter.AddLineGraph(ds_Channel8, Colors.Coral, 2, strDiscription);
                        lineGraph_Channel8.DataSource = ds_Channel8;
                    }
                    else
                    {
                        lineGraph_Channel8.DataSource = ds_Channel8;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel8))
                    {
                        plotter.Children.Add(dataFollowChart_Channel8);
                        dataFollowChart_Channel8.PointSource = lineGraph_Channel8;
                    }
                    else
                    {
                        dataFollowChart_Channel8.PointSource = lineGraph_Channel8;
                    }

                    iRet = RET.SUCCEEDED;
                }
            }
            catch (System.Exception ex)
            {
                iRet = RET.ERROR;
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_DATA_BINDING, "OscilloscopeDataBinding", ex);
            }

            return iRet;
        }             

        //*************************************************************************
        //函数名称：DisplayOscilloscope
        //函数功能：示波器波形显示
        //
        //输入参数：NONE        
        //       
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public void DisplayOscilloscope()
        {
            int iRet = -1;           
            Int64 iYMin = 0;
            Int64 iYMax = 0;
            double AcquisitionPeriod = 0;
            double AcquisitionDuration = 0;
           
            try
            {
                //获取采样周期与时长
                AcquisitionPeriod = OthersHelper.GetAcquisitionPeriod_ForFaultData(ViewModelSet.FaultDataOscilloscope?.SamplingPeriod);
                AcquisitionDuration = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope?.SamplingDuration.Replace("μs", "").Replace("ms", ""));

                this.plotter.Dispatcher.BeginInvoke(new Action(() =>
                {
                    //没有完成画图
                    FaultAcquisitionInfoSet.IsDrawingCompleted = false;
                                     
                    //赋值
                    for (int iPoints = 0; iPoints < FaultAcquisitionData.Channel1.Count; iPoints++)
                    {                       
                        //数据初始化与数据绑定
                        if (iPoints == 0)
                        {
                            ClearOscilloscopeData();
                            OscilloscopeDataBinding();
                        }

                        //通道数据赋值
                        if (lstChannelInfo[1].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden && FaultAcquisitionData.Channel1.Count != 0)    //Lilbert添加 && AcquisitionData.Channel1.Count != 0 保护代码不崩溃
                        {                            
                                lstChannelInfo[1].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[1].YValue = FaultAcquisitionData.Channel1[iPoints] * FaultAcquisitionInfoSet.lstExchangeValue[0] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling);
                                lstChannelInfo[1].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[1].XValue, lstChannelInfo[1].YValue));                                                                                                                                              
                        }

                        if (lstChannelInfo[2].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden && FaultAcquisitionData.Channel2.Count != 0)    //Lilbert添加 && AcquisitionData.Channel2.Count != 0 保护代码不崩溃
                        {
                            if (FaultAcquisitionData.Channel2.Count > iPoints)          //由Lilbert添加if (AcquisitionData.Channel2.Count > iPoints)保护代码不崩溃
                            {
                                lstChannelInfo[2].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[2].YValue = FaultAcquisitionData.Channel2[iPoints] * FaultAcquisitionInfoSet.lstExchangeValue[1] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling);
                                lstChannelInfo[2].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[2].XValue, lstChannelInfo[2].YValue));
                            }                                                     
                        }

                        if (lstChannelInfo[3].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden && FaultAcquisitionData.Channel3.Count != 0)    //Lilbert添加 && AcquisitionData.Channel3.Count != 0 保护代码不崩溃
                        {
                            if (FaultAcquisitionData.Channel3.Count > iPoints)          //由Lilbert添加if (AcquisitionData.Channel3.Count > iPoints)保护代码不崩溃
                            {
                                lstChannelInfo[3].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[3].YValue = FaultAcquisitionData.Channel3[iPoints] * FaultAcquisitionInfoSet.lstExchangeValue[2] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling);
                                lstChannelInfo[3].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[3].XValue, lstChannelInfo[3].YValue));
                            }                                                      
                        }

                        if (lstChannelInfo[4].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden && FaultAcquisitionData.Channel4.Count != 0)    //Lilbert添加 && AcquisitionData.Channel4.Count != 0 保护代码不崩溃
                        {
                            if (FaultAcquisitionData.Channel4.Count > iPoints)          //由Lilbert添加if (AcquisitionData.Channel4.Count > iPoints)保护代码不崩溃
                            {
                                lstChannelInfo[4].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[4].YValue = FaultAcquisitionData.Channel4[iPoints] * FaultAcquisitionInfoSet.lstExchangeValue[3] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling);
                                lstChannelInfo[4].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[4].XValue, lstChannelInfo[4].YValue));
                            }                                                       
                        }

                        if (lstChannelInfo[5].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden && FaultAcquisitionData.Channel5.Count != 0)    //Lilbert添加 && AcquisitionData.Channel4.Count != 0 保护代码不崩溃
                        {
                            if (FaultAcquisitionData.Channel5.Count > iPoints)          //由Lilbert添加if (AcquisitionData.Channel4.Count > iPoints)保护代码不崩溃
                            {
                                lstChannelInfo[5].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[5].YValue = FaultAcquisitionData.Channel5[iPoints] * FaultAcquisitionInfoSet.lstExchangeValue[4] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling);
                                lstChannelInfo[5].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[5].XValue, lstChannelInfo[5].YValue));
                            }
                        }

                        if (lstChannelInfo[6].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden && FaultAcquisitionData.Channel6.Count != 0)    //Lilbert添加 && AcquisitionData.Channel4.Count != 0 保护代码不崩溃
                        {
                            if (FaultAcquisitionData.Channel6.Count > iPoints)          //由Lilbert添加if (AcquisitionData.Channel4.Count > iPoints)保护代码不崩溃
                            {
                                lstChannelInfo[6].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[6].YValue = FaultAcquisitionData.Channel6[iPoints] * FaultAcquisitionInfoSet.lstExchangeValue[5] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling);
                                lstChannelInfo[6].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[6].XValue, lstChannelInfo[6].YValue));
                            }
                        }

                        if (lstChannelInfo[7].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden && FaultAcquisitionData.Channel7.Count != 0)    //Lilbert添加 && AcquisitionData.Channel4.Count != 0 保护代码不崩溃
                        {
                            if (FaultAcquisitionData.Channel7.Count > iPoints)          //由Lilbert添加if (AcquisitionData.Channel4.Count > iPoints)保护代码不崩溃
                            {
                                lstChannelInfo[7].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[7].YValue = FaultAcquisitionData.Channel7[iPoints] * FaultAcquisitionInfoSet.lstExchangeValue[6] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling);
                                lstChannelInfo[7].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[7].XValue, lstChannelInfo[7].YValue));
                            }
                        }

                        if (lstChannelInfo[8].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden && FaultAcquisitionData.Channel8.Count != 0)    //Lilbert添加 && AcquisitionData.Channel4.Count != 0 保护代码不崩溃
                        {
                            if (FaultAcquisitionData.Channel8.Count > iPoints)          //由Lilbert添加if (AcquisitionData.Channel4.Count > iPoints)保护代码不崩溃
                            {
                                lstChannelInfo[8].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[8].YValue = FaultAcquisitionData.Channel8[iPoints] * FaultAcquisitionInfoSet.lstExchangeValue[7] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling);
                                lstChannelInfo[8].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[8].XValue, lstChannelInfo[8].YValue));
                            }
                        }
                    }

                    //画图 
                    iRet = FaultDataOscilloscopeModel.StaticYMaxminPoint(lstChannelInfo, ref iYMax, ref iYMin);
                    if (iRet == RET.SUCCEEDED)
                    {
                        plotter.Viewport.Visible = new System.Windows.Rect(-FaultDataOscilloscope.Vacant, iYMin - FaultDataOscilloscope.Vacant, AcquisitionDuration + FaultDataOscilloscope.Vacant * 2, iYMax - iYMin + FaultDataOscilloscope.Vacant * 2);
                    }

                    //完成画图
                    FaultAcquisitionInfoSet.IsDrawingCompleted = true;                 
                }));

                //记录本次采集数据
                FaultDataOscilloscopeModel.EvaluateLastWaveDataFromCurrent();

                //可以单位转换
                ViewModelSet.Main.IsUnitExchangedEnabled = true;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_ACQUISITION_DATA_DISPLAY, "DisplayOscilloscope", ex);        
            }
        }

        //*************************************************************************
        //函数名称：DisplayOscilloscopeLoop
        //函数功能：示波器波形循环
        //
        //输入参数：NONE        
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.01.01
        //*************************************************************************
        public void DisplayOscilloscopeLoop()
        {
            try
            {
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadSwitch = true;
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadPause = false;
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadDispose = false;
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadWorking = true;
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes = 0;
                ThreadPool.QueueUserWorkItem(PthreadStatement.MicrosecondsOscilloscopeDrawing.Run, null);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_DISPLAY_OSCILLOSCOPE_LOOP, "DisplayOscilloscopeLoop", ex);
            }
        }

        //*************************************************************************
        //函数名称：TimerCallBack
        //函数功能：时钟回调
        //
        //输入参数：NONE        
        //       
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public void TimerCallBack(object sender, long JumpPeriod, long interval)
        {
            double AcquisitionPeriod = 0;
            double Channel1Exchange = 1;
            double Channel2Exchange = 1;
            double Channel3Exchange = 1;
            double Channel4Exchange = 1;
            double Channel5Exchange = 1;
            double Channel6Exchange = 1;
            double Channel7Exchange = 1;
            double Channel8Exchange = 1;

            double Channel1Doubling = 1;
            double Channel2Doubling = 1;
            double Channel3Doubling = 1;
            double Channel4Doubling = 1;
            double Channel5Doubling = 1;
            double Channel6Doubling = 1;
            double Channel7Doubling = 1;
            double Channel8Doubling = 1;

            try
            {
                for (int i = 0; i < FaultAcquisitionInfoSet.lstExchangeValue.Count; i++)
                {
                    switch (i)
                    {
                        case 0:
                            Channel1Exchange = FaultAcquisitionInfoSet.lstExchangeValue[i];
                            Channel1Doubling = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        case 1:
                            Channel2Exchange = FaultAcquisitionInfoSet.lstExchangeValue[i];
                            Channel2Doubling = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        case 2:
                            Channel3Exchange = FaultAcquisitionInfoSet.lstExchangeValue[i];
                            Channel3Doubling = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        case 3:
                            Channel4Exchange = FaultAcquisitionInfoSet.lstExchangeValue[i];
                            Channel4Doubling = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        case 4:
                            Channel5Exchange = FaultAcquisitionInfoSet.lstExchangeValue[i];
                            Channel5Doubling = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        case 5:
                            Channel6Exchange = FaultAcquisitionInfoSet.lstExchangeValue[i];
                            Channel6Doubling = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        case 6:
                            Channel7Exchange = FaultAcquisitionInfoSet.lstExchangeValue[i];
                            Channel7Doubling = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        case 7:
                            Channel8Exchange = FaultAcquisitionInfoSet.lstExchangeValue[i];
                            Channel8Doubling = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        default:
                            break;
                    }
                }
                
                //更新前台数据               
                this.plotter.Dispatcher.BeginInvoke(new Action(() =>
                {
                    //没有完成画图
                    FaultAcquisitionInfoSet.IsDrawingCompleted = false;

                    //获取采样周期
                    AcquisitionPeriod = OthersHelper.GetAcquisitionPeriod_ForFaultData(ViewModelSet.FaultDataOscilloscope.SamplingPeriod);

                    //初始化
                    if (PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes == 0)
                    {
                        ClearOscilloscopeData();
                        OscilloscopeDataBinding();
                    }

                    //通道赋值        
                    if (lstChannelInfo[1].IsUsed == true && FaultAcquisitionData.Channel1.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {
                        lstChannelInfo[1].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[1].YValue = FaultAcquisitionData.Channel1[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel1Exchange * Channel1Doubling;
                    }

                    if (lstChannelInfo[2].IsUsed == true && FaultAcquisitionData.Channel2.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {

                        lstChannelInfo[2].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[2].YValue = FaultAcquisitionData.Channel2[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel2Exchange * Channel2Doubling;
                    }

                    if (lstChannelInfo[3].IsUsed == true && FaultAcquisitionData.Channel3.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {
                        lstChannelInfo[3].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[3].YValue = FaultAcquisitionData.Channel3[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel3Exchange * Channel3Doubling;
                    }

                    if (lstChannelInfo[4].IsUsed == true && FaultAcquisitionData.Channel4.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {
                        lstChannelInfo[4].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[4].YValue = FaultAcquisitionData.Channel4[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel4Exchange * Channel4Doubling;
                    }

                    if (lstChannelInfo[5].IsUsed == true && FaultAcquisitionData.Channel5.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {
                        lstChannelInfo[5].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[5].YValue = FaultAcquisitionData.Channel5[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel5Exchange * Channel5Doubling;
                    }

                    if (lstChannelInfo[6].IsUsed == true && FaultAcquisitionData.Channel6.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {
                        lstChannelInfo[6].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[6].YValue = FaultAcquisitionData.Channel6[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel6Exchange * Channel6Doubling;
                    }

                    if (lstChannelInfo[7].IsUsed == true && FaultAcquisitionData.Channel7.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {
                        lstChannelInfo[7].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[7].YValue = FaultAcquisitionData.Channel7[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel7Exchange * Channel7Doubling;
                    }

                    if (lstChannelInfo[8].IsUsed == true && FaultAcquisitionData.Channel8.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {
                        lstChannelInfo[8].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[8].YValue = FaultAcquisitionData.Channel8[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel8Exchange * Channel8Doubling;
                    }

                    PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes++;

                    //获取动态坐标数据
                    FaultDataOscilloscopeModel.DynamicXStartPoint(lstChannelInfo, ref dX_DynamicStart);
                    FaultDataOscilloscopeModel.DynamicYMaxPoint(lstChannelInfo, ref dY_DynamicMax, ref dY_DynamicMin);

                    //示波器赋值
                    for (int iChannelNumber = 1; iChannelNumber <= FaultDataOscilloscope.ChannelNumber; iChannelNumber++)
                    {
                        if (lstChannelInfo[iChannelNumber].IsUsed == true)
                        {
                            double x = lstChannelInfo[iChannelNumber].XValue;
                            double y = lstChannelInfo[iChannelNumber].YValue;
                            lstChannelInfo[iChannelNumber].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(x, y));
                        }
                    }

                    //画图
                    plotter.Viewport.Visible = new System.Windows.Rect(dX_DynamicStart, dY_DynamicMin, FaultDataOscilloscope.DynamicDisplayPoint * OthersHelper.GetAcquisitionPeriod(ViewModelSet.FaultDataOscilloscope.SamplingPeriod), dY_DynamicMax - dY_DynamicMin);
                }));
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("TimerCallBack", ex);
            }
        }

        //*************************************************************************
        //函数名称：DisplayOscilloscopeImport
        //函数功能：导入的波形展示
        //
        //输入参数：NONE        
        //       
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public void DisplayOscilloscopeImport()
        {
            int iRet = -1;
            double AcquisitionPeriod = 0;
            double AcquisitionDuration = 0;
            Int64 iYMin = 0;
            Int64 iYMax = 0;

            try
            {
                //获取采样周期与时长
                AcquisitionPeriod = OthersHelper.GetAcquisitionPeriod_ForFaultData(ViewModelSet.FaultDataOscilloscope?.SamplingPeriod);
                AcquisitionDuration = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope?.SamplingDuration.Replace("μs", "").Replace("ms", ""));

                //清除数据
                iRet = ClearOscilloscopeData();
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //数据绑定
                iRet = OscilloscopeDataBinding();
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //数据展示
                for (int i = 0; i < FaultAcquisitionData.Channel1.Count; i++)
                {                   
                    if (lstChannelInfo[1].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                    {
                        lstChannelInfo[1].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.FaultDataOscilloscope.SamplingPeriod);
                        lstChannelInfo[1].YValue = FaultAcquisitionData.Channel1[i] * FaultAcquisitionInfoSet.lstExchangeValue[0] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling);
                        lstChannelInfo[1].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[1].XValue, lstChannelInfo[1].YValue));                      
                    }

                    if (lstChannelInfo[2].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                    {
                        lstChannelInfo[2].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.FaultDataOscilloscope.SamplingPeriod);
                        lstChannelInfo[2].YValue = FaultAcquisitionData.Channel2[i] * FaultAcquisitionInfoSet.lstExchangeValue[1] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling);
                        lstChannelInfo[2].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[2].XValue, lstChannelInfo[2].YValue));                    
                    }

                    if (lstChannelInfo[3].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                    {
                        lstChannelInfo[3].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.FaultDataOscilloscope.SamplingPeriod);
                        lstChannelInfo[3].YValue = FaultAcquisitionData.Channel3[i] * FaultAcquisitionInfoSet.lstExchangeValue[2] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling);
                        lstChannelInfo[3].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[3].XValue, lstChannelInfo[3].YValue));                     
                    }

                    if (lstChannelInfo[4].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                    {
                        lstChannelInfo[4].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.FaultDataOscilloscope.SamplingPeriod);
                        lstChannelInfo[4].YValue = FaultAcquisitionData.Channel4[i] * FaultAcquisitionInfoSet.lstExchangeValue[3] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling);
                        lstChannelInfo[4].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[4].XValue, lstChannelInfo[4].YValue));                        
                    }

                    if (lstChannelInfo[5].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                    {
                        lstChannelInfo[5].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.FaultDataOscilloscope.SamplingPeriod);
                        lstChannelInfo[5].YValue = FaultAcquisitionData.Channel5[i] * FaultAcquisitionInfoSet.lstExchangeValue[4] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling);
                        lstChannelInfo[5].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[5].XValue, lstChannelInfo[5].YValue));
                    }

                    if (lstChannelInfo[6].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                    {
                        lstChannelInfo[6].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.FaultDataOscilloscope.SamplingPeriod);
                        lstChannelInfo[6].YValue = FaultAcquisitionData.Channel6[i] * FaultAcquisitionInfoSet.lstExchangeValue[5] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling);
                        lstChannelInfo[6].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[6].XValue, lstChannelInfo[6].YValue));
                    }

                    if (lstChannelInfo[7].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                    {
                        lstChannelInfo[7].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.FaultDataOscilloscope.SamplingPeriod);
                        lstChannelInfo[7].YValue = FaultAcquisitionData.Channel7[i] * FaultAcquisitionInfoSet.lstExchangeValue[6] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling);
                        lstChannelInfo[7].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[7].XValue, lstChannelInfo[7].YValue));
                    }

                    if (lstChannelInfo[8].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                    {
                        lstChannelInfo[8].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.FaultDataOscilloscope.SamplingPeriod);
                        lstChannelInfo[8].YValue = FaultAcquisitionData.Channel8[i] * FaultAcquisitionInfoSet.lstExchangeValue[7] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling);
                        lstChannelInfo[8].FaultDataOscilloscope.Add(new FaultDataOscilloscopePoint(lstChannelInfo[8].XValue, lstChannelInfo[8].YValue));
                    }
                }

                //画图 
                iRet = FaultDataOscilloscopeModel.StaticYMaxminPoint(lstChannelInfo, ref iYMax, ref iYMin);
                if (iRet == RET.SUCCEEDED)
                {
                    plotter.Viewport.Visible = new System.Windows.Rect(-FaultDataOscilloscope.Vacant, iYMin - FaultDataOscilloscope.Vacant, AcquisitionDuration + FaultDataOscilloscope.Vacant * 2, iYMax - iYMin + FaultDataOscilloscope.Vacant * 2);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_TURN_ON, "DisplayOscilloscopeImport", ex);
            }
        }

        //*************************************************************************
        //函数名称：plotter_MouseLeftButtonUp
        //函数功能：计算
        //
        //输入参数：NONE        
        //       
        //输出参数：NONE
        //
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************              
        private void plotter_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            string strIndex = null;
            string strTime = null;

            double dCH1Value = 0;
            double dCH2Value = 0;
            double dCH3Value = 0;
            double dCH4Value = 0;
            double dCH5Value = 0;
            double dCH6Value = 0;
            double dCH7Value = 0;
            double dCH8Value = 0;

            int[] arrMax = new int[8];
            int[] arrMin = new int[8];
            double[] arrAverage = new double[8];
            double[] arrRMS = new double[8];
            FaultDataOsilloscopeCalculateSet clsData = null;

            int iMistake = 0;
            try
            {
                #region 获取当前数据
                if (ViewModelSet.FaultDataOscilloscope == null || lstChannelInfo == null)
                {
                    return;
                }

                ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate = new ObservableCollection<FaultDataOsilloscopeCalculateSet>();          
                if (lstChannelInfo[1].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel1.MarkerPosition.X, 2));
                    strIndex = FaultDataOscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel1.MarkerPosition.X, 2));
                }
                else if (lstChannelInfo[2].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel2.MarkerPosition.X, 2));
                    strIndex = FaultDataOscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel2.MarkerPosition.X, 2));
                }
                else if (lstChannelInfo[3].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel3.MarkerPosition.X, 2));
                    strIndex = FaultDataOscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel3.MarkerPosition.X, 2));
                }
                else if (lstChannelInfo[4].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel4.MarkerPosition.X, 2));
                    strIndex = FaultDataOscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel4.MarkerPosition.X, 2));
                }
                else if (lstChannelInfo[5].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel5.MarkerPosition.X, 2));
                    strIndex = FaultDataOscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel5.MarkerPosition.X, 2));
                }
                else if (lstChannelInfo[6].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel6.MarkerPosition.X, 2));
                    strIndex = FaultDataOscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel6.MarkerPosition.X, 2));
                }
                else if (lstChannelInfo[7].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel7.MarkerPosition.X, 2));
                    strIndex = FaultDataOscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel7.MarkerPosition.X, 2));
                }
                else if (lstChannelInfo[8].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel8.MarkerPosition.X, 2));
                    strIndex = FaultDataOscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel8.MarkerPosition.X, 2));
                }
                else
                {
                    for (int i = 0; i <= 7; i++)
                    {
                        FaultDataOsilloscopeCalculateSet clsTemp = new FaultDataOsilloscopeCalculateSet();
                        switch (i)
                        {
                            case 0:
                                clsTemp.Title = "当前光标";
                                break;
                            case 1:
                                clsTemp.Title = "上一个光标";
                                break;
                            case 2:
                                clsTemp.Title = "光标间距";
                                break;
                            case 3:
                                clsTemp.Title = "区间最大值";
                                break;
                            case 4:
                                clsTemp.Title = "区间最小值";
                                break;
                            case 5:
                                clsTemp.Title = "区间峰峰值";
                                break;
                            case 6:
                                clsTemp.Title = "区间平均值";
                                break;
                            case 7:
                                clsTemp.Title = "区间均方根";
                                break;
                            default:
                                break;
                        }

                        ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate.Add(clsTemp);
                    }

                    return;
                }
             
                if (lstChannelInfo[1].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    dCH1Value = Math.Round(dataFollowChart_Channel1.MarkerPosition.Y, 2);
                }              

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    dCH2Value = Math.Round(dataFollowChart_Channel2.MarkerPosition.Y, 2);
                }
                
                if (lstChannelInfo[3].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    dCH3Value = Math.Round(dataFollowChart_Channel3.MarkerPosition.Y, 2);
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    dCH4Value = Math.Round(dataFollowChart_Channel4.MarkerPosition.Y, 2);
                }

                if (lstChannelInfo[5].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                {
                    dCH5Value = Math.Round(dataFollowChart_Channel5.MarkerPosition.Y, 2);
                }

                if (lstChannelInfo[6].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                {
                    dCH6Value = Math.Round(dataFollowChart_Channel6.MarkerPosition.Y, 2);
                }

                if (lstChannelInfo[7].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                {
                    dCH7Value = Math.Round(dataFollowChart_Channel7.MarkerPosition.Y, 2);
                }

                if (lstChannelInfo[8].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                {
                    dCH8Value = Math.Round(dataFollowChart_Channel8.MarkerPosition.Y, 2);
                }

                clsData = FaultDataOscilloscopeModel.AddCalculateSet("当前光标", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value, dCH5Value, dCH6Value, dCH7Value, dCH8Value);
                ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 获取上一条数据
                iMistake = 1;
                strTime = Convert.ToString(ViewModelSet.FaultDataOscilloscope.dLastTime);
                strIndex = Convert.ToString(ViewModelSet.FaultDataOscilloscope.iIndex);
                clsData = FaultDataOscilloscopeModel.AddCalculateSet("上一个光标", strIndex, strTime, ViewModelSet.FaultDataOscilloscope.dLastCH1Value, ViewModelSet.FaultDataOscilloscope.dLastCH2Value, ViewModelSet.FaultDataOscilloscope.dLastCH3Value, ViewModelSet.FaultDataOscilloscope.dLastCH4Value, ViewModelSet.FaultDataOscilloscope.dLastCH5Value, ViewModelSet.FaultDataOscilloscope.dLastCH6Value, ViewModelSet.FaultDataOscilloscope.dLastCH7Value, ViewModelSet.FaultDataOscilloscope.dLastCH8Value);
                ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate.Add(clsData);

                ViewModelSet.FaultDataOscilloscope.dLastTime = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].AcquisitionTime);
                ViewModelSet.FaultDataOscilloscope.iIndex = Convert.ToInt32(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Number);
                ViewModelSet.FaultDataOscilloscope.dLastCH1Value = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel1Value);
                ViewModelSet.FaultDataOscilloscope.dLastCH2Value = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel2Value);
                ViewModelSet.FaultDataOscilloscope.dLastCH3Value = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel3Value);
                ViewModelSet.FaultDataOscilloscope.dLastCH4Value = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel4Value);
                ViewModelSet.FaultDataOscilloscope.dLastCH5Value = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel5Value);
                ViewModelSet.FaultDataOscilloscope.dLastCH6Value = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel6Value);
                ViewModelSet.FaultDataOscilloscope.dLastCH7Value = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel7Value);
                ViewModelSet.FaultDataOscilloscope.dLastCH8Value = Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel8Value);
                #endregion

                #region 光标距离
                iMistake = 2;
                strTime = Convert.ToString(Math.Round(Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].AcquisitionTime) - Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].AcquisitionTime), 2));
                strIndex = Convert.ToString(Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Number) - Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Number));

                if (lstChannelInfo[1].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    dCH1Value = Math.Round(Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel1Value) - Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Channel1Value), 2);
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    dCH2Value = Math.Round(Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel2Value) - Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Channel2Value), 2);
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    dCH3Value = Math.Round(Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel3Value) - Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Channel3Value), 2);
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    dCH4Value = Math.Round(Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel4Value) - Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Channel4Value), 2);
                }

                if (lstChannelInfo[5].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                {
                    dCH5Value = Math.Round(Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel5Value) - Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Channel5Value), 2);
                }

                if (lstChannelInfo[6].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                {
                    dCH6Value = Math.Round(Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel6Value) - Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Channel6Value), 2);
                }

                if (lstChannelInfo[7].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                {
                    dCH7Value = Math.Round(Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel7Value) - Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Channel7Value), 2);
                }

                if (lstChannelInfo[8].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                {
                    dCH8Value = Math.Round(Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Channel8Value) - Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Channel8Value), 2);
                }

                clsData = FaultDataOscilloscopeModel.AddCalculateSet("光标间距", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value, dCH5Value, dCH6Value, dCH7Value, dCH8Value);
                ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 最大值
                iMistake = 3;
                arrMax = FaultDataOscilloscopeModel.GetMaxminOfArrayWaveData(true, ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Number, ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Number);

                if (lstChannelInfo[1].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH1Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH1Value = arrMax[0];
                        }
                        else
                        {
                            dCH1Value = Math.Round(arrMax[0] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[0], 2);
                        }
                    }                                  
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH2Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH2Value = arrMax[1];
                        }
                        else
                        {
                            dCH2Value = Math.Round(arrMax[1] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[1], 2);
                        }
                    }                                  
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH3Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH3Value = arrMax[2];
                        }
                        else
                        {
                            dCH3Value = Math.Round(arrMax[2] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[2], 2);
                        }
                    }                                 
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH4Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH4Value = arrMax[3];
                        }
                        else
                        {
                            dCH4Value = Math.Round(arrMax[3] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[3], 2);
                        }
                    }          
                }

                if (lstChannelInfo[5].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH5Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH5Value = arrMax[4];
                        }
                        else
                        {
                            dCH5Value = Math.Round(arrMax[4] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[4], 2);
                        }
                    }
                }

                if (lstChannelInfo[6].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH6Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH6Value = arrMax[5];
                        }
                        else
                        {
                            dCH6Value = Math.Round(arrMax[5] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[5], 2);
                        }
                    }
                }

                if (lstChannelInfo[7].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH7Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH7Value = arrMax[6];
                        }
                        else
                        {
                            dCH7Value = Math.Round(arrMax[6] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[6], 2);
                        }
                    }
                }

                if (lstChannelInfo[8].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH8Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH8Value = arrMax[7];
                        }
                        else
                        {
                            dCH8Value = Math.Round(arrMax[7] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[7], 2);
                        }
                    }
                }

                clsData = FaultDataOscilloscopeModel.AddCalculateSet("区间最大值", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value, dCH5Value, dCH6Value, dCH7Value, dCH8Value);
                ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 最小值
                iMistake = 4;
                arrMin = FaultDataOscilloscopeModel.GetMaxminOfArrayWaveData(false, ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Number, ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Number);

                if (lstChannelInfo[1].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH1Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH1Value = arrMin[0];
                        }
                        else
                        {
                            dCH1Value = Math.Round(arrMin[0] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[0], 2);
                        }
                    }                       
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH2Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH2Value = arrMin[1];
                        }
                        else
                        {
                            dCH2Value = Math.Round(arrMin[1] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[1], 2);
                        }
                    }       
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH3Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH3Value = arrMin[2];
                        }
                        else
                        {
                            dCH3Value = Math.Round(arrMin[2] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[2], 2);
                        }
                    }   
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH4Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH4Value = arrMin[3];
                        }
                        else
                        {
                            dCH4Value = Math.Round(arrMin[3] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[3], 2);
                        }
                    }   
                }

                if (lstChannelInfo[5].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH5Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH5Value = arrMin[4];
                        }
                        else
                        {
                            dCH5Value = Math.Round(arrMin[4] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[4], 2);
                        }
                    }
                }

                if (lstChannelInfo[6].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH6Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH6Value = arrMin[5];
                        }
                        else
                        {
                            dCH6Value = Math.Round(arrMin[5] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[5], 2);
                        }
                    }
                }

                if (lstChannelInfo[7].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH7Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH7Value = arrMin[6];
                        }
                        else
                        {
                            dCH7Value = Math.Round(arrMin[6] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[6], 2);
                        }
                    }
                }

                if (lstChannelInfo[8].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH8Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH8Value = arrMin[7];
                        }
                        else
                        {
                            dCH8Value = Math.Round(arrMin[7] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[7], 2);
                        }
                    }
                }

                clsData = FaultDataOscilloscopeModel.AddCalculateSet("区间最小值", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value, dCH5Value, dCH6Value, dCH7Value, dCH8Value);
                ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 峰峰值
                iMistake = 5;
                if (lstChannelInfo[1].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH1Value = arrMax[0] - arrMin[0];
                        }
                        else
                        {
                            dCH1Value = Math.Round((arrMax[0] - arrMin[0]) * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[0], 2);
                        }
                    }
                    else
                    {
                        dCH1Value = 0;
                    }
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH2Value = arrMax[1] - arrMin[1];
                        }
                        else
                        {
                            dCH2Value = Math.Round((arrMax[1] - arrMin[1]) * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[1], 2);
                        }
                    }
                    else
                    {
                        dCH2Value = 0;
                    }  
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH3Value = arrMax[2] - arrMin[2];
                        }
                        else
                        {
                            dCH3Value = Math.Round((arrMax[2] - arrMin[2]) * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[2], 2);
                        }
                    }
                    else
                    {
                        dCH3Value = 0;
                    }
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH4Value = arrMax[3] - arrMin[3];
                        }
                        else
                        {
                            dCH4Value = Math.Round((arrMax[3] - arrMin[3]) * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[3], 2);
                        }
                    }
                    else
                    {
                        dCH4Value = 0;
                    }                                   
                }

                if (lstChannelInfo[5].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH5Value = arrMax[4] - arrMin[4];
                        }
                        else
                        {
                            dCH5Value = Math.Round((arrMax[4] - arrMin[4]) * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[4], 2);
                        }
                    }
                    else
                    {
                        dCH5Value = 0;
                    }
                }

                if (lstChannelInfo[6].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH6Value = arrMax[5] - arrMin[5];
                        }
                        else
                        {
                            dCH6Value = Math.Round((arrMax[5] - arrMin[5]) * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[5], 2);
                        }
                    }
                    else
                    {
                        dCH6Value = 0;
                    }
                }

                if (lstChannelInfo[7].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH7Value = arrMax[6] - arrMin[6];
                        }
                        else
                        {
                            dCH7Value = Math.Round((arrMax[6] - arrMin[6]) * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[6], 2);
                        }
                    }
                    else
                    {
                        dCH7Value = 0;
                    }
                }

                if (lstChannelInfo[8].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH8Value = arrMax[7] - arrMin[7];
                        }
                        else
                        {
                            dCH8Value = Math.Round((arrMax[7] - arrMin[7]) * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[7], 2);
                        }
                    }
                    else
                    {
                        dCH8Value = 0;
                    }
                }

                clsData = FaultDataOscilloscopeModel.AddCalculateSet("区间峰峰值", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value, dCH5Value, dCH6Value, dCH7Value, dCH8Value);
                ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 平均值
                iMistake = 6;
                arrAverage = FaultDataOscilloscopeModel.GetAverageOfArrayWaveData(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Number, ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Number);

                if (lstChannelInfo[1].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH1Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH1Value = arrAverage[0];
                        }
                        else
                        {
                            dCH1Value = Math.Round(arrAverage[0] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[0], 2);
                        }
                    }
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH2Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH2Value = arrAverage[1];
                        }
                        else
                        {
                            dCH2Value = Math.Round(arrAverage[1] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[1], 2);
                        }
                    }
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH3Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH3Value = arrAverage[2];
                        }
                        else
                        {
                            dCH3Value = Math.Round(arrAverage[2] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[2], 2);
                        }
                    }
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH4Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH4Value = arrAverage[3];
                        }
                        else
                        {
                            dCH4Value = Math.Round(arrAverage[3] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[3], 2);
                        }
                    }
                }

                if (lstChannelInfo[5].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH5Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH5Value = arrAverage[4];
                        }
                        else
                        {
                            dCH5Value = Math.Round(arrAverage[4] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[4], 2);
                        }
                    }
                }

                if (lstChannelInfo[6].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH6Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH6Value = arrAverage[5];
                        }
                        else
                        {
                            dCH6Value = Math.Round(arrAverage[5] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[5], 2);
                        }
                    }
                }

                if (lstChannelInfo[7].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH7Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH7Value = arrAverage[6];
                        }
                        else
                        {
                            dCH7Value = Math.Round(arrAverage[6] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[6], 2);
                        }
                    }
                }

                if (lstChannelInfo[8].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH8Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH8Value = arrAverage[7];
                        }
                        else
                        {
                            dCH8Value = Math.Round(arrAverage[7] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[7], 2);
                        }
                    }
                }

                clsData = FaultDataOscilloscopeModel.AddCalculateSet("区间平均值", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value, dCH5Value, dCH6Value, dCH7Value, dCH8Value);
                ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 均方根
                iMistake = 7;
                arrRMS = FaultDataOscilloscopeModel.GetRMSOfArrayWaveData(ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[1].Number, ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate[0].Number);

                if (lstChannelInfo[1].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH1Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH1Value = arrRMS[0];
                        }
                        else
                        {
                            dCH1Value = Math.Round(arrRMS[0] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[0], 2);
                        }
                    }
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH2Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH2Value = arrRMS[1];
                        }
                        else
                        {
                            dCH2Value = Math.Round(arrRMS[1] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[1], 2);
                        }
                    }
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH3Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH3Value = arrRMS[2];
                        }
                        else
                        {
                            dCH3Value = Math.Round(arrRMS[2] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[2], 2);
                        }
                    }
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH4Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH4Value = arrRMS[3];
                        }
                        else
                        {
                            dCH4Value = Math.Round(arrRMS[3] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[3], 2);
                        }
                    }
                }

                if (lstChannelInfo[5].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH5Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH5Value = arrRMS[4];
                        }
                        else
                        {
                            dCH5Value = Math.Round(arrRMS[4] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[4], 2);
                        }
                    }
                }

                if (lstChannelInfo[6].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH6Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH6Value = arrRMS[5];
                        }
                        else
                        {
                            dCH6Value = Math.Round(arrRMS[5] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[5], 2);
                        }
                    }
                }

                if (lstChannelInfo[7].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH7Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH7Value = arrRMS[6];
                        }
                        else
                        {
                            dCH7Value = Math.Round(arrRMS[6] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[6], 2);
                        }
                    }
                }

                if (lstChannelInfo[8].IsUsed && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH8Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH8Value = arrRMS[7];
                        }
                        else
                        {
                            dCH8Value = Math.Round(arrRMS[7] * Convert.ToDouble(ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].Doubling) * FaultAcquisitionInfoSet.lstExchangeValue[7], 2);
                        }
                    }
                }

                clsData = FaultDataOscilloscopeModel.AddCalculateSet("区间均方根", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value, dCH5Value, dCH6Value, dCH7Value, dCH8Value);
                ViewModelSet.FaultDataOscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion
            }
            catch (System.Exception ex)
            {
                string strInfo = "IMistake: " + iMistake + " ";
                strInfo += "Index: " + strIndex + " ";
                strInfo += "Time: " + strTime + " ";

                strInfo += "Channel1.MarkerPosition.X: " + dataFollowChart_Channel1.MarkerPosition.X + " ";
                strInfo += "Channel1.MarkerPosition.Y: " + dataFollowChart_Channel1.MarkerPosition.Y + " ";

                strInfo += "CH1Value: " + dCH1Value + " ";
                strInfo += "CH2Value: " + dCH2Value + " ";
                strInfo += "CH3Value: " + dCH3Value + " ";
                strInfo += "CH4Value: " + dCH4Value + " ";
                strInfo += "CH5Value: " + dCH5Value + " ";
                strInfo += "CH6Value: " + dCH6Value + " ";
                strInfo += "CH7Value: " + dCH7Value + " ";
                strInfo += "CH8Value: " + dCH8Value + " ";

                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_PLOTTER_MOUSE_LEFT_BUTTON_UP, "plotter_MouseLeftButtonUp-" + strInfo, ex);
            }        
        }
        #endregion
    }

    public class FaultDataOscilloscopePoint
    {
        public double Time { get; set; }
        public double Value { get; set; }

        public FaultDataOscilloscopePoint(double time, double value)
        {
            Time = time;
            Value = value;
        }
    }

    public class FaultDataOscilloscopePointCollection : RingArray<FaultDataOscilloscopePoint>
    {
        private const int TOTAL_POINTS = FaultDataOscilloscope.Collection;
        public FaultDataOscilloscopePointCollection() : base(TOTAL_POINTS) { }
    }

    public class FaultDataChannelInfo
    {
        public FaultDataOscilloscopePointCollection FaultDataOscilloscope = new FaultDataOscilloscopePointCollection();//数据环   
        public Queue YValueQueue= new Queue();//Y轴数据队列
       
        public double XValue = 0;//x轴数据
        public double YValue = 0;//y轴数据
        public bool IsUsed = false;//通道是否使用
    }
}
