﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Data;
using ServoStudio.Models;
using System.Collections.Generic;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class DigitalIOViewModel
    {
        #region 私有字段
        private static bool IsInitialized = true;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 服务
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 属性
        public virtual ObservableCollection<string> DI1Logic { get; set; }//数字IO输入方式    
        public virtual ObservableCollection<string> DI2Logic { get; set; }//数字IO输入方式  
        public virtual ObservableCollection<string> DI3Logic { get; set; }//数字IO输入方式  
        public virtual ObservableCollection<string> DI4Logic { get; set; }//数字IO输入方式  
        public virtual ObservableCollection<string> DI5Logic { get; set; }//数字IO输入方式  
        public virtual ObservableCollection<string> DI6Logic { get; set; }//数字IO输入方式   
        public virtual ObservableCollection<string> DI1Function { get; set; }//数字IO输入功能
        public virtual ObservableCollection<string> DI2Function { get; set; }//数字IO输入功能
        public virtual ObservableCollection<string> DI3Function { get; set; }//数字IO输入功能
        public virtual ObservableCollection<string> DI4Function { get; set; }//数字IO输入功能
        public virtual ObservableCollection<string> DI5Function { get; set; }//数字IO输入功能
        public virtual ObservableCollection<string> DI6Function { get; set; }//数字IO输入功能
        public virtual ObservableCollection<string> DO1Logic { get; set; }//数字IO输出方式  
        public virtual ObservableCollection<string> DO2Logic { get; set; }//数字IO输出方式     
        public virtual ObservableCollection<string> DO3Logic { get; set; }//数字IO输出方式     
        public virtual ObservableCollection<string> DO4Logic { get; set; }//数字IO输出方式     
        public virtual ObservableCollection<string> DO5Logic { get; set; }//数字IO输出方式     
        public virtual ObservableCollection<string> DO6Logic { get; set; }//数字IO输出方式        
        public virtual ObservableCollection<string> DO1Function { get; set; }//数字IO输出功能
        public virtual ObservableCollection<string> DO2Function { get; set; }//数字IO输出功能
        public virtual ObservableCollection<string> DO3Function { get; set; }//数字IO输出功能
        public virtual ObservableCollection<string> DO4Function { get; set; }//数字IO输出功能
        public virtual ObservableCollection<string> DO5Function { get; set; }//数字IO输出功能
        public virtual ObservableCollection<string> DO6Function { get; set; }//数字IO输出功能

        public virtual string SelectedDI1Logic { get; set; }
        public virtual string SelectedDI2Logic { get; set; }
        public virtual string SelectedDI3Logic { get; set; }
        public virtual string SelectedDI4Logic { get; set; }
        public virtual string SelectedDI5Logic { get; set; }
        public virtual string SelectedDI6Logic { get; set; }

        public virtual string SelectedDI1Function { get; set; }
        public virtual string SelectedDI2Function { get; set; }
        public virtual string SelectedDI3Function { get; set; }
        public virtual string SelectedDI4Function { get; set; }
        public virtual string SelectedDI5Function { get; set; }
        public virtual string SelectedDI6Function { get; set; }

        public virtual string SelectedDO1Logic { get; set; }
        public virtual string SelectedDO2Logic { get; set; }
        public virtual string SelectedDO3Logic { get; set; }
        public virtual string SelectedDO4Logic { get; set; }
        public virtual string SelectedDO5Logic { get; set; }
        public virtual string SelectedDO6Logic { get; set; }

        public virtual string SelectedDO1Function { get; set; }
        public virtual string SelectedDO2Function { get; set; }
        public virtual string SelectedDO3Function { get; set; }
        public virtual string SelectedDO4Function { get; set; }
        public virtual string SelectedDO5Function { get; set; }
        public virtual string SelectedDO6Function { get; set; }
        #endregion

        #region 构造函数
        public DigitalIOViewModel()
        {
            ViewModelSet.Digital = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.DIGITALIO;
        }    
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：DigitalIOLoaded
        //函数功能：DigitalIO控件Loaded
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.14
        //*************************************************************************
        public void DigitalIOLoaded()
        {
            int iRet = -1;

            try
            {
                //下拉列表初始化
                ComboBoxInitialize();

                if (IsInitialized == true)
                {
                    //标志位更新
                    IsInitialized = false;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadDigitalIOParameter();
                    }
                    else
                    {
                        GetDefaultDigitalIOParameter();
                    }
                }
                else
                {
                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadDigitalIOParameter();
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }
                }

                //更新选中的DI功能
                RefreshSelectedDIFunction();
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_LOADED, "DigitalIOLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：WriteDigitalIOParameter
        //函数功能：写IO参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.04
        //*************************************************************************
        public void WriteDigitalIOParameter()
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary(ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.DIGITALIO, TaskName.DigitalIO, lstTransmittingDataInfo);

                //更新选中的DI功能
                RefreshSelectedDIFunction();
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_WRITE_PARAMETER, "WriteIOParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadDigitalIOParameter
        //函数功能：读IO参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.04
        //*************************************************************************
        public void ReadDigitalIOParameter()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.DIGITALIO, TaskName.DigitalIO, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_READ_PARAMETER, "ReadDigitalIOParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultDigitalIOParameter
        //函数功能：获取IO的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.03
        //*************************************************************************
        public void GetDefaultDigitalIOParameter()
        {
            try
            {
                SelectedDI1Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI1 Logic Select", "Default"));
                SelectedDI2Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI2 Logic Select", "Default"));
                SelectedDI3Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI3 Logic Select", "Default"));
                SelectedDI4Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI4 Logic Select", "Default"));
                SelectedDI5Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI5 Logic Select", "Default"));
                SelectedDI6Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI6 Logic Select", "Default"));

                SelectedDI1Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI1 Function Select", "Default"));
                SelectedDI2Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI2 Function Select", "Default"));
                SelectedDI3Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI3 Function Select", "Default"));
                SelectedDI4Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI4 Function Select", "Default"));
                SelectedDI5Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI5 Function Select", "Default"));
                SelectedDI6Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI6 Function Select", "Default"));

                SelectedDO1Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO1 Logic Select", "Default"));
                SelectedDO2Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO2 Logic Select", "Default"));
                SelectedDO3Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO3 Logic Select", "Default"));
                SelectedDO4Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO4 Logic Select", "Default"));
                SelectedDO5Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO5 Logic Select", "Default"));
                SelectedDO6Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO6 Logic Select", "Default"));

                SelectedDO1Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO1 Function Select", "Default"));
                SelectedDO2Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO2 Function Select", "Default"));
                SelectedDO3Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO3 Function Select", "Default"));
                SelectedDO4Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO4 Function Select", "Default"));
                SelectedDO5Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO5 Function Select", "Default"));
                SelectedDO6Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO6 Function Select", "Default"));
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_GET_DEFAULT_PARAMETER, "GetDefaultDigitalIOParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：SaveIOConfigFile
        //函数功能：保存IO配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.14
        //*************************************************************************
        public void SaveIOConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                //选择配置文件存放位置
                iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.IOConfig);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //生成配置文件
                iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetIOConfigToDataTable(), ExcelType.IOConfig);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2002);
                }
                else
                {
                    ShowNotification(2003);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_SAVE_IO_CONFIG_FILE, "SaveIOConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetIOConfigFile
        //函数功能：获取IO配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.14
        //*************************************************************************
        public void GetIOConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            DataTable dt = null;

            try
            {
                //获取配置文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(strFilePath, ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //判断是否导入正确的配置文件
                if (dt.Columns[3].ColumnName != "IOInterface")
                {
                    ShowNotification(2001);
                    return;
                }

                iRet = GetIOConfigFromDataTable(dt);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2007);
                }
                else
                {
                    ShowNotification(RET.ERROR);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_GET_IO_CONFIG_FILE, "GetIOConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationDigitalIOParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.05
        //*************************************************************************
        public void EvaluationDigitalIOParameter()
        {
            try
            {
                SelectedDI1Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI1 Logic Select", "Index")));
                SelectedDI2Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI2 Logic Select", "Index")));
                SelectedDI3Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI3 Logic Select", "Index")));
                SelectedDI4Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI4 Logic Select", "Index")));
                SelectedDI5Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI5 Logic Select", "Index")));
                SelectedDI6Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI6 Logic Select", "Index")));

                SelectedDI1Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI1 Function Select", "Index")));
                SelectedDI2Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI2 Function Select", "Index")));
                SelectedDI3Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI3 Function Select", "Index")));
                SelectedDI4Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI4 Function Select", "Index")));
                SelectedDI5Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI5 Function Select", "Index")));
                SelectedDI6Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DI, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DI6 Function Select", "Index")));

                SelectedDO1Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO1 Logic Select", "Index")));
                SelectedDO2Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO2 Logic Select", "Index")));
                SelectedDO3Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO3 Logic Select", "Index")));
                SelectedDO4Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO4 Logic Select", "Index")));
                SelectedDO5Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO5 Logic Select", "Index")));
                SelectedDO6Logic = DigitalIOModel.ReflectIndexToItem(IOOperation.LOGIC_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO6 Logic Select", "Index")));

                SelectedDO1Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO1 Function Select", "Index")));
                SelectedDO2Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO2 Function Select", "Index")));
                SelectedDO3Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO3 Function Select", "Index")));
                SelectedDO4Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO4 Function Select", "Index")));
                SelectedDO5Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO5 Function Select", "Index")));
                SelectedDO6Function = DigitalIOModel.ReflectIndexToItem(IOOperation.FUNCTION_DO, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "DO6 Function Select", "Index")));
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_EVENT_GET_PARAMETER, "CommunicationSet_evtGetDigitalIOParameter", ex);
            }
        }
     
        //*************************************************************************
        //函数名称：NormalSettingNavigation
        //函数功能：一般参数设置导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.03
        //*************************************************************************
        public void NormalSettingNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.NORMALSET;
                NavigationService.Navigate("NormalSettingView", null, ViewModelSet.Main);
            }           
        }

        //*************************************************************************
        //函数名称：OscilloscopeNavigation
        //函数功能：示波器导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.03
        //*************************************************************************
        public void OscilloscopeNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.OSCILLOSCOPE;
                NavigationService.Navigate("OscilloscopeView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        public void OnSelectedDI1LogicChanged() { GlobalCurrentInput.SelectedDI1Logic = SelectedDI1Logic; }//DI1输入方式
        public void OnSelectedDI2LogicChanged() { GlobalCurrentInput.SelectedDI2Logic = SelectedDI2Logic; }//DI2输入方式
        public void OnSelectedDI3LogicChanged() { GlobalCurrentInput.SelectedDI3Logic = SelectedDI3Logic; }//DI3输入方式
        public void OnSelectedDI4LogicChanged() { GlobalCurrentInput.SelectedDI4Logic = SelectedDI4Logic; }//DI4输入方式
        public void OnSelectedDI5LogicChanged() { GlobalCurrentInput.SelectedDI5Logic = SelectedDI5Logic; }//DI5输入方式
        public void OnSelectedDI6LogicChanged() { GlobalCurrentInput.SelectedDI6Logic = SelectedDI6Logic; }//DI6输入方式

        public void OnSelectedDI1FunctionChanged() { GlobalCurrentInput.SelectedDI1Function = SelectedDI1Function; }//DI1功能
        public void OnSelectedDI2FunctionChanged() { GlobalCurrentInput.SelectedDI2Function = SelectedDI2Function; }//DI2功能
        public void OnSelectedDI3FunctionChanged() { GlobalCurrentInput.SelectedDI3Function = SelectedDI3Function; }//DI3功能
        public void OnSelectedDI4FunctionChanged() { GlobalCurrentInput.SelectedDI4Function = SelectedDI4Function; }//DI4功能
        public void OnSelectedDI5FunctionChanged() { GlobalCurrentInput.SelectedDI5Function = SelectedDI5Function; }//DI5功能
        public void OnSelectedDI6FunctionChanged() { GlobalCurrentInput.SelectedDI6Function = SelectedDI6Function; }//DI6功能

        public void OnSelectedDO1LogicChanged() { GlobalCurrentInput.SelectedDO1Logic = SelectedDO1Logic; }//DO1输入方式
        public void OnSelectedDO2LogicChanged() { GlobalCurrentInput.SelectedDO2Logic = SelectedDO2Logic; }//DO2输入方式
        public void OnSelectedDO3LogicChanged() { GlobalCurrentInput.SelectedDO3Logic = SelectedDO3Logic; }//DO3输入方式
        public void OnSelectedDO4LogicChanged() { GlobalCurrentInput.SelectedDO4Logic = SelectedDO4Logic; }//DO4输入方式
        public void OnSelectedDO5LogicChanged() { GlobalCurrentInput.SelectedDO5Logic = SelectedDO5Logic; }//DO5输入方式
        public void OnSelectedDO6LogicChanged() { GlobalCurrentInput.SelectedDO6Logic = SelectedDO6Logic; }//DO6输入方式

        public void OnSelectedDO1FunctionChanged() { GlobalCurrentInput.SelectedDO1Function = SelectedDO1Function; }//DO1功能
        public void OnSelectedDO2FunctionChanged() { GlobalCurrentInput.SelectedDO2Function = SelectedDO2Function; }//DO2功能
        public void OnSelectedDO3FunctionChanged() { GlobalCurrentInput.SelectedDO3Function = SelectedDO3Function; }//DO3功能
        public void OnSelectedDO4FunctionChanged() { GlobalCurrentInput.SelectedDO4Function = SelectedDO4Function; }//DO4功能
        public void OnSelectedDO5FunctionChanged() { GlobalCurrentInput.SelectedDO5Function = SelectedDO5Function; }//DO5功能
        public void OnSelectedDO6FunctionChanged() { GlobalCurrentInput.SelectedDO6Function = SelectedDO6Function; }//DO6功能

        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：GetIOConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.14
        //*************************************************************************
        private DataTable GetIOConfigToDataTable()
        {
            DataTable dt = new DataTable();

            try
            {
                DigitalIOModel.MakeValueToDataTable("DI1 Function Select", "DI1功能选择", SelectedDI1Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DI1 Logic Select", "DI1逻辑选择", SelectedDI1Logic, ref dt);

                DigitalIOModel.MakeValueToDataTable("DI2 Function Select", "DI2功能选择", SelectedDI2Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DI2 Logic Select", "DI2逻辑选择", SelectedDI2Logic, ref dt);

                DigitalIOModel.MakeValueToDataTable("DI3 Function Select", "DI3功能选择", SelectedDI3Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DI3 Logic Select", "DI3逻辑选择", SelectedDI3Logic, ref dt);

                DigitalIOModel.MakeValueToDataTable("DI4 Function Select", "DI4功能选择", SelectedDI4Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DI4 Logic Select", "DI4逻辑选择", SelectedDI4Logic, ref dt);

                DigitalIOModel.MakeValueToDataTable("DI5 Function Select", "DI5功能选择", SelectedDI5Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DI5 Logic Select", "DI5逻辑选择", SelectedDI5Logic, ref dt);

                DigitalIOModel.MakeValueToDataTable("DI6 Function Select", "DI6功能选择", SelectedDI6Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DI6 Logic Select", "DI6逻辑选择", SelectedDI6Logic, ref dt);

                //DigitalIOModel.MakeValueToDataTable("DI1 Function Select", "DI1功能选择", SelectedDI1Function, ref dt);
                //DigitalIOModel.MakeValueToDataTable("DI2 Function Select", "DI2功能选择", SelectedDI2Function, ref dt);
                //DigitalIOModel.MakeValueToDataTable("DI3 Function Select", "DI3功能选择", SelectedDI3Function, ref dt);
                //DigitalIOModel.MakeValueToDataTable("DI4 Function Select", "DI4功能选择", SelectedDI4Function, ref dt);
                //DigitalIOModel.MakeValueToDataTable("DI5 Function Select", "DI5功能选择", SelectedDI5Function, ref dt);
                //DigitalIOModel.MakeValueToDataTable("DI6 Function Select", "DI6功能选择", SelectedDI6Function, ref dt);

                DigitalIOModel.MakeValueToDataTable("DO1 Function Select", "DO1功能选择", SelectedDO1Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DO1 Logic Select", "DO1逻辑选择", SelectedDO1Logic, ref dt);

                DigitalIOModel.MakeValueToDataTable("DO2 Function Select", "DO2功能选择", SelectedDO2Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DO2 Logic Select", "DO2逻辑选择", SelectedDO2Logic, ref dt);

                DigitalIOModel.MakeValueToDataTable("DO3 Function Select", "DO3功能选择", SelectedDO3Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DO3 Logic Select", "DO3逻辑选择", SelectedDO3Logic, ref dt);

                DigitalIOModel.MakeValueToDataTable("DO4 Function Select", "DO4功能选择", SelectedDO4Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DO4 Logic Select", "DO4逻辑选择", SelectedDO4Logic, ref dt);

                DigitalIOModel.MakeValueToDataTable("DO5 Function Select", "DO5功能选择", SelectedDO5Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DO5 Logic Select", "DO5逻辑选择", SelectedDO5Logic, ref dt);

                DigitalIOModel.MakeValueToDataTable("DO6 Function Select", "DO6功能选择", SelectedDO6Function, ref dt);
                DigitalIOModel.MakeValueToDataTable("DO6 Logic Select", "DO6逻辑选择", SelectedDO6Logic, ref dt);

                //DigitalIOModel.MakeValueToDataTable("DO1 Function Select", "DO1功能选择", SelectedDO1Function, ref dt);
                //DigitalIOModel.MakeValueToDataTable("DO2 Function Select", "DO2功能选择", SelectedDO2Function, ref dt);
                //DigitalIOModel.MakeValueToDataTable("DO3 Function Select", "DO3功能选择", SelectedDO3Function, ref dt);
                //DigitalIOModel.MakeValueToDataTable("DO4 Function Select", "DO4功能选择", SelectedDO4Function, ref dt);
                //DigitalIOModel.MakeValueToDataTable("DO5 Function Select", "DO5功能选择", SelectedDO5Function, ref dt);
                //DigitalIOModel.MakeValueToDataTable("DO6 Function Select", "DO6功能选择", SelectedDO6Function, ref dt);

                return dt;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_GET_IO_CONFIG_TO_DATATABLE, "GetIOConfigToDataTable", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetIOConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.14
        //*************************************************************************
        private int GetIOConfigFromDataTable(DataTable dt)
        {
            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                SelectedDI1Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI1 Function Select", "Default");
                SelectedDI1Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI1 Logic Select", "Default");

                SelectedDI2Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI2 Function Select", "Default");
                SelectedDI2Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI2 Logic Select", "Default");

                SelectedDI3Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI3 Function Select", "Default");
                SelectedDI3Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI3 Logic Select", "Default");

                SelectedDI4Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI4 Function Select", "Default");
                SelectedDI4Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI4 Logic Select", "Default");

                SelectedDI5Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI5 Function Select", "Default");
                SelectedDI5Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI5 Logic Select", "Default");

                SelectedDI6Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI6 Function Select", "Default");
                SelectedDI6Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI6 Logic Select", "Default");

                //SelectedDI1Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI1 Function Select", "Default");
                //SelectedDI2Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI2 Function Select", "Default");
                //SelectedDI3Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI3 Function Select", "Default");
                //SelectedDI4Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI4 Function Select", "Default");
                //SelectedDI5Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI5 Function Select", "Default");
                //SelectedDI6Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DI6 Function Select", "Default");

                SelectedDO1Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO1 Function Select", "Default");
                SelectedDO1Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO1 Logic Select", "Default");

                SelectedDO2Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO2 Function Select", "Default");
                SelectedDO2Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO2 Logic Select", "Default");

                SelectedDO3Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO3 Function Select", "Default");
                SelectedDO3Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO3 Logic Select", "Default");

                SelectedDO4Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO4 Function Select", "Default");
                SelectedDO4Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO4 Logic Select", "Default");

                SelectedDO5Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO5 Function Select", "Default");
                SelectedDO5Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO5 Logic Select", "Default");

                SelectedDO6Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO6 Function Select", "Default");
                SelectedDO6Logic = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO6 Logic Select", "Default");

                //SelectedDO1Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO1 Function Select", "Default");
                //SelectedDO2Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO2 Function Select", "Default");
                //SelectedDO3Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO3 Function Select", "Default");
                //SelectedDO4Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO4 Function Select", "Default");
                //SelectedDO5Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO5 Function Select", "Default");
                //SelectedDO6Function = OthersHelper.GetCellValueFromDataTable(dt, "Name", "DO6 Function Select", "Default");

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_GET_IO_CONFIG_FROM_DATATABLE, "GetIOConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：下拉列表初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.22&2023.01.04
        //*************************************************************************
        public void ComboBoxInitialize()
        {            
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                DI1Logic = new ObservableCollection<string>() { "低电平有效", "高电平有效", "上升沿有效", "下降沿有效", "双边沿有效" };
                DI2Logic = new ObservableCollection<string>() { "低电平有效", "高电平有效", "上升沿有效", "下降沿有效", "双边沿有效" };
                DI3Logic = new ObservableCollection<string>() { "低电平有效", "高电平有效", "上升沿有效", "下降沿有效", "双边沿有效" };
                DI4Logic = new ObservableCollection<string>() { "低电平有效", "高电平有效", "上升沿有效", "下降沿有效", "双边沿有效" };
                DI5Logic = new ObservableCollection<string>() { "低电平有效", "高电平有效", "上升沿有效", "下降沿有效", "双边沿有效" };
                DI6Logic = new ObservableCollection<string>() { "低电平有效", "高电平有效", "上升沿有效", "下降沿有效", "双边沿有效" };

                SelectedDI1Logic = "低电平有效";
                SelectedDI2Logic = "低电平有效";
                SelectedDI3Logic = "低电平有效";
                SelectedDI4Logic = "低电平有效";
                SelectedDI5Logic = "低电平有效";
                SelectedDI6Logic = "低电平有效";

                DI1Function = new ObservableCollection<string>() { "无配置", "负限位", "正限位",  "回零", "禁能", "使能", "急停", "故障清除" };
                DI2Function = new ObservableCollection<string>() { "无配置", "负限位", "正限位",  "回零", "禁能", "使能", "急停", "故障清除" };
                DI3Function = new ObservableCollection<string>() { "无配置", "负限位", "正限位",  "回零", "禁能", "使能", "急停", "故障清除" };
                DI4Function = new ObservableCollection<string>() { "无配置", "负限位", "正限位",  "回零", "禁能", "使能", "急停", "故障清除" };
                DI5Function = new ObservableCollection<string>() { "无配置", "负限位", "正限位",  "回零", "禁能", "使能", "急停", "故障清除" };
                DI6Function = new ObservableCollection<string>() { "无配置", "负限位", "正限位",  "回零", "禁能", "使能", "急停", "故障清除" };

                SelectedDI1Function = "负限位";
                SelectedDI2Function = "负限位";
                SelectedDI3Function = "负限位";
                SelectedDI4Function = "负限位";
                SelectedDI5Function = "负限位";
                SelectedDI6Function = "负限位";

                DO1Logic = new ObservableCollection<string>() { "有效时输出低电平", "有效时输出高电平" };
                DO2Logic = new ObservableCollection<string>() { "有效时输出低电平", "有效时输出高电平" };
                DO3Logic = new ObservableCollection<string>() { "有效时输出低电平", "有效时输出高电平" };
                DO4Logic = new ObservableCollection<string>() { "有效时输出低电平", "有效时输出高电平" };
                DO5Logic = new ObservableCollection<string>() { "有效时输出低电平", "有效时输出高电平" };
                DO6Logic = new ObservableCollection<string>() { "有效时输出低电平", "有效时输出高电平" };
            
                SelectedDO1Logic = "有效时输出低电平";
                SelectedDO2Logic = "有效时输出低电平";
                SelectedDO3Logic = "有效时输出低电平";
                SelectedDO4Logic = "有效时输出低电平";
                SelectedDO5Logic = "有效时输出低电平";
                SelectedDO6Logic = "有效时输出低电平";

                DO1Function = new ObservableCollection<string>() { "无配置", "GPIO", "故障", "抱闸", "位置到达", "Ecat总线控制"};
                DO2Function = new ObservableCollection<string>() { "无配置", "GPIO", "故障", "抱闸", "位置到达", "Ecat总线控制" };
                DO3Function = new ObservableCollection<string>() { "无配置", "GPIO", "故障", "抱闸", "位置到达", "Ecat总线控制" };
                DO4Function = new ObservableCollection<string>() { "无配置", "GPIO", "故障", "抱闸", "位置到达", "Ecat总线控制" };
                DO5Function = new ObservableCollection<string>() { "无配置", "GPIO", "故障", "抱闸", "位置到达", "Ecat总线控制" };
                DO6Function = new ObservableCollection<string>() { "无配置", "GPIO", "故障", "抱闸", "位置到达", "Ecat总线控制" };

                SelectedDO1Function = "无配置";
                SelectedDO2Function = "无配置";
                SelectedDO3Function = "无配置";
                SelectedDO4Function = "无配置";
                SelectedDO5Function = "无配置";
                SelectedDO6Function = "无配置";
            }
            else
            {
                DI1Logic = new ObservableCollection<string>() { "ActiveLow", "ActiveHigh", "ActiveRisingEdge", "ActiveFallingEdge", "ActiveDoubleEdge" };
                DI2Logic = new ObservableCollection<string>() { "ActiveLow", "ActiveHigh", "ActiveRisingEdge", "ActiveFallingEdge", "ActiveDoubleEdge" };
                DI3Logic = new ObservableCollection<string>() { "ActiveLow", "ActiveHigh", "ActiveRisingEdge", "ActiveFallingEdge", "ActiveDoubleEdge" };
                DI4Logic = new ObservableCollection<string>() { "ActiveLow", "ActiveHigh", "ActiveRisingEdge", "ActiveFallingEdge", "ActiveDoubleEdge" };
                DI5Logic = new ObservableCollection<string>() { "ActiveLow", "ActiveHigh", "ActiveRisingEdge", "ActiveFallingEdge", "ActiveDoubleEdge" };
                DI6Logic = new ObservableCollection<string>() { "ActiveLow", "ActiveHigh", "ActiveRisingEdge", "ActiveFallingEdge", "ActiveDoubleEdge" };

                SelectedDI1Logic = "ActiveLow";
                SelectedDI2Logic = "ActiveLow";
                SelectedDI3Logic = "ActiveLow";
                SelectedDI4Logic = "ActiveLow";
                SelectedDI5Logic = "ActiveLow";
                SelectedDI6Logic = "ActiveLow";

                DI1Function = new ObservableCollection<string>() { "PositiveLimit", "NegativeLimit", "ZeroReturnSwitch", "Disabled", "Enabled", "MotorOverTemperature", "StartUp", "NormalStop", "QuickStop", "PositiveInching", "ReverseInching", "ClearFault", "Reset" };
                DI2Function = new ObservableCollection<string>() { "PositiveLimit", "NegativeLimit", "ZeroReturnSwitch", "Disabled", "Enabled", "MotorOverTemperature", "StartUp", "NormalStop", "QuickStop", "PositiveInching", "ReverseInching", "ClearFault", "Reset" };
                DI3Function = new ObservableCollection<string>() { "PositiveLimit", "NegativeLimit", "ZeroReturnSwitch", "Disabled", "Enabled", "MotorOverTemperature", "StartUp", "NormalStop", "QuickStop", "PositiveInching", "ReverseInching", "ClearFault", "Reset" };
                DI4Function = new ObservableCollection<string>() { "PositiveLimit", "NegativeLimit", "ZeroReturnSwitch", "Disabled", "Enabled", "MotorOverTemperature", "StartUp", "NormalStop", "QuickStop", "PositiveInching", "ReverseInching", "ClearFault", "Reset" };
                DI5Function = new ObservableCollection<string>() { "PositiveLimit", "NegativeLimit", "ZeroReturnSwitch", "Disabled", "Enabled", "MotorOverTemperature", "StartUp", "NormalStop", "QuickStop", "PositiveInching", "ReverseInching", "ClearFault", "Reset" };
                DI6Function = new ObservableCollection<string>() { "PositiveLimit", "NegativeLimit", "ZeroReturnSwitch", "Disabled", "Enabled", "MotorOverTemperature", "StartUp", "NormalStop", "QuickStop", "PositiveInching", "ReverseInching", "ClearFault", "Reset" };

                SelectedDI1Function = "PositiveLimit";
                SelectedDI2Function = "PositiveLimit";
                SelectedDI3Function = "PositiveLimit";
                SelectedDI4Function = "PositiveLimit";
                SelectedDI5Function = "PositiveLimit";
                SelectedDI6Function = "PositiveLimit";

                DO1Logic = new ObservableCollection<string>() { "Output low level when valid", "Output high level when valid" };
                DO2Logic = new ObservableCollection<string>() { "Output low level when valid", "Output high level when valid" };
                DO3Logic = new ObservableCollection<string>() { "Output low level when valid", "Output high level when valid" };
                DO4Logic = new ObservableCollection<string>() { "Output low level when valid", "Output high level when valid" };
                DO5Logic = new ObservableCollection<string>() { "Output low level when valid", "Output high level when valid" };
                DO6Logic = new ObservableCollection<string>() { "Output low level when valid", "Output high level when valid" };

                SelectedDO1Logic = "Output low level when valid";
                SelectedDO2Logic = "Output low level when valid";
                SelectedDO3Logic = "Output low level when valid";
                SelectedDO4Logic = "Output low level when valid";
                SelectedDO5Logic = "Output low level when valid";
                SelectedDO6Logic = "Output low level when valid";

                DO1Function = new ObservableCollection<string>() { "GeneralDO", "Fault", "BandBrake", "TargetArrival" };
                DO2Function = new ObservableCollection<string>() { "GeneralDO", "Fault", "BandBrake", "TargetArrival" };
                DO3Function = new ObservableCollection<string>() { "GeneralDO", "Fault", "BandBrake", "TargetArrival" };
                DO4Function = new ObservableCollection<string>() { "GeneralDO", "Fault", "BandBrake", "TargetArrival" };
                DO5Function = new ObservableCollection<string>() { "GeneralDO", "Fault", "BandBrake", "TargetArrival" };
                DO6Function = new ObservableCollection<string>() { "GeneralDO", "Fault", "BandBrake", "TargetArrival" };

                SelectedDO1Function = "GeneralDO";
                SelectedDO2Function = "GeneralDO";
                SelectedDO3Function = "GeneralDO";
                SelectedDO4Function = "GeneralDO";
                SelectedDO5Function = "GeneralDO";
                SelectedDO6Function = "GeneralDO";
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.04
        //*************************************************************************
        private int AddParameterInfoDictionary(ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                dicParameterInfo.Add("DI1 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI1Function));
                dicParameterInfo.Add("DI1 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI1Logic));

                dicParameterInfo.Add("DI2 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI2Function));
                dicParameterInfo.Add("DI2 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI2Logic));

                dicParameterInfo.Add("DI3 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI3Function));
                dicParameterInfo.Add("DI3 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI3Logic));

                dicParameterInfo.Add("DI4 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI4Function));
                dicParameterInfo.Add("DI4 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI4Logic));

                dicParameterInfo.Add("DI5 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI5Function));
                dicParameterInfo.Add("DI5 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI5Logic));

                dicParameterInfo.Add("DI6 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI6Function));
                dicParameterInfo.Add("DI6 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI6Logic));



                dicParameterInfo.Add("DO1 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO1Function));
                dicParameterInfo.Add("DO1 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO1Logic));

                dicParameterInfo.Add("DO2 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO2Function));
                dicParameterInfo.Add("DO2 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO2Logic));

                dicParameterInfo.Add("DO3 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO3Function));
                dicParameterInfo.Add("DO3 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO3Logic));

                dicParameterInfo.Add("DO4 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO4Function));
                dicParameterInfo.Add("DO4 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO4Logic));

                dicParameterInfo.Add("DO5 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO5Function));
                dicParameterInfo.Add("DO5 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO5Logic));

                dicParameterInfo.Add("DO6 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO6Function));
                dicParameterInfo.Add("DO6 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO6Logic));



                //dicParameterInfo.Add("DI1 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI1Logic));
                //dicParameterInfo.Add("DI2 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI2Logic));
                //dicParameterInfo.Add("DI3 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI3Logic));
                //dicParameterInfo.Add("DI4 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI4Logic));
                //dicParameterInfo.Add("DI5 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI5Logic));
                //dicParameterInfo.Add("DI6 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DI, SelectedDI6Logic));

                //dicParameterInfo.Add("DI1 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI1Function));
                //dicParameterInfo.Add("DI2 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI2Function));
                //dicParameterInfo.Add("DI3 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI3Function));
                //dicParameterInfo.Add("DI4 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI4Function));
                //dicParameterInfo.Add("DI5 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI5Function));
                //dicParameterInfo.Add("DI6 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DI, SelectedDI6Function));

                //dicParameterInfo.Add("DO1 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO1Logic));
                //dicParameterInfo.Add("DO2 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO2Logic));
                //dicParameterInfo.Add("DO3 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO3Logic));
                //dicParameterInfo.Add("DO4 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO4Logic));
                //dicParameterInfo.Add("DO5 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO5Logic));
                //dicParameterInfo.Add("DO6 Logic Select", DigitalIOModel.ReflectItemToIndex(IOOperation.LOGIC_DO, SelectedDO6Logic));

                //dicParameterInfo.Add("DO1 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO1Function));
                //dicParameterInfo.Add("DO2 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO2Function));
                //dicParameterInfo.Add("DO3 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO3Function));
                //dicParameterInfo.Add("DO4 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO4Function));
                //dicParameterInfo.Add("DO5 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO5Function));
                //dicParameterInfo.Add("DO6 Function Select", DigitalIOModel.ReflectItemToIndex(IOOperation.FUNCTION_DO, SelectedDO6Function));

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                SelectedDI1Logic = GlobalCurrentInput.SelectedDI1Logic;//DI1输入方式
                SelectedDI2Logic = GlobalCurrentInput.SelectedDI2Logic;//DI2输入方式
                SelectedDI3Logic = GlobalCurrentInput.SelectedDI3Logic;//DI3输入方式
                SelectedDI4Logic = GlobalCurrentInput.SelectedDI4Logic;//DI4输入方式
                SelectedDI5Logic = GlobalCurrentInput.SelectedDI5Logic;//DI5输入方式
                SelectedDI6Logic = GlobalCurrentInput.SelectedDI6Logic;//DI6输入方式

                SelectedDI1Function = GlobalCurrentInput.SelectedDI1Function;//DI1功能
                SelectedDI2Function = GlobalCurrentInput.SelectedDI2Function;//DI2功能
                SelectedDI3Function = GlobalCurrentInput.SelectedDI3Function;//DI3功能
                SelectedDI4Function = GlobalCurrentInput.SelectedDI4Function;//DI4功能
                SelectedDI5Function = GlobalCurrentInput.SelectedDI5Function;//DI5功能
                SelectedDI6Function = GlobalCurrentInput.SelectedDI6Function;//DI6功能

                SelectedDO1Logic = GlobalCurrentInput.SelectedDO1Logic;//DO1输入方式
                SelectedDO2Logic = GlobalCurrentInput.SelectedDO2Logic;//DO2输入方式
                SelectedDO3Logic = GlobalCurrentInput.SelectedDO3Logic;//DO3输入方式
                SelectedDO4Logic = GlobalCurrentInput.SelectedDO4Logic;//DO4输入方式
                SelectedDO5Logic = GlobalCurrentInput.SelectedDO5Logic;//DO5输入方式
                SelectedDO6Logic = GlobalCurrentInput.SelectedDO6Logic;//DO6输入方式

                SelectedDO1Function = GlobalCurrentInput.SelectedDO1Function;//DO1功能
                SelectedDO2Function = GlobalCurrentInput.SelectedDO2Function;//DO2功能
                SelectedDO3Function = GlobalCurrentInput.SelectedDO3Function;//DO3功能
                SelectedDO4Function = GlobalCurrentInput.SelectedDO4Function;//DO4功能
                SelectedDO5Function = GlobalCurrentInput.SelectedDO5Function;//DO5功能
                SelectedDO6Function = GlobalCurrentInput.SelectedDO6Function;//DO6功能
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }     

        //*************************************************************************
        //函数名称：RefreshSelectedDIFunction
        //函数功能：更新DI选中的Function
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.07
        //*************************************************************************
        private void RefreshSelectedDIFunction()
        {
            if (SoftwareStateParameterSet.SelectedDIFunction == null)
            {
                SoftwareStateParameterSet.SelectedDIFunction = new List<string>();
            }
            else
            {
                SoftwareStateParameterSet.SelectedDIFunction.Clear();
            }

            SoftwareStateParameterSet.SelectedDIFunction.Add(SelectedDI1Function);
            SoftwareStateParameterSet.SelectedDIFunction.Add(SelectedDI2Function);
            SoftwareStateParameterSet.SelectedDIFunction.Add(SelectedDI3Function);
            SoftwareStateParameterSet.SelectedDIFunction.Add(SelectedDI4Function);
            SoftwareStateParameterSet.SelectedDIFunction.Add(SelectedDI5Function);
            SoftwareStateParameterSet.SelectedDIFunction.Add(SelectedDI6Function);
        }    

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }
}