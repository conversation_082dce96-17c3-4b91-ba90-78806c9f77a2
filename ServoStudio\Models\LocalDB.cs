﻿using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ServoStudio.Models
{
    public class LocalDB
    {
        public LocalDB()
        {
            Init();
        }

        private static bool ScanSlaveAxisIDFlag = false;

        private List<SlaveAxisAddress> SlaveAxisAddresses;
        private void Init()
        {
            SlaveAxisAddresses = new List<SlaveAxisAddress>();

            for (int i = 1; i < 256; i++)
            {
                bool a =AddSlaveAxisIDSet.GetSlaveIDList.Contains($"SLAVE-{i}");
                if (a)
                {
                    SlaveAxisAddresses.Add(new SlaveAxisAddress()
                    {
                        Id = i,
                        SlaveID = $"SLAVE-{i}",
                        AxisID = $"AXIS-1"
                    });
                }
            }

            //for (int i = 0; i < AddSlaveAxisIDSet.GetSlaveIDList.Count; i++)
            //{
            //    string[] b = AddSlaveAxisIDSet.GetSlaveIDList.Where(q => q.Contains($"SlaveID-{i}")).ToArray();

            //    string a = b.ToString();

            //    SlaveAxisAddresses.Add(new SlaveAxisAddress()
            //    {
            //        Id = i,
            //        SlaveID = $"SlaveID{i}",
            //        //SlaveID = AddSlaveAxisIDSet.GetSlaveIDList[i].ToArray(),
            //        //SlaveID = (string)AddSlaveAxisIDSet.GetSlaveIDList.Select(q => q.ElementAtOrDefault(i)),
            //        AxisID = $"AxisID1"
            //    });
            //}

            //for (int i = 0; i < 30; i++)
            //{
            //    SlaveAxisAddresses.Add(new SlaveAxisAddress()
            //    {
            //        Id = i,
            //        SlaveID = $"SlaveID{i}",
            //        AxisID = $"AxisID{i}"
            //    });
            //}
        }

        public List<SlaveAxisAddress> GetSlaveAxisAddresses()
        {
            return SlaveAxisAddresses;
        }

        public void AddSlaveAxisAddress(SlaveAxisAddress SlaveAxis)
        {
            SlaveAxisAddresses.Add(SlaveAxis);
        }

        public void DelSlaveAxisAddress(int id)
        {
            var model = SlaveAxisAddresses.FirstOrDefault(t => t.Id == id);
            if (model != null)
            {
                SlaveAxisAddresses.Remove(model);
            }
        }

        public List<SlaveAxisAddress> GetSlaveAxisAddressesBySlaveID(string name)
        {
            return SlaveAxisAddresses.Where(q => q.SlaveID.Contains(name)).ToList();
        }

        public SlaveAxisAddress GetSlaveAxisAddressById(int id)
        {
            var model = SlaveAxisAddresses.FirstOrDefault(t => t.Id == id);
            if (model != null)
            {
                return new SlaveAxisAddress()
                {
                    Id = model.Id,
                    SlaveID = model.SlaveID,
                    AxisID = model.AxisID
                };
            }
            return null;
        }

        public void ScanSlaveAxisAddress()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> TransmittingDataInfo = new List<TransmitingDataInfoSet>();
            TransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = "9999" });

            SlaveAxisAddresses = new List<SlaveAxisAddress>();

            //for (int i = 0; i < 5; i++)
            //{
            //    SlaveAxisAddresses.Add(new SlaveAxisAddress()
            //    {
            //        Id = i + 1,
            //        SlaveID = $"SlaveID-{i + 1}",
            //        AxisID = "AxisID-1"
            //    });
            //}

            iRet = ViewModelSet.CommunicationSet.SerialPortParameterSet_For_ScanSlaveAxisID();
            if (iRet != RET.SUCCEEDED)
            {
                ViewModelSet.Main?.ShowHintInfo("系统异常，请关闭系统后重试...");
                return;
            }

            //建立连接，接收事件注册
            iRet = ViewModelSet.CommunicationSet.OpenSerialPortConnection_For_ScanSlaveAxisID();
            if (iRet == RET.SUCCEEDED)
            {
                //SoftwareStateParameterSet.OpenConnectionFlag = true;                
                for (int i = 2; i < 3; i++)
                {
                    //回送测试
                    //EchoTest_For_ScanSlaveAxisID(i.ToString("X2"));
                    iRet = HexHelper.AddSerialPortTask(PageName.COMMUNICATIONSET, i.ToString("X2"), "00", TaskName.Test, FunctionCode.TEST_COMMUNICATION, null, TransmittingDataInfo);

                    //if (!AddSlaveAxisIDSet.Abc)
                    //{
                    //    SlaveAxisAddresses.Add(new SlaveAxisAddress()
                    //    {
                    //        Id = i + 1,
                    //        SlaveID = $"SlaveID-{i + 1}",
                    //        AxisID = "AxisID-1"
                    //    });

                    //    //ScanSlaveAxisIDFlag = false;
                    //    //CommunicationSet.SerialPortInfo.Close();
                    //}
                    //else
                    //{
                    //    AddSlaveAxisIDSet.Abc = false;
                    //    //ScanSlaveAxisIDFlag = true;
                    //    DelSlaveAxisAddress(i);
                    //}

                    //AddSlaveAxisIDSet.Abc = false;
                }               

                //this.Query();

                //AddSlaveAxisIDSet.GetSlaveIDList = new ObservableCollection<string>(AddSlaveAxisIDList.Select(q => q.SlaveID).ToArray());
            }
            else if (iRet == RET.ERROR)
            {
                ViewModelSet.Main?.ShowHintInfo("串口通信连接失败，端口可能被占用...");
            }
            //CommunicationSet.SerialPortInfo.Close();
        }

        //*************************************************************************
        //函数名称：EchoTest
        //函数功能：回传测试
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.11.04
        //*************************************************************************
        public void EchoTest_For_ScanSlaveAxisID(string slaveID)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> TransmittingDataInfo = new List<TransmitingDataInfoSet>();
            TransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = "9999" });

            //if (IsFirstTest == "true")
            //{
            //    SoftwareStateParameterSet.IsFirstEchoTest = true;
            //}
            //else
            //{
            //    SoftwareStateParameterSet.IsFirstEchoTest = false;
            //}

            //判断串口状态
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                ViewModelSet.Main?.ShowHintInfo("串口未开启，请稍后重试...");
                return;
            }

            //添加串口任务
            iRet = HexHelper.AddSerialPortTask(PageName.COMMUNICATIONSET, slaveID, SoftwareStateParameterSet.AxisID, TaskName.Test, FunctionCode.TEST_COMMUNICATION, null, TransmittingDataInfo);
            //if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            //{
            //    iRet = HexHelper.AddSerialPortTask(PageName.COMMUNICATIONSET, slaveID, SoftwareStateParameterSet.AxisID, TaskName.Test, FunctionCode.TEST_COMMUNICATION, null, TransmittingDataInfo);
            //}
            //else
            //{
            //    iRet = HexHelper.AddSerialPortTask(PageName.COMMUNICATIONSET, slaveID, SoftwareStateParameterSet.AxisID, TaskName.Test1, FunctionCode.TEST_COMMUNICATION, null, TransmittingDataInfo);
            //}
            if (iRet != RET.SUCCEEDED)
            {
                ViewModelSet.Main?.ShowHintInfo("系统异常，请关闭系统后重试...");
                return;
            }

            

            //开启任务管理与发送线程
            //if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
            //{
            //    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
               
            //    ScanSlaveAxisIDFlag = true;
            //}
            //else
            //{
            //    ScanSlaveAxisIDFlag = true;
            //}
        }
    }
}
