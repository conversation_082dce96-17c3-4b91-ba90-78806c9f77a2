﻿using Microsoft.Research.DynamicDataDisplay;
using Microsoft.Research.DynamicDataDisplay.Common;
using Microsoft.Research.DynamicDataDisplay.DataSources;
using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using ServoStudio.Models;
using ServoStudio.ViewModels;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace ServoStudio.Views
{   
    public partial class OscilloscopeView : UserControl
    {
        #region 公有字段
        public LineGraph lineGraph_Channel1 = new LineGraph();
        public LineGraph lineGraph_Channel2 = new LineGraph();
        public LineGraph lineGraph_Channel3 = new LineGraph();
        public LineGraph lineGraph_Channel4 = new LineGraph();
        #endregion

        #region 私有字段
        private List<ChannelInfo> lstChannelInfo = null;//通道信息
        private double dX_DynamicStart = 0;//x轴动态起点坐标
        private double dY_DynamicMin = 0;//y轴动态最小数据坐标
        private double dY_DynamicMax = 0;//y轴动态最大数据坐标          
        #endregion

        #region 构造函数
        public OscilloscopeView()
        {
            InitializeComponent();
            ViewModelSet.OscilloscopeView = this;
           
        }
        #endregion

        #region 方法  
        //*************************************************************************
        //函数名称：ClearOscilloscopeData
        //函数功能：清除示波器数据
        //
        //输入参数：NONE        
        //       
        //输出参数：0:NO_EFFECT
        //         1:SUCCEED
        //        -1:WRONG
        //
        //编码作者：Ryan
        //更新时间：2019.11.27
        //*************************************************************************
        public int ClearOscilloscopeData()
        {          
            try
            {
                dX_DynamicStart = 0;//x轴动态起点坐标
                dY_DynamicMax = 0;//y轴动态最大数据坐标

                if (lstChannelInfo != null)
                {
                    if (lstChannelInfo.Count != 0)
                    {
                        if (lstChannelInfo[1].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel1))
                                plotter.Children.Remove(lineGraph_Channel1);

                            if (plotter.Children.Contains(dataFollowChart_Channel1))
                                plotter.Children.Remove(dataFollowChart_Channel1);
                        }

                        if (lstChannelInfo[2].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel2))
                                plotter.Children.Remove(lineGraph_Channel2);

                            if (plotter.Children.Contains(dataFollowChart_Channel2))
                                plotter.Children.Remove(dataFollowChart_Channel2);
                        }

                        if (lstChannelInfo[3].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel3))
                                plotter.Children.Remove(lineGraph_Channel3);

                            if (plotter.Children.Contains(dataFollowChart_Channel3))
                                plotter.Children.Remove(dataFollowChart_Channel3);
                        }

                        if (lstChannelInfo[4].IsUsed == true)
                        {
                            if (plotter.Children.Contains(lineGraph_Channel4))
                                plotter.Children.Remove(lineGraph_Channel4);

                            if (plotter.Children.Contains(dataFollowChart_Channel4))
                                plotter.Children.Remove(dataFollowChart_Channel4);
                        }
                    }
                }
             
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_CLEAR_DATA, "ClearOscilloscopeData", ex);
                return RET.ERROR;
            }
        }
    
        //*************************************************************************
        //函数名称：OscilloscopeDataBinding
        //函数功能：示波器数据绑定
        //
        //输入参数：NONE        
        //       
        //输出参数：0:NO_EFFECT
        //         1:SUCCEED
        //        -1:WRONG
        //        
        //编码作者：Ryan
        //更新时间：2019.11.27
        //*************************************************************************
        private int OscilloscopeDataBinding()
        {
            int iRet = RET.NO_EFFECT;
            lstChannelInfo = new List<ChannelInfo>();

            try
            {
                if (ViewModelSet.Oscilloscope == null)
                {
                    return RET.NO_EFFECT;
                }
        
                for (int i = 0; i <= Oscilloscope.ChannelNumber; i++)
                {
                    ChannelInfo channelInfo = new ChannelInfo();
                    lstChannelInfo.Add(channelInfo);
                }

                if (ViewModelSet.Oscilloscope.SelectedSampleChannel1Index != 0)
                {
                    lstChannelInfo[1].IsUsed = true;

                    var ds_Channel1 = new EnumerableDataSource<OscilloscopePoint>(lstChannelInfo[1].Oscilloscope);
                    ds_Channel1.SetXMapping(x => x.Time);
                    ds_Channel1.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel1))
                    {
                        string strDiscription = ViewModelSet.Oscilloscope?.SelectedSamplingChannel1 + " " + ViewModelSet.Oscilloscope?.GetUnitByUiChannel(1);
                        lineGraph_Channel1 = plotter.AddLineGraph(ds_Channel1, Colors.Red, 2, strDiscription);
                        lineGraph_Channel1.DataSource = ds_Channel1;
                    }
                    else
                    {
                        lineGraph_Channel1.DataSource = ds_Channel1;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel1))
                    {   
                        plotter.Children.Add(dataFollowChart_Channel1);
                        dataFollowChart_Channel1.PointSource = lineGraph_Channel1;
                    }
                    else
                    {
                        dataFollowChart_Channel1.PointSource = lineGraph_Channel1;
                    }

                    iRet = RET.SUCCEEDED;
                }

                if (ViewModelSet.Oscilloscope.SelectedSampleChannel2Index != 0)
                {
                    lstChannelInfo[2].IsUsed = true;

                    var ds_Channel2 = new EnumerableDataSource<OscilloscopePoint>(lstChannelInfo[2].Oscilloscope);
                    ds_Channel2.SetXMapping(x => x.Time);
                    ds_Channel2.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel2))
                    {
                        string strDiscription = ViewModelSet.Oscilloscope.SelectedSamplingChannel2 + " " + ViewModelSet.Oscilloscope?.GetUnitByUiChannel(2);
                        lineGraph_Channel2 = plotter.AddLineGraph(ds_Channel2, Colors.Orange, 2, strDiscription);
                        lineGraph_Channel2.DataSource = ds_Channel2;
                    }
                    else
                    {
                        lineGraph_Channel2.DataSource = ds_Channel2;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel2))
                    {
                        plotter.Children.Add(dataFollowChart_Channel2);
                        dataFollowChart_Channel2.PointSource = lineGraph_Channel2;
                    }
                    else
                    {
                        dataFollowChart_Channel2.PointSource = lineGraph_Channel2;
                    }

                    iRet = RET.SUCCEEDED;
                }

                if (ViewModelSet.Oscilloscope.SelectedSampleChannel3Index != 0)
                {
                    lstChannelInfo[3].IsUsed = true;

                    var ds_Channel3 = new EnumerableDataSource<OscilloscopePoint>(lstChannelInfo[3].Oscilloscope);
                    ds_Channel3.SetXMapping(x => x.Time);
                    ds_Channel3.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel3))
                    {
                        string strDiscription = ViewModelSet.Oscilloscope.SelectedSamplingChannel3 + " " + ViewModelSet.Oscilloscope?.GetUnitByUiChannel(3);
                        lineGraph_Channel3 = plotter.AddLineGraph(ds_Channel3, Colors.Blue, 2, strDiscription);
                        lineGraph_Channel3.DataSource = ds_Channel3;
                    }
                    else
                    {
                        lineGraph_Channel3.DataSource = ds_Channel3;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel3))
                    {
                        plotter.Children.Add(dataFollowChart_Channel3);
                        dataFollowChart_Channel3.PointSource = lineGraph_Channel3;
                    }
                    else
                    {
                        dataFollowChart_Channel3.PointSource = lineGraph_Channel3;
                    }

                    iRet = RET.SUCCEEDED;
                }

                if (ViewModelSet.Oscilloscope.SelectedSampleChannel4Index != 0)
                {
                    lstChannelInfo[4].IsUsed = true;

                    var ds_Channel4 = new EnumerableDataSource<OscilloscopePoint>(lstChannelInfo[4].Oscilloscope);
                    ds_Channel4.SetXMapping(x => x.Time);
                    ds_Channel4.SetYMapping(y => y.Value);

                    if (!plotter.Children.Contains(lineGraph_Channel4))
                    {
                        string strDiscription = ViewModelSet.Oscilloscope.SelectedSamplingChannel4 + " " + ViewModelSet.Oscilloscope?.GetUnitByUiChannel(4);
                        lineGraph_Channel4 = plotter.AddLineGraph(ds_Channel4, Colors.Green, 2, strDiscription);
                        lineGraph_Channel4.DataSource = ds_Channel4;
                    }
                    else
                    {
                        lineGraph_Channel4.DataSource = ds_Channel4;
                    }

                    if (!plotter.Children.Contains(dataFollowChart_Channel4))
                    {
                        plotter.Children.Add(dataFollowChart_Channel4);
                        dataFollowChart_Channel4.PointSource = lineGraph_Channel4;
                    }
                    else
                    {
                        dataFollowChart_Channel4.PointSource = lineGraph_Channel4;
                    }

                    iRet = RET.SUCCEEDED;
                }
            }
            catch (System.Exception ex)
            {
                iRet = RET.ERROR;
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_DATA_BINDING, "OscilloscopeDataBinding", ex);
            }

            return iRet;
        }             

        //*************************************************************************
        //函数名称：DisplayOscilloscope
        //函数功能：示波器波形显示
        //
        //输入参数：NONE        
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.01.01&2021.10.10
        //*************************************************************************
        public void DisplayOscilloscope()
        {
            int iRet = -1;           
            Int64 iYMin = 0;
            Int64 iYMax = 0;
            double AcquisitionPeriod = 0;
            double AcquisitionDuration = 0;
           
            try
            {
                //获取采样周期与时长
                AcquisitionPeriod = OthersHelper.GetAcquisitionPeriod(ViewModelSet.Oscilloscope?.SelectedSamplingPeriod);
                AcquisitionDuration = Convert.ToDouble(ViewModelSet.Oscilloscope?.SelectedSamplingDuration.Replace("μs", "").Replace("ms", ""));

                this.plotter.Dispatcher.BeginInvoke(new Action(() =>
                {
                    //没有完成画图
                    AcquisitionInfoSet.IsDrawingCompleted = false;
                                     
                    //赋值
                    for (int iPoints = 0; iPoints < AcquisitionData.Channel1.Count; iPoints++)
                    {                       
                        //数据初始化与数据绑定
                        if (iPoints == 0)
                        {
                            ClearOscilloscopeData();
                            OscilloscopeDataBinding();
                        }

                        //通道数据赋值
                        if (lstChannelInfo[1].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden && AcquisitionData.Channel1.Count != 0)    //Lilbert添加 && AcquisitionData.Channel1.Count != 0 保护代码不崩溃
                        {
                                lstChannelInfo[1].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[1].YValue = AcquisitionData.Channel1[iPoints] * ViewModelSet.Oscilloscope.GetExchangeValueByUiChannel(1) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling);
                                lstChannelInfo[1].Oscilloscope.Add(new OscilloscopePoint(lstChannelInfo[1].XValue, lstChannelInfo[1].YValue));
                        }

                        if (lstChannelInfo[2].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden && AcquisitionData.Channel2.Count != 0)    //Lilbert添加 && AcquisitionData.Channel2.Count != 0 保护代码不崩溃
                        {
                            if (AcquisitionData.Channel2.Count > iPoints)          //由Lilbert添加if (AcquisitionData.Channel2.Count > iPoints)保护代码不崩溃
                            {
                                lstChannelInfo[2].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[2].YValue = AcquisitionData.Channel2[iPoints] * ViewModelSet.Oscilloscope.GetExchangeValueByUiChannel(2) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling);
                                lstChannelInfo[2].Oscilloscope.Add(new OscilloscopePoint(lstChannelInfo[2].XValue, lstChannelInfo[2].YValue));
                            }
                        }

                        if (lstChannelInfo[3].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden && AcquisitionData.Channel3.Count != 0)    //Lilbert添加 && AcquisitionData.Channel3.Count != 0 保护代码不崩溃
                        {
                            if (AcquisitionData.Channel3.Count > iPoints)          //由Lilbert添加if (AcquisitionData.Channel3.Count > iPoints)保护代码不崩溃
                            {
                                lstChannelInfo[3].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[3].YValue = AcquisitionData.Channel3[iPoints] * ViewModelSet.Oscilloscope.GetExchangeValueByUiChannel(3) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling);
                                lstChannelInfo[3].Oscilloscope.Add(new OscilloscopePoint(lstChannelInfo[3].XValue, lstChannelInfo[3].YValue));
                            }
                        }

                        if (lstChannelInfo[4].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden && AcquisitionData.Channel4.Count != 0)    //Lilbert添加 && AcquisitionData.Channel4.Count != 0 保护代码不崩溃
                        {
                            if (AcquisitionData.Channel4.Count > iPoints)          //由Lilbert添加if (AcquisitionData.Channel4.Count > iPoints)保护代码不崩溃
                            {
                                lstChannelInfo[4].XValue = iPoints * AcquisitionPeriod;
                                lstChannelInfo[4].YValue = AcquisitionData.Channel4[iPoints] * ViewModelSet.Oscilloscope.GetExchangeValueByUiChannel(4) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling);
                                lstChannelInfo[4].Oscilloscope.Add(new OscilloscopePoint(lstChannelInfo[4].XValue, lstChannelInfo[4].YValue));
                            }
                        }
                    }

                    //画图 
                    iRet = OscilloscopeModel.StaticYMaxminPoint(lstChannelInfo, ref iYMax, ref iYMin);
                    if (iRet == RET.SUCCEEDED)
                    {
                        plotter.Viewport.Visible = new System.Windows.Rect(-Oscilloscope.Vacant, iYMin - Oscilloscope.Vacant, AcquisitionDuration + Oscilloscope.Vacant * 2, iYMax - iYMin + Oscilloscope.Vacant * 2);
                    }

                    //完成画图
                    AcquisitionInfoSet.IsDrawingCompleted = true;                 
                }));

                //记录本次采集数据
                OscilloscopeModel.EvaluateLastWaveDataFromCurrent();

                //可以单位转换
                ViewModelSet.Main.IsUnitExchangedEnabled = true;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_ACQUISITION_DATA_DISPLAY, "DisplayOscilloscope", ex);        
            }
        }

        //*************************************************************************
        //函数名称：DisplayOscilloscopeLoop
        //函数功能：示波器波形循环
        //
        //输入参数：NONE        
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.01.01
        //*************************************************************************
        public void DisplayOscilloscopeLoop()
        {
            try
            {
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadSwitch = true;
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadPause = false;
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadDispose = false;
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadWorking = true;
                PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes = 0;
                ThreadPool.QueueUserWorkItem(PthreadStatement.MicrosecondsOscilloscopeDrawing.Run, null);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_DISPLAY_OSCILLOSCOPE_LOOP, "DisplayOscilloscopeLoop", ex);
            }
        }

        //*************************************************************************
        //函数名称：TimerCallBack
        //函数功能：时钟回调
        //
        //输入参数：NONE        
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.01.01
        //*************************************************************************
        public void TimerCallBack(object sender, long JumpPeriod, long interval)
        {
            double AcquisitionPeriod = 0;
            double Channel1Exchange = 1;
            double Channel2Exchange = 1;
            double Channel3Exchange = 1;
            double Channel4Exchange = 1;

            double Channel1Doubling = 1;
            double Channel2Doubling = 1;
            double Channel3Doubling = 1;
            double Channel4Doubling = 1;

            try
            {
                for (int i = 0; i < AcquisitionInfoSet.lstExchangeValue.Count; i++)
                {
                    switch (i)
                    {
                        case 0:
                            Channel1Exchange = AcquisitionInfoSet.lstExchangeValue[i];
                            Channel1Doubling = Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        case 1:
                            Channel2Exchange = AcquisitionInfoSet.lstExchangeValue[i];
                            Channel2Doubling = Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        case 2:
                            Channel3Exchange = AcquisitionInfoSet.lstExchangeValue[i];
                            Channel3Doubling = Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        case 3:
                            Channel4Exchange = AcquisitionInfoSet.lstExchangeValue[i];
                            Channel4Doubling = Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[i].Doubling);
                            break;
                        default:
                            break;
                    }
                }
                
                //更新前台数据               
                this.plotter.Dispatcher.BeginInvoke(new Action(() =>
                {                   
                    //没有完成画图
                    AcquisitionInfoSet.IsDrawingCompleted = false;

                    //获取采样周期
                    AcquisitionPeriod = OthersHelper.GetAcquisitionPeriod(ViewModelSet.Oscilloscope.SelectedSamplingPeriod);

                    //初始化
                    if (PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes == 0)
                    {
                        ClearOscilloscopeData();
                        OscilloscopeDataBinding();
                    }

                    //通道赋值        
                    if (lstChannelInfo[1].IsUsed == true && AcquisitionData.Channel1.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {
                        lstChannelInfo[1].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[1].YValue = AcquisitionData.Channel1[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel1Exchange * Channel1Doubling;
                    }

                    if (lstChannelInfo[2].IsUsed == true && AcquisitionData.Channel2.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {

                        lstChannelInfo[2].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[2].YValue = AcquisitionData.Channel2[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel2Exchange * Channel2Doubling;
                    }

                    if (lstChannelInfo[3].IsUsed == true && AcquisitionData.Channel3.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {
                        lstChannelInfo[3].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[3].YValue = AcquisitionData.Channel3[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel3Exchange * Channel3Doubling;
                    }

                    if (lstChannelInfo[4].IsUsed == true && AcquisitionData.Channel4.Count > PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes)
                    {
                        lstChannelInfo[4].XValue = PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes * AcquisitionPeriod;
                        lstChannelInfo[4].YValue = AcquisitionData.Channel4[PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes] * Channel4Exchange * Channel4Doubling;
                    }

                    PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadCallBackTimes++;
                                 
                    //获取动态坐标数据
                    OscilloscopeModel.DynamicXStartPoint(lstChannelInfo, ref dX_DynamicStart);
                    OscilloscopeModel.DynamicYMaxPoint(lstChannelInfo, ref dY_DynamicMax, ref dY_DynamicMin);

                    //示波器赋值
                    for (int iChannelNumber = 1; iChannelNumber <= Oscilloscope.ChannelNumber; iChannelNumber++)
                    {
                        if (lstChannelInfo[iChannelNumber].IsUsed == true)
                        {
                            double x = lstChannelInfo[iChannelNumber].XValue;
                            double y = lstChannelInfo[iChannelNumber].YValue;
                            lstChannelInfo[iChannelNumber].Oscilloscope.Add(new OscilloscopePoint(x, y));
                        }
                    }

                    //画图
                    plotter.Viewport.Visible = new System.Windows.Rect(dX_DynamicStart, dY_DynamicMin, Oscilloscope.DynamicDisplayPoint * OthersHelper.GetAcquisitionPeriod(ViewModelSet.Oscilloscope.SelectedSamplingPeriod), dY_DynamicMax - dY_DynamicMin);
                }));
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("TimerCallBack", ex);
            }
        }

        //*************************************************************************
        //函数名称：DisplayOscilloscopeImport
        //函数功能：导入的波形展示
        //
        //输入参数：NONE        
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2019.11.27
        //*************************************************************************
        public void DisplayOscilloscopeImport()
        {
            int iRet = -1;
            double AcquisitionPeriod = 0;
            double AcquisitionDuration = 0;
            Int64 iYMin = 0;
            Int64 iYMax = 0;

            try
            {
                //获取采样周期与时长
                AcquisitionPeriod = OthersHelper.GetAcquisitionPeriod(ViewModelSet.Oscilloscope?.SelectedSamplingPeriod);
                AcquisitionDuration = Convert.ToDouble(ViewModelSet.Oscilloscope?.SelectedSamplingDuration.Replace("μs", "").Replace("ms", ""));

                //清除数据
                iRet = ClearOscilloscopeData();
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //数据绑定
                iRet = OscilloscopeDataBinding();
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //数据展示
                for (int i = 0; i < AcquisitionData.Channel1.Count; i++)
                {                   
                    if (lstChannelInfo[1].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                    {
                        lstChannelInfo[1].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.Oscilloscope.SelectedSamplingPeriod);
                        lstChannelInfo[1].YValue = AcquisitionData.Channel1[i] * ViewModelSet.Oscilloscope.GetExchangeValueByUiChannel(1) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling);
                        lstChannelInfo[1].Oscilloscope.Add(new OscilloscopePoint(lstChannelInfo[1].XValue, lstChannelInfo[1].YValue));
                    }

                    if (lstChannelInfo[2].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                    {
                        lstChannelInfo[2].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.Oscilloscope.SelectedSamplingPeriod);
                        lstChannelInfo[2].YValue = AcquisitionData.Channel2[i] * ViewModelSet.Oscilloscope.GetExchangeValueByUiChannel(2) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling);
                        lstChannelInfo[2].Oscilloscope.Add(new OscilloscopePoint(lstChannelInfo[2].XValue, lstChannelInfo[2].YValue));
                    }

                    if (lstChannelInfo[3].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                    {
                        lstChannelInfo[3].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.Oscilloscope.SelectedSamplingPeriod);
                        lstChannelInfo[3].YValue = AcquisitionData.Channel3[i] * ViewModelSet.Oscilloscope.GetExchangeValueByUiChannel(3) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling);
                        lstChannelInfo[3].Oscilloscope.Add(new OscilloscopePoint(lstChannelInfo[3].XValue, lstChannelInfo[3].YValue));
                    }

                    if (lstChannelInfo[4].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                    {
                        lstChannelInfo[4].XValue = i * OthersHelper.GetAcquisitionPeriod(ViewModelSet.Oscilloscope.SelectedSamplingPeriod);
                        lstChannelInfo[4].YValue = AcquisitionData.Channel4[i] * ViewModelSet.Oscilloscope.GetExchangeValueByUiChannel(4) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling);
                        lstChannelInfo[4].Oscilloscope.Add(new OscilloscopePoint(lstChannelInfo[4].XValue, lstChannelInfo[4].YValue));
                    }
                }

                //画图 
                iRet = OscilloscopeModel.StaticYMaxminPoint(lstChannelInfo, ref iYMax, ref iYMin);
                if (iRet == RET.SUCCEEDED)
                {
                    plotter.Viewport.Visible = new System.Windows.Rect(-Oscilloscope.Vacant, iYMin - Oscilloscope.Vacant, AcquisitionDuration + Oscilloscope.Vacant * 2, iYMax - iYMin + Oscilloscope.Vacant * 2);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_TURN_ON, "DisplayOscilloscopeImport", ex);
            }
        }

        //*************************************************************************
        //函数名称：plotter_MouseLeftButtonUp
        //函数功能：计算
        //
        //输入参数：NONE        
        //       
        //输出参数：NONE
        //
        //编码作者：Ryan
        //更新时间：2019.12.02
        //*************************************************************************              
        private void plotter_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            string strIndex = null;
            string strTime = null;

            double dCH1Value = 0;
            double dCH2Value = 0;
            double dCH3Value = 0;
            double dCH4Value = 0;

            int[] arrMax = new int[4];
            int[] arrMin = new int[4];
            double[] arrAverage = new double[4];
            double[] arrRMS = new double[4];
            OsilloscopeCalculateSet clsData = null;

            int iMistake = 0;
            try
            {
                #region 获取当前数据
                if (ViewModelSet.Oscilloscope == null || lstChannelInfo == null)
                {
                    return;
                }

                ViewModelSet.Oscilloscope.OsilloscopeCalculate = new ObservableCollection<OsilloscopeCalculateSet>();          
                if (lstChannelInfo[1].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel1.MarkerPosition.X, 2));
                    strIndex = OscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel1.MarkerPosition.X, 2));
                }
                else if (lstChannelInfo[2].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel2.MarkerPosition.X, 2));
                    strIndex = OscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel2.MarkerPosition.X, 2));
                }
                else if (lstChannelInfo[3].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel3.MarkerPosition.X, 2));
                    strIndex = OscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel3.MarkerPosition.X, 2));
                }
                else if (lstChannelInfo[4].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    strTime = Convert.ToString(Math.Round(dataFollowChart_Channel4.MarkerPosition.X, 2));
                    strIndex = OscilloscopeModel.GetIndexOfArrayWaveData(Math.Round(dataFollowChart_Channel4.MarkerPosition.X, 2));
                }
                else
                {
                    for (int i = 0; i <= 7; i++)
                    {
                        OsilloscopeCalculateSet clsTemp = new OsilloscopeCalculateSet();
                        switch (i)
                        {
                            case 0:
                                clsTemp.Title = "当前光标";
                                break;
                            case 1:
                                clsTemp.Title = "上一个光标";
                                break;
                            case 2:
                                clsTemp.Title = "光标间距";
                                break;
                            case 3:
                                clsTemp.Title = "区间最大值";
                                break;
                            case 4:
                                clsTemp.Title = "区间最小值";
                                break;
                            case 5:
                                clsTemp.Title = "区间峰峰值";
                                break;
                            case 6:
                                clsTemp.Title = "区间平均值";
                                break;
                            case 7:
                                clsTemp.Title = "区间均方根";
                                break;
                            default:
                                break;
                        }

                        ViewModelSet.Oscilloscope.OsilloscopeCalculate.Add(clsTemp);
                    }

                    return;
                }
             
                if (lstChannelInfo[1].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    dCH1Value = Math.Round(dataFollowChart_Channel1.MarkerPosition.Y, 2);
                }              

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    dCH2Value = Math.Round(dataFollowChart_Channel2.MarkerPosition.Y, 2);
                }
                
                if (lstChannelInfo[3].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    dCH3Value = Math.Round(dataFollowChart_Channel3.MarkerPosition.Y, 2);
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    dCH4Value = Math.Round(dataFollowChart_Channel4.MarkerPosition.Y, 2);
                }

                clsData = OscilloscopeModel.AddCalculateSet("当前光标", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value);
                ViewModelSet.Oscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 获取上一条数据
                iMistake = 1;
                strTime = Convert.ToString(ViewModelSet.Oscilloscope.dLastTime);
                strIndex = Convert.ToString(ViewModelSet.Oscilloscope.iIndex);
                clsData = OscilloscopeModel.AddCalculateSet("上一个光标", strIndex, strTime, ViewModelSet.Oscilloscope.dLastCH1Value, ViewModelSet.Oscilloscope.dLastCH2Value, ViewModelSet.Oscilloscope.dLastCH3Value, ViewModelSet.Oscilloscope.dLastCH4Value);
                ViewModelSet.Oscilloscope.OsilloscopeCalculate.Add(clsData);

                ViewModelSet.Oscilloscope.dLastTime = Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].AcquisitionTime);
                ViewModelSet.Oscilloscope.iIndex = Convert.ToInt32(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Number);
                ViewModelSet.Oscilloscope.dLastCH1Value = Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Channel1Value);
                ViewModelSet.Oscilloscope.dLastCH2Value = Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Channel2Value);
                ViewModelSet.Oscilloscope.dLastCH3Value = Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Channel3Value);
                ViewModelSet.Oscilloscope.dLastCH4Value = Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Channel4Value);
                #endregion

                #region 光标距离
                iMistake = 2;
                strTime = Convert.ToString(Math.Round(Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].AcquisitionTime) - Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[1].AcquisitionTime), 2));
                strIndex = Convert.ToString(Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Number) - Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[1].Number));

                if (lstChannelInfo[1].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    dCH1Value = Math.Round(Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Channel1Value) - Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[1].Channel1Value), 2);
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    dCH2Value = Math.Round(Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Channel2Value) - Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[1].Channel2Value), 2);
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    dCH3Value = Math.Round(Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Channel3Value) - Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[1].Channel3Value), 2);
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    dCH4Value = Math.Round(Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Channel4Value) - Convert.ToDouble(ViewModelSet.Oscilloscope.OsilloscopeCalculate[1].Channel4Value), 2);
                }

                clsData = OscilloscopeModel.AddCalculateSet("光标间距", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value);
                ViewModelSet.Oscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 最大值
                iMistake = 3;
                arrMax = OscilloscopeModel.GetMaxminOfArrayWaveData(true, ViewModelSet.Oscilloscope.OsilloscopeCalculate[1].Number, ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Number);

                if (lstChannelInfo[1].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH1Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH1Value = arrMax[0];
                        }
                        else
                        {
                            dCH1Value = Math.Round(arrMax[0] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling) * AcquisitionInfoSet.lstExchangeValue[0], 2);
                        }
                    }                                  
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH2Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH2Value = arrMax[1];
                        }
                        else
                        {
                            dCH2Value = Math.Round(arrMax[1] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling) * AcquisitionInfoSet.lstExchangeValue[1], 2);
                        }
                    }                                  
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH3Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH3Value = arrMax[2];
                        }
                        else
                        {
                            dCH3Value = Math.Round(arrMax[2] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling) * AcquisitionInfoSet.lstExchangeValue[2], 2);
                        }
                    }                                 
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (arrMax == null)
                    {
                        dCH4Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH4Value = arrMax[3];
                        }
                        else
                        {
                            dCH4Value = Math.Round(arrMax[3] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling) * AcquisitionInfoSet.lstExchangeValue[3], 2);
                        }
                    }          
                }

                clsData = OscilloscopeModel.AddCalculateSet("区间最大值", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value);
                ViewModelSet.Oscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 最小值
                iMistake = 4;
                arrMin = OscilloscopeModel.GetMaxminOfArrayWaveData(false, ViewModelSet.Oscilloscope.OsilloscopeCalculate[1].Number, ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Number);

                if (lstChannelInfo[1].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH1Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH1Value = arrMin[0];
                        }
                        else
                        {
                            dCH1Value = Math.Round(arrMin[0] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling) * AcquisitionInfoSet.lstExchangeValue[0], 2);
                        }
                    }                       
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH2Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH2Value = arrMin[1];
                        }
                        else
                        {
                            dCH2Value = Math.Round(arrMin[1] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling) * AcquisitionInfoSet.lstExchangeValue[1], 2);
                        }
                    }       
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH3Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH3Value = arrMin[2];
                        }
                        else
                        {
                            dCH3Value = Math.Round(arrMin[2] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling) * AcquisitionInfoSet.lstExchangeValue[2], 2);
                        }
                    }   
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (arrMin == null)
                    {
                        dCH4Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH4Value = arrMin[3];
                        }
                        else
                        {
                            dCH4Value = Math.Round(arrMin[3] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling) * AcquisitionInfoSet.lstExchangeValue[3], 2);
                        }
                    }   
                }

                clsData = OscilloscopeModel.AddCalculateSet("区间最小值", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value);
                ViewModelSet.Oscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 峰峰值
                iMistake = 5;
                if (lstChannelInfo[1].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH1Value = arrMax[0] - arrMin[0];
                        }
                        else
                        {
                            dCH1Value = Math.Round((arrMax[0] - arrMin[0]) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling) * AcquisitionInfoSet.lstExchangeValue[0], 2);
                        }
                    }
                    else
                    {
                        dCH1Value = 0;
                    }
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH2Value = arrMax[1] - arrMin[1];
                        }
                        else
                        {
                            dCH2Value = Math.Round((arrMax[1] - arrMin[1]) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling) * AcquisitionInfoSet.lstExchangeValue[1], 2);
                        }
                    }
                    else
                    {
                        dCH2Value = 0;
                    }  
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH3Value = arrMax[2] - arrMin[2];
                        }
                        else
                        {
                            dCH3Value = Math.Round((arrMax[2] - arrMin[2]) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling) * AcquisitionInfoSet.lstExchangeValue[2], 2);
                        }
                    }
                    else
                    {
                        dCH3Value = 0;
                    }
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (arrMax != null && arrMin != null)
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH4Value = arrMax[3] - arrMin[3];
                        }
                        else
                        {
                            dCH4Value = Math.Round((arrMax[3] - arrMin[3]) * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling) * AcquisitionInfoSet.lstExchangeValue[3], 2);
                        }
                    }
                    else
                    {
                        dCH4Value = 0;
                    }                                   
                }

                clsData = OscilloscopeModel.AddCalculateSet("区间峰峰值", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value);
                ViewModelSet.Oscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 平均值
                iMistake = 6;
                arrAverage = OscilloscopeModel.GetAverageOfArrayWaveData(ViewModelSet.Oscilloscope.OsilloscopeCalculate[1].Number, ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Number);

                if (lstChannelInfo[1].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH1Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH1Value = arrAverage[0];
                        }
                        else
                        {
                            dCH1Value = Math.Round(arrAverage[0] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling) * AcquisitionInfoSet.lstExchangeValue[0], 2);
                        }
                    }
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH2Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH2Value = arrAverage[1];
                        }
                        else
                        {
                            dCH2Value = Math.Round(arrAverage[1] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling) * AcquisitionInfoSet.lstExchangeValue[1], 2);
                        }
                    }
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH3Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH3Value = arrAverage[2];
                        }
                        else
                        {
                            dCH3Value = Math.Round(arrAverage[2] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling) * AcquisitionInfoSet.lstExchangeValue[2], 2);
                        }
                    }
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (arrAverage == null)
                    {
                        dCH4Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH4Value = arrAverage[3];
                        }
                        else
                        {
                            dCH4Value = Math.Round(arrAverage[3] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling) * AcquisitionInfoSet.lstExchangeValue[3], 2);
                        }
                    }
                }

                clsData = OscilloscopeModel.AddCalculateSet("区间平均值", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value);
                ViewModelSet.Oscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion

                #region 均方根
                iMistake = 7;
                arrRMS = OscilloscopeModel.GetRMSOfArrayWaveData(ViewModelSet.Oscilloscope.OsilloscopeCalculate[1].Number, ViewModelSet.Oscilloscope.OsilloscopeCalculate[0].Number);

                if (lstChannelInfo[1].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH1Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH1Value = arrRMS[0];
                        }
                        else
                        {
                            dCH1Value = Math.Round(arrRMS[0] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[0].Doubling) * AcquisitionInfoSet.lstExchangeValue[0], 2);
                        }
                    }
                }

                if (lstChannelInfo[2].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH2Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH2Value = arrRMS[1];
                        }
                        else
                        {
                            dCH2Value = Math.Round(arrRMS[1] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[1].Doubling) * AcquisitionInfoSet.lstExchangeValue[1], 2);
                        }
                    }
                }

                if (lstChannelInfo[3].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH3Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH3Value = arrRMS[2];
                        }
                        else
                        {
                            dCH3Value = Math.Round(arrRMS[2] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[2].Doubling) * AcquisitionInfoSet.lstExchangeValue[2], 2);
                        }
                    }
                }

                if (lstChannelInfo[4].IsUsed && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (arrRMS == null)
                    {
                        dCH4Value = 0;
                    }
                    else
                    {
                        string strDouble = ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling;
                        if (string.IsNullOrEmpty(strDouble))
                        {
                            dCH4Value = arrRMS[3];
                        }
                        else
                        {
                            dCH4Value = Math.Round(arrRMS[3] * Convert.ToDouble(ViewModelSet.Oscilloscope.OscilloscopeProperty[3].Doubling) * AcquisitionInfoSet.lstExchangeValue[3], 2);
                        }
                    }
                }

                clsData = OscilloscopeModel.AddCalculateSet("区间均方根", strIndex, strTime, dCH1Value, dCH2Value, dCH3Value, dCH4Value);
                ViewModelSet.Oscilloscope.OsilloscopeCalculate.Add(clsData);
                #endregion
            }
            catch (System.Exception ex)
            {
                string strInfo = "IMistake: " + iMistake + " ";
                strInfo += "Index: " + strIndex + " ";
                strInfo += "Time: " + strTime + " ";

                strInfo += "Channel1.MarkerPosition.X: " + dataFollowChart_Channel1.MarkerPosition.X + " ";
                strInfo += "Channel1.MarkerPosition.Y: " + dataFollowChart_Channel1.MarkerPosition.Y + " ";

                strInfo += "CH1Value: " + dCH1Value + " ";
                strInfo += "CH2Value: " + dCH2Value + " ";
                strInfo += "CH3Value: " + dCH3Value + " ";
                strInfo += "CH4Value: " + dCH4Value + " ";

                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_PLOTTER_MOUSE_LEFT_BUTTON_UP, "plotter_MouseLeftButtonUp-" + strInfo, ex);
            }        
        }
        #endregion
    }

    public class OscilloscopePoint
    {
        public double Time { get; set; }
        public double Value { get; set; }

        public OscilloscopePoint(double time, double value)
        {
            Time = time;
            Value = value;
        }
    }

    public class OscilloscopePointCollection : RingArray<OscilloscopePoint>
    {
        private const int TOTAL_POINTS = Oscilloscope.Collection;
        public OscilloscopePointCollection() : base(TOTAL_POINTS) { }
    }

    public class ChannelInfo
    {
        public OscilloscopePointCollection Oscilloscope = new OscilloscopePointCollection();//数据环   
        public Queue YValueQueue= new Queue();//Y轴数据队列
       
        public double XValue = 0;//x轴数据
        public double YValue = 0;//y轴数据
        public bool IsUsed = false;//通道是否使用
    }
}
