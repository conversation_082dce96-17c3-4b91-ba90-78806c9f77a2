﻿<UserControl x:Class="ServoStudio.Views.TorqueView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:Views="clr-namespace:ServoStudio.Views"
             mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="30"/>
            <ColumnDefinition Width ="Auto"/>
        </Grid.ColumnDefinitions>

        <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="3"  Grid.Column="0" Height="150" Width="Auto" HorizontalAlignment="Left" ShowMenu="False" Source="pack://application:,,,/ServoStudio;component/Resource/Torque.png" ShowBorder="False" Opacity="0.65"/>
    
        <Label Grid.Row="0" Grid.Column="1"  Background="Black" Width="10" Height="10" Margin="5"/>
        <Label Grid.Row="1" Grid.Column="1"  Background="Red" Width="10" Height="10" Margin="5"/>

        <Label Grid.Row="0" Grid.Column="2"  Margin="5" Content="Target Torque：目标转矩" FontSize="10pt"/>
        <Label Grid.Row="1" Grid.Column="2"  Margin="5" Content="T.Acc：转矩斜坡" FontSize="10pt"/>
    </Grid>
</UserControl>
