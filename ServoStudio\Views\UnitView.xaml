﻿<UserControl x:Class="ServoStudio.Views.UnitView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:UnitViewModel}"
              d:DesignHeight="800" d:DesignWidth="1300">
    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding UnitLoadedCommand}" EventName="Loaded"/>
        <!--<dxmvvm:EventToCommand Command="{Binding UnitUnloadedCommand}" EventName="Unloaded"/>-->
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

        <Grid Margin="10">
            <TabControl Name="tabControl" SelectedIndex="{Binding SelectedTabIndex}" Grid.Row="0" Padding="0" BorderBrush="LightGray" Background="{x:Null}" BorderThickness="0,1,0,0">
                <TabItem Header="单位设置" TabIndex="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="260"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="4" Margin="3,10" Style="{StaticResource LabelStyle}" Content="单位设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="位置单位" Style="{StaticResource LabelStyle}" />
                        <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Margin="10,9"  Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding PositionUnit}" SelectedItem="{Binding SelectedPositionUnit, Mode=TwoWay}"/>

                        <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="转矩单位" Style="{StaticResource LabelStyle}"/>
                        <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding TorqueUnit}" SelectedItem="{Binding SelectedTorqueUnit, Mode=TwoWay}"/>

                        <Label Grid.Row="3" Grid.Column="1" Margin="10,9" Content="速度单位" Style="{StaticResource LabelStyle}"/>
                        <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding SpeedUnit}" SelectedItem="{Binding SelectedSpeedUnit, Mode=TwoWay}"/>

                        <Label Grid.Row="4" Grid.Column="1" Margin="10,9" Content="加速单位" Style="{StaticResource LabelStyle}"/>
                        <dxe:ComboBoxEdit Grid.Row="4" Grid.Column="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding AccelerationUnit}" SelectedItem="{Binding SelectedAccelerationUnit, Mode=TwoWay}"/>

                        <Label Grid.Row="5" Grid.Column="1"  Grid.ColumnSpan="3" Margin="10,9" Content="提示：单位设置完成后，请点击 [确认设置] 按钮" Style="{StaticResource LabelStyle}" Foreground="Red"/>

                        <Label Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="4" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <StackPanel Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="4" HorizontalAlignment="Right" Orientation="Horizontal">
                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding SetDefaultUnitCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="默认单位" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" Command="{Binding SetSelectedUnitCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="确定设置" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>
                        </StackPanel>

                        <Grid Grid.Row="9" Grid.Column="0" Grid.ColumnSpan="4">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="Auto"/>

                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                            <Label Grid.Row="1" Grid.Column="0" Content="1.单位设置" Style="{StaticResource LabelStyle}"/>
                            <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                            <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                            <Label Grid.Row="1" Grid.Column="2" Content="2.限幅保护" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                            <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                            <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                            <Label Grid.Row="1" Grid.Column="4" Content="3.一般设定" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                            <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                            <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                            <Label Grid.Row="1" Grid.Column="6" Content="4.数字IO" Foreground="Gray" Style="{StaticResource LabelStyle}"/>

                            <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                        <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
                        <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

                        <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                        <Label Grid.Row="1" Grid.Column="2" Content="2.电机反馈" Style="{StaticResource LabelStyle}"/>
                        <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

                        <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                        <Label Grid.Row="1" Grid.Column="4" Content="3.单位设置" Style="{StaticResource LabelStyle}"/>
                        <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                        <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                        <Label Grid.Row="1" Grid.Column="6" Content="4.限幅保护" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                        <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                        <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                        <Label Grid.Row="1" Grid.Column="8" Content="5.一般设定" Foreground="Gray" Style="{StaticResource LabelStyle}"/>-->

                            <!--<dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="17" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Backward_32x32.png}" ShowBorder="False">
                            <dxmvvm:Interaction.Behaviors>
                                <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding MotorFeedbackNavigationCommand}"/>
                            </dxmvvm:Interaction.Behaviors>
                        </dxe:ImageEdit>
                        <Label Grid.Row="1" Grid.Column="18" FontSize="12" Content="上页" Style="{StaticResource LabelStyle}"/>-->

                            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="19" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Forward_32x32.png}" ShowBorder="False">
                                <dxmvvm:Interaction.Behaviors>
                                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding LimitAmplitudeNavigationCommand}"/>
                                </dxmvvm:Interaction.Behaviors>
                            </dxe:ImageEdit>
                            <Label Grid.Column="20" FontSize="12" Content="下页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                        </Grid>
                    </Grid>
                </TabItem>

                <TabItem Header="电子齿轮比" TabIndex="1">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding GearRatioAlreadySetCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Label Grid.Row="0" Margin="3,10" Style="{StaticResource LabelStyle}" Content="齿轮比图示" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <dxe:ImageEdit Grid.Row="1" Height="190" Width ="Auto"  HorizontalAlignment="Left" Margin="30,10" ShowMenu="False" Source="pack://application:,,,/ServoStudio;component/Resource/Gear.png" ShowBorder="False" Opacity="0.8"/>

                        <Label Grid.Row="2" Margin="3,10" Style="{StaticResource LabelStyle}" Content="齿轮比设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Grid Grid.Row ="3">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="260"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="2" Foreground="Red" Margin="10,9" Content="{Binding GearRatioHint}" ContentStringFormat="Gear Ratio = 电机分辨率/负载轴分辨率 = {0}"  Style="{StaticResource LabelStyle}"/>

                            <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="电机分辨率" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="1" Grid.Column="2" Style="{StaticResource TextBoxStyle}" Margin="10,9"  Text="{Binding MotorRevolutions,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>

                            <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="负载轴分辨率" Style="{StaticResource LabelStyle}"/>
                            <TextBox Grid.Row="2" Grid.Column="2" Style="{StaticResource TextBoxStyle}" Margin="10,9"  Text="{Binding LoadShaftRevolutions,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>

                        <Label Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="6" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <StackPanel Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="6" HorizontalAlignment="Right" Orientation="Horizontal">
                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadUnitParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultUnitParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Download_16x16.png}" Command="{Binding WriteUnitParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>
                        </StackPanel>

                        <Grid Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="6">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="Auto"/>

                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                            <Label Grid.Row="1" Grid.Column="0" Content="1.单位设置" Style="{StaticResource LabelStyle}"/>
                            <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                            <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                            <Label Grid.Row="1" Grid.Column="2" Content="2.限幅保护" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                            <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                            <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                            <Label Grid.Row="1" Grid.Column="4" Content="3.一般设定" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                            <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                            <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                            <Label Grid.Row="1" Grid.Column="6" Content="4.数字IO" Foreground="Gray" Style="{StaticResource LabelStyle}"/>

                            <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                        <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
                        <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

                        <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                        <Label Grid.Row="1" Grid.Column="2" Content="2.电机反馈" Style="{StaticResource LabelStyle}"/>
                        <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

                        <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                        <Label Grid.Row="1" Grid.Column="4" Content="3.单位设置" Style="{StaticResource LabelStyle}"/>
                        <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                        <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                        <Label Grid.Row="1" Grid.Column="6" Content="4.限幅保护" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                        <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                        <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                        <Label Grid.Row="1" Grid.Column="8" Content="5.一般设定" Foreground="Gray" Style="{StaticResource LabelStyle}"/>-->

                            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="12" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                                <dxmvvm:Interaction.Behaviors>
                                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetUnitConfigFileCommand}"/>
                                </dxmvvm:Interaction.Behaviors>
                            </dxe:ImageEdit>
                            <Label Grid.Column="13" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2" />

                            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="14" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                                <dxmvvm:Interaction.Behaviors>
                                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SaveUnitConfigFileCommand}"/>
                                </dxmvvm:Interaction.Behaviors>
                            </dxe:ImageEdit>
                            <Label Grid.Column="16" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                            <!--<dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="17" Margin="20,0,5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Backward_32x32.png}" ShowBorder="False">
                            <dxmvvm:Interaction.Behaviors>
                                <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding MotorFeedbackNavigationCommand}"/>
                            </dxmvvm:Interaction.Behaviors>
                        </dxe:ImageEdit>
                        <Label Grid.Row="1" Grid.Column="18" FontSize="12" Content="上页" Style="{StaticResource LabelStyle}"/>-->

                            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="19" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Forward_32x32.png}" ShowBorder="False">
                                <dxmvvm:Interaction.Behaviors>
                                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding LimitAmplitudeNavigationCommand}"/>
                                </dxmvvm:Interaction.Behaviors>
                            </dxe:ImageEdit>
                            <Label Grid.Column="20" FontSize="12" Content="下页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                        </Grid>
                    </Grid>
                </TabItem>
            </TabControl>
        </Grid>

    </ScrollViewer>
        
</UserControl>
