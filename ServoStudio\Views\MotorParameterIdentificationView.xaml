﻿<UserControl x:Class="ServoStudio.Views.MotorParameterIdentificationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:dxrt="http://schemas.devexpress.com/winfx/2008/xaml/ribbon/themekeys"             
             xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
             xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
             xmlns:dxnt="http://schemas.devexpress.com/winfx/2008/xaml/navbar/themekeys"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
             xmlns:dxn="http://schemas.devexpress.com/winfx/2008/xaml/navbar"
             xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"  
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             mc:Ignorable="d"
             xmlns:converter="clr-namespace:Converter"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:MotorParameterIdentificationViewModel}">
    
    <UserControl.Resources>
        <converter:BackgroundConverter x:Key="ColorConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>

        <Style x:Key="myLabelStyle" TargetType="Label">
            <Setter Property="FontFamily" Value="Microsoft YaHei"/>
            <Setter Property="FontSize" Value="9pt"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Margin" Value="3"/>
            <Setter Property="FontStyle" Value="Normal"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Trigger.Setters>
                        <Setter Property="FontStyle" Value="Italic"/>
                        <Setter Property="Foreground" Value="Green"/>
                    </Trigger.Setters>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding MotorParameterIdentificationLoadedCommand}" EventName="Loaded"/>
        <dxmvvm:EventToCommand Command="{Binding MotorParameterIdentificationUnloadedCommand}" EventName="Unloaded"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="90"/>
                <ColumnDefinition Width="160"/>
                <ColumnDefinition Width="160"/>
                <ColumnDefinition Width="160"/>
                <ColumnDefinition Width="160"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="5"  Orientation="Horizontal" Margin="0,0,0,10">
                <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Separator_16x16.png}" Command="{Binding ParameterIdentificationSwitchCommand}" Margin="0,0,6,0" Width="95">
                    <Label Content="{Binding ParameterIdentificationSwitchHint}" Style="{StaticResource LabelStyle}" Margin="0" />
                </dx:SimpleButton>

                <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/ReversSort_16x16.png" Command="{Binding MotorParameterIdentificationModifyCommand}" Margin="0,0,6,0" Width="95" IsEnabled="{Binding ModifyParameterButtonEnabled}">
                    <Label Content="参数替换" Style="{StaticResource LabelStyle}" Margin="0" />
                </dx:SimpleButton>
            </StackPanel>

            <Border Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="5" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

            <Label Grid.Row="2" Grid.Column="0" Content="选中替换" Style="{StaticResource LabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="2" Grid.Column="1" Content="参数名称" Style="{StaticResource LabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="2" Grid.Column="2" Content="参数单位" Style="{StaticResource LabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="2" Grid.Column="3" Content="当前参数值" Style="{StaticResource LabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="2" Grid.Column="4" Content="辨识参数值" Style="{StaticResource LabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>

            <Border Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="5" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

            <dxe:CheckEdit Grid.Row="4" Grid.Column="0"   IsChecked="{Binding SelectedWindingResistance, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            <Label Grid.Row="4" Grid.Column="1" Content="电机电阻" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="4" Grid.Column="2" Content="0.001ohm" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="4" Grid.Column="3" Content="{Binding WindingResistance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" />
            <Label Grid.Row="4" Grid.Column="4" Content="{Binding WindingResistance_Identification,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" Foreground="{Binding WindingResistance_Color,Converter={StaticResource ColorConverter},Mode=Default,UpdateSourceTrigger=PropertyChanged}"/>

            <dxe:CheckEdit Grid.Row="5" Grid.Column="0"   IsChecked="{Binding SelectedWindingInductance, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            <Label Grid.Row="5" Grid.Column="1" Content="电机电感" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="5" Grid.Column="2" Content="0.01mH" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="5" Grid.Column="3" Content="{Binding WindingInductance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"  FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center"/>
            <Label Grid.Row="5" Grid.Column="4" Content="{Binding WindingInductance_Identification,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"  FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" Foreground="{Binding WindingInductance_Color,Converter={StaticResource ColorConverter},Mode=Default,UpdateSourceTrigger=PropertyChanged}"/>

            <dxe:CheckEdit Grid.Row="6" Grid.Column="0"   IsChecked="{Binding SelectedAbsEncoderSingleTurnBit, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            <Label Grid.Row="6" Grid.Column="1" Content="单圈值分辨率位数" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="6" Grid.Column="2" Content="bit" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="6" Grid.Column="3" Content="{Binding AbsEncoderSingleTurnBit ,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center"/>
            <Label Grid.Row="6" Grid.Column="4" Content="{Binding AbsEncoderSingleTurnBit_Identification,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" Foreground="{Binding AbsEncoderSingleTurnBit_Color,Converter={StaticResource ColorConverter},Mode=Default,UpdateSourceTrigger=PropertyChanged}"/>

            <dxe:CheckEdit Grid.Row="7" Grid.Column="0"   IsChecked="{Binding SelectedLineUVWSequence, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            <Label Grid.Row="7" Grid.Column="1" Content="电机动力线相序" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="7" Grid.Column="2" Content="1" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="7" Grid.Column="3" Content="{Binding LineUVWSequence,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center"/>
            <Label Grid.Row="7" Grid.Column="4" Content="{Binding LineUVWSequence_Identification,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" Foreground="{Binding LineUVWSequence_Color,Converter={StaticResource ColorConverter},Mode=Default,UpdateSourceTrigger=PropertyChanged}"/>

            <dxe:CheckEdit Grid.Row="8" Grid.Column="0"   IsChecked="{Binding SelectedMotorPolePairsNumber, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            <Label Grid.Row="8" Grid.Column="1" Content="极对数" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="8" Grid.Column="2" Content="1" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="8" Grid.Column="3" Content="{Binding MotorPolePairsNumber,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center"/>
            <Label Grid.Row="8" Grid.Column="4" Content="{Binding MotorPolePairsNumber_Identification,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" Foreground="{Binding MotorPolePairsNumber_Color,Converter={StaticResource ColorConverter},Mode=Default,UpdateSourceTrigger=PropertyChanged}"/>

            <dxe:CheckEdit Grid.Row="9" Grid.Column="0"   IsChecked="{Binding SelectedAbsEncoderOffset, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            <Label Grid.Row="9" Grid.Column="1" Content="绝对式编码器偏置" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="9" Grid.Column="2" Content="1" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" FontSize="10pt"/>
            <Label Grid.Row="9" Grid.Column="3" Content="{Binding AbsEncoderOffset,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center"/>
            <Label Grid.Row="9" Grid.Column="4" Content="{Binding AbsEncoderOffset_Identification,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontSize="10pt" Style="{StaticResource myLabelStyle}" HorizontalContentAlignment="Center" Foreground="{Binding AbsEncoderOffset_Color,Converter={StaticResource ColorConverter},Mode=Default,UpdateSourceTrigger=PropertyChanged}"/>

            <Border Grid.Row="10" Grid.Column="0" Grid.ColumnSpan="5" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

            <StackPanel Grid.Row="11" Grid.Column="0" Grid.ColumnSpan="5" Orientation="Horizontal" HorizontalAlignment="Right">
                <Label Style="{StaticResource LabelStyle}" Content="提示：参数正在识别，请耐心等待结果" Margin="10,4" Foreground="Green" HorizontalContentAlignment="Right" Visibility="{Binding HintVisibility, Converter={StaticResource VisibilityConverter}}"/>
                <Label Style="{StaticResource LabelStyle}" Content="提示：替换参数值，将在伺服重启后生效" Margin="10,4" Foreground="Red" HorizontalContentAlignment="Right"/>
            </StackPanel>

        </Grid>

    </ScrollViewer>
       
</UserControl>
