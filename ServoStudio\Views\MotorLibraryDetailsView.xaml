﻿<UserControl x:Class="ServoStudio.Views.MotorLibraryDetailsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
             xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
             xmlns:dxnt="http://schemas.devexpress.com/winfx/2008/xaml/navbar/themekeys"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
             xmlns:dxn="http://schemas.devexpress.com/winfx/2008/xaml/navbar"
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"  
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars" 
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"  
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:MotorLibraryDetailsViewModel}">
    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding MotorLibraryDetailsLoadedCommand}" EventName="Loaded"/>   
    </dxmvvm:Interaction.Behaviors>

    <UserControl.Resources>
        <DataTemplate x:Key="gridDataTemplate">
            <dxe:TextEdit Text="{Binding RowData.Row.Default, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" IsReadOnly="{Binding Path=DataContext.ReadOnly, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" >
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                </dxmvvm:Interaction.Behaviors>
            </dxe:TextEdit>
        </DataTemplate>
    </UserControl.Resources>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

        <Grid >
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="240"/>
                    <ColumnDefinition Width="50"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="240"/>
                    <ColumnDefinition Width="10"/>
                </Grid.ColumnDefinitions>

                <Label Grid.Row="0" Grid.Column="1" Margin="10,3" Content="参数表名称" Style="{StaticResource LabelStyle}" />
                <TextBox Grid.Row="0" Grid.Column="2" Style="{StaticResource TextBoxStyle}" Margin="10,3" Text="{Binding FileName,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsReadOnly="{Binding ReadOnly}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="LostFocus" Command="{Binding CheckIsNameExistCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </TextBox>

                <Label Grid.Row="0" Grid.Column="4" Margin="10,3" Content="参数表作者" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="0" Grid.Column="5" Style="{StaticResource TextBoxStyle}" Margin="10,3" Text="{Binding Author,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsReadOnly="{Binding ReadOnly}"/>

                <Label Grid.Row="1" Grid.Column="1" Margin="10,3" Content="电机类型" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="1" Grid.Column="2" Style="{StaticResource TextBoxStyle}" Margin="10,3" Text="{Binding MotorType,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsReadOnly="{Binding ReadOnly}"/>

                <Label Grid.Row="1" Grid.Column="4" Margin="10,3" Content="编码器类型" Style="{StaticResource LabelStyle}"/>
                <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="5" Margin="10,3" Width="Auto" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding EncoderType}" SelectedItem="{Binding SelectedEncoderType,Mode=TwoWay}" IsReadOnly="{Binding ReadOnly}"/>

                <Label Grid.Row="2" Grid.Column="1" Margin="10,3" Content="参数表备注" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="4"  Style="{StaticResource TextBoxStyle}" Margin="10,3,10,9" Text="{Binding Comment,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsReadOnly="{Binding ReadOnly}"/>
            </Grid>

            <Border Grid.Row="1" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

            <Grid Grid.Row="2" Margin="9">
                <dxg:GridControl  Width="690" Height="375"  SelectionMode="Row" ItemsSource="{Binding MotorLibraryDetails}" AutoGenerateColumns="AddNew">
                    <dxg:GridControl.View>
                        <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Never">
                            <dxg:TableView.FormatConditions>
                                <dxg:FormatCondition Expression="Not IsNullOrEmpty([Default])" FieldName="Default">
                                    <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                </dxg:FormatCondition>
                            </dxg:TableView.FormatConditions>
                        </dxg:TableView>
                    </dxg:GridControl.View>

                    <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="*"/>
                    <dxg:GridColumn FieldName="MotorFeedbackInterface" Header="参数描述" IsSmart="True" Width="*"/>
                    <dxg:GridColumn FieldName="Default" Header="当前参数值" IsSmart="True" Width="*" CellTemplate="{StaticResource gridDataTemplate}"/>
                    <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="*"/>
                </dxg:GridControl>
            </Grid>
        </Grid>

    </ScrollViewer>
  
</UserControl>
