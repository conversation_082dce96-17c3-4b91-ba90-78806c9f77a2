# ServoStudio 数据采集功能分析报告

## 1. 概述

ServoStudio上位机软件具有强大的数据采集功能，主要包括两大类：
- **示波器数据采集**：用于实时波形监控和分析
- **故障数据采集**：用于故障发生时的数据记录和分析

## 2. 数据采集架构

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    数据采集系统架构                          │
├─────────────────────────────────────────────────────────────┤
│  UI层                                                       │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ OscilloscopeView│  │FaultDataOscillo-│                  │
│  │                 │  │   scopeView     │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  ViewModel层                                                │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Oscilloscope    │  │FaultDataOscillo-│                  │
│  │   ViewModel     │  │ scopeViewModel  │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  通信层                                                     │
│  ┌─────────────────────────────────────────────────────────┤
│  │        CommunicationSetViewModel                        │
│  │  ┌─────────────────┐  ┌─────────────────┐              │
│  │  │   串口通信      │  │   协议解析      │              │
│  │  └─────────────────┘  └─────────────────┘              │
│  └─────────────────────────────────────────────────────────┤
├─────────────────────────────────────────────────────────────┤
│  数据层                                                     │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ AcquisitionData │  │FaultAcquisition-│                  │
│  │                 │  │     Data        │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件

#### 数据模型层
- **AcquisitionInfoSet**: 存储采集任务的静态信息
- **AcquisitionData**: 存储从硬件上传的原始波形数据
- **FaultAcquisitionInfoSet**: 故障数据采集信息
- **FaultAcquisitionData**: 故障数据存储

#### 通信协议层
- **功能码**: 
  - `0x01`: 普通数据采集
  - `0x03`: 故障数据采集
- **执行码**:
  - `0x01`: 下达采集任务
  - `0x02`: 查询采集状态
  - `0x03`: 数据上传
  - `0x04`: 停止采集

## 3. 示波器数据采集功能

### 3.1 功能特性
- **多通道采集**: 支持最多4个通道同时采集
- **灵活采样**: 可配置采样周期、采样时长
- **触发模式**: 支持多种触发条件
- **连续采样**: 支持单次和连续采样模式
- **实时显示**: 采集数据实时波形显示

### 3.2 通道配置系统

#### 3.2.1 XML配置文件结构
示波器通道配置基于XML文件`ServoStudio\Xml\OscilloscopeOptions.xml`：

```xml
<OscilloscopeOptions>
    <SampleChannels>
        <Channel GroupName="位置 (5)" ID="1" ItemName="位置指令" Address="00" />
        <Channel GroupName="位置 (5)" ID="2" ItemName="滤波后位置指令" Address="1A" />
        <Channel GroupName="Debug (4)" ID="42" ItemName="Debug参数5" Address="2A" />
        <Channel GroupName="位置 (5)" ID="43" ItemName="位置增量的增量" Address="2B" />
        <!-- 更多通道配置... -->
    </SampleChannels>
    <SamplingPeriods>
        <Period>100μs</Period>
        <Period>200μs</Period>
        <!-- 更多采样周期... -->
    </SamplingPeriods>
</OscilloscopeOptions>
```

#### 3.2.2 通道配置字段说明

| 字段 | 作用 | 唯一性要求 | 示例 |
|------|------|------------|------|
| **GroupName** | UI分组显示和单位推断 | 建议唯一 | "位置 (5)" |
| **ID** | 系统内部唯一标识 | 必须唯一 | 42 |
| **ItemName** | UI显示名称和单位字典键 | 建议唯一 | "Debug参数5" |
| **Address** | 硬件采集地址 | 建议唯一 | "2A" |

#### 3.2.3 通道映射机制

系统实现了完整的通道映射机制来解决数据错位问题：

```csharp
// 通道映射数据结构
public class ChannelMapping
{
    public int ChannelId { get; set; }      // XML中的通道ID
    public int UiChannelIndex { get; set; } // UI通道索引(1-4)
}

// 映射关系存储
public static List<int> lstChannelMapping = new List<int>();     // 数据索引→UI通道
public static List<int> lstUiToDataMapping = new List<int>();    // UI通道→数据索引
```

#### 3.2.4 数据分配流程

1. **通道选择阶段**：
   ```csharp
   // 用户在UI选择通道时，系统记录选择的通道ID
   int channelId = GetChannelIdByName(SelectedSamplingChannel1);
   channelMappings.Add(new ChannelMapping { ChannelId = channelId, UiChannelIndex = 1 });
   ```

2. **数据发送阶段**：
   ```csharp
   // 按通道ID排序生成采集指令
   channelIds.Sort();
   string addresses = string.Concat(channelIds.Select(id => GetSampleAddressByIndex(id)));
   ```

3. **数据接收阶段**：
   ```csharp
   // 根据映射关系正确分配数据
   int uiChannelIndex = AcquisitionInfoSet.lstChannelMapping[currentDataIndex];
   switch (uiChannelIndex)
   {
       case 1: AcquisitionData.Channel1 = receivedData; break;
       case 2: AcquisitionData.Channel2 = receivedData; break;
       // ...
   }
   ```

#### 3.2.5 单位推断系统

系统根据`GroupName`自动推断通道单位：

```csharp
private static string InferUnitFromGroupName(string groupName, string itemName)
{
    if (groupName.StartsWith("位置"))
        return SelectUnit.Position;     // "cnt"
    else if (groupName.StartsWith("速度"))
        return SelectUnit.Speed;        // "cnt/s"
    else if (groupName.StartsWith("转矩"))
        return itemName.Contains("电流") ? "mA" : "‰";
    else if (groupName.StartsWith("Debug"))
        return "1";                     // 无量纲
    // 更多规则...
}
```

#### 3.2.6 配置验证机制

系统在启动时自动验证配置完整性：

```csharp
private void ValidateChannelConfiguration(List<SampleChannelInfoSet> channels)
{
    // 检查ID重复
    var duplicateIds = channels.GroupBy(c => c.ID).Where(g => g.Count() > 1);

    // 检查ItemName重复
    var duplicateNames = channels.GroupBy(c => c.ItemName).Where(g => g.Count() > 1);

    // 检查Address重复
    var duplicateAddresses = channels.GroupBy(c => c.Address).Where(g => g.Count() > 1);

    // 输出警告信息
    if (issues.Any())
    {
        System.Diagnostics.Debug.WriteLine("=== 通道配置验证警告 ===");
        // 详细警告信息...
    }
}
```

### 3.3 采集参数配置
```csharp
// 采样参数
public virtual ObservableCollection<string> SamplingPeriod { get; set; }    // 采样周期
public virtual string SelectedSamplingPeriod { get; set; }
public virtual ObservableCollection<string> SamplingDuration { get; set; }  // 采样时长
public virtual string SelectedSamplingDuration { get; set; }
public virtual ObservableCollection<string> ContinuousSampling { get; set; } // 连续采样
```

### 3.4 采集流程

#### 3.4.1 采集启动流程
1. **前置检查**
   - 检查串口连接状态
   - 检查是否存在其他采集任务
   - 检查上次波形是否绘制完成

2. **参数打包**
   - 更新通道列表和单位信息
   - 打包采样设置为16进制字符串
   - 计算采样总长度

3. **任务下发**
   - 调用`SerialPort_DataTransmiting_For_ParameterAcquisition`
   - 添加到串口任务队列
   - 后台线程轮询发送

#### 3.4.2 数据上传流程
1. **状态查询**
   - 周期性发送状态查询指令
   - 硬件返回当前采集状态

2. **数据上传**
   - 采集完成后发送数据上传指令
   - 按数据包号分批上传
   - 解析并存储原始数据

3. **数据处理**
   - 应用倍乘系数
   - 单位换算
   - 绑定到图表控件显示

### 3.5 关键代码实现

#### 采集启动
```csharp
public void ParameterAcquisitionStart()
{
    // 前置检查
    if (!CheckPreconditions()) return;
    
    // 参数打包
    RefreshAcquisitionList();
    string transmittingContent = GetTransmittingContent();
    
    // 任务下发
    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterAcquisition(
        PageName.OSCILLOSCOPE, TaskName.AssigningAcquisition, transmittingContent);
}
```

#### 数据解析
```csharp
// 在CommunicationSetViewModel.AnalyseReceivingMessage中
if (functionCode == FunctionCode.ACQUISITION && 
    executedCode == AcquisitionExecutedCode.UPLOAD_ACQUISITION)
{
    // 解析数据帧
    HexHelper.ReceivingMessage_ForUploading();
    
    // 检查是否上传完成
    if (IsUploadCompleted())
    {
        // 触发数据显示
        ViewModelSet.OscilloscopeView?.DisplayOscilloscope();
    }
}
```

## 4. 故障数据采集功能

### 4.1 功能特性
- **扩展通道**: 支持最多8个通道采集
- **故障触发**: 在故障发生时自动触发采集
- **历史数据**: 保存故障发生前后的数据
- **数据分析**: 提供故障数据分析工具

### 4.2 故障采集配置
```csharp
// 故障采集通道
public virtual string SampleChannel1 { get; set; }  // 采样通道1-8
public virtual string SampleChannel2 { get; set; }
// ... 最多8个通道

// 采样参数
public virtual string SamplingPeriod { get; set; }   // 采样周期
public virtual string SamplingDuration { get; set; } // 采样时长
```

### 4.3 故障采集流程

#### 4.3.1 故障检测与触发
1. **故障监控**
   - 实时监控系统状态
   - 检测故障条件
   - 自动触发采集

2. **数据采集**
   - 使用功能码`0x03`
   - 采集故障前后数据
   - 保存到故障数据库

#### 4.3.2 数据管理
1. **数据存储**
   - 按故障类型分类存储
   - 包含时间戳信息
   - 支持数据导出

2. **数据查询**
   - 按时间范围查询
   - 按故障类型筛选
   - 支持数据对比分析

## 5. 通信协议详解

### 5.1 报文格式
```
┌──────┬──────┬──────┬──────┬──────┬──────┬──────┬──────┐
│ 报头 │站号  │轴号  │功能码│执行码│数据长│ 数据 │ 报尾 │
│ AA   │ XX   │ XX   │ XX   │ XX   │ XX   │ ... │ 55   │
└──────┴──────┴──────┴──────┴──────┴──────┴──────┴──────┘
```

### 5.2 功能码定义
- **0x01**: 普通数据采集
- **0x03**: 故障数据采集
- **0x08**: 通信测试
- **0x0D**: 参数读取
- **0x0E**: 参数写入

### 5.3 执行码定义
```csharp
public class AcquisitionExecutedCode
{
    public const string ACQUISITION = "01";        // 数据采集
    public const string ASK_ACQUISITION = "02";    // 状态问询
    public const string UPLOAD_ACQUISITION = "03"; // 数据上传
    public const string STOP_ACQUISITION = "04";   // 停止采集
}
```

## 6. 数据处理与显示

### 6.1 数据转换
1. **原始数据处理**
   - 16进制字符串转数值
   - 应用换算系数
   - 单位转换

2. **波形数据生成**
   - 计算时间轴坐标
   - 生成Point集合
   - 绑定到图表控件

### 6.2 显示优化
1. **动态显示**
   - 支持波形滚动显示
   - 自动调整显示范围
   - 实时更新波形

2. **性能优化**
   - 数据抽样显示
   - 虚拟化技术
   - 多线程处理

## 7. 配置管理

### 7.1 预设配置
- **示波器预设**: 保存常用采集配置
- **通道配置**: 预定义通道参数
- **触发配置**: 常用触发条件

### 7.2 配置文件
```xml
<!-- OscilloscopePresetConfigs.xml -->
<OscilloscopePresets>
    <Preset Name="默认配置">
        <SamplingPeriod>100μs</SamplingPeriod>
        <SamplingDuration>1000ms</SamplingDuration>
        <Channels>
            <Channel1>位置反馈</Channel1>
            <Channel2>速度反馈</Channel2>
        </Channels>
    </Preset>
</OscilloscopePresets>
```

## 8. 错误处理与异常管理

### 8.1 通信异常
- **连接断开**: 自动重连机制
- **数据丢失**: 重传机制
- **超时处理**: 超时重试

### 8.2 数据异常
- **数据校验**: CRC校验
- **格式验证**: 数据格式检查
- **范围检查**: 数据合理性验证

## 9. 性能特性

### 9.1 采集性能
- **最高采样率**: 10kHz
- **通道数量**: 普通4通道，故障8通道
- **数据长度**: 最大12000点
- **实时性**: 毫秒级响应

### 9.2 显示性能
- **刷新率**: 60FPS
- **数据点**: 支持万点级显示
- **内存占用**: 优化的内存管理
- **CPU使用**: 多线程优化

## 10. 扩展功能

### 10.1 数据导出
- **格式支持**: Excel、CSV、TXT
- **批量导出**: 支持批量数据导出
- **自定义格式**: 可配置导出格式

### 10.2 数据分析
- **FFT分析**: 频域分析
- **统计分析**: 最值、均值、RMS
- **对比分析**: 多组数据对比
- **趋势分析**: 长期数据趋势

## 11. 示波器通道配置最佳实践

### 11.1 通道配置规范

#### 11.1.1 命名规范
- **GroupName**: 使用"类别 (数字)"格式，如"位置 (5)"
- **ItemName**: 使用描述性名称，避免重复
- **ID**: 确保唯一性，建议按功能分段编号
- **Address**: 使用硬件定义的地址，通常为16进制

#### 11.1.2 配置示例

```xml
<!-- 位置类参数 -->
<Channel GroupName="位置 (5)" ID="1" ItemName="位置指令" Address="00" />
<Channel GroupName="位置 (5)" ID="2" ItemName="滤波后位置指令" Address="1A" />
<Channel GroupName="位置 (5)" ID="3" ItemName="位置反馈" Address="01" />

<!-- 速度类参数 -->
<Channel GroupName="速度 (6)" ID="7" ItemName="位置环输出速度指令" Address="03" />
<Channel GroupName="速度 (6)" ID="8" ItemName="速度指令" Address="04" />
<Channel GroupName="速度 (6)" ID="9" ItemName="速度反馈" Address="05" />

<!-- 调试参数 -->
<Channel GroupName="Debug (4)" ID="41" ItemName="Debug参数4" Address="29" />
<Channel GroupName="Debug (4)" ID="42" ItemName="Debug参数5" Address="2A" />
```

### 11.2 通道配置注意事项

#### 11.2.1 字段重要性说明

| 字段 | 重要性 | 影响范围 | 注意事项 |
|------|--------|----------|----------|
| **ID** | 高 | 系统内部标识 | 必须唯一，否则系统错误 |
| **ItemName** | 高 | UI显示、单位查找 | 建议唯一，重复会导致选择混淆 |
| **Address** | 高 | 硬件通信 | 必须与硬件地址匹配 |
| **GroupName** | 中 | UI分组、单位推断 | 前缀影响单位推断，数字仅影响UI分组 |

#### 11.2.2 常见问题与解决方案

1. **数据错位问题**
   - **症状**: 通道A显示通道B的数据
   - **原因**: 通道映射关系错误
   - **解决**: 确保ID和Address正确对应

2. **单位显示错误**
   - **症状**: 波形显示单位不正确
   - **原因**: GroupName前缀不匹配或ItemName未在单位字典中
   - **解决**: 使用正确的GroupName前缀或手动添加单位映射

3. **通道不显示**
   - **症状**: 下拉列表中没有某个通道
   - **原因**: XML配置错误或解析失败
   - **解决**: 检查XML格式和字段完整性

#### 11.2.3 扩展配置技巧

1. **分组优化**
   - 相关参数使用相同GroupName前缀
   - 使用数字区分不同子分组
   - 例如："位置 (1)"、"位置 (2)"分别用于不同类型的位置参数

2. **ID编号策略**
   - 按功能分段：1-10位置参数，11-20速度参数
   - 预留空间：每类参数预留足够ID空间便于扩展
   - 避免冲突：新增通道使用未占用的ID

3. **动态单位处理**
   - 利用系统的单位推断机制
   - 特殊单位可在代码中添加映射
   - 考虑未来扩展XML添加Unit属性

## 12. 总结

ServoStudio的数据采集功能是一个功能完善、性能优异的系统：

### 12.1 优势
- **架构清晰**: MVVM模式，层次分明
- **功能丰富**: 支持多种采集模式
- **性能优秀**: 高采样率，实时显示
- **扩展性强**: 支持多种数据格式和分析
- **配置灵活**: 基于XML的通道配置系统

### 12.2 应用场景
- **系统调试**: 实时监控系统状态
- **故障分析**: 故障数据记录和分析
- **性能优化**: 系统性能评估
- **质量控制**: 产品质量检测
- **参数配置**: 通过XML灵活配置采集通道

这个数据采集系统为伺服系统的开发、调试和维护提供了强有力的工具支持。