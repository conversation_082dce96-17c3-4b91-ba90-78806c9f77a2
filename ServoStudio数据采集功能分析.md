# ServoStudio 数据采集功能分析报告

## 1. 概述

ServoStudio上位机软件具有强大的数据采集功能，主要包括两大类：
- **示波器数据采集**：用于实时波形监控和分析
- **故障数据采集**：用于故障发生时的数据记录和分析

## 2. 数据采集架构

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    数据采集系统架构                          │
├─────────────────────────────────────────────────────────────┤
│  UI层                                                       │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ OscilloscopeView│  │FaultDataOscillo-│                  │
│  │                 │  │   scopeView     │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  ViewModel层                                                │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Oscilloscope    │  │FaultDataOscillo-│                  │
│  │   ViewModel     │  │ scopeViewModel  │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  通信层                                                     │
│  ┌─────────────────────────────────────────────────────────┤
│  │        CommunicationSetViewModel                        │
│  │  ┌─────────────────┐  ┌─────────────────┐              │
│  │  │   串口通信      │  │   协议解析      │              │
│  │  └─────────────────┘  └─────────────────┘              │
│  └─────────────────────────────────────────────────────────┤
├─────────────────────────────────────────────────────────────┤
│  数据层                                                     │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ AcquisitionData │  │FaultAcquisition-│                  │
│  │                 │  │     Data        │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件

#### 数据模型层
- **AcquisitionInfoSet**: 存储采集任务的静态信息
- **AcquisitionData**: 存储从硬件上传的原始波形数据
- **FaultAcquisitionInfoSet**: 故障数据采集信息
- **FaultAcquisitionData**: 故障数据存储

#### 通信协议层
- **功能码**: 
  - `0x01`: 普通数据采集
  - `0x03`: 故障数据采集
- **执行码**:
  - `0x01`: 下达采集任务
  - `0x02`: 查询采集状态
  - `0x03`: 数据上传
  - `0x04`: 停止采集

## 3. 示波器数据采集功能

### 3.1 功能特性
- **多通道采集**: 支持最多4个通道同时采集
- **灵活采样**: 可配置采样周期、采样时长
- **触发模式**: 支持多种触发条件
- **连续采样**: 支持单次和连续采样模式
- **实时显示**: 采集数据实时波形显示

### 3.2 采集参数配置
```csharp
// 采样参数
public virtual ObservableCollection<string> SamplingPeriod { get; set; }    // 采样周期
public virtual string SelectedSamplingPeriod { get; set; }
public virtual ObservableCollection<string> SamplingDuration { get; set; }  // 采样时长
public virtual string SelectedSamplingDuration { get; set; }
public virtual ObservableCollection<string> ContinuousSampling { get; set; } // 连续采样
```

### 3.3 采集流程

#### 3.3.1 采集启动流程
1. **前置检查**
   - 检查串口连接状态
   - 检查是否存在其他采集任务
   - 检查上次波形是否绘制完成

2. **参数打包**
   - 更新通道列表和单位信息
   - 打包采样设置为16进制字符串
   - 计算采样总长度

3. **任务下发**
   - 调用`SerialPort_DataTransmiting_For_ParameterAcquisition`
   - 添加到串口任务队列
   - 后台线程轮询发送

#### 3.3.2 数据上传流程
1. **状态查询**
   - 周期性发送状态查询指令
   - 硬件返回当前采集状态

2. **数据上传**
   - 采集完成后发送数据上传指令
   - 按数据包号分批上传
   - 解析并存储原始数据

3. **数据处理**
   - 应用倍乘系数
   - 单位换算
   - 绑定到图表控件显示

### 3.4 关键代码实现

#### 采集启动
```csharp
public void ParameterAcquisitionStart()
{
    // 前置检查
    if (!CheckPreconditions()) return;
    
    // 参数打包
    RefreshAcquisitionList();
    string transmittingContent = GetTransmittingContent();
    
    // 任务下发
    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterAcquisition(
        PageName.OSCILLOSCOPE, TaskName.AssigningAcquisition, transmittingContent);
}
```

#### 数据解析
```csharp
// 在CommunicationSetViewModel.AnalyseReceivingMessage中
if (functionCode == FunctionCode.ACQUISITION && 
    executedCode == AcquisitionExecutedCode.UPLOAD_ACQUISITION)
{
    // 解析数据帧
    HexHelper.ReceivingMessage_ForUploading();
    
    // 检查是否上传完成
    if (IsUploadCompleted())
    {
        // 触发数据显示
        ViewModelSet.OscilloscopeView?.DisplayOscilloscope();
    }
}
```

## 4. 故障数据采集功能

### 4.1 功能特性
- **扩展通道**: 支持最多8个通道采集
- **故障触发**: 在故障发生时自动触发采集
- **历史数据**: 保存故障发生前后的数据
- **数据分析**: 提供故障数据分析工具

### 4.2 故障采集配置
```csharp
// 故障采集通道
public virtual string SampleChannel1 { get; set; }  // 采样通道1-8
public virtual string SampleChannel2 { get; set; }
// ... 最多8个通道

// 采样参数
public virtual string SamplingPeriod { get; set; }   // 采样周期
public virtual string SamplingDuration { get; set; } // 采样时长
```

### 4.3 故障采集流程

#### 4.3.1 故障检测与触发
1. **故障监控**
   - 实时监控系统状态
   - 检测故障条件
   - 自动触发采集

2. **数据采集**
   - 使用功能码`0x03`
   - 采集故障前后数据
   - 保存到故障数据库

#### 4.3.2 数据管理
1. **数据存储**
   - 按故障类型分类存储
   - 包含时间戳信息
   - 支持数据导出

2. **数据查询**
   - 按时间范围查询
   - 按故障类型筛选
   - 支持数据对比分析

## 5. 通信协议详解

### 5.1 报文格式
```
┌──────┬──────┬──────┬──────┬──────┬──────┬──────┬──────┐
│ 报头 │站号  │轴号  │功能码│执行码│数据长│ 数据 │ 报尾 │
│ AA   │ XX   │ XX   │ XX   │ XX   │ XX   │ ... │ 55   │
└──────┴──────┴──────┴──────┴──────┴──────┴──────┴──────┘
```

### 5.2 功能码定义
- **0x01**: 普通数据采集
- **0x03**: 故障数据采集
- **0x08**: 通信测试
- **0x0D**: 参数读取
- **0x0E**: 参数写入

### 5.3 执行码定义
```csharp
public class AcquisitionExecutedCode
{
    public const string ACQUISITION = "01";        // 数据采集
    public const string ASK_ACQUISITION = "02";    // 状态问询
    public const string UPLOAD_ACQUISITION = "03"; // 数据上传
    public const string STOP_ACQUISITION = "04";   // 停止采集
}
```

## 6. 数据处理与显示

### 6.1 数据转换
1. **原始数据处理**
   - 16进制字符串转数值
   - 应用换算系数
   - 单位转换

2. **波形数据生成**
   - 计算时间轴坐标
   - 生成Point集合
   - 绑定到图表控件

### 6.2 显示优化
1. **动态显示**
   - 支持波形滚动显示
   - 自动调整显示范围
   - 实时更新波形

2. **性能优化**
   - 数据抽样显示
   - 虚拟化技术
   - 多线程处理

## 7. 配置管理

### 7.1 预设配置
- **示波器预设**: 保存常用采集配置
- **通道配置**: 预定义通道参数
- **触发配置**: 常用触发条件

### 7.2 配置文件
```xml
<!-- OscilloscopePresetConfigs.xml -->
<OscilloscopePresets>
    <Preset Name="默认配置">
        <SamplingPeriod>100μs</SamplingPeriod>
        <SamplingDuration>1000ms</SamplingDuration>
        <Channels>
            <Channel1>位置反馈</Channel1>
            <Channel2>速度反馈</Channel2>
        </Channels>
    </Preset>
</OscilloscopePresets>
```

## 8. 错误处理与异常管理

### 8.1 通信异常
- **连接断开**: 自动重连机制
- **数据丢失**: 重传机制
- **超时处理**: 超时重试

### 8.2 数据异常
- **数据校验**: CRC校验
- **格式验证**: 数据格式检查
- **范围检查**: 数据合理性验证

## 9. 性能特性

### 9.1 采集性能
- **最高采样率**: 10kHz
- **通道数量**: 普通4通道，故障8通道
- **数据长度**: 最大12000点
- **实时性**: 毫秒级响应

### 9.2 显示性能
- **刷新率**: 60FPS
- **数据点**: 支持万点级显示
- **内存占用**: 优化的内存管理
- **CPU使用**: 多线程优化

## 10. 扩展功能

### 10.1 数据导出
- **格式支持**: Excel、CSV、TXT
- **批量导出**: 支持批量数据导出
- **自定义格式**: 可配置导出格式

### 10.2 数据分析
- **FFT分析**: 频域分析
- **统计分析**: 最值、均值、RMS
- **对比分析**: 多组数据对比
- **趋势分析**: 长期数据趋势

## 11. 总结

ServoStudio的数据采集功能是一个功能完善、性能优异的系统：

### 11.1 优势
- **架构清晰**: MVVM模式，层次分明
- **功能丰富**: 支持多种采集模式
- **性能优秀**: 高采样率，实时显示
- **扩展性强**: 支持多种数据格式和分析

### 11.2 应用场景
- **系统调试**: 实时监控系统状态
- **故障分析**: 故障数据记录和分析
- **性能优化**: 系统性能评估
- **质量控制**: 产品质量检测

这个数据采集系统为伺服系统的开发、调试和维护提供了强有力的工具支持。