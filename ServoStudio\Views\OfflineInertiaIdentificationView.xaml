﻿<UserControl x:Class="ServoStudio.Views.OfflineInertiaIdentificationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dxnt="http://schemas.devexpress.com/winfx/2008/xaml/navbar/themekeys"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
             xmlns:dxn="http://schemas.devexpress.com/winfx/2008/xaml/navbar"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars" 
             xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"  
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:OfflineInertiaIdentificationViewModel}">

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding OfflineInertiaIdentificationLoadedCommand}" EventName="Loaded"/>
        <dxmvvm:EventToCommand Command="{Binding OfflineInertialdentificationUnloadedCommand}" EventName="Unloaded"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Separator_16x16.png}" Command="{Binding OfflineInertiaIdentificationSwitchCommand}" Margin="0,0,6,0" Width="103">
                    <Label Content="{Binding InertiaSwitchHint}" Style="{StaticResource LabelStyle}" Margin="0" />
                </dx:SimpleButton>

                <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=PieStyleDonut_16x16.png}" Margin="0,0,6,0" Width="103" IsEnabled="{Binding InertiaGetButtonEnabled}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding OfflineInertiaIdentificationStartCommand}" CommandParameter="2"/>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding OfflineInertiaIdentificationStopCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                    <Label Content="获取惯量比" Style="{StaticResource LabelStyle}" Margin="0" />
                </dx:SimpleButton>

                <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/ReversSort_16x16.png" Command="{Binding OfflineInertiaIdentificationModifyCommand}" Margin="0,0,6,0" Width="103" IsEnabled="{Binding InertiaModifyButtonEnabled}">
                    <Label Content="替换惯量比" Style="{StaticResource LabelStyle}" Margin="0" />
                </dx:SimpleButton>
            </StackPanel>

            <Border Grid.Row="1" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

            <Grid Grid.Row="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="50"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="180"/>
                    <ColumnDefinition Width="60"/>
                    <ColumnDefinition Width="10"/>
                </Grid.ColumnDefinitions>

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="3"  Grid.Column="1" Height="150" Width="Auto" Margin="0,8,0,2" HorizontalAlignment="Left" ShowMenu="False" Source="pack://application:,,,/ServoStudio;component/Resource/Position.png" ShowBorder="False" Opacity="0.65"/>

                <StackPanel Grid.Row="0" Grid.Column="3" Grid.ColumnSpan="3" Orientation="Horizontal">
                    <Label  Foreground="Red" Content="{Binding LoadInertiaRatioInitial , UpdateSourceTrigger=PropertyChanged}" ContentStringFormat='初始负载惯量比： {0} %' Style="{StaticResource LabelStyle}" Margin="10,6,10,8"/>
                    <Label  Foreground="Green" Content="{Binding LoadInertiaRatio , UpdateSourceTrigger=PropertyChanged}" ContentStringFormat='当前负载惯量比： {0} %' Style="{StaticResource LabelStyle}" Margin="10,6,10,8"/>
                </StackPanel>

                <Label   Grid.Row="1" Grid.Column="3" Margin="10,9" Content="惯量辨识运行距离" Style="{StaticResource LabelStyle}" />
                <TextBox Grid.Row="1" Grid.Column="4" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9"  Text="{Binding InertiaIdentificationPosition,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="1" Grid.Column="5" Style="{StaticResource TextBoxStyle_Unit}" Text="{Binding PositionUnit}"/>

                <Label   Grid.Row="2" Grid.Column="3" Margin="10,9" Content="惯量辨识加速时间" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="2" Grid.Column="4"  Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9"  Text="{Binding InertiaIdentificationAcceleration,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="2" Grid.Column="5" Style="{StaticResource TextBoxStyle_Unit}" Text="ms"/>

            </Grid>

            <Border Grid.Row="3" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

            <StackPanel Grid.Row="4" Orientation="Vertical" HorizontalAlignment="Right">
                <Label Content="提示：鼠标右键长按  [获取惯量比]  按钮，惯量识别开始;按键松开，随即停止" Style="{StaticResource LabelStyle}" Margin="10,9" Foreground="Red" HorizontalContentAlignment="Right"/>
            </StackPanel>

        </Grid>

    </ScrollViewer>
        
</UserControl>
