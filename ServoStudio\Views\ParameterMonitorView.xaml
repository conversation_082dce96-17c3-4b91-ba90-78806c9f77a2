﻿<UserControl x:Class="ServoStudio.Views.ParameterMonitorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:dxrt="http://schemas.devexpress.com/winfx/2008/xaml/ribbon/themekeys"             
             xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
             xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
             xmlns:dxnt="http://schemas.devexpress.com/winfx/2008/xaml/navbar/themekeys"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
             xmlns:dxn="http://schemas.devexpress.com/winfx/2008/xaml/navbar"
             xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"  
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:ParameterMonitorViewModel}"
             d:DesignHeight="600" d:DesignWidth="900">
    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding ParameterMonitorLoadedCommand}"/>
    </dxmvvm:Interaction.Behaviors>

    <Grid>
        <dxg:GridControl Margin="5" SelectionMode="Row" ItemsSource="{Binding ParameterMonitor}" AutoGenerateColumns="AddNew" >
            <dxg:GridControl.View>
                <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25">
                    <dxg:TableView.FormatConditions>
                        <dxg:FormatCondition ApplyToRow="True" Expression="[IsMonitored] = 'True'" FieldName="IsMonitored">
                            <dx:Format Background="#FFCBAFED"/>
                        </dxg:FormatCondition>
                    </dxg:TableView.FormatConditions>
                </dxg:TableView>
            </dxg:GridControl.View>

            <dxg:GridColumn FieldName="IsMonitored" Header="是否监控数据" IsSmart="True" ReadOnly="False">
                <dxg:GridColumn.CellTemplate>
                    <DataTemplate>
                        <dxe:CheckEdit VerticalAlignment="Center" HorizontalAlignment="Center" IsChecked="{Binding RowData.Row.IsMonitored, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                            <dxmvvm:Interaction.Behaviors>
                                <dxmvvm:EventToCommand EventName="Unchecked" Command="{Binding Path=DataContext.RefreshMonitorDataTableCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}"/>
                                <dxmvvm:EventToCommand EventName="Checked" Command="{Binding Path=DataContext.RefreshMonitorDataTableCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}"/>
                            </dxmvvm:Interaction.Behaviors>
                        </dxe:CheckEdit>
                    </DataTemplate>
                </dxg:GridColumn.CellTemplate>
            </dxg:GridColumn>

            <dxg:GridColumn FieldName="Classification" Header="索引类别" IsSmart="True" Width="2*"/>
            <dxg:GridColumn FieldName="Index" Header="索引号" IsSmart="True" Width="2*"/>
            <dxg:GridColumn FieldName="DataType" Header="数据类型" IsSmart="True" Width="2*"/>
            <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="2*"/>      
            <dxg:GridColumn FieldName="Description" Header="参数描述" IsSmart="True" Width="2*"/>
            <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="2*"/>
        </dxg:GridControl>
    </Grid>
</UserControl>
