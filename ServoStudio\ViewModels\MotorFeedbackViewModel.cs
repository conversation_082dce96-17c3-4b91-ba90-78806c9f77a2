﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using System.Collections.ObjectModel;
using System.ComponentModel;
using ServoStudio;
using System.Data;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Collections.Generic;
using ServoStudio.Models;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class MotorFeedbackViewModel
    {
        #region 私有字段
        private static bool IsEncoderSet = false;
        private static bool IsInitialized = true;
        private static bool IsEvaluationAll = true;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 服务
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        protected virtual IDialogService DialogService { get { return this.GetService<IDialogService>(); } }
        #endregion

        #region 属性
        public virtual string SelectedTabIndex { get; set; }

        #region 电机
        public virtual ObservableCollection<string> MotorType { get; set; }//电机类型
        public virtual string SelectedMotorType { get; set; }//选中的电机类型

        public virtual ObservableCollection<string> MotorID { get; set; }//电机型号       
        public virtual string SelectedMotorID { get; set; }//选中的电机型号
        public virtual string MotorRatedPower { get; set; }//电机额定功率
        public virtual string MotorRatedFrequency { get; set; }//电机额定频率
        //public virtual string MotorRatedVoltage { get; set; }//电机额定电压
        public virtual ObservableCollection<string> MotorRatedVoltage { get; set; }//电机额定电压
        public virtual string SelectedMotorRatedVoltage { get; set; }//选中的电机额定电压
        public virtual string MotorRatedCurrent { get; set; }//电机额定电流
        public virtual string MotorRatedTorque { get; set; }//电机额定转矩
        public virtual string MotorRatedSpeed { get; set; }//电机额定转速

        public virtual string MotorMaxCurrent { get; set; }//电机最大电流
        public virtual string MotorMaxTorque { get; set; }//电机最大转矩
        public virtual string MotorMaxSpeed { get; set; }//电机最大转速

        public virtual string MotorPolePairsNumber { get; set; }//电机极对数
        public virtual string MotorWindingResistance { get; set; }//电机线电阻
        public virtual string MotorWindingInductance { get; set; }//电机线电感
        public virtual string MotorRotorInertia { get; set; }//电机转动惯量
        public virtual string MotorBackEMF { get; set; }//电机线反电势系数数
        public virtual string MotorTorqueConstant { get; set; }//电机转矩系数
        public virtual string MotorMechanicalConstant { get; set; }//电机机械系数
        public virtual string LinearMotorPitch { get; set; }//直线电机节距
        public virtual string OverSpeedValue { get; set; }//超速预警阈值

        public virtual int IsRotatingMotorPageEnabled { get; set; }//是否旋转型无刷电机界面可以使用
        public virtual int IsLinearMotorPageEnabled { get; set; }//是否直线电机界面可以使用

        public virtual bool TextBoxEnabled { get; set; }//TextBox的使能

        #endregion

        #region 编码器
        public virtual ObservableCollection<string> EncoderType { get; set; }//编码器类型
        public virtual string SelectedEncoderType { get; set; }//选中的编码器类型

        public virtual int IsBISSCPageEnabled { get; set; }//是否BISSC界面可以使用

        public virtual string ABSEncoderSingleTurnBit { get; set; }//编码器单圈分辨率
        public virtual string ABSEncoderMultiTurnBit { get; set; }//编码器多圈分辨率
        public virtual string ABSEncoderOffset { get; set; }//绝对式编码器偏置
        public virtual string ABZEncoderPulses { get; set; }//ABZ编码器脉冲数

        public virtual string BissCEncoderLength { get; set; }//Biss-C总位置长度
        #endregion

        public virtual int C1 { get; set; }//Biss-C总位置长度
        public virtual int C2 { get; set; }//Biss-C总位置长度
        public virtual int C3 { get; set; }//Biss-C总位置长度

        #endregion

        #region 构造函数
        public MotorFeedbackViewModel()
        {
            ViewModelSet.MotorFeedback = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.MOTORFEEDBACK;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：MotorFeedbackLoaded
        //函数功能：MotorFeedback控件Loaded
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.10.16&Lilbert
        //*************************************************************************
        public void MotorFeedbackLoaded()
        {
            int iRet = -1;

            try
            {
                TextBoxEnabled = false;  //由Lilbert添加TextBox使能
                //下拉列表初始化
                ComboBoxInitialize();

                if (IsInitialized == true)
                {
                    //标志位更新
                    IsInitialized = false;
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadMotorFeedbackParameter("All");
                    }
                    else
                    {
                        GetDefaultMotorFeedbackParameter("All");
                    }                                                    
                }
                else
                {
                    //标志位更新
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadMotorFeedbackParameter("All");
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }
                }               
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_LOADED, "MotorFeedbackLoaded", ex);
            }          
        }

        //*************************************************************************
        //函数名称：MotorFeedbackUnloaded
        //函数功能：电机反馈界面退出
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.23
        //*************************************************************************
        public void MotorFeedbackUnloaded()
        {

            //List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            ////获取要写入的数据字典
            //AddParameterInfoDictionary(ref dicParameterInfo);

            ////获取参数详细信息
            //OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);

            ////获取发送任务信息
            //ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

            ////下达任务
            //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MOTORPARAMETERIDENTIFICATION, TaskName.ParameterIdentificationUnload, lstTransmittingDataInfo);
        }

        //*************************************************************************
        //函数名称：WriteMotorFeedbackParameter
        //函数功能：写电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.01
        //*************************************************************************
        public void WriteMotorFeedbackParameter(string strCategory)
        {
            int iRet = -1;
            bool bEEPROM = false;          
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                //iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                iRet = OthersHelper.CheckInputParametersCorrected_For_MotorFeedback(ref lstParameterInfo_ForWrite);     //由Lilbert于2022.11.24更改提示信息方式
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MOTORFEEDBACK, TaskName.MotorFeedback, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_WRITE_PARAMETER, "WriteMotorFeedbackParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadMotorFeedbackParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void ReadMotorFeedbackParameter(string strCategory)
        {
            int iRet = -1;                
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
              
                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MOTORFEEDBACK, TaskName.MotorFeedback, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_READ_MOTOR_FEEDBACK_PARAMETER, "ReadMotorFeedbackParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultMotorFeedbackParameter
        //函数功能：获取电机配置的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.03&2021.10.10&2022.10.21
        //*************************************************************************
        public void GetDefaultMotorFeedbackParameter(string strCategory)
        {
            string strEncoderValue = null;
            string strMotorTypeValue = null;
            string strMotorRatedVoltageValue = null;

            try
            {
                if (strCategory == "0")
                {
                    MotorRatedPower = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Power", "Default");
                    MotorRatedFrequency = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Frequency", "Default");  //由Lilbert增加电机额定频率
                    //MotorRatedVoltage = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Voltage", "Default");
                    MotorRatedCurrent = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Current", "Default");
                    MotorRatedTorque = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Torque", "Default");
                    MotorRatedSpeed = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Speed", "Default");

                    MotorMaxCurrent = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Current", "Default");
                    MotorMaxTorque = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Torque", "Default");
                    MotorMaxSpeed = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Speed", "Default");

                    MotorPolePairsNumber = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Pole Pairs Number", "Default");
                    MotorWindingResistance = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Resistance", "Default");
                    MotorWindingInductance = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Inductance", "Default");
                    MotorRotorInertia = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rotor Inertia", "Default");
                    MotorBackEMF = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Back EMF", "Default");
                    MotorTorqueConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Torque Constant", "Default");
                    MotorMechanicalConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Mechanical Constant", "Default");

                    LinearMotorPitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Linear Motor Pitch", "Default");//由Lilbert增加直线电机节距
                    BissCEncoderLength = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Biss-C Encoder length", "Default");  //由Lilbert增加Biss-C总位置长度

                    OverSpeedValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Over Speed Value", "Default");//由Lilbert增加超速预警阈值 

                    strMotorRatedVoltageValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Voltage", "Default");
                    if (strMotorRatedVoltageValue == "24")
                    {
                        SelectedMotorRatedVoltage = "24V";
                    }
                    else if (strMotorRatedVoltageValue == "36")
                    {
                        SelectedMotorRatedVoltage = "36V";
                    }
                    else if (strMotorRatedVoltageValue == "48")
                    {
                        SelectedMotorRatedVoltage = "48V";
                    }
                    else if (strMotorRatedVoltageValue == "60")
                    {
                        SelectedMotorRatedVoltage = "60V";
                    }
                    else if (strMotorRatedVoltageValue == "110")
                    {
                        SelectedMotorRatedVoltage = "110V";
                    }
                    else if (strMotorRatedVoltageValue == "220")
                    {
                        SelectedMotorRatedVoltage = "220V";
                    }
                    else if (strMotorRatedVoltageValue == "380")
                    {
                        SelectedMotorRatedVoltage = "380V";
                    }

                    strMotorTypeValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Type", "Default");//由Lilbert增加电机类型 
                    if (strMotorTypeValue == "0")
                    {
                        SelectedMotorType = "旋转电机";
                    }
                    else
                    {
                        SelectedMotorType = "直线电机";
                    }
                }
                else if (strCategory == "1")
                {
                    ABSEncoderSingleTurnBit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Single-Turn Bit", "Default");
                    ABSEncoderMultiTurnBit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Multi-Turn Bit", "Default");
                    ABSEncoderOffset = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Offset", "Default");
                    ABZEncoderPulses = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abz Encoder Pulses", "Default");

                    BissCEncoderLength = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Biss-C Encoder length", "Default");  //由Lilbert增加Biss-C总位置长度

                    strEncoderValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Encoder Type", "Default");
                    if (strEncoderValue == "1")
                    {
                        SelectedEncoderType = "增量式编码器";
                    }
                    else if (strEncoderValue == "2")
                    {
                        SelectedEncoderType = "多摩川编码器";
                    }
                    else if (strEncoderValue == "3")
                    {
                        SelectedEncoderType = "尼康编码器4M";
                    }
                    else if (strEncoderValue == "4")
                    {
                        SelectedEncoderType = "尼康编码器2.5M";
                    }
                    else if (strEncoderValue == "5")
                    {
                        SelectedEncoderType = "旋转式编码器";    //由Lilbert添加旋转式编码器
                    }
                    else if (strEncoderValue == "6")
                    {
                        SelectedEncoderType = "通信型增量式编码器";    //由Lilbert添加通信型增量式编码器
                    }
                    else if (strEncoderValue == "7")
                    {
                        SelectedEncoderType = "BISS-C绝对式式编码器";   //由Lilbert添加BISS-C绝对式式编码器
                    }
                    else if (strEncoderValue == "8")
                    {
                        SelectedEncoderType = "BISS-C增量式编码器";     //由Lilbert添加BISS-C增量式编码器
                    }
                    else if (strEncoderValue == "9")
                    {
                        SelectedEncoderType = "ABZ增量式编码器+HALL";     //由Lilbert添加ABZ增量式编码器+Hall
                    }
                    else if (strEncoderValue == "10")
                    {
                        SelectedEncoderType = "HALL-UVW";     //由Lilbert添加HALL-UVW
                    }
                    else
                    {
                        SelectedEncoderType = "SSI编码器";     //由Lilbert添加SSI编码器
                    }
                }
                else
                {
                    MotorRatedPower = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Power", "Default");
                    MotorRatedFrequency = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Frequency", "Default");  //由Lilbert增加电机额定频率
                    //MotorRatedVoltage = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Voltage", "Default");
                    MotorRatedCurrent = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Current", "Default");
                    MotorRatedTorque = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Torque", "Default");
                    MotorRatedSpeed = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Speed", "Default");

                    MotorMaxCurrent = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Current", "Default");
                    MotorMaxTorque = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Torque", "Default");
                    MotorMaxSpeed = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Speed", "Default");

                    MotorPolePairsNumber = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Pole Pairs Number", "Default");
                    MotorWindingResistance = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Resistance", "Default");
                    MotorWindingInductance = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Inductance", "Default");
                    MotorRotorInertia = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rotor Inertia", "Default");
                    MotorBackEMF = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Back EMF", "Default");
                    MotorTorqueConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Torque Constant", "Default");
                    MotorMechanicalConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Mechanical Constant", "Default");

                    LinearMotorPitch = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Linear Motor Pitch", "Default");//由Lilbert增加直线电机节距
                    OverSpeedValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Over Speed Value", "Default");//由Lilbert增加超速预警阈值

                    ABSEncoderSingleTurnBit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Single-Turn Bit", "Default");
                    ABSEncoderMultiTurnBit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Multi-Turn Bit", "Default");
                    ABSEncoderOffset = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Offset", "Default");
                    ABZEncoderPulses = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abz Encoder Pulses", "Default");

                    BissCEncoderLength = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Biss-C Encoder length", "Default");  //由Lilbert增加Biss-C总位置长度

                    strMotorRatedVoltageValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Voltage", "Default");
                    if (strMotorRatedVoltageValue == "24")
                    {
                        SelectedMotorRatedVoltage = "24V";
                    }
                    else if (strMotorRatedVoltageValue == "36")
                    {
                        SelectedMotorRatedVoltage = "36V";
                    }
                    else if (strMotorRatedVoltageValue == "48")
                    {
                        SelectedMotorRatedVoltage = "48V";
                    }
                    else if (strMotorRatedVoltageValue == "60")
                    {
                        SelectedMotorRatedVoltage = "60V";
                    }
                    else if (strMotorRatedVoltageValue == "110")
                    {
                        SelectedMotorRatedVoltage = "110V";
                    }
                    else if (strMotorRatedVoltageValue == "220")
                    {
                        SelectedMotorRatedVoltage = "220V";
                    }
                    else if (strMotorRatedVoltageValue == "380")
                    {
                        SelectedMotorRatedVoltage = "380V";
                    }

                    strMotorTypeValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Type", "Default");
                    if (strMotorTypeValue == "0")
                    {
                        SelectedMotorType = "旋转电机";
                    }
                    else
                    {
                        SelectedMotorType = "直线电机";
                    }

                    strEncoderValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Encoder Type", "Default");
                    if (strEncoderValue == "1")
                    {
                        SelectedEncoderType = "增量式编码器";
                    }
                    else if (strEncoderValue == "2")
                    {
                        SelectedEncoderType = "多摩川编码器";
                    }
                    else if (strEncoderValue == "3")
                    {
                        SelectedEncoderType = "尼康编码器4M";
                    }
                    else if (strEncoderValue == "4")
                    {
                        SelectedEncoderType = "尼康编码器2.5M";
                    }
                    else if (strEncoderValue == "5")
                    {
                        SelectedEncoderType = "旋转式编码器";      //由Lilbert添加旋转式编码器
                    }
                    else if (strEncoderValue == "6")
                    {
                        SelectedEncoderType = "通信型增量式编码器";     //由Lilbert添加通信型增量式编码器
                    }
                    else if (strEncoderValue == "7")
                    {
                        SelectedEncoderType = "BISS-C绝对式式编码器";   //由Lilbert添加BISS-C绝对式式编码器
                    }
                    else if (strEncoderValue == "8")
                    {
                        SelectedEncoderType = "BISS-C增量式编码器";     //由Lilbert添加BISS-C增量式编码器
                    }
                    else if (strEncoderValue == "9")
                    {
                        SelectedEncoderType = "ABZ增量式编码器+HALL";     //由Lilbert添加ABZ增量式编码器+Hall
                    }
                    else if (strEncoderValue == "10")
                    {
                        SelectedEncoderType = "HALL-UVW";     //由Lilbert添加HALL-UVW
                    }
                    else
                    {
                        SelectedEncoderType = "SSI编码器";     //由Lilbert添加SSI编码器
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_GET_DEFAULT_PARAMETER, "GetDefaultMotorFeedbackParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：SaveMotorConfigFile
        //函数功能：保存电机配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.23
        //*************************************************************************
        public void SaveMotorConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            string strFileName = null;
            string strDateTime = null;

            try
            {
                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否保存到电机参数库中...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    strDateTime = DateTime.Now.ToString();
                    strFileName = "电机反馈_配置文件" + strDateTime.Replace("/", "").Replace(":", "").Replace(" ", "_");
                    strFilePath = FilePath.MotorLibrary + strFileName + ".xlsx";

                    //写入参数库日志
                    iRet = ExcelHelper.WriteIntoExcel(FilePath.MotorLibraryLog, GetMotorLogToDataTable(strDateTime, strFileName), ExcelType.MotorLibraryLog);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }

                    //写入参数库
                    iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetMotorConfigToDataTable(), ExcelType.MotorConfig);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(RET.ERROR);
                    }
                    else
                    {
                        ShowNotification(2002);
                    }
                }
                else
                {
                    //选择配置文件存放位置
                    iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.MotorConfig);    
                    if (iRet == RET.NO_EFFECT)
                    {
                        return;
                    }

                    //生成配置文件
                    iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetMotorConfigToDataTable(), ExcelType.MotorConfig);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ShowNotification(2002);
                    }
                    else
                    {
                        ShowNotification(2003);
                    }
                }             
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_SAVE_MOTOR_CONFIG_FILE, "SaveMotorConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetMotorConfigFile
        //函数功能：获取电机配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void GetMotorConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            DataTable dt = null;

            try
            {
                if (MessageBoxService.ShowMessage("是否从电机参数库中加载并下载...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    MotorLibraryNavigation();
                    return;   
                }

                //获取配置文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(strFilePath, ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //判断是否导入正确的配置文件
                if (dt.Columns[3].ColumnName != FileInterface.MOTORFEEDBACK)
                {
                    ShowNotification(2001);
                    return;
                }
                 
                //导入参数          
                iRet = GetMotorConfigFromDataTable(dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //是否下载  
                OthersHelper.GetWindowsStartupPosition();
                if (MessageBoxService.ShowMessage("是否下载当前导入的全部电机反馈参数...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    WriteMotorFeedbackParameter("All");
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_GET_MOTOR_CONFIG_FILE, "GetMotorConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationMotorFeedbackParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.02&2021.10.10&2022.10.21
        //*************************************************************************
        public void EvaluationMotorFeedbackParameter()
        {
            string strEncoderValue = null;
            string strMotorTypeValue = null;
            string strMotorRatedVoltageValue = null;

            if (IsEvaluationAll)
            {
                //标志位更新
                IsEvaluationAll = false;

                //赋值
                SelectedMotorType = GlobalCurrentInput.SelectedMotorType;//电机类型
                //SelectedMotorID = GlobalCurrentInput.SelectedMotorID;//电机编号           

                MotorRatedPower = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Power", "Index"));//额定功率   
                MotorRatedFrequency = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Frequency", "Index"));//由Lilbert增加直线电机额定功率         
                //MotorRatedVoltage = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Voltage", "Index"));//额定电压
                MotorRatedCurrent = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Current", "Index"));//额定电流
                MotorRatedTorque = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Torque", "Index"));//额定转矩
                MotorRatedSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Speed", "Index"));//额定转速

                MotorMaxCurrent = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Current", "Index"));//最大电流
                MotorMaxTorque = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Torque", "Index"));//最大转矩
                MotorMaxSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Speed", "Index"));//最大转速

                //MotorPolePairsNumber = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Pole Pairs Number", "Index"));//极对数
                //MotorWindingResistance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Resistance", "Index"));//线电阻
                //MotorWindingInductance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Inductance", "Index"));//线电感
                //MotorRotorInertia = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rotor Inertia", "Index"));//转动惯量
                //MotorBackEMF = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Back EMF", "Index"));//电机反电势常数
                //MotorTorqueConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Torque Constant", "Index"));//转矩系数
                //MotorMechanicalConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Mechanical Constant", "Index"));//机械系数                

                LinearMotorPitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Linear Motor Pitch", "Index"));//直线电机节距  //由Lilbert增加直线电机节距
                //OverSpeedValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Over Speed Value", "Index"));//超速报警阈值  

                ABSEncoderSingleTurnBit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Single-Turn Bit", "Index"));//编码器单圈分辨率
                ABSEncoderMultiTurnBit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Multi-Turn Bit", "Index"));//编码器多圈分辨率
                //ABSEncoderOffset = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Offset", "Index"));//绝对式编码器偏置
                ABZEncoderPulses = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abz Encoder Pulses", "Index"));//ABZ编码器脉冲数 

                BissCEncoderLength = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Biss-C Encoder length", "Index"));//Biss-C总位置长度  //由Lilbert增加Biss-C总位置长度

                strMotorRatedVoltageValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Voltage", "Index"));//额定电压
                switch (strMotorRatedVoltageValue)
                {
                    case "24":
                        SelectedMotorRatedVoltage = "24V";
                        break;
                    case "36":
                        SelectedMotorRatedVoltage = "36V";
                        break;
                    case "48":
                        SelectedMotorRatedVoltage = "48V";
                        break;
                    case "60":
                        SelectedMotorRatedVoltage = "60V";
                        break;
                    case "110":
                        SelectedMotorRatedVoltage = "110V";
                        break;
                    case "220":
                        SelectedMotorRatedVoltage = "220V";
                        break;
                    case "380":
                        SelectedMotorRatedVoltage = "380V";
                        break;
                    default:
                        break;
                }

                strMotorTypeValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Type", "Index"));//电机类型
                switch (strMotorTypeValue)
                {
                    case "0":
                        SelectedMotorType = "旋转电机";
                        break;
                    case "1":
                        SelectedMotorType = "直线电机";
                        break;
                    default:
                        break;
                }

                strEncoderValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Encoder Type", "Index"));//编码器类型  
                switch (strEncoderValue)
                {
                    case "1":
                        SelectedEncoderType = "增量式编码器";
                        break;
                    case "2":
                        SelectedEncoderType = "多摩川编码器";
                        break;
                    case "3":
                        SelectedEncoderType = "尼康编码器4M";
                        break;
                    case "4":
                        SelectedEncoderType = "尼康编码器2.5M";
                        break;
                    case "5":
                        SelectedEncoderType = "旋转式编码器";   //由Lilbert添加旋转式编码器
                        break;
                    case "6":
                        SelectedEncoderType = "通信型增量式编码器";   //由Lilbert添加通信型增量式编码器
                        break;
                    case "7":
                        SelectedEncoderType = "BISS-C绝对式编码器";   //由Lilbert添加BISS-C绝对式编码器
                        break;
                    case "8":
                        SelectedEncoderType = "BISS-C增量式编码器";   //由Lilbert添加BISS-C增量式编码器
                        break;
                    case "9":
                        SelectedEncoderType = "ABZ增量式编码器+HALL";//由Lilbert添加ABZ增量式编码器+HALL
                        break;
                    case "10":
                        SelectedEncoderType = "HALL-UVW";//由Lilbert添加HALL-UVW
                        break;
                    case "11":
                        SelectedEncoderType = "SSI编码器";//由Lilbert添加SSI编码器
                        break;
                    default:
                        break;
                }
            }
            else
            {
                SelectedMotorType = GlobalCurrentInput.SelectedMotorType;//电机类型
                //SelectedMotorID = GlobalCurrentInput.SelectedMotorID;//电机编号           

                MotorRatedPower = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Power", "Index"));//额定功率   
                MotorRatedFrequency = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Frequency", "Index"));//由Lilbert增加直线电机额定频率         
                //MotorRatedVoltage = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Voltage", "Index"));//额定电压
                MotorRatedCurrent = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Current", "Index"));//额定电流
                MotorRatedTorque = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Torque", "Index"));//额定转矩
                MotorRatedSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Speed", "Index"));//额定转速

                MotorMaxCurrent = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Current", "Index"));//最大电流
                MotorMaxTorque = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Torque", "Index"));//最大转矩
                MotorMaxSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Max Speed", "Index"));//最大转速

                //MotorPolePairsNumber = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Pole Pairs Number", "Index"));//极对数
                //MotorWindingResistance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Resistance", "Index"));//线电阻
                //MotorWindingInductance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Inductance", "Index"));//线电感
                //MotorRotorInertia = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rotor Inertia", "Index"));//转动惯量
                //MotorBackEMF = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Back EMF", "Index"));//电机反电势常数
                //MotorTorqueConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Torque Constant", "Index"));//转矩系数
                //MotorMechanicalConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Mechanical Constant", "Index"));//机械系数

                LinearMotorPitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Linear Motor Pitch", "Index"));//直线电机节距  //由Lilbert增加直线电机节距
                //OverSpeedValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Over Speed Value", "Index"));//超速报警阈值  

                ABSEncoderSingleTurnBit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Single-Turn Bit", "Index"));//编码器单圈分辨率
                ABSEncoderMultiTurnBit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Multi-Turn Bit", "Index"));//编码器多圈分辨率
                //ABSEncoderOffset = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Offset", "Index"));//绝对式编码器偏置
                ABZEncoderPulses = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abz Encoder Pulses", "Index"));//ABZ编码器脉冲数 

                BissCEncoderLength = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Biss-C Encoder length", "Index"));//Biss-C总位置长度  //由Lilbert增加Biss-C总位置长度

                strMotorRatedVoltageValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Rated Voltage", "Index"));//额定电压
                switch (strMotorRatedVoltageValue)
                {
                    case "24":
                        SelectedMotorRatedVoltage = "24V";
                        break;
                    case "36":
                        SelectedMotorRatedVoltage = "36V";
                        break;
                    case "48":
                        SelectedMotorRatedVoltage = "48V";
                        break;
                    case "60":
                        SelectedMotorRatedVoltage = "60V";
                        break;
                    case "110":
                        SelectedMotorRatedVoltage = "110V";
                        break;
                    case "220":
                        SelectedMotorRatedVoltage = "220V";
                        break;
                    case "380":
                        SelectedMotorRatedVoltage = "380V";
                        break;
                    default:
                        break;
                }

                strMotorTypeValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Type", "Index"));//电机类型
                switch (strMotorTypeValue)
                {
                    case "0":
                        SelectedMotorType = "旋转电机";
                        break;
                    case "1":
                        SelectedMotorType = "直线电机";
                        break;
                    default:
                        break;
                }

                strEncoderValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Encoder Type", "Index"));//编码器类型  
                switch (strEncoderValue)
                {
                    case "1":
                        SelectedEncoderType = "增量式编码器";
                        break;
                    case "2":
                        SelectedEncoderType = "多摩川编码器";
                        break;
                    case "3":
                        SelectedEncoderType = "尼康编码器4M";
                        break;
                    case "4":
                        SelectedEncoderType = "尼康编码器2.5M";
                        break;
                    case "5":
                        SelectedEncoderType = "旋转式编码器";   //由Lilbert添加旋转式编码器
                        break;
                    case "6":
                        SelectedEncoderType = "通信型增量式编码器";   //由Lilbert添加通信型增量式编码器
                        break;
                    case "7":
                        SelectedEncoderType = "BISS-C绝对式编码器";   //由Lilbert添加BISS-C绝对式编码器
                        break;
                    case "8":
                        SelectedEncoderType = "BISS-C增量式编码器";   //由Lilbert添加BISS-C增量式编码器
                        break;
                    case "9":
                        SelectedEncoderType = "ABZ增量式编码器+HALL";//由Lilbert添加ABZ增量式编码器+HALL
                        break;
                    case "10":
                        SelectedEncoderType = "HALL-UVW";//由Lilbert添加HALL-UVW
                        break;
                    case "11":
                        SelectedEncoderType = "SSI编码器";//由Lilbert添加SSI编码器
                        break;
                    default:
                        break;
                }
               
            }           
        }

        //*************************************************************************
        //函数名称：UnitNavigation
        //函数功能：单位设置导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.24
        //*************************************************************************
        public void UnitNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                if (!IsEncoderSet)
                {
                    ShowHintInfo("请留意【编码器】配置");
                    IsEncoderSet = true;
                }
                
                SoftwareStateParameterSet.CurrentPageName = PageName.UNIT;
                NavigationService.Navigate("UnitView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：CommunicationSetNavigation
        //函数功能：通信配置导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.01
        //*************************************************************************
        public void CommunicationSetNavigation()
        {
            //SoftwareStateParameterSet.CurrentPageName = PageName.COMMUNICATIONSET;
            //ViewModelSet.Main?.CommunicationSetNavigation();
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.COMMUNICATIONSET;
                ViewModelSet.Main?.CommunicationSetNavigation();
            }
        }

        //*************************************************************************
        //函数名称：MotorFeedbackAutoLearnNavigation
        //函数功能：电机参数自学习页面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.02
        //*************************************************************************
        public void MotorFeedbackAutoLearnNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.MOTORFEEDBACKAUTOLEARN;
                NavigationService.Navigate("MotorFeedbackAutoLearnView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：MotorLibraryNavigation
        //函数功能：电机参数库导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.26
        //*************************************************************************
        public void MotorLibraryNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.MOTORLIBRARY;
                NavigationService.Navigate("MotorLibraryView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：EncoderAlreadySet
        //函数功能：编码器已经设置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.24
        //*************************************************************************
        public void EncoderAlreadySet()
        {
            IsEncoderSet = true;
        }      

        //*************************************************************************
        //函数名称：ChangeMotorParameterIdentification
        //函数功能：是否更改电机参数识别
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.27
        //*************************************************************************
        public void MotorParameterIdentification()
        {
            int iRet = -1;
            
            try
            {
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return;
                }

                if (ViewModelSet.MotorParameterIdentification == null)
                {
                    ViewModelSet.MotorParameterIdentification = new MotorParameterIdentificationViewModel();
                }
           
                UICommand cancelCommand = new UICommand()
                {
                    Caption = "返回",
                    IsCancel = true,
                };

                UICommand result  = DialogService.ShowDialog(new List<UICommand>() { cancelCommand }, "电机参数辨识", ViewModelSet.MotorParameterIdentification);                                       
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_MOTOR_PARAMETER_IDENTIFICATION, "MotorParameterIdentification", ex);
            }  
        }

        public void OnSelectedMotorTypeChanged()//电机类型
        {
            GlobalCurrentInput.SelectedMotorType = SelectedMotorType;

            if (SelectedMotorType == "旋转电机")
            {
                C1 = 1;
                C2 = 2;
                C3 = 3;
                IsRotatingMotorPageEnabled = ControlVisibility.Visible;
                IsLinearMotorPageEnabled = ControlVisibility.Collapsed;
            }            
            else if (SelectedMotorType == "直线电机" && SelectedEncoderType == "BISS-C绝对式编码器")
            {
                C1 = 5;
                C2 = 6;
                C3 = 7;
                IsRotatingMotorPageEnabled = ControlVisibility.Collapsed;
                IsLinearMotorPageEnabled = ControlVisibility.Visible;
            }
            else
            {
                IsRotatingMotorPageEnabled = ControlVisibility.Collapsed;
                IsLinearMotorPageEnabled = ControlVisibility.Visible;
            }
        }

        public void OnSelectedEncoderTypeChanged()//编码器类型
        {
            GlobalCurrentInput.SelectedEncoderType = SelectedEncoderType;

            if (SelectedEncoderType == "BISS-C绝对式编码器" && SelectedMotorType == "直线电机")
            {
                C1 = 5;
                C2 = 6;
                C3 = 7;
                IsBISSCPageEnabled = ControlVisibility.Visible;
                //IsLinearMotorPageEnabled = ControlVisibility.Collapsed;
            }
            else if (SelectedEncoderType == "BISS-C绝对式编码器")
            {
                C1 = 1;
                C2 = 2;
                C3 = 3;
                IsBISSCPageEnabled = ControlVisibility.Visible;
            }
            else
            {
                IsBISSCPageEnabled = ControlVisibility.Collapsed;
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        public void OnSelectedMotorIDChanged() { GlobalCurrentInput.SelectedMotorID = SelectedMotorID; }//电机编号

        public void OnMotorRatedPowerChanged() { GlobalCurrentInput.MotorRatedPower = MotorRatedPower; }//额定功率
        public void OnMotorRatedFrequencyChanged() { GlobalCurrentInput.MotorRatedFrequency = MotorRatedFrequency; }//额定频率
        //public void OnMotorRatedVoltageChanged() { GlobalCurrentInput.MotorRatedVoltage = MotorRatedVoltage; }//额定电压
        public void OnSelectedMotorRatedVoltageChanged() { GlobalCurrentInput.SelectedMotorRatedVoltage = SelectedMotorRatedVoltage; }//额定电压
        public void OnMotorRatedCurrentChanged() { GlobalCurrentInput.MotorRatedCurrent = MotorRatedCurrent; }//额定电流
        public void OnMotorRatedTorqueChanged() { GlobalCurrentInput.MotorRatedTorque = MotorRatedTorque; }//额定转矩
        public void OnMotorRatedSpeedChanged() { GlobalCurrentInput.MotorRatedSpeed = MotorRatedSpeed; }//额定转速

        public void OnMotorMaxCurrentChanged() { GlobalCurrentInput.MotorMaxCurrent = MotorMaxCurrent; }//最大电流
        public void OnMotorMaxTorqueChanged() { GlobalCurrentInput.MotorMaxTorque = MotorMaxTorque; }//最大转矩
        public void OnMotorMaxSpeedChanged() { GlobalCurrentInput.MotorMaxSpeed = MotorMaxSpeed; }//最大转速

        public void OnMotorPolePairsNumberChanged() { GlobalCurrentInput.MotorPolePairsNumber = MotorPolePairsNumber; }//极对数     
        public void OnMotorWindingResistanceChanged() { GlobalCurrentInput.MotorWindingResistance = MotorWindingResistance; }//线电阻
        public void OnMotorWindingInductanceChanged() { GlobalCurrentInput.MotorWindingInductance = MotorWindingInductance; }//线电感
        public void OnMotorRotorInertiaChanged() { GlobalCurrentInput.MotorRotorInertia = MotorRotorInertia; }//转动惯量
        public void OnMotorBackEMFChanged() { GlobalCurrentInput.MotorBackEMF = MotorBackEMF; }//反电势常数
        public void OnMotorTorqueConstantChanged() { GlobalCurrentInput.MotorTorqueConstant = MotorTorqueConstant; }//转矩系数
        public void OnMotorMechanicalConstantChanged() { GlobalCurrentInput.MotorMechanicalConstant = MotorMechanicalConstant; }//机械系数

        public void OnLinearMotorPitchChanged() { GlobalCurrentInput.LinearMotorPitch = LinearMotorPitch; }//直线电机节距   //由Lilbert增加直线电机节距
        public void OnOverSpeedValueChanged() { GlobalCurrentInput.OverSpeedValue = OverSpeedValue; }//超速预警阈值        //由Lilbert增加超速预警阈值        
        public void OnABSEncoderSingleTurnBitChanged() { GlobalCurrentInput.ABSEncoderSingleTurnBit = ABSEncoderSingleTurnBit; }//单圈分辨率
        public void OnABSEncoderMultiTurnBitChanged() { GlobalCurrentInput.ABSEncoderMultiTurnBit = ABSEncoderMultiTurnBit; }//多圈分辨率
        public void OnABSEncoderOffsetChanged() { GlobalCurrentInput.ABSEncoderOffset = ABSEncoderOffset; }//绝对式编码器偏置
        public void OnABZEncoderPulsesChanged() { GlobalCurrentInput.ABZEncoderPulses = ABZEncoderPulses; }//ABZ编码器脉冲数  
        public void OnBissCEncoderLengthChanged() { GlobalCurrentInput.BissCEncoderLength = BissCEncoderLength; }//Biss-C总位置长度   //由Lilbert增加Biss-C总位置长度
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：GetMotorConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.10.23&2022.10.21
        //*************************************************************************
        private DataTable GetMotorConfigToDataTable()
        {
            DataTable dt = new DataTable();

            try
            {
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Type", "电机类型", SelectedMotorType, "", ref dt);
                //OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor ID", "电机编号", SelectedMotorID, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Rated Frequency", "额定频率", MotorRatedFrequency, "", ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Rated Power", "额定功率", MotorRatedPower, "0.01KW", ref dt);
                //OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Rated Voltage", "额定电压", MotorRatedVoltage, "V", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Rated Voltage", "额定电压", SelectedMotorRatedVoltage, "V", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Rated Current", "额定电流", MotorRatedCurrent,"0.1A", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Rated Torque", "额定转矩", MotorRatedTorque, "0.01Nm", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Rated Speed", "额定转速", MotorRatedSpeed, "rpm", ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Max Current", "最大电流", MotorMaxCurrent, "0.1A", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Max Torque", "最大转矩", MotorMaxTorque, "0.01Nm", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Max Speed", "最大转速", MotorMaxSpeed, "rpm", ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Pole Pairs Number", "极对数", MotorPolePairsNumber, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Winding Resistance", "线电阻", MotorWindingResistance, "0.001Ohm", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Winding Inductance", "线电感", MotorWindingInductance, "0.01mH", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Rotor Inertia", "转动惯量", MotorRotorInertia, "0.01kg*cm2", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Back EMF", "电机反电势常数", MotorBackEMF, "0.01mV/rpm", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Torque Constant", "转矩常数", MotorTorqueConstant, "0.01N.m/A", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Motor Mechanical Constant", "机械常数", MotorMechanicalConstant, "0.01ms", ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Linear Motor Pitch", "直线电机节距", LinearMotorPitch, "mm", ref dt);  //由Lilbert增加直线电机节距
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Over Speed Value", "超速预警阈值", OverSpeedValue, "rpm", ref dt);    //由Lilbert增加超速预警阈值 

                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"Encoder Type", "编码器类型", SelectedEncoderType, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "ABS Encoder Multi-Turn Bit", "多圈值分辨率位数", ABSEncoderMultiTurnBit, "bit", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"ABS Encoder Single-Turn Bit", "单圈值分辨率位数", ABSEncoderSingleTurnBit, "bit", ref dt); 
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"ABS Encoder Offset", "绝对式编码器偏置", ABSEncoderOffset, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK,"ABZ Encoder Pulses", "ABZ编码器脉冲数", ABZEncoderPulses, "1P/Rev", ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Biss-C Encoder length", "Biss-C总位置长度", BissCEncoderLength, "", ref dt);  //由Lilbert增加Biss-C总位置长度

                return dt;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_GET_MOTOR_CONFIF_TO_DATATABLE,"GetMotorConfigToDataTable", ex);
                return null;
            }
        }
        private DataTable GetMotorLogToDataTable(string DateTime, string FileName)
        {
            DataTable dtMotorLibraryLog = new DataTable();
            ExcelHelper.ReadFromExcel(FilePath.MotorLibraryLog, ref dtMotorLibraryLog);

            if (dtMotorLibraryLog.Columns.Count == 0)
            {
                DataColumnCollection columns = dtMotorLibraryLog.Columns;
                columns.Add("Name", typeof(String));
                columns.Add("MotorType", typeof(String));
                columns.Add("EncoderType", typeof(String));
                columns.Add("Author", typeof(String));
                columns.Add("DateTime", typeof(String));
                columns.Add("Comment", typeof(String));
                columns.Add("Valid", typeof(String));
            }

            DataRow clsDataRow = dtMotorLibraryLog.NewRow();
            clsDataRow["Name"] = FileName;
            clsDataRow["MotorType"] = "Unknown";
            clsDataRow["EncoderType"] = SelectedEncoderType;
            clsDataRow["Author"] = "ServoStudio";
            clsDataRow["DateTime"] = DateTime;
            clsDataRow["Comment"] = "";
            clsDataRow["Valid"] = "True";
            dtMotorLibraryLog.Rows.Add(clsDataRow);
          
            return dtMotorLibraryLog;
        }

        //*************************************************************************
        //函数名称：GetMotorConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.13&2022.10.21
        //*************************************************************************
        private int GetMotorConfigFromDataTable(DataTable dt)
        {
            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                SelectedMotorType = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Type", "Default");
                SelectedMotorID = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor ID", "Default");
                MotorRatedFrequency = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Rated Frequency", "Default");

                MotorRatedPower = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Rated Power", "Default");
                //MotorRatedVoltage = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Rated Voltage", "Default");
                SelectedMotorRatedVoltage = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Rated Voltage", "Default");
                MotorRatedCurrent = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Rated Current", "Default");
                MotorRatedTorque = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Rated Torque", "Default");
                MotorRatedSpeed = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Rated Speed", "Default");

                MotorMaxCurrent = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Max Current", "Default");
                MotorMaxTorque = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Max Torque", "Default");
                MotorMaxSpeed = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Max Speed", "Default");

                MotorPolePairsNumber = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Pole Pairs Number", "Default");
                MotorWindingResistance = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Winding Resistance", "Default");
                MotorWindingInductance = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Winding Inductance", "Default");
                MotorRotorInertia = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Rotor Inertia", "Default");
                MotorBackEMF = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Back EMF", "Default");
                MotorTorqueConstant = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Torque Constant", "Default");
                MotorMechanicalConstant = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Mechanical Constant", "Default");

                LinearMotorPitch = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Linear Motor Pitch", "Default");   //由Lilbert增加直线电机节距
                OverSpeedValue = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Over Speed Value", "Default");      //由Lilbert增加超速预警阈值 

                SelectedEncoderType = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Encoder Type", "Default");

                ABSEncoderSingleTurnBit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Abs Encoder Single-Turn Bit", "Default");
                ABSEncoderMultiTurnBit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Abs Encoder Multi-Turn Bit", "Default");
                ABSEncoderOffset = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Abs Encoder Offset", "Default");
                ABZEncoderPulses = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Abz Encoder Pulses", "Default");

                BissCEncoderLength = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Biss-C Encoder length", "Default");  //由Lilbert增加Biss-C总位置长度

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_GET_MOTOR_CONFIG_FROM_DATATABLE, "GetMotorConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：属性初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.04.15&2021.10.10&2022.10.21
        //*************************************************************************
        private void ComboBoxInitialize()
        {
            try
            {
                EncoderType = new ObservableCollection<string>() { "增量式编码器", "多摩川编码器", "尼康编码器4M","尼康编码器2.5M","旋转式编码器", "通信型增量式编码器", "BISS-C绝对式编码器", "ABZ增量式编码器+HALL", "HALL-UVW", "SSI编码器" };   //由Lilbert添加旋转式编码器
                SelectedEncoderType = "增量式编码器";

                MotorRatedVoltage = new ObservableCollection<string>() { "24V", "36V", "48V", "60V", "110V", "220V", "380V" };
                SelectedMotorRatedVoltage = "220V";

                MotorID = new ObservableCollection<string>() { "电机型号1" };
                MotorType = new ObservableCollection<string>() { "旋转电机", "直线电机" };  //由Lilbert增加直线电机

                //目前不需要
                SelectedMotorID = "电机型号1";
                SelectedMotorType = "旋转电机";              
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_PROPERTY_INITIALIZE, "ComboBoxInitialize", ex);
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.18&2022.10.21
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                SelectedMotorType = GlobalCurrentInput.SelectedMotorType;//电机类型
                SelectedMotorID = GlobalCurrentInput.SelectedMotorID;//电机编号
                SelectedEncoderType = GlobalCurrentInput.SelectedEncoderType;//编码器类型
                MotorRatedFrequency = GlobalCurrentInput.MotorRatedFrequency;//额定频率

                MotorRatedPower = GlobalCurrentInput.MotorRatedPower;//额定功率
                //MotorRatedVoltage = GlobalCurrentInput.MotorRatedVoltage;//额定电压
                SelectedMotorRatedVoltage = GlobalCurrentInput.SelectedMotorRatedVoltage;//额定电压
                MotorRatedCurrent = GlobalCurrentInput.MotorRatedCurrent;//额定电流
                MotorRatedTorque = GlobalCurrentInput.MotorRatedTorque;//额定转矩
                MotorRatedSpeed = GlobalCurrentInput.MotorRatedSpeed;//额定转速

                MotorMaxCurrent = GlobalCurrentInput.MotorMaxCurrent;//最大电流
                MotorMaxTorque = GlobalCurrentInput.MotorMaxTorque;//最大转矩
                MotorMaxSpeed = GlobalCurrentInput.MotorMaxSpeed;//最大转速

                MotorPolePairsNumber = GlobalCurrentInput.MotorPolePairsNumber;//极对数
                MotorWindingResistance = GlobalCurrentInput.MotorWindingResistance;//线电阻
                MotorWindingInductance = GlobalCurrentInput.MotorWindingInductance;//线电感
                MotorRotorInertia = GlobalCurrentInput.MotorRotorInertia;//转动惯量
                MotorBackEMF = GlobalCurrentInput.MotorBackEMF;//电机反电势常数
                MotorTorqueConstant = GlobalCurrentInput.MotorTorqueConstant;//转矩系数
                MotorMechanicalConstant = GlobalCurrentInput.MotorMechanicalConstant;//机械系数

                LinearMotorPitch = GlobalCurrentInput.LinearMotorPitch;//直线电机节距  //由Lilbert增加直线电机节距
                OverSpeedValue = GlobalCurrentInput.OverSpeedValue;//超速预警阈值     //由Lilbert增加超速预警阈值 

                ABSEncoderSingleTurnBit = GlobalCurrentInput.ABSEncoderSingleTurnBit;//编码器单圈分辨率
                ABSEncoderMultiTurnBit = GlobalCurrentInput.ABSEncoderMultiTurnBit;//编码器多圈分辨率
                ABSEncoderOffset = GlobalCurrentInput.ABSEncoderOffset;//绝对式编码器偏置
                ABZEncoderPulses = GlobalCurrentInput.ABZEncoderPulses;//ABZ编码器脉冲数

                BissCEncoderLength = GlobalCurrentInput.BissCEncoderLength;//Biss-C总位置长度   //由Lilbert增加Biss-C总位置长度
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.02&2021.10.10&2022.10.21
        //*************************************************************************
        private int AddParameterInfoDictionary(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            string strEncoderValue = null;
            string strMotorTypeValue = null;
            string strMotorRatedVoltageValue = null;
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                switch (SelectedMotorType)//由Lilbert增加电机类型
                {
                    case "旋转电机":
                        strMotorTypeValue = "0";
                        break;
                    case "直线电机":
                        strMotorTypeValue = "1";
                        break;
                    default:
                        break;
                }

                switch (SelectedMotorRatedVoltage)
                {
                    case "24V":
                        strMotorRatedVoltageValue = "24";
                        break;
                    case "36V":
                        strMotorRatedVoltageValue = "36";
                        break;
                    case "48V":
                        strMotorRatedVoltageValue = "48";
                        break;
                    case "60V":
                        strMotorRatedVoltageValue = "60";
                        break;
                    case "110V":
                        strMotorRatedVoltageValue = "110";
                        break;
                    case "220V":
                        strMotorRatedVoltageValue = "220";
                        break;
                    case "380V":
                        strMotorRatedVoltageValue = "380";
                        break;
                    default:
                        break;
                }

                switch (SelectedEncoderType)
                {
                    case "增量式编码器":
                        strEncoderValue = "1";
                        break;
                    case "多摩川编码器":
                        strEncoderValue = "2";
                        break;
                    case "尼康编码器4M":
                        strEncoderValue = "3";
                        break;
                    case "尼康编码器2.5M":
                        strEncoderValue = "4";
                        break;
                    case "旋转式编码器":
                        strEncoderValue = "5";   //由Lilbert添加旋转式编码器
                        break;
                    case "通信型增量式编码器":   //由Lilbert添加通信型增量式编码器
                        strEncoderValue = "6";
                        break;
                    case "BISS-C绝对式编码器":   //由Lilbert添加BISS-C绝对式编码器
                        strEncoderValue = "7";
                        break;
                    case "BISS-C增量式编码器":   //由Lilbert添加BISS-C增量式编码器
                        strEncoderValue = "8";
                        break;
                    case "ABZ增量式编码器+HALL":   //由Lilbert添加ABZ增量式编码器+HALL
                        strEncoderValue = "9";
                        break;
                    case "HALL-UVW":   //由Lilbert添加HALL-UVW
                        strEncoderValue = "10";
                        break;
                    case "SSI编码器":   //由Lilbert添加SSI编码器
                        strEncoderValue = "11";
                        break;
                    default:
                        break;
                }

                switch (strCategory)
                {
                    case "0":
                        dicParameterInfo.Add("Motor Type", strMotorTypeValue);//由Lilbert增加电机类型
                        dicParameterInfo.Add("Motor Rated Frequency", MotorRatedFrequency);//由Lilbert增加电机频率
                        dicParameterInfo.Add("Motor Rated Power", MotorRatedPower);
                        //dicParameterInfo.Add("Motor Rated Voltage", MotorRatedVoltage);
                        dicParameterInfo.Add("Motor Rated Voltage", strMotorRatedVoltageValue);
                        dicParameterInfo.Add("Motor Rated Current", MotorRatedCurrent);
                        dicParameterInfo.Add("Motor Rated Torque", MotorRatedTorque);
                        dicParameterInfo.Add("Motor Rated Speed", MotorRatedSpeed);
                        dicParameterInfo.Add("Motor Max Current", MotorMaxCurrent);
                        dicParameterInfo.Add("Motor Max Torque", MotorMaxTorque);
                        dicParameterInfo.Add("Motor Max Speed", MotorMaxSpeed);

                        dicParameterInfo.Add("Motor Pole Pairs Number", MotorPolePairsNumber);
                        dicParameterInfo.Add("Motor Winding Resistance", MotorWindingResistance);
                        dicParameterInfo.Add("Motor Winding Inductance", MotorWindingInductance);
                        dicParameterInfo.Add("Motor Rotor Inertia", MotorRotorInertia);
                        dicParameterInfo.Add("Motor Back EMF", MotorBackEMF);
                        dicParameterInfo.Add("Motor Torque Constant", MotorTorqueConstant);
                        dicParameterInfo.Add("Motor Mechanical Constant", MotorMechanicalConstant);

                        dicParameterInfo.Add("Linear Motor Pitch", LinearMotorPitch);   //由Lilbert增加直线电机节距
                        dicParameterInfo.Add("Biss-C Encoder length", BissCEncoderLength);  //由Lilbert增加Biss-C总位置长度

                        dicParameterInfo.Add("Over Speed Value", OverSpeedValue);      //由Lilbert增加超速预警阈值  
                        break;
                    case "1":
                        dicParameterInfo.Add("Encoder Type", strEncoderValue);
                        dicParameterInfo.Add("Abs Encoder Single-Turn Bit", ABSEncoderSingleTurnBit);
                        dicParameterInfo.Add("Abs Encoder Multi-Turn Bit", ABSEncoderMultiTurnBit);
                        dicParameterInfo.Add("Abs Encoder Offset", ABSEncoderOffset);
                        dicParameterInfo.Add("Abz Encoder Pulses", ABZEncoderPulses);

                        dicParameterInfo.Add("Biss-C Encoder length", BissCEncoderLength);  //由Lilbert增加Biss-C总位置长度
                        break;
                    default:
                        dicParameterInfo.Add("Motor Type", strMotorTypeValue);//由Lilbert增加电机类型
                        dicParameterInfo.Add("Motor Rated Frequency", MotorRatedFrequency);//由Lilbert增加电机频率
                        dicParameterInfo.Add("Motor Rated Power", MotorRatedPower);
                        //dicParameterInfo.Add("Motor Rated Voltage", MotorRatedVoltage);
                        dicParameterInfo.Add("Motor Rated Voltage", strMotorRatedVoltageValue);
                        dicParameterInfo.Add("Motor Rated Current", MotorRatedCurrent);
                        dicParameterInfo.Add("Motor Rated Torque", MotorRatedTorque);
                        dicParameterInfo.Add("Motor Rated Speed", MotorRatedSpeed);
                        dicParameterInfo.Add("Motor Max Current", MotorMaxCurrent);
                        dicParameterInfo.Add("Motor Max Torque", MotorMaxTorque);
                        dicParameterInfo.Add("Motor Max Speed", MotorMaxSpeed);

                        //dicParameterInfo.Add("Motor Pole Pairs Number", MotorPolePairsNumber);
                        //dicParameterInfo.Add("Motor Winding Resistance", MotorWindingResistance);
                        //dicParameterInfo.Add("Motor Winding Inductance", MotorWindingInductance);
                        //dicParameterInfo.Add("Motor Rotor Inertia", MotorRotorInertia);
                        //dicParameterInfo.Add("Motor Back EMF", MotorBackEMF);
                        //dicParameterInfo.Add("Motor Torque Constant", MotorTorqueConstant);
                        //dicParameterInfo.Add("Motor Mechanical Constant", MotorMechanicalConstant);

                        dicParameterInfo.Add("Linear Motor Pitch", LinearMotorPitch);   //由Lilbert增加直线电机节距
                        dicParameterInfo.Add("Biss-C Encoder length", BissCEncoderLength);  //由Lilbert增加Biss-C总位置长度

                        //dicParameterInfo.Add("Over Speed Value", OverSpeedValue);      //由Lilbert增加超速预警阈值                         

                        dicParameterInfo.Add("Encoder Type", strEncoderValue);
                        dicParameterInfo.Add("Abs Encoder Single-Turn Bit", ABSEncoderSingleTurnBit);
                        dicParameterInfo.Add("Abs Encoder Multi-Turn Bit", ABSEncoderMultiTurnBit);
                        //dicParameterInfo.Add("Abs Encoder Offset", ABSEncoderOffset);
                        dicParameterInfo.Add("Abz Encoder Pulses", ABZEncoderPulses);

                        //dicParameterInfo.Add("Pitch Motor Encoder Lines", PitchMotorEncoderLines);  //由Lilbert增加每转线数
                        break;
                }
               
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowHintInfo(string strInfo)
        {
            ViewModelSet.Main?.ShowHintInfo(strInfo);
        }
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }
}