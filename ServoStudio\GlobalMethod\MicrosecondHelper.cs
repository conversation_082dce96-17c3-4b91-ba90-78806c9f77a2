﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.InteropServices;
using System.Threading;
using ServoStudio.GlobalConstant;

namespace ServoStudio.GlobalMethod
{
    //基于系统性能计数器的定时器，计数单位是1微秒
    //注意：该定时器会独占一个CPU核心，尝试定时器与主程序运行在同一核心将导致程序失去响应
    public delegate void ThreadAbortHint();//线程退出委托
    public class MicrosecondHelper
    {
        #region 事件
        public event ThreadAbortHint evtThreadAbortHint;
        #endregion

        #region 字段
        /// <param name="sender">事件的发起者，即定时器对象</param>
        /// <param name="JumpPeriod">上次调用和本次调用跳跃的周期数</param>
        /// <param name="interval">上次调用和本次调用之间的间隔时间（微秒）</param>
        public delegate void OnTickHandle(object sender, long JumpPeriod, long interval);//定时器事件的委托定义
        private OnTickHandle _tick = null;//回调函数定义

        private ThreadStart _tsRunTimer = null;//线程起始函数
        private Thread _thRunTimer = null;//线程 
        private int _points = 0;//需要采样的个数     
        private bool _isDisposed = true;//是否销毁定时器
        private bool _isRunning = false;//是否正在运行定时器
        private bool _isInterrupted = false;//是否中断

        private int _delay = 0;//首次启动延时（微秒）
        private long _period = 0;//定时器周期（微秒）
        private byte _cpuIndex = 1;//定时器运行时独占的CPU核心索引序号
        private long _freq = 0;//系统性能计数频率（每秒）
        private long _freqmms = 0;//系统性能计数频率（每微秒）
        #endregion

        #region Kernel32 方法声明
        [DllImport("Kernel32.dll")]
        private static extern bool QueryPerformanceCounter(out long lpPerformanceCount);//获取当前系统性能计数
        [DllImport("Kernel32.dll")]
        private static extern bool QueryPerformanceFrequency(out long lpFrequency);//获取当前系统性能频率
        [DllImport("kernel32.dll")]
        private static extern UIntPtr SetThreadAffinityMask(IntPtr hThread, UIntPtr dwThreadAffinityMask);//指定某一特定线程运行在指定的CPU核心
        [DllImport("kernel32.dll")]
        private static extern IntPtr GetCurrentThread();//获取当前线程的Handler
        #endregion

        #region 构造函数
        /// <param name="delay">首次启动定时器延时时间（微秒）</param>
        /// <param name="period">定时器触发的周期（微秒）</param>
        /// <param name="cpuIndex">指定定时器线程独占的CPU核心索引，必须>0，不允许为定时器分配0#CPU</param>
        /// <param name="tick">定时器触发时的回调函数</param>
        public MicrosecondHelper(int delay, long period, byte cpuIndex, OnTickHandle tick)
        {
            try
            {                   
                long lFrequency = 0;
                QueryPerformanceFrequency(out lFrequency);

                if (lFrequency > 0)
                {
                    _freq = lFrequency;
                    _freqmms = lFrequency / 1000000;
                    _tick = tick;
                    _delay = delay;
                    _period = period;
                    _cpuIndex = cpuIndex;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MICROSECONDHELPER_CONSTRUCTOR, "MicrosecondHelper", ex);
            }
        }
        #endregion

        #region 公有方法
        public void Start(int iPoints)
        {
            try
            {
                if (_tick == null) return;

                _tsRunTimer = new ThreadStart(RunTimer);
                _thRunTimer = new Thread(_tsRunTimer);
                _thRunTimer.Name = "MicrosecondTimer Thread";
                _thRunTimer.IsBackground = true;

                _isDisposed = false;
                _isInterrupted = false;
                _isRunning = true;
                _points = iPoints;

                _thRunTimer.Start();
            }
            catch (System.Exception ex)
            {
                _thRunTimer.Abort();
                SoftwareErrorHelper.CatchDispose(ERROR.MICROSECONDHELPER_START, "MicrosecondHelper.Start", ex);
            }
        }

        public void Dispose()
        {
            _isDisposed = true;
            _isRunning = false;
            _isInterrupted = false;

            if (_thRunTimer.IsAlive) _thRunTimer.Abort();
        }

        public void Interrupt()
        {
            _isRunning = true;
            _isInterrupted = true;
            _isDisposed = false;
        }

        public void Restart()
        {
            _isRunning = true;
            _isInterrupted = false;
            _isDisposed = false;
        }
        #endregion

        #region 私有方法
        private void RunTimer()
        {
            int iPoints = 0;//采样个数
            long q1, q2;

            try
            {
                if (_cpuIndex != 0)
                {
                    UIntPtr up = SetThreadAffinityMask(GetCurrentThread(), new UIntPtr(GetCpuID(_cpuIndex)));
                    if (up == UIntPtr.Zero) return; //为定时器分配CPU核心时失败
                }

                QueryPerformanceCounter(out q1);
                QueryPerformanceCounter(out q2);

                if (_delay > 0)
                {
                    while (q2 < q1 + _delay * _freqmms)
                    {
                        QueryPerformanceCounter(out q2);
                    }
                }

                QueryPerformanceCounter(out q1);
                QueryPerformanceCounter(out q2);

                while (_isRunning)
                {
                    if (!_isInterrupted)
                    {
                        QueryPerformanceCounter(out q2);

                        if (q2 > q1 + _freqmms * _period)
                        {
                            if ((!_isDisposed) && (iPoints <= _points))
                            {
                                _tick(this, (q2 - q1) / (_freqmms * _period), (q2 - q1) / _freqmms);
                            }
                            else
                            {
                                return;
                            }

                            q1 = q2;
                            iPoints++;
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MICROSECONDHELPER_RUN_TIMER, "MicrosecondHelper.RunTimer", ex);
            }
            finally
            {
                //回调函数，告知前台数采完毕
                if (evtThreadAbortHint != null)
                    evtThreadAbortHint();

                _thRunTimer.Abort();               
            }
        }

        private ulong GetCpuID(int idx)//根据CPU的索引序号获取CPU的标识序号
        {
            ulong cpuid = 0;

            if (idx < 0 || idx >= System.Environment.ProcessorCount)
            {
                idx = 0;
            }

            cpuid |= 1UL << idx;

            return cpuid;
        }
        #endregion
    }
}
