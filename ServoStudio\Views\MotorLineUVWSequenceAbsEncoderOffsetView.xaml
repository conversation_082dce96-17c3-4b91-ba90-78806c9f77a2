﻿<UserControl x:Class="ServoStudio.Views.MotorLineUVWSequenceAbsEncoderOffsetView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking" 
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
             xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:Views="clr-namespace:ServoStudio.Views"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:converter="clr-namespace:Converter"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:MotorLineUVWSequenceAbsEncoderOffsetViewModel}"
             d:DesignHeight="850" d:DesignWidth="1400">
    <dxmvvm:Interaction.Behaviors>
        <dxwui:WinUIDialogService  DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:MotorParameterIdentificationView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxmvvm:EventToCommand EventName="Loaded" Command="{Binding MotorLineUVWSequenceAbsEncoderOffsetLoadedCommand}"/>
        <dxmvvm:EventToCommand Command="{Binding MotorLineUVWSequenceAbsEncoderOffsetUnloadedCommand}" EventName="Unloaded"/>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields" UseWin8NotificationsIfAvailable="True"/>
    </dxmvvm:Interaction.Behaviors>

    <UserControl.Resources>
        <converter:BackgroundConverter x:Key="ColorConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>
        
    </UserControl.Resources>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!--TabControl Name="tabControl" SelectedIndex="{Binding SelectedTabIndex}" Grid.Row="0" Padding="0" BorderBrush="LightGray" Background="{x:Null}" BorderThickness="0,1,0,0"-->
            <!--TabItem Header="电机" TabIndex="0"-->
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="170"/>
                    <ColumnDefinition Width="103"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="170"/>
                    <ColumnDefinition Width="103"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <!--<Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="类型选择" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                    <Label Grid.Row="1" Grid.Column="1" Margin="10,3" Content="电机类型" Style="{StaticResource LabelStyle}" />
                    <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Width="253" Margin="10,3" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding MotorType}" SelectedItem="{Binding SelectedMotorType,Mode=TwoWay}"/>-->

                <!--<Label Grid.Row="1" Grid.Column="5" Margin="10,3" Content="电机编号" Style="{StaticResource LabelStyle}"/>
                    <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Width="253" Margin="10,3" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding MotorID}"  SelectedItem="{Binding SelectedMotorID,Mode=TwoWay}"/>-->

                <!--<Label Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10,3,3" Style="{StaticResource LabelStyle}" Content="额定参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>-->

                <!--<Label Grid.Row="3" Grid.Column="1" Margin="10,2" Content="额定频率" Style="{StaticResource LabelStyle}"/>
                    <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding MotorRatedFrequency,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="3" Grid.Column="3" Text="Hz" Style="{StaticResource TextBoxStyle_Unit}"/>-->

                <!--<Label Grid.Row="3" Grid.Column="5" Margin="10,2" Content="额定功率" Style="{StaticResource LabelStyle}"/>
                    <TextBox Grid.Row="3" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding MotorRatedPower,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="3" Grid.Column="7" Text="0.01KW" Style="{StaticResource TextBoxStyle_Unit}"/>-->

                <!--<Label Grid.Row="4" Grid.Column="1" Margin="10,2" Content="额定电压" Style="{StaticResource LabelStyle}"/>
                    <TextBox Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding MotorRatedVoltage,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="4" Grid.Column="3" Text="V" Style="{StaticResource TextBoxStyle_Unit}"/>-->

                <!--<Label Grid.Row="4" Grid.Column="5" Margin="10,2" Content="额定电流" Style="{StaticResource LabelStyle}" />
                    <TextBox Grid.Row="4" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding MotorRatedCurrent,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="4" Grid.Column="7" Text="0.1A" Style="{StaticResource TextBoxStyle_Unit}"/>-->

                <!--<Label Grid.Row="5" Grid.Column="1" Margin="10,2" Content="额定转矩" Style="{StaticResource LabelStyle}" Visibility="{Binding IsRotatingMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>
                    <TextBox Grid.Row="5" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding MotorRatedTorque,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsRotatingMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>
                    <TextBox Grid.Row="5" Grid.Column="3" Text="0.01Nm" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsRotatingMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>-->

                <!--<Label Grid.Row="5" Grid.Column="1" Margin="10,2" Content="额定推力" Style="{StaticResource LabelStyle}" Visibility="{Binding IsLinearMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>
                    <TextBox Grid.Row="5" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding MotorRatedTorque,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsLinearMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>
                    <TextBox Grid.Row="5" Grid.Column="3" Text="0.01N" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsLinearMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>-->

                <!--<Label Grid.Row="5" Grid.Column="5" Margin="10,2" Content="额定速度" Style="{StaticResource LabelStyle}" Visibility="{Binding IsRotatingMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>
                    <TextBox Grid.Row="5" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding MotorRatedSpeed,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsRotatingMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>
                    <TextBox Grid.Row="5" Grid.Column="7" Text="rpm" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsRotatingMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>-->

                <!--<Label Grid.Row="5" Grid.Column="5" Margin="10,2" Content="额定速度" Style="{StaticResource LabelStyle}" Visibility="{Binding IsLinearMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>
                    <TextBox Grid.Row="5" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding MotorRatedSpeed,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsLinearMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>
                    <TextBox Grid.Row="5" Grid.Column="7" Text="mm/s" Style="{StaticResource TextBoxStyle_Unit}" Visibility="{Binding IsLinearMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>-->

                <!--<Label Grid.Row="6" Grid.Column="1" Margin="10,2" Content="编码器型号" Style="{StaticResource LabelStyle}" />
                    <TextBox Grid.Row="6" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding MotorEncoderType,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />-->
                <!--<TextBox Grid.Row="6" Grid.Column="3" Text="mm/s" Style="{StaticResource TextBoxStyle_Unit}" />-->

                <!--<Label Grid.Row="6" Grid.Column="5" Margin="10,9" Content="单圈值分辨率位数" Style="{StaticResource LabelStyle}" />
                    <TextBox Grid.Row="6" Grid.Column="6" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding ABSEncoderSingleTurnBit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="6" Grid.Column="7" Style="{StaticResource TextBoxStyle_Unit}" Text="bit"/>-->

                <!--<Label Grid.Row="7" Grid.Column="1" Margin="10,9" Content="多圈值分辨率位数" Style="{StaticResource LabelStyle}"/>
                    <TextBox Grid.Row="7" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9"  Text="{Binding ABSEncoderMultiTurnBit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="7" Grid.Column="3" Style="{StaticResource TextBoxStyle_Unit}" Text="bit"/>-->

                <!--<Label Grid.Row="7" Grid.Column="5" Margin="10,9" Content="ABZ编码器脉冲数" Style="{StaticResource LabelStyle}" />
                    <TextBox Grid.Row="7" Grid.Column="6" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding ABZEncoderPulses,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="7" Grid.Column="7" Style="{StaticResource TextBoxStyle_Unit}" Text="1P/Rev"/>-->

                <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="9" Margin="3" Style="{StaticResource LabelStyle}" Content="电机动力线相序" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                <!--<Label Grid.Row="1" Grid.Column="1" Margin="10,2" Content="电机动力线相序" Style="{StaticResource LabelStyle}" />
            <TextBox Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding MotorLineUVWSequence,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
            <TextBox Grid.Row="1" Grid.Column="3" Text="1" Style="{StaticResource TextBoxStyle_Unit}"/>-->
                <Label Grid.Row="1" Grid.Column="1" Margin="10,2" Content="电机动力线相序" Style="{StaticResource LabelStyle}" />
                <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Width="253" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding MotorLineUVWSequenceType}" SelectedItem="{Binding SelectedMotorLineUVWSequenceType,Mode=TwoWay}"/>

                <Label Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,3,3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                <StackPanel Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="5" Orientation="Horizontal" HorizontalAlignment="Left">
                    <!--<Label Style="{StaticResource LabelStyle}" Content="提示：参数正在识别，请耐心等待结果" Margin="10,4" Foreground="Green" HorizontalContentAlignment="Right" Visibility="{Binding HintVisibility, Converter={StaticResource VisibilityConverter}}"/>-->
                    <!--<Label Style="{StaticResource LabelStyle}" Content="提示：替换参数值，将在伺服重启后生效" Margin="10,4" Foreground="Red" HorizontalContentAlignment="Right"/>-->
                    <Label Style="{StaticResource LabelStyle}" Content="{Binding MotorLineUVWSequenceInformation,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Margin="10,4" Foreground="Red" HorizontalContentAlignment="Right"/>
                </StackPanel>

                <StackPanel Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="9" HorizontalAlignment="Right" Orientation="Horizontal">

                    <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadMotorLineUVWSequenceParameterCommand}" CommandParameter="{Binding SelectedIndex, ElementName=tabControl}">
                        <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                    </dx:SimpleButton>

                    <!--<dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultMotorFeedbackAutoLearnParameterCommand}" CommandParameter="{Binding Path=SelectedIndex, ElementName=tabControl}">
                    <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>-->

                    <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" Command="{Binding WriteMotorLineUVWSequenceParameterCommand}" CommandParameter="{Binding SelectedIndex, ElementName=tabControl}">
                        <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                    </dx:SimpleButton>

                    <dx:SimpleButton Margin="10,0,0,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Properties_16x16.png}" Command="{Binding MotorLineUVWSequenceIdentificationCommand}" Width="180">
                        <Label Content="电机动力线相序辨识" Style="{StaticResource LabelStyle}" Margin="0"/>
                    </dx:SimpleButton>
                </StackPanel>

                <Label Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="9" Margin="3" Style="{StaticResource LabelStyle}" Content="编码器初始位置偏置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                <Label Grid.Row="5" Grid.Column="1" Margin="10,2" Content="绝对式编码器偏置" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="5" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding AbsEncoderOffset,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="5" Grid.Column="3" Text="1" Style="{StaticResource TextBoxStyle_Unit}"/>


                <Label Grid.Row="16" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,3,3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                <StackPanel Grid.Row="17" Grid.Column="2" Grid.ColumnSpan="5" Orientation="Horizontal" HorizontalAlignment="Left">
                    <!--<Label Style="{StaticResource LabelStyle}" Content="提示：参数正在识别，请耐心等待结果" Margin="10,4" Foreground="Green" HorizontalContentAlignment="Right" Visibility="{Binding HintVisibility, Converter={StaticResource VisibilityConverter}}"/>-->
                    <!--<Label Style="{StaticResource LabelStyle}" Content="提示：替换参数值，将在伺服重启后生效" Margin="10,4" Foreground="Red" HorizontalContentAlignment="Right"/>-->
                    <Label Style="{StaticResource LabelStyle}" Content="{Binding AbsEncoderOffsetInformation,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Margin="10,4" Foreground="Red" HorizontalContentAlignment="Right"/>
                </StackPanel>

                <StackPanel Grid.Row="17" Grid.Column="0" Grid.ColumnSpan="9" HorizontalAlignment="Right" Orientation="Horizontal">

                    <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadMotorAbsEncoderOffsetParameterCommand}" CommandParameter="{Binding SelectedIndex, ElementName=tabControl}">
                        <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                    </dx:SimpleButton>

                    <!--<dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultMotorFeedbackAutoLearnParameterCommand}" CommandParameter="{Binding Path=SelectedIndex, ElementName=tabControl}">
                    <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>-->

                    <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" Command="{Binding WriteMotorAbsEncoderOffsetParameterCommand}" CommandParameter="{Binding SelectedIndex, ElementName=tabControl}">
                        <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                    </dx:SimpleButton>

                    <dx:SimpleButton Margin="10,0,0,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Properties_16x16.png}" Command="{Binding MotorParameterAbsEncoderOffsetIdentificationCommand}" Width="180">
                        <Label Content="编码器初始位置偏置辨识" Style="{StaticResource LabelStyle}" Margin="0"/>
                    </dx:SimpleButton>
                </StackPanel>
            </Grid>
            <!--/TabItem-->

            <!--<TabItem Header="编码器" TabIndex="1">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding EncoderAlreadySetCommand}"/>
                </dxmvvm:Interaction.Behaviors>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="170"/>
                        <ColumnDefinition Width="90"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="5" Margin="3,10" Style="{StaticResource LabelStyle}" Content="编码器参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                    <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="编码器类型" Style="{StaticResource LabelStyle}" />
                    <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding EncoderType}" SelectedItem="{Binding SelectedEncoderType,Mode=TwoWay}"/>

                    <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="单圈值分辨率位数" Style="{StaticResource LabelStyle}" />
                    <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding ABSEncoderSingleTurnBit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="2" Grid.Column="3" Style="{StaticResource TextBoxStyle_Unit}" Text="bit"/>

                    <Label Grid.Row="3" Grid.Column="1" Margin="10,9" Content="多圈值分辨率位数" Style="{StaticResource LabelStyle}"/>
                    <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9"  Text="{Binding ABSEncoderMultiTurnBit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="3" Grid.Column="3" Style="{StaticResource TextBoxStyle_Unit}" Text="bit"/>

                    <Label Grid.Row="4" Grid.Column="1" Margin="10,9" Content="绝对式编码器偏置" Style="{StaticResource LabelStyle}"/>
                    <TextBox Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ABSEncoderOffset,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>

                    <Label Grid.Row="5" Grid.Column="1" Margin="10,9" Content="ABZ编码器脉冲数" Style="{StaticResource LabelStyle}" />
                    <TextBox Grid.Row="5" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding ABZEncoderPulses,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <TextBox Grid.Row="5" Grid.Column="3" Style="{StaticResource TextBoxStyle_Unit}" Text="1P/Rev"/>

                    <Label Grid.Row="6" Grid.Column="1" Margin="10,9" Content="每转线数" Style="{StaticResource LabelStyle}" Visibility="{Binding IsLinearMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>
                    <TextBox Grid.Row="6" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding PitchMotorEncoderLines,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Visibility="{Binding IsLinearMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>
                    <TextBox Grid.Row="6" Grid.Column="3" Style="{StaticResource TextBoxStyle_Unit}" Text="cnt/节距" Visibility="{Binding IsLinearMotorPageEnabled,Converter={StaticResource VisibilityConverter}}" FontStyle="Normal"/>

                    <Label Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="5" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                    <StackPanel Grid.Row="8" Grid.Column="0" Grid.ColumnSpan="5" HorizontalAlignment="Right" Orientation="Horizontal">
                        <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadMotorFeedbackParameterCommand}" CommandParameter="{Binding Path=SelectedIndex, ElementName=tabControl}">
                            <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                        </dx:SimpleButton>

                        <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultMotorFeedbackParameterCommand}" CommandParameter="{Binding Path=SelectedIndex, ElementName=tabControl}">
                            <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                        </dx:SimpleButton>

                        <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Download_16x16.png}" Command="{Binding WriteMotorFeedbackParameterCommand}" CommandParameter="{Binding Path=SelectedIndex, ElementName=tabControl}">
                            <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                        </dx:SimpleButton>

                        -->
            <!--<dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Properties_16x16.png}" Command="{Binding MotorParameterIdentificationCommand}">
                            <Label Content="参数辨识" Style="{StaticResource LabelStyle}" Margin="0"/>
                        </dx:SimpleButton>-->
            <!--
                    </StackPanel>
                </Grid>
            </TabItem>-->
            <!--/TabControl-->

            <Grid Grid.Row="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>

                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="2" Content="2.电机参数" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="4" Content="3.电机反馈" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="6" Content="4.参数学习" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="8" Content="5.参数辨识" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="9" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="10" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="10" Content="6.JOG调试" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="11" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="12" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="12" Content="7.辨识整定" Foreground="Gray" Style="{StaticResource LabelStyle}"/>-->


                <dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

                <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="2" Content="2.电机参数" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>-->

                <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="2" Content="2.电机反馈" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="4" Content="3.参数学习" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="6" Content="4.参数辨识" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="8" Content="5.JOG调试" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="9" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="10" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="10" Content="6.辨识整定" Foreground="Gray" Style="{StaticResource LabelStyle}"/>

                <!--<dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="12" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=SortAsc_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding MotorLibraryNavigationCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Column="13" FontSize="12" Content="参数库" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2" />-->

                <!--<dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="14" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetMotorConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Column="15" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2" />-->

                <!--<dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="16" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SaveMotorConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Column="17" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>-->

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="18" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Backward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding MotorFeedbackAutoLearnNavigationCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="19" FontSize="12" Content="上页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="20" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Forward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding JogDriectionDebug_For_AddInterfaceCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="21" FontSize="12" Content="下页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>
            </Grid>
        </Grid>

    </ScrollViewer>
       
</UserControl>
