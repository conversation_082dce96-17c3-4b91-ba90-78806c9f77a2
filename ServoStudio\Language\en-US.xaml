﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:ServoStudio.Language"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!--向导-->
    <system:String x:Key="Caption_Wizard">Guide</system:String>
    <system:String x:Key="Caption_SetupWizard">SetupWizard</system:String>

    <system:String x:Key="BarButtonItem_CommunicationSet">CommunicationSet</system:String>
    <system:String x:Key="BarButtonItem_MotorFeedback">MotorFeedback</system:String>
    <system:String x:Key="BarButtonItem_MotorFeedbackAutoLearn">MotorFeedbackAutoLearn</system:String>
    <system:String x:Key="BarButtonItem_MotorParameterIdentification_AddInterface">MotorPhaseFinding</system:String>
    <system:String x:Key="BarButtonItem_JogDriectionDebug">JogDriectionDebug</system:String>
    <system:String x:Key="BarButtonItem_InertiaIdentificationParameterSelf_Tunning">ParameterSelfLearning</system:String>

    <!--配置-->
    <system:String x:Key="Caption_Config">Config</system:String>
    <system:String x:Key="Caption_SystemConfig">SystemConfig</system:String>

    <system:String x:Key="BarButtonItem_UnitSet">UnitSet</system:String>
    <system:String x:Key="BarButtonItem_LimitAmplitude">LimitAmplitude</system:String>
    <system:String x:Key="BarButtonItem_NormalSetting">NormalSetting</system:String>
    <system:String x:Key="BarButtonItem_DigitalIO">DigitalIO</system:String>

    <system:String x:Key="Caption_ThreeLoopConfig">ThreeLoopConfig</system:String>

    <system:String x:Key="BarButtonItem_CurrentLoop">CurrentLoop</system:String>
    <system:String x:Key="BarButtonItem_SpeedLoop">SpeedLoop</system:String>
    <system:String x:Key="BarButtonItem_PositionLoop">PositionLoop</system:String>

    <!--调试-->
    <system:String x:Key="Caption_Debug">Debug</system:String>
    <system:String x:Key="Caption_ManualDebug">ManualDebug</system:String>

    <system:String x:Key="BarButtonItem_Oscilloscope">Oscilloscope</system:String>
    <system:String x:Key="BarButtonItem_ThreeLoop">ThreeLoop</system:String>
    <system:String x:Key="BarButtonItem_FunctionGenerator">FunctionGenerator</system:String>
    <system:String x:Key="BarButtonItem_Action">Action</system:String>
    <system:String x:Key="BarButtonItem_ParameterTunning">ParameterTunning</system:String>

    <!--参数-->
    <system:String x:Key="Caption_Parameter">Parameter</system:String>
    <system:String x:Key="Caption_ParameterOnLine">ParameterOnLine</system:String>

    <system:String x:Key="BarButtonItem_ParameterReadWrite">ParameterReadWrite</system:String>
    <system:String x:Key="BarButtonItem_RefreshParameterReadAndWrite">RealtimeRefresh</system:String>

    <system:String x:Key="Caption_ParameterFactory">ParameterFactory</system:String>

    <system:String x:Key="BarButtonItem_FactoryReset">FactoryReset</system:String>

    <system:String x:Key="Caption_ImportExport">ImportExport</system:String>

    <system:String x:Key="BarButtonItem_ImportConfigFile">ParameterImport</system:String>
    <system:String x:Key="BarButtonItem_ExportConfigFile">ParameterExport</system:String>

    <system:String x:Key="Caption_EEPROM">EEPROM</system:String>

    <system:String x:Key="BarButtonItem_SaveRAMtoEEPROM">SaveRAMtoEEPROM</system:String>
    <system:String x:Key="BarButtonItem_ReadEEPROMtoRAM">ReadEEPROMtoRAM</system:String>

    <system:String x:Key="Caption_ParameterMonitoring">ParameterMonitor</system:String>

    <system:String x:Key="BarButtonItem_ParameterMonitor">MonitoringSetting</system:String>
    <system:String x:Key="BarButtonItem_ExportMoniotor">MonitoringSaving</system:String>

    <system:String x:Key="Caption_ParameterLibrary">ParameterLibrary</system:String>

    <system:String x:Key="BarButtonItem_MotorLibrary">MotorLibrary</system:String>

    <!--帮助-->
    <system:String x:Key="Caption_Help">Help</system:String>
    <system:String x:Key="Caption_SoftwareMaintenance">SoftwareMaintenance</system:String>

    <system:String x:Key="BarButtonItem_SystemRestart">&#160;SystemRestart&#160;</system:String>

    <system:String x:Key="Caption_HardwareMaintenance">HardwareMaintenance</system:String>

    <system:String x:Key="BarButtonItem_FirmwareUpdate">&#160;FirmwareUpdate&#160;</system:String>

    <system:String x:Key="Caption_DeviceInformation">DeviceInformation</system:String>

    <system:String x:Key="BarButtonItem_GetEditionInfo">&#160;GetEditionInfo&#160;</system:String>

    <system:String x:Key="Caption_Instructions">Instructions</system:String>

    <system:String x:Key="BarButtonItem_OpenManualBook">&#160;ManualBook&#160;</system:String>
    <system:String x:Key="BarButtonItem_OpenServoMaintenanceManualBook">&#160;ServoMaintenanceManualBook&#160;</system:String>

    <system:String x:Key="Caption_PathSetting">PathSetting</system:String>

    <system:String x:Key="BarButtonItem_ParameterPath">&#160;ParameterPath&#160;</system:String>

    <system:String x:Key="Caption_PasswordSetting">PasswordSetting</system:String>

    <system:String x:Key="BarButtonItem_ModifyPassword">&#160;ModifyPassword&#160;</system:String>

    <system:String x:Key="Caption_LanguageSwitch">LanguageSwitch</system:String>

    <system:String x:Key="BarButtonItem_LanguageSwitch">&#160;LanguageSwitch&#160;</system:String>

    <!--报警-->
    <system:String x:Key="Caption_Alarm">Alarm</system:String>
    <system:String x:Key="Caption_AlarmHistory">AlarmHistory</system:String>

    <system:String x:Key="BarButtonItem_HardwareAlarmHistory">&#160;HardwareAlarmHistory&#160;</system:String>
    <system:String x:Key="BarButtonItem_DeleteElectronicErrorHistory">&#160;DeleteElectronicErrorHistory&#160;</system:String>

    <system:String x:Key="Caption_AlarmMode">AlarmMode</system:String>

    <system:String x:Key="BarButtonItem_SidebarAlarm">SidebarAlarm</system:String>

    <system:String x:Key="Caption_AlarmAnalysis">AlarmAnalysis</system:String>

    <system:String x:Key="BarButtonItem_AlarmReason">&#160;AlarmReason&#160;</system:String>

    <!--出厂-->
    <system:String x:Key="Caption_Factory">Factory</system:String>
    <system:String x:Key="Caption_OnekeyFactory">OnekeyFactory</system:String>

    <system:String x:Key="BarButtonItem_AllFactoryReset">&#160;AllFactoryReset&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllSystemReset">&#160;AllSystemReset&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllConfigStop">&#160;AllConfigStop&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllAxisAction">&#160;AllAxisAction&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllServoEnabled">&#160;AllServoEnabled&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllAxisStop">&#160;AllAxisStop&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllServoDisabled">&#160;AllServoDisabled&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllFaultReset">&#160;AllFaultReset&#160;</system:String>

    <!--管理-->
    <system:String x:Key="Caption_Manage">Manage</system:String>
    <system:String x:Key="Caption_ThemeSetting">ThemeSetting</system:String>

    <system:String x:Key="Caption_SoftwareException">SoftwareException</system:String>

    <system:String x:Key="BarButtonItem_SoftwareErrorLog">&#160;QueryLog&#160;</system:String>
    <system:String x:Key="BarButtonItem_DeleteSoftwareErrorHistory">&#160;DeleteLog&#160;</system:String>
    <system:String x:Key="BarButtonItem_OpenLogFile">&#160;LogLink&#160;</system:String>

    <system:String x:Key="Caption_FactoryMode">FactoryMode</system:String>

    <system:String x:Key="BarButtonItem_SetOneKeyShortcut">&#160;FactorySetting&#160;</system:String>


    <!--报警信息-->
    <system:String x:Key="Caption_AlarmInformation">AlarmInfo</system:String>
    <system:String x:Key="Caption_layoutPanel_ErrorAlarm">AlarmInfo</system:String>

    <system:String x:Key="Header_SlideAlarm0">RealtimeAlarm1</system:String>

    <system:String x:Key="Label_SlideAlarm0_Description">Description</system:String>
    <system:String x:Key="Label_SlideAlarm0_ID">AlarmID</system:String>
    <system:String x:Key="Label_SlideAlarm0_Level">AlarmLevel</system:String>
    <system:String x:Key="Label_SlideAlarm0_Time">AlarmTime</system:String>
    <system:String x:Key="Label_SlideAlarm0_Measures">Measures</system:String>

    <system:String x:Key="Header_SlideAlarm1">RealtimeAlarm2</system:String>

    <system:String x:Key="Label_SlideAlarm1_Description">Description</system:String>
    <system:String x:Key="Label_SlideAlarm1_ID">AlarmID</system:String>
    <system:String x:Key="Label_SlideAlarm1_Level">AlarmLevel</system:String>
    <system:String x:Key="Label_SlideAlarm1_Time">AlarmTime</system:String>
    <system:String x:Key="Label_SlideAlarm1_Measures">Measures</system:String>

    <system:String x:Key="Header_SlideAlarm2">RealtimeAlarm3</system:String>

    <system:String x:Key="Label_SlideAlarm2_Description">Description</system:String>
    <system:String x:Key="Label_SlideAlarm2_ID">AlarmID</system:String>
    <system:String x:Key="Label_SlideAlarm2_Level">AlarmLevel</system:String>
    <system:String x:Key="Label_SlideAlarm2_Time">AlarmTime</system:String>
    <system:String x:Key="Label_SlideAlarm2_Measures">Measures</system:String>

    <system:String x:Key="Header_SlideAlarm3">RealtimeAlarm4</system:String>

    <system:String x:Key="Label_SlideAlarm3_Description">Description</system:String>
    <system:String x:Key="Label_SlideAlarm3_ID">AlarmID</system:String>
    <system:String x:Key="Label_SlideAlarm3_Level">AlarmLevel</system:String>
    <system:String x:Key="Label_SlideAlarm3_Time">AlarmTime</system:String>
    <system:String x:Key="Label_SlideAlarm3_Measures">Measures</system:String>

    <system:String x:Key="Header_SlideAlarm4">RealtimeAlarm5</system:String>

    <system:String x:Key="Label_SlideAlarm4_Description">Description</system:String>
    <system:String x:Key="Label_SlideAlarm4_ID">AlarmID</system:String>
    <system:String x:Key="Label_SlideAlarm4_Level">AlarmLevel</system:String>
    <system:String x:Key="Label_SlideAlarm4_Time">AlarmTime</system:String>
    <system:String x:Key="Label_SlideAlarm4_Measures">Measures</system:String>

    <!--监控数据-->
    <system:String x:Key="Caption_MonitoringData">Monitor</system:String>
    <system:String x:Key="Caption_layoutPanel_ParameterMonitoring">Monitor</system:String>

    <system:String x:Key="Header_FieldName_ParameterName">Name</system:String>
    <system:String x:Key="Header_FieldName_Value">Value</system:String>
    <system:String x:Key="Header_FieldName_Unit">Unit</system:String>

    <!--快捷设置-->
    <system:String x:Key="Header_Setting">Setting</system:String>

    <system:String x:Key="NavBarItem_SwitchAxis">SwitchAxis</system:String>
    <system:String x:Key="NavBarItem_FaultReset">FaultReset</system:String>
    <system:String x:Key="NavBarItem_SystemReset">SystemReset</system:String>

    <!--状态栏-->
    <system:String x:Key="Label_CommunicationStatus">CommState:</system:String>

    <system:String x:Key="Label_StatusWord">ServoStatus:</system:String>

    <system:String x:Key="Label_Information">Message:</system:String>




    
    


    <!--管理员-->
    <system:String x:Key="Label_AdministratorKey">AdminPassword</system:String>




    <!--通信设置-->
    <system:String x:Key="Label_CommunicationSet">CommunicationSet</system:String>

    <system:String x:Key="Label_SerialPortNum">SerialPortNum</system:String>
    <system:String x:Key="Label_BaudRate">BaudRate</system:String>
    <system:String x:Key="Label_DataBit">DataBit</system:String>
    <system:String x:Key="Label_EndBit">EndBit</system:String>
    <system:String x:Key="Label_CheckBit">CheckBit</system:String>


    <!--伺服驱动器选择-->
    <system:String x:Key="Label_ServoSelect">ServoDriverSelection</system:String>

    <system:String x:Key="Label_Servo">Servo</system:String>

    <!--地址设置-->
    <system:String x:Key="Label_AddressSet">AddressSet</system:String>

    <system:String x:Key="Label_StationAddress">StationAddress</system:String>
    <system:String x:Key="Label_AxisAddress">AxisAddress</system:String>
    <system:String x:Key="Label_Add">Add</system:String>
    <system:String x:Key="Label_Scan">Scan</system:String>

    <!--设置执行-->
    <system:String x:Key="Label_Setup">Setup</system:String>

    <system:String x:Key="Label_RefreshSerialPortNum">RefreshPort</system:String>
    <system:String x:Key="Label_GetSerialPortConnection">Connection</system:String>
    <system:String x:Key="Label_CloseSerialPortConnection">Disconnect</system:String>
    <system:String x:Key="Label_EchoTest">EchoTest</system:String>


    <system:String x:Key="Label_CommunicationSet1">1.CommunicationSet</system:String>
    <system:String x:Key="Label_MotorFeedback">2.MotorFeedback</system:String>
    <system:String x:Key="Label_ParameterLearning">3.Identification</system:String>
    <system:String x:Key="Label_ParameterIdentification">4.PhaseFinding</system:String>
    <system:String x:Key="Label_JogDebug">5.JOGDebug</system:String>
    <system:String x:Key="Label_ParameterSelf-learning">6.SelfTuning</system:String>

    <system:String x:Key="Label_PageDown">PgDn</system:String>



    <!--电流环-->
    <system:String x:Key="Label_Legend">Diagram of current loop — Click the legend to set corresponding parameters</system:String>

    <system:String x:Key="Label_FirstOrderLow-passFilter">FirstLow-passFilter</system:String>
    <system:String x:Key="Label_NotchFilter">NotchFilter</system:String>
    <system:String x:Key="Label_TorqueLimit">TorqueLimit</system:String>
    <system:String x:Key="Label_PIController">PIController</system:String>
    <system:String x:Key="Label_VoltageCommand">VoltageCommand</system:String>
    <system:String x:Key="Label_TorqueCommand">TorqueCommand</system:String>
    <system:String x:Key="Label_CurrentFeedback">CurrentFeedback</system:String>
    <system:String x:Key="Label_TorqueFeedforward">TorqueFeedforward</system:String>
    <system:String x:Key="Label_SecondOrderLow-passFilter">SecondLow-passFilter</system:String>

    <system:String x:Key="Header_LowPassFilter">LowPassFilter</system:String>
    <system:String x:Key="Label_FirstTrqcmdFilterTime">FirstTrqcmdFilterTime</system:String>
    <system:String x:Key="Label_SecondTrqcmdFilterFreq">SecondTrqcmdFilterFreq</system:String>
    <system:String x:Key="Label_SecondTrqcmdFilterQ">SecondTrqcmdFilterQ</system:String>
    <system:String x:Key="Label_Prompt_SecondTrqcmdFilterFreq">Prompt: the second torque command filter frequency is 5000, indicating that the second torque is not used</system:String>

    <system:String x:Key="Header_NotchFilter">NotchFilter</system:String>
    <system:String x:Key="Label_NotchFilterFrequency1">NotchFilter&#x0a;Frequency1</system:String>
    <system:String x:Key="Label_NotchFilterQFactor1">NotchFilter&#x0a;QFactor1</system:String>
    <system:String x:Key="Label_NotchFilterDepth1">NotchFilter&#x0a;Depth1</system:String>
    <system:String x:Key="Label_NotchFilterFrequency2">NotchFilter&#x0a;Frequency2</system:String>
    <system:String x:Key="Label_NotchFilterQFactor2">NotchFilter&#x0a;QFactor2</system:String>
    <system:String x:Key="Label_NotchFilterDepth2">NotchFilter&#x0a;Depth2</system:String>
    <system:String x:Key="Label_NotchFilterFrequency3">NotchFilter&#x0a;Frequency3</system:String>
    <system:String x:Key="Label_NotchFilterQFactor3">NotchFilter&#x0a;QFactor3</system:String>
    <system:String x:Key="Label_NotchFilterDepth3">NotchFilter&#x0a;Depth3</system:String>
    <system:String x:Key="Label_NotchFilterFrequency4">NotchFilter&#x0a;Frequency4</system:String>
    <system:String x:Key="Label_NotchFilterQFactor4">NotchFilter&#x0a;QFactor4</system:String>
    <system:String x:Key="Label_NotchFilterDepth4">NotchFilter&#x0a;Depth4</system:String>

    <system:String x:Key="Header_TorqueLimit">TorqueLimit</system:String>
    <system:String x:Key="Label_ForwardInternalTorqueLimit">ForwardInternalTorqueLimit</system:String>
    <system:String x:Key="Label_ReverseInternalTorqueLimit">ReverseInternalTorqueLimit</system:String>
    <system:String x:Key="Label_ForwardExternalTorqueLimit">ForwardExternalTorqueLimit</system:String>
    <system:String x:Key="Label_ReverseExternalTorqueLimit">ReverseExternalTorqueLimit</system:String>

    <system:String x:Key="Label_ReadCurrentLoopParameter">RefreshParameter</system:String>
    <system:String x:Key="Label_GetDefaultCurrentLoopParameter">DefaultParameter</system:String>
    <system:String x:Key="Label_WriteCurrentLoopParameter">WriteParameter</system:String>



    <!--数字IO-->
    <system:String x:Key="Label_DigitalInput">DigitalInput</system:String>

    <system:String x:Key="Label_PortNumber">PortNumber</system:String>
    <system:String x:Key="Label_LogicalSelect">LogicalSelect</system:String>
    <system:String x:Key="Label_FunctionSelect">FunctionSelect</system:String>
    <system:String x:Key="Label_State">State</system:String>

    <system:String x:Key="Label_DI1_State">Normal</system:String>
    <system:String x:Key="Label_DI2_State">Normal</system:String>
    <system:String x:Key="Label_DI3_State">Normal</system:String>
    <system:String x:Key="Label_DI4_State">Normal</system:String>
    <system:String x:Key="Label_DI5_State">Normal</system:String>
    <system:String x:Key="Label_DI6_State">Normal</system:String>

    <system:String x:Key="Label_DigitalOutput">DigitalOutput</system:String>

    <system:String x:Key="Label_DO1_State">Normal</system:String>
    <system:String x:Key="Label_DO2_State">Normal</system:String>
    <system:String x:Key="Label_DO3_State">Normal</system:String>
    <system:String x:Key="Label_DO4_State">Normal</system:String>
    <system:String x:Key="Label_DO5_State">Normal</system:String>
    <system:String x:Key="Label_DO6_State">Normal</system:String>

    <system:String x:Key="Label_ReadDigitalIOParameter">RefreshParameter</system:String>
    <system:String x:Key="Label_GetDefaultDigitalIOParameter">DefaultParameter</system:String>
    <system:String x:Key="Label_WriteDigitalIOParameter">WriteParameter</system:String>

    <system:String x:Key="Label_UnitSet">1.UnitSet</system:String>
    <system:String x:Key="Label_LimitProtection">2.LimitProtection</system:String>
    <system:String x:Key="Label_NormalSet">3.NormalSet</system:String>
    <system:String x:Key="Label_DigitalIO">4.DigitalIO</system:String>

    <system:String x:Key="Label_Load">Load</system:String>
    <system:String x:Key="Label_Save">Save</system:String>
    <system:String x:Key="Label_PageUp">PgUp</system:String>





    <!--<system:String x:Key="NavBarItem_SlideAlarmDisplayControl">Alarm</system:String>-->
















    <!--<system:String x:Key="MotorFeedbackAutoLearn">MotorFeedbackAutoLearn</system:String>-->
</ResourceDictionary>