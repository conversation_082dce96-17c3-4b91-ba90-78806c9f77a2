﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.IO.Ports;
using System.Collections.Generic;
using ServoStudio.Models;
using System.Windows;
using System.Text;
using System.Threading;
using System.IO;
using System.ComponentModel;
using System.Linq;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class CommunicationSetViewModel
    {
        #region 事件
        public event EvaluationSerialPortConnectState evtEvaluationSerialPortConnectState;//状态栏串口连接状态
        public event EvaluationSerialPortWorkProcess evtEvaluationSerialPortWorkProcess;//状态栏串口任务
        public event EvaluationParamterReadAndWrite evtEvaluationParamterReadAndWrite;//参数读写赋值
        public event EvaluationParamterReadAndWrite evtEvaluationDiffParamterReadAndWrite;//差异参数读写赋值 由Lilbert于2023.05.17添加
        public event GetMotorParameterIdentification evtGetMotorParameterIdentification;//获取电机参数识别数据
        public event EvaluationAxisAddress evtEvaluationAxisAddress;//轴地址赋值
        public event ControlEnabled evtOscilliscopeButtonEnabled;//示波器按钮使能
        public event ControlEnabled evtFaultDataOscilliscopeButtonEnabled;//故障数据示波器按钮使能
        public event ShowNotification evtShowNotification;//信息推送
        public event CheckIdentificationComplete evtCheckIdentificationComplete;//电机参数识别完成
        public event Action evtBatchReadCompletedForImport; //导入前批量读取完成事件
        #endregion

        #region 字段
        private bool IsFileExist;
        public static bool IsInitialized = true;
        private static readonly object syncObj = new object();
        public virtual bool IsSelectedServoNameEnabled { get; set; }//Jog按钮属性
        public virtual int IsAxisAddressResetEnabled { get; set; }//轴地址重置按钮是否可以使用
        #endregion

        public List<string> ConfigServoList = new List<string>();//配置伺服集合

        #region 服务       
        protected virtual INotificationService DefaultNotificationService { get { return this.GetService<INotificationService>(); } }
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }

        [ServiceProperty(Key = "AddSlaveAxisIDLibraryDetails")]
        protected virtual IDialogService DialogService_AddSlaveAxisIDLibraryDetails { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "AddSlaveAxisID")]
        protected virtual IDialogService DialogService_AddSlaveAxisID { get { return this.GetService<IDialogService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }

        #endregion

        #region 属性
        public virtual ObservableCollection<string> SerialPortNum { get; set; }//串口号
        public virtual string SelectedSerialPortNum { get; set; }//选中串口号

        public virtual ObservableCollection<string> BaudRate { get; set; }//波特率
        public virtual string SelectedBaudRate { get; set; }//选中波特率

        public virtual ObservableCollection<string> DataBit { get; set; }//数据位
        public virtual string SelectedDataBit { get; set; }//选中数据位

        public virtual ObservableCollection<string> CheckBit { get; set; }//校验位
        public virtual string SelectedCheckBit { get; set; }//选中校验位

        public virtual ObservableCollection<string> EndBit { get; set; }//停止位
        public virtual string SelectedEndBit { get; set; }//选中停止位

        public virtual ObservableCollection<string> SlaveID { get; set; }//从站ID
        public virtual string SelectedSlaveID { get; set; }//选中的从站ID

        public virtual ObservableCollection<string> AxisID { get; set; }//转轴ID
        public virtual string SelectedAxisID { get; set; }//选中的转轴ID

        public virtual ObservableCollection<string> ServoName { get; set; }//伺服驱动器名称
        public virtual string SelectedServoName { get; set; }//选中的伺服驱动器名称
        #endregion

        #region 构造函数
        public CommunicationSetViewModel()
        {
            ServoXmlHelper.XmlServoConfigs();

            ViewModelSet.CommunicationSet = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.COMMUNICATIONSET;
        }
        #endregion

        #region 公有方法-串口设置

        //*************************************************************************
        //函数名称：RefreshSerialPortNum
        //函数功能：刷新串口信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.02.10
        //*************************************************************************
        public void RefreshSerialPortNumFromGlobalVariable()
        {
            try
            {
                SerialPortNum = new ObservableCollection<string>();
                foreach (string sp in System.IO.Ports.SerialPort.GetPortNames())
                {
                    SerialPortNum.Add(sp);
                }

                if (SerialPortNum != null)
                {
                    if (SerialPortNum.Count != 0)
                    {
                        //SelectedSerialPortNum = SerialPortNum[0];
                        SelectedSerialPortNum = GlobalCurrentInput.SelectedSerialPortNum;//由Lilbert于2023.02.10添加切换到通讯配置时，保留之前连接的串口
                    }
                    else
                    {
                        SelectedSerialPortNum = null;
                    }
                }
                else
                {
                    SelectedSerialPortNum = null;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_REFRESH_SERIAL_PORT_NUM, "RefreshSerialPortNum", ex);
            }
        }

        //*************************************************************************
        //函数名称：RefreshSerialPortNum
        //函数功能：刷新串口信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.5
        //*************************************************************************
        public void RefreshSerialPortNum()
        {
            try
            {
                SerialPortNum = new ObservableCollection<string>();
                foreach (string sp in System.IO.Ports.SerialPort.GetPortNames())
                {
                    SerialPortNum.Add(sp);
                }

                if (SerialPortNum != null)
                {
                    if (SerialPortNum.Count != 0)
                    {
                        SelectedSerialPortNum = SerialPortNum[0];
                        //SelectedSerialPortNum = GlobalCurrentInput.SelectedSerialPortNum;
                    }
                    else
                    {
                        SelectedSerialPortNum = null;
                    }
                }
                else
                {
                    SelectedSerialPortNum = null;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_REFRESH_SERIAL_PORT_NUM, "RefreshSerialPortNum", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetSerialPortConnection()
        //函数功能：串口连接
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.5
        //*************************************************************************
        public void GetSerialPortConnection()
        {
            int iRet = -1;

            //串口参数设置
            iRet = SerialPortParameterSet();
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(RET.ERROR);
                return;
            }

            //建立连接，接收事件注册
            iRet = OpenSerialPortConnection();
            if (iRet == RET.SUCCEEDED)
            {
                SoftwareStateParameterSet.OpenConnectionFlag = true;
                //回送测试  
                EchoTest(IsFirstTest: "true");
            
                //获取默认参数单位
                OthersHelper.GetDefaultUnit();
                OthersHelper.GetSelectDefaultUnit();

                //获取默认示波器单位
                OthersHelper.GetOscilloscopeParameterUnitSet();            
            }
            else if (iRet == RET.ERROR)
            {
                ShowNotification(1002);
            }            
        }

        //*************************************************************************
        //函数名称：CanGetSerialPortConnection()
        //函数功能：是否可以串口连接
        //
        //输入参数：None
        //         
        //输出参数：true ：OK
        //         false: NG
        //        
        //编码作者：Ryan
        //更新时间：2019.11.5
        //*************************************************************************
        public bool CanGetSerialPortConnection()
        {
            if (string.IsNullOrEmpty(SelectedSerialPortNum))
            {
                return false;
            }      
            else
            {
                if (!SoftwareStateParameterSet.IsConnected)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }               
        }

        //*************************************************************************
        //函数名称：CloseSerialPortConnection
        //函数功能：关闭串口连接
        //
        //输入参数：None
        //         
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.11.5
        //*************************************************************************
        public void CloseSerialPortConnection()
        {
            try
            {
                if (CommunicationSet.SerialPortInfo != null)
                {
                    if (CommunicationSet.SerialPortInfo.IsOpen)
                    {
                        CommunicationSet.SerialPortInfo.Close();                          
                    }
                }

                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_NotConnect);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_NotConnect);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_NotConnect1);
                SoftwareStateParameterSet.IsConnected = false;
                SoftwareStateParameterSet.CloseConnectionFlag = true;

                //获取默认参数单位
                OthersHelper.GetDefaultUnit();
                OthersHelper.GetSelectDefaultUnit();

                //获取默认示波器单位
                OthersHelper.GetOscilloscopeParameterUnitSet();
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_CLOSE_SERIAL_PORT_CONNECTION, "CloseSerialPortConnection", ex);
            }
        }        

        //*************************************************************************
        //函数名称：RefreshAddScanSlaveAxisIDFlag
        //函数功能：刷新添加扫描从站标志
        //
        //输入参数：None
        //         
        //输出参数：None
        //         
        //        
        //编码作者：Lilbert
        //更新时间：2023.01.05
        //*************************************************************************
        public void RefreshAddScanSlaveAxisIDFlag()
        {

            if (ConfigServo.SelectConfigAxisID == 1)
            {
                IsSelectedServoNameEnabled = true;
            }
            else
            {
                IsSelectedServoNameEnabled = false;
            }            
        }

        //*************************************************************************
        //函数名称：EchoTest
        //函数功能：回传测试
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.11.04
        //*************************************************************************
        public void EchoTest(string IsFirstTest)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> TransmittingDataInfo = new List<TransmitingDataInfoSet>();
            TransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = "9999" });

            if (IsFirstTest == "true")
            {
                SoftwareStateParameterSet.IsFirstEchoTest = true;
            }
            else
            {
                SoftwareStateParameterSet.IsFirstEchoTest = false;
            }

            //判断串口状态
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(1000);
                return;
            }

            //添加串口任务
            //iRet = HexHelper.AddSerialPortTask(PageName.COMMUNICATIONSET, SoftwareStateParameterSet.StationID, SoftwareStateParameterSet.AxisID, TaskName.Test, FunctionCode.TEST_COMMUNICATION, null, TransmittingDataInfo);
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                iRet = HexHelper.AddSerialPortTask(PageName.COMMUNICATIONSET, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.Test, FunctionCode.TEST_COMMUNICATION, null, TransmittingDataInfo);
            }
            else
            {
                iRet = HexHelper.AddSerialPortTask(PageName.COMMUNICATIONSET, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.Test1, FunctionCode.TEST_COMMUNICATION, null, TransmittingDataInfo);
            }
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(RET.ERROR);
                return;
            }

            //开启任务管理与发送线程
            if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
            {
                ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
            }
        }
        #endregion

        #region 公有方法-串口接收与发送
        //*************************************************************************
        //函数名称：SerialPortInfo_DataReceived
        //函数功能：串口接收事件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.01.10
        //*************************************************************************
        public void SerialPortInfo_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            int iRet = -1;
            int iRet_Message = -1;
            int iRet_UpdateMessage = -1;
            string strInfo = null;
            byte[] bData = null;

            try
            {
                #region 正常交互
                if (!FirmwareUpdateSet.IsUpdate)
                {
                    //接收数据
                    iRet_Message = HexHelper.ReceivingData();
                    if (iRet_Message != RET.SUCCEEDED)
                    {
                        return;
                    }

                    //查找完整报文
                    iRet_Message = HexHelper.FindCompleteMessage(ref CommunicationSet.Receiving);
                    if (iRet_Message != RET.SUCCEEDED)
                    {
                        return;
                    }

                    //发送暂停
                    PthreadStatement.SerialPortTransmiting.PthreadPause = true;

                    //更新任务为执行完成
                    iRet = HexHelper.ChangeSerialPortTaskToExecuted();
                    if (iRet != RET.SUCCEEDED)
                    {
                        return;
                    }

                    //数据分析
                    iRet = AnalyseReceivingMessage();
                    if (iRet != RET.SUCCEEDED)
                    {
                        return;
                    }                    
                }
                #endregion

                #region 固件升级   
                if (FirmwareUpdateSet.IsUpdate)
                {                   
                    //停止时钟和所有线程工作
                    ViewModelSet.Main.Timer_System.IsEnabled = false;
                    OthersHelper.CloseAllThread();
                    
                    lock (syncObj)
                    {
                        //接收数据
                        iRet = HexHelper.ReceivingData(FirmwareUpdateSet.Process, ref bData, ref strInfo);
                        if (iRet == RET.SUCCEEDED)
                        {
                            //数据分析
                            iRet_UpdateMessage = AnalyseReceivingMessage_ForFirmwareUpdate(bData, strInfo);
                        }
                        else
                        {
                            if (FirmwareUpdateSet.Process == FirmwareUpdateProcess.DATA)
                            {
                                //数据分析
                                iRet_UpdateMessage = AnalyseReceivingMessage_ForFirmwareUpdate(bData, "Invalid");
                            }
                            else
                            {
                                //跳转到Finally,并停止固件升级
                                iRet_UpdateMessage = RET.ERROR;
                            }
                        }                            
                    }                                                                        
                }
                #endregion
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("SerialPortInfo_DataReceived", ex);
            }
            finally
            {
                #region 正常交互               
                if (!FirmwareUpdateSet.IsUpdate)//报文拼接成功
                {
                    if (iRet_Message == RET.SUCCEEDED)
                    {
                        //开启时钟和所有线程工作
                        PthreadStatement.SerialPortTransmiting.PthreadPause = false;
                    }
                }
                #endregion

                #region 固件升级
                if (FirmwareUpdateSet.IsUpdate)
                {
                    if (iRet_UpdateMessage == RET.ERROR)//固件升级失败
                    {
                        //开启时钟和所有线程工作
                        PthreadStatement.SerialPortTransmiting.PthreadPause = false;
                        ViewModelSet.Main.Timer_System.IsEnabled = true;

                        //发送‘a’，停止升级
                        ViewModelSet.FirmwareUpdate?.FirmwareUpdateAbort();

                        //信息提示
                        ShowNotification("FAILED-固件升级失败");
                        WindowSet.clsMainWindow?.FirmUpdateAgain();

                        //进度条
                        ProgressValue(0);
                    }
                    else if (iRet_UpdateMessage == RET.SUCCEEDED)//固件升级成功
                    {
                        //开启时钟和所有线程工作
                        PthreadStatement.SerialPortTransmiting.PthreadPause = false;
                        ViewModelSet.Main.Timer_System.IsEnabled = true;
                      
                        //进度条
                        ProgressValue(0);

                        //清空固件升级集合
                        OthersHelper.ClearFirmwareUpdateSet();

                        //所有的界面都能再切换
                        ViewModelSet.Main.IsAllPageEnabled = true;

                        //信息提示
                        ShowNotification("SUCCEED-固件升级成功，正在重启硬件，请等待芯片指示灯重新亮起后再次连接软件");
                        ShowNotification_CrossThread(3007);
                           
                        //CheckBox使能
                        ViewModelSet.FirmwareUpdate.CheckBoxEnabled = true;                        
                    }
                }
                #endregion
            }
        }     

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_ParameterRead_For_Softwareversion
        //函数功能：串口读数据任务下达——为导出参数时导出软件版本号
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskState                                  任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2021.07.16
        //*************************************************************************
        public void SerialPort_DataTransmiting_ParameterRead_For_Softwareversion(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.PARAMETER_READ, null, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_READ, "SerialPort_DataTransmiting_ParameterRead_For_Softwareversion", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterRead
        //函数功能：串口读数据任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskState                                  任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.16
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterRead(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.PARAMETER_READ, null, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_READ, "SerialPort_DataTransmiting_For_ParameterRead", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterWrite_For_Timestamp
        //函数功能：串口写数据任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskState                                  任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //由Lilbert添加，为3个芯片写时间戳
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2021.11.15
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterWrite_For_Timestamp(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //添加串口任务
                //iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.StationID, SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.PARAMETER_WRITE, null, TransmittingDataInfo);
                iRet = HexHelper.AddSerialPortTask(strCurrentPage, "FF", SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.PARAMETER_WRITE, null, TransmittingDataInfo);        //由Lilbert添加，为3个芯片写时间戳
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE_FOR_TIMESTAMP, "SerialPort_DataTransmiting_For_ParameterWrite_For_Timestamp", ex);
            }
        }


        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterWrite_For_Timestamp
        //函数功能：串口写数据任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskState                                  任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //由Lilbert添加，为3个芯片写时间戳
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2021.11.15
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterWrite_For_Spark(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //添加串口任务
                //iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.StationID, SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.PARAMETER_WRITE, null, TransmittingDataInfo);
                iRet = HexHelper.AddSerialPortTask(strCurrentPage, "FF", SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.PARAMETER_WRITE, null, TransmittingDataInfo);        //由Lilbert添加，为3个芯片写时间戳
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE_FOR_TIMESTAMP, "SerialPort_DataTransmiting_For_ParameterWrite_For_Spark", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterWrite
        //函数功能：串口写数据任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskState                                  任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.16
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterWrite(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.PARAMETER_WRITE, null, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                
                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE, "SerialPort_DataTransmiting_For_ParameterWrite", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterWrite_ForAxisAddressReset
        //函数功能：串口写数据任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskState                                  任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2024.05.27
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterWrite_ForAxisAddressReset(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(strCurrentPage, "FE", "", strTaskName, FunctionCode.PARAMETER_WRITE, null, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE, "SerialPort_DataTransmiting_For_ParameterWrite_ForAxisAddressReset", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterWrite_For_FactoryReset
        //函数功能：串口写数据任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskState                                  任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.04.12
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterWrite_For_FactoryReset(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //添加串口任务
                //iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.StationID, SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.PARAMETER_WRITE, null, TransmittingDataInfo);
                //if (iRet != RET.SUCCEEDED)
                //{
                //    ShowNotification(RET.ERROR);
                //    return;
                //}
                for (int i = 1; i <= 3; i++)
                {
                    //添加串口任务
                    iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.PARAMETER_WRITE, null, TransmittingDataInfo);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }
                    Thread.Sleep(2000);
                }


                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE, "SerialPort_DataTransmiting_For_ParameterWrite", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_AxisStartWrite
        //函数功能：串口写数据任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskState                                  任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.16
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_AllAxis(string strTaskName, int iIndex)
        {
            int iRet = -1;
            double ABSEncoderSingleTurnBit = 0;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (OthersHelper.IsInputInteger(SoftwareStateParameterSet.ABSEncoderSingleTurnBit))
                {
                    ABSEncoderSingleTurnBit = Math.Pow(2, Convert.ToInt32(SoftwareStateParameterSet.ABSEncoderSingleTurnBit));
                }

                if (strTaskName == TaskName.AllAxisFactoryReset)
                {
                    dicParameterInfo.Add("System Prm Init", "1");
                }
                else if (strTaskName == TaskName.AllAxisSystemReset)
                {
                    dicParameterInfo.Add("System Reset", "1");
                }
                else if (strTaskName == TaskName.AllAxisDisabled)
                {
                    dicParameterInfo.Add("Control Word", "6");
                }
                else if (strTaskName == TaskName.AllAxisConfigStop)
                {
                    dicParameterInfo.Add("DI1 Function Select", "6");
                    dicParameterInfo.Add("DI1 Logic Select", "1");
                    dicParameterInfo.Add("Ac Off Discharge Switch", "1");
                }
                else if (strTaskName == TaskName.AllAxisRunning)
                {
                    dicParameterInfo.Add("Target Velocity", (5*ABSEncoderSingleTurnBit).ToString());
                    dicParameterInfo.Add("Profile Acceleration", (500 * ABSEncoderSingleTurnBit).ToString());
                    dicParameterInfo.Add("Profile Deceleration", (500 * ABSEncoderSingleTurnBit).ToString());
                    dicParameterInfo.Add("Modes Of Operation", "3");
                    dicParameterInfo.Add("Motion Profile Type", "0");
                }
                else if (strTaskName == TaskName.AllAxisStop)
                {
                    dicParameterInfo.Add("Target Velocity", "0");
                    dicParameterInfo.Add("Profile Acceleration", (500 * ABSEncoderSingleTurnBit).ToString());
                    dicParameterInfo.Add("Profile Deceleration", (500 * ABSEncoderSingleTurnBit).ToString());
                    dicParameterInfo.Add("Modes Of Operation", "3");
                    dicParameterInfo.Add("Motion Profile Type", "0");
                }

                OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);                                  
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo);
                   
                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(PageName.MAIN, SoftwareStateParameterSet.lstAxisInfo[SoftwareStateParameterSet.AxisIndex].SlaveID, SoftwareStateParameterSet.lstAxisInfo[SoftwareStateParameterSet.AxisIndex].AxisID, strTaskName, FunctionCode.PARAMETER_WRITE, null, lstTransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_ALL_AXIS, "SerialPort_DataTransmiting_For_AllAxis", ex);
            }
        }      

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_AxisStatus
        //函数功能：串口读数据任务下达
        //
        //输入参数：int AxisIndex    轴信息                            
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.09.14&2023.02.07
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_AxisStatus(string AxisIndex)
        {
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取写入信息
                //if (GlobalCurrentInput.SelectedServoName == "6合一伺服" || GlobalCurrentInput.SelectedServoName == "4合一伺服" || GlobalCurrentInput.SelectedServoName == "2合一伺服" || GlobalCurrentInput.SelectedServoName == "MD4伺服") //由Lilbert于2023.02.07添加多轴和单轴的选择
                if (ConfigServo.SelectConfigSlaveID != 1) //由Lilbert于2023.02.07添加多轴和单轴的选择
                {
                    if (AxisIndex == "A")//A轴
                    {
                        TransmitingDataInfoSet clsData = new TransmitingDataInfoSet();
                        clsData.Address = "604100";
                        clsData.Content = "60410010";
                        clsData.DataType = "UINT16";
                        lstTransmittingDataInfo.Add(clsData);
                        HexHelper.AddSerialPortTask(PageName.FIRMWAREUPDATE, SoftwareStateParameterSet.lstAxisInfo[0].SlaveID, SoftwareStateParameterSet.lstAxisInfo[0].AxisID, TaskName.AxisStatusA, FunctionCode.PARAMETER_READ, null, lstTransmittingDataInfo);
                    }
                    else//B轴
                    {
                        TransmitingDataInfoSet clsData = new TransmitingDataInfoSet();
                        clsData.Address = "684100";
                        clsData.Content = "68410010";
                        clsData.DataType = "UINT16";
                        lstTransmittingDataInfo.Add(clsData);
                        HexHelper.AddSerialPortTask(PageName.FIRMWAREUPDATE, SoftwareStateParameterSet.lstAxisInfo[1].SlaveID, SoftwareStateParameterSet.lstAxisInfo[1].AxisID, TaskName.AxisStatusB, FunctionCode.PARAMETER_READ, null, lstTransmittingDataInfo);
                    }
                }
                else
                {
                    TransmitingDataInfoSet clsData = new TransmitingDataInfoSet();
                    clsData.Address = "604100";
                    clsData.Content = "60410010";
                    clsData.DataType = "UINT16";
                    lstTransmittingDataInfo.Add(clsData);
                    HexHelper.AddSerialPortTask(PageName.FIRMWAREUPDATE, SoftwareStateParameterSet.lstAxisInfo[0].SlaveID, SoftwareStateParameterSet.lstAxisInfo[0].AxisID, TaskName.AxisStatusA, FunctionCode.PARAMETER_READ, null, lstTransmittingDataInfo);
                }
                //if (AxisIndex == "A")//A轴
                //{
                //    TransmitingDataInfoSet clsData = new TransmitingDataInfoSet();
                //    clsData.Address = "604100";
                //    clsData.Content = "60410010";
                //    clsData.DataType = "UINT16";
                //    lstTransmittingDataInfo.Add(clsData);
                //    HexHelper.AddSerialPortTask(PageName.FIRMWAREUPDATE, SoftwareStateParameterSet.lstAxisInfo[0].SlaveID, SoftwareStateParameterSet.lstAxisInfo[0].AxisID, TaskName.AxisStatusA, FunctionCode.PARAMETER_READ, null, lstTransmittingDataInfo);
                //}
                //else//B轴
                //{
                //    TransmitingDataInfoSet clsData = new TransmitingDataInfoSet();
                //    clsData.Address = "684100";
                //    clsData.Content = "68410010";
                //    clsData.DataType = "UINT16";
                //    lstTransmittingDataInfo.Add(clsData);
                //    HexHelper.AddSerialPortTask(PageName.FIRMWAREUPDATE, SoftwareStateParameterSet.lstAxisInfo[1].SlaveID, SoftwareStateParameterSet.lstAxisInfo[1].AxisID, TaskName.AxisStatusB, FunctionCode.PARAMETER_READ, null, lstTransmittingDataInfo);
                //}

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_AXIS_STATUS, "SerialPort_DataTransmiting_For_AxisStatus", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterBatchWrite
        //函数功能：串口批处理写数据
        //
        //输入参数：int iIndex    索引号
        //         List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo    批处理数据信息集合
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.16
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterBatchWrite(int iIndex, List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //发送数据集合数量判断
                if (lstTransmittingDataInfo == null)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (lstTransmittingDataInfo.Count == 0 || lstTransmittingDataInfo.Count - 1 < iIndex)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(PageName.MAIN, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.BatchWrite, FunctionCode.PARAMETER_WRITE, null, lstTransmittingDataInfo[iIndex]);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE, "SerialPort_DataTransmiting_For_ParameterWrite", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterBatchWrite_ForCompare
        //函数功能：串口批处理写数据
        //
        //输入参数：int iIndex    索引号
        //         List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo    批处理数据信息集合
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterBatchWrite_ForCompare(int iIndex, List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo, string strTaskName = TaskName.BatchWrite_ForImportConfig)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //发送数据集合数量判断
                if (lstTransmittingDataInfo == null)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (lstTransmittingDataInfo.Count == 0 || lstTransmittingDataInfo.Count - 1 < iIndex)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(PageName.MAIN, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.PARAMETER_WRITE, null, lstTransmittingDataInfo[iIndex]);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE, "SerialPort_DataTransmiting_For_ParameterWrite", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_ParameterBatchRead_For_Softwareversion
        //函数功能：串口批处理读数据——为参数导出软件版本号
        //
        //输入参数：int iIndex    索引号
        //         List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo    批处理数据信息集合
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2021.07.16
        //*************************************************************************
        public void SerialPort_DataTransmiting_ParameterBatchRead_For_Softwareversion(int iIndex, List<List<TransmitingDataInfoSet>> lstReceivingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //发送数据集合数量判断
                if (lstReceivingDataInfo == null)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (lstReceivingDataInfo.Count == 0 || lstReceivingDataInfo.Count - 1 < iIndex)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(PageName.MAIN, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.BatchRead, FunctionCode.PARAMETER_READ, null, lstReceivingDataInfo[iIndex]);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE, "SerialPort_DataTransmiting_ParameterBatchRead_For_Softwareversion", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterBatchRead
        //函数功能：串口批处理读数据
        //
        //输入参数：int iIndex    索引号
        //         List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo    批处理数据信息集合
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.16
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterBatchRead(int iIndex, List<List<TransmitingDataInfoSet>> lstReceivingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //发送数据集合数量判断
                if (lstReceivingDataInfo == null)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (lstReceivingDataInfo.Count == 0 || lstReceivingDataInfo.Count - 1 < iIndex)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(PageName.MAIN, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.BatchRead, FunctionCode.PARAMETER_READ, null, lstReceivingDataInfo[iIndex]);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE, "SerialPort_DataTransmiting_For_ParameterWrite", ex);
            }
        }

        public void SerialPort_DataTransmiting_For_ParameterBatchRead_ForImport(int iIndex, List<List<TransmitingDataInfoSet>> lstReceivingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //发送数据集合数量判断
                if (lstReceivingDataInfo == null)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (lstReceivingDataInfo.Count == 0 || lstReceivingDataInfo.Count - 1 < iIndex)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(PageName.MAIN, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.BatchReadForImport, FunctionCode.PARAMETER_READ, null, lstReceivingDataInfo[iIndex]);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE, "SerialPort_DataTransmiting_For_ParameterBatchRead_ForImport", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterBatchWrite
        //函数功能：串口批处理写数据
        //
        //输入参数：int iIndex    索引号
        //         List<List<TransmitingDataInfoSet>> lstTransmittingDataInfo    批处理数据信息集合
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterBatchRead_ForImportConfig(int iIndex, List<List<TransmitingDataInfoSet>> lstReceivingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //发送数据集合数量判断
                if (lstReceivingDataInfo == null)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (lstReceivingDataInfo.Count == 0 || lstReceivingDataInfo.Count - 1 < iIndex)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask_ForImportConfig(PageName.MAIN, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.BatchRead_ForImportConfig, FunctionCode.PARAMETER_READ, null, lstReceivingDataInfo[iIndex]);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE, "SerialPort_DataTransmiting_For_ParameterWrite", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterAcquisition
        //函数功能：串口数据采集任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskName                                   任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.16
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterAcquisition(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //更新采样任务状态
                OthersHelper.RefreshAcquisitionInfoSet();

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.ACQUISITION, AcquisitionExecutedCode.ACQUISITION, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //信息提示-若是连续只在第一次提示-先不提示
                if (AcquisitionInfoSet.ContinuousAcquisitionTimes == 0) { }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_ACQUISITION, "SerialPort_DataTransmiting_For_ParameterAcquisition", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_StopAcquisition
        //函数功能：串口数据停止采集任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskName                                   任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.01.14
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_StopAcquisition(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            //一共有三种情况会进入到这个函数
            //1.通信重新建立
            //2.固件升级
            //3.数据采集停止

            int iRet = -1;

            try
            {
                //判断串口状态 不要加报警提示因为跨线程，通信重新建立会return
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //判断当前任务，固件升级会return
                if (CommunicationSet.TaskName == TaskName.FirmwareUpdate)
                {
                    return;
                }

                //添加串口任务，数据采集任务终止会向下进行
                iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FunctionCode.ACQUISITION, AcquisitionExecutedCode.STOP_ACQUISITION, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_STOP_ACQUISITION, "SerialPort_DataTransmiting_For_StopAcquisition", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_StopAcquisitionFault
        //函数功能：串口故障数据停止故障采集任务下达-故障数据示波器停止采集
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskName                                   任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.04
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_StopAcquisitionFault(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            //一共有三种情况会进入到这个函数
            //1.通信重新建立
            //2.固件升级
            //3.数据采集停止

            int iRet = -1;

            try
            {
                //判断串口状态 不要加报警提示因为跨线程，通信重新建立会return
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //判断当前任务，固件升级会return
                if (CommunicationSet.TaskName == TaskName.FirmwareUpdate)
                {
                    return;
                }

                //添加串口任务，数据采集任务终止会向下进行
                if (SoftwareStateParameterSet.CurrentPageName == PageName.FAULTDATAOSCILLOSCOPE && strTaskName == TaskName.StopAcquisitionFault)
                {
                    iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.STOP_ACQUISITION, TransmittingDataInfo);
                }
                else
                {
                    //iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.CLEAR_ACQUISITION, TransmittingDataInfo);
                    SoftwareStateParameterSet.IsClearAcquisitionFault = false;
                }
                //iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.STOP_ACQUISITION, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_STOP_ACQUISITION, "SerialPort_DataTransmiting_For_StopAcquisition", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ClearAcquisitionFault
        //函数功能：串口故障数据停止故障采集任务下达-故障数据示波器停止采集
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskName                                   任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.04
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ClearAcquisitionFault(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            //一共有三种情况会进入到这个函数
            //1.通信重新建立
            //2.固件升级
            //3.数据采集停止

            int iRet = -1;

            try
            {
                //判断串口状态 不要加报警提示因为跨线程，通信重新建立会return
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //判断当前任务，固件升级会return
                if (CommunicationSet.TaskName == TaskName.FirmwareUpdate)
                {
                    return;
                }

                //添加串口任务，数据采集任务终止会向下进行
                //if (SoftwareStateParameterSet.CurrentPageName == PageName.FAULTDATAOSCILLOSCOPE && strTaskName == TaskName.StopAcquisitionFault)
                //{
                //    iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.STOP_ACQUISITION, TransmittingDataInfo);
                //}
                //else
                //{
                //    iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.CLEAR_ACQUISITION, TransmittingDataInfo);
                //    SoftwareStateParameterSet.IsClearAcquisitionFault = false;
                //}
                iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.CLEAR_ACQUISITION, TransmittingDataInfo);
                //iRet = HexHelper.AddSerialPortTask(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.STOP_ACQUISITION, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_STOP_ACQUISITION, "SerialPort_DataTransmiting_For_StopAcquisition", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_ParameterFaultAcquisition
        //函数功能：串口数据采集任务下达-故障数据
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskName                                   任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.03.29
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_ParameterFaultAcquisition(string strCurrentPage, string strTaskName, List<TransmitingDataInfoSet> TransmittingDataInfo)
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //更新采样任务状态
                OthersHelper.RefreshFaultAcquisitionInfoSet();

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask_For_Fault(strCurrentPage, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, strTaskName, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.ACQUISITION, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //信息提示-若是连续只在第一次提示-先不提示
                if (FaultAcquisitionInfoSet.ContinuousAcquisitionTimes == 0) { }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_ACQUISITION, "SerialPort_DataTransmiting_For_ParameterAcquisition", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_Update
        //函数功能：串口固件升级任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskState                                  任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：lilbert
        //更新时间：2023.02.07
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_Update(int selectedConfigSlaveID, int selectedConfigAxisID, string slaveID)
        {
            int iRet = -1;
            string strStationID = null;
            List<TransmitingDataInfoSet> TransmittingDataInfo = new List<TransmitingDataInfoSet>();
            TransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = "01" });

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //多合一伺服
                #region 2合一伺服
                if (selectedConfigSlaveID == 2 && selectedConfigAxisID == 2)
                {
                    strStationID = "00";
                }
                #endregion
                #region 4合一伺服
                if (selectedConfigSlaveID == 2 && selectedConfigAxisID == 4)
                {
                    if (slaveID == "SLAVE-1")
                    {
                        strStationID = "00";
                    }
                    else
                    {
                        strStationID = "01";
                    }
                }
                #endregion
                #region 6合一伺服
                if (selectedConfigSlaveID == 3 && selectedConfigAxisID == 6)
                {
                    if (slaveID == "SLAVE-1")
                    {
                        strStationID = "00";
                    }
                    else if (slaveID == "SLAVE-2")
                    {
                        strStationID = "01";
                    }
                    else
                    {
                        strStationID = "10";
                    }
                }
                #endregion

                //单轴伺服
                #region 单轴伺服
                if ((selectedConfigSlaveID == 1 && selectedConfigAxisID == 1) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 2) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 3) ||
                    (selectedConfigSlaveID == 1 && selectedConfigAxisID == 4) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 5) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 6) ||
                    (selectedConfigSlaveID == 1 && selectedConfigAxisID == 7) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 8) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 9) ||
                    (selectedConfigSlaveID == 1 && selectedConfigAxisID == 10) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 11) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 12) ||
                    (selectedConfigSlaveID == 1 && selectedConfigAxisID == 13) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 14) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 15) ||
                    (selectedConfigSlaveID == 1 && selectedConfigAxisID == 16) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 17) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 18) ||
                    (selectedConfigSlaveID == 1 && selectedConfigAxisID == 19) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 20) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 21) ||
                    (selectedConfigSlaveID == 1 && selectedConfigAxisID == 22) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 23) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 24) ||
                    (selectedConfigSlaveID == 1 && selectedConfigAxisID == 25) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 26) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 27) ||
                    (selectedConfigSlaveID == 1 && selectedConfigAxisID == 28) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 29) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 30) ||
                    (selectedConfigSlaveID == 1 && selectedConfigAxisID == 31) || (selectedConfigSlaveID == 1 && selectedConfigAxisID == 32) )
                {
                    if (slaveID == "SLAVE-1")
                    {
                        strStationID = "00";
                    }
                    else if (slaveID == "SLAVE-2")
                    {
                        strStationID = "01";
                    }
                    else if (slaveID == "SLAVE-3")
                    {
                        strStationID = "02";
                    }
                    else if (slaveID == "SLAVE-4")
                    {
                        strStationID = "03";
                    }
                    else if (slaveID == "SLAVE-5")
                    {
                        strStationID = "04";
                    }
                    else if (slaveID == "SLAVE-6")
                    {
                        strStationID = "05";
                    }
                    else if (slaveID == "SLAVE-7")
                    {
                        strStationID = "06";
                    }
                    else if (slaveID == "SLAVE-8")
                    {
                        strStationID = "07";
                    }
                    else if (slaveID == "SLAVE-9")
                    {
                        strStationID = "08";
                    }
                    else if (slaveID == "SLAVE-10")
                    {
                        strStationID = "09";
                    }
                    else if (slaveID == "SLAVE-11")
                    {
                        strStationID = "0A";
                    }
                    else if (slaveID == "SLAVE-12")
                    {
                        strStationID = "0B";
                    }
                    else if (slaveID == "SLAVE-13")
                    {
                        strStationID = "0C";
                    }
                    else if (slaveID == "SLAVE-14")
                    {
                        strStationID = "0D";
                    }
                    else if (slaveID == "SLAVE-15")
                    {
                        strStationID = "0E";
                    }
                    else if (slaveID == "SLAVE-16")
                    {
                        strStationID = "0F";
                    }
                    else if (slaveID == "SLAVE-17")
                    {
                        strStationID = "10";
                    }
                    else if (slaveID == "SLAVE-18")
                    {
                        strStationID = "11";
                    }
                    else if (slaveID == "SLAVE-19")
                    {
                        strStationID = "12";
                    }
                    else if (slaveID == "SLAVE-20")
                    {
                        strStationID = "13";
                    }
                    else if (slaveID == "SLAVE-21")
                    {
                        strStationID = "14";
                    }
                    else if (slaveID == "SLAVE-22")
                    {
                        strStationID = "15";
                    }
                    else if (slaveID == "SLAVE-23")
                    {
                        strStationID = "16";
                    }
                    else if (slaveID == "SLAVE-24")
                    {
                        strStationID = "17";
                    }
                    else if (slaveID == "SLAVE-25")
                    {
                        strStationID = "18";
                    }
                    else if (slaveID == "SLAVE-26")
                    {
                        strStationID = "19";
                    }
                    else if (slaveID == "SLAVE-27")
                    {
                        strStationID = "1A";
                    }
                    else if (slaveID == "SLAVE-28")
                    {
                        strStationID = "1B";
                    }
                    else if (slaveID == "SLAVE-29")
                    {
                        strStationID = "1C";
                    }
                    else if (slaveID == "SLAVE-30")
                    {
                        strStationID = "1D";
                    }
                    else if (slaveID == "SLAVE-31")
                    {
                        strStationID = "1E";
                    }
                    else
                    {
                        strStationID = "1F";
                    }
                }
               
                #endregion

                //switch (iARMIndex)
                //{
                //    case 1:
                //        strStationID = "00";
                //        break;
                //    case 2:
                //        strStationID = "01";
                //        break;
                //    default:
                //        strStationID = "10";
                //        break;
                //}

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(PageName.FIRMWAREUPDATE, strStationID, SoftwareStateParameterSet.AxisID, TaskName.FirmwareUpdate, FunctionCode.UPDATE, null, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_UPDATE, "SerialPort_DataTransmiting_For_Update", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_Update
        //函数功能：串口固件升级任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskState                                  任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.20
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_Update(int iARMIndex)
        {
            int iRet = -1;
            string strStationID = null;
            List<TransmitingDataInfoSet> TransmittingDataInfo = new List<TransmitingDataInfoSet>();
            TransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = "01" });

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                switch (iARMIndex)
                {
                    case 1:
                        strStationID = "00";
                        break;
                    case 2:
                        strStationID = "01";
                        break;
                    default:
                        strStationID = "10";
                        break;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(PageName.FIRMWAREUPDATE, strStationID, SoftwareStateParameterSet.AxisID, TaskName.FirmwareUpdate, FunctionCode.UPDATE, null, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_UPDATE, "SerialPort_DataTransmiting_For_Update", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPort_DataTransmiting_For_HardwareAlarm
        //函数功能：串口读硬件报警任务下达
        //
        //输入参数：string strCurrentPage                                当前页面名称
        //         string strTaskState                                  任务名称
        //         List<TransmitingDataInfoSet> TransmittingDataInfo    数据信息
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.16
        //*************************************************************************
        public void SerialPort_DataTransmiting_For_HardwareAlarm()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> TransmittingDataInfo = new List<TransmitingDataInfoSet>();
            TransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = "HardwareAlarm" });

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //添加串口任务
                iRet = HexHelper.AddSerialPortTask(PageName.MAIN, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.HardwareAlarm, FunctionCode.HARDWAREALARM, null, TransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_HARDWARE_ALARM, "SerialPort_DataTransmiting_For_HardwareAlarm", ex);
            }
        }
        #endregion

        #region 公有方法-其他
        //*************************************************************************
        //函数名称：CommunicationSetLoaded
        //函数功能：串口设置Loaded
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.02.02&2023.10.26
        //*************************************************************************
        public void CommunicationSetLoaded()
        {
            try
            {
                GetServoConfigsData();//获取伺服配置文件 由Lilbert于20231026添加

                EventRegister();

                //ConvertTxtToDataSet();
                
                IsSelectedServoNameEnabled = false;

                if (IsInitialized == true)
                {
                    InterfaceEvaluationInitialized();

                    IsInitialized = false;
                }
                else
                {
                    InterfaceEvaluationFromGlobalVariable();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_LOADED, "CommunicationSetLoaded", ex);
            }
        }


        //*************************************************************************
        //函数名称：GetSelectedServoName
        //函数功能：获取选中的伺服名称信息
        //
        //输入参数：string selectedServoName     选中的伺服名称
        //       
        //输出参数：void
        //        
        //编码作者：Lilbert
        //更新时间：2023.10.25
        //*************************************************************************
        public void GetSelectedServoName(string selectedServoName)
        {
            SlaveID = new ObservableCollection<string>();
            AxisID = new ObservableCollection<string>();

            if (selectedServoName == null) return;
            
            ConfigServo.SelectServoConfigs = ConfigServo.ServoConfigs.Where(s => s.ConfigServoName == selectedServoName).ToList();

            ConfigServo.SelectConfigID = (int)ConfigServo.SelectServoConfigs[0].GetType().GetProperty("Id").GetValue(ConfigServo.SelectServoConfigs[0], null);

            //配置从站数量
            ConfigServo.SelectConfigSlaveID = (int)ConfigServo.SelectServoConfigs[0].GetType().GetProperty("ConfigSlaveID").GetValue(ConfigServo.SelectServoConfigs[0], null);

            for (int i = 0; i < ConfigServo.SelectConfigSlaveID; i++)
            {
                SlaveID.Add("SLAVE-" + (i + 1).ToString());
            }

            SelectedSlaveID = "SLAVE-1";

            //配置轴数量
            ConfigServo.SelectConfigAxisID = (int)ConfigServo.SelectServoConfigs[0].GetType().GetProperty("ConfigAxisID").GetValue(ConfigServo.SelectServoConfigs[0], null);   
            for (int i = 0; i < ConfigServo.SelectConfigAxisID; i++)
            {
                AxisID.Add("AXIS-" + (i + 1).ToString());
            }

            SelectedAxisID = "AXIS-1";

            //配置参数表
            ConfigServo.SelectConfigParameter = ConfigServo.SelectServoConfigs[0].GetType().GetProperty("ConfigParameter").GetValue(ConfigServo.SelectServoConfigs[0], null).ToString();  
          
        }

        //public static List<ServoConfigsModel> GetServoConfigsData()
        //{
        //    //XDocument xDoc = XDocument.Load(FilePath.ConfigServo);

        //    XDocument xDoc = XDocument.Load(FilePath.ConfigServo);

        //    List<ServoConfigsModel> servoCofigsList = new List<ServoConfigsModel>();
        //    //string xPath = "OscilloscopePresetConfigs/config";
        //    var configs = xDoc.Descendants("config");
        //    foreach (var c in configs)
        //    {
        //        ServoConfigsModel servoConfig = new ServoConfigsModel
        //        {
        //            Id = c.Element("Id").Value.ChangeInt(),
        //            ConfigServoName = c.Element("ConfigServoName").Value,
        //            ConfigSlaveID = c.Element("ConfigSlaveID").Value.ChangeInt(),
        //            ConfigAxisID = c.Element("ConfigAxisID").Value.ChangeInt(),
        //            ConfigParameter = c.Element("ConfigParameter").Value
        //        };
        //        servoCofigsList.Add(servoConfig);
        //    }
        //    return servoCofigsList;
        //}

        //*************************************************************************
        //函数名称：GetAllConfig
        //函数功能：读取所有配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.30
        //*************************************************************************
        public void GetAllConfig()
        {
            int iRet = -1;

            try
            {
                //伺服驱动器名称写入配置文件
                //IniHelper.IniWriteValue("ServoStudio", "Path", GlobalCurrentInput.SelectedServoName + "_ParameterConfigV30.xlsx", FilePath.Ini);
                IniHelper.IniWriteValue("ServoStudio", "Path", ConfigServo.SelectConfigParameter, FilePath.Ini);

                //获取Ini参数配置文件地址
                iRet = OthersHelper.GetInitialConfigInfo();
                if (iRet != RET.SUCCEEDED)
                {
                    MessageBox.Show("系统初始化异常，部分功能失效!" + "\r\n" + "\r\n" + "请确认配置文件是否存在，并处于关闭状态...", "警告:", MessageBoxButton.OK);
                }

                //获取默认参数单位
                OthersHelper.GetDefaultUnit();
                OthersHelper.GetSelectDefaultUnit();

                //获取默认示波器单位
                OthersHelper.GetOscilloscopeParameterUnitSet();

                //查询Excel数据，并判断是否导入正确的配置文件
                iRet = ExcelHelper.ReadFromExcel(FilePath.Parameter, ref GlobalParameterSet.dt);
                if (iRet == RET.ERROR)
                {
                    GlobalParameterSet.dt = null;
                    GlobalParameterSet.dt_Export = null;

                    MessageBox.Show("系统初始化异常，部分功能失效!" + "\r\n" + "\r\n" + "请确认配置文件是否存在，并处于关闭状态...", "警告:", MessageBoxButton.OK);
                }
                else
                {
                    iRet = OthersHelper.CheckTitleOfConfig();
                    if (iRet != RET.SUCCEEDED)
                    {
                        GlobalParameterSet.dt = null;
                        GlobalParameterSet.dt_Export = null;

                        MessageBox.Show("系统初始化异常，部分功能失效!" + "\r\n" + "\r\n" + "请导入正确格式与内容的配置文件...", "警告:", MessageBoxButton.OK);
                    }
                }

                //复制文件用于导出
                iRet = OthersHelper.DataTableCopy(GlobalParameterSet.dt, ref GlobalParameterSet.dt_Export);
                if (iRet != RET.SUCCEEDED)
                {
                    GlobalParameterSet.dt = null;
                    GlobalParameterSet.dt_Export = null;
                }

                //复制文件用于导出  由Lilbert于2023.05.18添加
                iRet = OthersHelper.DataTableCopy(GlobalParameterSet.dt, ref GlobalParameterSet.dt_Export_ForCompare);
                if (iRet != RET.SUCCEEDED)
                {
                    GlobalParameterSet.dt = null;
                    GlobalParameterSet.dt_Export_ForCompare = null;
                }

                //更新字典信息
                iRet = OthersHelper.RefreshDictionary(GlobalParameterSet.dt, ref CommunicationSet.CurrentPointValue_AxisA, ref CommunicationSet.CurrentPointValue_AxisB);
                if (iRet != RET.SUCCEEDED)
                {
                    GlobalParameterSet.dt = null;
                    GlobalParameterSet.dt_Export = null;
                }

                //导入配置参数监控文件
                iRet = ExcelHelper.ReadFromExcel(FilePath.Monitor, ref GlobalParameterSet.dt_Monitor);
                if (iRet != RET.SUCCEEDED)
                {
                    MessageBox.Show("系统初始化异常，部分功能失效!" + "\r\n" + "\r\n" + "请确认Monitor文件是否存在，并处于关闭状态...", "警告:", MessageBoxButton.OK);
                }

                //导入伺服报警解释文件
                iRet = ExcelHelper.ReadFromExcel(FilePath.HardwareExplanation, ref GlobalParameterSet.dt_HardwareExplanation);
                if (iRet == RET.SUCCEEDED)
                {
                    OthersHelper.RefreshDictionary(GlobalParameterSet.dt_HardwareExplanation, ref GlobalParameterSet.lstHardwareAlarm);
                }
                else
                {
                    MessageBox.Show("系统初始化异常，部分功能失效!" + "\r\n" + "\r\n" + "请确认HardwareExplanation文件是否存在，并处于关闭状态...", "警告:", MessageBoxButton.OK);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_LOADED_CONFIG, "MainWindow_Loaded_Config", ex);
            }
        }

        //*************************************************************************
        //函数名称：MotorLibraryNavigation
        //函数功能：电机参数库导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.01
        //*************************************************************************
        public void MotorLibraryNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.MOTORLIBRARY;
                NavigationService.Navigate("MotorLibraryView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：MotorFeedbackNavigation
        //函数功能：电机反馈界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.02
        //*************************************************************************
        public void MotorFeedbackNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.MOTORFEEDBACK;
                NavigationService.Navigate("MotorFeedbackView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.02
        //*************************************************************************
        public void OnSelectedSerialPortNumChanged() { GlobalCurrentInput.SelectedSerialPortNum = SelectedSerialPortNum; }
        public void OnSelectedBaudRateChanged() { GlobalCurrentInput.SelectedBaudRate = SelectedBaudRate; }
        public void OnSelectedDataBitChanged() { GlobalCurrentInput.SelectedDataBit = SelectedDataBit; }
        public void OnSelectedCheckBitChanged() { GlobalCurrentInput.SelectedCheckBit = SelectedCheckBit; }
        public void OnSelectedEndBitChanged() { GlobalCurrentInput.SelectedEndBit = SelectedEndBit; }
        public void OnSelectedSlaveIDChanged()
        {
            GlobalCurrentInput.SelectedSlaveID = SelectedSlaveID;

            //OthersHelper.GetSelectedSlaveAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID, SelectedAxisID);
            OthersHelper.GetSelectedSlaveAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedAxisID);

            ViewModelSet.Main?.RefreshAxisAddress(OthersHelper.GetSelectedStationAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID)); //轴地址状态栏更新  
        }
        public void OnSelectedAxisIDChanged()
        {
            GlobalCurrentInput.SelectedAxisID = SelectedAxisID;

            //OthersHelper.GetSelectedSlaveAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID, SelectedAxisID);
            OthersHelper.GetSelectedSlaveAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedAxisID);

            ViewModelSet.Main?.RefreshAxisAddress(OthersHelper.GetSelectedStationAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID)); //轴地址状态栏更新            
        }
        public void OnSelectedServoNameChanged()
        {
            GlobalCurrentInput.SelectedServoName = SelectedServoName;

            if (SelectedServoName == "6合一伺服" || SelectedServoName == "MD4伺服")
            {
                IsAxisAddressResetEnabled = ControlVisibility.Collapsed;
            }
            else
            {
                IsAxisAddressResetEnabled = ControlVisibility.Visible;
            }

            GetSelectedServoName(SelectedServoName);

            //RefreshAddScanSlaveAxisIDFlag();

            GetAllConfig();//获取所有配置文件

            //ViewModelSet.Main?.RefreshAxisAddress(SelectedAxisID); //轴地址状态栏更新
        }
        #endregion

        //*************************************************************************
        //函数名称：ConvertTxtToDataSet
        //函数功能：从txt获取伺服配置文件信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.10.26
        //*************************************************************************
        private void ConvertTxtToDataSet()
        {
            string ReadLine;
            string[] array;

            ServoName = new ObservableCollection<string>();

            //StreamReader reader = new StreamReader(Path,System.Text.Encoding.GetEncoding("GB2312"));
            StreamReader reader = new StreamReader(FilePath.ConfigServoName, System.Text.Encoding.GetEncoding("utf-8"));

            while (reader.Peek() >= 0)
            {
                try
                {
                    ReadLine = reader.ReadLine();
                    if (ReadLine != "")
                    {
                        ReadLine = ReadLine.Replace("\"", "");
                        array = ReadLine.Split('\t');
                        if (array.Length == 0)
                        {
                            MessageBox.Show("您选择的导入数据类型有误，请重试！");
                            return;
                        }

                        ServoName.Add(array[0]);

                        ConfigServoList.Add(ReadLine);

                    }
                }
                catch (System.Exception ex)
                {
                    LogHelper.ErrorLog("ConvertTxtToDataSet", ex);
                }
            }
        }

        //*************************************************************************
        //函数名称：GetServoConfigsData
        //函数功能：获取伺服配置文件信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.10.26
        //*************************************************************************
        private void GetServoConfigsData()
        {
            List<ServoConfigsModel> servoConfigsModellist = new List<ServoConfigsModel>();
            ServoName = new ObservableCollection<string>();

            ConfigServo.ServoConfigs = servoConfigsModellist = ServoXmlHelper.GetServoConfigsData();

            ConfigServo.ConfigServoName = servoConfigsModellist.Select(s => s.ConfigServoName).ToList();

            for (int i = 0; i < ConfigServo.ConfigServoName.Count; i++)
            {
                ServoName.Add(ConfigServo.ConfigServoName[i]);
            }

            if (IsInitialized == true)
            {
                SelectedServoName = ConfigServo.ConfigServoName[0];
            }
            else
            {
                SelectedServoName = GlobalCurrentInput.SelectedServoName;
            }            
        }


        //*************************************************************************
        //函数名称：AddSlaveAxisID
        //函数功能：添加从站和轴地址
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.02.15
        //*************************************************************************
        public void AddSlaveAxisID(string Action)
        {           
            IsFileExist = true;

            try
            {
                if (ViewModelSet.AddSlaveAxisID == null)
                {
                    AddSlaveAxisIDSet.GetSlaveIDList = SlaveID;

                    ViewModelSet.AddSlaveAxisID = new AddSlaveAxisIDViewModel();
                }

                OthersHelper.GetWindowsStartupPosition();
                AddSlaveAxisIDSet.Action = Action;

                UICommand registerCommand = new UICommand() { Caption = "确认", IsCancel = false, };
                UICommand cancelCommand = new UICommand() { Caption = "返回", IsCancel = true, };

                if (Action == "AddSlaveAxisID")
                {
                    AddSlaveAxisIDSet.GetSlaveIDList = SlaveID;

                    UICommand result = DialogService_AddSlaveAxisID.ShowDialog(new List<UICommand>() { registerCommand, cancelCommand }, "添加从站地址和轴地址", ViewModelSet.AddSlaveAxisID);
                    if (registerCommand == result)
                    {
                        SlaveID = AddSlaveAxisIDSet.GetSlaveIDList;                        
                    }                   
                }
                else if (Action == "ScanSlaveAxisID")
                {
                    UICommand result = DialogService_AddSlaveAxisID.ShowDialog(new List<UICommand>() { registerCommand, cancelCommand }, "扫描从站地址和轴地址", ViewModelSet.AddSlaveAxisID);
                    if (registerCommand == result)
                    {
                        SlaveID = AddSlaveAxisIDSet.GetSlaveIDList;

                        //关闭串口
                        CommunicationSet.SerialPortInfo.Close();
                    }
                }                                                
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_ADD_SLAVE_AXIS_ID_LIBRARY_DETAILS, "AddSlaveAxisIDLibraryDetails", ex);
            }
        }

        //*************************************************************************
        //函数名称：AxisAddressReset
        //函数功能：轴地址重置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2024.05.24
        //*************************************************************************
        public void AxisAddressReset()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //串口参数设置
                iRet = SerialPortParameterSet();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                iRet = OpenSerialPortConnection_ForAxisAddressReset();

                if (iRet == RET.SUCCEEDED)
                {

                    if (MessageBoxService.ShowMessage("是否轴地址重置...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                    {
                        ParameterReadWriteModel.GetIndexAndDataType_ForAxisAddressReset(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite_ForAxisAddressReset(PageName.COMMUNICATIONSET, TaskName.AxisAddressReset, lstTransmittingDataInfo);
                    }
                }
                else if (iRet == RET.ERROR)
                {
                    ShowNotification(1002);
                }

            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_AXIS_ADDRESS_RESET, "AxisAddressReset", ex);
            }
        }

        //*************************************************************************
        //函数名称：SerialPortParameterSet_For_ScanSlaveAxisID
        //函数功能：串口参数设置
        //
        //输入参数：None
        //         
        //输出参数：true ：OK
        //         false: NG
        //        
        //编码作者：Lilbert
        //更新时间：2023.02.08
        //*************************************************************************
        public int SerialPortParameterSet_For_ScanSlaveAxisID()
        {
            try
            {
                CommunicationSet.SerialPortInfo = new SerialPort();
                CommunicationSet.SerialPortInfo.PortName = SelectedSerialPortNum;
                CommunicationSet.SerialPortInfo.BaudRate = Convert.ToInt32(SelectedBaudRate);
                CommunicationSet.SerialPortInfo.DataBits = Convert.ToInt32(SelectedDataBit);

                switch (SelectedCheckBit)
                {
                    case "无-NONE":
                        CommunicationSet.SerialPortInfo.Parity = Parity.None;
                        break;
                    case "奇校验-ODD":
                        CommunicationSet.SerialPortInfo.Parity = Parity.Odd;
                        break;
                    case "偶校验-EVEN":
                        CommunicationSet.SerialPortInfo.Parity = Parity.Even;
                        break;
                    case "1校验-MARK":
                        CommunicationSet.SerialPortInfo.Parity = Parity.Mark;
                        break;
                    case "0校验-SPACE":
                        CommunicationSet.SerialPortInfo.Parity = Parity.Space;
                        break;
                    default:
                        CommunicationSet.SerialPortInfo.Parity = Parity.None;
                        break;
                }

                switch (SelectedEndBit)
                {
                    case "无-NONE":
                        CommunicationSet.SerialPortInfo.StopBits = StopBits.None;
                        break;
                    case "1":
                        CommunicationSet.SerialPortInfo.StopBits = StopBits.One;
                        break;
                    case "2":
                        CommunicationSet.SerialPortInfo.StopBits = StopBits.Two;
                        break;
                    case "1.5":
                        CommunicationSet.SerialPortInfo.StopBits = StopBits.OnePointFive;
                        break;
                    default:
                        CommunicationSet.SerialPortInfo.StopBits = StopBits.None;
                        break;
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SERIAL_PORT_PARAMETER_SET, "SerialPortParameterSet", ex);

                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：OpenSerialPortConnection_For_ScanSlaveAxisID
        //函数功能：打开串口连接
        //
        //输入参数：None
        //         
        //输出参数：
        //        
        //编码作者：Lilbert
        //更新时间：2023.02.08
        //*************************************************************************
        public int OpenSerialPortConnection_For_ScanSlaveAxisID()
        {
            try
            {
                if (CommunicationSet.SerialPortInfo != null)
                {
                    if (!CommunicationSet.SerialPortInfo.IsOpen)
                    {
                        //连接并注册任务
                        CommunicationSet.SerialPortInfo.Open();
                        CommunicationSet.SerialPortInfo.DataReceived += SerialPortInfo_DataReceived;

                        //状态栏、侧边栏提示
                        //evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect);
                        if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                            evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect);
                        else
                            evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect1);

                        //evtEvaluationAxisAddress(SelectedAxisID);

                        //标志位更新
                        SoftwareStateParameterSet.IsConnected = true;

                        return RET.SUCCEEDED;
                    }
                }

                return RET.NO_EFFECT;
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_OPEN_SERIAL_PORT_CONNECTION, "OpenSerialPortConnection", ex);

                return RET.ERROR;
            }
        }

        #region 私有方法
        //*************************************************************************
        //函数名称：SerialPortParameterSet
        //函数功能：串口参数设置
        //
        //输入参数：None
        //         
        //输出参数：true ：OK
        //         false: NG
        //        
        //编码作者：Ryan
        //更新时间：2019.11.5
        //*************************************************************************
        private int SerialPortParameterSet()
        {
            try
            {
                CommunicationSet.SerialPortInfo = new SerialPort();
                CommunicationSet.SerialPortInfo.PortName = SelectedSerialPortNum;
                CommunicationSet.SerialPortInfo.BaudRate = Convert.ToInt32(SelectedBaudRate);
                CommunicationSet.SerialPortInfo.DataBits = Convert.ToInt32(SelectedDataBit);

                switch (SelectedCheckBit)
                {
                    case "无-NONE":
                        CommunicationSet.SerialPortInfo.Parity = Parity.None;
                        break;
                    case "奇校验-ODD":
                        CommunicationSet.SerialPortInfo.Parity = Parity.Odd;
                        break;
                    case "偶校验-EVEN":
                        CommunicationSet.SerialPortInfo.Parity = Parity.Even;
                        break;
                    case "1校验-MARK":
                        CommunicationSet.SerialPortInfo.Parity = Parity.Mark;
                        break;
                    case "0校验-SPACE":
                        CommunicationSet.SerialPortInfo.Parity = Parity.Space;
                        break;
                    default:
                        CommunicationSet.SerialPortInfo.Parity = Parity.None;
                        break;
                }

                switch (SelectedEndBit)
                {
                    case "无-NONE":
                        CommunicationSet.SerialPortInfo.StopBits = StopBits.None;
                        break;
                    case "1":
                        CommunicationSet.SerialPortInfo.StopBits = StopBits.One;
                        break;
                    case "2":
                        CommunicationSet.SerialPortInfo.StopBits = StopBits.Two;
                        break;
                    case "1.5":
                        CommunicationSet.SerialPortInfo.StopBits = StopBits.OnePointFive;
                        break;
                    default:
                        CommunicationSet.SerialPortInfo.StopBits = StopBits.None;
                        break;
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SERIAL_PORT_PARAMETER_SET, "SerialPortParameterSet", ex);

                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetSelectedServoName
        //函数功能：获取选中的伺服名称信息
        //
        //输入参数：string selectedServoName     选中的伺服名称
        //       
        //输出参数：void
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.30
        //*************************************************************************
        //public void GetSelectedServoName(string selectedServoName)
        //{
        //    if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
        //    {
        //        switch (selectedServoName)
        //        {
        //            case "6合一伺服":
        //                SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "4合一伺服":
        //                SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "MD4伺服":
        //                SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "2合一伺服":
        //                SlaveID = new ObservableCollection<string>() { "SLAVE-1" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "高压单轴伺服":
        //                SlaveID = new ObservableCollection<string>() { "" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "低压单轴伺服":
        //                SlaveID = new ObservableCollection<string>() { "" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "低压高功率伺服":
        //                SlaveID = new ObservableCollection<string>() { "" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;                   
        //            default:
        //                break;
        //        }
        //    }
        //    else
        //    {
        //        switch (selectedServoName)
        //        {                   
        //            case "6-in-1 Servo":
        //                SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "4-in-1 Servo":
        //                SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "MD4 Servo":
        //                SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "2-in-1 Servo":
        //                SlaveID = new ObservableCollection<string>() { "SLAVE-1" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "HV Uniaxial Servo":
        //                SlaveID = new ObservableCollection<string>() { "SLAVE-1" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "LV Uniaxial Servo":
        //                SlaveID = new ObservableCollection<string>() { "SLAVE-1" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            case "LV HighPower Servo":
        //                SlaveID = new ObservableCollection<string>() { "SLAVE-1" };
        //                AxisID = new ObservableCollection<string>() { "AXIS-1" };
        //                SelectedSlaveID = "SLAVE-1";
        //                SelectedAxisID = "AXIS-1";
        //                break;
        //            default:
        //                break;
        //        }
        //    }                       
        //}

        //*************************************************************************
        //函数名称：ConnectionSerialPort
        //函数功能：串口连接
        //
        //输入参数：None
        //         
        //输出参数：
        //        
        //编码作者：Lilbert
        //更新时间：2022.12.30
        //*************************************************************************
        public void ConnectionSerialPort()
        {
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect);
            else
                evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect1);            
        }

        //*************************************************************************
        //函数名称：OpenSerialPortConnection_Reconnection
        //函数功能：打开串口重新连接连接
        //
        //输入参数：None
        //         
        //输出参数：
        //        
        //编码作者：Lilbert
        //更新时间：2023.01.10
        //*************************************************************************
        public void OpenSerialPortConnection_Reconnection()
        {
            try
            {
                if (CommunicationSet.SerialPortInfo != null)
                {
                    if (!CommunicationSet.SerialPortInfo.IsOpen)
                    {
                        //连接并注册任务
                        CommunicationSet.SerialPortInfo.Open();
                        CommunicationSet.SerialPortInfo.DataReceived += SerialPortInfo_DataReceived;

                        //状态栏、侧边栏提示
                        //evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect);
                        if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                            evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect);
                        else
                            evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect1);

                        evtEvaluationAxisAddress(SelectedAxisID);

                        //标志位更新
                        //SoftwareStateParameterSet.IsConnected = true;

                        //return RET.SUCCEEDED;
                    }
                }

                //return RET.NO_EFFECT;
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                //if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                //    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                //else
                //    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_OPEN_SERIAL_PORT_CONNECTION, "OpenSerialPortConnection", ex);

                //return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：OpenSerialPortConnection
        //函数功能：打开串口连接
        //
        //输入参数：None
        //         
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.11.5
        //*************************************************************************
        private int OpenSerialPortConnection()
        {
            try
            {
                if (CommunicationSet.SerialPortInfo != null)
                {
                    if (!CommunicationSet.SerialPortInfo.IsOpen)
                    {
                        //连接并注册任务
                        CommunicationSet.SerialPortInfo.Open();
                        CommunicationSet.SerialPortInfo.DataReceived += SerialPortInfo_DataReceived;

                        //状态栏、侧边栏提示
                        //evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect);
                        if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                            evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect);
                        else
                            evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect1);

                        evtEvaluationAxisAddress(GlobalCurrentInput.SelectedAxisID);                        

                        //标志位更新
                        SoftwareStateParameterSet.IsConnected = true;

                        return RET.SUCCEEDED;                                   
                    }
                }

                return RET.NO_EFFECT;
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_OPEN_SERIAL_PORT_CONNECTION, "OpenSerialPortConnection", ex);

                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：OpenSerialPortConnection_ForAxisAddressReset
        //函数功能：打开串口连接
        //
        //输入参数：None
        //         
        //输出参数：
        //        
        //编码作者：Lilbert
        //更新时间：2024.05.24
        //*************************************************************************
        private int OpenSerialPortConnection_ForAxisAddressReset()
        {
            try
            {
                if (CommunicationSet.SerialPortInfo != null)
                {
                    if (!CommunicationSet.SerialPortInfo.IsOpen)
                    {
                        //连接并注册任务
                        CommunicationSet.SerialPortInfo.Open();
                        CommunicationSet.SerialPortInfo.DataReceived += SerialPortInfo_DataReceived;

                        //状态栏、侧边栏提示
                        //evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect);
                        if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                            evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect);
                        else
                            evtEvaluationSerialPortConnectState(CommunicationSet.SerialPortInfo.PortName + ConnectState.Explaination_Connect1);

                        evtEvaluationAxisAddress(GlobalCurrentInput.SelectedAxisID);

                        //标志位更新
                        SoftwareStateParameterSet.IsConnected = true;

                        return RET.SUCCEEDED;
                    }
                }

                return RET.NO_EFFECT;
            }
            catch (System.Exception ex)
            {
                //evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error);
                else
                    evtEvaluationSerialPortConnectState(ConnectState.Explaination_Error1);
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_OPEN_SERIAL_PORT_CONNECTION, "OpenSerialPortConnection_ForAxisAddressReset", ex);

                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：EventRegister
        //函数功能：事件注册
        //
        //输入参数：NONE
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.20
        //*************************************************************************
        private void EventRegister()
        {
            if (WindowSet.clsMainWindow != null)
            {
                evtShowNotification += WindowSet.clsMainWindow.ShowNotification;
            }

            if (ViewModelSet.Main != null)
            {
                evtEvaluationSerialPortConnectState += ViewModelSet.Main.RefreshSerialPortConnectState;
                evtEvaluationSerialPortWorkProcess += ViewModelSet.Main.RefreshSerialPortWorkState;
                evtEvaluationAxisAddress += ViewModelSet.Main.RefreshAxisAddress;
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationInitialized_For_CNUS
        //函数功能：串口参数初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.01.03
        //*************************************************************************
        public void InterfaceEvaluationInitialized_For_CNUS()
        {
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                SelectedServoName = "6合一伺服";
            }
            else
            {
                SelectedServoName = "6-in-1 Servo";
            }

            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                //ServoName = new ObservableCollection<string>() { "6合一伺服", "4合一伺服", "MD4伺服", "2合一伺服", "高压单轴伺服", "低压单轴伺服", "低压高功率伺服" };
            }
            else
            {
                ServoName = new ObservableCollection<string>() { "6-in-1 Servo", "4-in-1 Servo", "MD4 Servo", "2-in-1 Servo", "HV Uniaxial Servo", "LV Uniaxial Servo", "LV HighPower Servo" };
            }            
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationInitialized
        //函数功能：串口参数初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.02
        //*************************************************************************
        private void InterfaceEvaluationInitialized()
        {
            RefreshSerialPortNum();

            BaudRate = new ObservableCollection<string>() { "19200", "38400", "57600", "115200", "128000", "256000" };
            DataBit = new ObservableCollection<string>() { "5", "6", "7", "8" };
            CheckBit = new ObservableCollection<string>() { "无-NONE", "奇校验-ODD", "偶校验-EVEN", "1校验-MARK", "0校验-SPACE" };
            EndBit = new ObservableCollection<string>() { "无-NONE", "1", "2", "1.5" };
            //AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2", "AXIS-3", "AXIS-4", "AXIS-5", "AXIS-6" };
            //ServoName = new ObservableCollection<string>() { "6合一伺服", "4合一伺服", "2合一伺服", "高压单轴伺服", "低压单轴伺服", "低压高功率伺服" };           

            SelectedBaudRate = "115200";
            SelectedDataBit = "8";
            SelectedCheckBit = "无-NONE";
            SelectedEndBit = "1";
            //SelectedAxisID = "AXIS-1";
            //SelectedServoName = "6合一伺服";            
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.02.02&2023.02.10
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            //RefreshSerialPortNum();
            RefreshSerialPortNumFromGlobalVariable();//由Lilbert于2023.02.10添加切换到通讯配置时，保留之前连接的串口

            BaudRate = new ObservableCollection<string>() { "19200", "38400", "57600", "115200", "128000", "256000" };
            DataBit = new ObservableCollection<string>() { "5", "6", "7", "8" };
            CheckBit = new ObservableCollection<string>() { "无-NONE", "奇校验-ODD", "偶校验-EVEN", "1校验-MARK", "0校验-SPACE" };
            EndBit = new ObservableCollection<string>() { "无-NONE", "1", "2", "1.5" };
            //AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2", "AXIS-3", "AXIS-4", "AXIS-5", "AXIS-6" };
            //ServoName = new ObservableCollection<string>() { "6合一伺服", "4合一伺服", "2合一伺服", "高压单轴伺服", "低压单轴伺服", "高压低功率伺服" };            

            AddSlaveAxisIDSet.GetSlaveIDList = new ObservableCollection<string>() { "SLAVE-1" };//再次加载时初始化表格

            SelectedSerialPortNum = GlobalCurrentInput.SelectedSerialPortNum;//选中串口号
            SelectedBaudRate = GlobalCurrentInput.SelectedBaudRate;//选中波特率
            SelectedDataBit = GlobalCurrentInput.SelectedDataBit;//选中数据位
            SelectedCheckBit = GlobalCurrentInput.SelectedCheckBit;//选中校验位
            SelectedEndBit = GlobalCurrentInput.SelectedEndBit;//选中停止位
            SelectedAxisID = GlobalCurrentInput.SelectedAxisID;//选中的转轴ID
            SelectedSlaveID = GlobalCurrentInput.SelectedSlaveID;//选中的从站ID
            SelectedServoName = GlobalCurrentInput.SelectedServoName;//选中的伺服驱动器名称
        }

        //*************************************************************************
        //函数名称：ConvertTxtToDataSet
        //函数功能：从配置文件中获取伺服驱动器名称
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.10.10
        //*************************************************************************
        //private void ConvertTxtToDataSet()
        //{
        //    string ReadLine;
        //    string[] array;

        //    ServoName = new ObservableCollection<string>();

        //    //StreamReader reader = new StreamReader(Path,System.Text.Encoding.GetEncoding("GB2312"));
        //    StreamReader reader = new StreamReader(FilePath.ConfigServoName, System.Text.Encoding.GetEncoding("utf-8"));

        //    while (reader.Peek() >= 0)
        //    {
        //        try
        //        {
        //            ReadLine = reader.ReadLine();
        //            if (ReadLine != "")
        //            {
        //                ReadLine = ReadLine.Replace("\"", "");
        //                array = ReadLine.Split('\t');
        //                if (array.Length == 0)
        //                {
        //                    MessageBox.Show("您选择的导入数据类型有误，请重试！");
        //                    return;
        //                }

        //                ServoName.Add(array[0]);

        //                ConfigServoList.Add(ReadLine);
        //                //ConfigServoList.Add(array[0]);
        //                //ConfigServoList.Add(array[1]);
        //                //ConfigServoList.Add(array[2]);
        //                //ConfigServoList.Add(array[3]);

        //            }
        //        }
        //        catch (System.Exception ex)
        //        {
        //            LogHelper.ErrorLog("ConvertTxtToDataSet", ex);
        //        }
        //    }
        //}
        

        //*************************************************************************
        //函数名称：AnalyseReceivingMessage
        //函数功能：分析接收数据帧
        //
        //输入参数：NONE
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.12.20&2021.11.15&2022.05.24
        //*************************************************************************
        private int AnalyseReceivingMessage()
        {
            int iRet = -1;

            try
            {
                if (evtShowNotification == null || evtEvaluationSerialPortWorkProcess == null || evtEvaluationSerialPortConnectState == null)
                {
                    return RET.NO_EFFECT;
                }

                #region 回传测试-正常
                if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FunctionCode.TEST_COMMUNICATION)//回传测试响应正常
                {
                    iRet = HexHelper.ReceivingMessage_ForTestCommunication();
                    if (iRet == RET.SUCCEEDED)
                    {
                        if (SoftwareStateParameterSet.IsFirstEchoTest)
                        {
                            //信息提示
                            WindowSet.clsMainWindow.ShowNotification(1004);
                           
                            //系统对时
                            OthersHelper.AssignSynchronizationTask();

                            //下达单位换算任务
                            CurrentUnit.bInitialized = true;
                            OthersHelper.AssignUnitExchangeTask();
                        }
                        else
                        {
                            if (AddSlaveAxisIDSet.Action == "ScanSlaveAxisID")//由Lilbert于2023.02.09添加扫描从站地址选择
                            {
                                ViewModelSet.Main?.ShowHintInfo("从站扫描回送测试成功");
                            }
                            else
                            {
                                WindowSet.clsMainWindow.ShowNotification(1005);
                            }
                            //信息提示
                            //WindowSet.clsMainWindow.ShowNotification(1005);
                        }                        
                    }

                    return RET.SUCCEEDED;                     //由Lilbert添加，为3个芯片写时间戳
                }
                #endregion

                #region 参数读取-正常
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FunctionCode.PARAMETER_READ)//参数读取响应正常
                {
                    #region 读取数字还是字符串信息
                    if (CommunicationSet.TaskName == TaskName.ReadString)//读字符串，获取版本信息
                    {
                        iRet = HexHelper.ReceivingMessage_ForParameterReadString();
                    }
                    else//读数据
                    {
                        iRet = HexHelper.ReceivingMessage_ForParameterRead();
                    }
                    #endregion

                    #region 读取版本号字符串信息
                    if (CommunicationSet.TaskName == TaskName.ReadString_ForSoftVersion)//读取版本号信息
                    {
                        iRet = HexHelper.ReceivingMessage_ForParameterReadString();
                    }
                    //else//读数据
                    //{
                    //    iRet = HexHelper.ReceivingMessage_ForParameterRead();
                    //}
                    #endregion

                    #region 判断是否是批处理读取
                    if (SoftwareStateParameterSet.lstReceivingDataInfo.Count != 0 && CommunicationSet.TaskName == TaskName.BatchRead)
                    {
                        //switch (SoftwareStateParameterSet.iBatchIndex)
                        //{
                        //    case 0:
                        //        ViewModelSet.Main?.ShowHintInfo("电机参数读取成功"); break;
                        //    case 1:
                        //        ViewModelSet.Main?.ShowHintInfo("基本配置参数读取成功"); break;
                        //    case 2:
                        //        ViewModelSet.Main?.ShowHintInfo("运控参数读取成功"); break;
                        //    case 3:
                        //        ViewModelSet.Main?.ShowHintInfo("高级配置参数读取成功"); break;
                        //    case 4:
                        //        ViewModelSet.Main?.ShowHintInfo("端子输入参数读取成功"); break;
                        //    case 5:
                        //        ViewModelSet.Main?.ShowHintInfo("端子输出参数读取成功"); break;
                        //    case 6:
                        //        ViewModelSet.Main?.ShowHintInfo("故障保护参数读取成功"); break;
                        //    case 7:
                        //        ViewModelSet.Main?.ShowHintInfo("参数全部读取成功");                     
                        //        iRet = OthersHelper.ExportParameterInfo();
                        //        if (iRet != RET.NO_EFFECT)
                        //        {
                        //            ShowNotification_CrossThread(iRet);
                        //        }
                        //        break;
                        //    default: break;
                        //}

                        if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                        {
                            switch (SoftwareStateParameterSet.iBatchIndex)
                            {
                                case 0:
                                    ViewModelSet.Main?.ShowHintInfo("电机参数读取成功"); break;
                                case 1:
                                    ViewModelSet.Main?.ShowHintInfo("基本配置参数读取成功"); break;
                                case 2:
                                    ViewModelSet.Main?.ShowHintInfo("运控参数读取成功"); break;
                                case 3:
                                    ViewModelSet.Main?.ShowHintInfo("高级配置参数读取成功"); break;
                                case 4:
                                    ViewModelSet.Main?.ShowHintInfo("端子输入参数读取成功"); break;
                                case 5:
                                    ViewModelSet.Main?.ShowHintInfo("端子输出参数读取成功"); break;
                                case 6:
                                    ViewModelSet.Main?.ShowHintInfo("故障保护参数读取成功"); break;
                                case 7:
                                    ViewModelSet.Main?.ShowHintInfo("参数全部读取成功");
                                    iRet = OthersHelper.ExportParameterInfo();
                                    if (iRet != RET.NO_EFFECT)
                                    {
                                        ShowNotification_CrossThread(iRet);
                                    }
                                    break;
                                default: break;
                            }
                        }
                        else
                        {
                            switch (SoftwareStateParameterSet.iBatchIndex)
                            {
                                case 0:
                                    ViewModelSet.Main?.ShowHintInfo("Motor parameters read successfully"); break;
                                case 1:
                                    ViewModelSet.Main?.ShowHintInfo("Basic configuration parameters read successfully"); break;
                                case 2:
                                    ViewModelSet.Main?.ShowHintInfo("Operation control parameters read successfully"); break;
                                case 3:
                                    ViewModelSet.Main?.ShowHintInfo("Advanced configuration parameters read successfully"); break;
                                case 4:
                                    ViewModelSet.Main?.ShowHintInfo("Terminal input parameters read successfully"); break;
                                case 5:
                                    ViewModelSet.Main?.ShowHintInfo("Terminal output parameters read successfully"); break;
                                case 6:
                                    ViewModelSet.Main?.ShowHintInfo("Failure protection parameters read successfully"); break;
                                case 7:
                                    ViewModelSet.Main?.ShowHintInfo("Successfully read all parameters");
                                    iRet = OthersHelper.ExportParameterInfo();
                                    if (iRet != RET.NO_EFFECT)
                                    {
                                        ShowNotification_CrossThread(iRet);
                                    }
                                    break;
                                default: break;
                            }
                        }

                        if (SoftwareStateParameterSet.iBatchIndex < SoftwareStateParameterSet.lstReceivingDataInfo.Count - 1)
                        {
                            iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.BatchRead);
                            if (iRet != RET.ERROR)
                            {
                                if (SerialPortTask.TaskManagement[iRet].TaskName == TaskName.BatchRead && SerialPortTask.TaskManagement[iRet].ExecutionState == TaskState.EXECUTED)
                                {
                                    SoftwareStateParameterSet.iBatchIndex++;
                                    SerialPort_DataTransmiting_For_ParameterBatchRead(SoftwareStateParameterSet.iBatchIndex, SoftwareStateParameterSet.lstReceivingDataInfo);
                                }
                            }
                        }
                        else
                        {
                            //清空批量写入集合
                            SoftwareStateParameterSet.lstReceivingDataInfo.Clear();
                        }
                    }
                    else if (SoftwareStateParameterSet.lstReceivingDataInfo.Count != 0 && CommunicationSet.TaskName == TaskName.BatchReadForImport)
                    {
                        if (SoftwareStateParameterSet.iBatchIndex < SoftwareStateParameterSet.lstReceivingDataInfo.Count - 1)
                        {
                            iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.BatchReadForImport);
                            if (iRet != RET.ERROR)
                            {
                                if (SerialPortTask.TaskManagement[iRet].TaskName == TaskName.BatchReadForImport && SerialPortTask.TaskManagement[iRet].ExecutionState == TaskState.EXECUTED)
                                {
                                    SoftwareStateParameterSet.iBatchIndex++;
                                    SerialPort_DataTransmiting_For_ParameterBatchRead_ForImport(SoftwareStateParameterSet.iBatchIndex, SoftwareStateParameterSet.lstReceivingDataInfo);
                                }
                            }
                        }
                        else
                        {
                            //清空批量写入集合
                            SoftwareStateParameterSet.lstReceivingDataInfo.Clear();
                            //触发导入前的批量读取完成事件
                            evtBatchReadCompletedForImport?.Invoke();
                        }
                    }
                    else if (SoftwareStateParameterSet.lstReceivingDataInfo.Count != 0 && CommunicationSet.TaskName == TaskName.BatchReadForImport)
                    {
                        if (SoftwareStateParameterSet.iBatchIndex < SoftwareStateParameterSet.lstReceivingDataInfo.Count - 1)
                        {
                            iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.BatchReadForImport);
                            if (iRet != RET.ERROR)
                            {
                                if (SerialPortTask.TaskManagement[iRet].TaskName == TaskName.BatchReadForImport && SerialPortTask.TaskManagement[iRet].ExecutionState == TaskState.EXECUTED)
                                {
                                    SoftwareStateParameterSet.iBatchIndex++;
                                    SerialPort_DataTransmiting_For_ParameterBatchRead_ForImport(SoftwareStateParameterSet.iBatchIndex, SoftwareStateParameterSet.lstReceivingDataInfo);
                                }
                            }
                        }
                        else
                        {
                            //清空批量写入集合
                            SoftwareStateParameterSet.lstReceivingDataInfo.Clear();
                            //触发导入前的批量读取完成事件
                            evtBatchReadCompletedForImport?.Invoke();
                        }
                    }
                    else if (SoftwareStateParameterSet.lstReceivingDataInfo.Count != 0 && CommunicationSet.TaskName == TaskName.BatchReadForImport)
                    {
                        if (SoftwareStateParameterSet.iBatchIndex < SoftwareStateParameterSet.lstReceivingDataInfo.Count - 1)
                        {
                            iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.BatchReadForImport);
                            if (iRet != RET.ERROR)
                            {
                                if (SerialPortTask.TaskManagement[iRet].TaskName == TaskName.BatchReadForImport && SerialPortTask.TaskManagement[iRet].ExecutionState == TaskState.EXECUTED)
                                {
                                    SoftwareStateParameterSet.iBatchIndex++;
                                    SerialPort_DataTransmiting_For_ParameterBatchRead_ForImport(SoftwareStateParameterSet.iBatchIndex, SoftwareStateParameterSet.lstReceivingDataInfo);
                                }
                            }
                        }
                        else
                        {
                            //清空批量写入集合
                            SoftwareStateParameterSet.lstReceivingDataInfo.Clear();
                            //触发导入前的批量读取完成事件
                            evtBatchReadCompletedForImport?.Invoke();
                        }
                    }
                    #endregion

                    #region 判断是否是批处理读取-参数导入-差异处理
                    if (SoftwareStateParameterSet.lstReceivingDataInfo.Count != 0 && CommunicationSet.TaskName == TaskName.BatchRead_ForImportConfig)//由Lilbert于2023.04.23日添加
                    {
                        switch (SoftwareStateParameterSet.iBatchIndex_ForImportConfig)
                        {
                            case 0:
                                ViewModelSet.Main?.ShowHintInfo("电机参数读取成功"); break;
                            case 1:
                                ViewModelSet.Main?.ShowHintInfo("基本配置参数读取成功"); break;
                            case 2:
                                ViewModelSet.Main?.ShowHintInfo("运控参数读取成功"); break;
                            case 3:
                                ViewModelSet.Main?.ShowHintInfo("高级配置参数读取成功"); break;
                            case 4:
                                ViewModelSet.Main?.ShowHintInfo("端子输入参数读取成功"); break;
                            case 5:
                                ViewModelSet.Main?.ShowHintInfo("端子输出参数读取成功"); break;
                            case 6:
                                ViewModelSet.Main?.ShowHintInfo("故障保护参数读取成功"); break;
                            case 7:
                                ViewModelSet.Main?.ShowHintInfo("参数全部读取成功");
                                iRet = OthersHelper.ExportParameterInfo_ForImportConfig();
                                if (iRet != RET.NO_EFFECT)
                                {
                                    //ShowNotification_CrossThread(iRet);
                                    ViewModelSet.Main?.ShowHintInfo("文件保存成功...");                                    
                                }

                                Thread.Sleep(500);
                                // This is the crucial callback to continue the import process
                                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                                {
                                    ViewModelSet.Main?.ContinueWithImportAfterRefresh();
                                }));
                                break;
                            default: break;
                        }

                        if (SoftwareStateParameterSet.iBatchIndex_ForImportConfig < SoftwareStateParameterSet.lstReceivingDataInfo.Count - 1)
                        {
                            iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.BatchRead_ForImportConfig);
                            if (iRet != RET.ERROR)
                            {
                                if (SerialPortTask.TaskManagement[iRet].TaskName == TaskName.BatchRead_ForImportConfig && SerialPortTask.TaskManagement[iRet].ExecutionState == TaskState.EXECUTED)
                                {
                                    SoftwareStateParameterSet.iBatchIndex_ForImportConfig++;
                                    SerialPort_DataTransmiting_For_ParameterBatchRead_ForImportConfig(SoftwareStateParameterSet.iBatchIndex_ForImportConfig, SoftwareStateParameterSet.lstReceivingDataInfo);
                                }
                            }
                        }
                        else
                        {
                            //清空批量写入集合
                            SoftwareStateParameterSet.lstReceivingDataInfo.Clear();
                        }
                    }
                    #endregion

                    #region 正常读取
                    if (iRet == RET.SUCCEEDED  && CommunicationSet.TaskName == TaskName.MotorFeedback)
                    {
                        ViewModelSet.MotorFeedback?.EvaluationMotorFeedbackParameter();//回写电机反馈
                    }
                    if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.FaultDataConfig)//由Lilbert于20240312添加
                    {
                        ViewModelSet.FaultDataConfig?.EvaluationAlarmCacheSettingParameter();//回写故障数据配置参数
                    }
                    if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.FaultDataOscilloscopeConfig)//由Lilbert于20240312添加
                    {
                        ViewModelSet.FaultDataOscilloscope?.EvalutionAdjustmentParameter();//回写故障示波器数据配置参数
                    }
                    if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.MotorFeedbackAutoLearn)//由Lilbert添加参数自学习
                    {
                        ViewModelSet.MotorFeedbackAutoLearn?.EvaluationMotorFeedbackAutoLearnParameter();//回写参数自学习
                    }
                    if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterMotorLineUVWSequenceAbsEncoderOffsetIdentification)//由Lilbert添加电机动力线相序辨识绝对值编码器偏置辨识
                    {
                        ViewModelSet.MotorLineUVWSequenceAbsEncoderOffset?.EvaluationMotorLineUVWSequenceAbsEncoderOffsetParameter();//回写电机动力线相序辨识绝对值编码器偏置辨识
                    }
                    if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterInertiaIdentificationParameterSelfTunningIdentification)//由Lilbert添加电机负载惯量辨识和参数自整定
                    {
                        ViewModelSet.InertiaIdentificationParameterSelfTunning?.EvaluationMotorInertiaIdentificationParameterSelfTunningParameter();//回写电机负载惯量辨识和参数自整定
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.GearRatio)
                    {
                        ViewModelSet.Unit?.EvaluationUnitParameter();//回写电子齿轮比
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.LimitAmplitude)
                    {
                        ViewModelSet.LimitAmplitude?.EvaluationLimitAmplitudeParameter();//回写限幅保护
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.NormalSetting)
                    {
                        ViewModelSet.NormalSetting?.EvaluationNormalSettingParameter();//回写一般设置
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.DigitalIO)
                    {
                        ViewModelSet.Digital?.EvaluationDigitalIOParameter();//回写数字IO
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.SpeedLoop)
                    {
                        ViewModelSet.SpeedLoop?.EvaluationSpeedLoopParameter();//回写速度环
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.CurrentLoop)
                    {
                        ViewModelSet.CurrentLoop?.EvaluationCurrentLoopParameter();//回写电流环
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.AdvancedFeedback)
                    {
                        ViewModelSet.AdvancedFeedback?.EvaluationAdvancedFeedbackParameter();//回写高级功能参数
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.PositionLoop)
                    {
                        ViewModelSet.PositionLoop?.EvaluationPositionLoopParameter();//回写位置环
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.OfflineInertiaIdentification)
                    {
                        ViewModelSet.OfflineInertiaIdentification?.EvalutionOfflineInertiaIdentificationParameter();//回写离线惯量识别
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterIdentification)
                    {
                        ViewModelSet.MotorParameterIdentification?.EvaluationMotorParameterIdentification();//回写电机参数识别
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterIdentificationListen)
                    {
                        ViewModelSet.MotorParameterIdentification?.CallBackForListenIdentification();//回写是否辨识完成
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterAutoLearnIdentification)
                    {
                        ViewModelSet.MotorFeedbackAutoLearn?.EvaluationMotorParameterAutoLearnIdentification();//回写电机参数自学习识别   //由Lilbert添加电机参数自学习辨识
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterAutoLearnIdentificationListen)
                    {
                        ViewModelSet.MotorFeedbackAutoLearn?.CallBackForListenAutoLearnIdentification();//回写是否辨识完成      //由Lilbert添加回写是否辨识完成
                    }                    
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterMotorLineUVWSequenceAbsEncoderOffsetIdentificationListen)
                    {
                        if (MotorParameterMotorLineUVWSequenceAbsEncoderOffsetIdentificationSet.IsCallBackOn_MotorLineUVWSequenceIdentification == true)
                        {
                            ViewModelSet.MotorLineUVWSequenceAbsEncoderOffset?.CallBackForListenMotorLineUVWSequenceIdentification();//回写电机动力线相序是否辨识完成      //由Lilbert添加回写是否电机动力线相序完成
                        }
                        else
                        {
                            ViewModelSet.MotorLineUVWSequenceAbsEncoderOffset?.CallBackForListenAbsEncoderOffsetIdentification();//回写绝对值编码器偏置是否辨识完成      //由Lilbert添加回写是否绝对值编码器偏置辨识完成
                        }                       
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterMotorLineUVWSequenceAbsEncoderOffsetIdentification)
                    {
                        ViewModelSet.MotorLineUVWSequenceAbsEncoderOffset?.EvaluationMotorParameterMotorLineUVWSequenceAbsEncoderOffsetIdentification();//回写电机动力线相序绝对值编码器偏置识别   //由Lilbert添加电机动力线相序绝对值编码器偏置辨识
                    }                

                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterMotorLineUVWSequenceIdentification)
                    {
                        ViewModelSet.MotorLineUVWSequenceAbsEncoderOffset?.EvaluationMotorParameterMotorLineUVWSequenceIdentification();//回写电机动力线相序识别   //由Lilbert添加电机动力线相序辨识
                    }

                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterMotorAbsEncoderOffsetIdentification)
                    {
                        ViewModelSet.MotorLineUVWSequenceAbsEncoderOffset?.EvaluationMotorParameterAbsEncoderOffsetIdentification();//回写绝对值编码器偏置识别   //由Lilbert添加绝对值编码器偏置辨识
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterInertiaIdentificationParameterSelfTunningIdentificationListen)
                    {
                        if (MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_InertiaIdentification == true)
                        {
                            ViewModelSet.InertiaIdentificationParameterSelfTunning?.CallBackForListenInertiaIdentification();//回写电机负载惯量辨识是否辨识完成      //由Lilbert添加回写是否电机负载惯量辨识完成
                        }
                        else
                        {
                            ViewModelSet.InertiaIdentificationParameterSelfTunning?.CallBackForListenParameterSelfTunningIdentification();//回写电机参数自整定是否辨识完成      //由Lilbert添加回写是否电机参数自整定辨识完成
                        }
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterMotorInertiaIdentification)
                    {
                        ViewModelSet.InertiaIdentificationParameterSelfTunning?.EvaluationMotorInertiaIdentification();//回写电机负载惯量识别   //由Lilbert添加电机负载惯量辨识
                    }

                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterMotorSelfTunning)
                    {
                        ViewModelSet.InertiaIdentificationParameterSelfTunning?.EvaluationMotorParameterSelfTunningIdentification();//回写电机参数自整定识别   //由Lilbert添加电机参数自整定识别 
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.SeekZero)
                    {
                        ViewModelSet.SeekZero?.EvaluationSeekZeroParameter();//回写回零模式
                    }

                    //else if (iRet == RET.SUCCEEDED && CommunicationSet.CurrentPageName == PageName.OSCILLOSCOPE && CommunicationSet.TaskName == TaskName.FunctionGenerator + TaskName.ThreeLoop + TaskName.Action)
                    //{
                    //    ViewModelSet.Oscilloscope?.EvalutionAdjustmentParameter();//回写调试参数
                    //}

                    else if (iRet == RET.SUCCEEDED && CommunicationSet.CurrentPageName == PageName.OSCILLOSCOPE && CommunicationSet.TaskName == TaskName.FunctionGenerator + TaskName.Rigidity + TaskName.ThreeLoop + TaskName.Action + TaskName.ParameterTunning)//由Lilbert添加手动参数调优
                    {
                        ViewModelSet.Oscilloscope?.EvalutionAdjustmentParameter();//回写调试参数
                    }

                    //else if (iRet == RET.SUCCEEDED && CommunicationSet.CurrentPageName == PageName.FAULTDATAOSCILLOSCOPE)//由Lilbert添加故障数据示波器页
                    //{
                    //    ViewModelSet.FaultDataOscilloscope?.EvalutionAdjustmentParameter();//回写调试参数
                    //}

                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ReadString)
                    {
                        WindowSet.clsMainWindow?.ShowEdition();//回写读字符串的硬件信息
                    }

                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ReadString_ForSoftVersion)//由Lilbert于20230602添加
                    {
                        WindowSet.clsMainWindow?.ShowEdition_ForExportParas();//回写读版本号字符串的信息
                    }

                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ABSEncoderSingleTurnBit)
                    {
                        ViewModelSet.Main?.AssignAxisAction();//获取编码器单圈位数
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.Jog)
                    {
                        ViewModelSet.Jog?.EvaluationJogParameter();//回写Jog参数
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.JogDriection)
                    {
                        ViewModelSet.JogDriection?.EvaluationJogDriectionParameter();//回写Jog调试和电机方向参数     //由Lilbert添加Jog调试和电机方向
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ProgramJog)
                    {
                        ViewModelSet.ProgramJog?.EvaluationProgramJogParameter();//回写程序Jog参数
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.JogActualEnable)
                    {
                        ViewModelSet.Jog?.RefreshJogFlag("JogSwitch");
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.OfflineInertiaIdentificationActualEnable)
                    {
                        ViewModelSet.OfflineInertiaIdentification?.RefreshOfflineInertiaIdentificationFlag("Switched");
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterIdentificationActualEnable)
                    {
                        ViewModelSet.MotorParameterIdentification?.RefreshParameterIdentificationFlag("Switched");
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterAutoLearnIdentificationMotEstState)
                    {
                        ViewModelSet.MotorFeedbackAutoLearn?.RefreshParameterAutoLearnIdentificationFlag("Switched");//由Lilbert增加参数自学习辨识
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterMotorLineUVWSequenceIdentificationMotEstState)
                    {
                        ViewModelSet.MotorLineUVWSequenceAbsEncoderOffset?.RefreshParameterMotorLineUVWSequenceIdentificationFlag("Switched");//由Lilbert增加电机动力线相序辨识
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterAbsEncoderOffsetIdentificationMotEstState)
                    {
                        ViewModelSet.MotorLineUVWSequenceAbsEncoderOffset?.RefreshParameterAbsEncoderOffsetIdentificationFlag("Switched");//由Lilbert增加绝对值编码器偏置辨识
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterInertiaIdentificationMotEstState)
                    {
                        ViewModelSet.InertiaIdentificationParameterSelfTunning?.RefreshParameterMotorInertiaIdentificationFlag("Switched");//由Lilbert增加电机负载惯量辨识
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ParameterSelfTunningMotEstState)
                    {
                        ViewModelSet.InertiaIdentificationParameterSelfTunning?.RefreshParameterMotorParameterSelfTunningFlag("Switched");//由Lilbert参数自整定辨识
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.ProgramJogActualEnable)
                    {
                        ViewModelSet.ProgramJog?.RefreshProgramJogFlag("ProgramJogSwitch");
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.UnitExchanged)
                    {
                        iRet = OthersHelper.GetUnitExchangedInfo();//更新单位转换信息
                        if (iRet == RET.ERROR)
                        {
                            ShowNotification_CrossThread(3009);
                        }
                        else if (iRet == RET.NO_EFFECT)
                        {
                            ShowNotification_CrossThread(3010);
                        }
                        else if (iRet == RET.SUCCEEDED)
                        {
                            if (!CurrentUnit.bInitialized)
                            {
                                ShowNotification_CrossThread(3012);
                            }
                            
                            if (SoftwareStateParameterSet.CurrentPageName == PageName.FUNCTIONGENERATOR)
                            {
                                SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.FunctionGenerator;
                                NavigationService.Navigate("OscilloscopeView", null, this);
                            }
                            else if (SoftwareStateParameterSet.CurrentPageName == PageName.ACTIONDEBUG)
                            {
                                SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.Action;
                                NavigationService.Navigate("OscilloscopeView", null, this);
                            }
                            else if (SoftwareStateParameterSet.CurrentPageName == PageName.PARAMETERTUNNING)
                            {
                                SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.ParameterTunning;
                                NavigationService.Navigate("OscilloscopeView", null, this);
                            }
                            else if (SoftwareStateParameterSet.CurrentPageName == PageName.LIMITAMPLITUDE)
                            {
                                NavigationService.Navigate("LimitAmplitudeView", null, this);
                            }
                            else if (SoftwareStateParameterSet.CurrentPageName == PageName.OFFLINEINERTIAINENTIFICATION)
                            {
                                NavigationService.Navigate("OfflineInertiaIdentificationView", null, this);
                            }
                        }
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.AxisStatusA)
                    {
                        if (HexHelper.GetStatusWord(0, Convert.ToUInt16(ARMStatus.AxisValueA))=="55")//A轴伺服状态判断
                        {
                            ShowNotification_CrossThread(3013);
                            ARMStatus.IsFirmUpdate = false;
                        }
                        else
                        {
                            SerialPort_DataTransmiting_For_AxisStatus(AxisIndex: "B");
                        }
                    }
                    else if (iRet == RET.SUCCEEDED && CommunicationSet.TaskName == TaskName.AxisStatusB)
                    {
                        if (HexHelper.GetStatusWord(0, Convert.ToUInt16(ARMStatus.AxisValueB)) == "55")//B轴伺服状态判断
                        {
                            ShowNotification_CrossThread(3013);
                            ARMStatus.IsFirmUpdate = false;
                        }
                        else
                        {
                            ARMStatus.IsFirmUpdate = true;
                        }
                    }
                    else if (iRet == RET.SUCCEEDED && evtEvaluationParamterReadAndWrite != null && (CommunicationSet.CurrentPageName == PageName.Advanced || CommunicationSet.CurrentPageName == PageName.Auxiliary || CommunicationSet.CurrentPageName == PageName.Basic || CommunicationSet.CurrentPageName == PageName.CIA402 ||
                             CommunicationSet.CurrentPageName == PageName.Common ||CommunicationSet.CurrentPageName == PageName.Control || CommunicationSet.CurrentPageName == PageName.DI || CommunicationSet.CurrentPageName == PageName.DO || CommunicationSet.CurrentPageName == PageName.FaultAndProtection || CommunicationSet.CurrentPageName == PageName.Motor))
                    {
                        evtEvaluationParamterReadAndWrite(CommunicationSet.TaskName);
                    }
                    #endregion

                    #region 失败读取
                    if (CommunicationSet.TaskName != TaskName.BatchRead && CommunicationSet.TaskName != TaskName.BatchRead_ForImportConfig && CommunicationSet.TaskName != TaskName.IntervalRefresh && iRet != RET.SUCCEEDED)//非实时读取任务的失败提示
                    {
                        ViewModelSet.Main?.ShowHintInfo("参数读取失败");
                    }
                    #endregion
                }
                #endregion

                #region 参数写入-正常
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FunctionCode.PARAMETER_WRITE)//参数写入响应正常
                {
                    iRet = HexHelper.ReceivingMessage_ForParameterWrite();
                    if (iRet == RET.SUCCEEDED)
                    {
                        #region 判断是否是批处理写入
                        if (SoftwareStateParameterSet.lstTransmittingDataInfo.Count != 0 && CommunicationSet.TaskName == TaskName.BatchWrite)
                        {
                            //switch (SoftwareStateParameterSet.iBatchIndex)
                            //{
                            //    case 0:
                            //        ViewModelSet.Main?.ShowHintInfo("电机参数写入成功"); break;
                            //    case 1:
                            //        ViewModelSet.Main?.ShowHintInfo("基本配置参数写入成功"); break;
                            //    case 2:
                            //        ViewModelSet.Main?.ShowHintInfo("运控参数写入成功"); break;
                            //    case 3:
                            //        ViewModelSet.Main?.ShowHintInfo("高级配置参数写入成功"); break;
                            //    case 4:
                            //        ViewModelSet.Main?.ShowHintInfo("端子输入参数写入成功"); break;
                            //    case 5:
                            //        ViewModelSet.Main?.ShowHintInfo("端子输出参数写入成功"); break;
                            //    case 6:
                            //        ViewModelSet.Main?.ShowHintInfo("故障保护参数写入成功"); break;
                            //    case 7:
                            //        ViewModelSet.Main?.ShowHintInfo("参数全部写入成功");     //由Lilbert添加
                            //        break;
                            //    default: break;
                            //}
                            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                            {
                                switch (SoftwareStateParameterSet.iBatchIndex)
                                {
                                    case 0:
                                        ViewModelSet.Main?.ShowHintInfo("电机参数读取成功"); break;
                                    case 1:
                                        ViewModelSet.Main?.ShowHintInfo("基本配置参数读取成功"); break;
                                    case 2:
                                        ViewModelSet.Main?.ShowHintInfo("运控参数读取成功"); break;
                                    case 3:
                                        ViewModelSet.Main?.ShowHintInfo("高级配置参数读取成功"); break;
                                    case 4:
                                        ViewModelSet.Main?.ShowHintInfo("端子输入参数读取成功"); break;
                                    case 5:
                                        ViewModelSet.Main?.ShowHintInfo("端子输出参数读取成功"); break;
                                    case 6:
                                        ViewModelSet.Main?.ShowHintInfo("故障保护参数读取成功"); break;
                                    case 7:
                                        ViewModelSet.Main?.ShowHintInfo("参数全部读取成功");
                                        iRet = OthersHelper.ExportParameterInfo();
                                        if (iRet != RET.NO_EFFECT)
                                        {
                                            ShowNotification_CrossThread(iRet);
                                        }
                                        break;
                                    default: break;
                                }
                            }
                            else
                            {
                                switch (SoftwareStateParameterSet.iBatchIndex)
                                {
                                    case 0:
                                        ViewModelSet.Main?.ShowHintInfo("Motor parameters read successfully"); break;
                                    case 1:
                                        ViewModelSet.Main?.ShowHintInfo("Basic configuration parameters read successfully"); break;
                                    case 2:
                                        ViewModelSet.Main?.ShowHintInfo("Operation control parameters read successfully"); break;
                                    case 3:
                                        ViewModelSet.Main?.ShowHintInfo("Advanced configuration parameters read successfully"); break;
                                    case 4:
                                        ViewModelSet.Main?.ShowHintInfo("Terminal input parameters read successfully"); break;
                                    case 5:
                                        ViewModelSet.Main?.ShowHintInfo("Terminal output parameters read successfully"); break;
                                    case 6:
                                        ViewModelSet.Main?.ShowHintInfo("Failure protection parameters read successfully"); break;
                                    case 7:
                                        ViewModelSet.Main?.ShowHintInfo("Successfully read all parameters");
                                        iRet = OthersHelper.ExportParameterInfo();
                                        if (iRet != RET.NO_EFFECT)
                                        {
                                            ShowNotification_CrossThread(iRet);
                                        }                                       
                                        break;
                                    default: break;
                                }
                            }

                            if (SoftwareStateParameterSet.iBatchIndex < SoftwareStateParameterSet.lstTransmittingDataInfo.Count - 1)
                            {
                                iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.BatchWrite);
                                if (iRet != RET.ERROR)
                                {
                                    if (SerialPortTask.TaskManagement[iRet].TaskName == TaskName.BatchWrite && SerialPortTask.TaskManagement[iRet].ExecutionState == TaskState.EXECUTED)
                                    {
                                        SoftwareStateParameterSet.iBatchIndex++;
                                        SerialPort_DataTransmiting_For_ParameterBatchWrite(SoftwareStateParameterSet.iBatchIndex, SoftwareStateParameterSet.lstTransmittingDataInfo);
                                    }
                                }                                
                            }
                            else
                            {
                                //清空批量写入集合
                                SoftwareStateParameterSet.lstTransmittingDataInfo.Clear();

                                //重新计算参数单位换算
                                CurrentUnit.bInitialized = true;
                                OthersHelper.AssignUnitExchangeTask();
                            }
                        }
                        #endregion

                        #region 判断是否是批处理写入-参数导入-差异处理
                        if (SoftwareStateParameterSet.lstTransmittingDataInfo.Count != 0 && CommunicationSet.TaskName == TaskName.BatchWrite_ForImportConfig)
                        {
                            switch (SoftwareStateParameterSet.iBatchIndex_ForImportConfig)
                            {
                                case 0:
                                    ViewModelSet.Main?.ShowHintInfo("电机参数写入成功"); break;
                                case 1:
                                    ViewModelSet.Main?.ShowHintInfo("基本配置参数写入成功"); break;
                                case 2:
                                    ViewModelSet.Main?.ShowHintInfo("运控参数写入成功"); break;
                                case 3:
                                    ViewModelSet.Main?.ShowHintInfo("高级配置参数写入成功"); break;
                                case 4:
                                    ViewModelSet.Main?.ShowHintInfo("端子输入参数写入成功"); break;
                                case 5:
                                    ViewModelSet.Main?.ShowHintInfo("端子输出参数写入成功"); break;
                                case 6:
                                    ViewModelSet.Main?.ShowHintInfo("故障保护参数写入成功"); break;
                                case 7:
                                    ViewModelSet.Main?.ShowHintInfo("参数全部写入成功"); break;
                                default: break;
                            }

                            if (SoftwareStateParameterSet.iBatchIndex_ForImportConfig < SoftwareStateParameterSet.lstTransmittingDataInfo.Count - 1)
                            {
                                iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.BatchWrite_ForImportConfig);
                                if (iRet != RET.ERROR)
                                {
                                    if (SerialPortTask.TaskManagement[iRet].TaskName == TaskName.BatchWrite_ForImportConfig && SerialPortTask.TaskManagement[iRet].ExecutionState == TaskState.EXECUTED)
                                    {
                                        SoftwareStateParameterSet.iBatchIndex_ForImportConfig++;
                                        SerialPort_DataTransmiting_For_ParameterBatchWrite_ForCompare(SoftwareStateParameterSet.iBatchIndex_ForImportConfig, SoftwareStateParameterSet.lstTransmittingDataInfo);
                                    }
                                }
                            }
                            else
                            {
                                //清空批量写入集合
                                SoftwareStateParameterSet.lstTransmittingDataInfo.Clear();

                                //重新计算参数单位换算
                                CurrentUnit.bInitialized = true;
                                OthersHelper.AssignUnitExchangeTask();
                            }
                        }
                        #endregion

                        #region 判断是否是批处理写入-参数导入-带刷新
                        if (SoftwareStateParameterSet.lstTransmittingDataInfo.Count != 0 && CommunicationSet.TaskName == TaskName.ImportWriteAndRefresh)
                        {
                            if (SoftwareStateParameterSet.iBatchIndex_ForImportConfig < SoftwareStateParameterSet.lstTransmittingDataInfo.Count - 1)
                            {
                                iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.ImportWriteAndRefresh);
                                if (iRet != RET.ERROR)
                                {
                                    if (SerialPortTask.TaskManagement[iRet].TaskName == TaskName.ImportWriteAndRefresh && SerialPortTask.TaskManagement[iRet].ExecutionState == TaskState.EXECUTED)
                                    {
                                        SoftwareStateParameterSet.iBatchIndex_ForImportConfig++;
                                        SerialPort_DataTransmiting_For_ParameterBatchWrite_ForCompare(SoftwareStateParameterSet.iBatchIndex_ForImportConfig, SoftwareStateParameterSet.lstTransmittingDataInfo, TaskName.ImportWriteAndRefresh);
                                    }
                                }
                            }
                            else
                            {
                                //清空批量写入集合
                                SoftwareStateParameterSet.lstTransmittingDataInfo.Clear();

                                // 写入成功后，从设备读取参数更新到UI界面
                                if (ViewModelSet.ParameterReadWrite != null)
                                {
                                    ViewModelSet.ParameterReadWrite.ParameterRead(SoftwareStateParameterSet.CurrentPageName);
                                }

                                //重新计算参数单位换算
                                CurrentUnit.bInitialized = true;
                                OthersHelper.AssignUnitExchangeTask();
                            }
                        }
                        #endregion

                        #region 判断是否全部轴工作
                        else if (SoftwareStateParameterSet.lstAxisInfo.Count != 0 && 
                            (CommunicationSet.TaskName == TaskName.AllAxisFactoryReset || CommunicationSet.TaskName == TaskName.AllAxisDisabled ||
                            CommunicationSet.TaskName == TaskName.AllAxisRunning || CommunicationSet.TaskName == TaskName.AllAxisStop ||
                            CommunicationSet.TaskName == TaskName.AllAxisConfigStop))
                        {
                            string strTaskName = CommunicationSet.TaskName;
                            if (SoftwareStateParameterSet.AxisIndex == SoftwareStateParameterSet.lstAxisInfo.Count - 1)
                            {
                                ViewModelSet.Main?.ShowHintInfo(strTaskName + "设置成功");
                                ViewModelSet.Main.Timer_System.IsEnabled = true;
                            }
                            else if (SoftwareStateParameterSet.AxisIndex < SoftwareStateParameterSet.lstAxisInfo.Count - 1)
                            {
                                iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == strTaskName);
                                if (iRet != -1)
                                {
                                    if (SerialPortTask.TaskManagement[iRet].TaskName == strTaskName && SerialPortTask.TaskManagement[iRet].ExecutionState == TaskState.EXECUTED)
                                    {
                                        SoftwareStateParameterSet.AxisIndex++;
                                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_AllAxis(strTaskName, SoftwareStateParameterSet.AxisIndex);
                                    }
                                }
                            }
                        }
                        #endregion

                        #region 判断是否全部轴使能
                        else if (ControlWordSet.WriteSwitch && ControlWordSet.ListValue.Count == 18)
                        {
                            if (ControlWordSet.IndexOfList == ControlWordSet.ListValue.Count - 1)
                            {
                                if (TaskName.AllAxisEnabled == "全部故障清除")                  //由Lilbert与20220524添加“全部故障清除”
                                {
                                    ViewModelSet.Main?.ShowHintInfo(TaskName.AllAxisEnabled + "设置成功");
                                }
                                else
                                {                                   
                                    ViewModelSet.Main?.ShowHintInfo(TaskName.AllFaultReset + "设置成功");
                                }
                                
                                OthersHelper.ClearControlWordSet();

                                SoftwareStateParameterSet.AxisID = SoftwareStateParameterSet.LastAxisID;
                                //SoftwareStateParameterSet.SlaveID = SoftwareStateParameterSet.LastStationID;
                                SoftwareStateParameterSet.SlaveID = SoftwareStateParameterSet.LastSlaveID;

                                //ViewModelSet.Main?.RefreshAxisAddress(OthersHelper.GetSelectedAxisID(SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID)); //轴地址状态栏更新
                                ViewModelSet.Main?.RefreshAxisAddress(OthersHelper.GetSelectedStationAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID)); //轴地址状态栏更新
                                ViewModelSet.Main.Timer_System.IsEnabled = true;
                            }
                            else if (ControlWordSet.IndexOfList < ControlWordSet.ListValue.Count - 1)
                            {
   
                                iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.ControlWord);
                                if (iRet != -1)
                                {
                                    if (SerialPortTask.TaskManagement[iRet].TaskName == TaskName.ControlWord && SerialPortTask.TaskManagement[iRet].ExecutionState == TaskState.EXECUTED)
                                    {
                                        ControlWordSet.IndexOfList++;
                                        if (ControlWordSet.IndexOfList % 3 == 0)
                                        {
                                            SoftwareStateParameterSet.AxisIndex++;
                                            SoftwareStateParameterSet.SlaveID = SoftwareStateParameterSet.lstAxisInfo[SoftwareStateParameterSet.AxisIndex].SlaveID;
                                            SoftwareStateParameterSet.AxisID = SoftwareStateParameterSet.lstAxisInfo[SoftwareStateParameterSet.AxisIndex].AxisID;
                                        }
                                    }
                                }
                            }
                        }
                        #endregion

                        #region 判断是否写控制字
                        else if (ControlWordSet.WriteSwitch && ControlWordSet.ListValue.Count > 0 && ControlWordSet.ListValue.Count < 18)
                        {
                            if (ControlWordSet.IndexOfList == ControlWordSet.ListValue.Count - 1)
                            {                          
                                if (ControlWordSet.IsExistTaskAfterControlWord && ControlWordSet.TaskName == TaskName.FunctionGenerator)
                                {
                                    ViewModelSet.Oscilloscope?.ControlFunctionGenerator("1");
                                }

                                OthersHelper.RefreshAllServoStatusHint(ControlWordSet.TaskName);
                                ViewModelSet.Main?.ShowHintInfo(ControlWordSet.TaskName + "设置成功");
                                OthersHelper.ClearControlWordSet();
                            }
                            else if (ControlWordSet.IndexOfList < ControlWordSet.ListValue.Count - 1)
                            {                              
                                iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.ControlWord);
                                if (iRet == -1)
                                {
                                    ControlWordSet.IndexOfList++;//初始从-1转换为0
                                }
                                else
                                {
                                    if (SerialPortTask.TaskManagement[iRet].TaskName == TaskName.ControlWord && SerialPortTask.TaskManagement[iRet].ExecutionState == TaskState.EXECUTED)
                                    {
                                        ControlWordSet.IndexOfList++;
                                    }
                                }
                            }
                        }
                        #endregion
                                             
                        #region 正常写入
                        else
                        {
                            #region 其他
                            //时间戳任务不展示
                            if (CommunicationSet.TaskName != TaskName.Timestamp)
                            {                               
                                ViewModelSet.Main?.ShowHintInfo(CommunicationSet.TaskName + "设置成功");
                            }
                              
                            //重新计算单位换算公式 
                            if (CommunicationSet.TaskName == "电机反馈" || CommunicationSet.TaskName == "额定转矩" || CommunicationSet.TaskName == "编码器类型" || CommunicationSet.TaskName == "单圈值分辨率位数" || CommunicationSet.TaskName == "ABZ编码器脉冲数")
                            {
                                CurrentUnit.bInitialized = true;
                                OthersHelper.AssignUnitExchangeTask();
                            }

                            //电机反馈参数下达后，System Prm Init字段置2
                            if (CommunicationSet.TaskName == TaskName.MotorFeedback)
                            {
                                OthersHelper.SystemParameterInitialize();
                            }

                            //参数恢复出厂值后刷新当前界面
                            if (CommunicationSet.TaskName == TaskName.SystemParameterInitialize)
                            {
                                ViewModelSet.Main?.RefreshParameterReadAndWriteTabControl();
                            }
                            #endregion

                            #region 离线惯量识别
                            if (CommunicationSet.TaskName == TaskName.OfflineInertiaIdentificationOperatingMode && ViewModelSet.OfflineInertiaIdentification != null)
                            {
                                ViewModelSet.OfflineInertiaIdentification.IsInitialized = false;
                                OthersHelper.OperatingControl("Operating Setting", "4", TaskName.OfflineInertiaIdentificationSwitch, PageName.OFFLINEINERTIAINENTIFICATION);
                            }
                            else if (CommunicationSet.TaskName == TaskName.OfflineInertiaIdentificationSwitch && ViewModelSet.OfflineInertiaIdentification != null)
                            {
                                if (!ViewModelSet.OfflineInertiaIdentification.IsClosed)
                                {
                                    OthersHelper.GetActualEnableStatus(TaskName.OfflineInertiaIdentificationActualEnable);
                                }
                                else
                                {
                                    OthersHelper.OperatingControl("Operating End", "1", TaskName.OfflineInertiaIdentificationOperatingEnd, PageName.OFFLINEINERTIAINENTIFICATION);
                                }
                            }
                            else if (CommunicationSet.TaskName == TaskName.OfflineInertiaIdentificationContinuously && OfflineInertiaIdentificationSet.IsContinuous) 
                            {
                                OthersHelper.OperatingControl("Operating Setting", "2", TaskName.OfflineInertiaIdentificationContinuously, PageName.OFFLINEINERTIAINENTIFICATION);
                            }
                            else if (CommunicationSet.TaskName == TaskName.OfflineInertiaIdentificationUnload && ViewModelSet.OfflineInertiaIdentification != null)
                            {
                                OthersHelper.GetActualEnableStatus(TaskName.OfflineInertiaIdentificationActualEnable);
                                ViewModelSet.OfflineInertiaIdentification.IsClosed = true;
                            }
                            #endregion

                            #region 电机参数辨识
                            if (CommunicationSet.TaskName == TaskName.ParameterIdentificationOperatingMode && ViewModelSet.MotorParameterIdentification != null)
                            {
                                ViewModelSet.MotorParameterIdentification.IsInitialized = false;
                                OthersHelper.OperatingControl("Operating Setting", "4", TaskName.ParameterIdentificationSwitch, PageName.MOTORPARAMETERIDENTIFICATION);
                            }
                            else if (CommunicationSet.TaskName == TaskName.ParameterIdentificationSwitch && ViewModelSet.MotorParameterIdentification != null)//电机参数识别使能/禁能
                            {
                                if (!ViewModelSet.MotorParameterIdentification.IsClosed)
                                {
                                    OthersHelper.GetActualEnableStatus(TaskName.ParameterIdentificationActualEnable);
                                }
                                else
                                {
                                    OthersHelper.OperatingControl("Operating End", "1", TaskName.ParameterIdentificationOperatingEnd, PageName.MOTORPARAMETERIDENTIFICATION);
                                }
                            }
                            else if (CommunicationSet.TaskName == TaskName.ParameterIdentificationUnload && ViewModelSet.MotorParameterIdentification != null)
                            {
                                //判断内部是否使能，若使能先禁能，再退出
                                OthersHelper.GetActualEnableStatus(TaskName.ParameterIdentificationActualEnable);
                                ViewModelSet.MotorParameterIdentification.IsClosed = true;
                            }
                            #endregion

                            #region 电机参数自学习辨识
                            if (CommunicationSet.TaskName == TaskName.ParameterAutoLearnIdentificationOperatingMode && ViewModelSet.MotorFeedbackAutoLearn != null)
                            {
                                OthersHelper.GetMotEstStateStatus(TaskName.ParameterAutoLearnIdentificationMotEstState);//由Lilbert添加参数自学习辨识状态
                            }
                            #endregion

                            #region 电机动力线相序辨识
                            if (CommunicationSet.TaskName == TaskName.ParameterMotorLineUVWSequenceIdentificationOperatingMode && ViewModelSet.MotorLineUVWSequenceAbsEncoderOffset != null)
                            {
                                OthersHelper.GetMotEstStateStatus(TaskName.ParameterMotorLineUVWSequenceIdentificationMotEstState);//由Lilbert添加电机动力线相序辨识状态
                            }
                            #endregion

                            #region 绝对值编码器偏置辨识
                            if (CommunicationSet.TaskName == TaskName.ParameterAbsEncoderOffsetIdentificationOperatingMode && ViewModelSet.MotorLineUVWSequenceAbsEncoderOffset != null)
                            {
                                OthersHelper.GetMotEstStateStatus(TaskName.ParameterAbsEncoderOffsetIdentificationMotEstState);//由Lilbert添加绝对值编码器偏置辨识状态
                            }
                            #endregion

                            #region 电机负载惯量辨识
                            if (CommunicationSet.TaskName == TaskName.ParameterInertiaIdentificationOperatingMode && ViewModelSet.InertiaIdentificationParameterSelfTunning != null)
                            {
                                OthersHelper.GetMotEstStateStatus(TaskName.ParameterInertiaIdentificationMotEstState);//由Lilbert添加电机负载惯量辨识状态
                            }
                            #endregion

                            #region 电机参数自整定辨识
                            if (CommunicationSet.TaskName == TaskName.ParameterSelfTunningOperatingMode && ViewModelSet.InertiaIdentificationParameterSelfTunning != null)
                            {
                                ViewModelSet.InertiaIdentificationParameterSelfTunning.IsParameterSelfTunningInitialized = false;
                                OthersHelper.OperatingControl("Auto Tunning", "2", TaskName.ParameterSelfTunningSwitch, PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING);
                            }
                            else if (CommunicationSet.TaskName == TaskName.ParameterSelfTunningSwitch && ViewModelSet.InertiaIdentificationParameterSelfTunning != null)//参数自整开关
                            {
                                if (!ViewModelSet.InertiaIdentificationParameterSelfTunning.IsClosed)
                                {
                                    ViewModelSet.InertiaIdentificationParameterSelfTunning.IsInitialized_ForParameterSelfTunningIdentification = false;
                                    ViewModelSet.InertiaIdentificationParameterSelfTunning?.RefreshParameterSelfTunningDriectionFlag("ParameterSelfTunningSwitch");
                                    OthersHelper.GetMotEstStateStatus(TaskName.ParameterSelfTunningMotEstState);//由Lilbert添加电机参数自整定辨识状态
                                }
                                else
                                {
                                    ViewModelSet.InertiaIdentificationParameterSelfTunning.IsParameterSelfTunningInitialized = true;
                                    OthersHelper.OperatingControl("Auto Tunning", "0", TaskName.ParameterSelfTunningOperatingEnd, PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING);
                                }
                            }
                            #endregion

                            #region Jog模式选择
                            if (CommunicationSet.TaskName == TaskName.JogOperatingMode && ViewModelSet.Jog != null)
                            {
                                ViewModelSet.Jog.IsInitialized = false;
                                OthersHelper.OperatingControl("Operating Setting", "4", TaskName.JogSwitch, PageName.JOG);
                            }
                            else if (CommunicationSet.TaskName == TaskName.JogSwitch && ViewModelSet.Jog != null)//Jog使能/禁能
                            {
                                if (!ViewModelSet.Jog.IsClosed)
                                {
                                    OthersHelper.GetActualEnableStatus(TaskName.JogActualEnable);                  
                                }
                                else
                                {
                                    OthersHelper.OperatingControl("Operating End", "1", TaskName.JogOperatingEnd, PageName.JOG);
                                }                                                                                                 
                            }
                            else if (CommunicationSet.TaskName == TaskName.JogContinuously && JogSet.IsContinuous)//连续Jog
                            {
                                if (JogSet.Direction == "2")//正转
                                {
                                    OthersHelper.OperatingControl("Operating Setting", "2", TaskName.JogContinuously, PageName.JOG);
                                }
                                else//反转
                                {
                                    OthersHelper.OperatingControl("Operating Setting", "3", TaskName.JogContinuously, PageName.JOG);
                                }
                            }
                            #endregion

                            #region Jog方向模式选择   
                            if (CommunicationSet.TaskName == TaskName.JogDriectionOperatingMode && ViewModelSet.JogDriection != null)
                            {
                                ViewModelSet.JogDriection.IsJogDriectionInitialized = false;
                                OthersHelper.OperatingControl("Motor Jog Command", "16", TaskName.JogDriectionSwitch, PageName.MOTORDRIECTIONJOGPAGE);
                            }
                            else if (CommunicationSet.TaskName == TaskName.JogDriectionSwitch && ViewModelSet.JogDriection != null)//Jog使能/禁能
                            {
                                if (!ViewModelSet.JogDriection.IsClosed)
                                {
                                    ViewModelSet.JogDriection?.RefreshJogDriectionFlag("JogSwitch");
                                    //OthersHelper.GetActualEnableStatus(TaskName.JogActualEnable);
                                }
                                else
                                {
                                    ViewModelSet.JogDriection.IsJogDriectionInitialized = true;
                                    OthersHelper.OperatingControl("Motor Jog Command", "0", TaskName.JogDriectionOperatingEnd, PageName.MOTORDRIECTIONJOGPAGE);
                                }
                            }
                            else if (CommunicationSet.TaskName == TaskName.JogDriectionContinuously && JogDirectionSet.IsContinuous)//连续Jog
                            {
                                if (JogDirectionSet.Direction == "2")//正转
                                {
                                    //OthersHelper.OperatingControl("Operating Setting", "2", TaskName.JogDriectionContinuously, PageName.MOTORDRIECTIONJOGPAGE);
                                    OthersHelper.OperatingControl("Motor Jog Command", "17", TaskName.JogRunDriection, PageName.MOTORDRIECTIONJOGPAGE);
                                }
                                else//反转
                                {
                                    OthersHelper.OperatingControl("Motor Jog Command", "18", TaskName.JogRunDriection, PageName.MOTORDRIECTIONJOGPAGE);
                                }
                            }
                            #endregion

                            #region ProgramJog模式选择
                            if (CommunicationSet.TaskName == TaskName.ProgramJogOperatingMode && ViewModelSet.ProgramJog != null)
                            {
                                ViewModelSet.ProgramJog.IsInitialized = false;
                                OthersHelper.OperatingControl("Operating Setting", "4", TaskName.ProgramJogSwitch, PageName.PROGRAMJOG);
                            }
                            else if (CommunicationSet.TaskName == TaskName.ProgramJogSwitch && ViewModelSet.ProgramJog != null)//ProgramJog使能/禁能
                            {
                                if (!ViewModelSet.ProgramJog.IsClosed)
                                {
                                    OthersHelper.GetActualEnableStatus(TaskName.ProgramJogActualEnable);
                                }
                                else
                                {
                                    OthersHelper.OperatingControl("Operating End", "1", TaskName.ProgramJogOperatingEnd, PageName.PROGRAMJOG);
                                }                                                                                                          
                            }
                            else if (CommunicationSet.TaskName == TaskName.ProgramJogContinuously && ProgramJogSet.IsContinuous)//连续ProgramJog
                            {
                                if (ProgramJogSet.Direction == "2")//正转
                                {
                                    OthersHelper.OperatingControl("Operating Setting", "2", TaskName.ProgramJogContinuously, PageName.PROGRAMJOG);
                                }
                                else//反转
                                {
                                    OthersHelper.OperatingControl("Operating Setting", "3", TaskName.ProgramJogContinuously, PageName.PROGRAMJOG);
                                }
                            }
                        }
                        #endregion

                        #endregion
                    }
                    else
                    {
                        ViewModelSet.Main?.ShowHintInfo("参数写入失败");
                        ViewModelSet.Main.Timer_System.IsEnabled = true;
                    }
                }
                #endregion

                #region 数据采集
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FunctionCode.ACQUISITION && HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(12, 2) == AcquisitionExecutedCode.ACQUISITION)//数据采集响应正常
                {
                    //若连续采样停止
                    iRet = HexHelper.StopContinuousAcquisition();
                    if (iRet == RET.SUCCEEDED)
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }

                    //数据分析
                    iRet = HexHelper.ReceivingMessage_ForAcquisition();
                    if (iRet == RET.SUCCEEDED)
                    {
                        //信息提示
                        AcquisitionInfoSet.CurrentProcess = TaskName.AskingAcquisitionState;
                        evtEvaluationSerialPortWorkProcess(AcquisitionInfoSet.CurrentProcess);
                    }
                    else
                    {                     
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //信息提示
                        evtShowNotification(3002);

                        //按钮使能
                        evtOscilliscopeButtonEnabled();

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }
                }
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FunctionCode.ACQUISITION && HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(12, 2) == AcquisitionExecutedCode.ASK_ACQUISITION)//采集状态询问正常
                {
                    //若连续采样停止
                    iRet = HexHelper.StopContinuousAcquisition();
                    if (iRet == RET.SUCCEEDED)
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }

                    //数据分析
                    iRet = HexHelper.ReceivingMessage_ForAskingAcquisitionState();
                    if (iRet == 100)//正在采集
                    {
                        //更新采样工作进展
                        AcquisitionInfoSet.CurrentProcess = TaskName.Acquiring;
                        evtEvaluationSerialPortWorkProcess(AcquisitionInfoSet.CurrentProcess);
                    }
                    else if (iRet == 101)//采集完毕
                    {
                        //更新采样工作进展                       
                        AcquisitionInfoSet.CurrentProcess = TaskName.UploadingAcquisition;
                        evtEvaluationSerialPortWorkProcess(AcquisitionInfoSet.CurrentProcess);

                        //提高数据上传速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskFast;                      
                    }
                    else if (iRet == 102)//未收到采样指令
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }
                    else if (iRet == 103)//接收数据帧错误
                    {                       
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //信息提示
                        evtShowNotification(3003);

                        //按钮使能
                        evtOscilliscopeButtonEnabled();

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }
                }
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FunctionCode.ACQUISITION && HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(12, 2) == AcquisitionExecutedCode.UPLOAD_ACQUISITION)//数据上传正常
                {
                    //若连续采样停止
                    iRet = HexHelper.StopContinuousAcquisition();
                    if (iRet == RET.SUCCEEDED)
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }

                    //数据分析
                    iRet = HexHelper.ReceivingMessage_ForUploading();
                    if (iRet == 100)//结束上传
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAcquisitionInfoSet();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //还原任务管理线程周期
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        if (AcquisitionInfoSet.AcquisitionSwitch)
                        {
                            //连续静态展示
                            if (AcquisitionInfoSet.IsContinuous && AcquisitionInfoSet.OscilloscopeDisplayMethod == OscilloscopeDisplayMethod.STATIC)
                            {
                                //判断是否画图完成-没有完成不可以画图
                                if (AcquisitionInfoSet.IsDrawingCompleted)
                                {
                                    ViewModelSet.OscilloscopeView?.DisplayOscilloscope();
                                }

                                //循环采集
                                ViewModelSet.Oscilloscope?.ParameterAcquisitionStart();

                                //连续采样累计
                                AcquisitionInfoSet.ContinuousAcquisitionTimes++;
                            }

                            //连续动态展示
                            if (AcquisitionInfoSet.IsContinuous && AcquisitionInfoSet.OscilloscopeDisplayMethod == OscilloscopeDisplayMethod.DYNAMIC)
                            {
                                //开启微秒级示波器画图线程
                                if (!PthreadStatement.MicrosecondsOscilloscopeDrawing.PthreadWorking)
                                {
                                    ViewModelSet.OscilloscopeView?.DisplayOscilloscopeLoop();
                                }

                                //循环采集
                                ViewModelSet.Oscilloscope?.LoopParameterAcquisitionStart();

                                //连续采样累计
                                AcquisitionInfoSet.ContinuousAcquisitionTimes++;
                            }

                            //不连续静态展示
                            if (!AcquisitionInfoSet.IsContinuous)
                            {
                                //画图
                                ViewModelSet.OscilloscopeView?.DisplayOscilloscope();

                                //连续采样累计
                                AcquisitionInfoSet.ContinuousAcquisitionTimes = 0;

                                //按钮使能
                                evtOscilliscopeButtonEnabled();
                            }
                        }
                        else
                        {
                            //按钮使能
                            evtOscilliscopeButtonEnabled();
                        }
                    }
                    else if (iRet == 101)//接续上传
                    {
                        //若连续采样停止
                        iRet = HexHelper.StopContinuousAcquisition();
                        if (iRet == RET.SUCCEEDED)
                        {
                            //清空所有采样任务-用于数据保护
                            OthersHelper.ClearAllAcquisitionTask();

                            //更新采样工作进展                                              
                            evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                            //还原任务管理线程周期
                            PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                            return RET.SUCCEEDED;
                        }
                        else
                        {
                            //信息提示
                            evtEvaluationSerialPortWorkProcess(AcquisitionInfoSet.CurrentProcess + "-第" + AcquisitionInfoSet.CurrentMessageNumber + "帧");

                            if (AcquisitionInfoSet.CurrentMessageNumber >= 10)
                            {
                                ViewModelSet.Main?.ShowHintInfo("请耐心等待波形绘制，勿反复下发任务");
                            }
                        }
                    }
                    else if (iRet == RET.ERROR)
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //按钮使能
                        evtOscilliscopeButtonEnabled();

                        //还原任务管理线程周期
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        //信息提示
                        evtShowNotification(3004);
                    }
                }
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FunctionCode.ACQUISITION && HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(12, 2) == AcquisitionExecutedCode.STOP_ACQUISITION)//停止采样任务
                {
                    iRet = HexHelper.ReceivingMessage_ForStopAcquisition();
                    if (iRet == RET.SUCCEEDED)
                    {
                        //还原任务管理线程周期
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;
                    }
                }
                #endregion

                #region 故障数据采集
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FaultFunctionCode.ACQUISITION && HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(12, 2) == FaultAcquisitionExecutedCode.ACQUISITION)//故障数据采集响应正常
                {
                    //若连续采样停止
                    iRet = HexHelper.StopFaultContinuousAcquisition();
                    if (iRet == RET.SUCCEEDED)
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllFaultAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }

                    //故障数据采集分析
                    iRet = HexHelper.ReceivingMessage_ForFaultAcquisition();
                    if (iRet == RET.SUCCEEDED)
                    {
                        //信息提示
                        FaultAcquisitionInfoSet.CurrentProcess = TaskName.FaultAskingAcquisitionState;
                        evtEvaluationSerialPortWorkProcess(FaultAcquisitionInfoSet.CurrentProcess);
                    }
                    else
                    {
                        //清空所有故障数据采样任务-用于数据保护
                        //OthersHelper.ClearFaultAcquisitionInfoSet();
                        OthersHelper.ClearAllFaultAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //信息提示
                        evtShowNotification(3029);

                        //按钮使能
                        evtFaultDataOscilliscopeButtonEnabled();

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }
                }
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FaultFunctionCode.ACQUISITION && HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(12, 2) == FaultAcquisitionExecutedCode.ASK_ACQUISITION)//故障数据采集状态询问正常
                {
                    //若连续采样停止
                    iRet = HexHelper.StopFaultContinuousAcquisition();
                    if (iRet == RET.SUCCEEDED)
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllFaultAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }

                    //故障数据采集分析
                    iRet = HexHelper.ReceivingMessage_ForFaultAskingAcquisitionState();
                    //if (iRet == 100)//正在采集
                    //{
                    //    //更新采样工作进展
                    //    FaultAcquisitionInfoSet.CurrentProcess = TaskName.FaultAcquiring;
                    //    evtEvaluationSerialPortWorkProcess(FaultAcquisitionInfoSet.CurrentProcess);
                    //}
                    if (iRet == 101)//采集完毕
                    {
                        //更新采样工作进展                       
                        FaultAcquisitionInfoSet.CurrentProcess = TaskName.UploadingAcquisitionFault;
                        evtEvaluationSerialPortWorkProcess(FaultAcquisitionInfoSet.CurrentProcess);

                        //提高数据上传速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskFast;
                    }
                    else if (iRet == 102)//未收到采样指令
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllFaultAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //信息提示
                        ViewModelSet.Main?.ShowHintInfo("无故障数据...");

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }
                    else if (iRet == 103)//接收数据帧错误
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllFaultAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //信息提示
                        evtShowNotification(3030);

                        //按钮使能
                        evtFaultDataOscilliscopeButtonEnabled();

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }
                }
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FaultFunctionCode.ACQUISITION && (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(12, 2) == FaultAcquisitionExecutedCode.UPLOAD_ACQUISITION || HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(12, 2) == FaultAcquisitionExecutedCode.RETRANSMISSION_ACQUISITION))//故障数据上传和重传正常
                {
                    //若连续采样停止
                    iRet = HexHelper.StopFaultContinuousAcquisition();
                    if (iRet == RET.SUCCEEDED)
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllFaultAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //还原任务管理线程速度
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        return RET.SUCCEEDED;
                    }

                    //故障数据采集数据分析
                    iRet = HexHelper.ReceivingMessage_ForUploadingFault();
                    if (iRet == 100)//结束上传
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearFaultAcquisitionInfoSet();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //还原任务管理线程周期
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        if (FaultAcquisitionInfoSet.AcquisitionSwitch)
                        {                           
                            //不连续静态展示
                            if (!FaultAcquisitionInfoSet.IsContinuous)
                            {
                                //画图
                                ViewModelSet.FaultDataOscilloscopeView?.DisplayOscilloscope();

                                //连续采样累计
                                FaultAcquisitionInfoSet.ContinuousAcquisitionTimes = 0;

                                //按钮使能
                                evtFaultDataOscilliscopeButtonEnabled();
                            }
                        }
                        else
                        {
                            //按钮使能
                            evtFaultDataOscilliscopeButtonEnabled();
                        }
                    }
                    else if (iRet == 101)//接续上传
                    {
                        //若连续采样停止
                        iRet = HexHelper.StopFaultContinuousAcquisition();
                        if (iRet == RET.SUCCEEDED)
                        {
                            //清空所有采样任务-用于数据保护
                            OthersHelper.ClearAllFaultAcquisitionTask();

                            //更新采样工作进展                                              
                            evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                            //还原任务管理线程周期
                            PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                            return RET.SUCCEEDED;
                        }
                        else
                        {
                            SoftwareStateParameterSet.IsFaultUpload = true;
                            //信息提示
                            evtEvaluationSerialPortWorkProcess(FaultAcquisitionInfoSet.CurrentProcess + "-第" + FaultAcquisitionInfoSet.CurrentMessageNumber + "帧");

                            if (FaultAcquisitionInfoSet.CurrentMessageNumber >= 30)
                            {
                                ViewModelSet.Main?.ShowHintInfo("请耐心等待故障数据采集，勿反复下发任务");
                            }
                        }
                    }
                    else if (iRet == RET.ERROR)
                    {
                        //清空所有采样任务-用于数据保护
                        OthersHelper.ClearAllFaultAcquisitionTask();

                        //更新采样工作进展                                              
                        evtEvaluationSerialPortWorkProcess(TaskName.Empty);

                        //按钮使能
                        evtFaultDataOscilliscopeButtonEnabled();

                        //还原任务管理线程周期
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                        //信息提示
                        evtShowNotification(3031);
                    }
                }
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FaultFunctionCode.ACQUISITION && (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(12, 2) == FaultAcquisitionExecutedCode.CLEAR_ACQUISITION || HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(12, 2) == FaultAcquisitionExecutedCode.STOP_ACQUISITION))//故障数据停止采样任务
                {
                    iRet = HexHelper.ReceivingMessage_ForStopFaultAcquisition();
                    if (iRet == RET.SUCCEEDED)
                    {
                        //还原任务管理线程周期
                        PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;
                    }
                }
                #endregion

                #region 硬件报警
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FunctionCode.HARDWAREALARM)//获取硬件报警信息
                {
                    iRet = HexHelper.ReceivingMessage_ForHardwareAlarm();
                    if (iRet == RET.SUCCEEDED)
                    {
                        if (CommunicationSet.HardwareAlarmValue.Count == 0)
                        {
                            ViewModelSet.Main?.ShowHintInfo("当前无报警记录");
                        }

                        ViewModelSet.HardwareAlarmHistory?.EvaluationHardwareAlarmHistory();
                    }
                    else
                    {
                        evtShowNotification(3006);
                    }
                }
                #endregion

                #region 回传测试-异常
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FunctionCode.ERROR_TEST_COMMUNICATION)//回传测试响应异常
                {
                    evtShowNotification(3001);
                }
                #endregion

                #region 参数读取-异常
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FunctionCode.ERROR_PARAMETER_READ)//参数读取响应异常
                {
                    string strMistakeInfo = null;
                    iRet = HexHelper.ReceivingMessage_ForParameterMistake(ref strMistakeInfo);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ViewModelSet.Main?.ShowHintInfo("参数读错误，" + strMistakeInfo);
                    }                               
                }
                #endregion

                #region 参数写入-异常
                
                else if (HexHelper.BytesToHexString(CommunicationSet.Receiving).Substring(8, 2) == FunctionCode.ERROR_PARAMETER_WRITE)//参数写入响应异常
                {
                    string strMistakeInfo = null;
                      
                    //信息提示           
                    iRet = HexHelper.ReceivingMessage_ForParameterMistake(ref strMistakeInfo);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ViewModelSet.Main?.ShowHintInfo("参数写错误，" + strMistakeInfo);                     
                    } 
                    else if (iRet == RET.NO_EFFECT)
                    {
                        ViewModelSet.Main?.ShowHintInfo("伺服状态错误，" + strMistakeInfo);
                        ShowNotification_CrossThread(3014);
                    }

                    //异常处理
                    //if (CommunicationSet.TaskName == TaskName.BatchWrite || CommunicationSet.TaskName == TaskName.BatchRead)//批量读写
                    //{
                    //    SoftwareStateParameterSet.lstTransmittingDataInfo.Clear();
                    //}
                    if (CommunicationSet.TaskName == TaskName.BatchWrite || CommunicationSet.TaskName == TaskName.BatchRead || CommunicationSet.TaskName == TaskName.TorqueAction || CommunicationSet.TaskName == TaskName.SpeedAction || CommunicationSet.TaskName == TaskName.PositionAction || CommunicationSet.TaskName == TaskName.StopSeekZero || CommunicationSet.TaskName == TaskName.AllFaultReset || CommunicationSet.TaskName == TaskName.FaultReset || CommunicationSet.TaskName == TaskName.AllAxisEnabled || CommunicationSet.TaskName == TaskName.ServoDisabled || CommunicationSet.TaskName == TaskName.Emergency || CommunicationSet.TaskName == TaskName.FunctionGenerator || CommunicationSet.TaskName == TaskName.StopPositionAction)//批量读写   由Lilbert于20230728添加示波器运动模式下关闭写入控制字Task
                    {
                        ControlWordSet.WriteSwitch = false; //关闭写入控制字
                        SoftwareStateParameterSet.lstTransmittingDataInfo.Clear();
                    }
                }
                #endregion

                #region 接收数据帧错误
                else
                {
                    ViewModelSet.Main?.ShowHintInfo("报文错误，通信时丢失数据");
                }
                #endregion

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("AnalyseReceivingMessage", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AnalyseReceivingMessage_ForFirmwareUpdate
        //函数功能：分析接收数据帧
        //
        //输入参数：NONE
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.04.27&2022.04.22
        //*************************************************************************
        private int AnalyseReceivingMessage_ForFirmwareUpdate(byte[] bReceivingData, string strInfo)
        {            
            byte[] bData = null;

            try
            {
                #region 任务下发
                if (FirmwareUpdateSet.Process == FirmwareUpdateProcess.ASSIGNMENT)
                {
                    //清空升级任务
                    OthersHelper.ClearFirmwareUpdateTask();
                   
                    if (strInfo == "C")
                    {
                        //信息提示
                        ShowNotification("C-固件升级任务下达成功");

                        //次数累积清零
                        FirmwareUpdateSet.ReceivingTimes = 0;

                        //更新升级状态
                        FirmwareUpdateSet.Process = FirmwareUpdateProcess.INITIAL;

                        //发送起始帧
                        bData = YModemHelper.GetInitialPacket();
                        if (bData != null)
                        {
                            //写入起始帧信息
                            CommunicationSet.SerialPortInfo.Write(bData, 0, bData.Length);
                            return RET.NO_EFFECT; //不跳转到Finally
                        }
                        else
                        {
                            return RET.ERROR;//跳转到Finally
                        }                                         
                    }                    
                    else
                    {
                        //信息提示
                        ShowNotification("WAIT-固件升级任务下达，等待硬件反馈");

                        //次数累积
                        FirmwareUpdateSet.ReceivingTimes++;
                        if (FirmwareUpdateSet.ReceivingTimes < 5)
                        {
                            return RET.NO_EFFECT;
                        }
                        else
                        {
                            return RET.ERROR;
                        }
                    }
                }
                #endregion

                #region 起始帧
                else if (FirmwareUpdateSet.Process == FirmwareUpdateProcess.INITIAL)
                {                  
                    if (strInfo == "ACK")
                    {
                        //信息提示
                        ShowNotification("ACK-文件解析成功，数据总长：" + FirmwareUpdateSet.FileContent.Length/1024 + "KB，固件升级开始");//由Lilbert更改“固件升级”时显示单位问题

                        //次数累积清零
                        FirmwareUpdateSet.ReceivingTimes = 0;

                        //更新升级状态
                        FirmwareUpdateSet.Process = FirmwareUpdateProcess.DATA;

                        //获取数据帧
                        FirmwareUpdateSet.Offset = 0;
                        FirmwareUpdateSet.PackageNumber = 1;
                        FirmwareUpdateSet.DataSize = 0;
                        bData = YModemHelper.GetDataPacket(FirmwareUpdateSet.FileContent, FirmwareUpdateSet.Offset, FirmwareUpdateSet.PackageNumber, ref FirmwareUpdateSet.DataSize);
                        if (bData != null)
                        {
                            //写入数据帧信息
                            CommunicationSet.SerialPortInfo.Write(bData, 0, bData.Length);
                            return RET.NO_EFFECT;
                        }
                        else
                        {
                            return RET.ERROR;
                        }                       
                    }              
                    else 
                    {
                        //信息提示
                        ShowNotification("WAIT-文件解析，等待硬件反馈");
                        ShowNotification(HexHelper.BytesToHexString(bReceivingData));
                        foreach (var item in bReceivingData)
                        {
                            ShowNotification(item.ToString());
                        }

                        //次数累积
                        FirmwareUpdateSet.ReceivingTimes++;
                        if (FirmwareUpdateSet.ReceivingTimes < 5)
                        {
                            return RET.NO_EFFECT;
                        }
                        else
                        {
                            return RET.ERROR;
                        }
                    }
                }
                #endregion

                #region 数据帧              
                else if (FirmwareUpdateSet.Process == FirmwareUpdateProcess.DATA)
                {
                    if (strInfo == "ACK")//ACK
                    {
                        //更新固件升级状态
                        FirmwareUpdateSet.Offset += FirmwareUpdateSet.DataSize;
                        FirmwareUpdateSet.PackageNumber++;
                        FirmwareUpdateSet.SendingAgainTimes = 0;

                        //数据没有发送完成
                        if (FirmwareUpdateSet.Offset < FirmwareUpdateSet.FileContent.Length)
                        {
                            //获取数据帧
                            bData = YModemHelper.GetDataPacket(FirmwareUpdateSet.FileContent, FirmwareUpdateSet.Offset, FirmwareUpdateSet.PackageNumber, ref FirmwareUpdateSet.DataSize);
                            if (bData == null)
                            {
                                return RET.ERROR;
                            }

                            //写入数据帧信息
                            CommunicationSet.SerialPortInfo.Write(bData, 0, bData.Length);

                            //信息提示   
                            double dProgress = Convert.ToDouble(Math.Round(((double)FirmwareUpdateSet.Offset / (double)FirmwareUpdateSet.FileContent.Length), 3) * 100);
                            ProgressValue(dProgress);

                            //减少信息提示，降低stringbuilder拼接字符串数量
                            if (FirmwareUpdateSet.PackageNumber % 20 == 0)
                            {                               
                                ShowNotification("ACK-数据已下载：" + FirmwareUpdateSet.Offset/1024 + "KB"); //由Lilbert更改“固件升级”时显示单位问题                         
                            }                           
                        }
                        else//数据发送完成
                        {
                            //信息提示
                            ShowNotification("ACK-数据下载完成，等待升级");

                            //更新升级状态
                            FirmwareUpdateSet.Process = FirmwareUpdateProcess.EOT;

                            //写入结束帧信息
                            bData = new byte[] { YModemHelper.EOT };
                            CommunicationSet.SerialPortInfo.Write(bData, 0, bData.Length);

                            //进度条
                            ProgressValue(100);
                        }

                        return RET.NO_EFFECT;
                    }
                    else//NAK 或 CA 或 Invalid
                    {                     
                        //失败可以重新发送2次
                        if (FirmwareUpdateSet.SendingAgainTimes <= 2)
                        {
                            //更新固件升级状态
                            FirmwareUpdateSet.SendingAgainTimes++;

                            //数据没有发送完成
                            if (FirmwareUpdateSet.Offset < FirmwareUpdateSet.FileContent.Length)
                            {
                                //获取数据帧
                                bData = YModemHelper.GetDataPacket(FirmwareUpdateSet.FileContent, FirmwareUpdateSet.Offset, FirmwareUpdateSet.PackageNumber, ref FirmwareUpdateSet.DataSize);
                                if (bData == null)
                                {
                                    return RET.ERROR;
                                }

                                //写入数据帧信息
                                CommunicationSet.SerialPortInfo.Write(bData, 0, bData.Length);

                                //信息提示   
                                double dProgress = Convert.ToDouble(Math.Round(((double)FirmwareUpdateSet.Offset / (double)FirmwareUpdateSet.FileContent.Length), 3) * 100);
                                ProgressValue(dProgress);

                                //信息提示
                                ShowNotification("ACK[Send Again]-数据已下载：" + FirmwareUpdateSet.Offset/1024 + "KB");//由Lilbert更改“固件升级”时显示单位问题
                            }
                          
                            return RET.NO_EFFECT;
                        }
                        else
                        {
                            //信息提示
                            if (strInfo == "CA")
                            {
                                ShowNotification("CA-数据下载错误,停止升级");
                            }
                            else if (strInfo == "NAK")
                            {
                                ShowNotification("NAK-数据下载错误,停止升级");
                            }
                            else
                            {
                                ShowNotification("INVALID-串口状态不稳定,停止升级");
                            }

                            return RET.ERROR;
                        }                
                    }                  
                }
                #endregion

                #region 结束帧
                else if (FirmwareUpdateSet.Process == FirmwareUpdateProcess.EOT)
                {                  
                    if (strInfo == "ACK")
                    {
                        //信息提示
                        ShowNotification("ACK-即将升级完成");

                        //更新升级状态
                        FirmwareUpdateSet.Process = FirmwareUpdateProcess.EMPTY;

                        //写入结束帧信息
                        bData = YModemHelper.GetEmptyPacket();
                        CommunicationSet.SerialPortInfo.Write(bData, 0, bData.Length);

                        return RET.NO_EFFECT;
                    }                 
                    else 
                    {
                        //信息提示
                        if (strInfo == "CA")
                        {
                            ShowNotification("CA-升级结束指令错误，停止升级");
                        }
                        else
                        {
                            ShowNotification("NAK-升级结束指令错误，停止升级");
                        }

                        return RET.ERROR;
                    }             
                }
                #endregion

                #region 空帧
                else if (FirmwareUpdateSet.Process == FirmwareUpdateProcess.EMPTY)
                {
                    //信息提示
                    //ShowNotification("ACK-从站" + FirmwareUpdateSet.ARM[FirmwareUpdateSet.ARMIndex] + "升级完成");
                    ShowNotification("ACK-从站" + GlobalCurrentInput.SelectedSlaveID + "升级完成");

                    //更新升级状态
                    FirmwareUpdateSet.Process = FirmwareUpdateProcess.SUCCEED;

                    return RET.SUCCEEDED;                                 
                }
                #endregion

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("AnalyseReceivingMessage_ForFirmwareUpdate", ex);
                return RET.ERROR;
            }
        }
   
        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        private void ShowNotification_CrossThread(int In_iType)
        {
            WindowSet.clsMainWindow?.ShowNotification(In_iType);
        }
        private void ShowNotification(string strData)
        {
            if (FirmwareUpdateSet.ProcessNotification == null)
            {
                FirmwareUpdateSet.ProcessNotification = new StringBuilder();
            }
      
            FirmwareUpdateSet.ProcessNotification.Append(strData + "\r\n");
            WindowSet.clsFirmwareUpdate?.ShowNotification(FirmwareUpdateSet.ProcessNotification.ToString());
        }

        //*************************************************************************
        //函数名称：ProgressValue
        //函数功能：进度条
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.29
        //*************************************************************************
        private void ProgressValue(double dValue)
        {
            WindowSet.clsFirmwareUpdate?.ProgressValue(dValue);
        }
        #endregion
    }

}