﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Collections.Generic;
using System.Data;
using ServoStudio.Models;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class SpeedLoopViewModel
    {
        #region 私有字段
        private static bool IsInitialized = true;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 服务
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 属性
        public virtual ObservableCollection<string> SpeedAverageFilterConfig { get; set; }//速度反馈平均滤波配置
        public virtual string SelectedSpeedAverageFilterConfigIndex { get; set; }//速度反馈平均滤波配置

        public virtual ObservableCollection<string> TrqffControlSelect { get; set; }//转矩前馈选择
        public virtual string SelectedTrqffControlSelectIndex { get; set; }//转矩前馈选择

        public virtual string SpeedLoopGain { get; set; }//速度环增益
        public virtual string SpeedLoopTimeConstant { get; set; }//速度环积分时间常数
        public virtual string LoadInertiaRatio { get; set; }//负载惯量比
       
        public virtual string TrqffFilterFimeConstant { get; set; }//转矩前馈滤波时间常数
        public virtual string TrqffGain { get; set; }//转矩前馈增益
       
        public virtual string SpeedObserverGain { get; set; }//速度观测增益
        public virtual string SpeedObserverPosCompensationGain { get; set; }//速度观测补偿增益

        public virtual string SpeedFeedbackLPFTime { get; set; }//速度反馈低通滤波时间参数
        public virtual string TorqueFeedForwardMaFilterTime { get; set; }//转矩前馈平均滤波时间
        //背景
        public virtual int SpeedLoopGainBackground { get; set; }
        public virtual int SpeedLoopTimeConstantBackground { get; set; }
        public virtual int LoadInertiaRatioBackground { get; set; }
        public virtual int TrqffControlSelectBackground { get; set; }
        public virtual int TrqffFilterFimeConstantBackground { get; set; }
        public virtual int TrqffGainBackground { get; set; }
        public virtual int SpeedAverageFilterConfigBackground { get; set; }
        public virtual int SpeedObserverGainBackground { get; set; }
        public virtual int SpeedObserverPosCompensationGainBackground { get; set; }
        public virtual int SpeedFeedbackLPFTimeBackground { get; set; }
        public virtual int TorqueFeedForwardMaFilterTimeBackground { get; set; }
        #endregion

        #region 构造函数
        public SpeedLoopViewModel()
        {
            ViewModelSet.SpeedLoop = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.SPEEDLOOP;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：SpeedLoopLoaded
        //函数功能：SpeedLoop界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void SpeedLoopLoaded()
        {
            int iRet = -1;

            try
            {
                //背景初始化
                BackgroundInitialize();

                //ComboBox初始化
                ComboBoxInitialize();

                if (IsInitialized == true)
                {
                    //更新标志位
                    IsInitialized = false;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadSpeedLoopParameter();
                    }
                    else
                    {
                        GetDefaultSpeedLoopParameter();
                    }
                }
                else
                {
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadSpeedLoopParameter();
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }                    
                }               
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SPEEDLOOP_LOADED, "SpeedLoopLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：WriteSpeedLoopParameter
        //函数功能：写电流环参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void WriteSpeedLoopParameter()
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary(ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.SPEEDLOOP, TaskName.SpeedLoop, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SPEEDLOOP_WRITE_PARAMETER, "WriteSpeedLoopParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadSpeedLoopParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void ReadSpeedLoopParameter()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.SPEEDLOOP, TaskName.SpeedLoop, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SPEEDLOOP_READ_PARAMETER, "ReadSpeedLoopParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultSpeedLoopParameter
        //函数功能：获取限幅保护的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void GetDefaultSpeedLoopParameter()
        {
            try
            {
                SpeedLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Default");
                SpeedLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Default");
                LoadInertiaRatio = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Default");
                SelectedTrqffControlSelectIndex = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Trqff Control Select", "Default");
                TrqffFilterFimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Trqff Filter Fime Constant", "Default");
                TrqffGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Trqff Gain", "Default");
                SelectedSpeedAverageFilterConfigIndex = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Average Filter Config", "Default");
                SpeedObserverGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Gain", "Default");
                SpeedObserverPosCompensationGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Pos Compensation Gain", "Default");
                SpeedFeedbackLPFTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Feedback LPF Time", "Default");
                TorqueFeedForwardMaFilterTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Torque Feed Forward Ma Filter Time", "Default");
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SPEEDLOOP_GET_DEFAULT_PARAMETER, "GetDefaultSpeedLoopParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：SaveSpeedLoopConfigFile
        //函数功能：保存限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void SaveSpeedLoopConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                //选择配置文件存放位置
                iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.SpeedLoop);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //生成配置文件
                iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetSpeedLoopConfigToDataTable(), ExcelType.SpeedLoop);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2002);
                }
                else
                {
                    ShowNotification(2003);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SPEEDLOOP_SAVE_CONFIG_FILE, "SaveSpeedLoopConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetSpeedLoopConfigFile
        //函数功能：获取限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void GetSpeedLoopConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            DataTable dt = null;

            try
            {
                //获取配置文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(strFilePath, ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //判断是否导入正确的配置文件
                if (dt.Columns[3].ColumnName != FileInterface.SPEEDLOOP)
                {
                    ShowNotification(2001);
                    return;
                }

                //导入参数
                iRet = GetSpeedLoopConfigFromDataTable(dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //是否下载
                OthersHelper.GetWindowsStartupPosition();
                if (MessageBoxService.ShowMessage("是否下载当前导入的速度环参数...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    WriteSpeedLoopParameter();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SPEEDLOOP_GET_CONFIG_FILE, "GetSpeedLoopConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationSpeedLoopParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void EvaluationSpeedLoopParameter()
        {
            SpeedLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//速度环增益
            SpeedLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//速度环积分时间常数
            LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//负载惯量比
            SelectedTrqffControlSelectIndex = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Trqff Control Select", "Index"));//转矩前馈选择
            TrqffFilterFimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Trqff Filter Fime Constant", "Index"));//转矩前馈滤波时间常数
            TrqffGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Trqff Gain", "Index"));//转矩前馈增益
            SelectedSpeedAverageFilterConfigIndex = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Average Filter Config", "Index"));//速度反馈平均滤波配置
            SpeedObserverGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Gain", "Index"));//速度观测增益
            SpeedObserverPosCompensationGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Observer Pos Compensation Gain", "Index"));//速度观测补偿增益
            SpeedFeedbackLPFTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Feedback LPF Time", "Index"));//低通滤波器时间参数
            TorqueFeedForwardMaFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Torque Feed Forward Ma Filter Time", "Index"));//转矩前馈平均滤波时间
        }  

        //*************************************************************************
        //函数名称：ChangeBackground
        //函数功能：更换背景
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.16
        //*************************************************************************
        public void ChangeBackground(string strName)
        {
            if (strName == "速度环增益")
            {
                SpeedLoopGainBackground = BackgroundState.Selected;
                SpeedLoopTimeConstantBackground = BackgroundState.NotSelected;
                LoadInertiaRatioBackground = BackgroundState.NotSelected;
                TrqffControlSelectBackground = BackgroundState.NotSelected;
                TrqffFilterFimeConstantBackground = BackgroundState.NotSelected;
                TrqffGainBackground = BackgroundState.NotSelected;
                SpeedAverageFilterConfigBackground = BackgroundState.NotSelected;
                SpeedObserverGainBackground = BackgroundState.NotSelected;
                SpeedObserverPosCompensationGainBackground = BackgroundState.NotSelected;
                SpeedFeedbackLPFTimeBackground = BackgroundState.NotSelected;
                TorqueFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }
            else if (strName == "速度环积分")
            {
                SpeedLoopGainBackground = BackgroundState.NotSelected;
                SpeedLoopTimeConstantBackground = BackgroundState.Selected;
                LoadInertiaRatioBackground = BackgroundState.NotSelected;
                TrqffControlSelectBackground = BackgroundState.NotSelected;
                TrqffFilterFimeConstantBackground = BackgroundState.NotSelected;
                TrqffGainBackground = BackgroundState.NotSelected;
                SpeedAverageFilterConfigBackground = BackgroundState.NotSelected;
                SpeedObserverGainBackground = BackgroundState.NotSelected;
                SpeedObserverPosCompensationGainBackground = BackgroundState.NotSelected;
                SpeedFeedbackLPFTimeBackground = BackgroundState.NotSelected;
                TorqueFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }
            else if (strName == "负载惯量比")
            {
                SpeedLoopGainBackground = BackgroundState.NotSelected;
                SpeedLoopTimeConstantBackground = BackgroundState.NotSelected;
                LoadInertiaRatioBackground = BackgroundState.Selected;
                TrqffControlSelectBackground = BackgroundState.NotSelected;
                TrqffFilterFimeConstantBackground = BackgroundState.NotSelected;
                TrqffGainBackground = BackgroundState.NotSelected;
                SpeedAverageFilterConfigBackground = BackgroundState.NotSelected;
                SpeedObserverGainBackground = BackgroundState.NotSelected;
                SpeedObserverPosCompensationGainBackground = BackgroundState.NotSelected;
                SpeedFeedbackLPFTimeBackground = BackgroundState.NotSelected;
                TorqueFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }
            else if (strName == "前馈选择")
            {
                SpeedLoopGainBackground = BackgroundState.NotSelected;
                SpeedLoopTimeConstantBackground = BackgroundState.NotSelected;
                LoadInertiaRatioBackground = BackgroundState.NotSelected;
                TrqffControlSelectBackground = BackgroundState.Selected;
                TrqffFilterFimeConstantBackground = BackgroundState.NotSelected;
                TrqffGainBackground = BackgroundState.NotSelected;
                SpeedAverageFilterConfigBackground = BackgroundState.NotSelected;
                SpeedObserverGainBackground = BackgroundState.NotSelected;
                SpeedObserverPosCompensationGainBackground = BackgroundState.NotSelected;
                SpeedFeedbackLPFTimeBackground = BackgroundState.NotSelected;
                TorqueFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }
            else if (strName == "平滑常数")
            {
                SpeedLoopGainBackground = BackgroundState.NotSelected;
                SpeedLoopTimeConstantBackground = BackgroundState.NotSelected;
                LoadInertiaRatioBackground = BackgroundState.NotSelected;
                TrqffControlSelectBackground = BackgroundState.NotSelected;
                TrqffFilterFimeConstantBackground = BackgroundState.Selected;
                TrqffGainBackground = BackgroundState.NotSelected;
                SpeedAverageFilterConfigBackground = BackgroundState.NotSelected;
                SpeedObserverGainBackground = BackgroundState.NotSelected;
                SpeedObserverPosCompensationGainBackground = BackgroundState.NotSelected;
                SpeedFeedbackLPFTimeBackground = BackgroundState.NotSelected;
                TorqueFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }
            else if (strName == "前馈增益")
            {
                SpeedLoopGainBackground = BackgroundState.NotSelected;
                SpeedLoopTimeConstantBackground = BackgroundState.NotSelected;
                LoadInertiaRatioBackground = BackgroundState.NotSelected;
                TrqffControlSelectBackground = BackgroundState.NotSelected;
                TrqffFilterFimeConstantBackground = BackgroundState.NotSelected;
                TrqffGainBackground = BackgroundState.Selected;
                SpeedAverageFilterConfigBackground = BackgroundState.NotSelected;
                SpeedObserverGainBackground = BackgroundState.NotSelected;
                SpeedObserverPosCompensationGainBackground = BackgroundState.NotSelected;
                SpeedFeedbackLPFTimeBackground = BackgroundState.NotSelected;
                TorqueFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }
            else if (strName == "平均值滤波")
            {
                SpeedLoopGainBackground = BackgroundState.NotSelected;
                SpeedLoopTimeConstantBackground = BackgroundState.NotSelected;
                LoadInertiaRatioBackground = BackgroundState.NotSelected;
                TrqffControlSelectBackground = BackgroundState.NotSelected;
                TrqffFilterFimeConstantBackground = BackgroundState.NotSelected;
                TrqffGainBackground = BackgroundState.NotSelected;
                SpeedAverageFilterConfigBackground = BackgroundState.Selected;
                SpeedObserverGainBackground = BackgroundState.NotSelected;
                SpeedObserverPosCompensationGainBackground = BackgroundState.NotSelected;
                SpeedFeedbackLPFTimeBackground = BackgroundState.NotSelected;
                TorqueFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }
            else if (strName == "速度观测器")
            {
                SpeedLoopGainBackground = BackgroundState.NotSelected;
                SpeedLoopTimeConstantBackground = BackgroundState.NotSelected;
                LoadInertiaRatioBackground = BackgroundState.NotSelected;
                TrqffControlSelectBackground = BackgroundState.NotSelected;
                TrqffFilterFimeConstantBackground = BackgroundState.NotSelected;
                TrqffGainBackground = BackgroundState.NotSelected;
                SpeedAverageFilterConfigBackground = BackgroundState.NotSelected;
                SpeedObserverGainBackground = BackgroundState.Selected;
                SpeedObserverPosCompensationGainBackground = BackgroundState.Selected;
                SpeedFeedbackLPFTimeBackground = BackgroundState.NotSelected;
                TorqueFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }
            else if (strName == "低通滤波器")
            {
                SpeedLoopGainBackground = BackgroundState.NotSelected;
                SpeedLoopTimeConstantBackground = BackgroundState.NotSelected;
                LoadInertiaRatioBackground = BackgroundState.NotSelected;
                TrqffControlSelectBackground = BackgroundState.NotSelected;
                TrqffFilterFimeConstantBackground = BackgroundState.NotSelected;
                TrqffGainBackground = BackgroundState.NotSelected;
                SpeedAverageFilterConfigBackground = BackgroundState.NotSelected;
                SpeedObserverGainBackground = BackgroundState.NotSelected;
                SpeedObserverPosCompensationGainBackground = BackgroundState.NotSelected;
                SpeedFeedbackLPFTimeBackground = BackgroundState.Selected;
                TorqueFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
            }
            else if (strName == "前馈平均滤波")
            {
                SpeedLoopGainBackground = BackgroundState.NotSelected;
                SpeedLoopTimeConstantBackground = BackgroundState.NotSelected;
                LoadInertiaRatioBackground = BackgroundState.NotSelected;
                TrqffControlSelectBackground = BackgroundState.NotSelected;
                TrqffFilterFimeConstantBackground = BackgroundState.NotSelected;
                TrqffGainBackground = BackgroundState.NotSelected;
                SpeedAverageFilterConfigBackground = BackgroundState.NotSelected;
                SpeedObserverGainBackground = BackgroundState.NotSelected;
                SpeedObserverPosCompensationGainBackground = BackgroundState.NotSelected;
                SpeedFeedbackLPFTimeBackground = BackgroundState.NotSelected;
                TorqueFeedForwardMaFilterTimeBackground = BackgroundState.Selected;
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void OnSpeedLoopGainChanged() { GlobalCurrentInput.SpeedLoopGain_SpeedLoop = SpeedLoopGain; }//速度环增益
        public void OnSpeedLoopTimeConstantChanged() { GlobalCurrentInput.SpeedLoopTimeConstant_SpeedLoop = SpeedLoopTimeConstant; }//速度环积分时间常数
        public void OnLoadInertiaRatioChanged() { GlobalCurrentInput.LoadInertiaRatio = LoadInertiaRatio; }//负载惯量比
        public void OnSelectedTrqffControlSelectIndexChanged() { GlobalCurrentInput.SelectedTrqffControlSelectIndex = SelectedTrqffControlSelectIndex; }//转矩前馈选择
        public void OnTrqffFilterFimeConstantChanged() { GlobalCurrentInput.TrqffFilterFimeConstant = TrqffFilterFimeConstant; }//转矩前馈滤波时间常数
        public void OnTrqffGainChanged() { GlobalCurrentInput.TrqffGain = TrqffGain; }//转矩前馈增益
        public void OnSelectedSpeedAverageFilterConfigIndexChanged() { GlobalCurrentInput.SelectedSpeedAverageFilterConfigIndex = SelectedSpeedAverageFilterConfigIndex; }//速度反馈平均滤波配置
        public void OnSpeedObserverGainChanged() { GlobalCurrentInput.SpeedObserverGain = SpeedObserverGain; }//速度观测增益
        public void OnSpeedObserverPosCompensationGainChanged() { GlobalCurrentInput.SpeedObserverPosCompensationGain = SpeedObserverPosCompensationGain; }//速度观测补偿增益
        public void OnSpeedFeedbackLPFTimeChanged() { GlobalCurrentInput.SpeedFeedbackLPFTime = SpeedFeedbackLPFTime; }//低通滤波器时间参数
        public void OnTorqueFeedForwardMaFilterTimeChanged() { GlobalCurrentInput.TorqueFeedForwardMaFilterTime = TorqueFeedForwardMaFilterTime; }//转矩前馈平均滤波时间
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：BackgroundInitialize
        //函数功能：背景初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.16
        //*************************************************************************
        private void BackgroundInitialize()
        {
            SpeedLoopGainBackground = BackgroundState.NotSelected;
            SpeedLoopTimeConstantBackground = BackgroundState.NotSelected;
            LoadInertiaRatioBackground = BackgroundState.NotSelected;
            TrqffControlSelectBackground = BackgroundState.NotSelected;
            TrqffFilterFimeConstantBackground = BackgroundState.NotSelected;
            TrqffGainBackground = BackgroundState.NotSelected;
            SpeedAverageFilterConfigBackground = BackgroundState.NotSelected;
            SpeedObserverGainBackground = BackgroundState.NotSelected;
            SpeedObserverPosCompensationGainBackground = BackgroundState.NotSelected;
            SpeedFeedbackLPFTimeBackground = BackgroundState.NotSelected;
            TorqueFeedForwardMaFilterTimeBackground = BackgroundState.NotSelected;
        }

        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：下拉列表初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.16
        //*************************************************************************
        private void ComboBoxInitialize()
        {
            SpeedAverageFilterConfig = new ObservableCollection<string>() { "禁止速度反馈平均滤波", "2次平均", "4次平均", "8次平均", "16次平均" };
            TrqffControlSelect = new ObservableCollection<string>() { "无前馈", "内部前馈", "外部给定" };
        }

        //*************************************************************************
        //函数名称：GetSpeedLoopConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private DataTable GetSpeedLoopConfigToDataTable()
        {
            DataTable dt = new DataTable();

            try
            {
                OthersHelper.MakeValueToDataTable(FileInterface.SPEEDLOOP, "Speed Loop Gain", "速度环增益", SpeedLoopGain, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SPEEDLOOP, "Speed Loop Time Constant", "速度环积分时间常数", SpeedLoopTimeConstant, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SPEEDLOOP, "Load Inertia Ratio", "负载惯量比", LoadInertiaRatio, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SPEEDLOOP, "Trqff Control Select", "转矩前馈选择", SelectedTrqffControlSelectIndex, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SPEEDLOOP, "Trqff Filter Fime Constant", "转矩前馈滤波时间常数", TrqffFilterFimeConstant, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SPEEDLOOP, "Trqff Gain", "转矩前馈增益", TrqffGain, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SPEEDLOOP, "Speed Average Filter Config", "速度反馈平均滤波配置", SelectedSpeedAverageFilterConfigIndex, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SPEEDLOOP, "Speed Observer Gain", "速度观测增益", SpeedObserverGain, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SPEEDLOOP, "Speed Observer Pos Compensation Gain", "速度观测补偿增益", SpeedObserverPosCompensationGain, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SPEEDLOOP, "Speed Feedback LPF Time", "低通滤波器时间参数", SpeedFeedbackLPFTime, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SPEEDLOOP, "Torque Feed Forward Ma Filter Time", "转矩前馈平均滤波时间", TorqueFeedForwardMaFilterTime, ref dt);

                return dt;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SPEEDLOOP_GET_CONFIG_TO_DATATABLE, "GetSpeedLoopConfigToDataTable", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetSpeedLoopConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int GetSpeedLoopConfigFromDataTable(DataTable dt)
        {
            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                SpeedLoopGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Loop Gain", "Default");
                SpeedLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Loop Time Constant", "Default");
                LoadInertiaRatio = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Load Inertia Ratio", "Default");
                SelectedTrqffControlSelectIndex = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Trqff Control Select", "Default");
                TrqffFilterFimeConstant = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Trqff Filter Fime Constant", "Default");
                TrqffGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Trqff Gain", "Default");
                SelectedSpeedAverageFilterConfigIndex = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Average Filter Config", "Default");
                SpeedObserverGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Observer Gain", "Default");
                SpeedObserverPosCompensationGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Observer Pos Compensation Gain", "Default");
                SpeedFeedbackLPFTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Feedback LPF Time", "Default");
                TorqueFeedForwardMaFilterTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Torque Feed Forward Ma Filter Time", "Default");

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SPEEDLOOP_GET_CONFIG_FROM_DATATABLE, "GetSpeedLoopConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                SpeedLoopGain = GlobalCurrentInput.SpeedLoopGain_SpeedLoop;//速度环增益
                SpeedLoopTimeConstant = GlobalCurrentInput.SpeedLoopTimeConstant_SpeedLoop;//速度环积分时间常数
                LoadInertiaRatio = GlobalCurrentInput.LoadInertiaRatio;//负载惯量比
                SelectedTrqffControlSelectIndex = GlobalCurrentInput.SelectedTrqffControlSelectIndex;//转矩前馈选择
                TrqffFilterFimeConstant = GlobalCurrentInput.TrqffFilterFimeConstant;//转矩前馈滤波时间常数
                TrqffGain = GlobalCurrentInput.TrqffGain;//转矩前馈增益
                SelectedSpeedAverageFilterConfigIndex = GlobalCurrentInput.SelectedSpeedAverageFilterConfigIndex;//速度反馈平均滤波配置
                SpeedObserverGain = GlobalCurrentInput.SpeedObserverGain;//速度观测增益
                SpeedObserverPosCompensationGain = GlobalCurrentInput.SpeedObserverPosCompensationGain;//速度观测补偿增益
                SpeedFeedbackLPFTime = GlobalCurrentInput.SpeedFeedbackLPFTime;//低通滤波器时间参数
                TorqueFeedForwardMaFilterTime = GlobalCurrentInput.TorqueFeedForwardMaFilterTime;//转矩前馈平均滤波时间
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SPEEDLOOP_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int AddParameterInfoDictionary(ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                dicParameterInfo.Add("Speed Loop Gain", SpeedLoopGain);
                dicParameterInfo.Add("Speed Loop Time Constant", SpeedLoopTimeConstant);
                dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);
                dicParameterInfo.Add("Trqff Control Select", SelectedTrqffControlSelectIndex);
                dicParameterInfo.Add("Trqff Filter Fime Constant", TrqffFilterFimeConstant);
                dicParameterInfo.Add("Trqff Gain", TrqffGain);
                dicParameterInfo.Add("Speed Average Filter Config", SelectedSpeedAverageFilterConfigIndex);
                dicParameterInfo.Add("Speed Observer Gain", SpeedObserverGain);
                dicParameterInfo.Add("Speed Observer Pos Compensation Gain", SpeedObserverPosCompensationGain);
                dicParameterInfo.Add("Speed Feedback LPF Time", SpeedFeedbackLPFTime);
                dicParameterInfo.Add("Torque Feed Forward Ma Filter Time", TorqueFeedForwardMaFilterTime);

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SPEEDLOOP_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }
     
        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }
}