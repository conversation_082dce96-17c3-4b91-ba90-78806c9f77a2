<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.Xpf.Gauges.v16.2</name>
    </assembly>
    <members>
        <member name="T:DevExpress.Xpf.Gauges.Matrix5x8Presentation">

            <summary>
                <para>Contains presentation settings for a symbols panel of a matrix 5x8 type.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.FourteenSegmentsPresentation">

            <summary>
                <para>Contains presentation settings for a symbols panel of the fourteen segments type.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.ScaleLabelOptions">

            <summary>
                <para>Serves as a base for classes that contain appearance and behavior options for scale labels.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleLabelOptions.Addend">
            <summary>
                <para>Gets or sets a value that should be added to every label's value.  
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value which is added to every label's value on a scale.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleLabelOptions.AddendProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleLabelOptions.FormatString">
            <summary>
                <para>Gets or sets a value that specifies the format string for the display text on a scale. 
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that is the format string of a label. 

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleLabelOptions.FormatStringProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleLabelOptions.Multiplier">
            <summary>
                <para>Gets or sets a value by which every label's value should be multiplied.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value which is a multiplier applied to every label's value on a scale.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleLabelOptions.MultiplierProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleLabelOptions.Offset">
            <summary>
                <para>Gets or sets the offset specifying a label's position on a scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the label's offset.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleLabelOptions.OffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleLabelOptions.ShowFirst">
            <summary>
                <para>Gets or sets a value indicating whether or not the first label should be shown on a scale.
</para>
            </summary>
            <value><b>true</b> to display the first label on a scale; otherwise <b> false</b>.  
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleLabelOptions.ShowFirstProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleLabelOptions.ShowLast">
            <summary>
                <para>Gets or sets a value indicating whether or not the last label should be shown on a scale.


</para>
            </summary>
            <value><b>true</b> to display the last label on a scale; otherwise <b> false</b>.  

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleLabelOptions.ShowLastProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleLabelOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of scale labels.
</para>
            </summary>
            <value> An integer value that is the z-index.


</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleLabelOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ValueChangedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Gauges.ValueIndicatorBase.ValueChanged"/> event.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.ValueChangedEventArgs.NewValue">
            <summary>
                <para>Gets the new value of a property.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> that is the new value.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ValueChangedEventArgs.OldValue">
            <summary>
                <para>Gets the old value of a property.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> that is the old value.

</value>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleRangeBarCollection">

            <summary>
                <para>A collection that stores the range bars of a particular linear scale.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleRangeBarCollection.#ctor(DevExpress.Xpf.Gauges.LinearScale)">
            <summary>
                <para>Initializes a new instance of the LinearScaleRangeBarCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		A <see cref="T:DevExpress.Xpf.Gauges.LinearScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.PredefinedLinearScaleRangeBarPresentation">

            <summary>
                <para>Contains presentation settings for the linear scale range bar element.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.PredefinedLinearScaleRangeBarPresentation.ActualFill">
            <summary>
                <para>Gets the actual fill color of the linear scale range bar element.
</para>
            </summary>
            <value>A  <see cref="T:System.Windows.Media.Brush"/> object that is the actual fill color of the linear scale range bar.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.PredefinedLinearScaleRangeBarPresentation.Fill">
            <summary>
                <para>Specifies the fill color of the linear scale range bar element.
</para>
            </summary>
            <value>A  <see cref="T:System.Windows.Media.Brush"/> object that is fill color of the linear scale range bar.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.PredefinedLinearScaleRangeBarPresentation.FillProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleRangeBar">

            <summary>
                <para>A linear scale range bar.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleRangeBar.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearScaleRangeBar class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleRangeBar.AnchorValue">
            <summary>
                <para>Gets or sets the value on a scale that specifies the fixed edge of the range bar.

</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value on a scale.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleRangeBar.AnchorValueProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleRangeBar.Options">
            <summary>
                <para>Gets or sets the options of a range bar that specify its shape and position on a Linear scale.  
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.LinearScaleRangeBarOptions"/> object that contains the settings of the range bar.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleRangeBar.OptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleRangeBar.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for a linear scale range bar.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleRangeBar.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the range bar.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleRangeBarPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleRangeBar.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleMarkerOptions">

            <summary>
                <para>Contains layout options for a linear scale marker.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleMarkerOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearScaleMarkerOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleMarkerOptions.Orientation">
            <summary>
                <para>Provides different types of orientation for the marker on the Linear scale. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleMarkerOrientation"/> object that specifies possible ways the marker can be oriented.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleMarkerOptions.OrientationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleMarkerOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of a marker.
</para>
            </summary>
            <value> An integer value that is the z-index.


</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleMarkerOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.Matrix8x14Model">

            <summary>
                <para>For internal use.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.Matrix8x14Model.#ctor">
            <summary>
                <para>Initializes a new instance of the Matrix8x14Model class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Matrix8x14Model.Presentation">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Matrix8x14Model.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleNeedle">

            <summary>
                <para>An arc scale needle.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleNeedle.#ctor">
            <summary>
                <para>Initializes a new instance of the ArcScaleNeedle class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleNeedle.Options">
            <summary>
                <para>Gets or sets the options that allow you to customize the needle's  shape and position on a Circular scale.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleNeedleOptions"/> object that contain the settings for the needle.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleNeedle.OptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleNeedle.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for an arc scale needle.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleNeedle.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the needle.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleNeedlePresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleNeedle.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.RangeValue">

            <summary>
                <para>Stores one of the range values.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.RangeValue.#ctor(System.Double)">
            <summary>
                <para>Initializes a new instance of the RangeValue class with the specified value.
</para>
            </summary>
            <param name="value">
		A <see cref="T:System.Double"/> object that specifies one of the range values. This value is assigned to the <see cref="P:DevExpress.Xpf.Gauges.RangeValue.Value"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.RangeValue.#ctor(System.Double,DevExpress.Xpf.Gauges.RangeValueType)">
            <summary>
                <para>Initializes a new instance of the RangeValue class with the specified value and value type.
</para>
            </summary>
            <param name="value">
		A <see cref="T:System.Double"/> object that specifies one of the range values. This value is assigned to the <see cref="P:DevExpress.Xpf.Gauges.RangeValue.Value"/> property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Xpf.Gauges.RangeValueType"/> enumeration value that specifies how the assigned value should be treated - as percents or as an absolute value. This parameter is assigned to the <see cref="P:DevExpress.Xpf.Gauges.RangeValue.RangeValueType"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeValue.IsAbsolute">
            <summary>
                <para>Indicates whether or not a range boundary's value is set in absolute units.
</para>
            </summary>
            <value><b>true</b> if a range value is stored in absolute units; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeValue.IsPercent">
            <summary>
                <para>Indicates whether or not a range boundary's value is set as a percent.

</para>
            </summary>
            <value><b>true</b> if a range value is stored as a percent; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeValue.RangeValueType">
            <summary>
                <para>Returns a value specifying in which units to store range boundaries.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.RangeValueType"/> enumeration value that specifies the measurement unit for a range boundary.


</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeValue.Value">
            <summary>
                <para>Returns the value that specifies either the start or end boundary of a range.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is used to store the range boundaries in either absolute or relative units. 
</value>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.FourteenSegmentsModel">

            <summary>
                <para>For internal use.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.FourteenSegmentsModel.#ctor">
            <summary>
                <para>Initializes a new instance of the FourteenSegmentsModel class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.FourteenSegmentsModel.Presentation">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.FourteenSegmentsModel.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.StatePresentation">

            <summary>
                <para>Contains presentation settings for a state of a state indicator control. 
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.CreepingLineDirection">

            <summary>
                <para>Lists the possible directions of a creeping line that is shown on the symbols panel during the creeping line animation.  
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.CreepingLineDirection.LeftToRight">
            <summary>
                <para>A creeping line is shown on the symbols panel from left to right.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CreepingLineDirection.RightToLeft">
            <summary>
                <para>A creeping line is shown on the symbols panel from right to left.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleRangeBarOptions">

            <summary>
                <para>Contains appearance and layout options for an arc scale range bar.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleRangeBarOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the ArcScaleRangeBarOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleRangeBarOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of a range bar.
</para>
            </summary>
            <value>An integer value that is the z-index.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleRangeBarOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.Matrix8x14Presentation">

            <summary>
                <para>Contains presentation settings for a symbols panel of a matrix 8x14 type.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleMarkerPresentation">

            <summary>
                <para>Contains presentation settings for an arc scale marker.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.PredefinedDigitalGaugeLayerPresentation">

            <summary>
                <para>Contains presentation settings for the digital gauge layer element.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.PredefinedDigitalGaugeLayerPresentation.ActualFill">
            <summary>
                <para>Gets the actual fill color of the digital gauge layer element.
</para>
            </summary>
            <value>A  <see cref="T:System.Windows.Media.Brush"/> object that is the actual fill color of the digital gauge layer.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.PredefinedDigitalGaugeLayerPresentation.Fill">
            <summary>
                <para>Specifies the fill color of the digital gauge layer element.
</para>
            </summary>
            <value>A  <see cref="T:System.Windows.Media.Brush"/> object that is fill color of the digital gauge layer.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.PredefinedDigitalGaugeLayerPresentation.FillProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.PredefinedLinearScaleMarkerPresentation">

            <summary>
                <para>Contains presentation settings for the linear scale marker element.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.PredefinedLinearScaleMarkerPresentation.ActualFill">
            <summary>
                <para>Gets the actual fill color of the linear scale marker element.
</para>
            </summary>
            <value>A  <see cref="T:System.Windows.Media.Brush"/> object that is the actual fill color of the linear scale marker.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.PredefinedLinearScaleMarkerPresentation.Fill">
            <summary>
                <para>Specifies the fill color of the linear scale marker element.
</para>
            </summary>
            <value>A  <see cref="T:System.Windows.Media.Brush"/> object that is fill color of the linear scale marker.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.PredefinedLinearScaleMarkerPresentation.FillProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.Localization.GaugeLocalizer">

            <summary>
                <para>A base class that provides necessary functionality for custom localizers of the Gauge Controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.Localization.GaugeLocalizer.#ctor">
            <summary>
                <para>Initializes a new instance of the GaugeLocalizer class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.Localization.GaugeLocalizer.CreateDefaultLocalizer">
            <summary>
                <para>Returns a localizer object, which provides resources based on the thread's language and regional settings (culture).
</para>
            </summary>
            <returns>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object representing resources based on the thread's culture.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.Localization.GaugeLocalizer.CreateResXLocalizer">
            <summary>
                <para>Returns a localizer object, which provides resources based on the thread's language and regional settings (culture).
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object, which provides resources based on the thread's culture.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.Localization.GaugeLocalizer.GetString(DevExpress.Xpf.Gauges.Localization.GaugeStringId)">
            <summary>
                <para>Returns a localized string for the given string identifier.
</para>
            </summary>
            <param name="id">
		A <see cref="T:DevExpress.Xpf.Gauges.Localization.GaugeStringId"/> enumeration value identifying the string to localize.

            </param>
            <returns>A <see cref="T:System.String"/> corresponding to the specified identifier.
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SpindleCapOptions">

            <summary>
                <para>Contains layout options for a spindle cap.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.SpindleCapOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the SpindleCapOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SpindleCapOptions.FactorHeight">
            <summary>
                <para>Gets or sets a value by which the spindle cap's height should be multiplied. 
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the spindle cap's height multiplier.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SpindleCapOptions.FactorHeightProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SpindleCapOptions.FactorWidth">
            <summary>
                <para>Gets or sets a value by which the spindle cap's width should be multiplied. 
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the spindle cap's width multiplier.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SpindleCapOptions.FactorWidthProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SpindleCapOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of a spindle cap.
</para>
            </summary>
            <value> An integer value that is the z-index.


</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SpindleCapOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.RangeBase">

            <summary>
                <para>Serves as the base class for all ranges.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.RangeBase.#ctor">
            <summary>
                <para>Initializes a new instance of the RangeBase class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeBase.EndValue">
            <summary>
                <para>Gets or sets the end position of the range on a scale.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.RangeValue"/> object that allows to set the end position of the range in absolute or relative units.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeBase.EndValueAbsolute">
            <summary>
                <para>Gets a value that specifies the end range position on a scale in absolute units. 
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the end range position in absolute units. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.RangeBase.EndValueProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorEnter">
            <summary>
                <para>Occurs when any value indicator enters the current range.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorLeave">
            <summary>
                <para>Occurs when any value indicator leaves the current range.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeBase.IsHitTestVisible">
            <summary>
                <para>Gets or sets a value that defines whether or not a range can be returned as a hit-testing result.
</para>
            </summary>
            <value><b>true</b> in case the range can be shown as the result of hit testing; otherwise <b>false</b>.   
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.RangeBase.IsHitTestVisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeBase.Options">
            <summary>
                <para>Provides access to the settings that specify the shape and position of the current range.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.RangeOptions"/> object that contains the settings of the range.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.RangeBase.OptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeBase.StartValue">
            <summary>
                <para>Gets or sets the start position of the range on a scale.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.RangeValue"/> object that allows setting the start position of the range in either absolute or relative units. 

</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeBase.StartValueAbsolute">
            <summary>
                <para>Gets a value that specifies the start range position in absolute units.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the start range position in absolute units. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.RangeBase.StartValueProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ScaleCollection`1">

            <summary>
                <para>A base class for collections containing scales.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ScaleCollection`1.#ctor(DevExpress.Xpf.Gauges.AnalogGaugeControl)">
            <summary>
                <para>Initializes a new instance of the ScaleCollection&lt;T&gt; class with the specified owner.
</para>
            </summary>
            <param name="gauge">
		An <see cref="T:DevExpress.Xpf.Gauges.AnalogGaugeControl"/> class descendant that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ScaleLayerBase">

            <summary>
                <para>Serves as the base class for scale layers.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleLayerBase.Options">
            <summary>
                <para>Provides access to the settings that specify the shape and position of the current scale layer.



</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LayerOptions"/> object that contains the settings of the layer.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleLayerBase.OptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.CreepingLineAnimationCompletedEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Xpf.Gauges.CreepingLineAnimation.CreepingLineAnimationCompleted"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.CreepingLineAnimationCompletedEventHandler.Invoke(System.Object,System.EventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Xpf.Gauges.CreepingLineAnimation.CreepingLineAnimationCompleted"/> event.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:System.EventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.Matrix5x8Model">

            <summary>
                <para>For internal use.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.Matrix5x8Model.#ctor">
            <summary>
                <para>Initializes a new instance of the Matrix5x8Model class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Matrix5x8Model.Presentation">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Matrix5x8Model.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LayerOptions">

            <summary>
                <para>Contains layout options for a layer.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LayerOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the LayerOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LayerOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of a layer.
</para>
            </summary>
            <value> An integer value that is the z-index.


</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LayerOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SymbolsAnimation">

            <summary>
                <para>A base class for creeping line and blinking animation effects of a digital gauge control.

</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolsAnimation.RefreshTime">
            <summary>
                <para>Gets or sets the refresh time between two symbol animations.
</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> value that is the refresh time between two animations. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolsAnimation.RefreshTimeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ScaleCustomLabelCollection">

            <summary>
                <para>A collection that stores the custom labels of a particular scale.



</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ScaleCustomLabelCollection.#ctor(DevExpress.Xpf.Gauges.Scale)">
            <summary>
                <para>Initializes a new instance of the ScaleCustomLabelCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		A <see cref="T:DevExpress.Xpf.Gauges.Scale"/> class descendant that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.StateInfo">

            <summary>
                <para>For internal use.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.StateInfo.#ctor(DevExpress.Xpf.Gauges.Native.ILayoutCalculator,System.Int32,DevExpress.Xpf.Gauges.PresentationControl,DevExpress.Xpf.Gauges.PresentationBase)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="layoutCalculator">
		 

            </param>
            <param name="zIndex">
		 

            </param>
            <param name="presentationControl">
		 

            </param>
            <param name="presentation">
		 

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SevenSegmentsPresentation">

            <summary>
                <para>Contains presentation settings for a symbols panel of the seven segments type. 
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.SymbolSegmentsMapping">

            <summary>
                <para>Contains properties to define how a custom symbol should be displayed on a digital gauge control.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.SymbolSegmentsMapping.#ctor">
            <summary>
                <para>Initializes a new instance of the SymbolSegmentsMapping class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolSegmentsMapping.SegmentsStates">
            <summary>
                <para>Specifies appropriate segment states for a desired character.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.StatesMask"/> value that specifies segments states.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolSegmentsMapping.SegmentsStatesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolSegmentsMapping.Symbol">
            <summary>
                <para>Specifies a custom symbol that can be displayed on the symbols panel using symbol segments mapping.  
</para>
            </summary>
            <value>A <see cref="T:System.Char"/> value that specifies a custom symbol.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolSegmentsMapping.SymbolProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolSegmentsMapping.SymbolType">
            <summary>
                <para>Gets or sets a symbol type that is used for displaying a custom symbol on a digital gauge control. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SymbolType"/> enumeration value that specifies a symbol type for custom symbol mapping.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolSegmentsMapping.SymbolTypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LayerBase">

            <summary>
                <para>Serves as the base class for all layers.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LayerBase.#ctor">
            <summary>
                <para>Initializes a new instance of the LayerBase class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LayerBase.Visible">
            <summary>
                <para>Gets or sets whether the layer is visible.
</para>
            </summary>
            <value><b>true</b> if the layer is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LayerBase.VisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleLevelBarOptions">

            <summary>
                <para>Contains layout  options for a linear scale's level bar.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleLevelBarOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearScaleLevelBarOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleLevelBarOptions.FactorThickness">
            <summary>
                <para>Gets or sets a value by which the level bar's thickness should be multiplied.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the level bar's thickness multiplier.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLevelBarOptions.FactorThicknessProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleLevelBarOptions.Offset">
            <summary>
                <para>Gets or sets the offset specifying a level bar's position on a Linear scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the level bar's offset.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLevelBarOptions.OffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleLevelBarOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of a level bar.
</para>
            </summary>
            <value> An integer value that is the z-index.


</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLevelBarOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SegmentsView">

            <summary>
                <para>A base class for all segment view types of a digital gauge control.  

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.SegmentsView.#ctor">
            <summary>
                <para>Initializes a new instance of the SegmentsView class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SegmentsView.SymbolMapping">
            <summary>
                <para>For internal use. Provides the information about elements that are used in symbol mapping.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SymbolDictionary"/> object that stores the element that defines a symbol view of a digital gauge control.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SegmentsView.SymbolMappingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.MatrixView">

            <summary>
                <para>A base class for all matrix view types of a digital gauge control. 
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.State">

            <summary>
                <para>A state of a state indicator control. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.State.#ctor">
            <summary>
                <para>Initializes a new instance of the State class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.State.ElementInfo">
            <summary>
                <para>This property is hidden and intended for internal use only.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.StateInfo"/> object. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.State.ElementInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.State.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for a state.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.State.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the state indicator control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.StatePresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.State.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.StateCollection">

            <summary>
                <para>A collection that stores states of a particular state indicator control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.StateCollection.#ctor(DevExpress.Xpf.Gauges.StateIndicatorControl)">
            <summary>
                <para>Initializes a new instance of the StateCollection class with the specified owner.
</para>
            </summary>
            <param name="stateIndicator">
		A <see cref="T:DevExpress.Xpf.Gauges.StateIndicatorControl"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.StateIndicatorModel">

            <summary>
                <para>For internal use.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.StateIndicatorControl">

            <summary>
                <para>A <b>state indicator</b> control shipped with the DXGauges Suite.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.StateIndicatorControl.#ctor">
            <summary>
                <para>Initializes a new instance of the StateIndicatorControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.StateIndicatorControl.ActualModel">
            <summary>
                <para>Gets the actual model used to draw elements of a State Indicator.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.StateIndicatorControl"/> class descendant that is the actual model.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.StateIndicatorControl.ActualModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.StateIndicatorControl.AdditionalStates">
            <summary>
                <para>Provides access to the collection of state indicator additional states. 

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.StateCollection"/> object that is the collection of state indicator additional states.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.StateIndicatorControl.AdditionalStatesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.StateIndicatorControl.DefaultState">
            <summary>
                <para>Gets or sets the default state that specifies the <see cref="T:DevExpress.Xpf.Gauges.StateIndicatorControl"/> appearance when the state index is out of the predefined model states or additional states.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.State"/> object that specifies the default state of the State Indicator control.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.StateIndicatorControl.DefaultStateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.StateIndicatorControl.Model">
            <summary>
                <para>Gets or sets a model for the state indicator control that is used to draw its elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.StateIndicatorModel"/> class descendant that is the actual model.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.StateIndicatorControl.ModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.StateIndicatorControl.PredefinedModels">
            <summary>
                <para>Returns a list of predefined models for a State Indicator control. 
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.StateIndicatorControl.State">
            <summary>
                <para>Provides access to the current state of the <see cref="T:DevExpress.Xpf.Gauges.StateIndicatorControl"/>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.State"/> object containing the state image.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.StateIndicatorControl.StateCount">
            <summary>
                <para>Returns the total number of all states (both predefined and additional) that are currently available in the State Indicator control.

</para>
            </summary>
            <value>An integer value that is the number of the state indicator's states.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.StateIndicatorControl.StateCountProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.StateIndicatorControl.StateIndex">
            <summary>
                <para>Gets or sets the index of a state image that is currently displayed on the <see cref="T:DevExpress.Xpf.Gauges.StateIndicatorControl"/>.
</para>
            </summary>
            <value>An integer value that is the current state index.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.StateIndicatorControl.StateIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.StateIndicatorControl.StateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.MatrixView8x14">

            <summary>
                <para>A matrix8x14 symbols panel type of a digital gauge control.   

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.MatrixView8x14.#ctor">
            <summary>
                <para>Initializes a new instance of the MatrixView8x14 class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.MatrixView8x14.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the 8x14 matrix.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.Matrix8x14Presentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.MatrixView8x14.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.MatrixView5x8">

            <summary>
                <para>A matrix5x8 symbols panel type of digital gauge control.   

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.MatrixView5x8.#ctor">
            <summary>
                <para>Initializes a new instance of the MatrixView5x8 class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.MatrixView5x8.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the 5x8 matrix.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.Matrix5x8Presentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.MatrixView5x8.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SevenSegmentsView">

            <summary>
                <para>A seven segments symbols panel type of digital gauge control. 

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.SevenSegmentsView.#ctor">
            <summary>
                <para>Initializes a new instance of the SevenSegmentsView class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SevenSegmentsView.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the seven segment view type.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SevenSegmentsPresentation"/> object
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SevenSegmentsView.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.FourteenSegmentsView">

            <summary>
                <para>A fourteen segments symbols panel type of digital gauge control.   

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.FourteenSegmentsView.#ctor">
            <summary>
                <para>Initializes a new instance of the FourteenSegmentsView class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.FourteenSegmentsView.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the fourteen segment view type.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.FourteenSegmentsPresentation"/> object
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.FourteenSegmentsView.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.IndicatorLeaveEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorLeave"/> event.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.IndicatorLeaveEventHandler.Invoke(System.Object,DevExpress.Xpf.Gauges.IndicatorLeaveEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorLeave"/> event.

</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.Xpf.Gauges.IndicatorLeaveEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ScaleCustomElementCollection">

            <summary>
                <para>A collection that stores custom elements of a particular scale.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ScaleCustomElementCollection.#ctor(DevExpress.Xpf.Gauges.Scale)">
            <summary>
                <para>Initializes a new instance of the ScaleCustomElementCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		A <see cref="T:DevExpress.Xpf.Gauges.Scale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ScaleCustomElement">

            <summary>
                <para>A custom element on a scale.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ScaleCustomElement.#ctor">
            <summary>
                <para>Initializes a new instance of the ScaleCustomElement class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleCustomElement.Content">
            <summary>
                <para>Gets or sets the scale custom element's content. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value that is the custom element's content.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleCustomElement.ContentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleCustomElement.ContentTemplate">
            <summary>
                <para>Gets or sets the template  that defines the presentation of the custom element's content represented by the <see cref="P:DevExpress.Xpf.Gauges.ScaleCustomElement.Content"/>  property. This is a dependency property. 

</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object, representing the template which defines the presentation of the custom element's content.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleCustomElement.ContentTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleCustomElement.Visible">
            <summary>
                <para>Gets or sets whether the scale custom element is visible.
</para>
            </summary>
            <value><b>true</b> if the custom element is visible on the scale; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleCustomElement.VisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleCustomElement.ZIndex">
            <summary>
                <para>Gets or sets the z-index of a scale custom element.
</para>
            </summary>
            <value>An integer value that is the z-index.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleCustomElement.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SymbolOptions">

            <summary>
                <para>Contains options that define the layout of symbols inside the symbols panel.  
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.SymbolOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the SymbolOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolOptions.Margin">
            <summary>
                <para>Gets or sets the margin of a digital gauge symbol.  
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Thickness"/> value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolOptions.MarginProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolOptions.SkewAngleX">
            <summary>
                <para>Gets or sets a skew angle of a symbol  along the X axis. 
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is a symbol skew angle along the X-axis.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolOptions.SkewAngleXProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolOptions.SkewAngleY">
            <summary>
                <para>Gets or sets a skew angle of a symbol  along the Y-axis. 
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the symbol skew angle along the Y-axis.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolOptions.SkewAngleYProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.DigitalGaugeModel">

            <summary>
                <para>For internal use.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeModel.FourteenSegmentsModel">
            <summary>
                <para>For internal use. Gets or sets a model for the fourteen segments view type of the digital gauge  control that is used to draw its elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.FourteenSegmentsModel"/> class descendant that is the actual model.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeModel.FourteenSegmentsModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeModel.LayerModels">
            <summary>
                <para>For internal use. Provides access to a collection of layer models contained in the current Digital Gauge control.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LayerModelCollection"/> object that contains layer models of a digital gauge.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeModel.LayerModelsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeModel.Matrix5x8Model">
            <summary>
                <para>For internal use. Gets or sets a model for the matrix5x8 view type of the digital gauge control that is used to draw its elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.Matrix5x8Model"/> class descendant that is the actual model.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeModel.Matrix5x8ModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeModel.Matrix8x14Model">
            <summary>
                <para>For internal use. Gets or sets a model for the matrix8x14 view type of the digital gauge control that is used to draw its elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.Matrix8x14Model"/> class descendant that is the actual model.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeModel.Matrix8x14ModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeModel.SevenSegmentsModel">
            <summary>
                <para>For internal use. Gets or sets a model for the seven segments view type of the digital gauge  control that is used to draw its elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SevenSegmentsModel"/> class descendant that is the actual model.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeModel.SevenSegmentsModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.DigitalGaugeLayerPresentation">

            <summary>
                <para>Contains presentation settings for a digital gauge layer.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.AnalogGaugeControl">

            <summary>
                <para>A base class for all analog gauges shipped in the DXGauge Suite. 

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.AnalogGaugeControl.#ctor">
            <summary>
                <para>Initializes a new instance of the AnalogGaugeControl class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.AnalogGaugeControl.GetValueIndicator(DevExpress.Xpf.Gauges.StateIndicatorControl)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Gauges.AnalogGaugeControl.ValueIndicator"/> attached property for the specified  <see cref="T:DevExpress.Xpf.Gauges.StateIndicatorControl"/>.
</para>
            </summary>
            <param name="stateControl">
		A <see cref="T:DevExpress.Xpf.Gauges.StateIndicatorControl"/> object whose <see cref="P:DevExpress.Xpf.Gauges.AnalogGaugeControl.ValueIndicator"/> property's value is to be returned.

            </param>
            <returns>The value of the <see cref="P:DevExpress.Xpf.Gauges.AnalogGaugeControl.ValueIndicator"/> property for the specified state indicator.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.AnalogGaugeControl.ScalePanelTemplate">
            <summary>
                <para>Gets or sets a panel template that specifies how to arrange scales within a Gauge control. 
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.ItemsPanelTemplate"/>  object that is a panel template. 


</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.AnalogGaugeControl.ScalePanelTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.AnalogGaugeControl.SetValueIndicator(DevExpress.Xpf.Gauges.StateIndicatorControl,DevExpress.Xpf.Gauges.ValueIndicatorBase)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Gauges.AnalogGaugeControl.ValueIndicator"/> attached property for the specified <see cref="T:DevExpress.Xpf.Gauges.StateIndicatorControl"/>.
</para>
            </summary>
            <param name="stateControl">
		The state indicator control from which the property value is read.

            </param>
            <param name="value">
		The required <see cref="T:DevExpress.Xpf.Gauges.ValueIndicatorBase"/> class descendant. 

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.AnalogGaugeControl.ValueIndicator(DevExpress.Xpf.Gauges.StateIndicatorControl)">
            <summary>
                <para>Gets or sets a value that can be bound to any value indicator of the analog gauge control (needle, range bar, etc.). This is an attached property. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.ValueIndicatorBase"/> class descendant that specifies an indicator value to which a state indicator can be bound.   
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.AnalogGaugeControl.ValueIndicatorProperty">
            <summary>
                <para>Identifies the  attached property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SevenSegmentsModel">

            <summary>
                <para>For internal use.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.SevenSegmentsModel.#ctor">
            <summary>
                <para>Initializes a new instance of the SevenSegmentsModel class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SevenSegmentsModel.Presentation">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <value>
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SevenSegmentsModel.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.Scale">

            <summary>
                <para>Serves as the base class for all scales.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.Scale.#ctor">
            <summary>
                <para>Initializes a new instance of the Scale class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.CustomElements">
            <summary>
                <para>Provides access to a collection of custom elements  contained in the current Circular Scale or Linear Scale.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.ScaleCustomElementCollection"/> object that contains scale custom elements.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.CustomElementsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.CustomLabels">
            <summary>
                <para>Provides access to a collection of custom labels contained in the current scale.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.ScaleCustomLabelCollection"/> object that contains scale custom labels.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.CustomLabelsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.EndValue">
            <summary>
                <para>Gets or sets the end value of the scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value which is the end of the scale. 

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.EndValueProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.LabelPresentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of labels.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.ScaleLabelPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.LabelPresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.LineOptions">
            <summary>
                <para>Provides access to the options that specify the shape and position of a scale line, either Circular or Linear.  


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.ScaleLineOptions"/> object that contains line options.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.LineOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.MajorIntervalCount">
            <summary>
                <para>Gets or sets a value that specifies the number of intervals between major tickmarks on a scale.  
</para>
            </summary>
            <value>A <see cref="T:System.Int32"/> value that is the number of intervals between major tickmarks' on a scale. 

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.MajorIntervalCountProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.MajorTickmarkOptions">
            <summary>
                <para>Provides access to the options that define the appearance, behavior and location of major tickmarks within the current scale.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.MajorTickmarkOptions"/> object that contains settings for major tickmarks.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.MajorTickmarkOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.MinorIntervalCount">
            <summary>
                <para>Gets or sets a value that specifies the number of intervals between minor tickmarks on a scale.  
</para>
            </summary>
            <value>A <see cref="T:System.Int32"/> value that is the number of intervals between minor tickmarks' on a scale. 

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.MinorIntervalCountProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.MinorTickmarkOptions">
            <summary>
                <para>Provides access to the options that define the appearance, behavior and location of minor tickmarks within the current scale.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.MinorTickmarkOptions"/> object that contains settings for minor tickmarks.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.MinorTickmarkOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.PredefinedLabelPresentations">
            <summary>
                <para>Returns a list of predefined presentations for labels.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.PredefinedTickmarksPresentations">
            <summary>
                <para>Contains the list of predefined presentations for scale tickmarks.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.ShowLabels">
            <summary>
                <para>Gets or sets a value indicating whether or not labels should be displayed on a scale.
</para>
            </summary>
            <value><b>true</b> to show labels on a scale; otherwise <b>false</b>.  
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.ShowLabelsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.ShowLine">
            <summary>
                <para>Indicates whether or not a line should be displayed on a scale.
</para>
            </summary>
            <value>A  <see cref="T:DevExpress.Utils.DefaultBoolean"/> enumeration value that specifies a line's visibility on a scale.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.ShowLineProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.ShowMajorTickmarks">
            <summary>
                <para>Gets or sets whether or not the major tickmarks should be visible on a scale.
</para>
            </summary>
            <value>A  <see cref="T:DevExpress.Utils.DefaultBoolean"/> enumeration value that specifies the visibility of major tickmarks on a scale.



</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.ShowMajorTickmarksProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.ShowMinorTickmarks">
            <summary>
                <para>Gets or sets whether the minor tickmarks should be visible on a scale or not.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> enumeration value that specifies the visibility of minor tickmarks on a scale.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.ShowMinorTickmarksProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.StartValue">
            <summary>
                <para>Gets or sets the start value of the scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value which is a scale start. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.StartValueProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.Scale.TickmarksPresentation">
            <summary>
                <para>Returns whether the current presentation of scale tickmarks is circular or linear.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.TickmarksPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.Scale.TickmarksPresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.AnimationBase">

            <summary>
                <para>A base class for all animation available in the DXGauge Suite.

</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.AnimationBase.Enable">
            <summary>
                <para>Gets or sets a value specifying whether the digital gauge control should be animated using creeping line or blinking animation. 
</para>
            </summary>
            <value><b>true</b> to enable animation; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.AnimationBase.EnableProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SymbolLengthType">

            <summary>
                <para>Contains the values used to specify the symbol length (either width or height) for the current symbol view appearance on a digital gauge control. 
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolLengthType.Auto">
            <summary>
                <para>A symbol has a predefined length (width and height) according to the view type of the current symbols panel. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolLengthType.Fixed">
            <summary>
                <para> A symbol length is specified in absolute values. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolLengthType.Proportional">
            <summary>
                <para>A symbol is zoomed proportionally to fill the entire symbols panel.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolLengthType.Stretch">
            <summary>
                <para>A symbol is stretched to fill the entire symbols panel. Note that the height to width proportion will not be preserved in this case.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SymbolLength">

            <summary>
                <para>Contains the values used to specify the length (width or height) of a symbol on the symbols panel. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.SymbolLength.#ctor(DevExpress.Xpf.Gauges.SymbolLengthType,System.Double)">
            <summary>
                <para>Initializes a new instance of the SymbolLength class with the specified symbol length type and symbol length value.
</para>
            </summary>
            <param name="type">
		A <see cref="T:DevExpress.Xpf.Gauges.SymbolLengthType"/> enumeration value that specifies the type of a symbol length. This value is assigned to the <see cref="P:DevExpress.Xpf.Gauges.SymbolLength.Type"/> property.


            </param>
            <param name="length">
		A <see cref="T:System.Double"/> value that specifies the symbol length. This value is assigned to either the <see cref="P:DevExpress.Xpf.Gauges.SymbolLength.FixedLength"/> or <see cref="P:DevExpress.Xpf.Gauges.SymbolLength.ProportionalLength"/> property, depending on the value passed as the <i>type</i> parameter.


            </param>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.SymbolLength.#ctor(DevExpress.Xpf.Gauges.SymbolLengthType)">
            <summary>
                <para>Initializes a new instance of the SymbolLength class with the specified owner.
</para>
            </summary>
            <param name="type">
		A <see cref="T:DevExpress.Xpf.Gauges.SymbolLengthType"/> enumeration value that specifies one of the possible symbol length types. This value is assigned to the <see cref="P:DevExpress.Xpf.Gauges.SymbolLength.Type"/> property.


            </param>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolLength.FixedLength">
            <summary>
                <para> Provides access to the width and height of symbols that are set in absolute values.

</para>
            </summary>
            <value> A double value that is the symbol length specified in absolute value. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolLength.ProportionalLength">
            <summary>
                <para>Provides access to the width and height of symbols that are specified in proportional values. 
</para>
            </summary>
            <value> A <see cref="T:System.Double"/> value that is the symbols length specified in proportional values.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolLength.Type">
            <summary>
                <para>Returns the type of the SymbolLength object.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SymbolLengthType"/> enumeration value that specifies is the type of the SymbolLength object.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.TextVerticalAlignment">

            <summary>
                <para>Lists the values used to specify the vertical alignment of a text that is shown on the  symbols panel.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.TextVerticalAlignment.Bottom">
            <summary>
                <para><para>A text is docked to the bottom of the symbols panel.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.TextVerticalAlignment.Center">
            <summary>
                <para><para>A text is placed at the center of the symbols panel.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.TextVerticalAlignment.Top">
            <summary>
                <para><para>A text is docked to the top of the symbols panel.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.TextHorizontalAlignment">

            <summary>
                <para>Lists the values used to specify the horizontal alignment of a text that is shown on the  symbols panel.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.TextHorizontalAlignment.Center">
            <summary>
                <para><para>A text fragment (e.g. "Hello!!!") is positioned at the center of the symbols panel.</para>

<para></para>

<para>Before using horizontal alignment, you need to set the <see cref="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolCount"/> property to a value that is less than number of text symbols.</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.TextHorizontalAlignment.Left">
            <summary>
                <para><para>A text fragment (e.g. "Hello!!!") is positioned to the left side of the symbols panel.</para>

<para></para>

<para>Before using horizontal alignment, you need to set the <see cref="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolCount"/> property to the value that is less than the number of text symbols.  </para>


</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.TextHorizontalAlignment.Right">
            <summary>
                <para><para>A text fragment (e.g. "Hello!!!") is positioned to the right side of the symbols panel.</para>

<para></para>

<para>Before using horizontal alignment, you need to set the <see cref="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolCount"/> property to a value that is less than the number of text symbols.  </para>


</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.CreepingLineAnimation">

            <summary>
                <para>Contains settings to provide a creeping line animation for the digital gauge control. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.CreepingLineAnimation.#ctor">
            <summary>
                <para>Initializes a new instance of the CreepingLineAnimation class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CreepingLineAnimation.AdditionalSpaces">
            <summary>
                <para>Specifies the additional steps which the creeping line animation executes on the symbols panel relative to the inanimate text position.  


</para>
            </summary>
            <value>An integer value that is the additional step of the creeping line animation. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CreepingLineAnimation.AdditionalSpacesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="E:DevExpress.Xpf.Gauges.CreepingLineAnimation.CreepingLineAnimationCompleted">
            <summary>
                <para>Occurs when the creeping line animation is completed in the digital gauge control. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CreepingLineAnimation.Direction">
            <summary>
                <para>Gets or sets the direction of creeping line animation on the symbols panel. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.CreepingLineDirection"/> enumeration value that specifies the creeping line animation direction. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CreepingLineAnimation.DirectionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CreepingLineAnimation.FinalMoves">
            <summary>
                <para>Specifies the additional moves which the creeping line animation executes on the symbols panel relative to the inanimate text position.  

</para>
            </summary>
            <value>An integer value that is the additional move of the creeping line animation. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CreepingLineAnimation.FinalMovesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CreepingLineAnimation.InitialMoves">
            <summary>
                <para>Specifies the start segment from which the creeping line animation begins on the  symbols panel.  
</para>
            </summary>
            <value>An integer value that specifies the start segment of creeping line animation on the symbols panel. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CreepingLineAnimation.InitialMovesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CreepingLineAnimation.Repeat">
            <summary>
                <para>Gets or sets whether or not the creeping line animation should be repeated.
</para>
            </summary>
            <value><b>true</b> to repeat the creeping line animation for the digital gauge control; otherwise <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CreepingLineAnimation.RepeatProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CreepingLineAnimation.RepeatSpaces">
            <summary>
                <para>Specifies space segments that appear on the symbols panel each time the text animation is repeated.  
</para>
            </summary>
            <value>An integer value that specifies the number of space segments on the symbols panel.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CreepingLineAnimation.RepeatSpacesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CreepingLineAnimation.StartSpaces">
            <summary>
                <para>Specifies the start segment from which the creeping line animation begins on the symbols panel.  
</para>
            </summary>
            <value>An integer value that specifies the start segment of creeping line animation on the symbols panel. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CreepingLineAnimation.StartSpacesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.TextDirection">

            <summary>
                <para>Lists which parts of a text limited by the <see cref="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolCount"/> property should be shown on the symbols panel. 
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.TextDirection.LeftToRight">
            <summary>
                <para><para> The initial part of a text limited by the <see cref="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolCount"/> property value is shown on the symbols panel.</para>

<para>For instance, the image below shows the initial part of the "Hello!" text. The <see cref="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolCount"/> property is set to <b>3</b>. </para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.TextDirection.RightToLeft">
            <summary>
                <para><para> The final part of a text limited by the <see cref="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolCount"/> property value is shown on the symbols panel.</para>
 
<para>For instance, the image below shows the final part of the "Hello!" text. The <see cref="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolCount"/> property is set to <b>3</b>. </para>

<para></para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SymbolViewBase">

            <summary>
                <para>A base class for all symbol view types of a digital gauge control.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.SymbolViewBase.#ctor">
            <summary>
                <para>Initializes a new instance of the SymbolViewBase class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolViewBase.Animation">
            <summary>
                <para>Provides access to the animation object that allows you to customize animation for the current symbol view type.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SymbolsAnimation"/> class descendant.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolViewBase.AnimationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolViewBase.CustomSymbolMapping">
            <summary>
                <para>Provides  elements that are used in custom symbol mapping.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SymbolDictionary"/> object that stores elements for custom symbol mapping. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolViewBase.CustomSymbolMappingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolViewBase.Height">
            <summary>
                <para>Specifies the symbol's height for the current symbol view. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SymbolLength"/> object that is the symbol's height.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolViewBase.HeightProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolViewBase.Options">
            <summary>
                <para>Provides access to the settings that specify the symbol view position on the symbols panel. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SymbolOptions"/> object that contains the settings of the symbol view type. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolViewBase.OptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.SymbolViewBase.Width">
            <summary>
                <para>Specifies the symbol's width for the current symbol view. 

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SymbolLength"/> object  that is the symbol's width.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolViewBase.WidthProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.StatesMask">

            <summary>
                <para>A states mask that is used to display a custom symbol on a <see cref="T:DevExpress.Xpf.Gauges.DigitalGaugeControl"/>.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.StatesMask.#ctor(System.Boolean[])">
            <summary>
                <para>Initializes a new instance of the StatesMask class with specified initial states.

</para>
            </summary>
            <param name="initialStates">
		A <see cref="T:System.Boolean"/> array that contains the initial states.



            </param>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.StatesMask.States">
            <summary>
                <para>Provides access to the symbol states that are used to provide both custom symbol mapping and specify the blinking animation effect.   

</para>
            </summary>
            <value> A <see cref="T:System.Boolean"/> array that contains the symbol states.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.DigitalGaugeLayer">

            <summary>
                <para>A layer that contains properties to define the visual presentation of a digital gauge.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.DigitalGaugeLayer.#ctor">
            <summary>
                <para>Initializes a new instance of the DigitalGaugeLayer class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeLayer.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for a digital gauge layer.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeLayer.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the layer.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.DigitalGaugeLayerPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeLayer.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.DigitalGaugeLayerCollection">

            <summary>
                <para>A collection that stores the layers of a particular digital gauge.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.DigitalGaugeLayerCollection.#ctor(DevExpress.Xpf.Gauges.DigitalGaugeControl)">
            <summary>
                <para>Initializes a new instance of the DigitalGaugeLayerCollection class with the specified owner.
</para>
            </summary>
            <param name="gauge">
		A <see cref="T:DevExpress.Xpf.Gauges.DigitalGaugeControl"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.DigitalGaugeControl">

            <summary>
                <para>A digital gauge control shipped with the DXGauge Suite.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.DigitalGaugeControl.#ctor">
            <summary>
                <para>Initializes a new instance of the DigitalGaugeControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.ActualModel">
            <summary>
                <para>Gets the actual model used to draw elements of a Digital Gauge.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.DigitalGaugeModel"/> class descendant that is the actual model.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeControl.ActualModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.ActualSymbolView">
            <summary>
                <para>Gets the actual symbol view of the <see cref="T:DevExpress.Xpf.Gauges.DigitalGaugeControl"/>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SymbolViewBase"/> class descendant that is the actual symbol view.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeControl.ActualSymbolViewProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.Layers">
            <summary>
                <para>Provides access to a collection of  layers contained in the digital gauge. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.DigitalGaugeLayerCollection"/> object that contains digital gauge layers. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeControl.LayersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.Model">
            <summary>
                <para>Gets or sets a model for the digital gauge control that is used to draw its elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.DigitalGaugeModel"/> class descendant that is the actual model.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeControl.ModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.PredefinedModels">
            <summary>
                <para>Returns a list of predefined models for a Digital Gauge control. 
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolCount">
            <summary>
                <para>Specifies the total number of symbols (both containing a text and empty or only empty) that should be displayed on the symbols panel. 
</para>
            </summary>
            <value>An integer value that is the symbols number.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolCountProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolView">
            <summary>
                <para>Provides access to the settings of the current symbol view of the DigitalGaugeControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SymbolViewBase"/> class descendant that is the current symbol view. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolViewProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.Text">
            <summary>
                <para>Gets or sets a text that is displayed on the symbols panel of the digital gauge control. 

</para>
            </summary>
            <value>A <see cref="T:System.String"/> object that is the text displayed on the digital gauge control.   

</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.TextDirection">
            <summary>
                <para>Specifies which part of a text limited by the <see cref="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.SymbolCount"/> property should be shown on the symbols panel (either the initial or final).
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.TextDirection"/> enumeration value. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeControl.TextDirectionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.TextHorizontalAlignment">
            <summary>
                <para>Gets or sets the horizontal alignment of a text for the digital gauge control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.TextHorizontalAlignment"/> enumeration value. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeControl.TextHorizontalAlignmentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeControl.TextProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.DigitalGaugeControl.TextVerticalAlignment">
            <summary>
                <para>Gets or sets the text vertical alignment for the digital gauge control. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.TextVerticalAlignment"/> enumeration value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.DigitalGaugeControl.TextVerticalAlignmentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.BlinkingAnimation">

            <summary>
                <para>Contains settings to provide a blinking animation effect for the digital gauge control. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.BlinkingAnimation.#ctor">
            <summary>
                <para>Initializes a new instance of the BlinkingAnimation class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.BlinkingAnimation.SymbolsStates">
            <summary>
                <para>Specifies symbols states to show (hide) blinking animation on the symbols panel.  

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.StatesMask"/> value that specifies symbols states.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.BlinkingAnimation.SymbolsStatesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LayerCollection`1">

            <summary>
                <para>A collection that stores the layers of a particular scale.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LayerCollection`1.#ctor(DevExpress.Xpf.Gauges.Scale)">
            <summary>
                <para>Initializes a new instance of the LayerCollection&lt;T&gt; class with the specified owner.
</para>
            </summary>
            <param name="scale">
		A <see cref="T:DevExpress.Xpf.Gauges.Scale"/> class descendant that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ScaleLineOptions">

            <summary>
                <para>Contains layout and appearance options for a scale line.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ScaleLineOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the ScaleLineOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleLineOptions.Offset">
            <summary>
                <para>Gets or sets the offset specifying a line's position on a scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the line's offset.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleLineOptions.OffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleLineOptions.Thickness">
            <summary>
                <para>Gets or sets a value that specifies the thickness of the line on a scale.
</para>
            </summary>
            <value>A <see cref="T:System.Int32"/> value that is the thickness of the line.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleLineOptions.ThicknessProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleLineOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of a scale line.
</para>
            </summary>
            <value>A <see cref="T:System.Int32"/> value that is the z-index.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleLineOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.TickmarksPresentation">

            <summary>
                <para>Contains settings that define the presentation of scale tickmarks.

</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.PresentationBase">

            <summary>
                <para>A base for all classes that contain presentation settings.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.PresentationBase.PresentationName">
            <summary>
                <para>Returns the human-readable name of the current presentation.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which is the presentation name.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleMarkerPresentation">

            <summary>
                <para>Contains presentation settings for a linear scale marker.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.SymbolDictionary">

            <summary>
                <para>A dictionary that stores elements for custom symbol mapping. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.SymbolDictionary.#ctor(DevExpress.Xpf.Gauges.SymbolViewBase)">
            <summary>
                <para>Initializes a new instance of the SymbolDictionary class with the specified owner.
</para>
            </summary>
            <param name="symbolView">
		A <see cref="T:DevExpress.Xpf.Gauges.SymbolViewBase"/> class descendant that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.RangeOptions">

            <summary>
                <para>Contains  layout and appearance options for a range.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.RangeOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the RangeOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeOptions.Offset">
            <summary>
                <para>Gets or sets the offset specifying a range's position on a scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the range's offset.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.RangeOptions.OffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeOptions.Thickness">
            <summary>
                <para>Gets or sets a value that specifies the thickness of the range on a scale.
</para>
            </summary>
            <value>A <see cref="T:System.Int32"/> value that is the thickness of the range.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.RangeOptions.ThicknessProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of a range.
</para>
            </summary>
            <value> An integer value that is the z-index.


</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.RangeOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.RangeValueType">

            <summary>
                <para>Lists the values used to specify the measure units of a range.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.RangeValueType.Absolute">
            <summary>
                <para>Range value is specified in absolute measure units.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.RangeValueType.Percent">
            <summary>
                <para>Range value is specified in percents.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.RangeBarOptionsBase">

            <summary>
                <para>Serves as the base class for range bar options.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeBarOptionsBase.Offset">
            <summary>
                <para>Gets or sets the offset specifying a range bar's position on a scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the range bar's offset.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.RangeBarOptionsBase.OffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.RangeBarOptionsBase.Thickness">
            <summary>
                <para>Gets or sets a value that specifies the thickness of the range bar on a scale.
</para>
            </summary>
            <value>A <see cref="T:System.Int32"/> value that is the thickness of the range bar.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.RangeBarOptionsBase.ThicknessProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SymbolType">

            <summary>
                <para>Contains the values used to specify the symbol type to display custom symbol characters on a digital gauge control. 
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolType.Additional">
            <summary>
                <para><para> An additional symbol is displayed in the same segment with the previous character. </para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.SymbolType.Main">
            <summary>
                <para><para>A main symbol is displayed in its own segment.</para>

<para></para>

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ValueIndicatorPresentation">

            <summary>
                <para>A base class for all classes that contain presentation settings for value indicators.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.ValueIndicatorCollection`1">

            <summary>
                <para>A collection that stores the value indicators of a particular scale.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ValueIndicatorCollection`1.#ctor(DevExpress.Xpf.Gauges.Scale)">
            <summary>
                <para>Initializes a new instance of the ValueIndicatorCollection&lt;T&gt; class with the specified owner.
</para>
            </summary>
            <param name="scale">
		A <see cref="T:DevExpress.Xpf.Gauges.Scale"/> class descendant that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ValueIndicatorBase">

            <summary>
                <para>Serves as the base class for all value indicators.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ValueIndicatorBase.#ctor">
            <summary>
                <para>Initializes a new instance of the ValueIndicatorBase class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ValueIndicatorBase.Animation">
            <summary>
                <para>Provides access to the animation object that allows you to customize animation for the current value indicator.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.IndicatorAnimation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ValueIndicatorBase.AnimationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ValueIndicatorBase.IsHitTestVisible">
            <summary>
                <para>Gets or sets a value that defines whether or not an indicator can be returned as a hit-testing result.
 
</para>
            </summary>
            <value><b>true</b> in case the indicator can be shown as the result of hit testing; otherwise <b>false</b>.   
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ValueIndicatorBase.IsHitTestVisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ValueIndicatorBase.IsInteractive">
            <summary>
                <para>Gets or sets a value that indicates whether interactivity is enabled for the current value indicator or not.

</para>
            </summary>
            <value><b>true</b> to enable interactivity; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ValueIndicatorBase.IsInteractiveProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ValueIndicatorBase.Value">
            <summary>
                <para>Gets or sets the value of a scale indicator. 
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the position of a value indicator on the scale. 
</value>


        </member>
        <member name="E:DevExpress.Xpf.Gauges.ValueIndicatorBase.ValueChanged">
            <summary>
                <para>Occurs when the <see cref="P:DevExpress.Xpf.Gauges.ValueIndicatorBase.Value"/> property has been changed.
 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ValueIndicatorBase.ValueProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ValueIndicatorBase.Visible">
            <summary>
                <para>Gets or sets whether the value indicator is visible.
</para>
            </summary>
            <value><b>true</b> if the value indicator is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ValueIndicatorBase.VisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ValueChangedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Gauges.ValueIndicatorBase.ValueChanged"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ValueChangedEventHandler.Invoke(System.Object,DevExpress.Xpf.Gauges.ValueChangedEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Xpf.Gauges.ValueIndicatorBase.ValueChanged"/> event.

</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Gauges.ValueChangedEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.TickmarkOptions">

            <summary>
                <para>Serves as a base for classes that contain appearance and behavior options for tickmarks.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.TickmarkOptions.FactorLength">
            <summary>
                <para>Gets or sets a value by which the tickmarks' length should be multiplied.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the tickmarks' length multiplier.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.TickmarkOptions.FactorLengthProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.TickmarkOptions.FactorThickness">
            <summary>
                <para>Gets or sets a value by which the tickmarks' thickness should be multiplied.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the tickmarks' thickness multiplier.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.TickmarkOptions.FactorThicknessProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.TickmarkOptions.Offset">
            <summary>
                <para>Gets or sets the offset of tickmarks shown on a scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the tickmark position on a scale.


</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.TickmarkOptions.OffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.SpindleCapPresentation">

            <summary>
                <para>Contains presentation settings for a spindle cap.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.ScaleCustomLabel">

            <summary>
                <para>A scale custom label.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ScaleCustomLabel.#ctor">
            <summary>
                <para>Initializes a new instance of the ScaleCustomLabel class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleCustomLabel.Offset">
            <summary>
                <para>Gets or sets the offset specifying a custom label's position on a scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the custom label's offset.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleCustomLabel.OffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ScaleCustomLabel.Value">
            <summary>
                <para>Gets or sets the value about which the custom label is located on a scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ScaleCustomLabel.ValueProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.PredefinedElementKind">

            <summary>
                <para>Defines the kind of a predefined element.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.PredefinedElementKind.Name">
            <summary>
                <para>Returns the name of the predefined element.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> that is the element name.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.PredefinedElementKind.ToString">
            <summary>
                <para>Returns the textual representation of the PredefinedElementKind object.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value, which is the textual representation of the element kind.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.PredefinedElementKind.Type">
            <summary>
                <para>Returns the type of the predefined element.
</para>
            </summary>
            <value>A <see cref="T:System.Type"/> that is the element type.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.MarkerOptionsBase">

            <summary>
                <para>Serves as the base class for all marker options.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.MarkerOptionsBase.FactorHeight">
            <summary>
                <para>Gets or sets a value by which the marker's height should be multiplied.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the marker's height multiplier.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.MarkerOptionsBase.FactorHeightProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.MarkerOptionsBase.FactorWidth">
            <summary>
                <para>Gets or sets a value by which the marker's width should be multiplied.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the marker's width multiplier.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.MarkerOptionsBase.FactorWidthProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.MarkerOptionsBase.Offset">
            <summary>
                <para>Gets or sets the offset specifying a marker's position on a scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the marker's offset.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.MarkerOptionsBase.OffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.MajorTickmarkOptions">

            <summary>
                <para>Contains settings that define the layout and behavior of the major tickmarks along the scale.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.MajorTickmarkOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the MajorTickmarkOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.MajorTickmarkOptions.ShowFirst">
            <summary>
                <para>Gets or sets a value indicating whether or not the fitst major tickmark should be shown on a scale.
</para>
            </summary>
            <value><b>true</b> to display the first major tickmark; otherwise <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.MajorTickmarkOptions.ShowFirstProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.MajorTickmarkOptions.ShowLast">
            <summary>
                <para>Gets or sets a value indicating whether or not the last major tickmark should be shown on a scale.
</para>
            </summary>
            <value><b>true</b> to display the last major tickmark on a scale; otherwise <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.MajorTickmarkOptions.ShowLastProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.MajorTickmarkOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of major tickmarks.
</para>
            </summary>
            <value> An integer value that is the z-index.


</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.MajorTickmarkOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleRangeCollection">

            <summary>
                <para>A collection that stores the ranges of a particular linear scale.



</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleRangeCollection.#ctor(DevExpress.Xpf.Gauges.LinearScale)">
            <summary>
                <para>Initializes a new instance of the LinearScaleRangeCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		A <see cref="T:DevExpress.Xpf.Gauges.LinearScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleRangeBarOptions">

            <summary>
                <para>Contains appearance and layout options for a linear scale range bar.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleRangeBarOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearScaleRangeBarOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleRangeBarOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of a range bar.
</para>
            </summary>
            <value> An integer value that is the z-index.


</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleRangeBarOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleRange">

            <summary>
                <para>A linear scale range.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleRange.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearScaleRange class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleRange.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for a linear scale range.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleRange.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the range.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleRangePresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleRange.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleLevelBarPresentation">

            <summary>
                <para>Contains presentation settings for a linear scale level bar.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleMarkerCollection">

            <summary>
                <para>A collection that stores the markers of a particular linear scale.



</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleMarkerCollection.#ctor(DevExpress.Xpf.Gauges.LinearScale)">
            <summary>
                <para>Initializes a new instance of the LinearScaleMarkerCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		A <see cref="T:DevExpress.Xpf.Gauges.LinearScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleMarker">

            <summary>
                <para>A linear scale marker.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleMarker.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearScaleMarker class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleMarker.Options">
            <summary>
                <para>Gets or sets the options of a marker that specify its shape and position on a Linear scale.  
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleMarkerOptions"/> object that contains the settings of the marker.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleMarker.OptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleMarker.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for a linear scale marker.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleMarker.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the marker.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleMarkerPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleMarker.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleLayerPresentation">

            <summary>
                <para>Contains presentation settings for a linear scale layer.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleLevelBarCollection">

            <summary>
                <para>A collection that stores the level bars of a particular linear scale.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleLevelBarCollection.#ctor(DevExpress.Xpf.Gauges.LinearScale)">
            <summary>
                <para>Initializes a new instance of the LinearScaleLevelBarCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		A <see cref="T:DevExpress.Xpf.Gauges.LinearScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleLevelBar">

            <summary>
                <para>A linear scale level bar.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleLevelBar.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearScaleLevelBar class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleLevelBar.Options">
            <summary>
                <para>Gets or sets the options of a level bar that specify its shape and position on a Linear scale.  
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleLevelBarOptions"/> object that contains the settings of the level bar.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLevelBar.OptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleLevelBar.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for a linear gauge level bar.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleLevelBar.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the level bar.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleLevelBarPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLevelBar.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleLayerCollection">

            <summary>
                <para>A collection that stores the layers of a particular linear scale.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleLayerCollection.#ctor(DevExpress.Xpf.Gauges.LinearScale)">
            <summary>
                <para>Initializes a new instance of the LinearScaleLayerCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		A <see cref="T:DevExpress.Xpf.Gauges.LinearScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleLayer">

            <summary>
                <para>A layer that contains properties to define the visual presentation of a linear scale.



</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleLayer.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearScaleLayer class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleLayer.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for a linear scale layer.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleLayer.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the layer.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleLayerPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLayer.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleLabelOptions">

            <summary>
                <para>Contains behavior, layout and data representation options for linear scale labels.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleLabelOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearScaleLabelOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScaleLabelOptions.Orientation">
            <summary>
                <para>Provides different types of orientation for labels on the Linear scale. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleLabelOrientation"/> object that specifies possible ways labels can be oriented.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLabelOptions.OrientationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleIndicatorCollection`1">

            <summary>
                <para>A collection that stores the value indicators of a particular linear scale.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleIndicatorCollection`1.#ctor(DevExpress.Xpf.Gauges.LinearScale)">
            <summary>
                <para>Initializes a new instance of the LinearScaleIndicatorCollection&lt;T&gt; class with the specified owner.
</para>
            </summary>
            <param name="scale">
		A <see cref="T:DevExpress.Xpf.Gauges.LinearScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleIndicator">

            <summary>
                <para>Serves as the base class for a linear scale's value indicators.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleCollection">

            <summary>
                <para>A collection of linear scales.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScaleCollection.#ctor(DevExpress.Xpf.Gauges.LinearGaugeControl)">
            <summary>
                <para>Initializes a new instance of the LinearScaleCollection class with the specified owner.
</para>
            </summary>
            <param name="gauge">
		A <see cref="T:DevExpress.Xpf.Gauges.LinearGaugeControl"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScale">

            <summary>
                <para>A linear scale.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearScale.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearScale class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScale.LabelOptions">
            <summary>
                <para>Gets or sets the options that specify the position and format for labels displayed on the scale.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleLabelOptions"/> object that contains label settings.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScale.LabelOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScale.Layers">
            <summary>
                <para>Provides access to a collection of layers contained in the current Linear Scale.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleLayerCollection"/> object that contains scale layers.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScale.LayersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScale.LayoutMode">
            <summary>
                <para>Provides different types of layouts for the Linear Scale.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleLayoutMode"/> enumeration value that specifies the possible ways a linear scale can be positioned.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScale.LayoutModeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScale.LevelBars">
            <summary>
                <para>Provides access to a collection of level bars contained in the current Linear Scale.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleLevelBarCollection"/> object that contains scale level bars.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScale.LevelBarsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScale.LinePresentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of a line.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleLinePresentation"/> object.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScale.LinePresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScale.Markers">
            <summary>
                <para>Provides access to a collection of markers contained in the current  Linear Scale.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleMarkerCollection"/> object that contains scale markers.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScale.MarkersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScale.PredefinedLinePresentations">
            <summary>
                <para>Returns a list of predefined presentations for lines.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScale.RangeBars">
            <summary>
                <para>Provides access to a collection of range bars contained in the current Linear Scale.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleRangeBarCollection"/> object that contains scale range bars.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScale.RangeBarsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearScale.Ranges">
            <summary>
                <para>Provides access to a collection of ranges contained in the current Linear Scale.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleRangeCollection"/> object that contains scale ranges.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScale.RangesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearGaugeLayerCollection">

            <summary>
                <para>A collection that stores the layers of a particular linear gauge.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearGaugeLayerCollection.#ctor(DevExpress.Xpf.Gauges.LinearGaugeControl)">
            <summary>
                <para>Initializes a new instance of the LinearGaugeLayerCollection class with the specified owner.
</para>
            </summary>
            <param name="gauge">
		A <see cref="T:DevExpress.Xpf.Gauges.LinearGaugeControl"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearGaugeLayer">

            <summary>
                <para>A layer of a Linear Gauge.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearGaugeLayer.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearGaugeLayer class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeLayer.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for a linear gauge layer.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeLayer.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the layer.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearGaugeLayerPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearGaugeLayer.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearGaugeControl">

            <summary>
                <para>A <b>Linear Gauge</b> control shipped with the DXGauges Suite.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearGaugeControl.#ctor">
            <summary>
                <para>Initializes a new instance of the LinearGaugeControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeControl.ActualModel">
            <summary>
                <para>Gets the actual model used to draw elements of a Linear Gauge.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearGaugeModel"/> class descendant that is the actual model.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearGaugeControl.ActualModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.LinearGaugeControl.CalcHitInfo(System.Windows.Point)">
            <summary>
                <para>Returns information on the gauge elements located at the specified point.

</para>
            </summary>
            <param name="point">
		A <see cref="T:System.Drawing.Point"/> structure which specifies the test point coordinates relative to the gauge's top-left corner.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Gauges.LinearGaugeHitInfo"/> object, which contains information about the gauge elements located at the test point.

</returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeControl.Layers">
            <summary>
                <para>Provides access to a collection of  layers contained in the linear gauge. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearGaugeLayerCollection"/> object that contains linear gauge layers. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearGaugeControl.LayersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeControl.Model">
            <summary>
                <para>Gets or sets a model for the linear gauge control that is used to draw its elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearGaugeModel"/> class descendant that is the actual model.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearGaugeControl.ModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeControl.PredefinedModels">
            <summary>
                <para>Returns a list of predefined models for a Linear Gauge control. 
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeControl.Scales">
            <summary>
                <para>Provides access to a collection of scales contained in the linear gauge. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleCollection"/> object that contains linear gauge scales. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearGaugeControl.ScalesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearGaugeHitInfo">

            <summary>
                <para>Contains information about a specific point within a linear gauge.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeHitInfo.InLevelBar">
            <summary>
                <para>Gets a value indicating whether the test point is within a level bar.
</para>
            </summary>
            <value><b>true</b> if the test point is within a level bar; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeHitInfo.InMarker">
            <summary>
                <para>Gets a value indicating whether the test point is within a marker.
</para>
            </summary>
            <value><b>true</b> if the test point is within a marker; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeHitInfo.InRange">
            <summary>
                <para>Gets a value indicating whether the test point is within a range.
</para>
            </summary>
            <value><b>true</b> if the test point is within a range; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeHitInfo.InRangeBar">
            <summary>
                <para>Gets a value indicating whether the test point is within a range bar.
</para>
            </summary>
            <value><b>true</b> if the test point is within a range bar; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeHitInfo.InScale">
            <summary>
                <para>Gets a value indicating whether the test point is within a linear scale.
</para>
            </summary>
            <value><b>true</b> if the test point is within a scale; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeHitInfo.LevelBar">
            <summary>
                <para>Gets a level bar which is located under the test point.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleLevelBar"/> object that is the level bar located under the test point.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeHitInfo.Marker">
            <summary>
                <para>Gets a marker which is located under the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleMarker"/> object that is the marker located under the test point.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeHitInfo.Range">
            <summary>
                <para>Gets a range which is located under the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleRange"/> object that is the range located under the test point.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeHitInfo.RangeBar">
            <summary>
                <para>Gets a range bar which is located under the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScaleRangeBar"/> object that is the range bar located under the test point.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.LinearGaugeHitInfo.Scale">
            <summary>
                <para>Gets a scale which is located under the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LinearScale"/> object that is the scale located under the test point.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LayerPresentation">

            <summary>
                <para>A base class for all classes that contain presentation settings for layers.

</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.IndicatorLeaveEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorLeave"/> event.

</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.IndicatorEnterLeaveEventArgs">

            <summary>
                <para>A base class for classes that provide data for <see cref="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorEnter"/> and <see cref="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorLeave"/> events.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.IndicatorEnterLeaveEventArgs.Indicator">
            <summary>
                <para>Gets the object, for which either the <see cref="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorEnter"/> or <see cref="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorLeave"/> event has been raised.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.ValueIndicatorBase"/> class descendant representing the indicator.

</value>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.IndicatorAnimation">

            <summary>
                <para>Contains settings for animating a value indicator when it changes its value.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.IndicatorAnimation.#ctor">
            <summary>
                <para>Initializes a new instance of the IndicatorAnimation class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.IndicatorAnimation.Duration">
            <summary>
                <para>Gets or sets the duration of an animation effect.
</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> value that is the duration of an animation effect.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.IndicatorAnimation.DurationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.IndicatorAnimation.EasingFunction">
            <summary>
                <para>Gets or sets an animation function that defines how indicator values change during animation.


</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Media.Animation.IEasingFunction"/> interface.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.IndicatorAnimation.EasingFunctionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.IndicatorEnterEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorEnter"/> event.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.IndicatorEnterEventHandler.Invoke(System.Object,DevExpress.Xpf.Gauges.IndicatorEnterEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorEnter"/> event.

</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.Xpf.Gauges.IndicatorEnterEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.IndicatorEnterEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Gauges.RangeBase.IndicatorEnter"/> event.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.GaugeLayerCollection`1">

            <summary>
                <para>A base class for collections containing gauge layers.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeLayerCollection`1.#ctor(DevExpress.Xpf.Gauges.GaugeControlBase)">
            <summary>
                <para>Initializes a new instance of the GaugeLayerCollection&lt;T&gt; class with the specified owner.
</para>
            </summary>
            <param name="gauge">
		A <see cref="T:DevExpress.Xpf.Gauges.GaugeControlBase"/> class descendant that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.GaugeHitInfoBase">

            <summary>
                <para>A base class for classes that contains information about a specific point within a gauge.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.GaugeElementCollection`1">

            <summary>
                <para>A base class for collections containing gauge elements.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeElementCollection`1.#ctor">
            <summary>
                <para>Initializes a new instance of the GaugeElementCollection&lt;T&gt; class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.GaugeElement">

            <summary>
                <para>The base class for other gauge elements, and is intended to hide most properties of the <see cref="T:System.Windows.Controls.Control"/> class.


</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.GaugeDependencyObjectCollectionBase`1">

            <summary>
                <para>A base class for all collections in the DXGauges Suite.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeDependencyObjectCollectionBase`1.#ctor">
            <summary>
                <para>Initializes a new instance of the GaugeDependencyObjectCollectionBase&lt;T&gt; class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeDependencyObjectCollectionBase`1.Add(DevExpress.Xpf.Gauges.T)">
            <summary>
                <para>Appends the specified item to the current collection.
</para>
            </summary>
            <param name="item">
		An object to append to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeDependencyObjectCollectionBase`1.Clear">
            <summary>
                <para>Removes all items from the collection.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeDependencyObjectCollectionBase`1.Insert(System.Int32,DevExpress.Xpf.Gauges.T)">
            <summary>
                <para>Adds the specified item to the collection at the specified position.
</para>
            </summary>
            <param name="index">
		A zero-based integer which specifies the position at which the item is to be inserted.

            </param>
            <param name="item">
		An item to insert into the collection.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.GaugeDependencyObjectCollectionBase`1.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to individual items in the collection.

</para>
            </summary>
            <param name="index">
		A zero-based integer specifying the desired item's position within the collection. If it's negative or exceeds the last available index, an exception is raised. 

            </param>
            <value>An object which represents an item at the specified position.

</value>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeDependencyObjectCollectionBase`1.Move(System.Int32,System.Int32)">
            <summary>
                <para>Moves a specific item to another position within the collection.
</para>
            </summary>
            <param name="oldIndex">
		An integer value specifying the zero-based index of an item to be moved.

            </param>
            <param name="newIndex">
		An integer value specifying the zero-based destination index of the moved item.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeDependencyObjectCollectionBase`1.Remove(DevExpress.Xpf.Gauges.T)">
            <summary>
                <para>Removes the specified item from the collection.

</para>
            </summary>
            <param name="item">
		An item to be removed from the collection.

            </param>
            <returns><b>true</b> if the specified item was found and successfully removed from the collection; <b>false</b> if the specified item wasn't found.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeDependencyObjectCollectionBase`1.RemoveAt(System.Int32)">
            <summary>
                <para>Removes an item at the specified position from the collection.
</para>
            </summary>
            <param name="index">
		A zero-based integer specifying the index of the object to remove. If it's negative or exceeds the number of elements, an exception is raised.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.GaugeDependencyObjectCollection`1">

            <summary>
                <para>A base class for most collections in the DXGauges Suite.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeDependencyObjectCollection`1.#ctor">
            <summary>
                <para>Initializes a new instance of the GaugeDependencyObjectCollection&lt;T&gt; class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.GaugeDependencyObject">

            <summary>
                <para>Enables Windows Presentation Foundation (WPF) property system services for its derived model classes.
</para>
            </summary>

        </member>
        <member name="E:DevExpress.Xpf.Gauges.GaugeDependencyObject.PropertyChanged">
            <summary>
                <para>Occurs every time any of the GaugeDependencyObject class properties has changed its value.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.GaugeControlBase">

            <summary>
                <para>The base class for all gauge controls shipped with the DXGauges Suite.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeControlBase.#ctor">
            <summary>
                <para>Initializes a new instance of the GaugeControlBase class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.GaugeControlBase.Elements">
            <summary>
                <para>This property is hidden and intended for internal use only. Normally, you won't need to use it. 
</para>
            </summary>
            <value>A collection of elements. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.GaugeControlBase.ElementsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.GaugeControlBase.EnableAnimation">
            <summary>
                <para>Gets or sets a value specifying whether value indicators should be animated when changing their values.
</para>
            </summary>
            <value><b>true</b> to enable animation; otherwise, <b>false</b>.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.GaugeControlBase.EnableAnimationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.GaugeControlBase.HorizontalContentAlignment">
            <summary>
                <para>This property is hidden, because it is not supported in this class.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.HorizontalAlignment"/> value.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.GaugeControlBase.OnApplyTemplate">
            <summary>
                <para>Called after the template is completely generated and attached to the visual tree.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.GaugeControlBase.VerticalContentAlignment">
            <summary>
                <para>This property is hidden because it is not supported in this class.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.HorizontalAlignment"/> value.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.CircularGaugeLayerPresentation">

            <summary>
                <para>Contains presentation settings for a circular gauge layer.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.CircularGaugeLayerCollection">

            <summary>
                <para>A collection that stores the layers of a particular circular gauge.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.CircularGaugeLayerCollection.#ctor(DevExpress.Xpf.Gauges.CircularGaugeControl)">
            <summary>
                <para>Initializes a new instance of the CircularGaugeLayerCollection class with the specified owner.
</para>
            </summary>
            <param name="gauge">
		A <see cref="T:DevExpress.Xpf.Gauges.CircularGaugeControl"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.CircularGaugeLayer">

            <summary>
                <para>A layer of a Circular Gauge.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.CircularGaugeLayer.#ctor">
            <summary>
                <para>Initializes a new instance of the CircularGaugeLayer class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeLayer.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for a circular gauge layer.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeLayer.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the layer.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.CircularGaugeLayerPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CircularGaugeLayer.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.CircularGaugeHitInfo">

            <summary>
                <para>Contains information about a specific point within a circular gauge.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeHitInfo.InMarker">
            <summary>
                <para>Gets a value indicating whether the test point is within a marker.
</para>
            </summary>
            <value><b>true</b> if the test point is within a marker; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeHitInfo.InNeedle">
            <summary>
                <para>Gets a value indicating whether the test point is within a needle.
</para>
            </summary>
            <value><b>true</b> if the test point is within a needle; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeHitInfo.InRange">
            <summary>
                <para>Gets a value indicating whether the test point is within a range.
</para>
            </summary>
            <value><b>true</b> if the test point is within a range; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeHitInfo.InRangeBar">
            <summary>
                <para>Gets a value indicating whether the test point is within a range bar.
</para>
            </summary>
            <value><b>true</b> if the test point is within a range bar; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeHitInfo.InScale">
            <summary>
                <para>Gets a value indicating whether the test point is within a scale.
</para>
            </summary>
            <value><b>true</b> if the test point is within a scale; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeHitInfo.Marker">
            <summary>
                <para>Gets a marker which is located under the test point.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleMarker"/> object that is the marker located under the test point.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeHitInfo.Needle">
            <summary>
                <para>Gets a needle which is located under the test point.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleNeedle"/> object that is the needle located under the test point.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeHitInfo.Range">
            <summary>
                <para>Gets a range which is located under the test point.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleRange"/> object that is the range located under the test point.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeHitInfo.RangeBar">
            <summary>
                <para>Gets a range bar which is located under the test point.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleRangeBar"/> object that is the range bar located under the test point.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeHitInfo.Scale">
            <summary>
                <para>Gets a scale which is located under the test point.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScale"/> object that is the scale located under the test point.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleRangeBarPresentation">

            <summary>
                <para>Contains presentation settings for an arc scale range bar.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleRangeCollection">

            <summary>
                <para>A collection that stores the ranges of a particular arc scale.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleRangeCollection.#ctor(DevExpress.Xpf.Gauges.ArcScale)">
            <summary>
                <para>Initializes a new instance of the ArcScaleRangeCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		An <see cref="T:DevExpress.Xpf.Gauges.ArcScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleRangeBarCollection">

            <summary>
                <para>A collection that stores the range bars of a particular arc scale.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleRangeBarCollection.#ctor(DevExpress.Xpf.Gauges.ArcScale)">
            <summary>
                <para>Initializes a new instance of the ArcScaleRangeBarCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		An <see cref="T:DevExpress.Xpf.Gauges.ArcScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleRangeBar">

            <summary>
                <para>An arc scale range bar.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleRangeBar.#ctor">
            <summary>
                <para>Initializes a new instance of the ArcScaleRangeBar class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleRangeBar.AnchorValue">
            <summary>
                <para>Gets or sets the value on a scale that specifies a fixed edge of the range bar.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value on a scale.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleRangeBar.AnchorValueProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleRangeBar.Options">
            <summary>
                <para>Gets or sets the options of a range bar that specify its shape and position on a Circular scale.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleRangeBarOptions"/> object that contains the settings of the range bar.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleRangeBar.OptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleRangeBar.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for an arc scale range bar.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleRangeBar.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the range bar.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleRangeBarPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleRangeBar.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleRange">

            <summary>
                <para>An arc scale range.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleRange.#ctor">
            <summary>
                <para>Initializes a new instance of the ArcScaleRange class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleRange.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for an arc scale range.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleRange.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the range.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleRangePresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleRange.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleMarkerOptions">

            <summary>
                <para>Contains layout options for an arc scale marker.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleMarkerOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the ArcScaleMarkerOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleMarkerOptions.Orientation">
            <summary>
                <para>Provides different types of orientation for the marker on the Circular scale. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleMarkerOrientation"/> object that specifies possible ways the marker can be oriented.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleMarkerOptions.OrientationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleMarkerOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of a marker.
</para>
            </summary>
            <value>An integer value that is the z-index.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleMarkerOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleNeedlePresentation">

            <summary>
                <para>Contains presentation settings for an arc scale needle.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleNeedleOptions">

            <summary>
                <para>Contains layout options for arc scale needles.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleNeedleOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the ArcScaleNeedleOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleNeedleOptions.EndOffset">
            <summary>
                <para>Gets or sets the offset of the needle's end point  from the edge of the Circular scale.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the end offset of the needle.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleNeedleOptions.EndOffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleNeedleOptions.StartOffset">
            <summary>
                <para>Gets or sets the offset of the needle's starting point  from the Circular scale center.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the start offset of the needle.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleNeedleOptions.StartOffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleNeedleOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of a needle.
</para>
            </summary>
            <value>An integer value that is the z-index.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleNeedleOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleNeedleCollection">

            <summary>
                <para>A collection that stores the needles of a particular arc scale.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleNeedleCollection.#ctor(DevExpress.Xpf.Gauges.ArcScale)">
            <summary>
                <para>Initializes a new instance of the ArcScaleNeedleCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		An <see cref="T:DevExpress.Xpf.Gauges.ArcScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleMarkerCollection">

            <summary>
                <para>A collection that stores the markers of a particular arc scale.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleMarkerCollection.#ctor(DevExpress.Xpf.Gauges.ArcScale)">
            <summary>
                <para>Initializes a new instance of the ArcScaleMarkerCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		An <see cref="T:DevExpress.Xpf.Gauges.ArcScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleMarker">

            <summary>
                <para>An arc scale marker.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleMarker.#ctor">
            <summary>
                <para>Initializes a new instance of the ArcScaleMarker class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleMarker.Options">
            <summary>
                <para>Gets or sets the options of a marker that specify its shape and position on a Circular scale.  
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleMarkerOptions"/> object that contains the settings of the marker.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleMarker.OptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleMarker.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for an arc scale marker.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleMarker.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the marker.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleMarkerPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleMarker.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleLayerPresentation">

            <summary>
                <para>Contains presentation settings for an arc scale layer.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleLayerCollection">

            <summary>
                <para>A collection that stores the layers of a particular arc scale.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleLayerCollection.#ctor(DevExpress.Xpf.Gauges.ArcScale)">
            <summary>
                <para>Initializes a new instance of the ArcScaleLayerCollection class with the specified owner.
</para>
            </summary>
            <param name="scale">
		An <see cref="T:DevExpress.Xpf.Gauges.ArcScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleLayer">

            <summary>
                <para>A layer that contains properties to define the visual presentation of a circular scale.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleLayer.#ctor">
            <summary>
                <para>Initializes a new instance of the ArcScaleLayer class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleLayer.PredefinedPresentations">
            <summary>
                <para>Returns a list of predefined presentations for an arc scale layer.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleLayer.Presentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the layer.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleLayerPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLayer.PresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleLabelOptions">

            <summary>
                <para>Contains appearance and behavior options for arc scale labels.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleLabelOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the ArcScaleLabelOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScaleLabelOptions.Orientation">
            <summary>
                <para>Provides different types of orientation for labels on the Circular scale. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleLabelOrientation"/> object that specifies possible ways labels can be oriented.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLabelOptions.OrientationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleIndicatorCollection`1">

            <summary>
                <para>A collection that stores value indicators of a particular circular scale.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleIndicatorCollection`1.#ctor(DevExpress.Xpf.Gauges.ArcScale)">
            <summary>
                <para>Initializes a new instance of the ArcScaleIndicatorCollection&lt;T&gt; class with the specified owner.
</para>
            </summary>
            <param name="scale">
		An <see cref="T:DevExpress.Xpf.Gauges.ArcScale"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleIndicator">

            <summary>
                <para>A class that specifies  value indicators of a circular scale.  
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleCollection">

            <summary>
                <para>A collection of arc scales. 

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScaleCollection.#ctor(DevExpress.Xpf.Gauges.CircularGaugeControl)">
            <summary>
                <para>Initializes a new instance of the ArcScaleCollection class with the specified owner.
</para>
            </summary>
            <param name="gauge">
		A <see cref="T:DevExpress.Xpf.Gauges.CircularGaugeControl"/> object that should be the owner of the created collection.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScale">

            <summary>
                <para>An arc scale.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.ArcScale.#ctor">
            <summary>
                <para>Initializes a new instance of the ArcScale class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.ActualShowSpindleCap">
            <summary>
                <para>Gets an actual value indicating the visibility of a Spindle Cap.
</para>
            </summary>
            <value><b>true</b> to show a spindle cap; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.EndAngle">
            <summary>
                <para>Gets or sets the angle that specifies the scale end position.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the end angle of the scale.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.EndAngleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.LabelOptions">
            <summary>
                <para>Gets or sets the options that specify the position and format for labels displayed on the scale.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleLabelOptions"/> object that contains label settings.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.LabelOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.Layers">
            <summary>
                <para>Provides access to a collection of layers contained in the current Circular scale.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleLayerCollection"/> object that contains scale layers.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.LayersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.LayoutMode">
            <summary>
                <para>Provides different types of layouts for the Circular Scale.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleLayoutMode"/> enumeration value that specifies the possible ways a circular scale can be positioned.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.LayoutModeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.LinePresentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of a line.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.ArcScaleLinePresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.LinePresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.Markers">
            <summary>
                <para>Provides access to a collection of markers contained in the current Circular Scale.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleMarkerCollection"/> object that contains scale markers.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.MarkersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.Needles">
            <summary>
                <para>Provides access to a collection of needles contained in the current Circular Scale.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleNeedleCollection"/> object that contains scale needles.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.NeedlesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.PredefinedLinePresentations">
            <summary>
                <para>Returns a list of predefined presentations for lines.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.PredefinedSpindleCapPresentations">
            <summary>
                <para>Contains the list of predefined presentations for the Spindle Cap element.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.RangeBars">
            <summary>
                <para>Provides access to a collection of range bars contained in the current Circular Scale.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleRangeBarCollection"/> object that contains scale range bars.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.RangeBarsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.Ranges">
            <summary>
                <para>Provides access to a collection of ranges contained in the current Circular Scale.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleRangeCollection"/>  object that contains scale ranges.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.RangesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.ShowSpindleCap">
            <summary>
                <para>Gets or sets a value indicating whether a spindle cap should be shown or not.
</para>
            </summary>
            <value><b>true</b> to show a spindle cap; <b>false</b> to hide it.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.ShowSpindleCapProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.SpindleCapOptions">
            <summary>
                <para>Gets or sets the options that specify the position and format for a spindle cap displayed on the scale.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SpindleCapOptions"/> object that contains spindle cap settings. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.SpindleCapOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.SpindleCapPresentation">
            <summary>
                <para>Gets or sets the current presentation that specifies the appearance of the spindle cap.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.SpindleCapPresentation"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.SpindleCapPresentationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.ArcScale.StartAngle">
            <summary>
                <para>Gets or sets the angle that specifies the scale start position.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that is the start angle of the scale.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScale.StartAngleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleMarkerOrientation">

            <summary>
                <para>Lists the possible ways linear scale markers can be oriented.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleMarkerOrientation.Normal">
            <summary>
                <para><para>Linear scale marker is painted from right to left.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleMarkerOrientation.Reversed">
            <summary>
                <para><para>Linear scale marker is painted from left to right.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleLayoutMode">

            <summary>
                <para>Lists possible layouts of a linear scale.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLayoutMode.BottomToTop">
            <summary>
                <para><para>A Linear scale is positioned from bottom to top.</para>

<para></para>

<para>Note that you may need to change the <see cref="P:System.Windows.FrameworkElement.Width"/> and <see cref="P:System.Windows.FrameworkElement.Height"/> properties of <see cref="T:DevExpress.Xpf.Gauges.LinearGaugeControl"/> to provide a custom appearance. </para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLayoutMode.LeftToRight">
            <summary>
                <para><para>A linear scale is positioned from left to right.</para>

<para></para>

<para>Note that you may need to change the <see cref="P:System.Windows.FrameworkElement.Width"/> and <see cref="P:System.Windows.FrameworkElement.Height"/> properties of <see cref="T:DevExpress.Xpf.Gauges.LinearGaugeControl"/> to provide a custom appearance. </para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLayoutMode.RightToLeft">
            <summary>
                <para><para>A linear scale is positioned from right to left.</para>

<para></para>

<para>Note that you may need to change the <see cref="P:System.Windows.FrameworkElement.Width"/> and <see cref="P:System.Windows.FrameworkElement.Height"/> properties of <see cref="T:DevExpress.Xpf.Gauges.LinearGaugeControl"/> to provide a custom appearance. </para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLayoutMode.TopToBottom">
            <summary>
                <para><para>A linear scale is positioned from top to bottom. </para>

<para></para>

<para>Note that you may need to change the <see cref="P:System.Windows.FrameworkElement.Width"/> and <see cref="P:System.Windows.FrameworkElement.Height"/> properties of <see cref="T:DevExpress.Xpf.Gauges.LinearGaugeControl"/> to provide a custom appearance. </para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.LinearScaleLabelOrientation">

            <summary>
                <para>Lists the possible ways linear scale labels can be oriented.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLabelOrientation.BottomToTop">
            <summary>
                <para><para>Linear scale labels are painted from bottom to top.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLabelOrientation.LeftToRight">
            <summary>
                <para><para>Linear scale labels are painted from left to right.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.LinearScaleLabelOrientation.TopToBottom">
            <summary>
                <para><para>Linear scale labels are painted from top to bottom.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleMarkerOrientation">

            <summary>
                <para>Lists the possible ways arc scale markers can be oriented.


</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleMarkerOrientation.Normal">
            <summary>
                <para><para>An Arc scale marker isn't rotated when painted, and its default orientation is used. For example, in the picture below, a marker's model represents an arrow oriented from right to left.</para>

<para></para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleMarkerOrientation.RadialFromCenter">
            <summary>
                <para><para>The Arc scale marker is rotated, so that at any point on a scale a marker is radially oriented from the scale center.</para>

<para></para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleMarkerOrientation.RadialToCenter">
            <summary>
                <para><para>The Arc scale marker is rotated, so that at any point on a scale a marker is radially oriented to the scale center.</para>

<para></para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleMarkerOrientation.RotateClockwise">
            <summary>
                <para><para>The Arc scale marker is rotated clockwise by 90 degrees when being painted. For example, in the picture below a marker's model represents an arrow oriented from bottom to top.</para>

<para></para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleMarkerOrientation.RotateCounterclockwise">
            <summary>
                <para><para>The Arc scale marker is rotated counterclockwise by 90 degrees when being painted. For example, in the picture below a marker's model represents an arrow oriented from top to bottom.</para>

<para></para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleMarkerOrientation.Tangent">
            <summary>
                <para><para>The Arc scale marker is rotated, so that at any point on a scale a marker is tangentially oriented to the corresponding values on a scale.</para>

<para></para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleMarkerOrientation.UpsideDown">
            <summary>
                <para><para>The Arc scale marker is rotated by 180 degrees when being painted. For example, in the picture below, a marker's model represents an arrow oriented from left to right.</para>

<para></para>

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleLayoutMode">

            <summary>
                <para>Lists possible layouts of an arc scale.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLayoutMode.Auto">
            <summary>
                <para><para>An arc scale appears as an ellipse whose length depends on the start and end scale angles.</para>

<para></para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLayoutMode.Circle">
            <summary>
                <para><para>An arc scale always appears as a circle that tries to occupy as much space as possible.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLayoutMode.Ellipse">
            <summary>
                <para><para>An arc scale appears as an ellipse which sides are stretched.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLayoutMode.HalfTop">
            <summary>
                <para><para>An arc scale appears as the upper half of a circle which tries to occupy as much space as possible.</para>

<para></para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLayoutMode.QuarterTopLeft">
            <summary>
                <para><para>An arc scale appears as an upper left quarter circle.</para>
<para></para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLayoutMode.QuarterTopRight">
            <summary>
                <para><para>An arc scale appears as an upper right quarter circle. </para>
<para></para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLayoutMode.ThreeQuarters">
            <summary>
                <para><para>An arc scale is displayed as a three quarter circle. </para>
<para></para>

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.ArcScaleLabelOrientation">

            <summary>
                <para>Lists the possible ways arc scale labels can be oriented.


</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLabelOrientation.BottomToTop">
            <summary>
                <para><para>Arc scale labels are painted from bottom to top.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLabelOrientation.LeftToRight">
            <summary>
                <para><para>Arc scale labels are painted from left to right.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLabelOrientation.Radial">
            <summary>
                <para><para>Arc scale labels are painted radially to the corresponding values on a scale.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLabelOrientation.Tangent">
            <summary>
                <para><para>Arc scale labels are painted tangentially to the corresponding values on a scale.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.ArcScaleLabelOrientation.TopToBottom">
            <summary>
                <para><para>Arc scale labels are painted from top to bottom.</para>

<para></para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.CircularGaugeControl">

            <summary>
                <para>A <b>Circular Gauge</b> control shipped with the DXGauges Suite.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.CircularGaugeControl.#ctor">
            <summary>
                <para>Initializes a new instance of the CircularGaugeControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeControl.ActualModel">
            <summary>
                <para>Gets the actual model used to draw elements of a Circular Gauge.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.CircularGaugeModel"/> class descendant that is the actual model.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CircularGaugeControl.ActualModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="M:DevExpress.Xpf.Gauges.CircularGaugeControl.CalcHitInfo(System.Windows.Point)">
            <summary>
                <para>Returns information on the gauge elements located at the specified point.

</para>
            </summary>
            <param name="point">
		A <see cref="T:System.Drawing.Point"/> structure which specifies the test point coordinates relative to the gauge's top-left corner.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Gauges.CircularGaugeHitInfo"/> object, which contains information about the gauge elements located at the test point.

</returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeControl.Layers">
            <summary>
                <para>Provides access to a collection of  layers contained in the circular gauge. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.CircularGaugeLayerCollection"/> object that contains circular gauge layers. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CircularGaugeControl.LayersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeControl.Model">
            <summary>
                <para>Gets or sets a model for the circular gauge control that is used to draw its elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.CircularGaugeModel"/> class descendant that is the actual model.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CircularGaugeControl.ModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeControl.PredefinedModels">
            <summary>
                <para>Returns a list of predefined models for a Circular Gauge control. 
</para>
            </summary>
            <value>A <see cref="T:System.Collections.Generic.List`1"/> of <see cref="T:DevExpress.Xpf.Gauges.PredefinedElementKind"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.CircularGaugeControl.Scales">
            <summary>
                <para>Provides access to a collection of  scales contained in the Circular gauge. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Gauges.ArcScaleCollection"/> object that contains circular gauge scales. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.CircularGaugeControl.ScalesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.MinorTickmarkOptions">

            <summary>
                <para>Contains settings that define the layout and behavior of the minor tickmarks along the scale.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Gauges.MinorTickmarkOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the MinorTickmarkOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.MinorTickmarkOptions.ShowTicksForMajor">
            <summary>
                <para>Gets or sets a value indicating whether or not minor tickmarks should be visible at the positions of the corresponding major tickmarks.



</para>
            </summary>
            <value><b>true</b> to display minor tickmarks at the major tickmark positions; otherwise <b>false</b>.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.MinorTickmarkOptions.ShowTicksForMajorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="P:DevExpress.Xpf.Gauges.MinorTickmarkOptions.ZIndex">
            <summary>
                <para>Gets or sets the z-index of minor tickmarks.
</para>
            </summary>
            <value> An integer value that is the z-index.


</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.MinorTickmarkOptions.ZIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
        <member name="T:DevExpress.Xpf.Gauges.GaugeLayerBase">

            <summary>
                <para>Serves as the base class for gauge-related layers.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Gauges.GaugeLayerBase.Options">
            <summary>
                <para>Provides access to the settings that specify the shape and position of the current gauge layer.




</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Gauges.LayerOptions"/> object that contains the settings of the layer.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Gauges.GaugeLayerBase.OptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> </returns>


        </member>
    </members>
</doc>
