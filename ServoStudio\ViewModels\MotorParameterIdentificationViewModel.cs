﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using DevExpress.Mvvm.POCO;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using ServoStudio.Models;
using System.Windows.Threading;
using System.Windows;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class MotorParameterIdentificationViewModel
    {
        #region 字段
        public bool IsInitialized = true;//是否开始
        public bool IsClosed = false;//是否结束      
        private DispatcherTimer Timer_System = new DispatcherTimer();//系统时间  

        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 属性     
        public virtual int HintVisibility { get; set; }//参数识别信息提示是否可见
        public virtual bool ModifyParameterButtonEnabled { get; set; }//更换参数按钮是否可用
        public virtual string ParameterIdentificationSwitchHint { get; set; }//电机参数按钮切换
        public virtual bool SelectedWindingResistance { get; set; }//线电阻
        public virtual bool SelectedWindingInductance { get; set; }//线电感
        public virtual bool SelectedLineUVWSequence { get; set; }//线序
        public virtual bool SelectedAbsEncoderSingleTurnBit { get; set; }//单线圈分辨率位数
        public virtual bool SelectedMotorPolePairsNumber { get; set; }//极对数
        public virtual bool SelectedAbsEncoderOffset { get; set; }//绝对式编码器偏置

        public virtual string WindingResistance { get; set; }//线电阻
        public virtual string WindingInductance { get; set; }//线电感
        public virtual string LineUVWSequence { get; set; }//线序
        public virtual string AbsEncoderSingleTurnBit { get; set; }//单线圈分辨率位数
        public virtual string MotorPolePairsNumber { get; set; }//极对数
        public virtual string AbsEncoderOffset { get; set; }//绝对式编码器偏置

        public virtual string WindingResistance_Identification { get; set; }//识别线电阻
        public virtual string WindingInductance_Identification { get; set; }//识别线电感
        public virtual string LineUVWSequence_Identification { get; set; }//识别线序
        public virtual string AbsEncoderSingleTurnBit_Identification { get; set; }//识别单线圈分辨率位数
        public virtual string MotorPolePairsNumber_Identification { get; set; }//识别极对数
        public virtual string AbsEncoderOffset_Identification { get; set; }//绝对式编码器偏置

        public virtual int WindingResistance_Color { get; set; }//线电阻
        public virtual int WindingInductance_Color { get; set; }//线电感
        public virtual int LineUVWSequence_Color { get; set; }//线序
        public virtual int AbsEncoderSingleTurnBit_Color { get; set; }//单线圈分辨率位数
        public virtual int MotorPolePairsNumber_Color { get; set; }//极对数
        public virtual int AbsEncoderOffset_Color { get; set; }//绝对式编码器偏置
        #endregion

        #region 构造函数
        public MotorParameterIdentificationViewModel()
        {
            ViewModelSet.MotorParameterIdentification = this;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：MotorParameterIdentificationLoaded
        //函数功能：界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.27
        //*************************************************************************
        public void MotorParameterIdentificationLoaded()
        {
            //时钟初始化
            TimerInitialize();

            //标志位初始化
            RefreshParameterIdentificationFlag(Status: "Initialized");
         
            //读识别前参数
            ReadMotorParameterIdentification(IsListen: false);                       
        }

        //*************************************************************************
        //函数名称：MotorParameterIdentificationUnloaded
        //函数功能：界面退出
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.04
        //*************************************************************************
        public void MotorParameterIdentificationUnloaded()
        {

            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要写入的数据字典
            AddParameterInfoDictionary(ref dicParameterInfo);

            //获取参数详细信息
            OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MOTORPARAMETERIDENTIFICATION, TaskName.ParameterIdentificationUnload, lstTransmittingDataInfo);
        }

        //*************************************************************************
        //函数名称：ParameterIdentificationSwitch
        //函数功能：参数辨识使能禁能
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.16
        //*************************************************************************
        public void ParameterIdentificationSwitch()
        {
            if (IsInitialized)
            {
                OthersHelper.OperatingControl("Operating Mode", "4102", TaskName.ParameterIdentificationOperatingMode, PageName.MOTORPARAMETERIDENTIFICATION);
            }
            else
            {
                OthersHelper.OperatingControl("Operating Setting", "4", TaskName.ParameterIdentificationSwitch, PageName.MOTORPARAMETERIDENTIFICATION);
            }
        }

        //*************************************************************************
        //函数名称：MotorParameterIdentificationModify
        //函数功能：电机参数识别替换
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.04
        //*************************************************************************
        public void MotorParameterIdentificationModify()
        {
            if (SelectedAbsEncoderOffset)
            {
                MotorParameterIdentificationSet.AbsEncoderOffset = AbsEncoderOffset_Identification;
            }

            if (SelectedAbsEncoderSingleTurnBit)
            {
                MotorParameterIdentificationSet.AbsEncoderSingleTurnBit = AbsEncoderSingleTurnBit_Identification;
            }

            if (SelectedMotorPolePairsNumber)
            {
                MotorParameterIdentificationSet.MotorPolePairsNumber = MotorPolePairsNumber_Identification;
            }

            if (SelectedWindingInductance)
            {
                MotorParameterIdentificationSet.WindingInductance = WindingInductance_Identification;
            }

            if (SelectedWindingResistance)
            {
                MotorParameterIdentificationSet.WindingResistance = WindingResistance_Identification;
            }

            if (SelectedLineUVWSequence)
            {
                if (LineUVWSequence_Identification == "动力线相序UVW")
                {
                    MotorParameterIdentificationSet.LineUVWSequence = "0";
                }
                else
                {
                    MotorParameterIdentificationSet.LineUVWSequence = "1";
                }
            }
         
            //状态变更
            RefreshParameterIdentificationFlag(Status: "Modified");
        }

        //*************************************************************************
        //函数名称：RefreshParameterIdentificationFlag
        //函数功能：更新ParameterIdentification控制标志位
        //
        //输入参数：string Status    当前状态
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.16
        //*************************************************************************
        public void RefreshParameterIdentificationFlag(string Status)
        {         
            if (Status == "Initialized")//初始化
            {
                CheckBoxInitialized();
                Timer_System.IsEnabled = false;//时钟关

                MotorParameterIdentificationSet.IsCallBackOn = false;//监听回调关
                MotorParameterIdentificationSet.IsIndentification = false;//没有完成参数辨识   

                ModifyParameterButtonEnabled = false;
                HintVisibility = ControlVisibility.Hidden;
                ParameterIdentificationSwitchHint = "辨识开始";
            }
            else if (Status == "Switched")//辨识开始关闭
            {
                string strActualEnable = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Actual Enable", "Index"));
                if (strActualEnable == "1")
                {                   
                    if (IsClosed)
                    {
                        OthersHelper.OperatingControl("Operating Setting", "4", TaskName.ParameterIdentificationSwitch, PageName.MOTORPARAMETERIDENTIFICATION);
                    }
                    else
                    {
                        Timer_System.IsEnabled = true;//时钟开

                        MotorParameterIdentificationSet.IsCallBackOn = true;//监听回调开
                        MotorParameterIdentificationSet.IsIndentification = false;//没有完成参数辨识   

                        ModifyParameterButtonEnabled = false;
                        HintVisibility = ControlVisibility.Visible;
                        ParameterIdentificationSwitchHint = "辨识结束";

                        ShowNotification_CrossThread(2004);
                    }
                }
                else
                {                  
                    if (IsClosed)
                    {
                        OthersHelper.OperatingControl("Operating End", "1", TaskName.ParameterIdentificationOperatingEnd, PageName.MOTORPARAMETERIDENTIFICATION);
                    }
                    else
                    {
                        Timer_System.IsEnabled = false;//时钟关

                        MotorParameterIdentificationSet.IsCallBackOn = false;//监听回调关
                        MotorParameterIdentificationSet.IsIndentification = false;//没有完成参数辨识   

                        ModifyParameterButtonEnabled = false;
                        HintVisibility = ControlVisibility.Hidden;
                        ParameterIdentificationSwitchHint = "辨识开始";

                        ShowNotification_CrossThread(2005);
                    }
                }
            }
            else if (Status == "Completed")
            {
                Timer_System.IsEnabled = false;//时钟关

                MotorParameterIdentificationSet.IsCallBackOn = false;//监听回调关
                MotorParameterIdentificationSet.IsIndentification = true;//完成参数辨识   

                ModifyParameterButtonEnabled = true;
                HintVisibility = ControlVisibility.Hidden;
                ParameterIdentificationSwitchHint = "辨识开始";

                ShowNotification_CrossThread(2006);
            }
            else if (Status == "Modified")
            {
                ModifyParameterButtonEnabled = false;
                HintVisibility = ControlVisibility.Hidden;
                ParameterIdentificationSwitchHint = "辨识开始";

                ShowNotification_CrossThread(2007);
            }
            else if (Status == "Unloaded")
            {
                Timer_System.IsEnabled = false;
            }
        }

        //*************************************************************************
        //函数名称：CallBackForListenIdentification
        //函数功能：回调函数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.11.30
        //*************************************************************************
        public void CallBackForListenIdentification()
        {
            if (MotorParameterIdentificationSet.IsCallBackOn)//监听回调开
            {
                //是否完成参数识别
                string strIsComplete = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Parameter Identification State", "Index"));
                if (strIsComplete == "1")
                {
                    RefreshParameterIdentificationFlag(Status: "Completed");

                    //获取参数辨识参数
                    ReadMotorParameterIdentification(IsListen: false);
                }
            }
        }
    
        //*************************************************************************
        //函数名称：EvaluationMotorParameterIdentification
        //函数功能：回调函数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.27
        //*************************************************************************
        public void EvaluationMotorParameterIdentification()
        {             
            if (!MotorParameterIdentificationSet.IsIndentification) //没有参数识别
            {
                //识别前赋值
                WindingResistance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Resistance", "Index"));//额定功率     
                WindingInductance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Inductance", "Index"));//额定功率     
                LineUVWSequence = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Line UVW Sequence", "Index"));//额定功率    
                AbsEncoderSingleTurnBit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Single-Turn Bit", "Index"));//多圈值分辨率位数   
                MotorPolePairsNumber = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Pole Pairs Number", "Index"));//极对数  
                AbsEncoderOffset = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Offset", "Index"));//绝对式编码器偏置

                if (LineUVWSequence == "0")
                {
                    LineUVWSequence = "动力线相序UVW";
                }
                else
                {
                    LineUVWSequence = "动力线相序WVU";
                }

                MotorParameterIdentificationSet.WindingResistance = WindingResistance;
                MotorParameterIdentificationSet.WindingInductance = WindingInductance;
                MotorParameterIdentificationSet.LineUVWSequence = LineUVWSequence;
                MotorParameterIdentificationSet.AbsEncoderOffset = AbsEncoderOffset;
                MotorParameterIdentificationSet.MotorPolePairsNumber = MotorPolePairsNumber;
                MotorParameterIdentificationSet.AbsEncoderSingleTurnBit = AbsEncoderSingleTurnBit;           
            } 
            else//参数识别
            {
                //识别后赋值与字体颜色控制                   
                WindingResistance_Identification = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Resistance", "Index"));//额定功率     
                WindingInductance_Identification = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Winding Inductance", "Index"));//额定功率     
                LineUVWSequence_Identification = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Line UVW Sequence", "Index"));//额定功率 
                AbsEncoderSingleTurnBit_Identification = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Single-Turn Bit", "Index"));//多圈值分辨率位数   
                MotorPolePairsNumber_Identification = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Pole Pairs Number", "Index"));//极对数  
                AbsEncoderOffset_Identification = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Offset", "Index"));//绝对式编码器偏置

                if (LineUVWSequence_Identification == "0")
                {
                    LineUVWSequence_Identification = "动力线相序UVW";
                }
                else
                {
                    LineUVWSequence_Identification = "动力线相序WVU";
                }

                //字体颜色提示
                ColorControl();

                //识别标志位初始
                MotorParameterIdentificationSet.IsIndentification = false;
            }         
        }
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：CheckBoxInitialized
        //函数功能：控件属性值初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.18
        //*************************************************************************
        private void CheckBoxInitialized()
        {
            SelectedWindingResistance = false;
            SelectedWindingInductance = false;
            SelectedLineUVWSequence = false;
            SelectedAbsEncoderSingleTurnBit = false;
            SelectedMotorPolePairsNumber = false;
            SelectedAbsEncoderOffset = false;
        }

        //*************************************************************************
        //函数名称：ColorControl
        //函数功能：颜色管理
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.23
        //*************************************************************************
        private void ColorControl()
        {          
            if (WindingResistance == WindingResistance_Identification)
            {
                WindingResistance_Color = BackgroundState.Green;
            }
            else
            {
                WindingResistance_Color = BackgroundState.Red;
            }

            if (WindingInductance == WindingInductance_Identification)
            {
                WindingInductance_Color = BackgroundState.Green;
            }
            else
            {
                WindingInductance_Color = BackgroundState.Red;
            }

            if (LineUVWSequence == LineUVWSequence_Identification)
            {
                LineUVWSequence_Color = BackgroundState.Green;
            }
            else
            {
                LineUVWSequence_Color = BackgroundState.Red;
            }     
            
            if (MotorPolePairsNumber == MotorPolePairsNumber_Identification)
            {
                MotorPolePairsNumber_Color = BackgroundState.Green;
            }   
            else
            {
                MotorPolePairsNumber_Color = BackgroundState.Red;
            }

            if (AbsEncoderSingleTurnBit == AbsEncoderSingleTurnBit_Identification)
            {
                AbsEncoderSingleTurnBit_Color = BackgroundState.Green;
            }
            else
            {
                AbsEncoderSingleTurnBit_Color = BackgroundState.Red;
            }

            if (AbsEncoderOffset == AbsEncoderOffset_Identification)
            {
                AbsEncoderOffset_Color = BackgroundState.Green;
            }
            else
            {
                AbsEncoderOffset_Color = BackgroundState.Red;
            }
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        private void ShowNotification_CrossThread(int In_iType)
        {
            WindowSet.clsMainWindow?.ShowNotification(In_iType);
        }

        //*************************************************************************
        //函数名称：TimerInitialize
        //函数功能：时钟初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.04
        //*************************************************************************
        private void TimerInitialize()
        {
            Timer_System.Interval = TimeSpan.FromMilliseconds(TimerPeriod.MotorParameterIdentification);
            Timer_System.Tick += Timer_System_Tick;
            Timer_System.IsEnabled = false;
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.27
        //*************************************************************************
        private void AddParameterInfoDictionary(bool IsListen, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            if (IsListen)
            {
                dicParameterInfo.Add("Motor Parameter Identification State", null);
            }
            else
            {
                dicParameterInfo.Add("Motor Winding Resistance", null);
                dicParameterInfo.Add("Motor Winding Inductance", null);
                dicParameterInfo.Add("Motor Line UVW Sequence", null);
                dicParameterInfo.Add("Abs Encoder Single-Turn Bit", null);
                dicParameterInfo.Add("Motor Pole Pairs Number", null);
                dicParameterInfo.Add("Abs Encoder Offset", null);
            }                 
        }
        private void AddParameterInfoDictionary(ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            dicParameterInfo.Add("Abs Encoder Single-Turn Bit", MotorParameterIdentificationSet.AbsEncoderSingleTurnBit);
            dicParameterInfo.Add("Motor Pole Pairs Number", MotorParameterIdentificationSet.MotorPolePairsNumber);
            dicParameterInfo.Add("Abs Encoder Offset", MotorParameterIdentificationSet.AbsEncoderOffset);
            dicParameterInfo.Add("Motor Winding Resistance", MotorParameterIdentificationSet.WindingResistance);
            dicParameterInfo.Add("Motor Winding Inductance", MotorParameterIdentificationSet.WindingInductance);

            if (MotorParameterIdentificationSet.LineUVWSequence == "动力线相序UVW")
            {
                dicParameterInfo.Add("Motor Line UVW Sequence", "0");
            }
            else
            {
                dicParameterInfo.Add("Motor Line UVW Sequence", "1");
            }                        
        }

        //*************************************************************************
        //函数名称：ReadMotorParameterIdentification
        //函数功能：读任务
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.27
        //*************************************************************************
        private int ReadMotorParameterIdentification(bool IsListen)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();
         
            try
            {
                //获取要读取的数据字典
                AddParameterInfoDictionary(IsListen, ref dicParameterInfo);

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    return RET.ERROR;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                if (IsListen)
                {
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.ParameterIdentificationListen, lstTransmittingDataInfo);
                }
                else
                {
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.ParameterIdentification, lstTransmittingDataInfo);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.IDENTIFICATION_READ, "ReadMotorParameterIdentification", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：Timer_System_Tick
        //函数功能：时钟
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.04
        //*************************************************************************
        private void Timer_System_Tick(object sender, EventArgs e)
        {
            //监听是否参数辨识完成
            ReadMotorParameterIdentification(IsListen: true);           
        }
        #endregion
    }
}