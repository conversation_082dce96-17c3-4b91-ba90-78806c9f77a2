﻿using ServoStudio.GlobalConstant;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Windows.Data;

namespace Converter
{
    class ContentConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                switch ((int)value)
                {
                    case ConnectState.NotConnect:
                        return "没有建立通信";
                    case ConnectState.Connect:
                        return "通信正常";
                    case ConnectState.Stop:
                        return "通信暂停";
                    case ConnectState.Error:
                        return "通信异常";
                    default:
                        return "通信异常";
                }
            }
            catch
            {
                return "通信异常";
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
