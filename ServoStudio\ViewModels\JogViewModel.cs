﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using ServoStudio.Models;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class JogViewModel
    {
        #region 字段
        public bool IsInitialized = true;
        public bool IsClosed = false;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 属性
        public virtual string JogSpeed { get; set; }//Jog点动速度
        public virtual string JogAccelerationTime { get; set; }//Jog加速时间
        public virtual string JogDecelerationTime { get; set; }//Jog减速时间
        public virtual string JogSwitchHint { get; set; }//Jog模块使能、禁能提示
        public virtual bool IsJogButtonEnabled { get; set; }//Jog按钮属性
        #endregion

        #region 构造函数
        public JogViewModel()
        {
            ViewModelSet.Jog = this;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：JogLoaded
        //函数功能：Jog界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void JogLoaded()
        {          
            //获取JOG参数
            ReadJogParameter();

            //Jog参数初始化
            RefreshJogFlag(Status: "JogInitialize");    
        }

        //*************************************************************************
        //函数名称：JogUnloaded
        //函数功能：Jog界面退出
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.08
        //*************************************************************************
        public void JogUnloaded()
        {
            //判断内部是否使能，若使能先禁能，再退出
            OthersHelper.GetActualEnableStatus(TaskName.JogActualEnable);
            IsClosed = true;          
        }

        //*************************************************************************
        //函数名称：ReadJogParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void ReadJogParameter()
        {
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要读取的数据字典
            AddParameterInfoDictionary(null, ref dicParameterInfo);

            //获取参数详细信息
            OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.JOG, TaskName.Jog, lstTransmittingDataInfo);      
        }

        //*************************************************************************
        //函数名称：EvaluationJogParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.15
        //*************************************************************************
        public void EvaluationJogParameter()
        {             
            JogSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Speed", "Index"));
            JogAccelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Acceleration Time", "Index"));
            JogDecelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Deceleration Time", "Index"));          
        }

        //*************************************************************************
        //函数名称：RefreshJogFlag
        //函数功能：更新Jog控制标志位
        //
        //输入参数：string Status    当前Jog状态
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.10.21
        //*************************************************************************
        public void RefreshJogFlag(string Status)
        {
            string strActualEnable = null;

            if (Status == "JogInitialize") 
            {
                JogSet.Direction = null;
                JogSet.IsContinuous = false;

                JogSwitchHint = "伺服使能";
                IsJogButtonEnabled = false;
            }
            else if (Status == "JogSwitch")
            {
                strActualEnable = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Actual Enable", "Index"));
                if (strActualEnable == "1")
                {               
                    if (IsClosed)
                    {
                        OthersHelper.OperatingControl("Operating Setting", "4", TaskName.JogSwitch, PageName.JOG);
                    }
                    else
                    {
                        JogSwitchHint = "伺服禁能";
                        IsJogButtonEnabled = true;

                        ShowNotification_CrossThread(2004);
                    }
                }
                else
                {                 
                    if (IsClosed)
                    {
                        OthersHelper.OperatingControl("Operating End", "1", TaskName.JogOperatingEnd, PageName.JOG);
                    }
                    else
                    {
                        JogSwitchHint = "伺服使能";
                        IsJogButtonEnabled = false;

                        ShowNotification_CrossThread(2005);
                    }
                }
            }
            else if (Status == "JogStop")
            {
                JogSet.Direction = null;
                JogSet.IsContinuous = false;
            }
            else//JogRun
            {
                JogSet.Direction = Status;
                JogSet.IsContinuous = true;
            }           
        }

        //*************************************************************************
        //函数名称：JogModelSwitch
        //函数功能：JOG模块使能禁能
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.23
        //*************************************************************************
        public void JogSwitch()
        {
            if (IsInitialized)
            {
                OthersHelper.OperatingControl("Operating Mode", "4098", TaskName.JogOperatingMode, PageName.JOG);
            }
            else
            {
                OthersHelper.OperatingControl("Operating Setting", "4", TaskName.JogSwitch, PageName.JOG);
            }               
        }

        //*************************************************************************
        //函数名称：JogRun
        //函数功能：JOG开启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.25
        //*************************************************************************
        public void JogRun(string strIndex)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要写入的数据字典
            AddParameterInfoDictionary(strIndex, ref dicParameterInfo);

            //获取参数详细信息
            OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);

            //判断输入参数是否正确
            iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
            if (iRet == RET.NO_EFFECT)
            {
                ShowNotification(2008);
                return;
            }
            else if (iRet == RET.ERROR)
            {
                return;
            }

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.JOG, TaskName.JogContinuously, lstTransmittingDataInfo);

            //JogSet设置
            RefreshJogFlag(strIndex);        
        }

        //*************************************************************************
        //函数名称：JogStop
        //函数功能：JOG关闭
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.25
        //*************************************************************************
        public void JogStop()
        {
            RefreshJogFlag(Status: "JogStop");
        }
        #endregion

        #region 私有方法 
        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private void AddParameterInfoDictionary(string strDirection, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            dicParameterInfo.Add("Jog Speed", JogSpeed);
            dicParameterInfo.Add("Jog Acceleration Time", JogAccelerationTime);
            dicParameterInfo.Add("Jog Deceleration Time", JogDecelerationTime);
            dicParameterInfo.Add("Operating Setting", strDirection);
        }
    
        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        private void ShowNotification_CrossThread(int In_iType)
        {
            WindowSet.clsMainWindow?.ShowNotification(In_iType);
        }
        #endregion
    }
}