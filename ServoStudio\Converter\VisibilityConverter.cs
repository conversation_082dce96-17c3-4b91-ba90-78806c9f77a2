﻿using ServoStudio.GlobalConstant;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Data;

namespace Converter
{
    public class VisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                switch ((int)value)
                {
                    case ControlVisibility.Hidden:
                        return Visibility.Hidden;

                    case ControlVisibility.Visible:
                        return Visibility.Visible;

                    case ControlVisibility.Collapsed:
                        return Visibility.Collapsed;

                    default:
                        return Visibility.Visible;
                }
            }
            catch
            {
                return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
