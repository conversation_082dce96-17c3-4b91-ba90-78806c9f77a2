﻿<UserControl x:Class="ServoStudio.Views.CurrentLoopView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:converter="clr-namespace:Converter"
             xmlns:Views="clr-namespace:ServoStudio.Views"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:CurrentLoopViewModel}"
             d:DesignHeight="800" d:DesignWidth="1300">
    <UserControl.Resources>
        <Style x:Key="myTabItem" TargetType="dx:DXTabItem">
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Trigger.Setters>
                        <Setter Property="FontStyle" Value="Italic"/>
                        <Setter Property="Foreground" Value="Orange"/>
                    </Trigger.Setters>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style  x:Key="myCheckEdit" TargetType="dxe:CheckEdit">
            <Style.Triggers>
                <Trigger Property="IsChecked" Value="True">
                    <Trigger.Setters>
                        <Setter Property="Foreground" Value="Green"/>
                    </Trigger.Setters>
                </Trigger>
            </Style.Triggers>
        </Style>

        <converter:BackgroundConverter x:Key="BackgroudConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>
        <converter:ContentConverter x:Key="ContentConverter"/>
    </UserControl.Resources>

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding CurrentLoopLoadedCommand}"/>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields" UseWin8NotificationsIfAvailable="True"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
        
        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="240"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Label Grid.Row="0" Margin="3" Style="{StaticResource LabelStyle}" Content="电流环图示 — 点击图例可设定相应参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Canvas Grid.Row="1">
                <Line Canvas.Top="111" Canvas.Left="38" Style="{StaticResource Line}"/>
                <Ellipse Canvas.Top="105" Canvas.Left="26" Style="{StaticResource StartDot}"/>
                <Line Canvas.Top="111" Canvas.Left="67" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="111" Canvas.Left="67" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="76" Canvas.Top="93" Name="LPF1" Content="一阶低通滤波" Style="{StaticResource BackgroundSwitch}" FontSize="10.667">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ShowCurrentLoopTabItemCommand}" CommandParameter="{Binding Content, ElementName=LPF1}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Canvas.Top="111" Canvas.Left="151" Style="{StaticResource Line}"/>
                <Line Canvas.Top="111" Canvas.Left="180" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="111" Canvas.Left="180" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="375" Canvas.Top="93" Name="Notch" Content="陷波滤波器" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ShowCurrentLoopTabItemCommand}" CommandParameter="{Binding Content, ElementName=Notch}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Canvas.Top="111" Canvas.Left="337" Style="{StaticResource Line}"/>
                <Line Canvas.Top="111" Canvas.Left="366" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="111" Canvas.Left="366" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="489" Canvas.Top="93" Name="Torque" Content="转矩限制" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ShowCurrentLoopTabItemCommand}" CommandParameter="{Binding Content, ElementName=Torque}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Canvas.Top="111" Canvas.Left="451" Style="{StaticResource Line}"/>
                <Line Canvas.Top="111" Canvas.Left="480" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="111" Canvas.Left="480" Style="{StaticResource RDArrow}"/>

                <Line Canvas.Top="111" Canvas.Left="639" Style="{StaticResource Line}"/>
                <Line Canvas.Top="111" Canvas.Left="666" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="111" Canvas.Left="666" Style="{StaticResource RDArrow}"/>
                <Label Content="PI控制器" Style="{StaticResource Function}" Canvas.Left="675" Canvas.Top="89"/>

                <Line Y2="70" Canvas.Top="129" Canvas.Left="621" Style="{StaticResource VerticalLine}"/>
                <Line Canvas.Top="137" Canvas.Left="621" Style="{StaticResource ULArrow}"/>
                <Line Canvas.Top="137" Canvas.Left="621" Style="{StaticResource URArrow}"/>

                <Line Canvas.Top="106" Canvas.Left="736" Style="{StaticResource Line}"/>
                <Ellipse Canvas.Top="101" Canvas.Left="774" Style="{StaticResource EndDot}"/>
                <Label Content="电压指令" Canvas.Left="756" Canvas.Top="112"/>


                <Ellipse Canvas.Top="93" Canvas.Left="603" Style="{StaticResource OperatorProfile}"/>

                <Label Content="转矩指令" Canvas.Left="7" Canvas.Top="117"/>

                <Ellipse Canvas.Top="193" Canvas.Left="732" Style="{StaticResource StartDot}"/>
                <Line X2="110" Canvas.Top="199" Canvas.Left="621" Style="{StaticResource Line}"/>
                <Label Content="电流反馈" Canvas.Left="714" Canvas.Top="205"/>

                <Label Content="+" Canvas.Left="583" Canvas.Top="107" Style="{StaticResource Operator}"/>
                <Label Content="-" Canvas.Left="611" Canvas.Top="129" Style="{StaticResource Operator}"/>
                <Label Content="Σ" Canvas.Left="616" Canvas.Top="97" Style="{StaticResource Operator}"/>
                <Line Canvas.Top="106" Canvas.Left="765" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="106" Canvas.Left="765" Style="{StaticResource RDArrow}"/>
                <Line X2="168"  Canvas.Top="30" Canvas.Left="38" Style="{StaticResource Line}"/>
                <Ellipse Canvas.Top="24" Canvas.Left="26" Style="{StaticResource StartDot}"/>
                <Label Content="转矩前馈" Canvas.Left="7" Canvas.Top="36"/>
                <Ellipse Canvas.Top="94" Canvas.Left="189" Style="{StaticResource OperatorProfile}"/>
                <Label Content="Σ" Canvas.Left="202" Canvas.Top="98" Style="{StaticResource Operator}"/>
                <Line Canvas.Top="111" Canvas.Left="224" Style="{StaticResource Line}"/>
                <Line Canvas.Top="111" Canvas.Left="253" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="111" Canvas.Left="253" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="262" Canvas.Top="93" x:Name="LPF2" Content="二阶低通滤波" Style="{StaticResource BackgroundSwitch}" FontSize="10.667">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ShowCurrentLoopTabItemCommand}" CommandParameter="{Binding Content, ElementName=LPF2}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>
                <Line Canvas.Top="111" Canvas.Left="565" Style="{StaticResource Line}"/>
                <Line Canvas.Top="111" Canvas.Left="594" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="111" Canvas.Left="594" Style="{StaticResource RDArrow}"/>
                <Line Y2="63" Canvas.Top="30" Canvas.Left="206" Style="{StaticResource VerticalLine}"/>
                <Line Canvas.Top="93" Canvas.Left="206" Style="{StaticResource DLArrow}"/>
                <Line Canvas.Top="93" Canvas.Left="206" Style="{StaticResource DRArrow}"/>
                <Label Content="+" Canvas.Left="169" Canvas.Top="107" Style="{StaticResource Operator}"/>
                <Label Content="+" Canvas.Left="190" Canvas.Top="66" Style="{StaticResource Operator}"/>
            </Canvas>

            <Grid Grid.Row="3" Margin="3">
                <TabControl Padding="0" SelectedIndex="{Binding SelectedTabIndex,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" BorderBrush="LightGray" Background="{x:Null}" BorderThickness="0,1,0,0">
                    <TabItem Header="低通滤波器" TabIndex="0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="15"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="180"/>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="180"/>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="第一转矩指令滤波时间参数" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding FirstTrqcmdFilterTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding FirstTrqcmdFilterTimeBackground,Converter={StaticResource BackgroudConverter}}"/>
                            <TextBox Grid.Row="1" Grid.Column="3" Text="0.01ms" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding FirstTrqcmdFilterTimeBackground,Converter={StaticResource BackgroudConverter}}"/>

                            <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="第二转矩指令滤波器频率" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding SecondTrqcmdFilterFreq,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding SecondTrqcmdFilterFreqBackground,Converter={StaticResource BackgroudConverter}}"/>
                            <TextBox Grid.Row="2" Grid.Column="3" Text="Hz" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding SecondTrqcmdFilterFreqBackground,Converter={StaticResource BackgroudConverter}}"/>

                            <Label Grid.Row="2" Grid.Column="5" Margin="10,9" Content="第二转矩指令滤波器Q值" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding SecondTrqcmdFilterQ,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding SecondTrqcmdFilterQBackground,Converter={StaticResource BackgroudConverter}}"/>

                            <Label Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="3"  Margin="10,9" Content="提示：第二转矩指令滤波器频率为5000，表示不使用第二转矩" Foreground="Red"  Style="{StaticResource LabelStyle}" />
                        </Grid>
                    </TabItem>

                    <TabItem Header="陷波滤波器" TabIndex="1">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="15"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="5"/>
                                <ColumnDefinition Width="130"/>
                                <ColumnDefinition Width="129"/>
                                <ColumnDefinition Width="50"/>

                                <ColumnDefinition Width="5"/>

                                <ColumnDefinition Width="130"/>
                                <ColumnDefinition Width="129"/>
                                <ColumnDefinition Width="50"/>

                                <ColumnDefinition Width="5"/>

                                <ColumnDefinition Width="130"/>
                                <ColumnDefinition Width="129"/>
                                <ColumnDefinition Width="50"/>

                                <ColumnDefinition Width="5"/>

                                <ColumnDefinition Width="130"/>
                                <ColumnDefinition Width="129"/>
                                <ColumnDefinition Width="50"/>

                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <dxe:CheckEdit Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2"  Margin="10,9" IsChecked="{Binding NotchFilter1Switch, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" Content="{Binding NotchFilter1Content}" Style="{StaticResource myCheckEdit}"/>
                            <dxe:CheckEdit Grid.Row="1" Grid.Column="5" Grid.ColumnSpan="2"  Margin="10,9" IsChecked="{Binding NotchFilter2Switch, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" Content="{Binding NotchFilter2Content}" Style="{StaticResource myCheckEdit}"/>
                            <dxe:CheckEdit Grid.Row="1" Grid.Column="9" Grid.ColumnSpan="2"  Margin="10,9" IsChecked="{Binding NotchFilter3Switch, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" Content="{Binding NotchFilter3Content}" Style="{StaticResource myCheckEdit}"/>
                            <dxe:CheckEdit Grid.Row="1" Grid.Column="13" Grid.ColumnSpan="2"  Margin="10,9" IsChecked="{Binding NotchFilter4Switch, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" Content="{Binding NotchFilter4Content}" Style="{StaticResource myCheckEdit}"/>

                            <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Height="38" Content="第一陷波&#x0a;滤波器频率" Style="{StaticResource LabelStyle}" IsEnabled="{Binding NotchFilter1Switch}" VerticalAlignment="Center" />
                            <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterFrequency1,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding NotchFilter1Switch}"/>
                            <TextBox Grid.Row="2" Grid.Column="3" Text="Hz" Style="{StaticResource TextBoxStyle_Unit}" IsEnabled="{Binding NotchFilter1Switch}"/>

                            <Label Grid.Row="3" Grid.Column="1" Margin="10,9" Content="第一陷波&#x0a;滤波器Q值" Style="{StaticResource LabelStyle}" IsEnabled="{Binding NotchFilter1Switch}"/>
                            <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterQFactor1,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding NotchFilter1Switch}"/>

                            <Label Grid.Row="4" Grid.Column="1" Margin="10,9" Content="第一陷波&#x0a;滤波器深度" Style="{StaticResource LabelStyle}" IsEnabled="{Binding NotchFilter1Switch}"/>
                            <TextBox Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterDepth1,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding NotchFilter1Switch}"/>

                            <Label Grid.Row="2" Grid.Column="5" Margin="10,9" Content="第二陷波&#x0a;滤波器频率" Style="{StaticResource LabelStyle}" IsEnabled="{Binding NotchFilter2Switch}" />
                            <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterFrequency2,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding NotchFilter2Switch}"/>
                            <TextBox Grid.Row="2" Grid.Column="7" Text="Hz" Style="{StaticResource TextBoxStyle_Unit}" IsEnabled="{Binding NotchFilter2Switch}"/>

                            <Label Grid.Row="3" Grid.Column="5" Margin="10,9" Content="第二陷波&#x0a;滤波器Q值" Style="{StaticResource LabelStyle}" IsEnabled="{Binding NotchFilter2Switch}"/>
                            <TextBox Grid.Row="3" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterQFactor2,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding NotchFilter2Switch}"/>

                            <Label Grid.Row="4" Grid.Column="5" Margin="10,9" Content="第二陷波&#x0a;滤波器深度" Style="{StaticResource LabelStyle}" IsEnabled="{Binding NotchFilter2Switch}"/>
                            <TextBox Grid.Row="4" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterDepth2,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding NotchFilter2Switch}"/>

                            <Label Grid.Row="2" Grid.Column="9" Margin="10,9" Content="第三陷波&#x0a;滤波器频率" Style="{StaticResource LabelStyle}" IsEnabled="{Binding NotchFilter3Switch}" />
                            <TextBox Grid.Row="2" Grid.Column="10" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterFrequency3,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"  IsEnabled="{Binding NotchFilter3Switch}"/>
                            <TextBox Grid.Row="2" Grid.Column="11" Text="Hz" Style="{StaticResource TextBoxStyle_Unit}"  IsEnabled="{Binding NotchFilter3Switch}"/>

                            <Label Grid.Row="3" Grid.Column="9" Margin="10,9" Content="第三陷波&#x0a;滤波器Q值" Style="{StaticResource LabelStyle}"  IsEnabled="{Binding NotchFilter3Switch}"/>
                            <TextBox Grid.Row="3" Grid.Column="10" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterQFactor3,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"  IsEnabled="{Binding NotchFilter3Switch}"/>

                            <Label Grid.Row="4" Grid.Column="9" Margin="10,9" Content="第三陷波&#x0a;滤波器深度" Style="{StaticResource LabelStyle}"  IsEnabled="{Binding NotchFilter3Switch}"/>
                            <TextBox Grid.Row="4" Grid.Column="10" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterDepth3,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"  IsEnabled="{Binding NotchFilter3Switch}"/>

                            <Label Grid.Row="2" Grid.Column="13" Margin="10,9" Content="第四陷波&#x0a;滤波器频率" Style="{StaticResource LabelStyle}"  IsEnabled="{Binding NotchFilter4Switch}"/>
                            <TextBox Grid.Row="2" Grid.Column="14" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterFrequency4,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding NotchFilter4Switch}"/>
                            <TextBox Grid.Row="2" Grid.Column="15" Text="Hz" Style="{StaticResource TextBoxStyle_Unit}" IsEnabled="{Binding NotchFilter4Switch}"/>

                            <Label Grid.Row="3" Grid.Column="13" Margin="10,9" Content="第四陷波&#x0a;滤波器Q值" Style="{StaticResource LabelStyle}" IsEnabled="{Binding NotchFilter4Switch}" />
                            <TextBox Grid.Row="3" Grid.Column="14" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterQFactor4,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding NotchFilter4Switch}"/>

                            <Label Grid.Row="4" Grid.Column="13" Margin="10,9" Content="第四陷波&#x0a;滤波器深度" Style="{StaticResource LabelStyle}" IsEnabled="{Binding NotchFilter4Switch}"/>
                            <TextBox Grid.Row="4" Grid.Column="14" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding NotchFilterDepth4,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding NotchFilter4Switch}"/>
                        </Grid>
                    </TabItem>

                    <TabItem Header="转矩限制" TabIndex="2">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="15"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="260"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="260"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="正转内部转矩限制值" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="1" Grid.Column="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ForwardInternalTorqueLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>

                            <Label Grid.Row="1" Grid.Column="4" Margin="10,9" Content="反转内部转矩限制值" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="1" Grid.Column="5" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ReverseInternalTorqueLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>

                            <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="正转外部转矩限制值" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="2" Grid.Column="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ForwardExternalTorqueLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>

                            <Label Grid.Row="2" Grid.Column="4" Margin="10,9" Content="反转外部转矩限制值" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="2" Grid.Column="5" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ReverseExternalTorqueLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>

                        </Grid>
                    </TabItem>
                </TabControl>
            </Grid>

            <Label Grid.Row="4" Margin="3,3,3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <StackPanel Grid.Row="5" HorizontalAlignment="Right" Orientation="Horizontal">
                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadCurrentLoopParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                    <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultCurrentLoopParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                    <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" Command="{Binding WriteCurrentLoopParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                    <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>
            </StackPanel>

            <!--<Grid Grid.Row="7">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="1" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetCurrentLoopConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="2" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" />

            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="3" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SaveCurrentLoopConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="4" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}"/>
        </Grid>-->
        </Grid>
        
    </ScrollViewer>
  
</UserControl>
