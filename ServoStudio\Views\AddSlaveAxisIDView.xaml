﻿<UserControl x:Class="ServoStudio.Views.AddSlaveAxisIDView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
             xmlns:local="clr-namespace:ServoStudio.Views"
             mc:Ignorable="d" 
             >
    
    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <StackPanel Orientation="Horizontal"  Margin="85,0,0,5">
                <!--<Button Content="添加"  Margin="10,3"  Width="180" Height="27" IsEnabled="{Binding IsAddSlaveAxisIDEnabled}"  Command="{Binding AddSlaveAxisIDCommand}"  />
            <Button Content="扫描" Margin="141,3" Width="181" Height="27" IsEnabled="{Binding IsScanSlaveAxisIDEnabled}" Command="{Binding ScanSlaveAxisIDCommand}"/>-->
                <dx:SimpleButton Margin="10,3" Width="180" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImage Image=AddItem_16x16.png}" IsEnabled="{Binding IsAddSlaveAxisIDEnabled}" Command="{Binding AddSlaveAxisIDCommand}" >
                    <Label Content="添加" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="140,3" Width="180" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImage Image=Zoom_16x16.png}" IsEnabled="{Binding IsScanSlaveAxisIDEnabled}" Command="{Binding ScanSlaveAxisIDCommand}" >
                    <Label Content="扫描" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>
            </StackPanel>

            <Grid Grid.Row="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="5"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="50"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="1"/>
                </Grid.ColumnDefinitions>

                <Label Grid.Row="1" Grid.Column="1" Margin="10,3" Content="从站地址" Style="{StaticResource LabelStyle}"/>
                <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Margin="10,3" Width="Auto" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding SlaveID}" SelectedItem="{Binding SelectedSlaveID,Mode=TwoWay}" IsReadOnly="{Binding ReadOnly}"/>

                <Label Grid.Row="1" Grid.Column="4" Margin="10,3" Content="轴地址" Style="{StaticResource LabelStyle}"/>
                <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="5" Margin="10,3" Width="Auto" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding AxisID}" SelectedItem="{Binding SelectedAxisID,Mode=TwoWay}" IsReadOnly="{Binding ReadOnly}"/>

            </Grid>

            <DataGrid Grid.Row="2" Margin="10,6" ColumnWidth="*" Width="710" Height="375" AutoGenerateColumns="False" CanUserAddRows="False" ItemsSource="{Binding AddSlaveAxisIDList}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="序号" Binding="{Binding Id}" IsReadOnly="True" />
                    <DataGridTextColumn Header="从站ID" Binding="{Binding SlaveID}" IsReadOnly="True"/>
                    <DataGridTextColumn Header="轴地址" Binding="{Binding AxisID}" IsReadOnly="True"/>
                    <DataGridTemplateColumn Header="操作">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <!--<Button Content="修改" Width="60" Height="25" Background="White" Foreground="Black" Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=DataGrid}}" CommandParameter="{Binding Id}"/>-->
                                    <Button Content="删除" Width="60" Height="22" Background="Red" Foreground="White" Command="{Binding DataContext.DelSlaveAxisIDCommand, RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=DataGrid}}" CommandParameter="{Binding Id}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>

            </DataGrid>

        </Grid>
    </ScrollViewer> 
    
    
</UserControl>
