﻿<UserControl x:Class="ServoStudio.Views.NormalSettingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"  
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking" 
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dxga="http://schemas.devexpress.com/winfx/2008/xaml/gauges"
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:NormalSettingViewModel}"
             d:DesignHeight="800" d:DesignWidth="1300">
    <UserControl.Resources>
        <Style TargetType="dxe:CheckEdit">
            <Style.Triggers>
                <Trigger Property="IsChecked" Value="True">
                    <Trigger.Setters>
                        <Setter Property="Foreground" Value="Green"/>
                    </Trigger.Setters>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand EventName="Loaded" Command="{Binding NormalSettingLoadedCommand}"/>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields" UseWin8NotificationsIfAvailable="True"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <TabControl Name="tabControl" SelectedIndex="{Binding SelectedTabIndex}" Grid.Row="0" Padding="0" BorderBrush="LightGray" Background="{x:Null}" BorderThickness="0,1,0,0">
                <TabItem Header="抱闸制动" TabIndex="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Label Grid.Row="0" Margin="3,10" Style="{StaticResource LabelStyle}" Content="抱闸制动图示" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <dxe:ImageEdit Grid.Row="1" Height="220" Width ="Auto"  HorizontalAlignment="Left" Margin="25,10" ShowMenu="False" Source="pack://application:,,,/ServoStudio;component/Resource/Brake.png" ShowBorder="False" Opacity="0.8"/>

                        <Label Grid.Row="2" Margin="3,10" Style="{StaticResource LabelStyle}" Content="抱闸制动设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Grid Grid.Row="3">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="20"/>

                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="60"/>

                                <ColumnDefinition Width="100"/>

                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="60"/>

                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <dxe:CheckEdit Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="3" Margin="10,9" IsChecked="{Binding IsBrakeChecked, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" Content="{Binding Hint_BrakeEnable}"/>

                            <Label   Grid.Row="2" Grid.Column="1" Margin="10,9" Content="抱闸释放至指令接收延时时间&#x0a;BRDT" Style="{StaticResource LabelStyle}"/>
                            <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding BrakeReleaseDelayTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding IsBrakeChecked}"/>
                            <TextBox Grid.Row="2" Grid.Column="3" Text="ms" Style="{StaticResource TextBoxStyle_Unit}" IsEnabled="{Binding IsBrakeChecked}"/>

                            <Label   Grid.Row="2" Grid.Column="5" Margin="10,9" Content="伺服OFF至抱闸制动等待时间&#x0a;BAWT" Style="{StaticResource LabelStyle}"/>
                            <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding BrakeActiveDelayTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding IsBrakeChecked}"/>
                            <TextBox Grid.Row="2" Grid.Column="7" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"  IsEnabled="{Binding IsBrakeChecked}"/>

                            <Label   Grid.Row="3" Grid.Column="1" Margin="10,9" Content="抱闸制动速度门限&#x0a;BAS" Style="{StaticResource LabelStyle}"/>
                            <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding BrakeActiveVelocity,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding IsBrakeChecked}"/>
                            <TextBox Grid.Row="3" Grid.Column="3" Text="rpm" Style="{StaticResource TextBoxStyle_Unit}" IsEnabled="{Binding IsBrakeChecked}"/>

                            <Label   Grid.Row="3" Grid.Column="5" Margin="10,9" Content="零速时抱闸制动至伺服OFF延时时间&#x0a;BADT" Style="{StaticResource LabelStyle}"/>
                            <TextBox Grid.Row="3" Grid.Column="6" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding BrakeActiveAllowedDelayTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding IsBrakeChecked}"/>
                            <TextBox Grid.Row="3" Grid.Column="7" Text="ms" Style="{StaticResource TextBoxStyle_Unit}" IsEnabled="{Binding IsBrakeChecked}"/>
                        </Grid>

                        <Label Grid.Row="4" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <StackPanel Grid.Row="5" HorizontalAlignment="Right" Orientation="Horizontal">
                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadNormalSettingParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultNormalSettingParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" Command="{Binding WriteNormalSettingParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>
                        </StackPanel>
                    </Grid>
                </TabItem>

                <TabItem Header="正反转" TabIndex="1">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="6" Margin="3,10" Style="{StaticResource LabelStyle}" Content="正反转设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Grid Grid.Row="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="2" Margin="10,9" Foreground="Red" Content="{Binding Hint_RotateDirection}" Style="{StaticResource LabelStyle}" />
                            <dxe:CheckEdit Grid.Row="1" Grid.Column="1" Margin="10,9" IsChecked="{Binding IsCCWChecked,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Content="以CCW方向为正转方向"/>
                            <dxe:CheckEdit Grid.Row="1" Grid.Column="2" Margin="10,9" IsChecked="{Binding IsCWChecked,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Content="以CW方向为正转方向"/>
                        </Grid>

                        <Label Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="6" Margin="3,10" Style="{StaticResource LabelStyle}" Content="正反转图示" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>
                        <dxe:ImageEdit Grid.Row="3" Height="200" Width="Auto" HorizontalAlignment="Left" Margin="30,3,10,3" ShowMenu="False" Source="pack://application:,,,/ServoStudio;component/Resource/Rotate.png" ShowBorder="False" Opacity="0.8"/>

                        <Label Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="6" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <StackPanel Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="6" HorizontalAlignment="Right" Orientation="Horizontal">
                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadNormalSettingParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultNormalSettingParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Download_16x16.png}" Command="{Binding WriteNormalSettingParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>
                        </StackPanel>
                    </Grid>
                </TabItem>

                <TabItem Header="停机方式" TabIndex="2">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="170"/>
                            <ColumnDefinition Width="103"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="170"/>
                            <ColumnDefinition Width="103"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="停机方式设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="超程停机方式" Style="{StaticResource LabelStyle}" />
                        <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Width="253" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding OverTravelStopMode}" SelectedItem="{Binding SelectedOverTravelStopMode,Mode=TwoWay}"/>

                        <Label Grid.Row="1" Grid.Column="4" Margin="10,9" Content="一级故障停机方式" Style="{StaticResource LabelStyle}" />
                        <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="5" Width="253" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding OneFaultStopMode}" SelectedItem="{Binding SelectedOneFaultStopMode,Mode=TwoWay}"/>

                        <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="强制停机方式" Style="{StaticResource LabelStyle}" />
                        <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="2" Width="253" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding ForceStopMode}" SelectedItem="{Binding SelectedForceStopMode,Mode=TwoWay}"/>

                        <Label Grid.Row="2" Grid.Column="4" Margin="10,9" Content="二级停机方式" Style="{StaticResource LabelStyle}" />
                        <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="5" Width="253" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding TwoFaultStopMode}" SelectedItem="{Binding SelectedTwoFaultStopMode,Mode=TwoWay}"/>

                        <!--<Label Grid.Row="2" Grid.Column="4" Margin="10,9" Content="故障1停机方式" Style="{StaticResource LabelStyle}" />
                    <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="5" Width="253" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding FaultStopMode}" SelectedItem="{Binding SelectedFaultStopMode,Mode=TwoWay}"/>-->

                        <Label Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <StackPanel Grid.Row="17" Grid.Column="0" Grid.ColumnSpan="9" HorizontalAlignment="Right" Orientation="Horizontal">
                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadNormalSettingShutdownMethodCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultNormalSettingShutdownMethodCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Download_16x16.png}" Command="{Binding WriteNormalSettingShutdownMethodCommand}" CommandParameter="{Binding SelectedTabIndex}">
                                <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>
                        </StackPanel>
                    </Grid>
                </TabItem>
            </TabControl>

            <Grid Grid.Row="4">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>

                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="0" Content="1.单位设置" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="2" Content="2.限幅保护" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="4" Content="3.一般设定" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="6" Content="4.数字IO" Foreground="Gray" Style="{StaticResource LabelStyle}"/>

                <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="2" Content="2.电机反馈" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="4" Content="3.单位设置" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="6" Content="4.限幅保护" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="8" Content="5.一般设定" Style="{StaticResource LabelStyle}"/>-->

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="12" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetNormalSettingConfigFileCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="13" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2" />

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="14" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SaveNormalSettingConfigFileCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="16" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="17" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Backward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding LimitAmplitudeNavigationCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="18" FontSize="12" Content="上页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="19" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Forward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding DigitalIONavigationCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="20" FontSize="12" Content="下页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

            </Grid>
        </Grid>

    </ScrollViewer>
       
</UserControl>