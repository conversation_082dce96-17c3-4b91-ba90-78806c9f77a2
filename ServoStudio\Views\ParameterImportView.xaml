﻿<UserControl x:Class="ServoStudio.Views.ParameterImportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors" 
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:converter="clr-namespace:Converter"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:ParameterImportViewModel}"
             d:DesignHeight="650" d:DesignWidth="1400">

    <UserControl.Resources>
        <converter:BackgroundConverter x:Key="BackgroudConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>
        <converter:ContentConverter x:Key="ContentConverter"/>
        <converter:BackgroundConverter x:Key="ColorConverter"/>

        <Style x:Key="myTabItem" TargetType="dx:DXTabItem">
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Trigger.Setters>
                        <Setter Property="FontStyle" Value="Italic"/>
                        <Setter Property="Foreground" Value="Red"/>
                    </Trigger.Setters>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!--当前参数-->
        <DataTemplate x:Key="CurrentTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.Current, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" ToolTip="{Binding RowData.Row.Comment}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>
                <dxe:ImageEdit Height="16" Width="Auto" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Apply_16x16.png}" ShowBorder="False" HorizontalAlignment="Right" VerticalAlignment="Center"  Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}" />
            </Grid>
        </DataTemplate>

        <!--索引号-->
        <DataTemplate x:Key="IndexTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.Index, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" ToolTip="{Binding RowData.Row.Comment}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>
                <dxe:ImageEdit Height="16" Width="Auto" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Apply_16x16.png}" ShowBorder="False" HorizontalAlignment="Right" VerticalAlignment="Center"  Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}" />
            </Grid>
        </DataTemplate>

        <!--参数名称-->
        <DataTemplate x:Key="NameTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.Name, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" ToolTip="{Binding RowData.Row.Comment}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>
                <dxe:ImageEdit Height="16" Width="Auto" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Apply_16x16.png}" ShowBorder="False" HorizontalAlignment="Right" VerticalAlignment="Center"  Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}" />
            </Grid>
        </DataTemplate>

        <!--参数描述-->
        <DataTemplate x:Key="DescriptionTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.Description, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" ToolTip="{Binding RowData.Row.Comment}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>
                <dxe:ImageEdit Height="16" Width="Auto" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Apply_16x16.png}" ShowBorder="False" HorizontalAlignment="Right" VerticalAlignment="Center"  Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}" />
            </Grid>
        </DataTemplate>

        <!--数据类型-->
        <DataTemplate x:Key="DataTypeTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.DataType, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" ToolTip="{Binding RowData.Row.Comment}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>
                <dxe:ImageEdit Height="16" Width="Auto" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Apply_16x16.png}" ShowBorder="False" HorizontalAlignment="Right" VerticalAlignment="Center"  Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}" />
            </Grid>
        </DataTemplate>

        <!--单位-->
        <DataTemplate x:Key="UnitTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.Unit, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" ToolTip="{Binding RowData.Row.Comment}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>
                <dxe:ImageEdit Height="16" Width="Auto" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Apply_16x16.png}" ShowBorder="False" HorizontalAlignment="Right" VerticalAlignment="Center"  Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}" />
            </Grid>
        </DataTemplate>

        <!--读写属性-->
        <DataTemplate x:Key="RWPropertyTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.RWProperty, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" ToolTip="{Binding RowData.Row.Comment}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>
                <dxe:ImageEdit Height="16" Width="Auto" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Apply_16x16.png}" ShowBorder="False" HorizontalAlignment="Right" VerticalAlignment="Center"  Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}" />
            </Grid>
        </DataTemplate>

        <!--最小值-->
        <DataTemplate x:Key="MinTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.Min, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" ToolTip="{Binding RowData.Row.Comment}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>
                <dxe:ImageEdit Height="16" Width="Auto" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Apply_16x16.png}" ShowBorder="False" HorizontalAlignment="Right" VerticalAlignment="Center"  Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}" />
            </Grid>
        </DataTemplate>

        <!--最大值-->
        <DataTemplate x:Key="MaxTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.Max, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" ToolTip="{Binding RowData.Row.Comment}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>
                <dxe:ImageEdit Height="16" Width="Auto" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Apply_16x16.png}" ShowBorder="False" HorizontalAlignment="Right" VerticalAlignment="Center"  Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}" />
            </Grid>
        </DataTemplate>

        <!--默认值-->
        <DataTemplate x:Key="DefaultTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.Default, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" ToolTip="{Binding RowData.Row.Comment}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>
                <dxe:ImageEdit Height="16" Width="Auto" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Apply_16x16.png}" ShowBorder="False" HorizontalAlignment="Right" VerticalAlignment="Center"  Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}" />
            </Grid>
        </DataTemplate>

    </UserControl.Resources>

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding DiffParameterReadWriteLoadedCommand}"/>
    </dxmvvm:Interaction.Behaviors>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="600"/>

        </Grid.RowDefinitions>

        <dx:DXTabControl Padding="0" Grid.Row ="0" Margin="5,10,10,0">
            <dx:DXTabItem Style="{StaticResource myTabItem}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding DiffParameterReadCommand}" CommandParameter="差异参数" />
                </dxmvvm:Interaction.Behaviors>

                <Grid>
                    <dxg:GridControl SelectionMode="Row" ItemsSource="{Binding DiffParameterImport}" AutoGenerateColumns="AddNew" FontStyle="Normal" ShowBorder="False">
                        <dxg:GridControl.View>
                            <dxg:TableView AllowEditing="False" AutoWidth="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Never" SearchPanelHorizontalAlignment="Stretch" ShowGroupPanel="False" SearchPanelHighlightResults="False">
                                <dxg:TableView.FormatConditions>

                                    <dxg:FormatCondition Expression="Contains([Index], '导入: ')" FieldName="Index">
                                        <dx:Format Foreground="#FFFF8200" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>

                                    <dxg:FormatCondition Expression="Contains([Name], '导入: ')" FieldName="Name">
                                        <dx:Format Foreground="#FFFF8200" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>

                                    <dxg:FormatCondition ApplyToRow="False" Expression="Contains([Description], '导入: ')" FieldName="Description">
                                        <dx:Format Foreground="#FFFF8200" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>

                                    <dxg:FormatCondition Expression="Contains([DataType], '导入: ')" FieldName="DataType">
                                        <dx:Format Foreground="#FFFF8200" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>

                                    <dxg:FormatCondition Expression="Contains([Unit], '导入: ')" FieldName="Unit">
                                        <dx:Format Foreground="#FFFF8200" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>

                                    <dxg:FormatCondition Expression="Contains([RWProperty], '导入: ')" FieldName="RWProperty">
                                        <dx:Format Foreground="#FFFF8200" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>

                                    <dxg:FormatCondition Expression="Contains([Min], '导入: ')" FieldName="Min">
                                        <dx:Format Foreground="#FFFF8200" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>

                                    <dxg:FormatCondition Expression="Contains([Max], '导入: ')" FieldName="Max">
                                        <dx:Format Foreground="#FFFF8200" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>

                                    <dxg:FormatCondition Expression="Contains([Default], '导入: ')" FieldName="Default">
                                        <dx:Format Foreground="#FFFF8200" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>

                                    <dxg:FormatCondition Expression="Contains([Current], '导入: ')" FieldName="Current">
                                        <dx:Format Foreground="#FFFF8200" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>

                                </dxg:TableView.FormatConditions>
                            </dxg:TableView>
                        </dxg:GridControl.View>
                      
                        <dxg:GridColumn FieldName="Index" Header="索引号" IsSmart="True" Width="*" CellTemplate="{StaticResource IndexTemplate}"/>
                        <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="2.5*" CellTemplate="{StaticResource NameTemplate}"/>
                        <dxg:GridColumn FieldName="Description" Header="参数描述" IsSmart="True" Width="2*" CellTemplate="{StaticResource DescriptionTemplate}"/>
                        <dxg:GridColumn FieldName="DataType" Header="数据类型" IsSmart="True" Width="*" CellTemplate="{StaticResource DataTypeTemplate}"/>
                        <dxg:GridColumn FieldName="Unit" Header="单位" Width="*" CellTemplate="{StaticResource UnitTemplate}"/>
                        <dxg:GridColumn FieldName="RWProperty" Header="读写属性" IsSmart="True" Width="*" CellTemplate="{StaticResource RWPropertyTemplate}"/>
                        <dxg:GridColumn FieldName="Min" Header="最小值" IsSmart="True" Width="*" CellTemplate="{StaticResource MinTemplate}"/>
                        <dxg:GridColumn FieldName="Max" Header="最大值" IsSmart="True" Width="*" CellTemplate="{StaticResource MaxTemplate}"/>
                        <dxg:GridColumn FieldName="Default" Header="默认值" IsSmart="True" Width="*" CellTemplate="{StaticResource DefaultTemplate}"/>
                        <dxg:GridColumn FieldName="Current" Header="当前参数值" IsSmart="True" Width="2*" CellTemplate="{StaticResource CurrentTemplate}"/>
                        <dxg:GridColumn FieldName="IsDownload" Visible="False"/>
                        <dxg:GridColumn FieldName="Classification" Visible="False"/>
                        <dxg:GridColumn FieldName="Comment" Visible="False"/>                        
                    </dxg:GridControl>
                </Grid>
            </dx:DXTabItem>

        </dx:DXTabControl>

    </Grid>
</UserControl>
