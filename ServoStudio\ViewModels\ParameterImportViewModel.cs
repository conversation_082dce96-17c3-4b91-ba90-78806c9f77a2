﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using DevExpress.Mvvm.POCO;
using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using ServoStudio.Models;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Threading;
using System.Threading;
using System.Data;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class ParameterImportViewModel
    {
        #region 字段
        private ObservableCollection<DiffParameterReadWriteSet> obsParameter_DiffParameters = new ObservableCollection<DiffParameterReadWriteSet>();//参数差异
        #endregion

        #region 属性
        public virtual ObservableCollection<DiffParameterReadWriteSet> DiffParameterImport { get; set; } //差异参数
        #endregion

        #region 构造函数
        public ParameterImportViewModel()
        {
            ViewModelSet.ParameterImport = this;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：DiffParameterReadWriteLoaded
        //函数功能：载入
        //
        //输入参数：
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17   
        //*************************************************************************
        public void DiffParameterReadWriteLoaded()
        {
            int iRet = -1;

            try
            {
                #region 事件注册
                if (ViewModelSet.CommunicationSet != null)
                {
                    ViewModelSet.CommunicationSet.evtEvaluationDiffParamterReadAndWrite += EvaluationItemValue;
                }
                #endregion

                #region 获取参数集合
                #region DiffParameters
                DiffParameterImport = new ObservableCollection<DiffParameterReadWriteSet>();
                iRet = ParameterReadWriteModel.RetrieveDataTable_For_Compare(TaskName.DiffParameters, ref obsParameter_DiffParameters);
                if (iRet == RET.SUCCEEDED)
                {
                    foreach (var i in obsParameter_DiffParameters)
                    {
                        DiffParameterImport.Add(new DiffParameterReadWriteSet()
                        {
                            Classification = i.Classification,
                            Index = i.Index,
                            Name = i.Name,
                            DataType = i.DataType,
                            Description = i.Description,
                            Current = i.Current,
                            Min = i.Min,
                            Max = i.Max,
                            Default = i.Default,
                            Unit = i.Unit,
                            RWProperty = i.RWProperty,
                            Comment = i.Comment,
                            IsDownload = ControlVisibility.Collapsed
                        });
                    }
                }

                #endregion

                #endregion

                #region 读取电机参数
                //ParameterRead(SoftwareStateParameterSet.CurrentPageName);
                #endregion
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_LOADED, "DiffParameterReadWriteLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：ParameterWrite
        //函数功能：参数写入
        //
        //输入参数：string In_strIndex   索引号（地址）
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public void ParameterWrite(DiffParameterReadWriteSet In_clsTemp)
        {
            bool bRet = false;
            string strCurrentValue = null;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();
            List<DiffParameterReadWriteSet> lstParameterInfo = new List<DiffParameterReadWriteSet>();

            try
            {
                //更新参数差异表字典信息
                OthersHelper.RefreshDictionary(GlobalParameterSet.dt_Diff, ref GlobalParameterSet.CurrentPointValue_AxisA, ref GlobalParameterSet.CurrentPointValue_AxisB);

                //若更改参数差异表界面当前参数值，则把更改值更新到参数差异表(GlobalParameterSet.dt_Diff)
                if (OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt_Diff, "Name", In_clsTemp.Name, "Current") != In_clsTemp.Current)
                {
                    OthersHelper.DataTableExportUpdate_ForCompare(ref GlobalParameterSet.dt_Diff, In_clsTemp.Index, In_clsTemp.Current);
                }

                if (In_clsTemp == null)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (In_clsTemp.RWProperty == "RO")
                {
                    ShowNotification(2009);
                    return;
                }

                if (In_clsTemp.Name == "Timestamp")
                {
                    ShowNotification(2019);
                    return;
                }

                //获取当前页面名称
                SoftwareStateParameterSet.CurrentPageName = In_clsTemp.Classification;

                //判断输入数据是否为16进制数
                if (OthersHelper.CheckIsInputHex(In_clsTemp.Current))
                {
                    In_clsTemp.Current = OthersHelper.TransferHexToDecimal(In_clsTemp.Current, In_clsTemp.DataType);
                }

                //判断输入数据是否为10进制数
                if (!OthersHelper.IsInputInteger(In_clsTemp.Current))
                {
                    ShowNotification(2008);
                    return;
                }

                //判断输入数据是否在数据类型范围内
                strCurrentValue = In_clsTemp.Current;
                bRet = OthersHelper.IsOutOfRange(ref strCurrentValue, In_clsTemp.Unit, In_clsTemp.DataType, In_clsTemp.Max, In_clsTemp.Min);
                if (bRet)
                {
                    In_clsTemp.Current = strCurrentValue;
                }
                else
                {
                    ShowNotification(In_clsTemp.Name, In_clsTemp.Description, In_clsTemp.Min, In_clsTemp.Max, In_clsTemp.Unit);
                    return;
                }

                //获取地址与数据类型
                lstParameterInfo.Add(In_clsTemp);
                ParameterReadWriteModel.GetIndexAndDataType_ForCompare(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(SoftwareStateParameterSet.CurrentPageName, In_clsTemp.Description, lstTransmittingDataInfo);

                //更改下载图标的状态为可见
                ChangeDownloadImageVisibility(In_clsTemp, ControlVisibility.Visible);

            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_PARAMETER_WRITE, "ParameterWrite", ex);
            }
        }

        //*************************************************************************
        //函数名称：ParameterRead
        //函数功能：参数读取
        //
        //输入参数：string In_strTabItem     
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public void ParameterRead(string In_strTabItem)
        {
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取当前页面名称
                SoftwareStateParameterSet.CurrentPageName = In_strTabItem;

                //获取地址与数据类型，写入串口
                switch (In_strTabItem)
                {
                    case TaskName.DiffParameters:
                        ParameterReadWriteModel.GetIndexAndDataType_For_Compare(obsParameter_DiffParameters, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName.DiffParameters, lstTransmittingDataInfo);
                        break;
                    default:
                        break;
                }

                //更改下载图标为全部不可见
                ChangeAllDownloadImageHidden(In_strTabItem);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_PARAMETER_READ, "ParameterRead", ex);
            }
        }
        #endregion

        #region 私有方法       
        //*************************************************************************
        //函数名称：ChangeDownloadButtonVisibility
        //函数功能：下载按钮控件展示与隐藏
        //
        //输入参数：NONE
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        private void ChangeDownloadImageVisibility(DiffParameterReadWriteSet In_clsTemp, int In_iControlVisibility)
        {
            int iIndex = -1;

            try
            {
                if (In_clsTemp.Classification == TaskName.DiffParameters && DiffParameterImport != null)
                {
                    iIndex = DiffParameterImport.FindIndex(Item => Item.Index == In_clsTemp.Index);
                    if (iIndex != -1)
                    {
                        DiffParameterImport[iIndex].IsDownload = In_iControlVisibility;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_DOWNLOAD_BUTTON_VISIBILITY, "ChangeDownloadImageVisibility", ex);
            }
        }

        //*************************************************************************
        //函数名称：ChangeAllDownloadButtonVisibility
        //函数功能：更改全部的下载按钮可见属性
        //
        //输入参数：int In_iControlVisibility    可见属性值
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        private void ChangeAllDownloadImageHidden(string strTaskName)
        {
            try
            {
                if (strTaskName == TaskName.DiffParameters && DiffParameterImport != null)
                {
                    foreach (var item in DiffParameterImport)
                    {
                        item.IsDownload = ControlVisibility.Collapsed;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_CHANGE_ALL_DOWNLOAD_BUTTON_VISIBILITY, "ChangeAllDownloadImageHidden", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationItemValue
        //函数功能：赋值数据
        //
        //输入参数：string In_strTabItem     TabItem名称
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        private void EvaluationItemValue(string In_strTabItem)
        {
            try
            {
                if (In_strTabItem == TaskName.DiffParameters)
                {
                    for (int i = 0; i < DiffParameterImport.Count; i++)
                    {
                        DiffParameterImport[i].Current = OthersHelper.GetCurrentValueOfIndex(DiffParameterImport[i].Index);                        
                    }
                }
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("EvaluationItemValue", ex);
            }                    
        }

        //*************************************************************************
        //函数名称：ShowNotification
        //函数功能：信息提示
        //
        //输入参数：In_iType     信息提示类型
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        private void ShowNotification(string In_strName, string In_strItem, string In_strMin, string In_strMax, string In_Unit)
        {
            //ViewModelSet.Main?.ShowNotification(In_strName, In_strItem, In_strMin, In_strMax, In_Unit, bExchanged: false);
            ViewModelSet.Main?.ShowNotification_For_ParameterReadWrite(In_strName, In_strItem, In_strMin, In_strMax, In_Unit, bExchanged: false);
        }
        #endregion
    }

    public class DiffParameterReadWriteSet : INotifyPropertyChanged
    {
        private string classification;
        public string Classification
        {
            get { return classification; }
            set
            {
                classification = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Classification"));
                }
            }
        }

        private string index;
        public string Index
        {
            get { return index; }
            set
            {
                index = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Index"));
                }
            }
        }

        private string name;
        public string Name
        {
            get { return name; }
            set
            {
                name = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Name"));
                }
            }
        }

        private string dataType;
        public string DataType
        {
            get { return dataType; }
            set
            {
                dataType = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("DataType"));
                }
            }
        }

        private string description;
        public string Description
        {
            get { return description; }
            set
            {
                description = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Description"));
                }
            }
        }

        private string current;
        public string Current
        {
            get { return current; }
            set
            {
                current = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Current"));
                }
            }
        }

        private string unit;
        public string Unit
        {
            get { return unit; }
            set
            {
                unit = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Unit"));
                }
            }
        }

        private string min;
        public string Min
        {
            get { return min; }
            set
            {
                min = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Min"));
                }
            }
        }

        private string max;
        public string Max
        {
            get { return max; }
            set
            {
                max = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Max"));
                }
            }
        }

        private string defaultvalue;
        public string Default
        {
            get { return defaultvalue; }
            set
            {
                defaultvalue = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Default"));
                }
            }
        }

        private string rwproperty;
        public string RWProperty
        {
            get { return rwproperty; }
            set
            {
                rwproperty = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("RWProperty"));
                }
            }
        }

        private string comment;
        public string Comment
        {
            get { return comment; }
            set
            {
                comment = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Comment"));
                }
            }
        }

        private int isDownload;//下载按钮展示
        public int IsDownload
        {
            get { return isDownload; }
            set
            {
                isDownload = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("IsDownload"));
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
    }
}