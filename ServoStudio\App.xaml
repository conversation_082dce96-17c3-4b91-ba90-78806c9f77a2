<Application x:Class="ServoStudio.App" 
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" 
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" 
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core" 
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars" 
             xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon" 
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors" 
             xmlns:dxrt="http://schemas.devexpress.com/winfx/2008/xaml/ribbon/themekeys" 
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui" 
             xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation" 
             xmlns:dxnt="http://schemas.devexpress.com/winfx/2008/xaml/navbar/themekeys" 
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking" 
             xmlns:dxn="http://schemas.devexpress.com/winfx/2008/xaml/navbar" 
             xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol" 
             StartupUri="MainWindow.xaml" 
             Startup="OnAppStartup_UpdateThemeName" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             d1p1:Ignorable="d" 
             xmlns:d1p1="http://schemas.openxmlformats.org/markup-compatibility/2006">
    
  <Application.Resources>
    <ResourceDictionary>
      <ResourceDictionary.MergedDictionaries>
        <!--<ResourceDictionary Source="Language\zh-CN.xaml">
          
        </ResourceDictionary>-->
        <!--<ResourceDictionary Source="Language\en-US.xaml"/>-->
      </ResourceDictionary.MergedDictionaries>
        
      <Style x:Key="GroupBoxStyle" TargetType="dxlc:GroupBox">
        <Setter Property="MinimizeElementVisibility" Value="Visible" />
        <Setter Property="Margin" Value="2" />
        <Setter Property="Padding" Value="5" />
        <Setter Property="Width" Value="Auto" />
      </Style>
        
      <Style x:Key="SlideAlarmGroupBoxStyle" TargetType="dxlc:GroupBox">
        <Setter Property="MinimizeElementVisibility" Value="Visible" />
        <Setter Property="Margin" Value="2" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Width" Value="222" />
      </Style>
        
      <Style x:Key="LabelStyle" TargetType="Label">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="9pt" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="FontStyle" Value="Normal" />
      </Style>
        
      <Style x:Key="ComboBoxStyle" TargetType="dxe:ComboBoxEdit">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="IsTextEditable" Value="False" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="SelectedIndex" Value="0" />
        <Setter Property="Height" Value="27" />
        <Setter Property="FontStyle" Value="Normal" />
      </Style>
        
      <Style x:Key="TextBoxStyle" TargetType="TextBox">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="Height" Value="27" />
        <Setter Property="FontStyle" Value="Normal" />
        <Style.Triggers>
          <Trigger Property="IsMouseOver" Value="True">
            <Trigger.Setters>
              <Setter Property="FontSize" Value="9.5pt" />
              <!--<Setter Property="Foreground" Value="Green"/>-->
              <Setter Property="BorderBrush" Value="Orange" />
              <Setter Property="BorderThickness" Value="1.1" />
            </Trigger.Setters>
          </Trigger>
        </Style.Triggers>
      </Style>
        
      <Style x:Key="TextBoxStyle_Unit" TargetType="TextBox">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Margin" Value="12" />
        <Setter Property="Height" Value="20" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="IsReadOnly" Value="True" />
        <Setter Property="TextAlignment" Value="Right" />
        <Setter Property="FontStyle" Value="Normal" />
      </Style>
        
      <Style x:Key="SettingComboBoxStyle" TargetType="dxe:ComboBoxEdit">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="IsTextEditable" Value="False" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="SelectedIndex" Value="0" />
        <Setter Property="Width" Value="240" />
        <Setter Property="Height" Value="27" />
      </Style>
        
      <Style x:Key="SpinStyle" TargetType="dxe:SpinEdit">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="IsTextEditable" Value="True" />
        <Setter Property="Margin" Value="3" />
        <Setter Property="AllowSpinOnMouseWheel" Value="True" />
        <Setter Property="Width" Value="240" />
        <Setter Property="Height" Value="27" />
        <Setter Property="Increment" Value="0.5" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
      </Style>
        
      <Style x:Key="ButtonStyle" TargetType="dx:SimpleButton">
        <Setter Property="Margin" Value="0,0,0,5" />
        <Setter Property="Width" Value="140" />
        <Setter Property="Height" Value="27" />
      </Style>
        
      <Style x:Key="Line" TargetType="Line">
        <Setter Property="Stroke" Value="Gray" />
        <Setter Property="StrokeThickness" Value="1.1" />
        <Setter Property="StrokeStartLineCap" Value="Round" />
        <Setter Property="StrokeEndLineCap" Value="Round" />
        <Setter Property="X1" Value="0" />
        <Setter Property="X2" Value="35" />
        <Setter Property="Y1" Value="0" />
      </Style>
        
      <Style x:Key="VerticalLine" TargetType="Line">
        <Setter Property="Stroke" Value="Gray" />
        <Setter Property="StrokeThickness" Value="1.1" />
        <Setter Property="StrokeStartLineCap" Value="Round" />
        <Setter Property="StrokeEndLineCap" Value="Round" />
        <Setter Property="X1" Value="0" />
        <Setter Property="X2" Value="0" />
        <Setter Property="Y1" Value="0" />
      </Style>
        
      <Style x:Key="LUArrow" TargetType="Line">
        <Setter Property="Stroke" Value="Gray" />
        <Setter Property="StrokeThickness" Value="1.1" />
        <Setter Property="StrokeStartLineCap" Value="Round" />
        <Setter Property="StrokeEndLineCap" Value="Round" />
        <Setter Property="X2" Value="8" />
        <Setter Property="Y2" Value="-3" />
      </Style>
        
      <Style x:Key="LDArrow" TargetType="Line">
        <Setter Property="Stroke" Value="Gray" />
        <Setter Property="StrokeThickness" Value="1.1" />
        <Setter Property="StrokeStartLineCap" Value="Round" />
        <Setter Property="StrokeEndLineCap" Value="Round" />
        <Setter Property="X2" Value="8" />
        <Setter Property="Y2" Value="3" />
      </Style>
        
      <Style x:Key="RUArrow" TargetType="Line">
        <Setter Property="Stroke" Value="Gray" />
        <Setter Property="StrokeThickness" Value="1.1" />
        <Setter Property="StrokeStartLineCap" Value="Round" />
        <Setter Property="StrokeEndLineCap" Value="Round" />
        <Setter Property="X2" Value="8" />
        <Setter Property="Y1" Value="-3" />
      </Style>
        
      <Style x:Key="RDArrow" TargetType="Line">
        <Setter Property="Stroke" Value="Gray" />
        <Setter Property="StrokeThickness" Value="1.1" />
        <Setter Property="StrokeStartLineCap" Value="Round" />
        <Setter Property="StrokeEndLineCap" Value="Round" />
        <Setter Property="X2" Value="8" />
        <Setter Property="Y1" Value="3" />
      </Style>
        
      <Style x:Key="DLArrow" TargetType="Line">
        <Setter Property="Stroke" Value="Gray" />
        <Setter Property="StrokeThickness" Value="1.1" />
        <Setter Property="StrokeStartLineCap" Value="Round" />
        <Setter Property="StrokeEndLineCap" Value="Round" />
        <Setter Property="X2" Value="-3" />
        <Setter Property="Y2" Value="-8" />
      </Style>
        
      <Style x:Key="DRArrow" TargetType="Line">
        <Setter Property="Stroke" Value="Gray" />
        <Setter Property="StrokeThickness" Value="1.1" />
        <Setter Property="StrokeStartLineCap" Value="Round" />
        <Setter Property="StrokeEndLineCap" Value="Round" />
        <Setter Property="X2" Value="3" />
        <Setter Property="Y2" Value="-8" />
      </Style>
        
      <Style x:Key="ULArrow" TargetType="Line">
        <Setter Property="Stroke" Value="Gray" />
        <Setter Property="StrokeThickness" Value="1.1" />
        <Setter Property="StrokeStartLineCap" Value="Round" />
        <Setter Property="StrokeEndLineCap" Value="Round" />
        <Setter Property="X2" Value="3" />
        <Setter Property="Y1" Value="-8" />
      </Style>
        
      <Style x:Key="URArrow" TargetType="Line">
        <Setter Property="Stroke" Value="Gray" />
        <Setter Property="StrokeThickness" Value="1.1" />
        <Setter Property="StrokeStartLineCap" Value="Round" />
        <Setter Property="StrokeEndLineCap" Value="Round" />
        <Setter Property="X2" Value="-3" />
        <Setter Property="Y1" Value="-8" />
      </Style>
        
      <Style x:Key="StartDot" TargetType="Ellipse">
        <Setter Property="Height" Value="11" />
        <Setter Property="Width" Value="11" />
        <Setter Property="Fill" Value="Red" />
      </Style>
        
      <Style x:Key="EndDot" TargetType="Ellipse">
        <Setter Property="Height" Value="11" />
        <Setter Property="Width" Value="11" />
        <Setter Property="Fill" Value="Green" />
      </Style>
        
      <Style x:Key="OperatorProfile" TargetType="Ellipse">
        <Setter Property="Height" Value="35" />
        <Setter Property="Width" Value="35" />
        <Setter Property="Stroke" Value="#FF3C8EE0" />
        <Setter Property="StrokeThickness" Value="2" />
      </Style>
        
      <Style x:Key="Operator" TargetType="Label">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="Margin" Value="0" />
      </Style>
        
      <Style x:Key="Function" TargetType="Label">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="9pt" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="Margin" Value="0" />
        <Setter Property="BorderThickness" Value="1.6" />
        <Setter Property="BorderBrush" Value="#FF3C8EE0" />
        <Setter Property="Width" Value="60" />
        <Setter Property="Height" Value="35" />
      </Style>
        
      <Style x:Key="BackgroundSwitch" TargetType="Label">
        <Setter Property="FontFamily" Value="Microsoft YaHei" />
        <Setter Property="FontSize" Value="9pt" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="Margin" Value="0" />
        <Setter Property="BorderThickness" Value="1.3" />
        <Setter Property="BorderBrush" Value="Orange" />
        <Setter Property="Width" Value="75" />
        <Setter Property="Height" Value="35" />
        <Style.Triggers>
          <Trigger Property="IsMouseOver" Value="True">
            <Trigger.Setters>
              <Setter Property="FontStyle" Value="Italic" />
              <Setter Property="Foreground" Value="OrangeRed" />
              <Setter Property="BorderThickness" Value="2" />
            </Trigger.Setters>
          </Trigger>
        </Style.Triggers>
      </Style>
        
      <Style x:Key="Jog" TargetType="dxe:CheckEdit">
        <Style.Triggers>
          <Trigger Property="IsChecked" Value="True">
            <Trigger.Setters>
              <Setter Property="Foreground" Value="SteelBlue" />
            </Trigger.Setters>
          </Trigger>
        </Style.Triggers>
      </Style>
        
      <Style x:Key="slider" TargetType="dxe:CheckEdit">
        <Setter Property="Template">
          <Setter.Value>
            <ControlTemplate TargetType="dxe:CheckEdit">
              <Grid>
                <VisualStateManager.VisualStateGroups>
                  <VisualStateGroup x:Name="CommonStates">
                    <VisualStateGroup.Transitions>
                      <VisualTransition GeneratedDuration="0:0:0.1" To="MouseOver" />
                      <VisualTransition From="MouseOver" GeneratedDuration="0:0:0.15" To="Normal" />
                    </VisualStateGroup.Transitions>
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="MouseOver">
                      <Storyboard>
                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="on">
                          <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                        </DoubleAnimationUsingKeyFrames>
                        <ObjectAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Visibility)" Storyboard.TargetName="off">
                          <DiscreteObjectKeyFrame KeyTime="0" Value="Hidden" />
                        </ObjectAnimationUsingKeyFrames>
                      </Storyboard>
                    </VisualState>
                    <VisualState x:Name="Pressed">
                      <Storyboard>
                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="pressed">
                          <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                        </DoubleAnimationUsingKeyFrames>
                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="off">
                          <EasingDoubleKeyFrame KeyTime="0" Value="0" />
                        </DoubleAnimationUsingKeyFrames>
                      </Storyboard>
                    </VisualState>
                    <VisualState x:Name="Disabled" />
                  </VisualStateGroup>
                  <VisualStateGroup x:Name="CheckStates">
                    <VisualStateGroup.Transitions>
                      <VisualTransition GeneratedDuration="0:0:0.15" To="Checked">
                        <VisualTransition.GeneratedEasingFunction>
                          <CircleEase EasingMode="EaseIn" />
                        </VisualTransition.GeneratedEasingFunction>
                        <Storyboard>
                          <DoubleAnimation Duration="0:0:0.15" From="1" To="1" Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="on">
                            <DoubleAnimation.EasingFunction>
                              <CircleEase EasingMode="EaseInOut" />
                            </DoubleAnimation.EasingFunction>
                          </DoubleAnimation>
                          <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="X" Storyboard.TargetName="translate">
                            <EasingDoubleKeyFrame KeyTime="0" Value="0" />
                            <EasingDoubleKeyFrame KeyTime="0:0:0.15" Value="52" />
                          </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                      </VisualTransition>
                      <VisualTransition GeneratedDuration="0:0:0.15" To="Unchecked">
                        <Storyboard>
                          <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="X" Storyboard.TargetName="translate">
                            <EasingDoubleKeyFrame KeyTime="0" Value="52" />
                            <EasingDoubleKeyFrame KeyTime="0:0:0.15" Value="0" />
                          </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                      </VisualTransition>
                    </VisualStateGroup.Transitions>
                    <VisualState x:Name="Checked">
                      <Storyboard>
                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="X" Storyboard.TargetName="translate">
                          <EasingDoubleKeyFrame KeyTime="0" Value="52" />
                        </DoubleAnimationUsingKeyFrames>
                        <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.Opacity)" Storyboard.TargetName="on">
                          <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                        </DoubleAnimationUsingKeyFrames>
                      </Storyboard>
                    </VisualState>
                    <VisualState x:Name="Unchecked" />
                    <VisualState x:Name="Indeterminate" />
                  </VisualStateGroup>
                </VisualStateManager.VisualStateGroups>
                <Border x:Name="back" Background="Green">
                  <Border Margin="0.8" Background="White" />
                </Border>
                <Grid x:Name="off_Copy" HorizontalAlignment="Right" Height="13" Margin="0,1,20,0" VerticalAlignment="Center" Width="18" FlowDirection="LeftToRight">
                  <Path Stretch="Fill" Fill="White" Data="F1 M 676.602,232.803C 676.576,233.415 676.257,233.734 675.645,233.76L 671.717,233.76C 671.105,233.734 670.786,233.415 670.76,232.803L 670.76,222.717C 670.786,222.105 671.105,221.786 671.717,221.76L 675.645,221.76C 676.257,221.786 676.576,222.105 676.602,222.717L 676.602,232.803 Z M 674.865,232.023L 674.865,223.497L 672.497,223.497L 672.497,232.023L 674.865,232.023 Z M 683.076,223.497L 679.918,223.497L 679.918,226.813L 682.602,226.813L 682.602,228.55L 679.918,228.55L 679.918,233.76L 678.181,233.76L 678.181,221.76L 683.076,221.76L 683.076,223.497 Z M 688.76,223.497L 685.602,223.497L 685.602,226.813L 688.286,226.813L 688.286,228.55L 685.602,228.55L 685.602,233.76L 683.865,233.76L 683.865,221.76L 688.76,221.76L 688.76,223.497 Z " HorizontalAlignment="Stretch" Margin="0,1,0,0" UseLayoutRounding="False" VerticalAlignment="Stretch" />
                  <Path Stretch="Fill" Fill="#FF9A9C9D" Data="F1 M 676.602,232.803C 676.576,233.415 676.257,233.734 675.645,233.76L 671.717,233.76C 671.105,233.734 670.786,233.415 670.76,232.803L 670.76,222.717C 670.786,222.105 671.105,221.786 671.717,221.76L 675.645,221.76C 676.257,221.786 676.576,222.105 676.602,222.717L 676.602,232.803 Z M 674.865,232.023L 674.865,223.497L 672.497,223.497L 672.497,232.023L 674.865,232.023 Z M 683.076,223.497L 679.918,223.497L 679.918,226.813L 682.602,226.813L 682.602,228.55L 679.918,228.55L 679.918,233.76L 678.181,233.76L 678.181,221.76L 683.076,221.76L 683.076,223.497 Z M 688.76,223.497L 685.602,223.497L 685.602,226.813L 688.286,226.813L 688.286,228.55L 685.602,228.55L 685.602,233.76L 683.865,233.76L 683.865,221.76L 688.76,221.76L 688.76,223.497 Z " HorizontalAlignment="Stretch" Margin="0,0,0,1" UseLayoutRounding="False" VerticalAlignment="Stretch" />
                </Grid>
                <Grid x:Name="on_Copy" HorizontalAlignment="Left" Height="13" Margin="25,1,0,0" VerticalAlignment="Center" Width="14" FlowDirection="LeftToRight">
                  <Path Stretch="Fill" Fill="White" Data="F1 M 614.727,178.803C 614.699,179.415 614.362,179.734 613.716,179.76L 609.57,179.76C 608.925,179.734 608.588,179.415 608.56,178.803L 608.56,168.717C 608.588,168.105 608.925,167.786 609.57,167.76L 613.716,167.76C 614.362,167.786 614.699,168.105 614.727,168.717L 614.727,178.803 Z M 612.893,178.023L 612.893,169.497L 610.393,169.497L 610.393,178.023L 612.893,178.023 Z M 622.56,179.76L 620.914,179.76L 618.104,172.208C 618.186,172.577 618.227,172.865 618.227,173.074L 618.227,179.76L 616.393,179.76L 616.393,167.76L 618.039,167.76L 620.849,175.159C 620.767,174.79 620.727,174.502 620.727,174.293L 620.727,167.76L 622.56,167.76L 622.56,179.76 Z " HorizontalAlignment="Stretch" Margin="0,1,0,0" UseLayoutRounding="False" VerticalAlignment="Stretch" />
                  <Path Stretch="Fill" Fill="#FF9A9C9D" Data="F1 M 614.727,178.803C 614.699,179.415 614.362,179.734 613.716,179.76L 609.57,179.76C 608.925,179.734 608.588,179.415 608.56,178.803L 608.56,168.717C 608.588,168.105 608.925,167.786 609.57,167.76L 613.716,167.76C 614.362,167.786 614.699,168.105 614.727,168.717L 614.727,178.803 Z M 612.893,178.023L 612.893,169.497L 610.393,169.497L 610.393,178.023L 612.893,178.023 Z M 622.56,179.76L 620.914,179.76L 618.104,172.208C 618.186,172.577 618.227,172.865 618.227,173.074L 618.227,179.76L 616.393,179.76L 616.393,167.76L 618.039,167.76L 620.849,175.159C 620.767,174.79 620.727,174.502 620.727,174.293L 620.727,167.76L 622.56,167.76L 622.56,179.76 Z " HorizontalAlignment="Stretch" Margin="0,0,0,1" UseLayoutRounding="False" VerticalAlignment="Stretch" />
                </Grid>
                <Grid Margin="8">
                  <Grid x:Name="thumb" Width="35" HorizontalAlignment="Left">
                    <Border x:Name="off" CornerRadius="3">
                      <Border.Background>
                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                          <GradientStop Color="#FFB2B2B2" Offset="0" />
                          <GradientStop Color="#FFA1A1A1" Offset="1" />
                        </LinearGradientBrush>
                      </Border.Background>
                      <Border CornerRadius="2" Margin="1" BorderThickness="0,1,0,0" BorderBrush="#FFE5E5E5">
                        <Border.Background>
                          <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                            <GradientStop Color="#FFD9D9D9" Offset="0" />
                            <GradientStop Color="#FFCACACA" Offset="1" />
                          </LinearGradientBrush>
                        </Border.Background>
                      </Border>
                    </Border>
                    <Border x:Name="on" CornerRadius="3" Opacity="0">
                      <Border.Background>
                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                          <GradientStop Color="#FF7E9BCD" Offset="0" />
                          <GradientStop Color="#FF6383BD" Offset="1" />
                        </LinearGradientBrush>
                      </Border.Background>
                      <Border CornerRadius="2" Margin="1" BorderThickness="0,1,0,0" BorderBrush="#FFBFD8F3">
                        <Border.Background>
                          <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                            <GradientStop Color="#FFA1BEE5" Offset="0" />
                            <GradientStop Color="#FF85A3D0" Offset="1" />
                          </LinearGradientBrush>
                        </Border.Background>
                      </Border>
                    </Border>
                    <Border x:Name="pressed" CornerRadius="3" Opacity="0">
                      <Border.Background>
                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                          <GradientStop Color="#FFB2B2B2" Offset="1" />
                          <GradientStop Color="#FF8B8B8B" />
                        </LinearGradientBrush>
                      </Border.Background>
                      <Border CornerRadius="2" Margin="1" BorderThickness="0" BorderBrush="#FFE5E5E5">
                        <Border.Background>
                          <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                            <GradientStop Color="Silver" Offset="1" />
                            <GradientStop Color="#FFA7A7A7" />
                          </LinearGradientBrush>
                        </Border.Background>
                      </Border>
                    </Border>
                    <Grid.RenderTransform>
                      <TranslateTransform x:Name="translate" />
                    </Grid.RenderTransform>
                  </Grid>
                </Grid>
              </Grid>
            </ControlTemplate>
          </Setter.Value>
        </Setter>
      </Style>
        
    </ResourceDictionary>
  </Application.Resources>
    
</Application>