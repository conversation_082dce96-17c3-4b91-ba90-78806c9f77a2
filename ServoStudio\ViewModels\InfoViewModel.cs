﻿using DevExpress.Mvvm;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Utils;
using DevExpress.Xpf;
using System.Windows.Input;
using ServoStudio;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using DevExpress.Mvvm.POCO;
using System.Collections.Generic;
using System.IO;
using System.Diagnostics;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class InfoViewModel : BindableBase
    {
        #region 服务
        [ServiceProperty(Key = "Administrator")]
        protected virtual IDialogService DialogService { get { return this.GetService<IDialogService>(); } }
        #endregion

        #region 属性
        public virtual string Version { get; set; }
        public virtual string ReleasedDate { get; set; }
        public virtual string CompanyName { get; set; }//公司名称
        public virtual string CompanyDepartment { get; set; }//公司及部门
        #endregion

        #region 构造函数
        public InfoViewModel()
        {
            Version = "Software Version - " + SoftwareInfo.VERSION;
            ReleasedDate = "Released Date - " + SoftwareInfo.RELEASED_DATE;

            CompanyName = "了解" + GlobalCompanyInfo.CompanyName;
            CompanyDepartment = GlobalCompanyInfo.CompanyDepartment;
        }
        #endregion

        #region 使用说明
        ICommand showHelpCommand;
        public ICommand ShowHelpCommand
        {
            get
            {
                if (showHelpCommand == null)
                    showHelpCommand = new DelegateCommand(ShowHelp, false);
                return showHelpCommand;
            }
        }
        public void ShowHelp()
        {
            try
            {
                if (File.Exists(FilePath.Manual))
                {
                    Process process = new Process();
                    process.StartInfo.FileName = FilePath.Manual;
                    process.StartInfo.Arguments = "";
                    process.StartInfo.WindowStyle = ProcessWindowStyle.Maximized;
                    process.Start();
                }
                else
                {
                    ShowNotification(2006);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_OPEN_MANUALBOOK, "OpenManualBook", ex);
            }
        }
        #endregion

        #region 伺服维护手册                    
        ICommand servoMaintenanceManualCommand;
        public ICommand ServoMaintenanceManualCommand
        {
            get
            {
                if (servoMaintenanceManualCommand == null)
                    servoMaintenanceManualCommand = new DelegateCommand(ServoMaintenanceManual, false);
                return servoMaintenanceManualCommand;
            }
        }

        //由Lilbert于2022.05.19添加伺服维护手册
        public void ServoMaintenanceManual()   
        {
            try
            {
                if (File.Exists(FilePath.MaintenanceManual))
                {
                    Process process = new Process();
                    process.StartInfo.FileName = FilePath.MaintenanceManual;
                    process.StartInfo.Arguments = "";
                    process.StartInfo.WindowStyle = ProcessWindowStyle.Maximized;
                    process.Start();
                }
                else
                {
                    ShowNotification(2006);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_OPEN_MANUALBOOK, "OpenManualBook", ex);
            }
        }
        #endregion

        #region 季华实验室网站
        ICommand showContactUsCommand;
        public ICommand ShowContactUsCommand
        {
            get
            {
                if (showContactUsCommand == null)
                    showContactUsCommand = new DelegateCommand(ShowContactUs, false);
                return showContactUsCommand;
            }
        }
        public void ShowContactUs()
        {
            //DevExpress.Xpf.Core.DocumentPresenter.OpenLink("http://www.jihualab.com/");
            DevExpress.Xpf.Core.DocumentPresenter.OpenLink(GlobalCompanyInfo.CompanyWeb);//由Lilbert于2023年10月8日更改网站可配置
        }
        #endregion

        #region 管理员密码
        ICommand showAdministratorKey;
        public ICommand ShowAdministratorKeyCommand
        {
            get
            {
                if (showAdministratorKey == null)
                    showAdministratorKey = new DelegateCommand(AdministratorKey, false);
                return showAdministratorKey;
            }
        }
        
        public void AdministratorKey()
        {
            try
            {
                OthersHelper.GetWindowsStartupPosition();
             
                if (ViewModelSet.Administrator == null)
                {
                    ViewModelSet.Administrator = new AdministratorViewModel();
                }

                UICommand registerCommand = new UICommand()
                {
                    Caption = "确认",
                    IsCancel = false,
                    IsDefault = true,
                };

                UICommand cancelCommand = new UICommand()
                {
                    Caption = "取消",
                    IsCancel = true,
                    IsDefault = false,
                };

                UICommand result = DialogService.ShowDialog(
                dialogCommands: new List<UICommand>() { registerCommand, cancelCommand },
                title: "请输入管理员密码",
                viewModel: ViewModelSet.Administrator);

                if (result == registerCommand)
                {
                    string strKey = ViewModelSet.Administrator?.AdministratorKey;
                    if (strKey == null)
                    {
                        strKey = "";
                    }

                    string strPasswordFromFile = IniHelper.IniReadValue("ServoStudio", "Password", FilePath.Ini);

                    if (strKey != strPasswordFromFile)
                    {
                        ShowNotification(2017);
                    }                 
                    else
                    {
                        ShowNotification(2018);
                        ViewModelSet.Main.AdministratorVisibility = true;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.INFO_ADMINISTATION_KEY, "AdministratorKey", ex);
            }
        }

        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }   
}
