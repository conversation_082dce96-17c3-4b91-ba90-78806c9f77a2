# ServoStudio 示波器XML配置解决方案

## 🎯 问题分析

经过深入代码分析，发现问题的根本原因是：

1. **单位字典硬编码**：`dicAcquisitionUint`字典在代码中硬编码，无法通过XML配置
2. **GroupName逻辑存在但未使用**：代码中已有根据GroupName推断单位的逻辑，但只用于UI显示，未用于数据采集
3. **XML结构不完整**：当前XML只有基本的Channel信息，缺少单位配置

## 🔧 解决方案：扩展XML配置 + 修改单位推断逻辑

### 方案1：扩展XML结构（推荐）

#### 1.1 修改XML文件结构

在`OscilloscopeOptions.xml`中添加Unit属性：

```xml
<?xml version="1.0" encoding="utf-8" ?>
<OscilloscopeOptions>
  <SamplingPeriods>
    <Period>62.5μs</Period>
    <Period>125μs</Period>
    <Period>250μs</Period>
    <Period>500μs</Period>
    <Period>1ms</Period>
    <Period>2ms</Period>
    <Period>4ms</Period>
    <Period>8ms</Period>
  </SamplingPeriods>
  <SampleChannels>
    <Channel GroupName="停用 (1)" ID="0" ItemName="停用" Address="" Unit="" />
    <Channel GroupName="位置 (5)" ID="1" ItemName="位置指令" Address="03" Unit="Position" />
    <Channel GroupName="位置 (5)" ID="2" ItemName="滤波后位置指令" Address="1A" Unit="Position" />
    <Channel GroupName="位置 (5)" ID="3" ItemName="位置反馈" Address="04" Unit="Position" />
    <Channel GroupName="位置 (5)" ID="4" ItemName="位置差值" Address="05" Unit="Position" />
    <Channel GroupName="位置 (5)" ID="5" ItemName="控制器下发位置指令" Address="24" Unit="Position" />
    <Channel GroupName="位置 (5)" ID="6" ItemName="位置增量" Address="25" Unit="Position" />
    <Channel GroupName="位置 (5)" ID="43" ItemName="位置增量的增量" Address="2B" Unit="Position" />
    <Channel GroupName="速度 (7)" ID="7" ItemName="位置环输出速度指令" Address="06" Unit="Speed" />
    <Channel GroupName="速度 (7)" ID="8" ItemName="速度指令" Address="07" Unit="Speed" />
    <!-- ... 其他通道 ... -->
    <Channel GroupName="Debug (4)" ID="38" ItemName="Debug参数1" Address="26" Unit="" />
    <Channel GroupName="Debug (4)" ID="39" ItemName="Debug参数2" Address="27" Unit="" />
    <Channel GroupName="Debug (4)" ID="40" ItemName="Debug参数3" Address="28" Unit="" />
    <Channel GroupName="Debug (4)" ID="41" ItemName="Debug参数4" Address="29" Unit="" />
    <Channel GroupName="Debug (4)" ID="42" ItemName="Debug参数5" Address="2A" Unit="" />
  </SampleChannels>
</OscilloscopeOptions>
```

#### 1.2 修改数据结构

扩展`SampleChannelInfoSet`类：

```csharp
public class SampleChannelInfoSet
{
    public string GroupName { get; set; }
    public int ID { get; set; }
    public string ItemName { get; set; }
    public string Address { get; set; }
    public string Unit { get; set; }  // 新增Unit属性
}
```

#### 1.3 修改XML解析代码

更新`XmlHelper.GetSampleChannels()`方法：

```csharp
public static List<SampleChannelInfoSet> GetSampleChannels()
{
    List<SampleChannelInfoSet> channels = new List<SampleChannelInfoSet>();
    string xPath = "OscilloscopeOptions/SampleChannels/Channel";
    XmlNodeList xmlNodeList = xmlDocOptions.SelectNodes(xPath);
    foreach (XmlNode xNode in xmlNodeList)
    {
        SampleChannelInfoSet channel = new SampleChannelInfoSet
        {
            GroupName = xNode.Attributes["GroupName"].Value,
            ID = Convert.ToInt32(xNode.Attributes["ID"].Value),
            ItemName = xNode.Attributes["ItemName"].Value,
            Address = xNode.Attributes["Address"].Value,
            Unit = xNode.Attributes["Unit"]?.Value ?? ""  // 新增Unit读取，兼容旧格式
        };
        channels.Add(channel);
    }
    return channels;
}
```

#### 1.4 修改单位字典构建逻辑

更新`GetOscilloscopeParameterUnitSet()`方法：

```csharp
public static void GetOscilloscopeParameterUnitSet()
{
    SoftwareStateParameterSet.dicAcquisitionUint = new Dictionary<string, string>();

    // 从XML配置动态构建单位字典
    var channels = XmlHelper.GetSampleChannels();
    foreach (var channel in channels)
    {
        string unit = GetUnitFromConfig(channel);
        SoftwareStateParameterSet.dicAcquisitionUint[channel.ItemName] = unit;
    }
}

private static string GetUnitFromConfig(SampleChannelInfoSet channel)
{
    // 优先使用XML中配置的Unit
    if (!string.IsNullOrEmpty(channel.Unit))
    {
        switch (channel.Unit.ToLower())
        {
            case "position": return SelectUnit.Position;
            case "speed": return SelectUnit.Speed;
            case "torque": return SelectUnit.Torque;
            case "current": return "mA";
            case "voltage": return "V";
            case "pulse": return "Pulse";
            case "percent": return "‰";
            case "none":
            case "":
            default: return "";
        }
    }

    // 如果XML中没有配置Unit，则根据GroupName推断
    return InferUnitFromGroupName(channel.GroupName, channel.ItemName);
}

private static string InferUnitFromGroupName(string groupName, string itemName)
{
    if (string.IsNullOrEmpty(groupName)) return "";

    if (groupName.StartsWith("位置"))
        return SelectUnit.Position;
    else if (groupName.StartsWith("速度"))
        return SelectUnit.Speed;
    else if (groupName.StartsWith("转矩"))
        return itemName.Contains("电流") ? "mA" : "‰";
    else if (groupName.StartsWith("前馈"))
        return itemName.Contains("速度") ? SelectUnit.Speed : "‰";
    else if (groupName.StartsWith("编码器"))
        return "Pulse";
    else if (groupName.StartsWith("母线"))
        return itemName.Contains("电压") ? "V" : "mA";
    else if (groupName.StartsWith("标志") || groupName.StartsWith("Debug"))
        return "";
    else
        return "";
}
```

### 方案2：仅修改单位推断逻辑（最小改动）

如果不想修改XML结构，可以只修改单位推断逻辑：

```csharp
public static void GetOscilloscopeParameterUnitSet()
{
    SoftwareStateParameterSet.dicAcquisitionUint = new Dictionary<string, string>();

    // 保留原有的硬编码单位
    SoftwareStateParameterSet.dicAcquisitionUint.Add("停用", "");
    SoftwareStateParameterSet.dicAcquisitionUint.Add("母线电压", "V");
    // ... 其他原有配置 ...

    // 从XML配置动态添加缺失的单位
    var channels = XmlHelper.GetSampleChannels();
    foreach (var channel in channels)
    {
        if (!SoftwareStateParameterSet.dicAcquisitionUint.ContainsKey(channel.ItemName))
        {
            string unit = InferUnitFromGroupName(channel.GroupName, channel.ItemName);
            SoftwareStateParameterSet.dicAcquisitionUint.Add(channel.ItemName, unit);
        }
    }
}
```

## 🎯 推荐实施方案

### 立即解决方案（最小改动）

1. **修改`GetOscilloscopeParameterUnitSet()`方法**，添加动态单位推断逻辑
2. **无需修改XML文件**，利用现有的GroupName信息
3. **向后兼容**，不影响现有配置

### 长期优化方案

1. **扩展XML结构**，添加Unit属性
2. **提供配置工具**，让用户可以通过界面配置通道单位
3. **支持自定义单位**，满足特殊需求

## 📝 具体修改步骤

### 步骤1：修改OthersHelper.cs

在`GetOscilloscopeParameterUnitSet()`方法末尾添加：

```csharp
// 动态添加XML中配置但字典中缺失的通道单位
var channels = XmlHelper.GetSampleChannels();
foreach (var channel in channels)
{
    if (!SoftwareStateParameterSet.dicAcquisitionUint.ContainsKey(channel.ItemName))
    {
        string unit = InferUnitFromGroupName(channel.GroupName, channel.ItemName);
        SoftwareStateParameterSet.dicAcquisitionUint.Add(channel.ItemName, unit);
    }
}
```

添加单位推断方法：

```csharp
private static string InferUnitFromGroupName(string groupName, string itemName)
{
    if (string.IsNullOrEmpty(groupName)) return "";

    if (groupName.StartsWith("位置"))
        return SelectUnit.Position;
    else if (groupName.StartsWith("速度"))
        return SelectUnit.Speed;
    else if (groupName.StartsWith("转矩"))
        return itemName.Contains("电流") ? "mA" : "‰";
    else if (groupName.StartsWith("前馈"))
        return itemName.Contains("速度") ? SelectUnit.Speed : "‰";
    else if (groupName.StartsWith("编码器"))
        return "Pulse";
    else if (groupName.StartsWith("母线"))
        return itemName.Contains("电压") ? "V" : "mA";
    else if (groupName.StartsWith("标志") || groupName.StartsWith("Debug"))
        return "";
    else
        return "";
}
```

### 步骤2：测试验证

1. **添加测试通道**：
```xml
<Channel GroupName="位置 (5)" ID="43" ItemName="位置增量的增量" Address="2B" />
<Channel GroupName="Debug (4)" ID="42" ItemName="Debug参数5" Address="2A" />
```

2. **验证数据采集**：确认新增通道能正常采集数据
3. **验证单位显示**：确认波形显示正确的单位

## 🎉 优势

1. **完全基于XML配置**：用户可以通过修改XML文件添加新通道
2. **自动单位推断**：根据GroupName自动推断合适的单位
3. **向后兼容**：不影响现有配置和功能
4. **易于维护**：新增通道无需修改代码

这个解决方案完全满足您的需求：**通过XML文件就可以配置，而不用修改代码**！
