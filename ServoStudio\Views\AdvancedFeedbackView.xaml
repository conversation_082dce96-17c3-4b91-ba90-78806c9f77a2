﻿<UserControl x:Class="ServoStudio.Views.AdvancedFeedbackView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:converter="clr-namespace:Converter"
             xmlns:Views="clr-namespace:ServoStudio.Views"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:AdvancedFeedbackViewModel}"
             d:DesignHeight="800" d:DesignWidth="1300">
    <UserControl.Resources>
        <Style x:Key="myTabItem" TargetType="dx:DXTabItem">
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Trigger.Setters>
                        <Setter Property="FontStyle" Value="Italic"/>
                        <Setter Property="Foreground" Value="Orange"/>
                    </Trigger.Setters>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style  x:Key="myCheckEdit" TargetType="dxe:CheckEdit">
            <Style.Triggers>
                <Trigger Property="IsChecked" Value="True">
                    <Trigger.Setters>
                        <Setter Property="Foreground" Value="Green"/>
                    </Trigger.Setters>
                </Trigger>
            </Style.Triggers>
        </Style>

        <converter:BackgroundConverter x:Key="BackgroudConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>
        <converter:ContentConverter x:Key="ContentConverter"/>
    </UserControl.Resources>

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding AdvancedFeedbackLoadedCommand}"/>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields" UseWin8NotificationsIfAvailable="True"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
        
        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Label Grid.Row="0" Margin="3" Style="{StaticResource LabelStyle}" Content="高级功能配置参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Grid Grid.Row="1" Margin="3">
                <TabControl Padding="0" SelectedIndex="{Binding SelectedTabIndex,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" BorderBrush="LightGray" Background="{x:Null}" BorderThickness="0,1,0,0">
                    <TabItem Header="模式切换" TabIndex="0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="170"/>
                                <ColumnDefinition Width="90"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="180"/>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="增益切换参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                            <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="增益切换时间1" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding GainChangeTimeOne,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="1" Grid.Column="3" Text="ms" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="增益切换时间2" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding GainChangeTimeTwo,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="2" Grid.Column="3" Text="ms" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="1" Grid.Column="5" Margin="10,9" Content="增益切换等待时间1" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding GainChangeWaitTimeOne,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="1" Grid.Column="7" Text="ms" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="2" Grid.Column="5" Margin="10,9" Content="增益切换等待时间2" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding GainChangeWaitTimeTwo,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="2" Grid.Column="7" Text="ms" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="3" Grid.Column="1" Margin="10,9" Content="增益切换开关[0-3]bit" Style="{StaticResource LabelStyle}" />
                            <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="2" Width="240" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding GainSwitch}" SelectedItem="{Binding SelectedGainSwitch,Mode=TwoWay}"/>

                            <Label Grid.Row="3" Grid.Column="5" Margin="10,9" Content="增益切换开关[4-8]bit" Style="{StaticResource LabelStyle}" />
                            <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="6" Width="240" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding GainSwitchCondition}" SelectedItem="{Binding SelectedGainSwitchCondition,Mode=TwoWay}"/>

                            <Label Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="模式切换参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                            <Label Grid.Row="5" Grid.Column="1" Margin="10,9" Content="速度模式开关设置" Style="{StaticResource LabelStyle}" />
                            <dxe:ComboBoxEdit Grid.Row="5" Grid.Column="2" Width="240" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding SpeedModeSwitch}" SelectedItem="{Binding SelectedSpeedModeSwitch,Mode=TwoWay}"/>

                            <Label Grid.Row="5" Grid.Column="5" Margin="10,9" Content="模式开关(转矩指令)" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="5" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ModeSwitchTorqueValue,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="5" Grid.Column="7" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="6" Grid.Column="1" Margin="10,9" Content="模式开关(速度指令)" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="6" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ModeSwitchSpeedValue,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="6" Grid.Column="3" Text="rpm" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="6" Grid.Column="5" Margin="10,9" Content="模式开关(加速度)" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="6" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ModeSwitchAccValue,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="6" Grid.Column="7" Text="rpm/s" Style="{StaticResource TextBoxStyle_Unit}" />

                        </Grid>
                    </TabItem>

                    <TabItem Header="振动抑制" TabIndex="1">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="170"/>
                                <ColumnDefinition Width="90"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="180"/>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="A型抑振参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                            <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="A型抑振控制选择[0-3]bit" Style="{StaticResource LabelStyle}" />
                            <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Width="240" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding VibrationSuppressionASwitch}" SelectedItem="{Binding SelectedVibrationSuppressionASwitch,Mode=TwoWay}"/>

                            <Label Grid.Row="1" Grid.Column="5" Margin="10,9" Content="A型抑振控制选择[4-7]bit" Style="{StaticResource LabelStyle}" />
                            <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="6" Width="240" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding SelfTuningSet}" SelectedItem="{Binding SelectedSelfTuningSet,Mode=TwoWay}"/>

                            <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="A型抑振频率" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding VibsupFreq,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="2" Grid.Column="3" Text="0.1Hz" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="2" Grid.Column="5" Margin="10,9" Content="A型抑振增益补偿" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding VibsupGainComp,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="2" Grid.Column="7" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="3" Grid.Column="1" Margin="10,9" Content="A型抑振阻尼增益" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding VibsupDampingGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="3" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="摩擦补偿参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                            <Label Grid.Row="5" Grid.Column="1" Margin="10,9" Content="高级应用开关[0-3]bit" Style="{StaticResource LabelStyle}" />
                            <dxe:ComboBoxEdit Grid.Row="5" Grid.Column="2" Width="240" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding SpeedObserverSwitch}" SelectedItem="{Binding SelectedSpeedObserverSwitch,Mode=TwoWay}"/>

                            <Label Grid.Row="5" Grid.Column="5" Margin="10,9" Content="高级应用开关[4-7]bit" Style="{StaticResource LabelStyle}" />
                            <dxe:ComboBoxEdit Grid.Row="5" Grid.Column="6" Width="240" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding DisturbanceObserverSwitch}" SelectedItem="{Binding SelectedDisturbanceObserverSwitch,Mode=TwoWay}"/>

                            <Label Grid.Row="6" Grid.Column="1" Margin="10,9" Content="摩擦补偿增益" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="6" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding DisturbanceObserverGainOne,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="6" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="6" Grid.Column="5" Margin="10,9" Content="摩擦补偿增益2" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="6" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding DisturbanceObserverGainTwo,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="6" Grid.Column="7" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="7" Grid.Column="1" Margin="10,9" Content="摩擦补偿系数" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="7" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding DisturbanceObserverCoefficient,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="7" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="7" Grid.Column="5" Margin="10,9" Content="摩擦补偿频率补偿" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="7" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding DisturbanceObserverFreqCorrection,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="7" Grid.Column="7" Text="0.1Hz" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="8" Grid.Column="1" Margin="10,9" Content="摩擦补偿增益补偿" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="8" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding DisturbanceObserverGainCorrection,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="8" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="8" Grid.Column="5" Margin="10,9" Content="速度观测增益" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="8" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding SpeedObserverGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="8" Grid.Column="7" Text="Hz" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="9" Grid.Column="1" Margin="10,9" Content="速度观测补偿增益" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="9" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding SpeedObserverPosCompensationGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="9" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="10" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="末端抖动抑制参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                            <Label Grid.Row="11" Grid.Column="1" Margin="10,9" Content="末端抖动抑制控制选择" Style="{StaticResource LabelStyle}" />
                            <dxe:ComboBoxEdit Grid.Row="11" Grid.Column="2" Width="240" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding EndVibrationSuppressionOption}" SelectedItem="{Binding SelectedEndVibrationSuppressionOption,Mode=TwoWay}"/>

                            <Label Grid.Row="11" Grid.Column="5" Margin="10,9" Content="末端抖动抑制频率" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="11" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding EndVibrationSuppressionFrequency,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="11" Grid.Column="7" Text="0.1Hz" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="12" Grid.Column="1" Margin="10,9" Content="末端抖动抑制补偿" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="12" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding EndVibrationSuppressionCompensation,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="12" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}" />
                        </Grid>
                    </TabItem>

                    <TabItem Header="模型追踪" TabIndex="2">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="170"/>
                                <ColumnDefinition Width="90"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="180"/>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="模型追踪参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                            <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="模型追踪控制开关[0-3]bit" Style="{StaticResource LabelStyle}" />
                            <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Width="240" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding ModelFollowingSwitch}" SelectedItem="{Binding SelectedModelFollowingSwitch,Mode=TwoWay}"/>

                            <Label Grid.Row="1" Grid.Column="5" Margin="10,9" Content="模型追踪控制开关[4-7]bit" Style="{StaticResource LabelStyle}" />
                            <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="6" Width="240" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding ModelFollowingVibrationSuppressionSwitch}" SelectedItem="{Binding SelectedModelFollowingVibrationSuppressionSwitch,Mode=TwoWay}"/>

                            <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="模型追踪控制增益" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ModelFollowingControlGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="2" Grid.Column="3" Text="0.1Hz" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="2" Grid.Column="5" Margin="10,9" Content="模型追踪控制增益补偿" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding MFCGainCorrection,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="2" Grid.Column="7" Text="0.1%" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="3" Grid.Column="1" Margin="10,9" Content="模型追踪控制增益偏置(正向)" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding MFCForwardBias,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="3" Grid.Column="3" Text="0.1%" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="3" Grid.Column="5" Margin="10,9" Content="模型追踪控制增益偏置(反向)" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="3" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding MFCReverseBias,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="3" Grid.Column="7" Text="0.1%" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="4" Grid.Column="1" Margin="10,9" Content="振动抑制1频率A" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding VibrationSuppressionFrequencyA,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="4" Grid.Column="3" Text="0.1Hz" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="4" Grid.Column="5" Margin="10,9" Content="振动抑制1频率B" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="4" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding VibrationSuppressionFrequencyB,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="4" Grid.Column="7" Text="0.1Hz" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="5" Grid.Column="1" Margin="10,9" Content="模型追踪控制速度前馈补偿" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="5" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding MFCVelocityFeedforwardCompensation,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="5" Grid.Column="3" Text="0.1%" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="5" Grid.Column="5" Margin="10,9" Content="第2模型追踪控制增益" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="5" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding MFCGainTwo,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="5" Grid.Column="7" Text="0.1Hz" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="6" Grid.Column="1" Margin="10,9" Content="第2模型追踪控制增益补偿" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="6" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding MFCGainCorrectionTwo,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="6" Grid.Column="3" Text="0.1%" Style="{StaticResource TextBoxStyle_Unit}" />
                        </Grid>
                    </TabItem>

                    <TabItem Header="弱磁控制" TabIndex="3">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="170"/>
                                <ColumnDefinition Width="90"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="180"/>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="弱磁控制参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                            <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="弱磁控制电压反馈增益" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding WeakFieldControlGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="1" Grid.Column="3" Text="Hz" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="1" Grid.Column="5" Margin="10,9" Content="弱磁控制电压反馈时间常数" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding WeakFieldControlTimeConstant,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="1" Grid.Column="7" Text="0.001ms" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="弱磁最大速度对应的Id指令" Style="{StaticResource LabelStyle}" />
                            <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding WeakFieldMaxSpeedCorrespondingToIdRef,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                            <TextBox Grid.Row="2" Grid.Column="3" Text="0.1%" Style="{StaticResource TextBoxStyle_Unit}" />

                            <Label Grid.Row="2" Grid.Column="5" Margin="10,9" Content="弱磁控制开关" Style="{StaticResource LabelStyle}" />
                            <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="6" Width="240" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding WeakFieldControlSwitch}" SelectedItem="{Binding SelectedWeakFieldControlSwitch,Mode=TwoWay}"/>

                        </Grid>

                    </TabItem>
                </TabControl>
            </Grid>

            <Label Grid.Row="10" Margin="3,3,3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <StackPanel Grid.Row="11" HorizontalAlignment="Right" Orientation="Horizontal">
                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadAdvancedFeedbackParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                    <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultAdvancedFeedbackParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                    <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" Command="{Binding WriteAdvancedFeedbackParameterCommand}" CommandParameter="{Binding SelectedTabIndex}">
                    <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>
            </StackPanel>

            <!--<Grid Grid.Row="7">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="1" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetCurrentLoopConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="2" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" />

            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="3" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SaveCurrentLoopConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="4" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}"/>
        </Grid>-->
        </Grid>
        
    </ScrollViewer>
     
</UserControl>
