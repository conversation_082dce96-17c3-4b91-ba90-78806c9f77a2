﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using System.Collections.ObjectModel;
using ServoStudio;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Collections.Generic;
using ServoStudio.Models;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.IO.Ports;
using System.Windows;
using System.Windows.Threading;
using System.Threading;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace ServoStudio.ViewModels
{

    public class MainWindowViewModel
    {
        #region 事件 
        public event EvaluationSerialPortConnectState evtEvaluationSerialPortConnectState;//状态栏串口连接状态   
        public event GetOtherViewModelProperty evtBrake;//获取BrakeViewModel属性值
        public event GetOtherViewModelProperty evtFilter;//获取FilterViewModel属性值    
        #endregion

        #region 字段       
        public DispatcherTimer Timer_System = new DispatcherTimer();//系统时间  
        private DispatcherTimer Timer_Info = new DispatcherTimer();//信息提示时间
        private DispatcherTimer Timer_RestartSystem = new DispatcherTimer();//重启系统时间
        private static int InfoDisplayCountDown;//信息展示倒计时
        private static int RestartSystemCoutDown;//系统重启倒计时
        private static bool IsInitialized = true;
        private string AllAxisFunction = null;

        public string SelectedFaultSamplingChannel1;
        public string SelectedFaultSamplingChannel2;
        public string SelectedFaultSamplingChannel3;
        public string SelectedFaultSamplingChannel4;
        public string SelectedFaultSamplingChannel5;
        public string SelectedFaultSamplingChannel6;
        public string SelectedFaultSamplingChannel7;
        public string SelectedFaultSamplingChannel8;
        #endregion

        #region 服务
        [ServiceProperty(Key = "ServiceWithDefaultNotifications")]
        protected virtual INotificationService DefaultNotificationService { get { return null; } }
        protected virtual ICurrentWindowService CurrentWindowService { get { return this.GetService<ICurrentWindowService>(); } }
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }

        [ServiceProperty(Key = "SwitchAxis")]
        protected virtual IDialogService DialogService_SwitchAxis { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "MotorParameterIdentification")]
        protected virtual IDialogService DialogService_MotorParameterIdentification { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "UnitSet")]
        protected virtual IDialogService DialogService_UnitSet { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "ModifyPassword")]
        protected virtual IDialogService DialogService_ModifyPassword { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "Jog")]
        protected virtual IDialogService DialogService_Jog { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "ProgramJog")]
        protected virtual IDialogService DialogService_ProgramJog { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "OfflineInertiaIdentification")]
        protected virtual IDialogService DialogService_OfflineInertiaIdentification { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "ParameterImport")]
        protected virtual IDialogService DialogService_ParameterImport { get { return this.GetService<IDialogService>(); } }
        #endregion

        #region 属性   
        //控件使能
        public virtual bool IsAllPageEnabled { get; set; }//是否界面可以使用 
        public virtual bool IsMonitoringPageHidden { get; set; }//监控界面是否隐藏
        public virtual string MonitoringPageExpandState { get; set; }//监控界面展开状态
        public virtual bool IsAlarmInfoPageHidden { get; set; }//报警信息界面是否隐藏
        public virtual bool IsQuickZoomPageHidden { get; set; }//快捷信息界面是否隐藏
        public virtual bool IsHintInfoEnabled { get; set; }//信息提示显示
        public virtual bool IsHintMonitorEnabled { get; set; }//参数监控显示
        public virtual bool IsRibbonMinimized { get; set; }//是否Ribbon最小化
        public virtual bool IsUnitExchangedEnabled { get; set; }//是否单位换算使能
        public virtual bool IsInertiaRatioEnabled { get; set; }//惯量比是否隐藏

        //状态栏
        public virtual string SerialPortConnectState { get; set; }//串口通信状态
        public virtual int SerialPortConnectStateFontColor { get; set; }//串口通信状态字体颜色
        public virtual string SerialPortWorkState { get; set; }//串口工作状态    
        public virtual string AxisAddress { get; set; }//轴地址
        public virtual string SystemStatus { get; set; }//系统状态
        public virtual string PositionFeedback { get; set; }//位置反馈
        public virtual string CurrentFeedback { get; set; }//电流反馈
        public virtual string SpeedFeedback { get; set; }//速度反馈
        public virtual string InertiaRatio { get; set; }//惯量比    
        public virtual string StatusWord { get; set; }//状态字
        public virtual int StatusWordFontColor { get; set; }//状态字颜色
        public virtual string HintInfo { get; set; }//信息提示
        public virtual int HintInfoFontColor { get; set; }//信息提示字体颜色

        //侧边栏
        public virtual ObservableCollection<SportMonitoringSet> SportMonitoring{ get; set; }//运动监视属性
        public virtual ObservableCollection<ParameterMonitoringSet> ParameterMonitoring { get; set; }//参数监视属性
        public virtual ObservableCollection<DigitalInputSet> DigitalInput { get; set; }//数字输入属性
        public virtual ObservableCollection<DigitalOutputSet> DigitalOutput { get; set; }//数字输出属性
        public virtual ObservableCollection<SlideHardwareAlarmSet> SlideHardwareAlarm { get; set; }//报警信息集合
        
        //功能栏
        public virtual string SlideAlarm { get; set; }//侧边框报警
        public virtual string SlideAlarmIcon { get; set; }
        public virtual string SlideMonitor { get; set; }//侧边栏监控
        public virtual string SlideMonitorIcon { get; set; }
        public virtual string SlideServoStatus { get; set; }//侧边栏伺服状态
        public virtual string SlideServoStatusIcon { get; set; }

        public virtual string SlideControlModel { get; set; }//侧边栏控制权
        public virtual string ControlModelIcon { get; set; }//侧边栏控制权图标

        //管理员
        public virtual bool AdministratorVisibility { get; set; }//管理员模式
        public virtual bool LevelFactoryModeVisibility { get; set; }//出厂模式

        //伺服使能禁能
        public virtual string ServoStatusSwitch { get; set; }//伺服使能禁能开关
        public virtual string ServoStatusIcon { get; set; }//伺服使能禁能图标

        public virtual string A { get; set; }//轮廓加速度
        public virtual string B { get; set; }//轮廓加速度
        public virtual string C { get; set; }//轮廓加速度
        public virtual string D { get; set; }//轮廓加速度
        #endregion

        #region 构造函数
        public MainWindowViewModel() { ViewModelSet.Main = this; }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：MainWindowLoaded
        //函数功能：主窗体Loaded
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.15
        //*************************************************************************
        public void MainWindowLoaded()
        {
            //初始界面导航           
            CommunicationSetNavigation();

            //ObservableCollection初始化
            ObservableCollectionInitialize();

            //时钟初始化
            DispatcherTimerInitialize();

            //事件注册
            EventInitialize();

            //其他初始化
            OthersInitialize();

            //线程初始化
            ThreadPool_SerialPortTransmitting();

            //判断系统是否已经开启
            CheckIsSystemOpened();
        }

        #region 文件导入导出
        //*************************************************************************
        //函数名称：ImportConfigFile
        //函数功能：打开Excel配置文件，获取配置文件数据，增加对软件版本号进行校验
        //
        //输入参数：None
        //
        //输出参数：None
        //
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public void ImportConfigFile(string Action)
        {
            int iRet = -1;
            try
            {
                // 1. 检查串口连接
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000); // "串口未开启，请稍后重试..."
                    return;
                }

                // 2. 提示用户正在刷新
                ShowHintInfo("正在刷新设备参数，请稍候...");

                // 3. 发起后台刷新任务
                // 这个方法会触发一个“导入前读取”的特定任务
                ExportConfigFile_ForImportConfig();
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_IMPORT_CONFIG_FILE, "MainWindowViewModel.ImportConfigFile", ex);
            }
        }

        /// <summary>
        /// 在后台参数刷新完成后，继续执行导入的后续步骤
        /// 此方法由 CommunicationSetViewModel 在完成特定任务后回调
        /// </summary>
        public void ContinueWithImportAfterRefresh()
        {
            int iRet = -1;
            string strFilePath = null;
            string strInfo = null;
            string stringInfo = null;

            try
            {
                // 关键修复：在执行任何操作前，先将硬件的实时值同步到用于UI和比对的DataTable中
                iRet = OthersHelper.DataTableExportUpdate(ref GlobalParameterSet.dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowHintInfo("同步实时参数失败，无法进行比对。");
                    return;
                }

                // 1. 获取导入文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                // 2. 版本校验
                // 获取伺服驱动器的软件版本号
                strInfo = SoftwareStateParameterSet.ServoSoftwareVersion.Substring(9, 4);
                // 查询导入Excel中软件版本号数据
                iRet = ExcelHelper.ReadCellValueFromExcel(strFilePath, ref GlobalParameterSet.dt_Import_SoftVersion);
                stringInfo = SoftwareStateParameterSet.ServoSoftwareVersion_For_Excel.Substring(9, 4);

                if (stringInfo != strInfo)
                {
                    ShowHintInfo("EEPROM版本号不同，不能导入文件参数...");
                    return;
                }

                // 3. 读取并比对差异
                iRet = ExcelHelper.ReadFromExcel_For_dtImport(strFilePath, ref GlobalParameterSet.dt_Import);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowHintInfo("导入文件加载失败，请确认文件是否被占用...");
                    return;
                }
                
                // 使用最新的硬件参数 (GlobalParameterSet.dt) 进行比对
                iRet = OthersHelper.Excel_For_Compare(GlobalParameterSet.dt_Import, GlobalParameterSet.dt, ref GlobalParameterSet.Out_dtImport, ref GlobalParameterSet.dt_Diff);

                // 4. 显示差异对话框
                if (ViewModelSet.ParameterImport == null)
                {
                    ViewModelSet.ParameterImport = new ParameterImportViewModel();
                }
                OthersHelper.GetWindowsStartupPosition();
                UICommand registerCommand = new UICommand() { Caption = "确认", IsCancel = false, };
                UICommand cancelCommand = new UICommand() { Caption = "返回", IsCancel = true, };
                UICommand result = DialogService_ParameterImport.ShowDialog(new List<UICommand>() { registerCommand, cancelCommand }, "参数导入差异表", ViewModelSet.ParameterImport);

                // 5. 用户确认后写入差异参数
                if (registerCommand == result)
                {
                    if (GlobalParameterSet.dt_Diff == null || GlobalParameterSet.dt_Diff.Rows.Count == 0)
                    {
                        ShowHintInfo("导入参数没有差异数据...");
                        return;
                    }

                    if (MessageBoxService.ShowMessage("是否将导入的差异参数写入到RAM中...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.No)
                    {
                        return;
                    }

                    iRet = OthersHelper.DataTableToTransmitingDataInfoSet_For_Compare(GlobalParameterSet.dt_Diff, ref SoftwareStateParameterSet.lstTransmittingDataInfo);
                    if (iRet == RET.SUCCEEDED)
                    {
                        SoftwareStateParameterSet.iBatchIndex_ForImportConfig = 0;
                        // 使用新的任务名发起写入，将刷新操作交由回调处理
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterBatchWrite_ForCompare(SoftwareStateParameterSet.iBatchIndex_ForImportConfig, SoftwareStateParameterSet.lstTransmittingDataInfo, TaskName.ImportWriteAndRefresh);
                    }
                    else
                    {
                        ShowNotification(RET.ERROR);
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_IMPORT_CONFIG_FILE_FOR_COMPARISONDIFF, "ContinueWithImportAfterRefresh", ex);
            }
        }

        //*************************************************************************
        //函数名称：ExportConfigFile
        //函数功能：导出配置文件,增加导出伺服驱动器软件版本号
        //
        //输入参数：None
        //
        //输出参数：None
        //
        //编码作者：Lilbert
        //更新时间：2023.06.02
        //*************************************************************************
        public void ExportConfigFile()
        {
            int iRet = -1;

            try
            {
                iRet = OthersHelper.DataTableToReceivingDataInfoSet(GlobalParameterSet.dt, ref SoftwareStateParameterSet.lstReceivingDataInfo);
                if (iRet == RET.SUCCEEDED)
                {
                    SoftwareStateParameterSet.iBatchIndex = 0;
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_ParameterBatchRead_For_Softwareversion(SoftwareStateParameterSet.iBatchIndex, SoftwareStateParameterSet.lstReceivingDataInfo);
                }
                else
                {
                    ShowNotification(RET.ERROR);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_EXPORT_CONFIG_FILE, "ExportConfigFile", ex);
            }

            //如果串口没打开，则不读取版本号
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                return;
            }
            else
            {
                GetEditionInfo_For_Softwareversion();    //获取伺服软件版本号
            }
        }

        //*************************************************************************
        //函数名称：ExportConfigFile
        //函数功能：导出配置文件
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public void ExportConfigFile_ForImportConfig()
        {
            int iRet = -1;

            try
            {
                iRet = OthersHelper.DataTableToReceivingDataInfoSet(GlobalParameterSet.dt, ref SoftwareStateParameterSet.lstReceivingDataInfo);
                if (iRet == RET.SUCCEEDED)
                {
                    SoftwareStateParameterSet.iBatchIndex_ForImportConfig = 0;
                    //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_ParameterBatchRead_For_Softwareversion(SoftwareStateParameterSet.iBatchIndex, SoftwareStateParameterSet.lstReceivingDataInfo);
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterBatchRead_ForImportConfig(SoftwareStateParameterSet.iBatchIndex_ForImportConfig, SoftwareStateParameterSet.lstReceivingDataInfo);
                }
                else
                {
                    ShowNotification(RET.ERROR);
                }

                //删除Excel文件
                //File.Delete(strFilePath);            
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_EXPORT_CONFIG_FILE, "ExportConfigFile", ex);
            }

            //如果串口没打开，则不读取版本号
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                return;
            }
            else
            {
                GetEditionInfo_For_Softwareversion();    //获取伺服软件版本号                
            }
        }

        //*************************************************************************
        //函数名称：ExportMoniotorFile
        //函数功能：导出参数监控文件
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.22
        //*************************************************************************
        public void ExportMoniotorFile()
        {
            int iRet = -1;

            try
            {
                //更新Excel配置文件
                iRet = ExcelHelper.WriteIntoExcel(FilePath.Monitor, GlobalParameterSet.dt_Monitor, ExcelType.Monitor);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2002);
                } 
                else
                {
                    ShowNotification(RET.ERROR);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_EXPORT_MONITOR_FILE, "ExportMoniotorFile", ex);
            }
        }
        #endregion

        #region 信息确认与快捷键执行
        //*************************************************************************
        //函数名称：ExitHint
        //函数功能：退出信息提示
        //
        //输入参数：None
        //         
        //输出参数：1：退出
        //        非1：不退出
        //        
        //编码作者：Ryan
        //更新时间：2019.11.21
        //*************************************************************************
        public int ExitHint()
        {           
            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return RET.NO_EFFECT;
                }

                if (MessageBoxService.ShowMessage("是否要关闭ServoStudio...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    return RET.SUCCEEDED;
                }
                else
                {
                    return RET.NO_EFFECT;
                }                   
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_EXIT, "Exit", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：SystemRestart
        //函数功能：系统重启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.03
        //*************************************************************************
        public void SystemRestart()
        {
            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (MessageBoxService.ShowMessage("是否要重启ServoStudio系统..." + "\r\n" + "重启可能会影响正在运行的设备!", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    Process p = new Process();
                    p.StartInfo.FileName = System.AppDomain.CurrentDomain.BaseDirectory + "ServoStudio.exe";
                    p.StartInfo.WindowStyle = ProcessWindowStyle.Normal;
                    p.StartInfo.UseShellExecute = false;
                    p.Start();

                    System.Environment.Exit(0);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SYSTEM_RESTART, "SystemRestart", ex);
            }
        }    
      
        //*************************************************************************
        //函数名称：SystemReset
        //函数功能：系统复位
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.25
        //*************************************************************************
        public void SystemReset()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                dicParameterInfo.Add("System Reset", "1");
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (MessageBoxService.ShowMessage("是否系统复位...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MAIN, TaskName.SystemReset, lstTransmittingDataInfo);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SYSTEM_RESET, "SystemReset", ex);
            }
        }

        //*************************************************************************
        //函数名称：ControlSourceOption
        //函数功能：伺服控制权切换
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.17
        //*************************************************************************
        public void ControlSourceOption()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                string strControlSourceOption = OthersHelper.GetCurrentValueOfIndex("0x200201");//获取控制权选择

                if (strControlSourceOption == "0")
                {
                    dicParameterInfo.Add("Control Source Option", "1");
                    iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }
                }
                else
                {
                    dicParameterInfo.Add("Control Source Option", "0");
                    iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }
                }

                //dicParameterInfo.Add("Control Source Option", "1");
                //iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                //if (iRet != RET.SUCCEEDED)
                //{
                //    ShowNotification(RET.ERROR);
                //    return;
                //}

                if (MessageBoxService.ShowMessage("是否切换伺服控制权并重启伺服...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: true);
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MAIN, TaskName.ControlSourceOption, lstTransmittingDataInfo);                    
                }               
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_CONTROL_SOURCE_OPTION, "ControlSourceOption", ex);
            }

        }

        //*************************************************************************
        //函数名称：FactoryReset
        //函数功能：恢复出厂值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.02.25&2022.04.12
        //*************************************************************************
        public void FactoryReset()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();
      
            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                dicParameterInfo.Add("System Prm Init", "1");
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (MessageBoxService.ShowMessage("是否恢复出厂值...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MAIN, TaskName.SystemParameterInitialize, lstTransmittingDataInfo);
                    //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite_For_FactoryReset(PageName.MAIN, TaskName.SystemParameterInitialize, lstTransmittingDataInfo);// 由Lilbert更改需要点击多次“恢复出厂值”按键后才能初始化成功
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_FACTORY_RESET, "FactoryReset", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadEEPROMtoRAM
        //函数功能：读EEPROM到RAM
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.27
        //*************************************************************************
        public void ReadEEPROMtoRAM()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                dicParameterInfo.Add("Load Prm From Eeprom", "1");
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                
                if (MessageBoxService.ShowMessage("是否读取EEPROM到RAM中...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MAIN, lstParameterInfo[0].Description, lstTransmittingDataInfo);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_READ_EEPROM_TO_RAM, "ReadEEPROMtoRAM", ex);
            }
        }

        //*************************************************************************
        //函数名称：SaveRAMtoEEPROM
        //函数功能：保存RAM到EEPROM
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.27
        //*************************************************************************
        public void SaveRAMtoEEPROM()
        {
            int iRet = -1;           
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();
            
            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                dicParameterInfo.Add("Save Prm To Eeprom", "1");
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                
                if (MessageBoxService.ShowMessage("是否保存RAM到EEPROM中...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MAIN, lstParameterInfo[0].Description, lstTransmittingDataInfo);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SAVE_RAM_TO_EEPROM, "SaveRAMtoEEPROM", ex);
            }
        }

        //*************************************************************************
        //函数名称：AllFaultReset
        //函数功能：全部故障清除
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.05.24
        //*************************************************************************
        public void AllFaultReset()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //更新轴地址集合
                OthersHelper.RefreshAxisSet(ARMIndex: "All");

                //判断软件位置
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //写入控制字集合
                if (MessageBoxService.ShowMessage("是否全部故障清除...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    //关闭实时刷新显示与任务
                    StopRefreshIntervalDisplayProperty();

                    //记录当前轴地址信息
                    //SoftwareStateParameterSet.LastStationID = SoftwareStateParameterSet.StationID;
                    SoftwareStateParameterSet.LastSlaveID = SoftwareStateParameterSet.SlaveID;
                    SoftwareStateParameterSet.LastAxisID = SoftwareStateParameterSet.AxisID;

                    //SoftwareStateParameterSet.StationID = SoftwareStateParameterSet.lstAxisInfo[SoftwareStateParameterSet.AxisIndex].StationID;
                    SoftwareStateParameterSet.SlaveID = SoftwareStateParameterSet.lstAxisInfo[SoftwareStateParameterSet.AxisIndex].StationID;
                    SoftwareStateParameterSet.AxisID = SoftwareStateParameterSet.lstAxisInfo[SoftwareStateParameterSet.AxisIndex].AxisID;

                    iRet = OthersHelper.WriteControlWordSet(true, 0, TaskName.AllFaultReset);
                    if (iRet == RET.NO_EFFECT)
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }

                    //开启任务管理与发送线程
                    if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                    {
                        ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_ALL_FAULT_RESET, "AllFaultReset", ex);
            }
        }

        //*************************************************************************
        //函数名称：FaultReset
        //函数功能：故障清除
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.27
        //*************************************************************************
        public void FaultReset()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //判断软件位置
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //写入控制字集合
                if (MessageBoxService.ShowMessage("是否故障清除...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    if (OthersHelper.GetCurrentValueOfIndex("0x200201") == "1")
                    {
                        ViewModelSet.Main?.ShowHintInfo("伺服控制权是Ecat控制，请切换伺服控制权...");
                        return;
                    }
                    else
                    {
                        iRet = OthersHelper.WriteControlWordSet(true, 0, TaskName.FaultReset);
                        if (iRet == RET.NO_EFFECT)
                        {
                            ShowNotification(RET.ERROR);
                            return;
                        }

                        //开启任务管理与发送线程
                        if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                        {
                            ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                        }
                    }

                    //iRet = OthersHelper.WriteControlWordSet(true, 0, TaskName.FaultReset);
                    //if (iRet == RET.NO_EFFECT)
                    //{
                    //    ShowNotification(RET.ERROR);
                    //    return;
                    //}

                    ////开启任务管理与发送线程
                    //if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                    //{
                    //    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                    //}
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_FAULT_RESET, "FaultReset", ex);
            }
        }       
        
        //*************************************************************************
        //函数名称：HistoryDataClear
        //函数功能：故障数据清除
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.18
        //*************************************************************************
        public void HistoryDataClear()
        {
            List<TransmitingDataInfoSet> lstTransmitingDataInfoSet = new List<TransmitingDataInfoSet>();

            //故障数据采集循环采样停止
            FaultAcquisitionInfoSet.AcquisitionSwitch = false;

            //关闭线程
            OthersHelper.CloseAllThread();

            //连续采集下系统界面允许切换
            ViewModelSet.Main.IsAllPageEnabled = true;

            //清除故障数据
            SoftwareStateParameterSet.IsClearAcquisitionFault = true;

            //清空最后一条历史记录
            OfflineFaultAcquisition.Last = new FaultAcquisitionParameterSet();

            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ClearAcquisitionFault(null, TaskName.ClearAcquisitionFault, lstTransmitingDataInfoSet);

            //信息提示
            ViewModelSet.Main?.ShowHintInfo("清除故障数据...");
            
        }

        //*************************************************************************
        //函数名称：AllServoDisabled
        //函数功能：伺服禁能全停
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.21
        //*************************************************************************
        public void AllServoDisabled()
        {
            int iRet = -1;
         
            //判断串口状态
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(1000);
                return;
            }

            //更新轴地址集合
            OthersHelper.RefreshAxisSet(ARMIndex: "All");

            //判断软件位置
            if (!OthersHelper.GetWindowsStartupPosition())
            {
                ShowNotification(RET.ERROR);
                return;
            }

            //下达任务
            if (MessageBoxService.ShowMessage("是否伺服全部禁能...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
            {
                //关闭实时刷新显示与任务
                StopRefreshIntervalDisplayProperty();

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_AllAxis(TaskName.AllAxisDisabled, SoftwareStateParameterSet.AxisIndex);
            }

            //开启任务管理与发送线程
            if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
            {
                ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
            }
        }

        //*************************************************************************
        //函数名称：AllFactoryReset
        //函数功能：伺服全部出厂设置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.03.04
        //*************************************************************************
        public void AllFactoryReset()
        {
            int iRet = -1;

            //判断串口状态
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(1000);
                return;
            }

            //更新轴地址集合
            OthersHelper.RefreshAxisSet(ARMIndex: "All");

            //判断软件位置
            if (!OthersHelper.GetWindowsStartupPosition())
            {
                ShowNotification(RET.ERROR);
                return;
            }

            //下达任务
            if (MessageBoxService.ShowMessage("是否伺服全部恢复出厂设置...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
            {
                //关闭实时刷新显示与任务
                StopRefreshIntervalDisplayProperty();

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_AllAxis(TaskName.AllAxisFactoryReset, SoftwareStateParameterSet.AxisIndex);
            }

            //开启任务管理与发送线程
            if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
            {
                ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
            }
        }

        //*************************************************************************
        //函数名称：AllConfigStop
        //函数功能：伺服全部配置急停
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.03.17
        //*************************************************************************
        public void AllConfigStop()
        {
            int iRet = -1;

            //判断串口状态
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(1000);
                return;
            }

            //更新轴地址集合
            OthersHelper.RefreshAxisSet(ARMIndex: "All");

            //判断软件位置
            if (!OthersHelper.GetWindowsStartupPosition())
            {
                ShowNotification(RET.ERROR);
                return;
            }

            //下达任务
            if (MessageBoxService.ShowMessage("是否伺服全部配置急停...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
            {
                //关闭实时刷新显示与任务
                StopRefreshIntervalDisplayProperty();

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_AllAxis(TaskName.AllAxisConfigStop, SoftwareStateParameterSet.AxisIndex);
            }

            //开启任务管理与发送线程
            if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
            {
                ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
            }
        }

        //*************************************************************************
        //函数名称：AllSystemReset
        //函数功能：伺服全部重启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.03.08
        //*************************************************************************
        public void AllSystemReset()
        {
            int iRet = -1;

            //判断串口状态
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(1000);
                return;
            }

            //更新轴地址集合
            OthersHelper.RefreshAxisSet(ARMIndex: "SystemReset");

            //判断软件位置
            if (!OthersHelper.GetWindowsStartupPosition())
            {
                ShowNotification(RET.ERROR);
                return;
            }

            //下达任务
            if (MessageBoxService.ShowMessage("是否伺服全部重启...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
            {
                //关闭实时刷新显示与任务
                StopRefreshIntervalDisplayProperty();

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_AllAxis(TaskName.AllAxisSystemReset, SoftwareStateParameterSet.AxisIndex);

                Thread.Sleep(1000);
                SoftwareStateParameterSet.AxisIndex++;
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_AllAxis(TaskName.AllAxisSystemReset, SoftwareStateParameterSet.AxisIndex);

                Thread.Sleep(1000);
                SoftwareStateParameterSet.AxisIndex++;
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_AllAxis(TaskName.AllAxisSystemReset, SoftwareStateParameterSet.AxisIndex);
            }

            //开启任务管理与发送线程
            if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
            {
                ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
            }
        }

        //*************************************************************************
        //函数名称：AllServoEnabled
        //函数功能：一键伺服全部使能
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.03.08
        //*************************************************************************
        public void AllServoEnabled()
        {
            int iRet = -1;

            //判断串口状态
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(1000);
                return;
            }

            //更新轴地址集合
            OthersHelper.RefreshAxisSet(ARMIndex: "All");

            //判断软件位置
            if (!OthersHelper.GetWindowsStartupPosition())
            {
                ShowNotification(RET.ERROR);
                return;
            }

            //写入控制字集合
            if (MessageBoxService.ShowMessage("是否伺服全部使能", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
            {
                //关闭实时刷新显示与任务
                StopRefreshIntervalDisplayProperty();

                //记录当前轴地址信息
                //SoftwareStateParameterSet.LastStationID = SoftwareStateParameterSet.StationID;
                SoftwareStateParameterSet.LastSlaveID = SoftwareStateParameterSet.SlaveID;
                SoftwareStateParameterSet.LastAxisID = SoftwareStateParameterSet.AxisID;

                //SoftwareStateParameterSet.StationID = SoftwareStateParameterSet.lstAxisInfo[SoftwareStateParameterSet.AxisIndex].StationID;
                SoftwareStateParameterSet.SlaveID = SoftwareStateParameterSet.lstAxisInfo[SoftwareStateParameterSet.AxisIndex].StationID;
                SoftwareStateParameterSet.AxisID = SoftwareStateParameterSet.lstAxisInfo[SoftwareStateParameterSet.AxisIndex].AxisID;

                //写入控制字
                OthersHelper.WriteControlWordSet(true, 0, TaskName.AllAxisEnabled);                                    
            }

            //开启任务管理与发送线程
            if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
            {
                ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
            }
        }

        //*************************************************************************
        //函数名称：AllAxisAction
        //函数功能：获取单圈编码器位数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.05.09
        //*************************************************************************
        public void AllAxisAction(string strFunction)
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            ObservableCollection<ParameterReadWriteSet> obsParameterInfo = new ObservableCollection<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //判断串口状态
            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(1000);
                return;
            }

            //更新轴地址集合
            OthersHelper.RefreshAxisSet(ARMIndex: "All");

            //判断软件位置
            if (!OthersHelper.GetWindowsStartupPosition())
            {
                ShowNotification(RET.ERROR);
                return;
            }

            //下达任务
            if (strFunction == "AllAxisRunning")
            {
                AllAxisFunction = TaskName.AllAxisRunning;
                if (MessageBoxService.ShowMessage("是否全部开启运动调试...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    //关闭实时刷新显示与任务
                    StopRefreshIntervalDisplayProperty();

                    //下达任务
                    dicParameterInfo.Add("Abs Encoder Single-Turn Bit", null);
                    OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo);

                    ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: false);
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.ABSEncoderSingleTurnBit, lstTransmittingDataInfo);
                }
            }
            else if (strFunction =="AllAxisStop")
            {
                AllAxisFunction = TaskName.AllAxisStop;
                if (MessageBoxService.ShowMessage("是否全部停止运动调试...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    //关闭实时刷新显示与任务
                    StopRefreshIntervalDisplayProperty();

                    //下达任务
                    dicParameterInfo.Add("Abs Encoder Single-Turn Bit", null);
                    OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo);

                    ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: false);
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.ABSEncoderSingleTurnBit, lstTransmittingDataInfo);
                }
            }

            //开启任务管理与发送线程
            if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
            {
                ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
            }
        }

        //*************************************************************************
        //函数名称：AssignAxisAction
        //函数功能：下达运动指令
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void AssignAxisAction()
        {
            SoftwareStateParameterSet.ABSEncoderSingleTurnBit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Abs Encoder Single-Turn Bit", "Index"));//编码器单圈分辨率           

            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_AllAxis(AllAxisFunction, SoftwareStateParameterSet.AxisIndex);
        }

        //*************************************************************************
        //函数名称：ServoDisabled
        //函数功能：伺服禁能
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.05.26
        //*************************************************************************
        public void ServoDisabled()
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //判断软件位置
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (OthersHelper.GetCurrentValueOfIndex("0x200201") == "1")
                {
                    ViewModelSet.Main?.ShowHintInfo("伺服控制权是Ecat控制，请切换伺服控制权...");
                    return;
                }
                else
                {
                    //写入控制字集合                
                    OthersHelper.WriteControlWordSet(true, 0, TaskName.ServoDisabled);

                    if (SoftwareStateParameterSet.CurrentPageName == PageName.MOTORLINEUVWSEQUENCEABSENCODEROFFSET ||
                        SoftwareStateParameterSet.CurrentPageName == PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING ||
                        SoftwareStateParameterSet.CurrentPageName == PageName.MOTORDRIECTIONJOGPAGE ||
                        SoftwareStateParameterSet.CurrentPageName == PageName.PARAMETERTUNNING ||
                        SoftwareStateParameterSet.CurrentPageName == PageName.MOTORFEEDBACKAUTOLEARN)   //由Lilbert于2022.11.22添加Fn禁能，Pn709写1
                    {
                        OthersHelper.OperatingControl("Fn Servo Off", "1", "伺服禁能", "");

                        if (SoftwareStateParameterSet.CurrentPageName == PageName.MOTORDRIECTIONJOGPAGE)
                        {
                            ViewModelSet.JogDriection?.RefreshJogDriectionFlag("JogStopForFN");
                        }
                    }
                    else
                    {
                        OthersHelper.OperatingControl("Fn Servo Off", "1", "", "");
                    }

                    if (SoftwareStateParameterSet.CurrentPageName == PageName.FIRMWAREUPDATE)
                    {
                        ViewModelSet.FirmwareUpdate?.FirmwareUpdateLoaded();
                    }

                    //开启任务管理与发送线程
                    if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                    {
                        ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                    }

                    //return;     
                }

            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SERVO_ENABLED, "ServoDisabled", ex);
            }
        }

        //*************************************************************************
        //函数名称：ServoEnabled
        //函数功能：伺服使能
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.20
        //*************************************************************************
        public void ServoEnabled()
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //判断软件位置
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //写入控制字集合

                if (ServoStatusSwitch == "伺服禁能")        // 由Lilbert添加伺服禁能不显示对话框
                {
                    OthersHelper.WriteControlWordSet(true, 0, TaskName.ServoDisabled);

                    if (SoftwareStateParameterSet.CurrentPageName == PageName.FIRMWAREUPDATE)
                    {
                        ViewModelSet.FirmwareUpdate?.FirmwareUpdateLoaded();
                    }

                    //开启任务管理与发送线程
                    if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                    {
                        ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                    }
                    return;
                }

                if (MessageBoxService.ShowMessage("是否" + ServoStatusSwitch + "...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    if (ServoStatusSwitch == "伺服使能")
                    {
                        OthersHelper.WriteControlWordSet(true, 0, TaskName.ServoEnabled);                       
                    }
                    else
                    {
                        OthersHelper.WriteControlWordSet(true, 0, TaskName.ServoDisabled);               
                    }

                    if (SoftwareStateParameterSet.CurrentPageName == PageName.FIRMWAREUPDATE)
                    {
                        ViewModelSet.FirmwareUpdate?.FirmwareUpdateLoaded();
                    }

                    //开启任务管理与发送线程
                    if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                    {
                        ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                    }
                }              
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SERVO_ENABLED, "ServoEnabled", ex);
            }
        }

        //*************************************************************************
        //函数名称：EmergencyStop
        //函数功能：系统急停
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.27
        //*************************************************************************
        public void EmergencyStop()
        {
            int iRet = -1;

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //写入控制字集合
                iRet = OthersHelper.WriteControlWordSet(true, 0, TaskName.Emergency);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //开启任务管理与发送线程
                if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                {
                    ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_EMERGENCY_STOP, "EmergencyStop", ex);
            }
        }

        //*************************************************************************
        //函数名称：OnlineInertiaIdentification
        //函数功能：在线惯量识别
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.25
        //*************************************************************************
        public void OnlineInertiaIdentification()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                MessageResult mRet = MessageBoxService.ShowMessage("是否在线惯量识别..." + " \r\n"+ "若确定，请在状态栏查看该字段参数值...", "请确定", MessageButton.YesNo, MessageIcon.Information);
                if (mRet == MessageResult.Yes)
                {
                    dicParameterInfo.Add("Motor Inertia Identification Online Start", "1");
                }
                else
                {
                    dicParameterInfo.Add("Motor Inertia Identification Online Start", "0");
                }

                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MAIN, TaskName.OnlineInertiaIdentification, lstTransmittingDataInfo);             
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_INERTIA_IDENTIFICATION, "InertiaIdentification", ex);
            }
        }

        ////*************************************************************************
        ////函数名称：MotorParameterIdentification_AddInterface
        ////函数功能：电机参数辨识界面导航
        ////
        ////输入参数：None
        ////         
        ////输出参数：None
        ////        
        ////编码作者：Lilbert
        ////更新时间：2021.11.02
        ////*************************************************************************
        //public void MotorParameterIdentification_AddInterface()
        //{
        //    SoftwareStateParameterSet.CurrentPageName = PageName.MOTORPARAMETERIDENTIFICATIONPAGE;
        //    NavigationService.Navigate("MotorParameterIdentificationView", null, this);
        //}

        //*************************************************************************
        //函数名称：MotorParameterIdentification_AddInterface
        //函数功能：电机参数辨识界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2021.11.17
        //*************************************************************************
        public void MotorParameterIdentification_AddInterface()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.MOTORLINEUVWSEQUENCEABSENCODEROFFSET;
            NavigationService.Navigate("MotorLineUVWSequenceAbsEncoderOffsetView", null, this);
        }

        //*************************************************************************
        //函数名称：MotorParameterIdentification
        //函数功能：更改电机参数识别
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.27
        //*************************************************************************
        public void MotorParameterIdentification()
        {
            int iRet = -1;

            try
            {
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return;
                }

                if (ViewModelSet.MotorParameterIdentification == null)
                {
                    ViewModelSet.MotorParameterIdentification = new MotorParameterIdentificationViewModel();
                }

                UICommand cancelCommand = new UICommand()
                {
                    Caption = "返回",
                    IsCancel = true,
                };

                UICommand result = DialogService_MotorParameterIdentification.ShowDialog(new List<UICommand>() { cancelCommand }, "电机参数辨识", ViewModelSet.MotorParameterIdentification);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_MOTOR_PARAMETER_IDENTIFICATION, "MotorParameterIdentification", ex);
            }
        }

        //*************************************************************************
        //函数名称：SwitchAxis
        //函数功能：轴地址转换
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.18&2021.06.25&2021.11.17&2022.05.26
        //*************************************************************************
        public void SwitchAxis()
        {
            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (ViewModelSet.SwitchAxis == null)
                {
                    ViewModelSet.SwitchAxis = new SwitchAxisViewModel();
                }

                UICommand registerCommand = new UICommand()
                {
                    Caption = "确认",
                    IsCancel = false,
                    IsDefault = true,
                };

                UICommand cancelCommand = new UICommand()
                {
                    Caption = "取消",
                    IsCancel = true,
                    IsDefault = false,
                };

                UICommand result = DialogService_SwitchAxis.ShowDialog(
                dialogCommands: new List<UICommand>() { registerCommand, cancelCommand },
                title: "轴地址切换",
                viewModel: ViewModelSet.SwitchAxis);

                if (result == registerCommand)
                {                                        
                     if (CommunicationSet.TaskName != TaskName.JogSwitch && ViewModelSet.Jog != null)//Jog使能/禁能    //由Lilbert添加在JOG页面切换轴时，把当前轴禁能
                    {
                        if (ViewModelSet.Jog.IsClosed)
                        {
                            OthersHelper.GetActualEnableStatus(TaskName.JogActualEnable);
                        }
                        else
                        {
                            OthersHelper.OperatingControl("Operating End", "1", TaskName.JogOperatingEnd, PageName.JOG);
                        }
                    }

                    #region 轴地址变更
                    //OthersHelper.GetSelectedAxisID(ViewModelSet.SwitchAxis.SelectedAxisID);
                    //OthersHelper.GetSelectedSlaveAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, ViewModelSet.SwitchAxis.SelectedSlaveID, ViewModelSet.SwitchAxis.SelectedAxisID);
                    OthersHelper.GetSelectedSlaveAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, ViewModelSet.SwitchAxis.SelectedAxisID);

                    //RefreshAxisAddress(ViewModelSet.SwitchAxis.SelectedAxisID); //轴地址状态栏更新                    
                    RefreshAxisAddress(OthersHelper.GetSelectedStationAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID));//轴地址状态栏更新

                    GlobalCurrentInput.SelectedSlaveID = ViewModelSet.SwitchAxis.SelectedSlaveID;     //由Lilbert添加显示当前从站   
                    GlobalCurrentInput.SelectedAxisID = ViewModelSet.SwitchAxis.SelectedAxisID;     //由Lilbert添加显示当前轴                                     
                    #endregion

                    #region 当前页面的数据更新
                    switch (SoftwareStateParameterSet.CurrentPageName)
                    {
                        case PageName.MOTORFEEDBACK:
                            MotorFeedbackNavigation();
                            break;
                        case PageName.NORMALSET:
                            NormalSettingNavigation();
                            break;
                        case PageName.JOGPAGE:
                            //if (CommunicationSet.TaskName == TaskName.JogSwitch && ViewModelSet.Jog != null)//Jog使能/禁能
                            //{
                            //    if (!ViewModelSet.Jog.IsClosed)
                            //    {
                            //        OthersHelper.GetActualEnableStatus(TaskName.JogActualEnable);
                            //    }
                            //    else
                            //    {
                            //        OthersHelper.OperatingControl("Operating End", "1", TaskName.JogOperatingEnd, PageName.JOG);
                            //    }
                            //}
                                //RefreshStatusWord(SoftwareStateParameterSet.ServoStatus);
                                break;
                        case PageName.DIGITALIO:
                            DigitalIONavigation();
                            break;
                        case PageName.SPEEDLOOP:
                            SpeedLoopNavigation();
                            break;
                        case PageName.CURRENTLOOP:
                            CurrentLoopNavigation();
                            break;
                        //case PageName.ADVANCED_ADVANCED:
                        //    AdvancedNavigation();
                        //    break;
                        case PageName.POSITIONLOOP:
                            PositionLoopNavigation();
                            break;
                        case PageName.SEEKZERO:
                            SeekZeroNavigation();
                            break;                        
                        case PageName.Advanced:
                        case PageName.Auxiliary:
                        case PageName.Basic:
                        case PageName.CIA402:
                        case PageName.Common:
                        case PageName.Control:
                        case PageName.DI:
                        case PageName.DO:
                        case PageName.FaultAndProtection:
                        case PageName.Motor:
                            ViewModelSet.ParameterReadWrite?.ParameterRead(SoftwareStateParameterSet.CurrentPageName);
                            break;
                        default:
                            ViewModelSet.Oscilloscope.ReadAdjustmentParameter("All");        //由Lilbert添加三环调试界面刷新参数
                            break;
                    }
                    #endregion

                    #region 重新计算单位换算
                    CurrentUnit.bInitialized = true;
                    OthersHelper.AssignUnitExchangeTask();
                    #endregion

                    #region 更新报警信息 
                    if (!IsAlarmInfoPageHidden)//报警信息侧边栏状态为展开
                    {
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_HardwareAlarm();
                    }
                    #endregion
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SWITCH_AXIS, "SwitchAxis", ex);
            }
        }

        //*************************************************************************
        //函数名称：ModifyPassword
        //函数功能：修改密码
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.23
        //*************************************************************************
        public void ModifyPassword()
        {
            string strOldPassword = null;
            string strOldPasswordFromFile = null;
            string strNewPassword = null;

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (ViewModelSet.ModifyPassword == null)
                {
                    ViewModelSet.ModifyPassword = new ModifyPasswordViewModel();
                }

                UICommand registerCommand = new UICommand()
                {
                    Caption = "确认",
                    IsCancel = false,
                    IsDefault = true,
                };

                UICommand cancelCommand = new UICommand()
                {
                    Caption = "取消",
                    IsCancel = true,
                    IsDefault = false,
                };

                UICommand result = DialogService_ModifyPassword.ShowDialog(
                dialogCommands: new List<UICommand>() { registerCommand, cancelCommand },
                title: "管理员密码修改",
                viewModel: ViewModelSet.ModifyPassword);

                if (result == registerCommand)
                {
                    strOldPassword = ViewModelSet.ModifyPassword?.OldPassword;
                    strNewPassword = ViewModelSet.ModifyPassword?.NewPassword;

                    strOldPasswordFromFile = IniHelper.IniReadValue("ServoStudio", "Password", FilePath.Ini);

                    if (strOldPassword != strOldPasswordFromFile)
                    {
                        ShowNotification(2021);
                    }
                    else
                    {
                        IniHelper.IniWriteValue("ServoStudio", "Password", strNewPassword, FilePath.Ini);
                        ShowNotification(2022);
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_MODIFY_PASSWORD, "ModifyPassword", ex);
            }
        }

        //*************************************************************************
        //函数名称：LanguageSwitch
        //函数功能：切换语言
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.12.28
        //*************************************************************************
        public void LanguageSwitch()
        {
            try
            {
                Common.CurrentLanguageFile = Common.CurrentLanguageFile == @"Language\zh-CN.xaml"
                   ? @"Language\en-US.xaml"
                   : @"Language\zh-CN.xaml";

                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                {
                    //串口状态
                    ViewModelSet.CommunicationSet.ConnectionSerialPort();

                    //伺服状态
                    ControlServoStatusSwitch();

                    //状态字
                    RefreshStatusWord(HexHelper.GetStatusWord(0, Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x604100"))));

                    //轴地址状态栏更新
                    //RefreshAxisAddress(GlobalCurrentInput.SelectedAxisID);
                    RefreshAxisAddress(OthersHelper.GetSelectedStationAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID));

                    //任务
                    RefreshSerialPortWorkState(TaskName.Empty);

                    //驱动器名称
                    //ViewModelSet.CommunicationSet.InterfaceEvaluationInitialized_For_CNUS();

                    //数字IO
                    ViewModelSet.Digital.ComboBoxInitialize();

                    //陷波滤波器
                    ViewModelSet.CurrentLoop.RefreshCheckBox_For_CNUS();
                   
                    
                    //侧边栏报警
                    if (IsAlarmInfoPageHidden)
                    {
                        SlideAlarm = "报警展开";
                        SlideAlarmIcon = IconPath.AlarmOpen;
                    }
                    else
                    {
                        SlideAlarm = "报警收起";
                        SlideAlarmIcon = IconPath.AlarmClose;
                    }

                    //侧边栏监控
                    if (IsMonitoringPageHidden)
                    {
                        SlideMonitor = "监控展开";
                        SlideMonitorIcon = IconPath.MonitorOpen;
                    }
                    else
                    {
                        SlideMonitor = "监控收起";
                        SlideMonitorIcon = IconPath.MonitorClose;
                    }

                }
                else
                {
                    //串口状态
                    ViewModelSet.CommunicationSet.ConnectionSerialPort();

                    //伺服状态
                    ControlServoStatusSwitch();

                    //状态字
                    RefreshStatusWord(HexHelper.GetStatusWord(0, Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x604100"))));

                    //轴地址状态栏更新
                    //RefreshAxisAddress(GlobalCurrentInput.SelectedAxisID);
                    RefreshAxisAddress(OthersHelper.GetSelectedStationAxisID(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID));

                    //任务
                    RefreshSerialPortWorkState(TaskName.Empty1);

                    //驱动器名称
                    //ViewModelSet.CommunicationSet.InterfaceEvaluationInitialized_For_CNUS();

                    //数字IO
                    ViewModelSet.Digital.ComboBoxInitialize();

                    //陷波滤波器
                    ViewModelSet.CurrentLoop.RefreshCheckBox_For_CNUS();
                    
                   
                    //侧边栏报警
                    if (IsAlarmInfoPageHidden)
                    {
                        SlideAlarm = "AlarmOpen";
                        SlideAlarmIcon = IconPath.AlarmOpen;
                    }
                    else
                    {
                        SlideAlarm = "AlarmClose";
                        SlideAlarmIcon = IconPath.AlarmClose;
                    }

                    //侧边栏监控
                    if (IsMonitoringPageHidden)
                    {
                        SlideMonitor = "MonitorOpen";
                        SlideMonitorIcon = IconPath.MonitorOpen;
                    }
                    else
                    {
                        SlideMonitor = "MonitorClose";
                        SlideMonitorIcon = IconPath.MonitorClose;
                    }
                }


            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SWITCH_LANGUAHE, "LanguageSwitch", ex);
            }
        }

        //*************************************************************************
        //函数名称：InertiaIdentificationParameterSelf_Tunning_For_AddInterface
        //函数功能：负载惯量辨识和参数自整定
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void InertiaIdentificationParameterSelf_Tunning_For_AddInterface()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING;
            NavigationService.Navigate("InertiaIdentificationParameterSelfTunningView", null, this);
        }

        //*************************************************************************
        //函数名称：JogDriectionDebug_For_AddInterface
        //函数功能：Jog和电机旋转方向调试界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.02
        //*************************************************************************
        public void JogDriectionDebug_For_AddInterface()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.MOTORDRIECTIONJOGPAGE;
            NavigationService.Navigate("MotorDriectionJogView", null, this);
        }

        //*************************************************************************
        //函数名称：JogDebug_For_AddInterface
        //函数功能：Jog调试界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2021.11.02
        //*************************************************************************
        public void JogDebug_For_AddInterface()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.JOGPAGE;
            NavigationService.Navigate("JogView", null, this);
        }

        //*************************************************************************
        //函数名称：JogDebug
        //函数功能：JOG调试
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.28
        //*************************************************************************
        public void JogDebug()
        {
            int iRet = -1;

            try
            {
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (ViewModelSet.Jog == null)
                {
                    ViewModelSet.Jog = new JogViewModel();
                }

                UICommand cancelCommand = new UICommand()
                {
                    Caption = "返回",
                    IsCancel = true,
                    IsDefault = false,
                };

                UICommand result = DialogService_Jog.ShowDialog(
                dialogCommands: new List<UICommand>() { cancelCommand },
                title: "JOG调试",
                viewModel: ViewModelSet.Jog);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_JOG_DEBUG, "JogDebug", ex);
            }
        }

        //*************************************************************************
        //函数名称：ProgramJogDebug_For_AddInterface
        //函数功能：程序Jog调试界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2021.11.02
        //*************************************************************************
        public void ProgramJogDebug_For_AddInterface()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.PROGRAMJOGPAGE;
            NavigationService.Navigate("ProgramJogView", null, this);
        }

        //*************************************************************************
        //函数名称：ProgramJogDebug
        //函数功能：程序JOG调试
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.10.20
        //*************************************************************************
        public void ProgramJogDebug()
        {
            int iRet = -1;

            try
            {
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (ViewModelSet.ProgramJog == null)
                {
                    ViewModelSet.ProgramJog = new ProgramJogViewModel();
                }

                UICommand cancelCommand = new UICommand()
                {
                    Caption = "返回",
                    IsCancel = true,
                    IsDefault = false,
                };

                UICommand result = DialogService_ProgramJog.ShowDialog(
                dialogCommands: new List<UICommand>() { cancelCommand },
                title: "程序JOG调试",
                viewModel: ViewModelSet.ProgramJog);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_PROGRAM_JOG_DEBUG, "ProgramJogDebug", ex);
            }
        }

        //*************************************************************************
        //函数名称：OfflineInertiaIdentification
        //函数功能：离线惯量识别界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2021.11.02
        //*************************************************************************
        public void OfflineInertiaIdentification_For_AddInterface()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.OFFLINEINENTIFICATION;
            NavigationService.Navigate("OfflineInertiaIdentificationView", null, this);
        }

        //*************************************************************************
        //函数名称：OfflineInertiaIdentification
        //函数功能：离线惯量识别
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.17
        //*************************************************************************
        public void OfflineInertiaIdentification()
        {
            int iRet = -1;

            try
            {
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (ViewModelSet.OfflineInertiaIdentification == null)
                {
                    ViewModelSet.OfflineInertiaIdentification = new OfflineInertiaIdentificationViewModel();
                }

                UICommand cancelCommand = new UICommand()
                {
                    Caption = "返回",
                    IsCancel = true,
                    IsDefault = false,
                };

                UICommand result = DialogService_OfflineInertiaIdentification.ShowDialog(
                dialogCommands: new List<UICommand>() { cancelCommand },
                title: "离线惯量识别",
                viewModel: ViewModelSet.OfflineInertiaIdentification);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_OFFLINE_INERTIA_IDENTIFICATION, "OfflineInertiaIdentification", ex);
            }
        }
        #endregion

        #region 页面导航
        //*************************************************************************
        //函数名称：CommunicationSetNavigation
        //函数功能：通信配置界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.15
        //*************************************************************************
        public void CommunicationSetNavigation()
        {
            try
            {
                if (IsInitialized || !SoftwareStateParameterSet.IsConnected)
                {
                    SoftwareStateParameterSet.CurrentPageName = PageName.COMMUNICATIONSET;
                    NavigationService.Navigate("CommunicationSetView", null, this);

                    IsInitialized = false;
                }
                else
                {
                    if (SoftwareStateParameterSet.IsConnected)
                    {
                        if (!OthersHelper.GetWindowsStartupPosition())
                        {
                            return;
                        }

                        if (MessageBoxService.ShowMessage("重置通信将导致通信断开\r\n可能严重影响正在执行中的任务\r\n\r\n是否需要重新配置通信...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                        {
                            //更新状态栏
                            //RefreshSerialPortWorkState(TaskName.Empty);
                            //RefreshSerialPortConnectState(ConnectState.Explaination_NotConnect);
                            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                            {
                                RefreshSerialPortWorkState(TaskName.Empty);

                                RefreshSerialPortConnectState(ConnectState.Explaination_NotConnect);
                            }
                            else
                            {
                                RefreshSerialPortWorkState(TaskName.Empty1);

                                RefreshSerialPortConnectState(ConnectState.Explaination_NotConnect1);
                            }
                                

                            //关闭所有线程
                            OthersHelper.CloseAllThread();

                            //任务栈更新清零
                            SerialPortTask.TaskManagement = new List<TaskManagementSet>();

                            //接收的信息清空
                            SoftwareStateParameterSet.lstMessage = new List<byte>();

                            //通信连接状态
                            SoftwareStateParameterSet.IsConnected = false;

                            //导航
                            SoftwareStateParameterSet.CurrentPageName = PageName.COMMUNICATIONSET;
                            NavigationService.Navigate("CommunicationSetView", null, this);

                            //串口关闭
                            if (CommunicationSet.SerialPortInfo != null)
                            {
                                if (CommunicationSet.SerialPortInfo.IsOpen)
                                {
                                    CommunicationSet.SerialPortInfo.Close();
                                }
                            }
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_COMMUNICATION_NAVIGATION, "CommunicationSetNavigation", ex);
                CommunicationSet.SerialPortInfo = new SerialPort();
            }           
        }

        //*************************************************************************
        //函数名称：MotorFeedbackAutoLearnNavigation
        //函数功能：电机参数自学习界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.15
        //*************************************************************************
        public void MotorFeedbackAutoLearnNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.MOTORFEEDBACKAUTOLEARN;
            NavigationService.Navigate("MotorFeedbackAutoLearnView", null, this);
        }

        //*************************************************************************
        //函数名称：MotorFeedbackNavigation
        //函数功能：电机反馈界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.15
        //*************************************************************************
        public void MotorFeedbackNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.MOTORFEEDBACK;
            NavigationService.Navigate("MotorFeedbackView", null, this);     
        }

        //*************************************************************************
        //函数名称：LimitAmplitudeNavigation
        //函数功能：限幅保护界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.16
        //*************************************************************************
        public void LimitAmplitudeNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.LIMITAMPLITUDE;
            NavigationService.Navigate("LimitAmplitudeView", null, this);
        }

        //*************************************************************************
        //函数名称：NormalSettingNavigation
        //函数功能：一般设定导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.16
        //*************************************************************************
        public void NormalSettingNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.NORMALSET;
            NavigationService.Navigate("NormalSettingView", null, this);
        }

        //*************************************************************************
        //函数名称：DigitalIONavigation
        //函数功能：数字IO界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.16
        //*************************************************************************
        public void DigitalIONavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.DIGITALIO;
            NavigationService.Navigate("DigitalIOView", null, this);
        }

        //*************************************************************************
        //函数名称：SpeedLoopNavigation
        //函数功能：速度环
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.20
        //*************************************************************************
        public void SpeedLoopNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.SPEEDLOOP;
            NavigationService.Navigate("SpeedLoopView", null, this);
        }

        //*************************************************************************
        //函数名称：CurrentLoopNavigation
        //函数功能：电流环
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.20
        //*************************************************************************
        public void CurrentLoopNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.CURRENTLOOP;
            NavigationService.Navigate("CurrentLoopView", null, this);
        }

        //*************************************************************************
        //函数名称：AdvancedNavigation
        //函数功能：高级功能
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.14
        //*************************************************************************
        public void AdvancedNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.ADVANCEDFEEDBACK;
            NavigationService.Navigate("AdvancedFeedbackView", null, this);
        }

        //*************************************************************************
        //函数名称：PositionLoopNavigation
        //函数功能：位置环
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.16
        //*************************************************************************
        public void PositionLoopNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.POSITIONLOOP;
            NavigationService.Navigate("PositionLoopView", null, this);
        }

        //*************************************************************************
        //函数名称：SpeedNavigation
        //函数功能：速度模式导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.20
        //*************************************************************************
        public void SpeedNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.SPEEDACTION;
            NavigationService.Navigate("SpeedView", null, this);
        }

        //*************************************************************************
        //函数名称：PositionNavigation
        //函数功能：位置模式导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.20
        //*************************************************************************
        public void PositionNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.POSITIONACTION;
            NavigationService.Navigate("PositionView", null, this);
        }

        //*************************************************************************
        //函数名称：TorqueNavigation
        //函数功能：转矩模式导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.20
        //*************************************************************************
        public void TorqueNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.TORQUEACTION;
            NavigationService.Navigate("TorqueView", null, this);
        }

        //*************************************************************************
        //函数名称：SeekZeroNavigation
        //函数功能：寻零模式导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.20
        //*************************************************************************
        public void SeekZeroNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.SEEKZERO;
            NavigationService.Navigate("SeekZeroView", null, this);
        }

        //*************************************************************************
        //函数名称：ParameterReadWriteNavigation
        //函数功能：参数读写界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.05
        //*************************************************************************
        public void ParameterReadWriteNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.Motor;
            NavigationService.Navigate("ParameterReadWriteView", null, this);
        }

        //*************************************************************************
        //函数名称：OscilloscopeNavigation
        //函数功能：示波器显示导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.02
        //*************************************************************************
        public void OscilloscopeNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.OSCILLOSCOPE;
            SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.Calculate;
            NavigationService.Navigate("OscilloscopeView", null, this);
        }

        //*************************************************************************
        //函数名称：FunctionGeneratorNavigation
        //函数功能：函数发生器导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.19
        //*************************************************************************
        public void FunctionGeneratorNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.FUNCTIONGENERATOR;
            SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.FunctionGenerator;
            NavigationService.Navigate("OscilloscopeView", null, this);
        }

        //*************************************************************************
        //函数名称：ThreeLoopNavigation
        //函数功能：三环导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.20
        //*************************************************************************
        public void ThreeLoopNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.OSCILLOSCOPE;
            SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.ThreeLoop;
            NavigationService.Navigate("OscilloscopeView", null, this);
        }

        //*************************************************************************
        //函数名称：ActionNavigation
        //函数功能：运动导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.20
        //*************************************************************************
        public void ActionNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.ACTIONDEBUG;
            SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.Action;
            NavigationService.Navigate("OscilloscopeView", null, this);
        }

        //*************************************************************************
        //函数名称：ParameterTunningNavigation
        //函数功能：手动参数调优导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.04
        //*************************************************************************
        public void ParameterTunningNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.PARAMETERTUNNING;
            SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.ParameterTunning;
            NavigationService.Navigate("OscilloscopeView", null, this);
        }

        //*************************************************************************
        //函数名称：SoftwareErrorLogNavigation
        //函数功能：软件错误日志界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.17
        //*************************************************************************
        public void SoftwareErrorLogNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.SOFTWAREERROR;
            NavigationService.Navigate("SoftwareErrorLogView", null, this);
        }

        //*************************************************************************
        //函数名称：ParameterMonitorNavigation
        //函数功能：参数监控界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.04
        //*************************************************************************
        public void ParameterMonitorNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.PARAMETERMONITOR;
            NavigationService.Navigate("ParameterMonitorView", null, this);
        }

        //*************************************************************************
        //函数名称：FirmwareUpdateNavigation
        //函数功能：固件升级导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.23
        //*************************************************************************
        public void FirmwareUpdateNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.FIRMWAREUPDATE;
            NavigationService.Navigate("FirmwareUpdateView", null, this);
        }

        //*************************************************************************
        //函数名称：UnitNavigation
        //函数功能：单位设置导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020..07.23
        //*************************************************************************
        public void UnitNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.UNIT;
            NavigationService.Navigate("UnitView", null, this);
        }

        //*************************************************************************
        //函数名称：HardwareAlarmHistoryNavigation
        //函数功能：硬件报警历史导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.11
        //*************************************************************************
        public void HardwareAlarmHistoryNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.HARDWAREALARMHISTORY;
            NavigationService.Navigate("HardwareAlarmHistoryView", null, this);
        }

        //*************************************************************************
        //函数名称：HardwareAlarmMeasureNavigation
        //函数功能：硬件报警原因与措施导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.11
        //*************************************************************************
        public void HardwareAlarmMeasureNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.HARDWAREALARMMEASURE;
            NavigationService.Navigate("HardwareAlarmMeasureView", null, this);
        }

        //*************************************************************************
        //函数名称：HardwareAlarmMeasureNavigation
        //函数功能：故障数据查询导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public void HistoryDataQueryNavigation()
        {
            //SoftwareStateParameterSet.CurrentPageName = PageName.HISTORYDATAQUERY;
            SoftwareStateParameterSet.CurrentPageName = PageName.FAULTDATAOSCILLOSCOPE;
            NavigationService.Navigate("FaultDataOscilloscopeView", null, this);
        }

        //*************************************************************************
        //函数名称：HistoryDataConfigNavigation
        //函数功能：故障数据配置导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.09
        //*************************************************************************
        public void HistoryDataConfigNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.FAULTDATACONFIG;
            NavigationService.Navigate("FaultDataConfigView", null, this);
        }

        //*************************************************************************
        //函数名称：MotorLibraryNavigation
        //函数功能：参数库导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.26
        //*************************************************************************
        public void MotorLibraryNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.MOTORLIBRARY;
            NavigationService.Navigate("MotorLibraryView", null, this);
        }
        #endregion

        #region 状态栏与侧边栏
        //*************************************************************************
        //函数名称：RefreshSerialPortWorkState
        //函数功能：更新状态栏状态
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.23
        //*************************************************************************
        public void RefreshSerialPortWorkState(string strProcess)
        {
            //SerialPortWorkState = "任务：" + strProcess;
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                SerialPortWorkState = "任务：" + strProcess;
            }
            else
            {
                SerialPortWorkState = "Task：" + strProcess;
            }
        }

        //*************************************************************************
        //函数名称：RefreshSerialPortConnectState
        //函数功能：更新状态栏状态
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.12.23&2022.12.02
        //*************************************************************************
        public void RefreshSerialPortConnectState(string strState)
        {
            SerialPortConnectState = strState;
            switch (strState)
            {
                case ConnectState.Explaination_NotConnect:
                case ConnectState.Explaination_Stop:
                case ConnectState.Explaination_Error:
                case ConnectState.Explaination_OffLine://由Lilbert于2022.12.02添加离线状态
                    SerialPortConnectStateFontColor = BackgroundState.Red;
                    break;
                default:
                    SerialPortConnectStateFontColor = BackgroundState.Green;
                    break;
            }            
        }

        ////*************************************************************************
        ////函数名称：RefreshSlaveAxisAddress
        ////函数功能：更新轴地址状态
        ////
        ////输入参数：None
        ////         
        ////输出参数：None
        ////        
        ////编码作者：Lilbert
        ////更新时间：2023.01.06
        ////*************************************************************************
        //public void RefreshSlaveAxisAddress(string strAddress)
        //{
        //    //AxisAddress = "轴地址：" + strAddress;
        //    if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
        //    {
        //        AxisAddress = "轴地址：" + strAddress;
        //    }
        //    else
        //    {
        //        AxisAddress = "AxisAddress：" + strAddress;
        //    }
        //}

        //*************************************************************************
        //函数名称：RefreshAxisAddress
        //函数功能：更新轴地址状态
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.02.24&2022.12.30
        //*************************************************************************
        public void RefreshAxisAddress(string strAddress)
        {
            //AxisAddress = "轴地址：" + strAddress;
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                AxisAddress = "轴地址：" + strAddress;

                GlobalCurrentInput.SelectedAxisID = strAddress;
            }
            else
            {
                AxisAddress = "AxisAddress：" + strAddress;
            }
        }

        //*************************************************************************
        //函数名称：RefreshStatusWord
        //函数功能：更新系统状态
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.02.28&2022.11.29
        //*************************************************************************
        public void RefreshStatusWord(string strStatus)
        {           
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                switch (strStatus)
                {
                    case "8":
                    case "24":
                        StatusWord = ServoStatus.ERROR;
                        StatusWordFontColor = BackgroundState.Red;
                        SoftwareStateParameterSet.ServoStatus = ServoStatus.ERROR;

                        ServoStatusSwitchControl(true);

                        SlideServoStatusControl(Status: "");//由Lilbert于2022.11.29增加侧边栏伺服状态
                        break;
                    case "0":
                    case "80":
                    case "64":
                    case "49":
                    case "51":
                    case "33":
                    case "35":
                    case "23":
                    case "31":
                    case "7":
                    case "15":
                        StatusWord = ServoStatus.PREPARATION;
                        StatusWordFontColor = BackgroundState.Blue;
                        SoftwareStateParameterSet.ServoStatus = ServoStatus.PREPARATION;

                        ServoStatusSwitchControl(true);

                        SlideServoStatusControl(Status: "ServoDisabled");//由Lilbert于2022.11.29增加侧边栏伺服状态
                        break;
                    case "55":
                        StatusWord = ServoStatus.RUNING;
                        StatusWordFontColor = BackgroundState.Green;
                        SoftwareStateParameterSet.ServoStatus = ServoStatus.RUNING;

                        ServoStatusSwitchControl(false);

                        SlideServoStatusControl(Status: "ServoEnabled");//由Lilbert于2022.11.29增加侧边栏伺服状态
                        break;
                    default:
                        StatusWordFontColor = BackgroundState.Red;
                        StatusWord = ServoStatus.UNDEFINED;

                        ServoStatusSwitchControl(false);

                        SlideServoStatusControl(Status: "");//由Lilbert于2022.11.29增加侧边栏伺服状态
                        break;
                }
            }
            else
            {
                switch (strStatus)
                {
                    case "8":
                    case "24":
                        StatusWord = ServoStatus.ERROR1;
                        StatusWordFontColor = BackgroundState.Red;
                        SoftwareStateParameterSet.ServoStatus = ServoStatus.ERROR1;

                        ServoStatusSwitchControl(true);

                        SlideServoStatusControl(Status: "");//由Lilbert于2022.11.29增加侧边栏伺服状态
                        break;
                    case "0":
                    case "80":
                    case "64":
                    case "49":
                    case "51":
                    case "33":
                    case "35":
                    case "23":
                    case "31":
                    case "7":
                    case "15":
                        StatusWord = ServoStatus.PREPARATION1;
                        StatusWordFontColor = BackgroundState.Blue;
                        SoftwareStateParameterSet.ServoStatus = ServoStatus.PREPARATION1;

                        ServoStatusSwitchControl(true);

                        SlideServoStatusControl(Status: "ServoDisabled");//由Lilbert于2022.11.29增加侧边栏伺服状态
                        break;
                    case "55":
                        StatusWord = ServoStatus.RUNING1;
                        StatusWordFontColor = BackgroundState.Green;
                        SoftwareStateParameterSet.ServoStatus = ServoStatus.RUNING1;

                        ServoStatusSwitchControl(false);

                        SlideServoStatusControl(Status: "ServoEnabled");//由Lilbert于2022.11.29增加侧边栏伺服状态
                        break;
                    default:
                        StatusWordFontColor = BackgroundState.Red;
                        StatusWord = ServoStatus.UNDEFINED1;

                        ServoStatusSwitchControl(false);

                        SlideServoStatusControl(Status: "");//由Lilbert于2022.11.29增加侧边栏伺服状态
                        break;
                }
            }
        }       

        //*************************************************************************
        //函数名称：RefreshFeedback
        //函数功能：更新反馈
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.03&2023.01.03
        //*************************************************************************
        public void RefreshFeedback(string strName, string strValue, string strUnit)
        {            
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                switch (strName)
                {
                    case "Position Actual Value":
                        PositionFeedback = "位置反馈：" + OthersHelper.ExchangeUnit("Position Actual Value", DefaultUnit.PositionUnit + "-" + SelectUnit.Position, strValue) + " " + CurrentUnit.Position;
                        break;
                    case "Velocity Actual Value":
                        SpeedFeedback = "速度反馈：" + OthersHelper.ExchangeUnit("Velocity Actual Value", DefaultUnit.SpeedUnit + "-" + SelectUnit.Speed, strValue) + " " + CurrentUnit.Speed;
                        break;
                    case "Q Axis Actual Current":
                        CurrentFeedback = "电流反馈：" + strValue + " " + strUnit;
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (strName)
                {
                    case "Position Actual Value":
                        PositionFeedback = "PositionFeedback：" + OthersHelper.ExchangeUnit("Position Actual Value", DefaultUnit.PositionUnit + "-" + SelectUnit.Position, strValue) + " " + CurrentUnit.Position;
                        break;
                    case "Velocity Actual Value":
                        SpeedFeedback = "SpeedFeedback：" + OthersHelper.ExchangeUnit("Velocity Actual Value", DefaultUnit.SpeedUnit + "-" + SelectUnit.Speed, strValue) + " " + CurrentUnit.Speed;
                        break;
                    case "Q Axis Actual Current":
                        CurrentFeedback = "CurrentFeedback：" + strValue + " " + strUnit;
                        break;
                    default:
                        break;
                }
            }
        }

        //*************************************************************************
        //函数名称：RefreshAlarmInfo
        //函数功能：更新报警信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.12
        //*************************************************************************
        public void RefreshAlarmInfo()
        {
            //获取和分析报警信息集合
            GetAndAnalysisAlarmSet();

            //侧边栏报警控制
            SlideAlarmControl();          
        }
        #endregion

        #region 信息提示
        //*************************************************************************
        //函数名称：ShowNotification
        //函数功能：信息提示
        //
        //输入参数：In_iType     信息提示类型
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.23
        //*************************************************************************
        public void ShowNotification(int In_iType)
        {
            INotification notification = null;

            try
            {
                switch (In_iType)
                {
                    case RET.NO_EFFECT:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "指令无效，请关闭系统后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case RET.ERROR:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "系统异常，请关闭系统后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 1000:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "串口未开启，请稍后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 1001:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "伺服状态异常，请稍后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 1002:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "串口通信连接失败，端口可能被占用...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 1003:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "串口通信连接成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2000:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "加载失败，请确认文件是否被占用...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2001:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "加载失败，请导入正确配置文件...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2002:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "保存成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2003:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "保存失败，请确认文件是否被占用...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2004:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "设置成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2005:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "设置无效，请选择Config文件夹中的文件...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2006:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "文件不存在...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2007:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "加载成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2008:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "输入数据无效，可能输入异常字符，请重新输入...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2009:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "参数只读...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2010:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "当前存在数据采集任务，请稍后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2011:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "当前波形没有绘制完成，请稍后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2012:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "触发水平未设置，请设置后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2013:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "系统未记录最后一条采集数据...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2014:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "选择的DI功能不满足回零任务...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2015:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "固件升级失败，请导入升级文件...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2016:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "固件升级失败，请确认串口通信状态，并重启软件...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2017:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "密码错误!", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2018:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "密码正确，管理员模式解锁...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2019:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "时间戳字段禁止手动写入...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2020:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "串口未开启，单位将设置为默认单位...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2021:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "密码修改失败，原密码输入错误...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2022:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "密码修改成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2023:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "单位设置成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2024:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "电机参数辨识开始...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2025:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "电机参数辨识停止...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2026:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "电机参数辨识成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2027:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "电机参数替换成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2028:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "导入失败，升级文件名不能存在汉字字符...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2029:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "离线惯量识别开始...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2030:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "离线惯量识别停止...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2031:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "离线惯量识别替换成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2032:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "只能选择一条电机参数表...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                        break;
                    case 2033:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "参数表名称已存在，保存会覆盖原有参数库文件...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2034:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "删除成功...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    //增加提示软件版本不同的提示
                    case 2035:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "EEPROM版本号不同，不能导入文件...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2036:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "当前存在故障数据采集任务，请稍后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    case 2037:
                        notification = DefaultNotificationService.CreatePredefinedNotification("信息提示：", "导入参数没有差异数据...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                        break;
                    default:
                        return;
                }

                notification.ShowAsync();
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SHOW_NOTIFICATION, "ShowNotification" + In_iType, ex);
            }
        }

        public void ShowNotification_For_MotorFeedback(string In_strName, string In_strItem, string In_strMin, string In_strMax, string In_Unit, bool bExchanged)
        {
            string strMax = null;
            string strMin = null;
            string strInfo = null;
            string strInfol = null;
            INotification notification = null;

            try
            {
                if (!bExchanged)
                {
                    strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                    strInfol = In_strItem;
                }
                else
                {
                    if (In_Unit == "cnt" || In_Unit == "指令单位" || In_Unit == "编码器单位")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.PositionUnit + "-" + CurrentUnit.Position, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.PositionUnit + "-" + CurrentUnit.Position, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                            strInfol = In_strItem;
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Position + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;
                            strInfol = In_strItem;
                        }
                    }
                    else if (In_Unit == "cnt/s" || In_Unit == "指令单位/s")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                            strInfol = In_strItem;
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Speed + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;
                            strInfol = In_strItem;
                        }
                    }
                    else if (In_Unit == "cnt/s^2" || In_Unit == "指令单位/s^2" || In_Unit.ToUpper() == "指令单位/S2")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.AccelerationUnit + "-" + CurrentUnit.Acceleration, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.AccelerationUnit + "-" + CurrentUnit.Acceleration, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                            strInfol = In_strItem;
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Acceleration + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;
                            strInfol = In_strItem;
                        }
                    }
                    else if (In_Unit == "0.1%额定扭矩")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.TorqueUnit + "-" + CurrentUnit.Torque, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.TorqueUnit + "-" + CurrentUnit.Torque, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                            strInfol = In_strItem;
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Torque + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;
                            strInfol = In_strItem;
                        }
                    }
                    else
                    {
                        strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                        strInfol = In_strItem;
                    }
                }

                notification = DefaultNotificationService.CreatePredefinedNotification("参数超出合理范围：", strInfo, DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                notification.ShowAsync();

                //ShowHintInfo("参数超出合理范围：" + strInfol);
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml") //由Lilbert于2022.11.24添加信息显示方式
                {
                    ShowHintInfo("参数超出合理范围：" + strInfol);
                }
                else
                {
                    ShowHintInfo("Parameter out of reasonable range：" + strInfol);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SHOW_NOTIFICATION_MOTORFEEDBACK, "ShowNotification_For_MotorFeedback", ex);
            }
        }

        public void ShowNotification_For_ParameterReadWrite(string In_strName, string In_strItem, string In_strMin, string In_strMax, string In_Unit, bool bExchanged)
        {
            string strMax = null;
            string strMin = null;
            string strInfo = null;
            string strInfol = null;
            INotification notification = null;

            try
            {
                if (!bExchanged)
                {
                    strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                    strInfol = In_strItem;
                }
                else
                {
                    if (In_Unit == "cnt" || In_Unit == "指令单位" || In_Unit == "编码器单位")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.PositionUnit + "-" + CurrentUnit.Position, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.PositionUnit + "-" + CurrentUnit.Position, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                            strInfol = In_strItem;
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Position + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;
                            strInfol = In_strItem;
                        }
                    }
                    else if (In_Unit == "cnt/s" || In_Unit == "指令单位/s")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                            strInfol = In_strItem;
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Speed + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;
                            strInfol = In_strItem;
                        }
                    }
                    else if (In_Unit == "cnt/s^2" || In_Unit == "指令单位/s^2" || In_Unit.ToUpper() == "指令单位/S2")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.AccelerationUnit + "-" + CurrentUnit.Acceleration, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.AccelerationUnit + "-" + CurrentUnit.Acceleration, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                            strInfol = In_strItem;
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Acceleration + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;
                            strInfol = In_strItem;
                        }
                    }
                    else if (In_Unit == "0.1%额定扭矩")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.TorqueUnit + "-" + CurrentUnit.Torque, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.TorqueUnit + "-" + CurrentUnit.Torque, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                            strInfol = In_strItem;
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Torque + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;
                            strInfol = In_strItem;
                        }
                    }
                    else
                    {
                        strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                        strInfol = In_strItem;
                    }
                }

                notification = DefaultNotificationService.CreatePredefinedNotification("参数超出合理范围：", strInfo, DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                notification.ShowAsync();

                ShowHintInfo("参数超出合理范围：" + strInfol); //由Lilbert于2023.02.20添加信息显示方式
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SHOW_NOTIFICATION, "ShowNotification", ex);
            }
        }

        public void ShowNotification(string In_strName, string In_strItem, string In_strMin, string In_strMax, string In_Unit, bool bExchanged)
        {
            string strMax = null;
            string strMin = null;            
            string strInfo = null;            
            INotification notification = null;

            try
            {
                if (!bExchanged)
                {
                    strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                }
                else
                {
                    if (In_Unit == "cnt" || In_Unit == "指令单位" || In_Unit == "编码器单位")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.PositionUnit + "-" + CurrentUnit.Position, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.PositionUnit + "-" + CurrentUnit.Position, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;                            
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Position + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;                           
                        }          
                    }
                    else if (In_Unit == "cnt/s" || In_Unit == "指令单位/s")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Speed + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;
                        }  
                    }
                    else if (In_Unit == "cnt/s^2" || In_Unit == "指令单位/s^2" || In_Unit.ToUpper() == "指令单位/S2")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.AccelerationUnit + "-" + CurrentUnit.Acceleration, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.AccelerationUnit + "-" + CurrentUnit.Acceleration, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Acceleration + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;
                        }             
                    }
                    else if (In_Unit == "0.1%额定扭矩")
                    {
                        strMax = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.TorqueUnit + "-" + CurrentUnit.Torque, In_strMax);
                        strMin = OthersHelper.ExchangeUnit(In_strName, DefaultUnit.TorqueUnit + "-" + CurrentUnit.Torque, In_strMin);

                        if (string.IsNullOrEmpty(strMax) || string.IsNullOrEmpty(strMin))
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                        }
                        else
                        {
                            strInfo = In_strItem + "\r\n" + "单位：" + CurrentUnit.Torque + "\r\n" + "最小值：" + strMin + "\r\n" + "最大值：" + strMax;
                        }         
                    }
                    else
                    {
                        strInfo = In_strItem + "\r\n" + "单位：" + In_Unit + "\r\n" + "最小值：" + In_strMin + "\r\n" + "最大值：" + In_strMax;
                    }
                }
                         
                notification = DefaultNotificationService.CreatePredefinedNotification("参数超出合理范围：", strInfo, DateTime.Now.ToString(), NotificationPicture.NG_BULB);                                  
                notification.ShowAsync();
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SHOW_NOTIFICATION, "ShowNotification", ex);
            }
        }

        //*************************************************************************
        //函数名称：ShowHintInfo
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        public void ShowHintInfo(string strInfo)
        {
            if (strInfo == "时间戳设置成功")
            {
                return;
            }
            
            //重新倒计时
            InfoDisplayCountDown = 0;

            //信息提示
            HintInfo = strInfo;

            //时钟开启
            Timer_Info.IsEnabled = true;

            //信息提示展示
            IsHintInfoEnabled = true;
        }
        #endregion

        #region 其他方法           
        //*************************************************************************
        //函数名称：SetDefaultParameterPath
        //函数功能：设置配置参数载入默认路径
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.18&2023.11.10
        //*************************************************************************
        public void SetDefaultParameterPath()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (MessageBoxService.ShowMessage("是否重新设置配置文件载入路径...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.No)
                {
                    return;
                }

                //获取路径
                //iRet = ExcelHelper.GetReadPath(ref strFilePath);
                iRet = ExcelHelper.GetReadPath_With_Path(ref strFilePath);//由Lilbert于2023.11.10更改为获取默认路径
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //默认路径必须是Config文件夹下
                if (strFilePath.Contains("\\Config\\"))
                {
                    IniHelper.IniWriteValue("ServoStudio", "Path", Path.GetFileName(strFilePath), FilePath.Ini);
                    ShowNotification(2004);
                }
                else
                {
                    ShowNotification(2005);
                }            
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SET_DEFAULT_PARAMETER_PATH, "SetDefaultParameterPath", ex);
            }
        }

        //*************************************************************************
        //函数名称：SetOneKeyShortcut
        //函数功能：设置一键快捷
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        public void SetOneKeyShortcut()
        {
            string strRet = "false";

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (MessageBoxService.ShowMessage("是否开启出厂模式...", "请确定", MessageButton.YesNo, MessageIcon.Information) == MessageResult.Yes)
                {
                    SoftwareStateParameterSet.IsOneKeyShortCut = true;
                    LevelFactoryModeVisibility = true;
                    strRet = "true";
                }
                else
                {
                    SoftwareStateParameterSet.IsOneKeyShortCut = false;
                    LevelFactoryModeVisibility = false;
                    strRet = "false";
                }

                IniHelper.IniWriteValue("ServoStudio", "OneKeyShortCut", strRet, FilePath.Ini);
                IniHelper.IniWriteValue("ServoStudio", "AlarmAutoExpand", "false", FilePath.Ini);
                SoftwareStateParameterSet.IsAlarmAutoExpand = false;

                ShowNotification(2004);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SET_ONE_KET_SHORT_CUT, "SetOneKeyShortcut", ex);
            }
        }

        //*************************************************************************
        //函数名称：SetIsAlarmAutoExpand
        //函数功能：设置报警内容可以自动弹出
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        public void SetIsAlarmAutoExpand()
        {
            string strRet = "false";

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (MessageBoxService.ShowMessage("是否自动弹出侧边栏报警信息...", "请确定", MessageButton.YesNo, MessageIcon.Information) == MessageResult.Yes)
                {
                    SoftwareStateParameterSet.IsAlarmAutoExpand = true;
                    strRet = "true";
                }
                else
                {
                    SoftwareStateParameterSet.IsAlarmAutoExpand = false;
                    strRet = "false";
                }
                        
                IniHelper.IniWriteValue("ServoStudio", "AlarmAutoExpand", strRet, FilePath.Ini);
                ShowNotification(2004);              
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SET_IS_ALARM_AUTO_EXPAND, "SetIsAlarmAutoExpand", ex);
            }
        }

        //*************************************************************************
        //函数名称：OpenManualBook
        //函数功能：打开使用手册
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.03
        //*************************************************************************
        public void OpenManualBook()
        {           
            try
            {
                if (File.Exists(FilePath.Manual))
                {
                    Process process = new Process();
                    process.StartInfo.FileName = FilePath.Manual;
                    process.StartInfo.Arguments = "";
                    process.StartInfo.WindowStyle = ProcessWindowStyle.Maximized;
                    process.Start();
                }
                else
                {
                    ShowNotification(2006);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_OPEN_MANUALBOOK, "OpenManualBook", ex);
            }
        }

        //*************************************************************************
        //函数名称：OpenServoMaintenanceManualBook
        //函数功能：打开伺服维护手册
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.05.19
        //*************************************************************************
        public void OpenServoMaintenanceManualBook()
        {
            try
            {
                if (File.Exists(FilePath.MaintenanceManual))
                {
                    Process process = new Process();
                    process.StartInfo.FileName = FilePath.MaintenanceManual;
                    process.StartInfo.Arguments = "";
                    process.StartInfo.WindowStyle = ProcessWindowStyle.Maximized;
                    process.Start();
                }
                else
                {
                    ShowNotification(2006);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_OPEN_MANUALBOOK, "OpenManualBook", ex);
            }
        }

        //*************************************************************************
        //函数名称：RefreshParameterReadAndWriteTabControl
        //函数功能：更新TabControl-参数读写数据刷新
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.18
        //*************************************************************************
        public void RefreshParameterReadAndWriteTabControl()
        {
            if (SoftwareStateParameterSet.CurrentPageName == PageName.Advanced ||
                SoftwareStateParameterSet.CurrentPageName == PageName.Auxiliary ||
                SoftwareStateParameterSet.CurrentPageName == PageName.Basic || 
                SoftwareStateParameterSet.CurrentPageName == PageName.CIA402 ||
                SoftwareStateParameterSet.CurrentPageName == PageName.Common ||
                SoftwareStateParameterSet.CurrentPageName == PageName.Control || 
                SoftwareStateParameterSet.CurrentPageName == PageName.DI ||
                SoftwareStateParameterSet.CurrentPageName == PageName.DO || 
                SoftwareStateParameterSet.CurrentPageName == PageName.FaultAndProtection ||
                SoftwareStateParameterSet.CurrentPageName == PageName.Motor)
            {
                ViewModelSet.ParameterReadWrite?.ParameterRead(SoftwareStateParameterSet.CurrentPageName);
            }
        }

        //*************************************************************************
        //函数名称：GetEditionInfo_For_Softwareversion
        //函数功能：获取版本信息——为参数导出软件版本号
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.02
        //*************************************************************************
        public void GetEditionInfo_For_Softwareversion()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            ObservableCollection<ParameterReadWriteSet> obsParameterInfo = new ObservableCollection<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    //MessageBoxService.ShowMessage("系统发布 - 季华实验室机器人中心" + "\r\n" + "版本 - " + SoftwareInfo.VERSION + "\r\n" + "开发日期 - " + SoftwareInfo.RELEASED_DATE, "ServoStudio版本信息", MessageButton.OK, MessageIcon.Information);
                }
                else
                {
                    dicParameterInfo.Add("Device Name", null);
                    dicParameterInfo.Add("Hardware Version", null);
                    dicParameterInfo.Add("Software Version", null);
                    OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo);

                    ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: false);

                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_ParameterRead_For_Softwareversion(PageName.MAIN, TaskName.ReadString_ForSoftVersion, lstTransmittingDataInfo);  //由Lilbert于2023.06.02更改
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_READ, "GetEditionInfo_For_Softwareversion", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetEditionInfo
        //函数功能：获取版本信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.05.09
        //*************************************************************************
        public void GetEditionInfo()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            ObservableCollection<ParameterReadWriteSet> obsParameterInfo = new ObservableCollection<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    //MessageBoxService.ShowMessage("系统发布 - 季华实验室机器人中心" + "\r\n" + "版本 - " + SoftwareInfo.VERSION + "\r\n" + "开发日期 - " + SoftwareInfo.RELEASED_DATE, "ServoStudio版本信息", MessageButton.OK, MessageIcon.Information);
                    MessageBoxService.ShowMessage("系统发布 - " + GlobalCompanyInfo.CompanyDepartment1 + "\r\n" + "版本 - " + SoftwareInfo.VERSION + "\r\n" + "开发日期 - " + SoftwareInfo.RELEASED_DATE, "ServoStudio版本信息", MessageButton.OK, MessageIcon.Information);
                    
                }
                else
                {                   
                    dicParameterInfo.Add("Device Name", null);
                    dicParameterInfo.Add("Hardware Version", null);
                    dicParameterInfo.Add("Software Version", null);
                    OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo);

                    ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: false);
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.ReadString, lstTransmittingDataInfo);                   
                }                     
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_GET_EDITION_INFO, "GetEditionInfo", ex);
            }
        }

        //*************************************************************************
        //函数名称：ClearMotorEncoderMultilapsAndFaults
        //函数功能：清除电机编码器多圈和故障
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.13
        //*************************************************************************
        public void ClearMotorEncoderMultilapsAndFaults()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //判断软件位置
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //写入控制字集合
                if (MessageBoxService.ShowMessage("是否清除多圈和故障...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    OthersHelper.OperatingControl("Get Motor Encoder Command", "1", TaskName.ClearMotorEncoderMultilapsAndFaults, "");
                    //iRet = OthersHelper.WriteControlWordSet(true, 0, TaskName.FaultReset);
                    //if (iRet == RET.NO_EFFECT)
                    //{
                    //    ShowNotification(RET.ERROR);
                    //    return;
                    //}

                    //开启任务管理与发送线程
                    if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                    {
                        ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CLEAR_MULTILAPS_AND_FAULTS, "ClearMotorEncoderMultilapsAndFaults", ex);
            }
        }

        //*************************************************************************
        //函数名称：ClearMotorEncoderMultilaps
        //函数功能：清除电机编码器多圈
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.13
        //*************************************************************************
        public void ClearMotorEncoderMultilaps()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //判断软件位置
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //写入控制字集合
                if (MessageBoxService.ShowMessage("是否清除多圈...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    OthersHelper.OperatingControl("Get Motor Encoder Command", "3", TaskName.ClearMotorEncoderMultilaps, "");
                    //iRet = OthersHelper.WriteControlWordSet(true, 0, TaskName.FaultReset);
                    //if (iRet == RET.NO_EFFECT)
                    //{
                    //    ShowNotification(RET.ERROR);
                    //    return;
                    //}

                    //开启任务管理与发送线程
                    if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                    {
                        ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CLEAR_MULTILAPS_AND_FAULTS, "ClearMotorEncoderMultilaps", ex);
            }
        }

        //*************************************************************************
        //函数名称：ClearMotorEncoderFaults
        //函数功能：清除电机编码器故障
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.13
        //*************************************************************************
        public void ClearMotorEncoderFaults()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //判断软件位置
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //写入控制字集合
                if (MessageBoxService.ShowMessage("是否清除多圈...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    OthersHelper.OperatingControl("Get Motor Encoder Command", "4", TaskName.ClearMotorEncoderFaults, "");
                    //iRet = OthersHelper.WriteControlWordSet(true, 0, TaskName.FaultReset);
                    //if (iRet == RET.NO_EFFECT)
                    //{
                    //    ShowNotification(RET.ERROR);
                    //    return;
                    //}

                    //开启任务管理与发送线程
                    if (!PthreadStatement.SerialPortTransmiting.PthreadWorking)
                    {
                        ViewModelSet.Main?.ThreadPool_SerialPortTransmitting();
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CLEAR_MULTILAPS_AND_FAULTS, "ClearMotorEncoderFaults", ex);
            }
        }

        //*************************************************************************
        //函数名称：DeleteElectronicErrorHistory
        //函数功能：删除硬件异常记录
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.25
        //*************************************************************************
        public void DeleteElectronicErrorHistory()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>() { { "Clear Error History", "1" } };
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (MessageBoxService.ShowMessage("是否清除历史报警...", "请确定", MessageButton.YesNo) == MessageResult.Yes)
                {
                    ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MAIN, TaskName.ClearHardwareAlarm, lstTransmittingDataInfo);

                    if (SoftwareStateParameterSet.CurrentPageName == PageName.HARDWAREALARMHISTORY && ViewModelSet.HardwareAlarmHistory != null)
                    {
                        if (ViewModelSet.HardwareAlarmHistory.HardwareAlarmHistory != null)
                        {
                            ViewModelSet.HardwareAlarmHistory.HardwareAlarmHistory.Clear();
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_DELETE_ELECTRONIC_ERROR_HISTORY, "DeleteElectronicErrorHistory", ex);
            }
        }

        //*************************************************************************
        //函数名称：DeleteSoftwareErrorHistory
        //函数功能：删除软件异常记录
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.25
        //*************************************************************************
        public void DeleteSoftwareErrorHistory()
        {
            int iRet = -1;
            DataTable dt = new DataTable();

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return;
                }

                if (MessageBoxService.ShowMessage("是否删除软件异常日志...", "请确定", MessageButton.YesNo) == MessageResult.Yes)
                {
                    //查询Excel数据
                    iRet = ExcelHelper.ReadFromExcel(FilePath.SoftwareErrorLog, ref GlobalErrorSet.dtSoftware);
                    if (iRet != RET.SUCCEEDED)
                    {          
                        return;
                    }

                    //清除数据-只保留列名
                    GlobalErrorSet.dtSoftware = GlobalErrorSet.dtSoftware.Clone();

                    //更新Excel配置文件
                    iRet = ExcelHelper.WriteIntoExcel(FilePath.SoftwareErrorLog, GlobalErrorSet.dtSoftware, ExcelType.SoftwareErrorLog);
                    if (iRet == RET.ERROR)
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_DELETE_SOFTWARE_ERROR_HISTORY, "DeleteSoftwareErrorHistory", ex);
            }
        }

        //*************************************************************************
        //函数名称：OpenLogFile
        //函数功能：打开Log文件
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.30
        //*************************************************************************
        public void OpenLogFile()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                iRet = ExcelHelper.GetReadPath_ForLog(ref strFilePath);
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                if (File.Exists(strFilePath))
                {
                    Process process = new Process();
                    process.StartInfo.FileName = strFilePath;
                    process.StartInfo.Arguments = "";
                    process.StartInfo.WindowStyle = ProcessWindowStyle.Maximized;
                    process.Start();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_OPEN_LOG_FILE, "OpenLogFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：ExpandRibbon
        //函数功能：展开Ribbon
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.05.27
        //*************************************************************************
        public void ExpandRibbon()
        {
            IsRibbonMinimized = false;
        }

        //*************************************************************************
        //函数名称：ThreadPool_SerialPortTransmitting
        //函数功能：线程池_串口数据发送
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.19
        //*************************************************************************
        public void ThreadPool_SerialPortTransmitting()
        {
            bool bRet = false;

            try
            {
                //线程间隔
                PthreadStatement.SerialPortTransmiting.PthreadInterval = TimerPeriod.PthreadTaskNormal;

                //线程开关
                PthreadStatement.SerialPortTransmiting.PthreadSwitch = true;

                //线程暂停
                PthreadStatement.SerialPortTransmiting.PthreadPause = false;

                //线程工作
                PthreadStatement.SerialPortTransmiting.PthreadWorking = true;

                bRet = ThreadPool.QueueUserWorkItem(PthreadStatement.SerialPortTransmiting.Run, null);
                if (bRet == false)
                {
                    ShowNotification(RET.ERROR);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_THREAD_POOL_SERIAL_PORT_TRANSMITTING, "ThreadPool_SerialPortTransmitting", ex);
            }
        }

        //*************************************************************************
        //函数名称：SlideServoStatusControl
        //函数功能：侧边栏伺服状态控制
        //
        //输入参数：string Status    当前状态
        //                 
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.29
        //*************************************************************************
        public void SlideServoStatusControl(string Status)
        {
            if (Status == "ServoEnabled")
            {
                SlideServoStatus = "使能状态";
                SlideServoStatusIcon = IconPath.SlideServoEnabled;
            }
            else if (Status == "ServoDisabled")
            {
                SlideServoStatus = "禁能状态";
                SlideServoStatusIcon = IconPath.SlideServoDisabled;
            }
            else
            {
                SlideServoStatus = "故障状态";
                SlideServoStatusIcon = IconPath.SlideServoFault;
            }
        }

        //*************************************************************************
        //函数名称：ControlServoStatusSwitch
        //函数功能：伺服状态控制
        //
        //输入参数：true     使能
        //                 false    禁能
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.12.30
        //*************************************************************************
        public void ControlServoStatusSwitch()
        {
           
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                if (ServoStatusSwitch == "伺服使能")
                {
                    ServoStatusSwitch = "禁能状态";
                    ServoStatusIcon = IconPath.ServoEnabled;
                }
                else
                {
                    ServoStatusSwitch = "使能状态";
                    ServoStatusIcon = IconPath.ServoDisabled;
                }
            }
            else
            {
                if (ServoStatusSwitch == "伺服使能")
                {
                    ServoStatusSwitch = "DisabledState";
                    ServoStatusIcon = IconPath.ServoEnabled;
                }
                else
                {
                    ServoStatusSwitch = "EnabledState";
                    ServoStatusIcon = IconPath.ServoDisabled;
                }
            }
        }

        //*************************************************************************
        //函数名称：ServoStatusSwitchControl
        //函数功能：伺服状态控制
        //
        //输入参数：true     使能
        //                 false    禁能
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.08.06
        //*************************************************************************
        public void ServoStatusSwitchControl(bool bSwitch)
        {
            //if (bSwitch)
            //{
            //    ServoStatusSwitch = "禁能状态";
            //    ServoStatusIcon = IconPath.ServoEnabled;
            //}
            //else
            //{
            //    ServoStatusSwitch = "使能状态";
            //    ServoStatusIcon = IconPath.ServoDisabled;
            //}
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                if (bSwitch)
                {
                    ServoStatusSwitch = "禁能状态";
                    ServoStatusIcon = IconPath.ServoEnabled;
                }
                else
                {
                    ServoStatusSwitch = "使能状态";
                    ServoStatusIcon = IconPath.ServoDisabled;
                }
            }
            else
            {
                if (bSwitch)
                {
                    ServoStatusSwitch = "DisabledState";
                    ServoStatusIcon = IconPath.ServoEnabled;
                }
                else
                {
                    ServoStatusSwitch = "EnabledState";
                    ServoStatusIcon = IconPath.ServoDisabled;
                }
            }
        }

        //*************************************************************************
        //函数名称：SlideControlModelControl
        //函数功能：伺服控制权状态控制
        //
        //输入参数：true     上位机控制
        //                 false    Ecat控制
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.17
        //*************************************************************************
        public void SlideControlModelControl()
        {
            if (OthersHelper.GetCurrentValueOfIndex("0x200201") == "0")
            {
                SlideControlModel = "PC控制";
                ControlModelIcon = IconPath.ComputerControl;
            }
            else
            {
                SlideControlModel = "Ecat控制";
                ControlModelIcon = IconPath.EcatControl;
            }

        }

        //*************************************************************************
        //函数名称：SlidePanelDisplayControl
        //函数功能：侧边栏展示控制
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.04.14&2022.12.30
        //*************************************************************************
        public void SlidePanelDisplayControl(bool bMonitor, bool bAlarm)
        {
            if (WindowSet.clsMainWindow != null)
            {
                if (WindowSet.clsMainWindow.WindowState == WindowState.Maximized)
                {
                    return;
                }
            }

            IsMonitoringPageHidden = bMonitor;   
            //if (IsMonitoringPageHidden)
            //{
            //    SlideMonitor = "监控展开";
            //    SlideMonitorIcon = IconPath.MonitorOpen;
            //}
            //else
            //{
            //    SlideMonitor = "监控收起";
            //    SlideMonitorIcon = IconPath.MonitorClose;
            //}
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                if (IsMonitoringPageHidden)
                {
                    SlideMonitor = "监控展开";
                    SlideMonitorIcon = IconPath.MonitorOpen;
                }
                else
                {
                    SlideMonitor = "监控收起";
                    SlideMonitorIcon = IconPath.MonitorClose;
                }
            }
            else
            {
                if (IsMonitoringPageHidden)
                {
                    SlideMonitor = "MonitorOpen";
                    SlideMonitorIcon = IconPath.MonitorOpen;
                }
                else
                {
                    SlideMonitor = "MonitorClose";
                    SlideMonitorIcon = IconPath.MonitorClose;
                }
            }

             IsAlarmInfoPageHidden = bAlarm;
            //if (IsAlarmInfoPageHidden)
            //{
            //    SlideAlarm = "报警展开";
            //    SlideAlarmIcon = IconPath.AlarmOpen;
            //}
            //else
            //{
            //    SlideAlarm = "报警收起";
            //    SlideAlarmIcon = IconPath.AlarmClose;
            //}
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                if (IsAlarmInfoPageHidden)
                {
                    SlideAlarm = "报警展开";
                    SlideAlarmIcon = IconPath.AlarmOpen;
                }
                else
                {
                    SlideAlarm = "报警收起";
                    SlideAlarmIcon = IconPath.AlarmClose;
                }
                    
            }
            else
            {
                if (IsAlarmInfoPageHidden)
                {
                    SlideAlarm = "AlarmOpen";
                    SlideAlarmIcon = IconPath.AlarmOpen;
                }
                else
                {
                    SlideAlarm = "AlarmClose";
                    SlideAlarmIcon = IconPath.AlarmClose;
                }
            }         
        }       

        //*************************************************************************
        //函数名称：SlideAlarmDisplayControl
        //函数功能：侧边栏报警
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.14
        //*************************************************************************
        public void SlideAlarmDisplayControl()
        {
            IsAlarmInfoPageHidden = !IsAlarmInfoPageHidden;         
        }
        public void OnIsAlarmInfoPageHiddenChanged()
        {
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                if (IsAlarmInfoPageHidden)
                {
                    SlideAlarm = "报警展开";
                    SlideAlarmIcon = IconPath.AlarmOpen;
                }
                else
                {
                    SlideAlarm = "报警收起";
                    SlideAlarmIcon = IconPath.AlarmClose;
                }

            }
            else
            {
                if (IsAlarmInfoPageHidden)
                {
                    SlideAlarm = "AlarmOpen";
                    SlideAlarmIcon = IconPath.AlarmOpen;
                }
                else
                {
                    SlideAlarm = "AlarmClose";
                    SlideAlarmIcon = IconPath.AlarmClose;
                }
            }
            //if (IsAlarmInfoPageHidden)
            //{
            //    SlideAlarm = "报警展开";
            //    SlideAlarmIcon = IconPath.AlarmOpen;
            //}
            //else
            //{
            //    SlideAlarm = "报警收起";
            //    SlideAlarmIcon = IconPath.AlarmClose;
            //}
        }
   
        //*************************************************************************
        //函数名称：SlideMonitorDisplayControl
        //函数功能：侧边栏监控
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.14
        //*************************************************************************
        public void SlideMonitorDisplayControl()
        {
            IsMonitoringPageHidden = !IsMonitoringPageHidden;                  
        }
        public void OnIsMonitoringPageHiddenChanged()
        {
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                if (IsMonitoringPageHidden)
                {
                    SlideMonitor = "监控展开";
                    SlideMonitorIcon = IconPath.MonitorOpen;
                }
                else
                {
                    SlideMonitor = "监控收起";
                    SlideMonitorIcon = IconPath.MonitorClose;
                }
            }
            else
            {
                if (IsMonitoringPageHidden)
                {
                    SlideMonitor = "MonitorOpen";
                    SlideMonitorIcon = IconPath.MonitorOpen;
                }
                else
                {
                    SlideMonitor = "MonitorClose";
                    SlideMonitorIcon = IconPath.MonitorClose;
                }
            }
            //if (IsMonitoringPageHidden)
            //{
            //    SlideMonitor = "监控展开";
            //    SlideMonitorIcon = IconPath.MonitorOpen;
            //}
            //else
            //{
            //    SlideMonitor = "监控收起";
            //    SlideMonitorIcon = IconPath.MonitorClose;
            //}
        }

        public void OnIsQuickZoomPageHiddenChanged()
        {
            //IsQuickZoomPageHidden = !IsQuickZoomPageHidden;
        }
        #endregion

        #endregion

        #region 私有方法  
        //*************************************************************************
        //函数名称：ObservableCollectionInitialize
        //函数功能：ObservableCollection初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.15
        //*************************************************************************
        private void ObservableCollectionInitialize()
        {
            try
            {
                ParameterMonitoring = new ObservableCollection<ParameterMonitoringSet>();

                SportMonitoring = new ObservableCollection<SportMonitoringSet>()
                {
                    ViewModelSource.Create(()=>new SportMonitoringSet() {Content="位置反馈[cnt]",Value="0" }),
                    ViewModelSource.Create(()=>new SportMonitoringSet() {Content="位置误差[cnt]",Value="0" }),
                    ViewModelSource.Create(()=>new SportMonitoringSet() {Content="速度反馈[rpm]",Value="0.000" }),
                    ViewModelSource.Create(()=>new SportMonitoringSet() {Content="电流反馈[A]",Value="0" }),
                    ViewModelSource.Create(()=>new SportMonitoringSet() {Content="状态机",Value="Not Ready" }),
                    ViewModelSource.Create(()=>new SportMonitoringSet() {Content="最后故障",Value="无" })
                };
                              
                DigitalInput = new ObservableCollection<DigitalInputSet>()
                {
                    ViewModelSource.Create(()=>new DigitalInputSet() {Content="DI1[伺服准备][CN1-44]", Status = "正常" }),
                    ViewModelSource.Create(()=>new DigitalInputSet() {Content="DI2[伺服准备][CN1-44]", Status = "异常" }),
                    ViewModelSource.Create(()=>new DigitalInputSet() {Content="DI3[伺服准备][CN1-44]", Status = "正常"}),
                    ViewModelSource.Create(()=>new DigitalInputSet() {Content="DI4[伺服准备][CN1-44]", Status = "异常" }),
                    ViewModelSource.Create(()=>new DigitalInputSet() {Content="DI5[伺服准备][CN1-44]", Status = "正常" }),
                    ViewModelSource.Create(()=>new DigitalInputSet() {Content="DI6[伺服准备][CN1-44]", Status = "正常" }),
                };

                DigitalOutput = new ObservableCollection<DigitalOutputSet>()
                {
                    ViewModelSource.Create(()=>new DigitalOutputSet() {Content="DO1[伺服准备][CN1-44]", Status = "正常" }),
                    ViewModelSource.Create(()=>new DigitalOutputSet() {Content="DO2[伺服准备][CN1-44]", Status = "正常" }),
                    ViewModelSource.Create(()=>new DigitalOutputSet() {Content="DO3[伺服准备][CN1-44]", Status = "异常" }),
                    ViewModelSource.Create(()=>new DigitalOutputSet() {Content="DO4[伺服准备][CN1-44]", Status = "异常" }),
                    ViewModelSource.Create(()=>new DigitalOutputSet() {Content="DO5[伺服准备][CN1-44]", Status = "正常" }),
                    ViewModelSource.Create(()=>new DigitalOutputSet() {Content="DO6[伺服准备][CN1-44]", Status = "异常" }),
                };
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_INITIALIZE, "ObservableCollectionInitialize", ex);
            }
        }


        //*************************************************************************
        //函数名称：DispatcherTimerInitialize
        //函数功能：时钟初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.04
        //*************************************************************************
        private void DispatcherTimerInitialize()
        {
            try
            {
                //系统时间间隔
                Timer_System.Interval = TimeSpan.FromMilliseconds(TimerPeriod.System);
                //超过时间间隔时发生Timer_System_Tick(系统主时钟)
                Timer_System.Tick += Timer_System_Tick;
                Timer_System.Start();

                //信息提示时间间隔
                Timer_Info.Interval = TimeSpan.FromMilliseconds(TimerPeriod.HintInfo);
                Timer_Info.Tick += Timer_Info_Tick;

                //重启系统时间间隔
                Timer_RestartSystem.Interval = TimeSpan.FromMilliseconds(TimerPeriod.HintInfo);
                Timer_RestartSystem.Tick += Timer_RestartSystem_Tick; 
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_DISPATCHER_TIMER_INITIALIZE, "DispatcherTimerInitialize", ex);
                ShowNotification(RET.ERROR);
            }
        }

        //*************************************************************************
        //函数名称：EventInitialize
        //函数功能：注册事件初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.23
        //*************************************************************************
        private void EventInitialize()
        {
            if (WindowSet.clsMainWindow != null)
            {
                WindowSet.clsMainWindow.evtClosingHint += ExitHint;
            }

            if (PthreadStatement.SerialPortTransmiting != null)
            {
                PthreadStatement.SerialPortTransmiting.evtEvaluationSerialPortWorkProcess += RefreshSerialPortWorkState;
            }
        }

        //*************************************************************************
        //函数名称：OthersInitialize
        //函数功能：其他初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.14
        //*************************************************************************
        private void OthersInitialize()
        {
            //侧边栏
            SlidePanelDisplayControl(true, true);

            //伺服状态切换
            ServoStatusSwitchControl(true);

            //伺服控制权切换
            SlideControlModelControl();

            //状态栏
            //RefreshSerialPortWorkState(TaskName.Empty);
            //RefreshSerialPortConnectState(ConnectState.Explaination_NotConnect);
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                RefreshSerialPortWorkState(TaskName.Empty);

                RefreshSerialPortConnectState(ConnectState.Explaination_NotConnect);
            }
            else
            {
                RefreshSerialPortWorkState(TaskName.Empty1);

                RefreshSerialPortConnectState(ConnectState.Explaination_NotConnect1);
            }
                
            RefreshAxisAddress("待选择");
            RefreshStatusWord("8");

            //所有Page使能
            IsAllPageEnabled = true;
            IsUnitExchangedEnabled = true;
            IsQuickZoomPageHidden = false;

            //管理员模式
            AdministratorVisibility = false;

            if (SoftwareStateParameterSet.IsOneKeyShortCut)
            {
                LevelFactoryModeVisibility = true;
            }
            else
            {
                LevelFactoryModeVisibility = false;
            }

            //报警
            SlideAlarmGroupBoxState(State: "NoExistAlarm");
        }

        //*************************************************************************
        //函数名称：CheckIsSystemOpened
        //函数功能：判断系统是否已经开启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.14
        //*************************************************************************
        private void CheckIsSystemOpened()
        {
            System.Diagnostics.Process[] MyProcess = System.Diagnostics.Process.GetProcessesByName("ServoStudio");
            if (MyProcess.Length > 1)
            {
                OthersHelper.GetWindowsStartupPosition();
                MessageBoxService.ShowMessage("检测到ServoStudio可能已经开启，建议不要重复启动软件...", "系统提示", MessageButton.OK, MessageIcon.Warning);
            }
        }

        //*************************************************************************
        //函数名称：GetAndAnalysisAlarmSet
        //函数功能：获取并分析报警信息集合
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.12
        //*************************************************************************
        private void GetAndAnalysisAlarmSet()
        {
            try
            {
                #region 获取报警编号
                string strAlarmSet0 = OthersHelper.GetAlarmSet(OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Set0", "Index")));
                string strAlarmSet1 = OthersHelper.GetAlarmSet(OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Set1", "Index")));
                string strAlarmSet2 = OthersHelper.GetAlarmSet(OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Set2", "Index")));
                string strAlarmSet3 = OthersHelper.GetAlarmSet(OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Set3", "Index")));
                if (string.IsNullOrEmpty(strAlarmSet0) || string.IsNullOrEmpty(strAlarmSet1) || string.IsNullOrEmpty(strAlarmSet2) || string.IsNullOrEmpty(strAlarmSet3))
                {
                    SetHardwareAlarmInfoFlag(IsExist: false, IsPrompt: true);
                    return;
                }
                else
                {
                    HardwareAlarmInfoSet.CurrentAlarmSet = strAlarmSet3 + strAlarmSet2 + strAlarmSet1 + strAlarmSet0;
                    char[] arrAlarmSet = HardwareAlarmInfoSet.CurrentAlarmSet.ToCharArray();
                    HardwareAlarmInfoSet.CurrentAlarmBitList.Clear();

                    for (int i = arrAlarmSet.Length - 1; i >= 0; i--)
                    {
                        if (arrAlarmSet[i] == '1')
                        {
                            HardwareAlarmInfoSet.CurrentAlarmBitList.Add(new AlarmBitAndTime() { Bit = arrAlarmSet.Length - i, AlarmTime = DateTime.Now });
                        }
                    }
                }                                     
                #endregion

                #region 更新Last报警信息
                if (HardwareAlarmInfoSet.LastAlarmSet == HardwareAlarmInfoSet.CurrentAlarmSet)
                {
                    if (HardwareAlarmInfoSet.CurrentAlarmBitList.Count == 0)
                    {
                        SetHardwareAlarmInfoFlag(IsExist: false, IsPrompt: true);
                    }
                    else
                    {
                        SetHardwareAlarmInfoFlag(IsExist: true, IsPrompt: true);
                    }
                    return;          
                }
                else
                {
                    HardwareAlarmInfoSet.LastAlarmSet = HardwareAlarmInfoSet.CurrentAlarmSet;
                }
                #endregion

                #region 新消息处理
                if (HardwareAlarmInfoSet.CurrentAlarmBitList.Count == 0)
                {
                    SetHardwareAlarmInfoFlag(IsExist: false, IsPrompt:false);
                    SlideAlarmGroupBoxState(State:"NoExistAlarm");
                }
                else
                {
                    SetHardwareAlarmInfoFlag(IsExist: true, IsPrompt:false);

                    #region 更新时间戳
                    foreach (var item in HardwareAlarmInfoSet.CurrentAlarmBitList)
                    {
                        var query = HardwareAlarmInfoSet.LastAlarmBitList.Where(o => o.Bit == item.Bit).FirstOrDefault();
                        if (query != null)
                        {
                            item.AlarmTime = query.AlarmTime;
                        }
                    }

                    HardwareAlarmInfoSet.LastAlarmBitList.Clear();
                    HardwareAlarmInfoSet.CurrentAlarmBitList.ForEach(item => HardwareAlarmInfoSet.LastAlarmBitList.Add(item));
                    HardwareAlarmInfoSet.AlarmNum = HardwareAlarmInfoSet.LastAlarmBitList.Count;
                    #endregion

                    #region 获取报警详细信息
                    SlideHardwareAlarm.Clear();
                    foreach (var item in HardwareAlarmInfoSet.CurrentAlarmBitList)
                    {
                        AlarmInfoSet alarmInfoSet = GlobalParameterSet.lstHardwareAlarm.Where(o => o.Bit == item.Bit.ToString()).FirstOrDefault<AlarmInfoSet>();
                        if (alarmInfoSet != null)
                        {
                            SlideHardwareAlarmSet slideHardwareAlarmSet = new SlideHardwareAlarmSet();
                            slideHardwareAlarmSet.Code = alarmInfoSet.Code.Replace("0x", "");
                            slideHardwareAlarmSet.Content = alarmInfoSet.Content;
                            slideHardwareAlarmSet.DateTime = item.AlarmTime.ToString();
                            slideHardwareAlarmSet.Measure = alarmInfoSet.Measure;
                            slideHardwareAlarmSet.GroupBoxState = "Maximized";
                            switch (alarmInfoSet.Level)
                            {
                                case "1":
                                    slideHardwareAlarmSet.Level = "低级";
                                    break;
                                case "2":
                                    slideHardwareAlarmSet.Level = "中级";
                                    break;
                                case "3":
                                    slideHardwareAlarmSet.Level = "高级";
                                    break;
                                default:
                                    slideHardwareAlarmSet.Level = "无";
                                    break;
                            }

                            SlideHardwareAlarm.Add(slideHardwareAlarmSet);
                        }                      
                    }
                    #endregion

                    SlideAlarmGroupBoxState(State: "ExistAlarm");
                }              
                #endregion
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("GetAndAnalysisAlarmSet", ex);
            }
        }

        //*************************************************************************
        //函数名称：SetHardwareAlarmInfoFlag
        //函数功能：标志位设置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.13
        //*************************************************************************
        private void SetHardwareAlarmInfoFlag(bool IsExist, bool IsPrompt)
        {
            HardwareAlarmInfoSet.IsExistAlarm = IsExist;
            HardwareAlarmInfoSet.IsAlreadyPrompt = IsPrompt;
        }

        //*************************************************************************
        //函数名称：SlideAlarmControl
        //函数功能：侧边栏报警控制
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.12
        //*************************************************************************
        private void SlideAlarmControl()
        {
            //信息已经提示不做任何处理
            if (HardwareAlarmInfoSet.IsAlreadyPrompt)
            {
                return;
            }

            //报警信息自动开关
            if (SoftwareStateParameterSet.IsAlarmAutoExpand)
            {
                #region 报警信息侧边栏状态为展开
                if (!IsAlarmInfoPageHidden)
                {
                    if (HardwareAlarmInfoSet.IsExistAlarm)//有报警
                    {
                        //ShowHintInfo("伺服系统报警 -" + HardwareAlarmInfoSet.AlarmNum + "条故障信息");
                        if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                        {
                            ShowHintInfo("伺服系统报警 -" + HardwareAlarmInfoSet.AlarmNum + "条故障信息");
                        }
                        else
                        {
                            ShowHintInfo("Servo system alarm -" + HardwareAlarmInfoSet.AlarmNum + "fault messages");
                        }

                        if (AcquisitionInfoSet.IsExistTask)//当前有采样任务
                        {
                            IsAlarmInfoPageHidden = true;//侧边栏缩回                              
                        }

                        if (FaultAcquisitionInfoSet.IsExistTask)//当前有故障数据采样任务
                        {
                            IsAlarmInfoPageHidden = true;//侧边栏缩回                              
                        }
                    }
                    else//没有报警
                    {
                        IsAlarmInfoPageHidden = true;//侧边栏缩回  
                        //ShowHintInfo("伺服系统正常，侧边栏信息收起");
                        if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                        {
                            ShowHintInfo("伺服系统正常，侧边栏信息收起");
                        }
                        else
                        {
                            ShowHintInfo("The servo system is normal, and the sidebar information is folded");
                        }
                    }
                }
                #endregion

                #region 报警信息侧边栏状态为关闭
                if (IsAlarmInfoPageHidden)
                {
                    if (HardwareAlarmInfoSet.IsExistAlarm)//有报警
                    {
                        //ShowHintInfo("伺服系统报警 -" + HardwareAlarmInfoSet.AlarmNum + "条故障信息");
                        if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                        {
                            ShowHintInfo("伺服系统报警 -" + HardwareAlarmInfoSet.AlarmNum + "条故障信息");
                        }
                        else
                        {
                            ShowHintInfo("Servo system alarm -" + HardwareAlarmInfoSet.AlarmNum + "fault messages");
                        }

                        if (!AcquisitionInfoSet.IsExistTask)//当前有采样任务
                        {
                            IsAlarmInfoPageHidden = false;
                        }

                        if (FaultAcquisitionInfoSet.IsExistTask)//当前有故障数据采样任务
                        {
                            IsAlarmInfoPageHidden = true;//侧边栏缩回                              
                        }
                    }
                }
                #endregion
            }

            //报警信息非自动开关
            if (!SoftwareStateParameterSet.IsAlarmAutoExpand && HardwareAlarmInfoSet.IsExistAlarm)
            {
                //ShowHintInfo("伺服系统报警 -" + HardwareAlarmInfoSet.AlarmNum + "条故障信息");
                if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                {
                    ShowHintInfo("伺服系统报警 -" + HardwareAlarmInfoSet.AlarmNum + "条故障信息");
                }
                else
                {
                    ShowHintInfo("Servo system alarm -" + HardwareAlarmInfoSet.AlarmNum + "fault messages");
                }
            }      
        }

        //*************************************************************************
        //函数名称：SlideAlarmGroupBoxState
        //函数功能：侧边栏报警控制
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.13
        //*************************************************************************
        private void SlideAlarmGroupBoxState(string State)
        {
            if (SlideHardwareAlarm == null)
            {
                SlideHardwareAlarm = new ObservableCollection<SlideHardwareAlarmSet>();
            }
            
            if (State == "NoExistAlarm")
            {
                SlideHardwareAlarm.Clear();
            }

            int count = SlideHardwareAlarm.Count;
            for (int i = 0; i < 5 - count; i++)//最多5个报警
            {
                SlideHardwareAlarmSet slideHardwareAlarmSet = new SlideHardwareAlarmSet();
                slideHardwareAlarmSet.GroupBoxState = "Minimized";
                slideHardwareAlarmSet.DateTime = "";
                slideHardwareAlarmSet.Code = "";
                slideHardwareAlarmSet.Level = "";
                slideHardwareAlarmSet.Measure = "";
                slideHardwareAlarmSet.Content = "";
                SlideHardwareAlarm.Add(slideHardwareAlarmSet);
            }

            HardwareAlarmInfoSet.SlideAlarmState.Clear();
            foreach (var item in SlideHardwareAlarm)
            {
                HardwareAlarmInfoSet.SlideAlarmState.Add(item.GroupBoxState);
            }
            WindowSet.clsMainWindow?.ControlSlideAlarmState();
        }

        //*************************************************************************
        //函数名称：StopRefreshIntervalDisplayProperty
        //函数功能：停止间隔刷新属性
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.13
        //*************************************************************************
        private void StopRefreshIntervalDisplayProperty()
        {
            IsAlarmInfoPageHidden = true;
            IsMonitoringPageHidden = true;
            Timer_System.IsEnabled = false;
            Timer_RestartSystem.IsEnabled = true;
        }

        //*************************************************************************
        //函数名称：Timer_System_Tick
        //函数功能：系统主时钟
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.12.04&2022.05.19
        //*************************************************************************
        private void Timer_System_Tick(object sender, EventArgs e)
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            ObservableCollection<ParameterReadWriteSet> obsParameterInfo = new ObservableCollection<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo_All = new List<TransmitingDataInfoSet>();//总的发送数据集合
            List<TransmitingDataInfoSet> lstTransmittingDataInfo_Monitoring = new List<TransmitingDataInfoSet>();//数据监控
  
            try
            {
                #region 判断电脑端串口是否开启,60次后显示离线状态
                for (int i = 1; i <= 60; i++)
                {
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet != RET.SUCCEEDED)
                    {
                        if (i == 60)
                        {
                            IsHintMonitorEnabled = false;
                            RefreshSerialPortConnectState(ConnectState.Explaination_OffLine);                            
                            return;
                        }
                    }
                    else
                    {
                        IsHintMonitorEnabled = true;
                    }
                }
                #endregion

                #region 断线后重新连接
                //if (SoftwareStateParameterSet.OpenConnectionFlag && SoftwareStateParameterSet.CloseConnectionFlag && !CommunicationSetViewModel.IsInitialized)
                //{
                //    SoftwareStateParameterSet.OpenConnectionFlag = false;
                //    SoftwareStateParameterSet.CloseConnectionFlag = false;
                //    CommunicationSet.SerialPortInfo.Close();                    
                //    return;                   
                //}
                //else if (SoftwareStateParameterSet.OpenConnectionFlag && !CommunicationSet.SerialPortInfo.IsOpen)
                //{
                //    for (int i = 1; i <= 10; i++)
                //    {
                //        ViewModelSet.CommunicationSet?.OpenSerialPortConnection_Reconnection();

                //        iRet = HexHelper.CheckSerialPortStatus();
                //        if (iRet != RET.SUCCEEDED)
                //        {
                //            if (i == 10)
                //            {
                //                ViewModelSet.Main?.ShowHintInfo("伺服断线，请检查伺服连线状态...");
                //                IsHintMonitorEnabled = false;
                //                return;
                //            }
                //        }
                //        else
                //        {
                //            IsHintMonitorEnabled = true;
                //            RefreshSerialPortConnectState(ConnectState.Explaination_Connect);
                //        }
                //    }
                //}
                //else
                //{
                //    iRet = HexHelper.CheckSerialPortStatus();
                //    if (iRet != RET.SUCCEEDED)
                //    {
                //        IsHintMonitorEnabled = false;
                //        return;
                //    }
                //    else
                //    {
                //        IsHintMonitorEnabled = true;

                //        //ViewModelSet.ParameterReadWrite?.ParameterRead("电机参数");
                //    }
                //}
                //iRet = HexHelper.CheckSerialPortStatus();
                //if (iRet != RET.SUCCEEDED)
                //{
                //    IsHintMonitorEnabled = false;
                //    return;
                //}
                //else
                //{
                //    IsHintMonitorEnabled = true;
                //}
                #endregion

                #region 驱动器端断线重连
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    IsHintMonitorEnabled = false;
                    return;
                }
                else
                {
                    IsHintMonitorEnabled = true;
                }
                #endregion

                //伺服驱动器控制权
                SlideControlModelControl();

                #region 读参数任务下达
                //添加监听数据
                dicParameterInfo.Add("Position Actual Value", null);//位置反馈
                dicParameterInfo.Add("Velocity Actual Value", null);//速度反馈
                dicParameterInfo.Add("Q Axis Actual Current", null);//电流反馈   

                dicParameterInfo.Add("Status Word", null);//状态字
                dicParameterInfo.Add("Alarm Set0", null);//错误码
                dicParameterInfo.Add("Alarm Set1", null);//错误码
                dicParameterInfo.Add("Alarm Set2", null);//错误码
                dicParameterInfo.Add("Alarm Set3", null);//错误码

                dicParameterInfo.Add("Motor Inertia Identification Online Start", null);//负载惯量比开关            
                dicParameterInfo.Add("Load Inertia Ratio", null);//负载惯量比

                //故障数据配置参数
                dicParameterInfo.Add("Alarm Cache chennal 1 setting", null);//故障数据配置参数1
                dicParameterInfo.Add("Alarm Cache chennal 2 setting", null);//故障数据配置参数2
                dicParameterInfo.Add("Alarm Cache chennal Time setting", null);//故障数据配置通道时间

                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo);
                if (iRet == RET.SUCCEEDED)
                {
                    ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo, ref lstTransmittingDataInfo_All, IsCheckAddressDeviation: true);
                }

                //添加监控数据
                if (!IsMonitoringPageHidden || MonitoringPageExpandState != "Hidden")
                {
                    ParameterReadWriteModel.GetIndexAndDataType(GlobalParameterSet.dt_Monitor, ref lstTransmittingDataInfo_Monitoring);
                    lstTransmittingDataInfo_Monitoring.ForEach(item => lstTransmittingDataInfo_All.Add(item));
                }

                //发送数据
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.IntervalRefresh, lstTransmittingDataInfo_All);
                #endregion

                #region 参数赋值
                //监听数据赋值
                foreach (ParameterReadWriteSet item in obsParameterInfo)
                {
                    string strValue = OthersHelper.GetCurrentValueOfIndex(item.Index);
                    if (string.IsNullOrEmpty(strValue))
                    {
                        continue;
                    }
                                     
                    if (item.Name == "Status Word" && OthersHelper.IsOutOfRange(ref strValue, "1", "Uint16", null, null))//状态字
                    {
                        //string a = HexHelper.GetStatusWord_HighEightBit(0, Convert.ToUInt16(strValue));
                        //string b = HexHelper.GetStatusWord(0, Convert.ToUInt16(strValue));

                        RefreshStatusWord(HexHelper.GetStatusWord(0, Convert.ToUInt16(strValue)));                                            
                    }
                    //else if (item.Name == "Alarm Set3")//错误码
                    //{
                    //    RefreshAlarmInfo();
                    //}
                    else if (item.Name == "Alarm Set3" || item.Name == "Alarm Set1" || item.Name == "Alarm Set2")//错误码
                    {
                        RefreshAlarmInfo();
                    }
                    else if (item.Name == "Motor Inertia Identification Online Start")//惯量比开关
                    {
                        if (strValue == "1")
                        {
                            IsInertiaRatioEnabled = true;
                        }
                        else
                        {
                            IsInertiaRatioEnabled = false;
                        }
                    }
                    else if (item.Name == "Load Inertia Ratio")//惯量比
                    {
                        //InertiaRatio = "负载惯量比：" + strValue + " %";
                        if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                        {
                            InertiaRatio = "负载惯量比：" + strValue + " %";
                        }
                        else
                        {
                            InertiaRatio = "InertiaRatio：" + strValue + " %";
                        }
                    }
                    else if (item.Name == "Position Actual Value" || item.Name == "Velocity Actual Value" || item.Name == "Q Axis Actual Current")//反馈值
                    {
                        RefreshFeedback(item.Name, strValue, item.Unit);
                    } 
                }

                //监控数据赋值
                ParameterMonitoring = new ObservableCollection<ParameterMonitoringSet>();     
                for (int i = 0; i < GlobalParameterSet.dt_Monitor.Rows.Count; i++)
                {
                    if (i == 0 || i == 1 || i == 10 || i == 11 || i == 12 || i == 13 || i == 14 || i == 15 || i == 16 || i == 17 || i == 18 || i == 19)
                    {
                        string strName = Convert.ToString(GlobalParameterSet.dt_Monitor.Rows[i]["Description"]);
                        string strValue = OthersHelper.GetCurrentValueOfIndex(Convert.ToString(GlobalParameterSet.dt_Monitor.Rows[i]["Index"]));                        
                        string strUnit = Convert.ToString(GlobalParameterSet.dt_Monitor.Rows[i]["Unit"]);

                        ParameterMonitoring.Add(new ParameterMonitoringSet() { ParameterName = strName, Value = strValue, Unit = strUnit });
                    }
                    else if (i == 2 || i == 3)         //由Lilbert添加监控数据单位随着单位的改变而改变     位置指令值和实际位置跟随误差
                    {
                        string strName = Convert.ToString(GlobalParameterSet.dt_Monitor.Rows[i]["Description"]);
                        //string strValue = OthersHelper.GetCurrentValueOfIndex(Convert.ToString(GlobalParameterSet.dt_Monitor.Rows[i]["Index"]));
                        string strValue = OthersHelper.ExchangeUnit("Position Actual Value", DefaultUnit.PositionUnit + "-" + SelectUnit.Position, OthersHelper.GetCurrentValueOfIndex(Convert.ToString(GlobalParameterSet.dt_Monitor.Rows[i]["Index"])));
                        string strUnit = Convert.ToString(SelectUnit.Position);

                        ParameterMonitoring.Add(new ParameterMonitoringSet() { ParameterName = strName, Value = strValue, Unit = strUnit });
                    }
                    else if (i == 4 || i == 5 || i == 6)       //由Lilbert添加监控数据单位随着单位的改变而改变    位置环的输出值、速度指令值和实际速度跟随误差
                    {
                        string strName = Convert.ToString(GlobalParameterSet.dt_Monitor.Rows[i]["Description"]);
                        //string strValue = OthersHelper.GetCurrentValueOfIndex(Convert.ToString(GlobalParameterSet.dt_Monitor.Rows[i]["Index"]));
                        string strValue = OthersHelper.ExchangeUnit("Velocity Actual Value", DefaultUnit.SpeedUnit + "-" + SelectUnit.Speed, OthersHelper.GetCurrentValueOfIndex(Convert.ToString(GlobalParameterSet.dt_Monitor.Rows[i]["Index"])));
                        string strUnit = Convert.ToString(SelectUnit.Speed);

                        ParameterMonitoring.Add(new ParameterMonitoringSet() { ParameterName = strName, Value = strValue, Unit = strUnit });
                    }
                    else if (i == 7 || i == 8 || i == 9)      //由Lilbert添加监控数据单位随着单位的改变而改变     速度环的输出值、转矩指令值和实际转矩指令值
                    {
                        string strName = Convert.ToString(GlobalParameterSet.dt_Monitor.Rows[i]["Description"]);
                        string strValue = OthersHelper.GetCurrentValueOfIndex(Convert.ToString(GlobalParameterSet.dt_Monitor.Rows[i]["Index"]));
                        string strUnit = "‰";

                        ParameterMonitoring.Add(new ParameterMonitoringSet() { ParameterName = strName, Value = strValue, Unit = strUnit });
                    }                    
                }
                #endregion               
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("Timer_System_Tick", ex);
            }
        }

        //*************************************************************************
        //函数名称：Timer_Info_Tick
        //函数功能：倒计时
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void Timer_Info_Tick(object sender, EventArgs e)
        {
            if (InfoDisplayCountDown < TimerPeriod.HintCountDown)
            {
                InfoDisplayCountDown++;

                Timer_Info.IsEnabled = true;

                IsHintInfoEnabled = true;             
            }
            else
            {
                InfoDisplayCountDown = 0;

                Timer_Info.IsEnabled = false;

                IsHintInfoEnabled = false;
            }
        }

        //*************************************************************************
        //函数名称：Timer_RestartSystem_Tick
        //函数功能：倒计时
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.03.18
        //*************************************************************************
        private void Timer_RestartSystem_Tick(object sender, EventArgs e)
        {
            if (RestartSystemCoutDown < TimerPeriod.HintCountDown)
            {
                RestartSystemCoutDown++;
            }
            else
            {
                RestartSystemCoutDown = 0;

                Timer_System.IsEnabled = true;
                Timer_RestartSystem.IsEnabled = false;
            }
        }
        #endregion       
    }

    public class SportMonitoringSet//运动监视
    {
        public string Content { get; set; }
        public string Value { get; set; }
    }   
    public class ParameterMonitoringSet//参数监视
    {
        public string ParameterName { get; set; }
        public string Value { get; set; }
        public string Unit { get; set; }
    }  
    public class DigitalInputSet //数字IO输入监视
    {
        public string Content { get; set; }
        public string Status { get; set; }
    }   
    public class DigitalOutputSet//数字IO输出监视
    {
        public string Content { get; set; }
        public string Status { get; set; }
    }

    //伺服报警集合
    public class SlideHardwareAlarmSet
    {
        public string Code { get; set; }//故障编号
        public string Content { get; set; }//故障内容
        public string Level { get; set; }//故障等级
        public string DateTime { get; set; }//故障时间
        public string Measure { get; set; }//解除故障措施
        public string GroupBoxState { get; set; }//GroupBox状态
    }
}