﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using ServoStudio.GlobalMethod;
using System.Collections.Generic;
using ServoStudio.GlobalConstant;
using System.Collections.ObjectModel;
using System.Data;
using ServoStudio.Models;
using System.Windows.Threading;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class SeekZeroViewModel
    {
        #region 私有字段
        private static bool IsInitialized = true;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        private DispatcherTimer Timer_System = new DispatcherTimer();//系统时间    
        #endregion

        #region 属性
        public virtual ObservableCollection<string> HomingMethod { get; set; }//回零方法
        public virtual string SelectedHomingMethod { get; set; }//选中的回零方法
        public virtual string FastHomingSpeed { get; set; }//搜索原点高速
        public virtual string SlowHomingSpeed { get; set; }//搜索原点低速
        public virtual string HomingAcceleration { get; set; }//回零加速度
        public virtual string HomeOffset { get; set; }//原点偏移量
        public virtual string HomingStatus { get; set; }//回零状态

        public virtual string PositionUnit { get; set; }//位置单位
        public virtual string SpeedUnit { get; set; }//速度单位
        public virtual string AccelerationUnit { get; set; }//加速单位        

        public virtual int Image1Visibility { get; set; }
        public virtual int Image2Visibility { get; set; }
        public virtual int Image3Visibility { get; set; }
        public virtual int Image4Visibility { get; set; }
        public virtual int Image5Visibility { get; set; }
        public virtual int Image6Visibility { get; set; }
        public virtual int Image7Visibility { get; set; }
        public virtual int Image8Visibility { get; set; }
        public virtual int Image9Visibility { get; set; }
        public virtual int Image10Visibility { get; set; }
        public virtual int Image11Visibility { get; set; }
        public virtual int Image12Visibility { get; set; }
        public virtual int Image13Visibility { get; set; }
        public virtual int Image14Visibility { get; set; }
        public virtual int Image17Visibility { get; set; }
        public virtual int Image18Visibility { get; set; }
        public virtual int Image19Visibility { get; set; }
        public virtual int Image20Visibility { get; set; }
        public virtual int Image21Visibility { get; set; }
        public virtual int Image22Visibility { get; set; }
        public virtual int Image23Visibility { get; set; }
        public virtual int Image24Visibility { get; set; }
        public virtual int Image25Visibility { get; set; }
        public virtual int Image26Visibility { get; set; }
        public virtual int Image27Visibility { get; set; }
        public virtual int Image28Visibility { get; set; }
        public virtual int Image29Visibility { get; set; }
        public virtual int Image30Visibility { get; set; }
        public virtual int Image33Visibility { get; set; }
        public virtual int Image34Visibility { get; set; }
        public virtual int HeaderVisibility { get; set; }
        #endregion

        #region 构造函数
        public SeekZeroViewModel()
        {
            ViewModelSet.SeekZero = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.SEEKZERO;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：SeekZeroLoaded
        //函数功能：SeekZero界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void SeekZeroLoaded()
        {
            int iRet = -1;

            try
            {
                //时钟
                Timer_System.Interval = TimeSpan.FromMilliseconds(TimerPeriod.Homing);
                Timer_System.Tick += Timer_System_Tick; ;

                //ComboBox初始化
                ComboBoxInitialize();

                //赋值
                if (IsInitialized == true)
                {                  
                    IsInitialized = false;

                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadSeekZeroParameter();
                    }
                    else
                    {
                        GetDefaultSeekZeroParameter();
                    }
                }
                else
                {
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadSeekZeroParameter();
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }                
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_LOADED, "SeekZeroLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：SeekZeroUnloaded
        //函数功能：退出
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.02
        //*************************************************************************
        public void SeekZeroUnloaded()
        {
            Timer_System.IsEnabled = false;
        }

        //*************************************************************************
        //函数名称：StartSeekZero
        //函数功能：回零开始
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void StartSeekZero()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                iRet = IsSelectedDIFunctionMatch();
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2014);
                    return;
                }

                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary(ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                //下达任务              
                iRet = OthersHelper.WriteControlWordSet(false, -1, TaskName.SeekZero);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1001);
                    return;
                }

                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.SEEKZERO, TaskName.SeekZero, lstTransmittingDataInfo);

                //时钟开启-反复读
                Timer_System.IsEnabled = true;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_WRITE_PARAMETER, "WriteSeekZeroParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：StopSeekZero
        //函数功能：回零结束
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void StopSeekZero()
        {
            int iRet = -1;
      
            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //写入控制字集合
                iRet = OthersHelper.WriteControlWordSet(true, 0, TaskName.StopSeekZero);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(1001);
                }

                //时钟开启-反复读取状态字
                Timer_System.IsEnabled = false;

                //状态清空
                HomingStatus = "无";
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_WRITE_PARAMETER, "WriteSeekZeroParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadSeekZeroParameter
        //函数功能：读回零参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void ReadSeekZeroParameter()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.SEEKZERO, TaskName.SeekZero, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_READ_PARAMETER, "ReadSeekZeroParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：SaveSeekZeroConfigFile
        //函数功能：保存回零配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void SaveSeekZeroConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                //选择配置文件存放位置
                iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.SeekZero);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //生成配置文件
                iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetSeekZeroConfigToDataTable(), ExcelType.SeekZero);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2002);
                }
                else
                {
                    ShowNotification(2003);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_SAVE_CONFIG_FILE, "SaveSeekZeroConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetSeekZeroConfigFile
        //函数功能：获取回零配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void GetSeekZeroConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            DataTable dt = null;

            try
            {
                //获取配置文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(strFilePath, ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //判断是否导入正确的配置文件
                if (dt.Columns[3].ColumnName != FileInterface.SEEKZERO)
                {
                    ShowNotification(2001);
                    return;
                }

                iRet = GetSeekZeroConfigFromDataTable(dt);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2007);
                }
                else
                {
                    ShowNotification(RET.ERROR);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_GET_CONFIG_FILE, "GetSeekZeroConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultSeekZeroParameter
        //函数功能：获取回零默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void GetDefaultSeekZeroParameter()
        {
            try
            {
                HomingStatus = "无";

                PositionUnit = DefaultUnit.PositionUnit;
                SpeedUnit = DefaultUnit.SpeedUnit;
                AccelerationUnit = DefaultUnit.AccelerationUnit;

                FastHomingSpeed = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Fast Homing Speed", "Default");
                SlowHomingSpeed = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Slow Homing Speed", "Default");
                HomingAcceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Homing Acceleration", "Default");
                HomeOffset = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Home Offset", "Default");

                switch (OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Homing Method", "Default"))//回零模式
                {
                    case "1": SelectedHomingMethod = "负限位下降沿Z信号回零"; break;
                    case "2": SelectedHomingMethod = "正限位下降沿Z信号回零"; break;
                    case "3": SelectedHomingMethod = "（回零开关位于正向）下降沿Z信号回零"; break;
                    case "4": SelectedHomingMethod = "（回零开关位于正向）上升沿Z信号回零"; break;
                    case "5": SelectedHomingMethod = "（回零开关位于负向）下降沿Z信号回零"; break;
                    case "6": SelectedHomingMethod = "（回零开关位于负向）上升沿Z信号回零"; break;
                    case "7": SelectedHomingMethod = "正限位、回零开关负边沿下降沿Z信号回零"; break;
                    case "8": SelectedHomingMethod = "正限位、回零开关负边沿上升沿Z信号回零"; break;
                    case "9": SelectedHomingMethod = "正限位、回零开关正边沿上升沿Z信号回零"; break;
                    case "10": SelectedHomingMethod = "正限位、回零开关正边沿下降沿Z信号回零"; break;
                    case "11": SelectedHomingMethod = "负限位、回零开关正边沿下降沿Z信号回零"; break;
                    case "12": SelectedHomingMethod = "负限位、回零开关正边沿上升沿Z信号回零"; break;
                    case "13": SelectedHomingMethod = "负限位、回零开关负边沿上升沿Z信号回零"; break;
                    case "14": SelectedHomingMethod = "负限位、回零开关负边沿下降沿Z信号回零"; break;
                    case "17": SelectedHomingMethod = "负限位下降沿回零"; break;
                    case "18": SelectedHomingMethod = "正限位下降沿回零"; break;
                    case "19": SelectedHomingMethod = "（回零开关位于正向）下降沿回零"; break;
                    case "20": SelectedHomingMethod = "（回零开关位于正向）上升沿回零"; break;
                    case "21": SelectedHomingMethod = "（回零开关位于负向）下降沿回零"; break;
                    case "22": SelectedHomingMethod = "（回零开关位于负向）上升沿回零"; break;
                    case "23": SelectedHomingMethod = "正限位、回零开关负边沿下降沿回零"; break;
                    case "24": SelectedHomingMethod = "正限位、回零开关负边沿上升沿回零"; break;
                    case "25": SelectedHomingMethod = "正限位、回零开关正边沿上升沿回零"; break;
                    case "26": SelectedHomingMethod = "正限位、回零开关正边沿下降沿回零"; break;
                    case "27": SelectedHomingMethod = "负限位、回零开关正边沿下降沿回零"; break;
                    case "28": SelectedHomingMethod = "负限位、回零开关正边沿上升沿回零"; break;
                    case "29": SelectedHomingMethod = "负限位、回零开关负边沿上升沿回零"; break;
                    case "30": SelectedHomingMethod = "负限位、回零开关负边沿下降沿回零"; break;
                    case "33": SelectedHomingMethod = "负向运行Z信号回零"; break;
                    case "34": SelectedHomingMethod = "正向运行Z信号回零"; break;
                    case "35": SelectedHomingMethod = "认定此位为零点"; break;
                    default: SelectedHomingMethod = "负限位下降沿Z信号回零"; break;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_GET_DEFAULT_PARAMETER, "GetDefaultSeekZeroParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationSeekZeroParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.16
        //*************************************************************************
        public void EvaluationSeekZeroParameter()
        {
            SpeedUnit = CurrentUnit.Speed;
            AccelerationUnit = CurrentUnit.Acceleration;
            PositionUnit = CurrentUnit.Position;

            switch (OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Homing Method", "Index")))//回零模式
            {
                case "1": SelectedHomingMethod = "负限位下降沿Z信号回零"; break;
                case "2": SelectedHomingMethod = "正限位下降沿Z信号回零"; break;
                case "3": SelectedHomingMethod = "（回零开关位于正向）下降沿Z信号回零"; break;
                case "4": SelectedHomingMethod = "（回零开关位于正向）上升沿Z信号回零"; break;
                case "5": SelectedHomingMethod = "（回零开关位于负向）下降沿Z信号回零"; break;
                case "6": SelectedHomingMethod = "（回零开关位于负向）上升沿Z信号回零"; break;
                case "7": SelectedHomingMethod = "正限位、回零开关负边沿下降沿Z信号回零"; break;
                case "8": SelectedHomingMethod = "正限位、回零开关负边沿上升沿Z信号回零"; break;
                case "9": SelectedHomingMethod = "正限位、回零开关正边沿上升沿Z信号回零"; break;
                case "10": SelectedHomingMethod = "正限位、回零开关正边沿下降沿Z信号回零"; break;
                case "11": SelectedHomingMethod = "负限位、回零开关正边沿下降沿Z信号回零"; break;
                case "12": SelectedHomingMethod = "负限位、回零开关正边沿上升沿Z信号回零"; break;
                case "13": SelectedHomingMethod = "负限位、回零开关负边沿上升沿Z信号回零"; break;
                case "14": SelectedHomingMethod = "负限位、回零开关负边沿下降沿Z信号回零"; break;
                case "17": SelectedHomingMethod = "负限位下降沿回零"; break;
                case "18": SelectedHomingMethod = "正限位下降沿回零"; break;
                case "19": SelectedHomingMethod = "（回零开关位于正向）下降沿回零"; break;
                case "20": SelectedHomingMethod = "（回零开关位于正向）上升沿回零"; break;
                case "21": SelectedHomingMethod = "（回零开关位于负向）下降沿回零"; break;
                case "22": SelectedHomingMethod = "（回零开关位于负向）上升沿回零"; break;
                case "23": SelectedHomingMethod = "正限位、回零开关负边沿下降沿回零"; break;
                case "24": SelectedHomingMethod = "正限位、回零开关负边沿上升沿回零"; break;
                case "25": SelectedHomingMethod = "正限位、回零开关正边沿上升沿回零"; break;
                case "26": SelectedHomingMethod = "正限位、回零开关正边沿下降沿回零"; break;
                case "27": SelectedHomingMethod = "负限位、回零开关正边沿下降沿回零"; break;
                case "28": SelectedHomingMethod = "负限位、回零开关正边沿上升沿回零"; break;
                case "29": SelectedHomingMethod = "负限位、回零开关负边沿上升沿回零"; break;
                case "30": SelectedHomingMethod = "负限位、回零开关负边沿下降沿回零"; break;
                case "33": SelectedHomingMethod = "负向运行Z信号回零"; break;
                case "34": SelectedHomingMethod = "正向运行Z信号回零"; break;
                case "35": SelectedHomingMethod = "认定此位为零点"; break;
                default: SelectedHomingMethod = "负限位下降沿Z信号回零"; break;
            }

            FastHomingSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Fast Homing Speed", "Index"));//搜索原点高速
            SlowHomingSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Slow Homing Speed", "Index"));//搜索原点低速
            HomingAcceleration = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Homing Acceleration", "Index"));//回零加速度
            HomeOffset = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Home Offset", "Index"));//原点偏移量
            HomingStatus = "无";
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void OnFastHomingSpeedChanged() { GlobalCurrentInput.FastHomingSpeed = FastHomingSpeed; }//搜索原点高速
        public void OnSlowHomingSpeedChanged() { GlobalCurrentInput.SlowHomingSpeed = SlowHomingSpeed; }//搜索原点低速
        public void OnHomingAccelerationChanged() { GlobalCurrentInput.HomingAcceleration = HomingAcceleration; }//回零加速度
        public void OnHomeOffsetChanged() { GlobalCurrentInput.HomeOffset = HomeOffset; }//原点偏移量
        public void OnPositionUnitChanged() { GlobalCurrentInput.PositionUnit = PositionUnit; }//位置单位
        public void OnSpeedUnitChanged() { GlobalCurrentInput.SpeedUnit = SpeedUnit; }//速度单位
        public void OnAccelerationUnitChanged() { GlobalCurrentInput.AccelerationUnit = AccelerationUnit; }//加速度单位
        public void OnSelectedHomingMethodChanged()//原点复归方法
        {
            GlobalCurrentInput.SelectedHomingMethod = SelectedHomingMethod;

            #region Visibility设置
            HeaderVisibility = ControlVisibility.Visible;
            if (SelectedHomingMethod == "负限位下降沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Visible;               
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "正限位下降沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Visible;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "[回零开关位于正向]下降沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Visible;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "[回零开关位于正向]上升沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Visible;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "[回零开关位于负向]下降沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Visible;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "[回零开关位于负向]上升沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Visible;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "正限位、回零开关负边沿下降沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Visible;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "正限位、回零开关负边沿上升沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Visible;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "正限位、回零开关正边沿上升沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Visible;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "正限位、回零开关正边沿下降沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Visible;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "负限位、回零开关正边沿下降沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Visible;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "负限位、回零开关正边沿上升沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Visible;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "负限位、回零开关负边沿上升沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Visible;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "负限位、回零开关负边沿下降沿Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Visible;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "负限位下降沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Visible;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "正限位下降沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Visible;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "[回零开关位于正向]下降沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Visible;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "[回零开关位于正向]上升沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Visible;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "[回零开关位于负向]下降沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Visible;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "[回零开关位于负向]上升沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Visible;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "正限位、回零开关负边沿下降沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Visible;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "正限位、回零开关负边沿上升沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Visible;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "正限位、回零开关正边沿上升沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Visible;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "正限位、回零开关正边沿下降沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Visible;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "负限位、回零开关正边沿下降沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Visible;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "负限位、回零开关正边沿上升沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Visible;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "负限位、回零开关负边沿上升沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Visible;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "负限位、回零开关负边沿下降沿回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Visible;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "负向运行Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Visible;
                Image34Visibility = ControlVisibility.Collapsed;
            }
            else if (SelectedHomingMethod == "正向运行Z信号回零")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Visible;
            }
            else if (SelectedHomingMethod == "认定此位为零点")
            {
                Image1Visibility = ControlVisibility.Collapsed;
                Image2Visibility = ControlVisibility.Collapsed;
                Image3Visibility = ControlVisibility.Collapsed;
                Image4Visibility = ControlVisibility.Collapsed;
                Image5Visibility = ControlVisibility.Collapsed;
                Image6Visibility = ControlVisibility.Collapsed;
                Image7Visibility = ControlVisibility.Collapsed;
                Image8Visibility = ControlVisibility.Collapsed;
                Image9Visibility = ControlVisibility.Collapsed;
                Image10Visibility = ControlVisibility.Collapsed;
                Image11Visibility = ControlVisibility.Collapsed;
                Image12Visibility = ControlVisibility.Collapsed;
                Image13Visibility = ControlVisibility.Collapsed;
                Image14Visibility = ControlVisibility.Collapsed;
                Image17Visibility = ControlVisibility.Collapsed;
                Image18Visibility = ControlVisibility.Collapsed;
                Image19Visibility = ControlVisibility.Collapsed;
                Image20Visibility = ControlVisibility.Collapsed;
                Image21Visibility = ControlVisibility.Collapsed;
                Image22Visibility = ControlVisibility.Collapsed;
                Image23Visibility = ControlVisibility.Collapsed;
                Image24Visibility = ControlVisibility.Collapsed;
                Image25Visibility = ControlVisibility.Collapsed;
                Image26Visibility = ControlVisibility.Collapsed;
                Image27Visibility = ControlVisibility.Collapsed;
                Image28Visibility = ControlVisibility.Collapsed;
                Image29Visibility = ControlVisibility.Collapsed;
                Image30Visibility = ControlVisibility.Collapsed;
                Image33Visibility = ControlVisibility.Collapsed;
                Image34Visibility = ControlVisibility.Collapsed;

                HeaderVisibility = ControlVisibility.Collapsed;
            }
            #endregion
        }
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：ComboBox初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.26
        //*************************************************************************
        private void ComboBoxInitialize()
        {
            HomingMethod = new ObservableCollection<string>() {
                "负限位下降沿Z信号回零",
                "正限位下降沿Z信号回零",
                "[回零开关位于正向]下降沿Z信号回零",
                "[回零开关位于正向]上升沿Z信号回零",
                "[回零开关位于负向]下降沿Z信号回零",
                "[回零开关位于负向]上升沿Z信号回零",
                "正限位、回零开关负边沿下降沿Z信号回零",
                "正限位、回零开关负边沿上升沿Z信号回零",
                "正限位、回零开关正边沿上升沿Z信号回零",
                "正限位、回零开关正边沿下降沿Z信号回零",
                "负限位、回零开关正边沿下降沿Z信号回零",
                "负限位、回零开关正边沿上升沿Z信号回零",
                "负限位、回零开关负边沿上升沿Z信号回零",
                "负限位、回零开关负边沿下降沿Z信号回零",
                "负限位下降沿回零",
                "正限位下降沿回零",
                "[回零开关位于正向]下降沿回零",
                "[回零开关位于正向]上升沿回零",
                "[回零开关位于负向]下降沿回零",
                "[回零开关位于负向]上升沿回零",
                "正限位、回零开关负边沿下降沿回零",
                "正限位、回零开关负边沿上升沿回零",
                "正限位、回零开关正边沿上升沿回零",
                "正限位、回零开关正边沿下降沿回零",
                "负限位、回零开关正边沿下降沿回零",
                "负限位、回零开关正边沿上升沿回零",
                "负限位、回零开关负边沿上升沿回零",
                "负限位、回零开关负边沿下降沿回零",
                "负向运行Z信号回零",
                "正向运行Z信号回零",
                "认定此位为零点"
            };
        }

        //*************************************************************************
        //函数名称：GetSeekZeroConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private DataTable GetSeekZeroConfigToDataTable()
        {
            DataTable dt = new DataTable();

            try
            {
                OthersHelper.MakeValueToDataTable(FileInterface.SEEKZERO, "Fast Homing Speed", "回零快速度", FastHomingSpeed, SpeedUnit,ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SEEKZERO, "Slow Homing Speed", "回零慢速度", SlowHomingSpeed, SpeedUnit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SEEKZERO, "Homing Acceleration", "回零加速度", HomingAcceleration, AccelerationUnit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SEEKZERO, "Home Offset", "回零偏置", HomeOffset, PositionUnit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.SEEKZERO, "Homing Method", "回零方法", SelectedHomingMethod, "",ref dt);
     
                return dt;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_GET_CONFIG_TO_DATATABLE, "GetSeekZeroConfigToDataTable", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetSeekZeroConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int GetSeekZeroConfigFromDataTable(DataTable dt)
        {
            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                PositionUnit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Home Offset", "Unit");
                SpeedUnit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Fast Homing Speed", "Unit");
                AccelerationUnit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Homing Acceleration", "Unit");

                FastHomingSpeed = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Fast Homing Speed", "Default");
                SlowHomingSpeed = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Slow Homing Speed", "Default");
                HomingAcceleration = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Homing Acceleration", "Default");
                HomeOffset = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Home Offset", "Default");
                SelectedHomingMethod = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Homing Method", "Default");
                HomingStatus = "无";

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_GET_CONFIG_FROM_DATATABLE, "GetSeekZeroConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                HomingStatus = "无";
                FastHomingSpeed = GlobalCurrentInput.FastHomingSpeed;//搜索原点高速
                SlowHomingSpeed = GlobalCurrentInput.SlowHomingSpeed;//搜索原点低速
                HomingAcceleration = GlobalCurrentInput.HomingAcceleration;//回零加速度
                HomeOffset = GlobalCurrentInput.HomeOffset;//原点偏移量
                PositionUnit = GlobalCurrentInput.PositionUnit;
                SpeedUnit = GlobalCurrentInput.SpeedUnit;
                AccelerationUnit = GlobalCurrentInput.AccelerationUnit;
                SelectedHomingMethod = GlobalCurrentInput.SelectedHomingMethod;//原点复归方法
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int AddParameterInfoDictionary(ref Dictionary<string, string> dicParameterInfo)
        {
            string strValue = null;
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                dicParameterInfo.Add("Fast Homing Speed", OthersHelper.ExchangeUnit("Fast Homing Speed", CurrentUnit.Speed + "-" + DefaultUnit.SpeedUnit, FastHomingSpeed));
                dicParameterInfo.Add("Slow Homing Speed", OthersHelper.ExchangeUnit("Slow Homing Speed", CurrentUnit.Speed + "-" + DefaultUnit.SpeedUnit, SlowHomingSpeed));
                dicParameterInfo.Add("Homing Acceleration", OthersHelper.ExchangeUnit("Homing Acceleration", CurrentUnit.Acceleration + "-" + DefaultUnit.AccelerationUnit, HomingAcceleration));
                dicParameterInfo.Add("Home Offset", OthersHelper.ExchangeUnit("Home Offset", CurrentUnit.Position + "-" + DefaultUnit.PositionUnit, HomeOffset));

                switch (SelectedHomingMethod)
                {
                    case "负限位下降沿Z信号回零": strValue = "1"; break;
                    case "正限位下降沿Z信号回零": strValue = "2"; break;
                    case "[回零开关位于正向]下降沿Z信号回零": strValue = "3"; break;
                    case "[回零开关位于正向]上升沿Z信号回零": strValue = "4"; break;
                    case "[回零开关位于负向]下降沿Z信号回零": strValue = "5"; break;
                    case "[回零开关位于负向]上升沿Z信号回零": strValue = "6"; break;
                    case "正限位、回零开关负边沿下降沿Z信号回零": strValue = "7"; break;
                    case "正限位、回零开关负边沿上升沿Z信号回零": strValue = "8"; break;
                    case "正限位、回零开关正边沿上升沿Z信号回零": strValue = "9"; break;
                    case "正限位、回零开关正边沿下降沿Z信号回零": strValue = "10"; break;
                    case "负限位、回零开关正边沿下降沿Z信号回零": strValue = "11"; break;
                    case "负限位、回零开关正边沿上升沿Z信号回零": strValue = "12"; break;
                    case "负限位、回零开关负边沿上升沿Z信号回零": strValue = "13"; break;
                    case "负限位、回零开关负边沿下降沿Z信号回零": strValue = "14"; break;
                    case "负限位下降沿回零": strValue = "17"; break;
                    case "正限位下降沿回零": strValue = "18"; break;
                    case "[回零开关位于正向]下降沿回零": strValue = "19"; break;
                    case "[回零开关位于正向]上升沿回零": strValue = "20"; break;
                    case "[回零开关位于负向]下降沿回零": strValue = "21"; break;
                    case "[回零开关位于负向]上升沿回零": strValue = "22"; break;
                    case "正限位、回零开关负边沿下降沿回零": strValue = "23"; break;
                    case "正限位、回零开关负边沿上升沿回零": strValue = "24"; break;
                    case "正限位、回零开关正边沿上升沿回零": strValue = "25"; break;
                    case "正限位、回零开关正边沿下降沿回零": strValue = "26"; break;
                    case "负限位、回零开关正边沿下降沿回零": strValue = "27"; break;
                    case "负限位、回零开关正边沿上升沿回零": strValue = "28"; break;
                    case "负限位、回零开关负边沿上升沿回零": strValue = "29"; break;
                    case "负限位、回零开关负边沿下降沿回零": strValue = "30"; break;
                    case "负向运行Z信号回零": strValue = "33"; break;
                    case "正向运行Z信号回零": strValue = "34"; break;
                    case "认定此位为零点": strValue = "35"; break;
                    default: break;
                }

                dicParameterInfo.Add("Homing Method", strValue);
                dicParameterInfo.Add("Modes Of Operation", "6");

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }
   
        //*************************************************************************
        //函数名称：Timer_System_Tick
        //函数功能：时钟函数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.30
        //*************************************************************************
        private void Timer_System_Tick(object sender, EventArgs e)
        {
            int iRet = -1;
            string strValue = null;
            Dictionary<string, string> dicStatusWord = new Dictionary<string, string>();
            ObservableCollection<ParameterReadWriteSet> obsStatusWord = new ObservableCollection<ParameterReadWriteSet>();

            try
            {
                //获取参数详细信息
                dicStatusWord.Add("Status Word", null);                
                iRet = OthersHelper.GetParameterForRead(dicStatusWord, ref obsStatusWord);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取Status Word值
                strValue = OthersHelper.GetCurrentValueOfIndex(obsStatusWord[0].Index);
                if (OthersHelper.IsOutOfRange(ref strValue, "1", "Uint16", null, null))
                {
                    switch (HexHelper.GetStatusWord(1, Convert.ToUInt16(strValue)))
                    {
                        case "000":
                            HomingStatus = "正在回零";
                            break;
                        case "001":
                            HomingStatus = "回零被打断或未启动回零";
                            break;
                        case "010":
                            HomingStatus = "找到参考点，但是未到达目标位置点";
                            break;
                        case "011":
                            HomingStatus = "回零完成";
                            break;
                        case "100":
                            HomingStatus = "回零发生错误，速度不为零";
                            break;
                        case "101":
                            HomingStatus = "回零错误，速度为零";
                            break;
                        default:
                            break;
                    }
                }
                else
                {
                    HomingStatus = "无";
                }
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("Timer_System_Tick", ex);
            }                       
        }

        //*************************************************************************
        //函数名称：IsSelectedDIFunctionMatch
        //函数功能：是否满足DI功能
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.07
        //*************************************************************************
        private int IsSelectedDIFunctionMatch()
        {
            try
            {
                if (SoftwareStateParameterSet.SelectedDIFunction == null)
                {
                    return RET.ERROR;
                }

                if (SoftwareStateParameterSet.SelectedDIFunction.Count == 0)
                {
                    return RET.NO_EFFECT;
                }

                switch (SelectedHomingMethod)
                {
                    case "负限位下降沿Z信号回零":
                    case "负限位下降沿回零":
                        if (SoftwareStateParameterSet.SelectedDIFunction.Exists(item => item == "负限位"))
                        {
                            return RET.SUCCEEDED;
                        }
                        else
                        {
                            return RET.NO_EFFECT;
                        }
                    case "正限位下降沿Z信号回零":
                    case "正限位下降沿回零":
                        if (SoftwareStateParameterSet.SelectedDIFunction.Exists(item => item == "正限位"))
                        {
                            return RET.SUCCEEDED;
                        }
                        else
                        {
                            return RET.NO_EFFECT;
                        }
                    case "[回零开关位于正向]下降沿Z信号回零":
                    case "[回零开关位于正向]上升沿Z信号回零": 
                    case "[回零开关位于负向]下降沿Z信号回零": 
                    case "[回零开关位于负向]上升沿Z信号回零":
                    case "[回零开关位于正向]下降沿回零": 
                    case "[回零开关位于正向]上升沿回零": 
                    case "[回零开关位于负向]下降沿回零": 
                    case "[回零开关位于负向]上升沿回零":
                        if (SoftwareStateParameterSet.SelectedDIFunction.Exists(item => item == "回零开关"))
                        {
                            return RET.SUCCEEDED;
                        }
                        else
                        {
                            return RET.NO_EFFECT;
                        }                   
                    case "正限位、回零开关负边沿下降沿Z信号回零":
                    case "正限位、回零开关负边沿上升沿Z信号回零": 
                    case "正限位、回零开关正边沿上升沿Z信号回零": 
                    case "正限位、回零开关正边沿下降沿Z信号回零":
                    case "正限位、回零开关负边沿下降沿回零": 
                    case "正限位、回零开关负边沿上升沿回零": 
                    case "正限位、回零开关正边沿上升沿回零": 
                    case "正限位、回零开关正边沿下降沿回零":
                        if (SoftwareStateParameterSet.SelectedDIFunction.Exists(item => item == "正限位"))
                        {
                            return RET.SUCCEEDED;
                        }
                        else if (SoftwareStateParameterSet.SelectedDIFunction.Exists(item => item == "回零开关"))
                        {
                            return RET.SUCCEEDED;
                        }
                        else
                        {
                            return RET.NO_EFFECT;
                        }
                    case "负限位、回零开关正边沿下降沿Z信号回零": 
                    case "负限位、回零开关正边沿上升沿Z信号回零": 
                    case "负限位、回零开关负边沿上升沿Z信号回零": 
                    case "负限位、回零开关负边沿下降沿Z信号回零":
                    case "负限位、回零开关正边沿下降沿回零": 
                    case "负限位、回零开关正边沿上升沿回零":
                    case "负限位、回零开关负边沿上升沿回零": 
                    case "负限位、回零开关负边沿下降沿回零":
                        if (SoftwareStateParameterSet.SelectedDIFunction.Exists(item => item == "负限位"))
                        {
                            return RET.SUCCEEDED;
                        }
                        else if (SoftwareStateParameterSet.SelectedDIFunction.Exists(item => item == "回零开关"))
                        {
                            return RET.SUCCEEDED;
                        }
                        else
                        {
                            return RET.NO_EFFECT;
                        }                                                
                    case "负向运行Z信号回零": break;
                    case "正向运行Z信号回零": break;
                    case "认定此位为零点": break;
                    default: break;
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.SEEKZERO_IS_SELECTED_DI_FUNCTION_MATCH, "IsSelectedDIFunctionMatch", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }
}