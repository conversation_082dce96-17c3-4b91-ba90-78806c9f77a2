﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using ServoStudio.Models;
using ServoStudio.GlobalMethod;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class SplashScreenViewModel
    {
        #region 属性
        public virtual string CompanyDepartment { get; set; }//公司及部门 
        #endregion
        public SplashScreenViewModel()
        {
            //ViewModelSet.SwitchAxis = this;
            ViewModelSet.SplashScreen = this;

            OthersHelper.GetCompanyInfoConfigInfo();

            CompanyDepartment = GlobalCompanyInfo.CompanyDepartment;
        }    

    }
}