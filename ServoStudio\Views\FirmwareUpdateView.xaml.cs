﻿using ServoStudio.GlobalMethod;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace ServoStudio.Views
{
    /// <summary>
    /// Interaction logic for FirmwareUpdateView.xaml
    /// </summary>
    public partial class FirmwareUpdateView : UserControl
    {
        public FirmwareUpdateView()
        {
            InitializeComponent();
            WindowSet.clsFirmwareUpdate = this;
        }

        //*************************************************************************
        //函数名称：TxtProgress_TextChanged
        //函数功能：TextBox滚动展示最新信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.24
        //*************************************************************************
        private void TxtProgress_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                TxtProgress.ScrollToLine(TxtProgress.LineCount - 1);
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("TxtProgress_TextChanged", ex);
            }
        }

        //*************************************************************************
        //函数名称：ShowNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.24
        //*************************************************************************
        public void ShowNotification(string strInfo)
        {            
            try
            {
                this.TxtProgress.Dispatcher.BeginInvoke(new Action(() =>
                {
                    TxtProgress.Text = strInfo;
                }
                ));
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("ShowNotification_ForFirmwareUpdate", ex);
            }
        }

        //*************************************************************************
        //函数名称：ProgressValue
        //函数功能：进度条
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.29
        //*************************************************************************
        public void ProgressValue(double dValue)
        {
            try
            {
                this.ProgressBar.Dispatcher.BeginInvoke(new Action(() =>
                {
                    ProgressBar.Value = dValue;
                }
                ));
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("ProgressValue", ex);
            }
        }
    }
}
