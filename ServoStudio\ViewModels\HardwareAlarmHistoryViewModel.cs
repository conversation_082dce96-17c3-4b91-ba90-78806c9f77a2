﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Windows.Threading;
using System.Collections.Generic;
using System.Linq;
using ServoStudio.Models;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class HardwareAlarmHistoryViewModel
    {
        #region 属性
        public virtual ObservableCollection<HardwareAlarmSet> HardwareAlarmHistory { get; set; }//报警集合   
        #endregion

        #region 构造函数
        public HardwareAlarmHistoryViewModel()
        {
            ViewModelSet.HardwareAlarmHistory = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.HARDWAREALARMHISTORY;
        }
        #endregion

        #region 方法
        //*************************************************************************
        //函数名称：HardwareAlarmHistoryLoaded
        //函数功能：载入
        //
        //输入参数：
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.11
        //*************************************************************************
        public void HardwareAlarmHistoryLoaded()
        {
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_HardwareAlarm();
        }

        //*************************************************************************
        //函数名称：EvaluationHardwareAllAlarm
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.11
        //*************************************************************************
        public void EvaluationHardwareAlarmHistory()
        {
            HardwareAlarmHistory = new ObservableCollection<HardwareAlarmSet>();

            foreach (var item in CommunicationSet.HardwareAlarmValue)
            {               
                if (string.IsNullOrEmpty(item.Index) || item.DateTime == 0)
                {
                    continue;
                }

                HardwareAlarmSet hardwareAlarmSet = new HardwareAlarmSet();
                AlarmInfoSet clsAlarmInfoSet = GlobalParameterSet.lstHardwareAlarm.Where(o => o.Code == "0x" + item.Index).FirstOrDefault<AlarmInfoSet>();
                if (clsAlarmInfoSet != null)
                {
                    hardwareAlarmSet.Content = clsAlarmInfoSet.Content;
                    hardwareAlarmSet.Code = clsAlarmInfoSet.Code.Replace("0x", "");
                    hardwareAlarmSet.Measure = clsAlarmInfoSet.Measure;
                    hardwareAlarmSet.Reason = clsAlarmInfoSet.Reason;
                    hardwareAlarmSet.DateTime = new DateTime(1970, 1, 1).AddSeconds(item.DateTime).ToString();
                    switch (clsAlarmInfoSet.Level)
                    {
                        case "1":
                            hardwareAlarmSet.Level = "低级";
                            break;
                        case "2":
                            hardwareAlarmSet.Level = "中级";
                            break;
                        case "3":
                            hardwareAlarmSet.Level = "高级";
                            break;
                        default:
                            break;
                    }
                }
                else
                {
                    hardwareAlarmSet.Content = "报警信息未知";
                    hardwareAlarmSet.Level = "高级";
                    hardwareAlarmSet.Code = item.Index;
                    hardwareAlarmSet.DateTime = new DateTime(1970, 1, 1).AddSeconds(item.DateTime).ToString();
                }

                HardwareAlarmHistory.Add(hardwareAlarmSet);
            }
      
            if (HardwareAlarmHistory.Count == 0)
            {
                ShowNotification(3018);
            }   
            else if (HardwareAlarmHistory.Count == 10)
            {
                ShowNotification(3019);
            }              
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            WindowSet.clsMainWindow.ShowNotification(In_iType);
        }
        #endregion
    }

    //伺服报警集合
    public class HardwareAlarmSet
    {
        public string Code { get; set; }//故障编号
        public string Content { get; set; }//故障内容
        public string Level { get; set; }//故障等级
        public string DateTime { get; set; }//故障时间
        public string Reason { get; set; }//故障原因
        public string Measure { get; set; }//解除故障措施
    }
}