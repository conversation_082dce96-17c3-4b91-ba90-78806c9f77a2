<?xml version="1.0"?>
<doc>
    <assembly>
        <name>GalaSoft.MvvmLight.Platform</name>
    </assembly>
    <members>
        <member name="T:GalaSoft.MvvmLight.Command.EventToCommand">
            <summary>
            This <see cref="T:System.Windows.Interactivity.TriggerAction`1" /> can be
            used to bind any event on any FrameworkElement to an <see cref="T:System.Windows.Input.ICommand" />.
            Typically, this element is used in XAML to connect the attached element
            to a command located in a ViewModel. This trigger can only be attached
            to a FrameworkElement or a class deriving from FrameworkElement.
            <para>To access the EventArgs of the fired event, use a RelayCommand&lt;EventArgs&gt;
            and leave the CommandParameter and CommandParameterValue empty!</para>
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Command.EventToCommand.CommandParameterProperty">
            <summary>
            Identifies the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.CommandParameter" /> dependency property
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Command.EventToCommand.CommandProperty">
            <summary>
            Identifies the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.Command" /> dependency property
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Command.EventToCommand.MustToggleIsEnabledProperty">
            <summary>
            Identifies the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.MustToggleIsEnabled" /> dependency property
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Command.EventToCommand.Command">
            <summary>
            Gets or sets the ICommand that this trigger is bound to. This
            is a DependencyProperty.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Command.EventToCommand.CommandParameter">
            <summary>
            Gets or sets an object that will be passed to the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.Command" />
            attached to this trigger. This is a DependencyProperty.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Command.EventToCommand.CommandParameterValue">
            <summary>
            Gets or sets an object that will be passed to the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.Command" />
            attached to this trigger. This property is here for compatibility
            with the Silverlight version. This is NOT a DependencyProperty.
            For databinding, use the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.CommandParameter" /> property.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Command.EventToCommand.MustToggleIsEnabled">
            <summary>
            Gets or sets a value indicating whether the attached element must be
            disabled when the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.Command" /> property's CanExecuteChanged
            event fires. If this property is true, and the command's CanExecute 
            method returns false, the element will be disabled. If this property
            is false, the element will not be disabled when the command's
            CanExecute method changes. This is a DependencyProperty.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Command.EventToCommand.MustToggleIsEnabledValue">
            <summary>
            Gets or sets a value indicating whether the attached element must be
            disabled when the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.Command" /> property's CanExecuteChanged
            event fires. If this property is true, and the command's CanExecute 
            method returns false, the element will be disabled. This property is here for
            compatibility with the Silverlight version. This is NOT a DependencyProperty.
            For databinding, use the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.MustToggleIsEnabled" /> property.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.EventToCommand.OnAttached">
            <summary>
            Called when this trigger is attached to a FrameworkElement.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.EventToCommand.GetCommand">
            <summary>
            This method is here for compatibility
            with the Silverlight 3 version.
            </summary>
            <returns>The command that must be executed when
            this trigger is invoked.</returns>
        </member>
        <member name="P:GalaSoft.MvvmLight.Command.EventToCommand.PassEventArgsToCommand">
            <summary>
            Specifies whether the EventArgs of the event that triggered this
            action should be passed to the bound RelayCommand. If this is true,
            the command should accept arguments of the corresponding
            type (for example RelayCommand&lt;MouseButtonEventArgs&gt;).
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Command.EventToCommand.EventArgsConverter">
            <summary>
            Gets or sets a converter used to convert the EventArgs when using
            <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.PassEventArgsToCommand"/>. If PassEventArgsToCommand is false,
            this property is never used.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Command.EventToCommand.EventArgsConverterParameterPropertyName">
            <summary>
            The <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.EventArgsConverterParameter" /> dependency property's name.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Command.EventToCommand.EventArgsConverterParameter">
            <summary>
            Gets or sets a parameters for the converter used to convert the EventArgs when using
            <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.PassEventArgsToCommand"/>. If PassEventArgsToCommand is false,
            this property is never used. This is a dependency property.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Command.EventToCommand.EventArgsConverterParameterProperty">
            <summary>
            Identifies the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.EventArgsConverterParameter" /> dependency property.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Command.EventToCommand.AlwaysInvokeCommandPropertyName">
            <summary>
            The <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.AlwaysInvokeCommand" /> dependency property's name.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Command.EventToCommand.AlwaysInvokeCommand">
            <summary>
            Gets or sets a value indicating if the command should be invoked even
            if the attached control is disabled. This is a dependency property.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Command.EventToCommand.AlwaysInvokeCommandProperty">
            <summary>
            Identifies the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.AlwaysInvokeCommand" /> dependency property.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.EventToCommand.Invoke">
            <summary>
            Provides a simple way to invoke this trigger programatically
            without any EventArgs.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.EventToCommand.Invoke(System.Object)">
            <summary>
            Executes the trigger.
            <para>To access the EventArgs of the fired event, use a RelayCommand&lt;EventArgs&gt;
            and leave the CommandParameter and CommandParameterValue empty!</para>
            </summary>
            <param name="parameter">The EventArgs of the fired event.</param>
        </member>
        <member name="T:GalaSoft.MvvmLight.Command.IEventArgsConverter">
            <summary>
            The definition of the converter used to convert an EventArgs
            in the <see cref="T:GalaSoft.MvvmLight.Command.EventToCommand"/> class, if the
            <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.PassEventArgsToCommand"/> property is true.
            Set an instance of this class to the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.EventArgsConverter"/>
            property of the EventToCommand instance.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.IEventArgsConverter.Convert(System.Object,System.Object)">
            <summary>
            The method used to convert the EventArgs instance.
            </summary>
            <param name="value">An instance of EventArgs passed by the
            event that the EventToCommand instance is handling.</param>
            <param name="parameter">An optional parameter used for the conversion. Use
            the <see cref="P:GalaSoft.MvvmLight.Command.EventToCommand.EventArgsConverterParameter"/> property
            to set this value. This may be null.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="T:GalaSoft.MvvmLight.Threading.DispatcherHelper">
            <summary>
            Helper class for dispatcher operations on the UI thread.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.UIDispatcher">
            <summary>
            Gets a reference to the UI thread's dispatcher, after the
            <see cref="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.Initialize" /> method has been called on the UI thread.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.CheckBeginInvokeOnUI(System.Action)">
            <summary>
            Executes an action on the UI thread. If this method is called
            from the UI thread, the action is executed immendiately. If the
            method is called from another thread, the action will be enqueued
            on the UI thread's dispatcher and executed asynchronously.
            <para>For additional operations on the UI thread, you can get a
            reference to the UI thread's dispatcher thanks to the property
            <see cref="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.UIDispatcher" /></para>.
            </summary>
            <param name="action">The action that will be executed on the UI
            thread.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.RunAsync(System.Action)">
            <summary>
            Invokes an action asynchronously on the UI thread.
            </summary>
            <param name="action">The action that must be executed.</param>
            <returns>An object, which is returned immediately after BeginInvoke is called, that can be used to interact
             with the delegate as it is pending execution in the event queue.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.Initialize">
            <summary>
            This method should be called once on the UI thread to ensure that
            the <see cref="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.UIDispatcher" /> property is initialized.
            <para>In a Silverlight application, call this method in the
            Application_Startup event handler, after the MainPage is constructed.</para>
            <para>In WPF, call this method on the static App() constructor.</para>
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.Reset">
            <summary>
            Resets the class by deleting the <see cref="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.UIDispatcher"/>
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Views.NavigationService">
            <summary>
            Windows Phone Silverlight implementation of <see cref="T:GalaSoft.MvvmLight.Views.INavigationService"/>.
            This implementation can be used in Windows Phone applications (not Xamarin Forms).
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Views.NavigationService.RootPageKey">
            <summary>
            The key that is returned by the <see cref="P:GalaSoft.MvvmLight.Views.NavigationService.CurrentPageKey"/> property
            when the current Page is the root page.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Views.NavigationService.ParameterKeyName">
            <summary>
            Use this key name to retrieve the navigation parameter.
            </summary>
        </member>
        <member name="E:GalaSoft.MvvmLight.Views.NavigationService.Navigated">
            <summary>
            Occurs when a page navigation has happened.
            </summary>
        </member>
        <member name="E:GalaSoft.MvvmLight.Views.NavigationService.Navigating">
            <summary>
            Occurs when a page navigation is going to happen.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Views.NavigationService.CurrentPageKey">
            <summary>
            The key corresponding to the currently displayed page.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.GoBack">
            <summary>
            If possible, discards the current page and displays the previous page
            on the navigation stack.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String)">
            <summary>
            Displays a new page corresponding to the given key.
            Make sure to call the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Uri)"/> method first.
            </summary>
            <param name="pageKey">The key corresponding to the page
            that should be displayed.</param>
            <exception cref="T:System.ArgumentException">When this method is called for 
            a key that has not been configured earlier.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String,System.Object)">
            <summary>
            Displays a new page corresponding to the given key,
            and passes a parameter to the new page.
            Make sure to call the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Uri)"/> method first.
            </summary>
            <param name="pageKey">The key corresponding to the page
            that should be displayed.</param>
            <param name="parameter">The parameter that should be passed
            to the new page.</param>
            <exception cref="T:System.ArgumentException">When this method is called for 
            a key that has not been configured earlier.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Uri)">
            <summary>
            Adds a key/page pair to the navigation service.
            </summary>
            <param name="key">The key that will be used later
            in the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String)"/> or <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String,System.Object)"/> methods.</param>
            <param name="targetUri">The URI of the page corresponding to the key.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.GetAndRemoveParameter(System.Windows.Navigation.NavigationContext)">
            <summary>
            Allows a caller to get the navigation parameter corresponding 
            to the NavigationContext parameter.
            </summary>
            <param name="context">The <see cref="T:System.Windows.Navigation.NavigationContext"/> 
            of the navigated page.</param>
            <returns>The navigation parameter. If no parameter is found,
            returns null.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.GetAndRemoveParameter``1(System.Windows.Navigation.NavigationContext)">
            <summary>
            Allows a caller to get the navigation parameter corresponding 
            to the NavigationContext parameter.
            </summary>
            <typeparam name="T">The type of the retrieved parameter.</typeparam>
            <param name="context">The <see cref="T:System.Windows.Navigation.NavigationContext"/> 
            of the navigated page.</param>
            <returns>The navigation parameter casted to the proper type.
            If no parameter is found, returns default(T).</returns>
        </member>
        <member name="T:GalaSoft.MvvmLight.Views.DialogService">
            <summary>
            An implementation of <see cref="T:GalaSoft.MvvmLight.Views.IDialogService"/> allowing
            to display simple dialogs to the user. Note that this class
            uses the built in Windows Phone dialogs which may or may not
            be sufficient for your needs. Using this class is easy
            but feel free to develop your own IDialogService implementation
            if needed.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowError(System.String,System.String,System.String,System.Action)">
            <summary>
            Displays information about an error.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
            <remarks>Displaying dialogs in Windows Phone is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowError(System.Exception,System.String,System.String,System.Action)">
            <summary>
            Displays information about an error.
            </summary>
            <param name="error">The exception of which the message must be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
            <remarks>Displaying dialogs in Windows Phone is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessage(System.String,System.String)">
            <summary>
            Displays information to the user. The dialog box will have only
            one button with the text "OK".
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
            <remarks>Displaying dialogs in Windows Phone is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessage(System.String,System.String,System.String,System.Action)">
            <summary>
            Displays information to the user. The dialog box will have only
            one button.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
            <remarks>Displaying dialogs in Windows Phone is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessage(System.String,System.String,System.String,System.String,System.Action{System.Boolean})">
            <summary>
            Displays information to the user. The dialog box will have only
            one button.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonConfirmText">The text shown in the "confirm" button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="buttonCancelText">The text shown in the "cancel" button
            in the dialog box. If left null, the text "Cancel" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user. The callback method will get a boolean
            parameter indicating if the "confirm" button (true) or the "cancel" button
            (false) was pressed by the user.</param>
            <returns>A Task allowing this async method to be awaited. The task will return
            true or false depending on the dialog result.</returns>
            <remarks>Displaying dialogs in Windows Phone is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessageBox(System.String,System.String)">
            <summary>
            Displays information to the user in a simple dialog box. The dialog box will have only
            one button with the text "OK". This method should be used for debugging purposes.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
            <remarks>Displaying dialogs in Windows Phone is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
    </members>
</doc>
