﻿<UserControl x:Class="ServoStudio.Views.SpeedLoopView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:Views="clr-namespace:ServoStudio.Views"
             xmlns:converter="clr-namespace:Converter"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:SpeedLoopViewModel}"
             d:DesignHeight="800" d:DesignWidth="1300">
    <UserControl.Resources>
        <converter:BackgroundConverter x:Key="BackgroudConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>
        <converter:ContentConverter x:Key="ContentConverter"/>
    </UserControl.Resources>

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding SpeedLoopLoadedCommand}"/>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields" UseWin8NotificationsIfAvailable="True"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="315"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Label Grid.Row="0" Margin="3" Style="{StaticResource LabelStyle}" Content="速度环图示 — 点击图例可设定相应参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Canvas Grid.Row="1">
                <Label Content="速度指令" Canvas.Left="9" Canvas.Top="154"/>
                <Line X2="72" Canvas.Top="148" Canvas.Left="40" Style="{StaticResource Line}"/>
                <Line Canvas.Top="148" Canvas.Left="104" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="148" Canvas.Left="104" Style="{StaticResource RDArrow}"/>
                <Ellipse Canvas.Top="143" Canvas.Left="28" Style="{StaticResource StartDot}"/>

                <Line Y2="65" Canvas.Top="82" Canvas.Left="84" Style="{StaticResource VerticalLine}"/>
                <Line X2="82" Canvas.Top="82" Canvas.Left="84" Style="{StaticResource Line}"/>
                <Line Canvas.Top="82" Canvas.Left="159" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="82" Canvas.Left="159" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="169" Canvas.Top="64" Content="d/dt" Style="{StaticResource Function}" />

                <Line Canvas.Top="82" Canvas.Left="230" Style="{StaticResource Line}"/>
                <Line Canvas.Top="82" Canvas.Left="258" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="82" Canvas.Left="258" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="268" Canvas.Top="64" Name="lableA"  Content="平滑常数" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=lableA}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Canvas.Top="82" Canvas.Left="344" Style="{StaticResource Line}"/>
                <Line Canvas.Top="82" Canvas.Left="372" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="82" Canvas.Left="372" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="381" Canvas.Top="64" Name="lableB" Content="前馈增益" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=lableB}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line X2="20" Canvas.Top="82" Canvas.Left="457" Style="{StaticResource Line}" />
                <Line Y2="20" Canvas.Top="62" Canvas.Left="477" Style="{StaticResource VerticalLine}"/>
                <Line X2="20" Canvas.Top="62" Canvas.Left="477" Style="{StaticResource Line}"/>
                <Line Canvas.Top="62" Canvas.Left="489" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="62" Canvas.Left="489" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="498" Canvas.Top="31" Name="lableC" Content="前馈选择" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=lableC}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Y2="20" Canvas.Top="16" Canvas.Left="477" Style="{StaticResource VerticalLine}"/>
                <Line X2="20" Canvas.Top="36" Canvas.Left="477" Style="{StaticResource Line}"/>
                <Line Canvas.Top="36" Canvas.Left="489" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="36" Canvas.Left="489" Style="{StaticResource RDArrow}"/>

                <Line X2="437" Canvas.Top="16" Canvas.Left="40" Style="{StaticResource Line}"/>
                <Ellipse Canvas.Top="11" Canvas.Left="28" Style="{StaticResource StartDot}"/>
                <Label Content="转矩前馈" Canvas.Left="10" Canvas.Top="22"/>

                <Line Canvas.Top="148" Canvas.Left="372" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="148" Canvas.Left="372" Style="{StaticResource RDArrow}"/>
                <Line X2="228" Canvas.Top="148" Canvas.Left="151" Style="{StaticResource Line}"/>
                <Label Canvas.Left="381" Canvas.Top="131" Name="lableD" Content="速度环增益" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=lableD}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Canvas.Top="49" Canvas.Left="686" Style="{StaticResource Line}"/>
                <Line Y2="79" Canvas.Top="49" Canvas.Left="721" Style="{StaticResource VerticalLine}"/>
                <Line Canvas.Top="130" Canvas.Left="721" Style="{StaticResource DLArrow}"/>
                <Line Canvas.Top="130" Canvas.Left="721" Style="{StaticResource DRArrow}"/>

                <Line Y2="65" Canvas.Top="148" Canvas.Left="189" Style="{StaticResource VerticalLine}" StrokeStartLineCap="Flat"/>
                <Line X2="58" Canvas.Top="213" Canvas.Left="189" Style="{StaticResource Line}"/>
                <Line Canvas.Top="213" Canvas.Left="240" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="213" Canvas.Left="240" Style="{StaticResource RDArrow}"/>
                <Label Content="∫" FontSize="12pt"  Style="{StaticResource Function}" Canvas.Left="249" Canvas.Top="196"/>

                <Line X2="68" Canvas.Top="213" Canvas.Left="311" Style="{StaticResource Line}"/>
                <Line Canvas.Top="213" Canvas.Left="372" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="213" Canvas.Left="372" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="381" Canvas.Top="196" Name="lableE" Content="速度环积分" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=lableE}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Y2="46" Canvas.Top="167" Canvas.Left="721" Style="{StaticResource VerticalLine}"/>
                <Line Canvas.Top="175" Canvas.Left="721" Style="{StaticResource ULArrow}"/>
                <Line Canvas.Top="175" Canvas.Left="721" Style="{StaticResource URArrow}"/>
                <Line X2="264" Canvas.Top="213" Canvas.Left="457" Style="{StaticResource Line}" StrokeEndLineCap="Flat"/>

                <Line Canvas.Top="148" Canvas.Left="695" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="148" Canvas.Left="695" Style="{StaticResource RDArrow}"/>
                <Line X2="248" Canvas.Top="148" Canvas.Left="457" Style="{StaticResource Line}"/>

                <Line Canvas.Top="148" Canvas.Left="739" Style="{StaticResource Line}"/>
                <Line Canvas.Top="148" Canvas.Left="879" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="148" Canvas.Left="767" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="776" Canvas.Top="130" Name="lableF" Content="负载惯量比" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=lableF}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Canvas.Top="148" Canvas.Left="852" Style="{StaticResource Line}"/>
                <Ellipse Canvas.Top="143" Canvas.Left="888" Style="{StaticResource EndDot}"/>
                <Label Content="转矩指令" Canvas.Left="870" Canvas.Top="154"/>

                <Ellipse Canvas.Top="275" Canvas.Left="567" Style="{StaticResource StartDot}" />
                <Label Content="速度反馈" Canvas.Left="549" Canvas.Top="254"/>

                <Line X2="50" Canvas.Top="281" Canvas.Left="516" Style="{StaticResource Line}"/>
                <Line Canvas.Top="281" Canvas.Left="514" Style="{StaticResource LUArrow}"/>
                <Line Canvas.Top="281" Canvas.Left="514" Style="{StaticResource LDArrow}"/>
                <Label Canvas.Left="438" Canvas.Top="264" Name="lableG" Content="平均值滤波" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=lableG}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line X2="50" Canvas.Top="281" Canvas.Left="387" Style="{StaticResource Line}"/>
                <Line Canvas.Top="281" Canvas.Left="387" Style="{StaticResource LUArrow}"/>
                <Line Canvas.Top="281" Canvas.Left="387" Style="{StaticResource LDArrow}"/>
                <Label Canvas.Left="311" Canvas.Top="264" Name="lableH" Content="速度观测器" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=lableH}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line X2="50" Canvas.Top="281" Canvas.Left="260" Style="{StaticResource Line}"/>
                <Line Canvas.Top="281" Canvas.Left="258" Style="{StaticResource LUArrow}"/>
                <Line Canvas.Top="281" Canvas.Left="258" Style="{StaticResource LDArrow}"/>
                <Label Canvas.Left="182" Canvas.Top="264" Name="lableI" Content="低通滤波器" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=lableI}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Y2="114" Canvas.Top="167" Canvas.Left="131" Style="{StaticResource VerticalLine}"/>
                <Line Canvas.Top="175" Canvas.Left="131" Style="{StaticResource ULArrow}"/>
                <Line Canvas.Top="175" Canvas.Left="131" Style="{StaticResource URArrow}"/>
                <Line X2="50" Canvas.Top="281" Canvas.Left="131" Style="{StaticResource Line}" />

                <Ellipse Canvas.Top="131" Canvas.Left="114" Style="{StaticResource OperatorProfile}"/>
                <Label Content="+" Canvas.Left="90" Canvas.Top="141" Style="{StaticResource Operator}"/>
                <Label Content="-" Canvas.Left="122" Canvas.Top="165" Style="{StaticResource Operator}"/>
                <Label Content="Σ" Canvas.Left="127" Canvas.Top="135" Style="{StaticResource Operator}"/>

                <Ellipse Canvas.Top="131" Canvas.Left="704" Style="{StaticResource OperatorProfile}"/>
                <Label Content="Σ" Canvas.Left="717" Canvas.Top="135" Style="{StaticResource Operator}"/>
                <Label Content="+" Canvas.Left="683" Canvas.Top="142" Style="{StaticResource Operator}"/>
                <Label Content="+" Canvas.Left="708" Canvas.Top="168" Style="{StaticResource Operator}"/>
                <Label Content="+" Canvas.Left="708" Canvas.Top="95" Style="{StaticResource Operator}"/>
                <Line Canvas.Top="148" Canvas.Left="767" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="148" Canvas.Left="879" Style="{StaticResource RDArrow}"/>
                <Line Canvas.Top="49" Canvas.Left="574" Style="{StaticResource Line}"/>
                <Line Canvas.Top="49" Canvas.Left="602" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="49" Canvas.Left="602" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="611" Canvas.Top="31" x:Name="lableJ" Content="前馈平均滤波" FontSize="10.667" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content, ElementName=lableJ}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>
            </Canvas>

            <Label Grid.Row="2" Margin="3" Style="{StaticResource LabelStyle}" Content="速度环参数设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Grid Grid.Row="3">
                <Grid.RowDefinitions>
                    <RowDefinition Height="9"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="170"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="40"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="170"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="170"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Label Grid.Row="1" Grid.Column="1" Margin="10,2" Content="速度环增益" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding SpeedLoopGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding SpeedLoopGainBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="1" Grid.Column="3" Text="0.1Hz" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding SpeedLoopGainBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="1" Grid.Column="5" Margin="10,2" Content="速度环积分时间常数" Style="{StaticResource LabelStyle}" />
                <TextBox Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding SpeedLoopTimeConstant,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding SpeedLoopTimeConstantBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="1" Grid.Column="7" Text="0.01ms" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding SpeedLoopTimeConstantBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="1" Grid.Column="9" Margin="10,2" Content="负载惯量比" Style="{StaticResource LabelStyle}" />
                <TextBox Grid.Row="1" Grid.Column="10" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding LoadInertiaRatio,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding LoadInertiaRatioBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="2" Grid.Column="1" Margin="10,2" Content="转矩前馈选择" Style="{StaticResource LabelStyle}" />
                <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource SettingComboBoxStyle}" Width="Auto" ItemsSource="{Binding TrqffControlSelect}" SelectedIndex="{Binding SelectedTrqffControlSelectIndex, Mode=TwoWay}" Background="{Binding TrqffControlSelectBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="2" Grid.Column="5" Margin="10,2" Content="转矩前馈滤波时间常数" Style="{StaticResource LabelStyle}"  />
                <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding TrqffFilterFimeConstant,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding TrqffFilterFimeConstantBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="2" Grid.Column="7" Text="0.01ms" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding TrqffFilterFimeConstantBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="2" Grid.Column="9" Margin="10,2" Content="转矩前馈增益" Style="{StaticResource LabelStyle}"  />
                <TextBox Grid.Row="2" Grid.Column="10" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding TrqffGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding TrqffGainBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="3" Grid.Column="1" Margin="10,2" Content="速度反馈平均滤波" Style="{StaticResource LabelStyle}"/>
                <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource SettingComboBoxStyle}" Width="Auto" ItemsSource="{Binding SpeedAverageFilterConfig}" SelectedIndex="{Binding SelectedSpeedAverageFilterConfigIndex, Mode=TwoWay}"  Background="{Binding SpeedAverageFilterConfigBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="3" Grid.Column="5" Margin="10,2" Content="速度观测增益" Style="{StaticResource LabelStyle}" />
                <TextBox Grid.Row="3" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding SpeedObserverGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding SpeedObserverGainBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="3" Grid.Column="7" Text="Hz" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding SpeedObserverGainBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="3" Grid.Column="9" Margin="10,2" Content="速度观测补偿增益" Style="{StaticResource LabelStyle}" />
                <TextBox Grid.Row="3" Grid.Column="10" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding SpeedObserverPosCompensationGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding SpeedObserverPosCompensationGainBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="4" Grid.Column="1" Margin="10,2" Content="低通滤波时间参数" Style="{StaticResource LabelStyle}" />
                <TextBox Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding SpeedFeedbackLPFTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding SpeedFeedbackLPFTimeBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="4" Grid.Column="3" Text="0.01ms" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding SpeedFeedbackLPFTimeBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="4" Grid.Column="5" Margin="10,2" Content="转矩前馈平均滤波时间" Style="{StaticResource LabelStyle}"  />
                <TextBox Grid.Row="4" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,2" Style="{StaticResource TextBoxStyle}" Text="{Binding TorqueFeedForwardMaFilterTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding TorqueFeedForwardMaFilterTimeBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="4" Grid.Column="7" Text="0.1ms" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding TorqueFeedForwardMaFilterTimeBackground,Converter={StaticResource BackgroudConverter}}"/>

            </Grid>

            <Label Grid.Row="4" Margin="3,3,3,7" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <StackPanel Grid.Row="5" HorizontalAlignment="Right" Orientation="Horizontal">
                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadSpeedLoopParameterCommand}">
                    <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultSpeedLoopParameterCommand}">
                    <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" Command="{Binding WriteSpeedLoopParameterCommand}">
                    <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>
            </StackPanel>

            <!--<Grid Grid.Row="7">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="1" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetSpeedLoopConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="2" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" />

            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="3" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SaveSpeedLoopConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="4" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}"/>
        </Grid>-->
        </Grid>

    </ScrollViewer>
   
</UserControl>
