﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using DevExpress.Mvvm.POCO;
using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using ServoStudio.Models;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Threading;
using System.Threading;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class ParameterReadWriteViewModel
    {      
        #region 字段
        private ObservableCollection<ParameterReadWriteSet> obsParameter_CIA402 = new ObservableCollection<ParameterReadWriteSet>();//CIA402
        private ObservableCollection<ParameterReadWriteSet> obsParameter_Common = new ObservableCollection<ParameterReadWriteSet>();//轴共同参数
        private ObservableCollection<ParameterReadWriteSet> obsParameter_Motor = new ObservableCollection<ParameterReadWriteSet>();//电机参数
        private ObservableCollection<ParameterReadWriteSet> obsParameter_Basic = new ObservableCollection<ParameterReadWriteSet>();//基本配置参数
        private ObservableCollection<ParameterReadWriteSet> obsParameter_Control = new ObservableCollection<ParameterReadWriteSet>();//运控参数
        private ObservableCollection<ParameterReadWriteSet> obsParameter_Advanced = new ObservableCollection<ParameterReadWriteSet>();//高级配置参数
        private ObservableCollection<ParameterReadWriteSet> obsParameter_DI = new ObservableCollection<ParameterReadWriteSet>();//端子输入参数
        private ObservableCollection<ParameterReadWriteSet> obsParameter_DO = new ObservableCollection<ParameterReadWriteSet>();//端子输出参数
        private ObservableCollection<ParameterReadWriteSet> obsParameter_FaultAndProtection = new ObservableCollection<ParameterReadWriteSet>();//故障与保护参数
        private ObservableCollection<ParameterReadWriteSet> obsParameter_Auxiliary = new ObservableCollection<ParameterReadWriteSet>();//辅助参数
        #endregion

        #region 属性
        public virtual ObservableCollection<ParameterReadWriteSet> Parameter_CIA402 { get; set;} //CIA402
        public virtual ObservableCollection<ParameterReadWriteSet> Parameter_Common { get; set;} //轴共同参数
        public virtual ObservableCollection<ParameterReadWriteSet> Parameter_Motor { get; set;} //电机参数
        public virtual ObservableCollection<ParameterReadWriteSet> Parameter_Basic { get; set;} //基本配置参数
        public virtual ObservableCollection<ParameterReadWriteSet> Parameter_Control { get; set;} //运控参数
        public virtual ObservableCollection<ParameterReadWriteSet> Parameter_Advanced { get; set;} //高级配置参数
        public virtual ObservableCollection<ParameterReadWriteSet> Parameter_FaultAndProtection { get; set;} //故障与保护
        public virtual ObservableCollection<ParameterReadWriteSet> Parameter_Auxiliary { get; set;} //辅助参数
        public virtual ObservableCollection<ParameterReadWriteSet> Parameter_DI { get; set;} //端子输入参数
        public virtual ObservableCollection<ParameterReadWriteSet> Parameter_DO { get; set;} //端子输出参数
        #endregion

        #region 构造函数
        public ParameterReadWriteViewModel() { ViewModelSet.ParameterReadWrite = this; }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：ParameterReadWriteLoaded
        //函数功能：载入
        //
        //输入参数：
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.05
        //*************************************************************************
        public void ParameterReadWriteLoaded()
        {
            int iRet = -1;

            try
            {             
                #region 事件注册
                if (ViewModelSet.CommunicationSet != null)
                {
                    ViewModelSet.CommunicationSet.evtEvaluationParamterReadAndWrite += EvaluationItemValue;
                }
                #endregion

                #region 获取参数集合
                #region CIA402
                Parameter_CIA402 = new ObservableCollection<ParameterReadWriteSet>();
                iRet = ParameterReadWriteModel.RetrieveDataTable(TaskName.CIA402, ref obsParameter_CIA402);
                if (iRet == RET.SUCCEEDED)
                {
                    foreach (var i in obsParameter_CIA402)
                    {
                        Parameter_CIA402.Add(new ParameterReadWriteSet()
                        {
                            Classification = TaskName.CIA402,
                            Index = i.Index,
                            Name = i.Name,
                            DataType = i.DataType,
                            Description = i.Description,
                            CurrentValue = "0",
                            Min = i.Min,
                            Max = i.Max,
                            Default = i.Default,
                            Unit = i.Unit,
                            RWProperty = i.RWProperty,
                            Comment = i.Comment,
                            IsDownload = ControlVisibility.Collapsed
                        });
                    }
                }
                #endregion

                #region 轴共同参数
                Parameter_Common = new ObservableCollection<ParameterReadWriteSet>();
                iRet = ParameterReadWriteModel.RetrieveDataTable(TaskName.Common, ref obsParameter_Common);
                if (iRet == RET.SUCCEEDED)
                {
                    foreach (var i in obsParameter_Common)
                    {
                        Parameter_Common.Add(new ParameterReadWriteSet()
                        {
                            Classification = TaskName.Common,
                            Index = i.Index,
                            Name = i.Name,
                            DataType = i.DataType,
                            Description = i.Description,
                            CurrentValue = "0",
                            Min = i.Min,
                            Max = i.Max,
                            Default = i.Default,
                            Unit = i.Unit,
                            RWProperty = i.RWProperty,
                            Comment = i.Comment,
                            IsDownload = ControlVisibility.Collapsed
                        });
                    }
                }
                #endregion

                #region 电机参数
                Parameter_Motor = new ObservableCollection<ParameterReadWriteSet>();
                iRet = ParameterReadWriteModel.RetrieveDataTable(TaskName.Motor, ref obsParameter_Motor);
                if (iRet == RET.SUCCEEDED)
                {
                    foreach (var i in obsParameter_Motor)
                    {
                        Parameter_Motor.Add(new ParameterReadWriteSet()
                        {
                            Classification = TaskName.Motor,
                            Index = i.Index,
                            Name = i.Name,
                            DataType = i.DataType,
                            Description = i.Description,
                            CurrentValue = "0",
                            Min = i.Min,
                            Max = i.Max,
                            Default = i.Default,
                            Unit = i.Unit,
                            RWProperty = i.RWProperty,
                            Comment = i.Comment,
                            IsDownload = ControlVisibility.Collapsed
                        });
                    }
                }
                #endregion

                #region 基本配置参数
                Parameter_Basic = new ObservableCollection<ParameterReadWriteSet>();
                iRet = ParameterReadWriteModel.RetrieveDataTable(TaskName.Basic, ref obsParameter_Basic);
                if (iRet == RET.SUCCEEDED)
                {
                    foreach (var i in obsParameter_Basic)
                    {
                        Parameter_Basic.Add(new ParameterReadWriteSet()
                        {
                            Classification = TaskName.Basic,
                            Index = i.Index,
                            Name = i.Name,
                            DataType = i.DataType,
                            Description = i.Description,
                            CurrentValue = "0",
                            Min = i.Min,
                            Max = i.Max,
                            Default = i.Default,
                            Unit = i.Unit,
                            RWProperty = i.RWProperty,
                            Comment = i.Comment,
                            IsDownload = ControlVisibility.Collapsed
                        });
                    }
                }
                #endregion

                #region 运控参数
                Parameter_Control = new ObservableCollection<ParameterReadWriteSet>();
                iRet = ParameterReadWriteModel.RetrieveDataTable(TaskName.Control, ref obsParameter_Control);
                if (iRet == RET.SUCCEEDED)
                {
                    foreach (var i in obsParameter_Control)
                    {
                        Parameter_Control.Add(new ParameterReadWriteSet()
                        {
                            Classification = TaskName.Control,
                            Index = i.Index,
                            Name = i.Name,
                            DataType = i.DataType,
                            Description = i.Description,
                            CurrentValue = "0",
                            Min = i.Min,
                            Max = i.Max,
                            Default = i.Default,
                            Unit = i.Unit,
                            RWProperty = i.RWProperty,
                            Comment = i.Comment,
                            IsDownload = ControlVisibility.Collapsed
                        });
                    }
                }
                #endregion

                #region 高级配置参数
                Parameter_Advanced = new ObservableCollection<ParameterReadWriteSet>();
                iRet = ParameterReadWriteModel.RetrieveDataTable(TaskName.Advanced, ref obsParameter_Advanced);
                if (iRet == RET.SUCCEEDED)
                {
                    foreach (var i in obsParameter_Advanced)
                    {
                        Parameter_Advanced.Add(new ParameterReadWriteSet()
                        {
                            Classification = TaskName.Advanced,
                            Index = i.Index,
                            Name = i.Name,
                            DataType = i.DataType,
                            Description = i.Description,
                            CurrentValue = "0",
                            Min = i.Min,
                            Max = i.Max,
                            Default = i.Default,
                            Unit = i.Unit,
                            RWProperty = i.RWProperty,
                            Comment = i.Comment,
                            IsDownload = ControlVisibility.Collapsed
                        });
                    }
                }
                #endregion

                #region 端子输入参数
                Parameter_DI = new ObservableCollection<ParameterReadWriteSet>();
                iRet = ParameterReadWriteModel.RetrieveDataTable(TaskName.DI, ref obsParameter_DI);
                if (iRet == RET.SUCCEEDED)
                {
                    foreach (var i in obsParameter_DI)
                    {
                        Parameter_DI.Add(new ParameterReadWriteSet()
                        {
                            Classification = TaskName.DI,
                            Index = i.Index,
                            Name = i.Name,
                            DataType = i.DataType,
                            Description = i.Description,
                            CurrentValue = "0",
                            Min = i.Min,
                            Max = i.Max,
                            Default = i.Default,
                            Unit = i.Unit,
                            RWProperty = i.RWProperty,
                            Comment = i.Comment,
                            IsDownload = ControlVisibility.Collapsed
                        });
                    }
                }
                #endregion

                #region 端子输出参数
                Parameter_DO = new ObservableCollection<ParameterReadWriteSet>();
                iRet = ParameterReadWriteModel.RetrieveDataTable(TaskName.DO, ref obsParameter_DO);
                if (iRet == RET.SUCCEEDED)
                {
                    foreach (var i in obsParameter_DO)
                    {
                        Parameter_DO.Add(new ParameterReadWriteSet()
                        {
                            Classification = TaskName.DO,
                            Index = i.Index,
                            Name = i.Name,
                            DataType = i.DataType,
                            Description = i.Description,
                            CurrentValue = "0",
                            Min = i.Min,
                            Max = i.Max,
                            Default = i.Default,
                            Unit = i.Unit,
                            RWProperty = i.RWProperty,
                            Comment = i.Comment,
                            IsDownload = ControlVisibility.Collapsed
                        });
                    }
                }
                #endregion

                #region 故障与保护参数
                Parameter_FaultAndProtection = new ObservableCollection<ParameterReadWriteSet>();
                iRet = ParameterReadWriteModel.RetrieveDataTable(TaskName.FaultAndProtection, ref obsParameter_FaultAndProtection);
                if (iRet == RET.SUCCEEDED)
                {
                    foreach (var i in obsParameter_FaultAndProtection)
                    {
                        Parameter_FaultAndProtection.Add(new ParameterReadWriteSet()
                        {
                            Classification = TaskName.FaultAndProtection,
                            Index = i.Index,
                            Name = i.Name,
                            DataType = i.DataType,
                            Description = i.Description,
                            CurrentValue = "0",
                            Min = i.Min,
                            Max = i.Max,
                            Default = i.Default,
                            Unit = i.Unit,
                            RWProperty = i.RWProperty,
                            Comment = i.Comment,
                            IsDownload = ControlVisibility.Collapsed
                        });
                    }
                }
                #endregion

                #region 辅助参数
                Parameter_Auxiliary = new ObservableCollection<ParameterReadWriteSet>();
                iRet = ParameterReadWriteModel.RetrieveDataTable(TaskName.Auxiliary, ref obsParameter_Auxiliary);
                if (iRet == RET.SUCCEEDED)
                {
                    foreach (var i in obsParameter_Auxiliary)
                    {
                        Parameter_Auxiliary.Add(new ParameterReadWriteSet()
                        {
                            Classification = TaskName.Auxiliary,
                            Index = i.Index,
                            Name = i.Name,
                            DataType = i.DataType,
                            Description = i.Description,
                            CurrentValue = "0",
                            Min = i.Min,
                            Max = i.Max,
                            Default = i.Default,
                            Unit = i.Unit,
                            RWProperty = i.RWProperty,
                            Comment = i.Comment,
                            IsDownload = ControlVisibility.Collapsed
                        });
                    }
                }
                #endregion
                #endregion

                #region 读取电机参数
                ParameterRead(SoftwareStateParameterSet.CurrentPageName);
                #endregion
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_LOADED, "ParameterReadWriteLoaded", ex);
            }
        }
  
        //*************************************************************************
        //函数名称：ParameterWrite
        //函数功能：参数写入
        //
        //输入参数：string In_strIndex   索引号（地址）
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.05
        //*************************************************************************
        //public void ParameterWrite(ParameterReadWriteSet In_clsTemp)
        //{
        //    bool bRet = false;
        //    string strCurrentValue = null;
        //    List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();
        //    List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();

        //    try
        //    {
        //        if (In_clsTemp == null)
        //        {
        //            ShowNotification(RET.ERROR);
        //            return;
        //        }

        //        if (In_clsTemp.RWProperty == "RO")
        //        {
        //            ShowNotification(2009);
        //            return;
        //        }

        //        if (In_clsTemp.Name == "Timestamp")
        //        {
        //            ShowNotification(2019);
        //            return;
        //        }

        //        //获取当前页面名称
        //        SoftwareStateParameterSet.CurrentPageName = In_clsTemp.Classification;

        //        //判断输入数据是否为16进制数
        //        if (OthersHelper.CheckIsInputHex(In_clsTemp.CurrentValue))
        //        {
        //            In_clsTemp.CurrentValue = OthersHelper.TransferHexToDecimal(In_clsTemp.CurrentValue, In_clsTemp.DataType);
        //        }

        //        //判断输入数据是否为10进制数
        //        if (!OthersHelper.IsInputInteger(In_clsTemp.CurrentValue))
        //        {
        //            ShowNotification(2008);
        //            return;
        //        }

        //        //判断输入数据是否在数据类型范围内
        //        strCurrentValue = In_clsTemp.CurrentValue;
        //        bRet = OthersHelper.IsOutOfRange(ref strCurrentValue, In_clsTemp.Unit, In_clsTemp.DataType, In_clsTemp.Max, In_clsTemp.Min);
        //        if (bRet)
        //        {
        //            In_clsTemp.CurrentValue = strCurrentValue;
        //        }
        //        else
        //        {
        //            ShowNotification(In_clsTemp.Name, In_clsTemp.Description, In_clsTemp.Min, In_clsTemp.Max, In_clsTemp.Unit);
        //            return;
        //        }

        //        //获取地址与数据类型
        //        lstParameterInfo.Add(In_clsTemp);
        //        ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

        //        //下达任务
        //        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(SoftwareStateParameterSet.CurrentPageName, In_clsTemp.Description, lstTransmittingDataInfo);

        //        //更改下载图标的状态为可见
        //        ChangeDownloadImageVisibility(In_clsTemp, ControlVisibility.Visible);
        //    }
        //    catch (System.Exception ex)
        //    {
        //        SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_PARAMETER_WRITE, "ParameterWrite", ex);
        //    }
        //}

        //*************************************************************************
        //函数名称：ParameterWrite
        //函数功能：参数写入
        //
        //输入参数：string In_strIndex   索引号（地址）
        //
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.12.05&2023.11.17
        //*************************************************************************
        public void ParameterWrite(ParameterReadWriteSet In_clsTemp)
        {
            bool bRet = false;
            string strCurrentValue = null;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();

            try
            {
                if (In_clsTemp == null)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (In_clsTemp.RWProperty == "RO")
                {
                    ShowNotification(2009);
                    return;
                }

                if (In_clsTemp.Name == "Timestamp")
                {
                    ShowNotification(2019);
                    return;
                }

                //获取当前页面名称
                SoftwareStateParameterSet.CurrentPageName = In_clsTemp.Classification;

                if ((OthersHelper.GetCurrentValueOfIndex("0x200201") == "1") && (SoftwareStateParameterSet.CurrentPageName == TaskName.CIA402))
                {
                    //判断输入数据是否为16进制数
                    if (OthersHelper.CheckIsInputHex(In_clsTemp.CurrentValue))
                    {
                        In_clsTemp.CurrentValue = OthersHelper.TransferHexToDecimal(In_clsTemp.CurrentValue, In_clsTemp.DataType);
                    }

                    //判断输入数据是否为10进制数
                    if (!OthersHelper.IsInputInteger(In_clsTemp.CurrentValue))
                    {
                        ShowNotification(2008);
                        return;
                    }

                    //判断输入数据是否在数据类型范围内
                    strCurrentValue = In_clsTemp.CurrentValue;
                    bRet = OthersHelper.IsOutOfRange(ref strCurrentValue, In_clsTemp.Unit, In_clsTemp.DataType, In_clsTemp.Max, In_clsTemp.Min);
                    if (bRet)
                    {
                        In_clsTemp.CurrentValue = strCurrentValue;
                    }
                    else
                    {
                        ShowNotification(In_clsTemp.Name, In_clsTemp.Description, In_clsTemp.Min, In_clsTemp.Max, In_clsTemp.Unit);
                        return;
                    }

                    //获取地址与数据类型
                    lstParameterInfo.Add(In_clsTemp);
                    ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                    //下达任务
                    //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(SoftwareStateParameterSet.CurrentPageName, In_clsTemp.Description, lstTransmittingDataInfo);

                    //更改下载图标的状态为可见
                    ChangeDownloadImageVisibility(In_clsTemp, ControlVisibility.Visible);

                    ViewModelSet.Main?.ShowHintInfo("伺服控制权是Ecat控制，请切换伺服控制权...");
                    return;
                }
                else
                {
                    //判断输入数据是否为16进制数
                    if (OthersHelper.CheckIsInputHex(In_clsTemp.CurrentValue))
                    {
                        In_clsTemp.CurrentValue = OthersHelper.TransferHexToDecimal(In_clsTemp.CurrentValue, In_clsTemp.DataType);
                    }

                    //判断输入数据是否为10进制数
                    if (!OthersHelper.IsInputInteger(In_clsTemp.CurrentValue))
                    {
                        ShowNotification(2008);
                        return;
                    }

                    //判断输入数据是否在数据类型范围内
                    strCurrentValue = In_clsTemp.CurrentValue;
                    bRet = OthersHelper.IsOutOfRange(ref strCurrentValue, In_clsTemp.Unit, In_clsTemp.DataType, In_clsTemp.Max, In_clsTemp.Min);
                    if (bRet)
                    {
                        In_clsTemp.CurrentValue = strCurrentValue;
                    }
                    else
                    {
                        ShowNotification(In_clsTemp.Name, In_clsTemp.Description, In_clsTemp.Min, In_clsTemp.Max, In_clsTemp.Unit);
                        return;
                    }

                    //获取地址与数据类型
                    lstParameterInfo.Add(In_clsTemp);
                    ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                    //下达任务
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(SoftwareStateParameterSet.CurrentPageName, In_clsTemp.Description, lstTransmittingDataInfo);

                    //更改下载图标的状态为可见
                    ChangeDownloadImageVisibility(In_clsTemp, ControlVisibility.Visible);
                }

                //判断输入数据是否为16进制数
                //if (OthersHelper.CheckIsInputHex(In_clsTemp.CurrentValue))
                //{
                //    In_clsTemp.CurrentValue = OthersHelper.TransferHexToDecimal(In_clsTemp.CurrentValue, In_clsTemp.DataType);
                //}

                ////判断输入数据是否为10进制数
                //if (!OthersHelper.IsInputInteger(In_clsTemp.CurrentValue))
                //{
                //    ShowNotification(2008);
                //    return;
                //}

                ////判断输入数据是否在数据类型范围内
                //strCurrentValue = In_clsTemp.CurrentValue;
                //bRet = OthersHelper.IsOutOfRange(ref strCurrentValue, In_clsTemp.Unit, In_clsTemp.DataType, In_clsTemp.Max, In_clsTemp.Min);
                //if (bRet)
                //{
                //    In_clsTemp.CurrentValue = strCurrentValue;
                //}
                //else
                //{
                //    ShowNotification(In_clsTemp.Name, In_clsTemp.Description, In_clsTemp.Min, In_clsTemp.Max, In_clsTemp.Unit);
                //    return;
                //}

                ////获取地址与数据类型
                //lstParameterInfo.Add(In_clsTemp);
                //ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                ////下达任务
                //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(SoftwareStateParameterSet.CurrentPageName, In_clsTemp.Description, lstTransmittingDataInfo);

                ////更改下载图标的状态为可见
                //ChangeDownloadImageVisibility(In_clsTemp, ControlVisibility.Visible);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_PARAMETER_WRITE, "ParameterWrite", ex);
            }
        }

        //*************************************************************************
        //函数名称：ParameterRead
        //函数功能：参数读取
        //
        //输入参数：string In_strTabItem     
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.11
        //*************************************************************************
        public void ParameterRead(string In_strTabItem)
        {
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取当前页面名称
                SoftwareStateParameterSet.CurrentPageName = In_strTabItem;

                //获取地址与数据类型，写入串口
                switch (In_strTabItem)
                {
                    case TaskName.Common:
                        ParameterReadWriteModel.GetIndexAndDataType(obsParameter_Common, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName.Common, lstTransmittingDataInfo);
                        break;
                    case TaskName.Control:
                        ParameterReadWriteModel.GetIndexAndDataType(obsParameter_Control, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName.Control, lstTransmittingDataInfo);
                        break;
                    case TaskName.Motor:                       
                        ParameterReadWriteModel.GetIndexAndDataType(obsParameter_Motor, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName.Motor, lstTransmittingDataInfo);
                        break;
                    case TaskName.FaultAndProtection:
                        ParameterReadWriteModel.GetIndexAndDataType(obsParameter_FaultAndProtection, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName.FaultAndProtection, lstTransmittingDataInfo);
                        break;
                    case TaskName.DI:
                        ParameterReadWriteModel.GetIndexAndDataType(obsParameter_DI, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName.DI, lstTransmittingDataInfo);
                        break;
                    case TaskName.DO:
                        ParameterReadWriteModel.GetIndexAndDataType(obsParameter_DO, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName.DO, lstTransmittingDataInfo);
                        break;
                    case TaskName.Basic:
                        ParameterReadWriteModel.GetIndexAndDataType(obsParameter_Basic, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName.Basic, lstTransmittingDataInfo);
                        break;
                    case TaskName.Advanced:
                        ParameterReadWriteModel.GetIndexAndDataType(obsParameter_Advanced, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName.Advanced, lstTransmittingDataInfo);
                        break;
                    case TaskName.Auxiliary:
                        ParameterReadWriteModel.GetIndexAndDataType(obsParameter_Auxiliary, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName.Auxiliary, lstTransmittingDataInfo);
                        break;
                    case TaskName.CIA402:
                        ParameterReadWriteModel.GetIndexAndDataType(obsParameter_CIA402, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(SoftwareStateParameterSet.CurrentPageName, TaskName.CIA402, lstTransmittingDataInfo);
                        break;
                    default:
                        break;
                }

                //更改下载图标为全部不可见
                ChangeAllDownloadImageHidden(In_strTabItem);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_PARAMETER_READ, "ParameterRead", ex);
            }
        }
        #endregion

        #region 私有方法       
        //*************************************************************************
        //函数名称：ChangeDownloadButtonVisibility
        //函数功能：下载按钮控件展示与隐藏
        //
        //输入参数：NONE
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.10
        //*************************************************************************
        private void ChangeDownloadImageVisibility(ParameterReadWriteSet In_clsTemp, int In_iControlVisibility)
        {
            int iIndex = -1;

            try
            {
                if (In_clsTemp.Classification == TaskName.CIA402 && Parameter_CIA402 != null)
                {
                    iIndex = Parameter_CIA402.FindIndex(Item => Item.Index == In_clsTemp.Index);
                    if (iIndex != -1)
                    {
                        Parameter_CIA402[iIndex].IsDownload = In_iControlVisibility;
                    }               
                }
                else if (In_clsTemp.Classification == TaskName.Common && Parameter_Common != null)
                {
                    iIndex = Parameter_Common.FindIndex(Item => Item.Index == In_clsTemp.Index);
                    if (iIndex != -1)
                    {
                        Parameter_Common[iIndex].IsDownload = In_iControlVisibility;
                    }
                }
                else if (In_clsTemp.Classification == TaskName.Motor && Parameter_Motor != null)
                {
                    iIndex = Parameter_Motor.FindIndex(Item => Item.Index == In_clsTemp.Index);
                    if (iIndex != -1)
                    {
                        Parameter_Motor[iIndex].IsDownload = In_iControlVisibility;
                    }                  
                }
                else if (In_clsTemp.Classification == TaskName.Basic && Parameter_Basic != null)
                {
                    iIndex = Parameter_Basic.FindIndex(Item => Item.Index == In_clsTemp.Index);
                    if (iIndex != -1)
                    {
                        Parameter_Basic[iIndex].IsDownload = In_iControlVisibility;
                    }
                }
                else if (In_clsTemp.Classification == TaskName.Control && Parameter_Control != null)
                {
                    iIndex = Parameter_Control.FindIndex(Item => Item.Index == In_clsTemp.Index);
                    if (iIndex != -1)
                    {
                        Parameter_Control[iIndex].IsDownload = In_iControlVisibility;
                    }
                }
                else if (In_clsTemp.Classification == TaskName.Advanced && Parameter_Advanced != null)
                {
                    iIndex = Parameter_Advanced.FindIndex(Item => Item.Index == In_clsTemp.Index);
                    if (iIndex != -1)
                    {
                        Parameter_Advanced[iIndex].IsDownload = In_iControlVisibility;
                    }
                }
                else if (In_clsTemp.Classification == TaskName.DI && Parameter_DI != null)
                {
                    iIndex = Parameter_DI.FindIndex(Item => Item.Index == In_clsTemp.Index);
                    if (iIndex != -1)
                    {
                        Parameter_DI[iIndex].IsDownload = In_iControlVisibility;
                    }
                }
                else if (In_clsTemp.Classification == TaskName.DO && Parameter_DO != null)
                {
                    iIndex = Parameter_DO.FindIndex(Item => Item.Index == In_clsTemp.Index);
                    if (iIndex != -1)
                    {
                        Parameter_DO[iIndex].IsDownload = In_iControlVisibility;
                    }
                }
                else if (In_clsTemp.Classification == TaskName.FaultAndProtection && Parameter_FaultAndProtection != null)
                {
                    iIndex = Parameter_FaultAndProtection.FindIndex(Item => Item.Index == In_clsTemp.Index);
                    if (iIndex != -1)
                    {
                        Parameter_FaultAndProtection[iIndex].IsDownload = In_iControlVisibility;
                    }
                }
                else if (In_clsTemp.Classification == TaskName.Auxiliary && Parameter_Auxiliary != null)
                {
                    iIndex = Parameter_Auxiliary.FindIndex(Item => Item.Index == In_clsTemp.Index);
                    if (iIndex != -1)
                    {
                        Parameter_Auxiliary[iIndex].IsDownload = In_iControlVisibility;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_DOWNLOAD_BUTTON_VISIBILITY, "ChangeDownloadImageVisibility", ex);
            }
        }

        //*************************************************************************
        //函数名称：ChangeAllDownloadButtonVisibility
        //函数功能：更改全部的下载按钮可见属性
        //
        //输入参数：int In_iControlVisibility    可见属性值
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.11
        //*************************************************************************
        private void ChangeAllDownloadImageHidden(string strTaskName)
        {
            try
            {
                if (strTaskName == TaskName.CIA402 && Parameter_CIA402 != null)
                {
                    foreach(var item in Parameter_CIA402)
                    {
                        item.IsDownload = ControlVisibility.Collapsed;
                    }                            
                }
                else if (strTaskName == TaskName.Common && Parameter_Common != null)
                {
                    foreach (var item in Parameter_Common)
                    {
                        item.IsDownload = ControlVisibility.Collapsed;
                    }
                }
               else if (strTaskName == TaskName.Motor && Parameter_Motor != null)
                {
                    foreach (var item in Parameter_Motor)
                    {
                        item.IsDownload = ControlVisibility.Collapsed;
                    }
                }
                else if (strTaskName == TaskName.Basic && Parameter_Basic != null)
                {
                    foreach (var item in Parameter_Basic)
                    {
                        item.IsDownload = ControlVisibility.Collapsed;
                    }
                }
                else if (strTaskName == TaskName.Control && Parameter_Control != null)
                {
                    foreach (var item in Parameter_Control)
                    {
                        item.IsDownload = ControlVisibility.Collapsed;
                    }
                }
               else  if (strTaskName == TaskName.Advanced && Parameter_Advanced != null)
                {
                    foreach (var item in Parameter_Advanced)
                    {
                        item.IsDownload = ControlVisibility.Collapsed;
                    }
                }
               else if (strTaskName == TaskName.DI && Parameter_DI != null)
                {
                    foreach (var item in Parameter_DI)
                    {
                        item.IsDownload = ControlVisibility.Collapsed;
                    }
                }
                else if (strTaskName == TaskName.DO && Parameter_DO != null)
                {
                    foreach (var item in Parameter_DO)
                    {
                        item.IsDownload = ControlVisibility.Collapsed;
                    }
                }
                else if (strTaskName == TaskName.FaultAndProtection && Parameter_FaultAndProtection != null)
                {
                    foreach (var item in Parameter_FaultAndProtection)
                    {
                        item.IsDownload = ControlVisibility.Collapsed;
                    }
                }
               else  if (strTaskName == TaskName.Auxiliary && Parameter_Auxiliary != null)
                {
                    foreach (var item in Parameter_Auxiliary)
                    {
                        item.IsDownload = ControlVisibility.Collapsed;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_CHANGE_ALL_DOWNLOAD_BUTTON_VISIBILITY, "ChangeAllDownloadImageHidden", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationItemValue
        //函数功能：赋值数据
        //
        //输入参数：string In_strTabItem     TabItem名称
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.11
        //*************************************************************************
        private void EvaluationItemValue(string In_strTabItem)
        {
            try
            {
                if (In_strTabItem == TaskName.CIA402)
                {
                    for (int i = 0; i < Parameter_CIA402.Count; i++)
                    {                                          
                        Parameter_CIA402[i].CurrentValue = OthersHelper.GetCurrentValueOfIndex(Parameter_CIA402[i].Index);
                    }
                }

                if (In_strTabItem == TaskName.Common)
                {
                    for (int i = 0; i < Parameter_Common.Count; i++)
                    {
                        Parameter_Common[i].CurrentValue = OthersHelper.GetCurrentValueOfIndex(Parameter_Common[i].Index);
                    }
                }

                if (In_strTabItem == TaskName.Motor)
                {
                    for (int i = 0; i < Parameter_Motor.Count; i++)
                    {
                        Parameter_Motor[i].CurrentValue = OthersHelper.GetCurrentValueOfIndex(Parameter_Motor[i].Index);
                    }
                }

                if (In_strTabItem == TaskName.Basic)
                {
                    for (int i = 0; i < Parameter_Basic.Count; i++)
                    {
                        Parameter_Basic[i].CurrentValue = OthersHelper.GetCurrentValueOfIndex(Parameter_Basic[i].Index);
                    }
                }

                if (In_strTabItem == TaskName.Control)
                {
                    for (int i = 0; i < Parameter_Control.Count; i++)
                    {
                        Parameter_Control[i].CurrentValue = OthersHelper.GetCurrentValueOfIndex(Parameter_Control[i].Index);
                    }
                }

                if (In_strTabItem == TaskName.Advanced)
                {
                    for (int i = 0; i < Parameter_Advanced.Count; i++)
                    {    
                        Parameter_Advanced[i].CurrentValue = OthersHelper.GetCurrentValueOfIndex(Parameter_Advanced[i].Index);
                    }
                }

                if (In_strTabItem == TaskName.DI)
                {
                    for (int i = 0; i < Parameter_DI.Count; i++)
                    {
                        Parameter_DI[i].CurrentValue = OthersHelper.GetCurrentValueOfIndex(Parameter_DI[i].Index);
                    }
                }

                if (In_strTabItem == TaskName.DO)
                {
                    for (int i = 0; i < Parameter_DO.Count; i++)
                    {
                        Parameter_DO[i].CurrentValue = OthersHelper.GetCurrentValueOfIndex(Parameter_DO[i].Index);
                    }
                }

                if (In_strTabItem == TaskName.FaultAndProtection)
                {
                    for (int i = 0; i < Parameter_FaultAndProtection.Count; i++)
                    {
                        Parameter_FaultAndProtection[i].CurrentValue = OthersHelper.GetCurrentValueOfIndex(Parameter_FaultAndProtection[i].Index);
                    }
                }

                if (In_strTabItem == TaskName.Auxiliary)
                {
                    for (int i = 0; i < Parameter_Auxiliary.Count; i++)
                    {
                        Parameter_Auxiliary[i].CurrentValue = OthersHelper.GetCurrentValueOfIndex(Parameter_Auxiliary[i].Index);
                    }
                }
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("EvaluationItemValue", ex);
            }                    
        }

        //*************************************************************************
        //函数名称：ShowNotification
        //函数功能：信息提示
        //
        //输入参数：In_iType     信息提示类型
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.23
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        private void ShowNotification(string In_strName, string In_strItem, string In_strMin, string In_strMax, string In_Unit)
        {
            //ViewModelSet.Main?.ShowNotification(In_strName, In_strItem, In_strMin, In_strMax, In_Unit, bExchanged: false);
            ViewModelSet.Main?.ShowNotification_For_ParameterReadWrite(In_strName, In_strItem, In_strMin, In_strMax, In_Unit, bExchanged: false);
        }
        #endregion
    }

    public class ParameterReadWriteSet : INotifyPropertyChanged
    {
        private string classification;
        public string Classification
        {
            get { return classification; }
            set
            {
                classification = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Classification"));
                }
            }
        }

        private string index;
        public string Index
        {
            get { return index; }
            set
            {
                index = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Index"));
                }
            }
        }

        private string name;
        public string Name
        {
            get { return name; }
            set
            {
                name = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Name"));
                }
            }
        }

        private string dataType;
        public string DataType
        {
            get { return dataType; }
            set
            {
                dataType = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("DataType"));
                }
            }
        }

        private string description;
        public string Description
        {
            get { return description; }
            set
            {
                description = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Description"));
                }
            }
        }

        private string currentValue;
        public string CurrentValue
        {
            get { return currentValue; }
            set
            {
                currentValue = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("CurrentValue"));
                }
            }
        }

        private string unit;
        public string Unit
        {
            get { return unit; }
            set
            {
                unit = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Unit"));
                }
            }
        }

        private string min;
        public string Min
        {
            get { return min; }
            set
            {
                min = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Min"));
                }
            }
        }

        private string max;
        public string Max
        {
            get { return max; }
            set
            {
                max = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Max"));
                }
            }
        }

        private string defaultvalue;
        public string Default
        {
            get { return defaultvalue; }
            set
            {
                defaultvalue = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Default"));
                }
            }
        }

        private string rwproperty;
        public string RWProperty
        {
            get { return rwproperty; }
            set
            {
                rwproperty = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("RWProperty"));
                }
            }
        }

        private string comment;
        public string Comment
        {
            get { return comment; }
            set
            {
                comment = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Comment"));
                }
            }
        }

        private int isDownload;//下载按钮展示
        public int IsDownload
        {
            get { return isDownload; }
            set
            {
                isDownload = value;
                if (this.PropertyChanged != null)
                {
                    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("IsDownload"));
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
    }
}