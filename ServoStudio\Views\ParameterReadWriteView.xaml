﻿<UserControl xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"  
             x:Class="ServoStudio.Views.ParameterReadWriteView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm"
             xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
             xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
             xmlns:dxnt="http://schemas.devexpress.com/winfx/2008/xaml/navbar/themekeys"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
             xmlns:dxn="http://schemas.devexpress.com/winfx/2008/xaml/navbar"
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"  
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars" 
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:converter="clr-namespace:Converter"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:ParameterReadWriteViewModel}"
             d:DesignHeight="600" d:DesignWidth="900">
    
    <UserControl.Resources>
        <converter:BackgroundConverter x:Key="BackgroudConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>
        <converter:ContentConverter x:Key="ContentConverter"/>

        <Style x:Key="myTabItem" TargetType="dx:DXTabItem">
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Trigger.Setters>
                        <Setter Property="FontStyle" Value="Italic"/>  
                        <Setter Property="Foreground" Value="Red"/>
                    </Trigger.Setters>
                </Trigger>
            </Style.Triggers>
        </Style>

        <DataTemplate x:Key="CurrentValueTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.CurrentValue, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False" ToolTip="{Binding RowData.Row.Comment}">
                <!--dxe:TextEdit Grid.Column="0" Text="{Binding RowData.Row.CurrentValue, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" BorderThickness="0" ShowBorder="False"-->
                    <dxmvvm:Interaction.Behaviors>
                        <!--<dxmvvm:EventToCommand EventName="MouseDoubleClick" Command="{Binding Path=DataContext.TextBoxGetInputCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}"/>-->
                        <dxmvvm:KeyToCommand KeyGesture="Enter" Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}" />
                    </dxmvvm:Interaction.Behaviors>
                </dxe:TextEdit>

                <dxe:ImageEdit Height="16" Width="Auto" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Apply_16x16.png}" ShowBorder="False" HorizontalAlignment="Right" VerticalAlignment="Center"  Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}"/>

                <!--<dx:SimpleButton Grid.Column="1" Padding="0" Margin="0,0,0.5,0" Content="Download" Foreground="Red" FontSize="9" Glyph="{dx:DXImage Image=NavigateNext_16x16.png}" 
                                 Command="{Binding Path=DataContext.ParameterWriteCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}"
                                 Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}" />

                <dx:SimpleButton Grid.Column="2" Padding="0" Margin="0.5,0,0,0" Content="Return" Foreground="Green" FontSize="9" Glyph="{dx:DXImage Image=Backward_16x16.png}" 
                                 Command="{Binding Path=DataContext.ParameterWriteCancelCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}" CommandParameter="{Binding RowData.Row}"
                                 Visibility="{Binding RowData.Row.IsDownload, Converter={StaticResource VisibilityConverter}}"/>-->
            </Grid>
        </DataTemplate>

        <DataTemplate x:Key="IndexTemplate">
            <Grid>
                <Label ToolTip="{Binding RowData.Row.Comment}"  Content="{Binding RowData.Row.Index, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Margin="3,0" HorizontalAlignment="Left" VerticalAlignment="Center"/>
            </Grid>
        </DataTemplate>

        <DataTemplate x:Key="DataTypeTemplate">
            <Grid>
                <Label ToolTip="{Binding RowData.Row.Comment}"  Content="{Binding RowData.Row.DataType, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Margin="3,0" HorizontalAlignment="Left" VerticalAlignment="Center"/>
            </Grid>
        </DataTemplate>

        <DataTemplate x:Key="UnitTemplate">
            <Grid>
                <Label ToolTip="{Binding RowData.Row.Comment}"  Content="{Binding RowData.Row.Unit, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Margin="3,0" HorizontalAlignment="Left" VerticalAlignment="Center"/>
            </Grid>
        </DataTemplate>

        <DataTemplate x:Key="NameTemplate">
            <Grid>
                <Label ToolTip="{Binding RowData.Row.Comment}"  Content="{Binding RowData.Row.Name, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Margin="3,0" HorizontalAlignment="Left" VerticalAlignment="Center"/>  
            </Grid>
        </DataTemplate>

        <DataTemplate x:Key="DescriptionTemplate">
            <Grid>
                <Label ToolTip="{Binding RowData.Row.Comment}"  Content="{Binding RowData.Row.Description, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Margin="3,0" HorizontalAlignment="Left" VerticalAlignment="Center"/>
            </Grid>
        </DataTemplate>
    </UserControl.Resources>
    
    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding ParameterReadWriteLoadedCommand}"/>
    </dxmvvm:Interaction.Behaviors>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <dx:DXTabControl Padding="0" Grid.Row ="1">
            <dx:DXTabItem Header="电机参数" Style="{StaticResource myTabItem}" Visibility="{Binding Parameter_MotorPageEnabled,Converter={StaticResource VisibilityConverter}}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding ParameterReadCommand}" CommandParameter="电机参数" />
                </dxmvvm:Interaction.Behaviors>

                <Grid>
                    <dxg:GridControl SelectionMode="Row" ItemsSource="{Binding Parameter_Motor}" AutoGenerateColumns="AddNew" FontStyle="Normal" ShowBorder="False">
                        <dxg:GridControl.View>
                            <dxg:TableView AllowEditing="False" AutoWidth="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" ShowSearchPanelFindButton="True" SearchPanelHorizontalAlignment="Stretch" ShowGroupPanel="False">
                                <dxg:TableView.FormatConditions>
                                    <dxg:FormatCondition ApplyToRow="True" Expression="[RWProperty] = 'RO'" FieldName="RWProperty">
                                        <dx:Format Background="#FFC4E9F5"/>
                                    </dxg:FormatCondition>
                                    <dxg:FormatCondition Expression="[CurrentValue] Is Not Null" FieldName="CurrentValue">
                                        <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>
                                </dxg:TableView.FormatConditions>
                            </dxg:TableView>
                        </dxg:GridControl.View>

                        <dxg:GridColumn FieldName="Index" Header="索引号" IsSmart="True" Width="*" CellTemplate="{StaticResource IndexTemplate}"/>
                        <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="2.5*" CellTemplate="{StaticResource NameTemplate}"/>
                        <dxg:GridColumn FieldName="Description" Header="参数描述" IsSmart="True" Width="2*" CellTemplate="{StaticResource DescriptionTemplate}"/>
                        <dxg:GridColumn FieldName="DataType" Header="数据类型" IsSmart="True" Width="*" CellTemplate="{StaticResource DataTypeTemplate}"/>
                        <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="*" CellTemplate="{StaticResource UnitTemplate}"/>
                        <dxg:GridColumn FieldName="RWProperty" Header="读写属性" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Min" Header="最小值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Max" Header="最大值" IsSmart="True" Width="*"/>                    
                        <dxg:GridColumn FieldName="Default" Header="默认值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="CurrentValue" Header="当前参数值" IsSmart="True" Width="2*" CellTemplate="{StaticResource CurrentValueTemplate}"/>
                        <dxg:GridColumn FieldName="IsDownload" Visible="False"/>
                        <dxg:GridColumn FieldName="Classification" Visible="False"/> 
                        <dxg:GridColumn FieldName="Comment" Visible="False"/>
                    </dxg:GridControl>
                </Grid>
            </dx:DXTabItem>

            <dx:DXTabItem Header="基本配置参数" Style="{StaticResource myTabItem}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding ParameterReadCommand}" CommandParameter="基本配置参数" />
                </dxmvvm:Interaction.Behaviors>

                <Grid>
                    <dxg:GridControl SelectionMode="Row" ItemsSource="{Binding Parameter_Basic}" AutoGenerateColumns="AddNew" FontStyle="Normal" ShowBorder="False">
                        <dxg:GridControl.View>
                            <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" SearchPanelHorizontalAlignment="Stretch">
                                <dxg:TableView.FormatConditions>
                                    <dxg:FormatCondition ApplyToRow="True" Expression="[RWProperty] = 'RO'" FieldName="RWProperty">
                                        <dx:Format Background="#FFC4E9F5"/>
                                    </dxg:FormatCondition>
                                    <dxg:FormatCondition Expression="[CurrentValue] Is Not Null" FieldName="CurrentValue">
                                        <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>
                                </dxg:TableView.FormatConditions>
                            </dxg:TableView>
                        </dxg:GridControl.View>

                        <dxg:GridColumn FieldName="Index" Header="索引号" IsSmart="True" Width="*" CellTemplate="{StaticResource IndexTemplate}"/>
                        <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="2.5*" CellTemplate="{StaticResource NameTemplate}"/>
                        <dxg:GridColumn FieldName="Description" Header="参数描述" IsSmart="True" Width="2*" CellTemplate="{StaticResource DescriptionTemplate}"/>
                        <dxg:GridColumn FieldName="DataType" Header="数据类型" IsSmart="True" Width="*" CellTemplate="{StaticResource DataTypeTemplate}"/>
                        <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="*" CellTemplate="{StaticResource UnitTemplate}"/>
                        <dxg:GridColumn FieldName="RWProperty" Header="读写属性" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Min" Header="最小值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Max" Header="最大值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Default" Header="默认值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="CurrentValue" Header="当前参数值" IsSmart="True" Width="2*" CellTemplate="{StaticResource CurrentValueTemplate}"/>
                        <dxg:GridColumn FieldName="IsDownload" Visible="False"/>
                        <dxg:GridColumn FieldName="Classification" Visible="False"/>
                        <dxg:GridColumn FieldName="Comment" Visible="False"/>
                    </dxg:GridControl>
                </Grid>
            </dx:DXTabItem>

            <dx:DXTabItem Header="运控参数" Style="{StaticResource myTabItem}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding ParameterReadCommand}" CommandParameter="运控参数" />
                </dxmvvm:Interaction.Behaviors>

                <Grid>
                    <dxg:GridControl SelectionMode="Row" ItemsSource="{Binding Parameter_Control}" AutoGenerateColumns="AddNew" FontStyle="Normal" ShowBorder="False">
                        <dxg:GridControl.View>
                            <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" SearchPanelHorizontalAlignment="Stretch">
                                <dxg:TableView.FormatConditions>
                                    <dxg:FormatCondition ApplyToRow="True" Expression="[RWProperty] = 'RO'" FieldName="RWProperty">
                                        <dx:Format Background="#FFC4E9F5"/>
                                    </dxg:FormatCondition>
                                    <dxg:FormatCondition Expression="[CurrentValue] Is Not Null" FieldName="CurrentValue">
                                        <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>
                                </dxg:TableView.FormatConditions>
                            </dxg:TableView>
                        </dxg:GridControl.View>

                        <dxg:GridColumn FieldName="Index" Header="索引号" IsSmart="True" Width="*" CellTemplate="{StaticResource IndexTemplate}"/>
                        <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="2.5*" CellTemplate="{StaticResource NameTemplate}"/>
                        <dxg:GridColumn FieldName="Description" Header="参数描述" IsSmart="True" Width="2*" CellTemplate="{StaticResource DescriptionTemplate}"/>
                        <dxg:GridColumn FieldName="DataType" Header="数据类型" IsSmart="True" Width="*" CellTemplate="{StaticResource DataTypeTemplate}"/>
                        <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="*" CellTemplate="{StaticResource UnitTemplate}"/>
                        <dxg:GridColumn FieldName="RWProperty" Header="读写属性" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Min" Header="最小值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Max" Header="最大值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Default" Header="默认值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="CurrentValue" Header="当前参数值" IsSmart="True" Width="2*" CellTemplate="{StaticResource CurrentValueTemplate}"/>
                        <dxg:GridColumn FieldName="IsDownload" Visible="False"/>
                        <dxg:GridColumn FieldName="Classification" Visible="False"/>
                        <dxg:GridColumn FieldName="Comment" Visible="False"/>
                    </dxg:GridControl>
                </Grid>
            </dx:DXTabItem>

            <dx:DXTabItem Header="高级配置参数" Style="{StaticResource myTabItem}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding ParameterReadCommand}" CommandParameter="高级配置参数" />
                </dxmvvm:Interaction.Behaviors>

                <Grid>
                    <dxg:GridControl SelectionMode="Row" ItemsSource="{Binding Parameter_Advanced}" AutoGenerateColumns="AddNew" FontStyle="Normal" ShowBorder="False">
                        <dxg:GridControl.View>
                            <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" SearchPanelHorizontalAlignment="Stretch">
                                <dxg:TableView.FormatConditions>
                                    <dxg:FormatCondition ApplyToRow="True" Expression="[RWProperty] = 'RO'" FieldName="RWProperty">
                                        <dx:Format Background="#FFC4E9F5"/>
                                    </dxg:FormatCondition>
                                    <dxg:FormatCondition Expression="[CurrentValue] Is Not Null" FieldName="CurrentValue">
                                        <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>
                                </dxg:TableView.FormatConditions>
                            </dxg:TableView>
                        </dxg:GridControl.View>

                        <dxg:GridColumn FieldName="Index" Header="索引号" IsSmart="True" Width="*" CellTemplate="{StaticResource IndexTemplate}"/>
                        <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="2.5*" CellTemplate="{StaticResource NameTemplate}"/>
                        <dxg:GridColumn FieldName="Description" Header="参数描述" IsSmart="True" Width="2*" CellTemplate="{StaticResource DescriptionTemplate}"/>
                        <dxg:GridColumn FieldName="DataType" Header="数据类型" IsSmart="True" Width="*" CellTemplate="{StaticResource DataTypeTemplate}"/>
                        <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="*" CellTemplate="{StaticResource UnitTemplate}"/>
                        <dxg:GridColumn FieldName="RWProperty" Header="读写属性" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Min" Header="最小值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Max" Header="最大值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Default" Header="默认值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="CurrentValue" Header="当前参数值" IsSmart="True" Width="2*" CellTemplate="{StaticResource CurrentValueTemplate}"/>
                        <dxg:GridColumn FieldName="IsDownload" Visible="False"/>
                        <dxg:GridColumn FieldName="Classification" Visible="False"/>
                        <dxg:GridColumn FieldName="Comment" Visible="False"/>
                    </dxg:GridControl>
                </Grid>
            </dx:DXTabItem>

            <dx:DXTabItem Header="端子输入参数" Style="{StaticResource myTabItem}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding ParameterReadCommand}" CommandParameter="端子输入参数" />
                </dxmvvm:Interaction.Behaviors>

                <Grid>
                    <dxg:GridControl SelectionMode="Row" ItemsSource="{Binding Parameter_DI}" AutoGenerateColumns="AddNew" FontStyle="Normal" ShowBorder="False">
                        <dxg:GridControl.View>
                            <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" SearchPanelHorizontalAlignment="Stretch">
                                <dxg:TableView.FormatConditions>
                                    <dxg:FormatCondition ApplyToRow="True" Expression="[RWProperty] = 'RO'" FieldName="RWProperty">
                                        <dx:Format Background="#FFC4E9F5"/>
                                    </dxg:FormatCondition>
                                    <dxg:FormatCondition Expression="[CurrentValue] Is Not Null" FieldName="CurrentValue">
                                        <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>
                                </dxg:TableView.FormatConditions>
                            </dxg:TableView>
                        </dxg:GridControl.View>

                        <dxg:GridColumn FieldName="Index" Header="索引号" IsSmart="True" Width="*" CellTemplate="{StaticResource IndexTemplate}"/>
                        <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="2.5*" CellTemplate="{StaticResource NameTemplate}"/>
                        <dxg:GridColumn FieldName="Description" Header="参数描述" IsSmart="True" Width="2*" CellTemplate="{StaticResource DescriptionTemplate}"/>
                        <dxg:GridColumn FieldName="DataType" Header="数据类型" IsSmart="True" Width="*" CellTemplate="{StaticResource DataTypeTemplate}"/>
                        <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="*" CellTemplate="{StaticResource UnitTemplate}"/>
                        <dxg:GridColumn FieldName="RWProperty" Header="读写属性" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Min" Header="最小值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Max" Header="最大值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Default" Header="默认值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="CurrentValue" Header="当前参数值" IsSmart="True" Width="2*" CellTemplate="{StaticResource CurrentValueTemplate}"/>
                        <dxg:GridColumn FieldName="IsDownload" Visible="False"/>
                        <dxg:GridColumn FieldName="Classification" Visible="False"/>
                        <dxg:GridColumn FieldName="Comment" Visible="False"/>
                    </dxg:GridControl>
                </Grid>
            </dx:DXTabItem>

            <dx:DXTabItem Header="端子输出参数" Style="{StaticResource myTabItem}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding ParameterReadCommand}" CommandParameter="端子输出参数" />
                </dxmvvm:Interaction.Behaviors>

                <Grid>
                    <dxg:GridControl SelectionMode="Row" ItemsSource="{Binding Parameter_DO}" AutoGenerateColumns="AddNew" FontStyle="Normal" ShowBorder="False">
                        <dxg:GridControl.View>
                            <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" SearchPanelHorizontalAlignment="Stretch">
                                <dxg:TableView.FormatConditions>
                                    <dxg:FormatCondition ApplyToRow="True" Expression="[RWProperty] = 'RO'" FieldName="RWProperty">
                                        <dx:Format Background="#FFC4E9F5"/>
                                    </dxg:FormatCondition>
                                    <dxg:FormatCondition Expression="[CurrentValue] Is Not Null" FieldName="CurrentValue">
                                        <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>
                                </dxg:TableView.FormatConditions>
                            </dxg:TableView>
                        </dxg:GridControl.View>

                        <dxg:GridColumn FieldName="Index" Header="索引号" IsSmart="True" Width="*" CellTemplate="{StaticResource IndexTemplate}"/>
                        <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="2.5*" CellTemplate="{StaticResource NameTemplate}"/>
                        <dxg:GridColumn FieldName="Description" Header="参数描述" IsSmart="True" Width="2*" CellTemplate="{StaticResource DescriptionTemplate}"/>
                        <dxg:GridColumn FieldName="DataType" Header="数据类型" IsSmart="True" Width="*" CellTemplate="{StaticResource DataTypeTemplate}"/>
                        <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="*" CellTemplate="{StaticResource UnitTemplate}"/>
                        <dxg:GridColumn FieldName="RWProperty" Header="读写属性" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Min" Header="最小值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Max" Header="最大值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Default" Header="默认值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="CurrentValue" Header="当前参数值" IsSmart="True" Width="2*" CellTemplate="{StaticResource CurrentValueTemplate}"/>
                        <dxg:GridColumn FieldName="IsDownload" Visible="False"/>
                        <dxg:GridColumn FieldName="Classification" Visible="False"/>
                        <dxg:GridColumn FieldName="Comment" Visible="False"/>
                    </dxg:GridControl>
                </Grid>
            </dx:DXTabItem>

            <dx:DXTabItem Header="故障与保护参数" Style="{StaticResource myTabItem}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding ParameterReadCommand}" CommandParameter="故障与保护参数"/>
                </dxmvvm:Interaction.Behaviors>

                <Grid>
                    <dxg:GridControl SelectionMode="Row" ItemsSource="{Binding Parameter_FaultAndProtection}" AutoGenerateColumns="AddNew" FontStyle="Normal" ShowBorder="False">
                        <dxg:GridControl.View>
                            <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" SearchPanelHorizontalAlignment="Stretch">
                                <dxg:TableView.FormatConditions>
                                    <dxg:FormatCondition ApplyToRow="True" Expression="[RWProperty] = 'RO'" FieldName="RWProperty">
                                        <dx:Format Background="#FFC4E9F5"/>
                                    </dxg:FormatCondition>
                                    <dxg:FormatCondition Expression="[CurrentValue] Is Not Null" FieldName="CurrentValue">
                                        <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>
                                </dxg:TableView.FormatConditions>
                            </dxg:TableView>
                        </dxg:GridControl.View>

                        <dxg:GridColumn FieldName="Index" Header="索引号" IsSmart="True" Width="*" CellTemplate="{StaticResource IndexTemplate}"/>
                        <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="2.5*" CellTemplate="{StaticResource NameTemplate}"/>
                        <dxg:GridColumn FieldName="Description" Header="参数描述" IsSmart="True" Width="2*" CellTemplate="{StaticResource DescriptionTemplate}"/>
                        <dxg:GridColumn FieldName="DataType" Header="数据类型" IsSmart="True" Width="*" CellTemplate="{StaticResource DataTypeTemplate}"/>
                        <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="*" CellTemplate="{StaticResource UnitTemplate}"/>
                        <dxg:GridColumn FieldName="RWProperty" Header="读写属性" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Min" Header="最小值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Max" Header="最大值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Default" Header="默认值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="CurrentValue" Header="当前参数值" IsSmart="True" Width="2*" CellTemplate="{StaticResource CurrentValueTemplate}"/>
                        <dxg:GridColumn FieldName="IsDownload" Visible="False"/>
                        <dxg:GridColumn FieldName="Classification" Visible="False"/>
                        <dxg:GridColumn FieldName="Comment" Visible="False"/>
                    </dxg:GridControl>
                </Grid>
            </dx:DXTabItem>

            <dx:DXTabItem Header="辅助参数" Style="{StaticResource myTabItem}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding ParameterReadCommand}" CommandParameter="辅助参数" />
                </dxmvvm:Interaction.Behaviors>

                <Grid>
                    <dxg:GridControl SelectionMode="Row" ItemsSource="{Binding Parameter_Auxiliary}" AutoGenerateColumns="AddNew" FontStyle="Normal" ShowBorder="False">
                        <dxg:GridControl.View>
                            <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" SearchPanelHorizontalAlignment="Stretch">
                                <dxg:TableView.FormatConditions>
                                    <dxg:FormatCondition ApplyToRow="True" Expression="[RWProperty] = 'RO'" FieldName="RWProperty">
                                        <dx:Format Background="#FFC4E9F5"/>
                                    </dxg:FormatCondition>
                                    <dxg:FormatCondition Expression="[CurrentValue] Is Not Null" FieldName="CurrentValue">
                                        <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>
                                </dxg:TableView.FormatConditions>
                            </dxg:TableView>
                        </dxg:GridControl.View>

                        <dxg:GridColumn FieldName="Index" Header="索引号" IsSmart="True" Width="*" CellTemplate="{StaticResource IndexTemplate}"/>
                        <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="2.5*" CellTemplate="{StaticResource NameTemplate}"/>
                        <dxg:GridColumn FieldName="Description" Header="参数描述" IsSmart="True" Width="2*" CellTemplate="{StaticResource DescriptionTemplate}"/>
                        <dxg:GridColumn FieldName="DataType" Header="数据类型" IsSmart="True" Width="*" CellTemplate="{StaticResource DataTypeTemplate}"/>
                        <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="*" CellTemplate="{StaticResource UnitTemplate}"/>
                        <dxg:GridColumn FieldName="RWProperty" Header="读写属性" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Min" Header="最小值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Max" Header="最大值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Default" Header="默认值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="CurrentValue" Header="当前参数值" IsSmart="True" Width="2*" CellTemplate="{StaticResource CurrentValueTemplate}"/>
                        <dxg:GridColumn FieldName="IsDownload" Visible="False"/>
                        <dxg:GridColumn FieldName="Classification" Visible="False"/>
                        <dxg:GridColumn FieldName="Comment" Visible="False"/>
                    </dxg:GridControl>
                </Grid>
            </dx:DXTabItem>

            <dx:DXTabItem Header="CIA402" Style="{StaticResource myTabItem}">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding ParameterReadCommand}" CommandParameter="CIA402" />
                </dxmvvm:Interaction.Behaviors>

                <Grid>
                    <dxg:GridControl SelectionMode="Row" ItemsSource="{Binding Parameter_CIA402}" AutoGenerateColumns="AddNew" FontStyle="Normal" ShowBorder="False">
                        <dxg:GridControl.View>
                            <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" SearchPanelHorizontalAlignment="Stretch">
                                <dxg:TableView.FormatConditions>
                                    <dxg:FormatCondition ApplyToRow="True" Expression="[RWProperty] = 'RO'" FieldName="RWProperty">
                                        <dx:Format Background="#FFC4E9F5"/>
                                    </dxg:FormatCondition>
                                    <dxg:FormatCondition Expression="[CurrentValue] Is Not Null" FieldName="CurrentValue">
                                        <dx:Format Foreground="Green" TextDecorations="{x:Null}"/>
                                    </dxg:FormatCondition>
                                </dxg:TableView.FormatConditions>
                            </dxg:TableView>
                        </dxg:GridControl.View>

                        <dxg:GridColumn FieldName="Index" Header="索引号" IsSmart="True" Width="*" CellTemplate="{StaticResource IndexTemplate}"/>
                        <dxg:GridColumn FieldName="Name" Header="参数名称" IsSmart="True" Width="2.5*" CellTemplate="{StaticResource NameTemplate}"/>
                        <dxg:GridColumn FieldName="Description" Header="参数描述" IsSmart="True" Width="2*" CellTemplate="{StaticResource DescriptionTemplate}"/>
                        <dxg:GridColumn FieldName="DataType" Header="数据类型" IsSmart="True" Width="*" CellTemplate="{StaticResource DataTypeTemplate}"/>
                        <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="*" CellTemplate="{StaticResource UnitTemplate}"/>
                        <dxg:GridColumn FieldName="RWProperty" Header="读写属性" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Min" Header="最小值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Max" Header="最大值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="Default" Header="默认值" IsSmart="True" Width="*"/>
                        <dxg:GridColumn FieldName="CurrentValue" Header="当前参数值" IsSmart="True" Width="2*" CellTemplate="{StaticResource CurrentValueTemplate}"/>
                        <dxg:GridColumn FieldName="IsDownload" Visible="False"/>
                        <dxg:GridColumn FieldName="Classification" Visible="False"/>
                        <dxg:GridColumn FieldName="Comment" Visible="False"/>
                    </dxg:GridControl>
                </Grid>
            </dx:DXTabItem>
        </dx:DXTabControl>
        
    </Grid>
</UserControl>
