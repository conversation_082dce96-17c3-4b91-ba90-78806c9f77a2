﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using ServoStudio.Models;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class SwitchAxisViewModel
    {     
        public virtual ObservableCollection<string> AxisID { get; set; }//转轴ID
        public virtual string SelectedAxisID { get; set; }//选中的转轴ID   
        public virtual ObservableCollection<string> SlaveID { get; set; }//从站ID
        public virtual string SelectedSlaveID { get; set; }//选中的从站ID   
        public SwitchAxisViewModel() { ViewModelSet.SwitchAxis = this; }    
        public void SwitchAxisLoaded()
        {
            SlaveID = new ObservableCollection<string>();
            AxisID = new ObservableCollection<string>();

            for (int i = 0; i < ConfigServo.SelectConfigSlaveID; i++)
            {
                SlaveID.Add("SLAVE-" + (i + 1).ToString());
            }

            //for (int i = 0; i < ConfigServo.SelectConfigAxisID; i++)
            //{
            //    AxisID.Add("AXIS-" + (i + 1).ToString());
            //}

            #region 2合一伺服
            if (ConfigServo.SelectConfigSlaveID == 2 && ConfigServo.SelectConfigAxisID == 2)
            {
                for (int i = 0; i < ConfigServo.SelectConfigAxisID; i++)
                {
                    AxisID.Add("AXIS-" + (i + 1).ToString());
                }
            }
            #endregion
            #region 4合一伺服
            if (ConfigServo.SelectConfigSlaveID == 2 && ConfigServo.SelectConfigAxisID == 4)
            {
                for (int i = 0; i < ConfigServo.SelectConfigAxisID; i++)
                {
                    AxisID.Add("AXIS-" + (i + 1).ToString());
                }
            }
            #endregion
            #region 6合一伺服
            if (ConfigServo.SelectConfigSlaveID == 3 && ConfigServo.SelectConfigAxisID == 6)
            {
                for (int i = 0; i < ConfigServo.SelectConfigAxisID; i++)
                {
                    AxisID.Add("AXIS-" + (i + 1).ToString());
                }
            }
            #endregion
            #region 单轴伺服
            if ((ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 1) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 2) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 3) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 4) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 5) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 6) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 7) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 8) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 9) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 10) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 11) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 12) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 13) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 14) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 15) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 16) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 17) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 18) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 19) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 20) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 21) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 22) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 23) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 24) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 25) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 26) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 27) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 28) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 29) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 30) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 31) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 32))
            {
                for (int i = 0; i < ConfigServo.SelectConfigAxisID; i++)
                {
                    AxisID.Add("AXIS-" + (i + 1).ToString());
                }
            }
            #endregion

            //if (ConfigServo.SelectConfigSlaveID == 1)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 2)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 3)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 4)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 5)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 6)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 7)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 8)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 9)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 10)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 11)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 12)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 13)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 14)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 15)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 16)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 17)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 18)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 19)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 20)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 21)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 22)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 23)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22", "SLAVE-23" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 24)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22", "SLAVE-23", "SLAVE-24" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 25)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22", "SLAVE-23", "SLAVE-24", "SLAVE-25" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 26)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22", "SLAVE-23", "SLAVE-24", "SLAVE-25", "SLAVE-26" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 27)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22", "SLAVE-23", "SLAVE-24", "SLAVE-25", "SLAVE-26", "SLAVE-27" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 28)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22", "SLAVE-23", "SLAVE-24", "SLAVE-25", "SLAVE-26", "SLAVE-27", "SLAVE-28" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 29)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22", "SLAVE-23", "SLAVE-24", "SLAVE-25", "SLAVE-26", "SLAVE-27", "SLAVE-28", "SLAVE-29" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 30)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22", "SLAVE-23", "SLAVE-24", "SLAVE-25", "SLAVE-26", "SLAVE-27", "SLAVE-28", "SLAVE-29", "SLAVE-30" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 31)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22", "SLAVE-23", "SLAVE-24", "SLAVE-25", "SLAVE-26", "SLAVE-27", "SLAVE-28", "SLAVE-29", "SLAVE-30", "SLAVE-31" };
            //}
            //if (ConfigServo.SelectConfigSlaveID == 32)
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3", "SLAVE-4", "SLAVE-5", "SLAVE-6", "SLAVE-7", "SLAVE-8", "SLAVE-9", "SLAVE-10", "SLAVE-11", "SLAVE-12", "SLAVE-13", "SLAVE-14", "SLAVE-15", "SLAVE-16", "SLAVE-17", "SLAVE-18", "SLAVE-19", "SLAVE-20", "SLAVE-21", "SLAVE-22", "SLAVE-23", "SLAVE-24", "SLAVE-25", "SLAVE-26", "SLAVE-27", "SLAVE-28", "SLAVE-29", "SLAVE-30", "SLAVE-31", "SLAVE-32" };
            //}

            //if (ConfigServo.SelectConfigAxisID == 1)
            //{
            //    AxisID = new ObservableCollection<string>() { "AXIS-1" };
            //}
            //if (ConfigServo.SelectConfigAxisID == 2)
            //{
            //    AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
            //}
            //if (ConfigServo.SelectConfigAxisID == 3)
            //{
            //    AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2", "AXIS-3" };
            //}
            //if (ConfigServo.SelectConfigAxisID == 4)
            //{
            //    AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2", "AXIS-3", "AXIS-4" };
            //}
            //if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            //{

            //    switch (GlobalCurrentInput.SelectedServoName)
            //    {
            //        case "6合一伺服":
            //            SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3" };
            //            AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
            //            break;
            //        case "4合一伺服":
            //            SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2" };
            //            AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
            //            break;
            //        case "MD4伺服":
            //            SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2" };
            //            AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
            //            break;
            //        case "2合一伺服":
            //            SlaveID = new ObservableCollection<string>() { "SLAVE-1" };
            //            AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2" };
            //            break;
            //        case "高压单轴伺服":
            //            SlaveID = AddSlaveAxisIDSet.GetSlaveIDList;
            //            AxisID = new ObservableCollection<string>() { "AXIS-1" };
            //            break;
            //        case "低压单轴伺服":
            //            SlaveID = AddSlaveAxisIDSet.GetSlaveIDList;
            //            AxisID = new ObservableCollection<string>() { "AXIS-1" };
            //            break;
            //        case "低压高功率伺服":
            //            SlaveID = AddSlaveAxisIDSet.GetSlaveIDList;
            //            AxisID = new ObservableCollection<string>() { "AXIS-1" };
            //            break;
            //        default:
            //            break;
            //    }
            //}


            if (GlobalCurrentInput.SelectedSlaveID == null)//在轴切换时增加显示当从站
            {
                SelectedSlaveID = "SLAVE-1";
            }
            else
            {
                SelectedSlaveID = GlobalCurrentInput.SelectedSlaveID;
            }

            //AxisID = new ObservableCollection<string>() { "AXIS-1", "AXIS-2", "AXIS-3", "AXIS-4", "AXIS-5", "AXIS-6" };
            if (GlobalCurrentInput.SelectedAxisID == null)//在轴切换时增加显示当前轴
            {
                SelectedAxisID = "AXIS-1";
            }
            else
            {
                SelectedAxisID = GlobalCurrentInput.SelectedAxisID;
            }
        }
    }
}