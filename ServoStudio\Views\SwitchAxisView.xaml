﻿<UserControl x:Class="ServoStudio.Views.SwitchAxisView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors" 
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:SwitchAxisViewModel}">
    
    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding SwitchAxisLoadedCommand}"/>      
    </dxmvvm:Interaction.Behaviors>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>      
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="0.09*"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
            
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="0.09*"/>
        </Grid.ColumnDefinitions>

        <!--<Label Grid.Column="1" Margin="10" Content="从站ID" Style="{StaticResource LabelStyle}"/>
        <dxe:ComboBoxEdit  Grid.Column="2" Margin="10" Width="200" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding SlaveID}" SelectedItem="{Binding SelectedSlaveID, Mode=TwoWay}"/>-->

        <Label Grid.Column="4" Margin="10" Content="轴地址" Style="{StaticResource LabelStyle}"/>
        <dxe:ComboBoxEdit  Grid.Column="5" Margin="10" Width="200" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding AxisID}" SelectedItem="{Binding SelectedAxisID, Mode=TwoWay}"/>
    </Grid>
</UserControl>
