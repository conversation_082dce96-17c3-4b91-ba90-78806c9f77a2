# ServoStudio 优化代码示例

## 1. 启动性能优化示例

### 当前问题
MainWindowViewModel构造函数中执行了大量初始化操作，导致启动缓慢。

### 优化方案：延迟加载

```csharp
// 优化前的代码（ServoStudio/MainWindowViewModel.cs）
public MainWindowViewModel() 
{ 
    ViewModelSet.Main = this;
    
    // 这些初始化操作都在构造函数中执行，导致启动缓慢
    ObservableCollectionInitialize();
    DispatcherTimerInitialize();
    EventInitialize();
    OthersInitialize();
    ThreadPool_SerialPortTransmitting();
}

// 优化后的代码
public class MainWindowViewModel
{
    private readonly Lazy<IObservableCollectionService> _observableCollectionService;
    private readonly Lazy<ITimerService> _timerService;
    private readonly Lazy<IEventService> _eventService;
    private bool _isInitialized = false;
    
    public MainWindowViewModel(
        Lazy<IObservableCollectionService> observableCollectionService,
        Lazy<ITimerService> timerService,
        Lazy<IEventService> eventService)
    {
        _observableCollectionService = observableCollectionService;
        _timerService = timerService;
        _eventService = eventService;
        ViewModelSet.Main = this;
    }
    
    public async Task InitializeAsync()
    {
        if (_isInitialized) return;
        
        // 显示启动进度
        ShowStartupProgress("正在初始化界面组件...", 25);
        await Task.Run(() => _observableCollectionService.Value.Initialize());
        
        ShowStartupProgress("正在启动定时器服务...", 50);
        await Task.Run(() => _timerService.Value.Initialize());
        
        ShowStartupProgress("正在注册事件处理...", 75);
        await Task.Run(() => _eventService.Value.Initialize());
        
        ShowStartupProgress("初始化完成", 100);
        _isInitialized = true;
    }
}
```

### 启动画面改进

```csharp
// 新增启动画面服务
public class StartupService
{
    private readonly SplashScreenView _splashScreen;
    
    public StartupService()
    {
        _splashScreen = new SplashScreenView();
    }
    
    public async Task StartApplicationAsync()
    {
        // 显示启动画面
        _splashScreen.Show();
        
        try
        {
            // 并行初始化独立的服务
            var initTasks = new[]
            {
                InitializeConfigurationAsync(),
                InitializeLoggingAsync(),
                InitializeCommunicationAsync(),
                InitializeDataServicesAsync()
            };
            
            await Task.WhenAll(initTasks);
            
            // 初始化主窗口
            var mainWindow = new MainWindow();
            await ((MainWindowViewModel)mainWindow.DataContext).InitializeAsync();
            
            // 显示主窗口，隐藏启动画面
            mainWindow.Show();
            _splashScreen.Hide();
        }
        catch (Exception ex)
        {
            _splashScreen.Hide();
            MessageBox.Show($"启动失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
```

## 2. 内存优化示例

### 事件订阅内存泄漏修复

```csharp
// 优化前：容易造成内存泄漏
public class MainWindowViewModel
{
    public MainWindowViewModel()
    {
        // 直接订阅事件，容易造成内存泄漏
        GlobalEvents.DataReceived += OnDataReceived;
        Timer_System.Tick += Timer_System_Tick;
    }
    
    private void OnDataReceived(object sender, DataEventArgs e)
    {
        // 处理数据
    }
}

// 优化后：使用弱事件模式
public class MainWindowViewModel : IDisposable
{
    private readonly WeakEventManager _eventManager;
    private bool _disposed = false;
    
    public MainWindowViewModel()
    {
        _eventManager = new WeakEventManager();
        
        // 使用弱事件订阅
        _eventManager.Subscribe<DataEventArgs>(GlobalEvents.DataReceived, OnDataReceived);
        _eventManager.Subscribe<EventArgs>(Timer_System.Tick, Timer_System_Tick);
    }
    
    private void OnDataReceived(DataEventArgs e)
    {
        if (_disposed) return;
        // 处理数据
    }
    
    public void Dispose()
    {
        if (_disposed) return;
        
        _eventManager?.Dispose();
        _disposed = true;
    }
}

// 弱事件管理器实现
public class WeakEventManager : IDisposable
{
    private readonly List<IWeakEventSubscription> _subscriptions = new();
    
    public void Subscribe<T>(EventHandler<T> eventHandler, Action<T> handler) where T : EventArgs
    {
        var subscription = new WeakEventSubscription<T>(eventHandler, handler);
        _subscriptions.Add(subscription);
    }
    
    public void Dispose()
    {
        foreach (var subscription in _subscriptions)
        {
            subscription.Dispose();
        }
        _subscriptions.Clear();
    }
}
```

### 对象池实现

```csharp
// 大对象池管理
public class ObjectPool<T> where T : class, new()
{
    private readonly ConcurrentQueue<T> _objects = new();
    private readonly Func<T> _objectGenerator;
    private readonly Action<T> _resetAction;
    
    public ObjectPool(Func<T> objectGenerator = null, Action<T> resetAction = null)
    {
        _objectGenerator = objectGenerator ?? (() => new T());
        _resetAction = resetAction;
    }
    
    public T Get()
    {
        if (_objects.TryDequeue(out T item))
        {
            return item;
        }
        
        return _objectGenerator();
    }
    
    public void Return(T item)
    {
        if (item == null) return;
        
        _resetAction?.Invoke(item);
        _objects.Enqueue(item);
    }
}

// 在数据处理中使用对象池
public class DataProcessor
{
    private readonly ObjectPool<DataBuffer> _bufferPool;
    
    public DataProcessor()
    {
        _bufferPool = new ObjectPool<DataBuffer>(
            () => new DataBuffer(1024),
            buffer => buffer.Clear()
        );
    }
    
    public async Task ProcessDataAsync(byte[] data)
    {
        var buffer = _bufferPool.Get();
        try
        {
            buffer.Write(data);
            await ProcessBuffer(buffer);
        }
        finally
        {
            _bufferPool.Return(buffer);
        }
    }
}
```

## 3. 异步UI优化示例

### 异步命令实现

```csharp
// 优化前：同步命令阻塞UI
public ICommand ExportConfigFileCommand => new RelayCommand(() =>
{
    // 这个操作可能需要几秒钟，会阻塞UI
    ExportConfigFile();
});

// 优化后：异步命令
public ICommand ExportConfigFileCommand => new AsyncRelayCommand(async () =>
{
    try
    {
        IsExporting = true;
        ExportProgress = 0;
        
        await Task.Run(() =>
        {
            // 在后台线程执行耗时操作
            ExportConfigFileAsync(new Progress<int>(progress => 
            {
                // 更新进度到UI线程
                Application.Current.Dispatcher.Invoke(() => 
                {
                    ExportProgress = progress;
                });
            }));
        });
        
        ShowNotification("导出完成", NotificationType.Success);
    }
    catch (Exception ex)
    {
        ShowNotification($"导出失败: {ex.Message}", NotificationType.Error);
    }
    finally
    {
        IsExporting = false;
        ExportProgress = 0;
    }
});

// 异步命令基类
public class AsyncRelayCommand : ICommand
{
    private readonly Func<Task> _execute;
    private readonly Func<bool> _canExecute;
    private bool _isExecuting;
    
    public AsyncRelayCommand(Func<Task> execute, Func<bool> canExecute = null)
    {
        _execute = execute ?? throw new ArgumentNullException(nameof(execute));
        _canExecute = canExecute;
    }
    
    public event EventHandler CanExecuteChanged;
    
    public bool CanExecute(object parameter)
    {
        return !_isExecuting && (_canExecute?.Invoke() ?? true);
    }
    
    public async void Execute(object parameter)
    {
        if (_isExecuting) return;
        
        _isExecuting = true;
        RaiseCanExecuteChanged();
        
        try
        {
            await _execute();
        }
        catch (Exception ex)
        {
            // 处理异常
            HandleException(ex);
        }
        finally
        {
            _isExecuting = false;
            RaiseCanExecuteChanged();
        }
    }
    
    private void RaiseCanExecuteChanged()
    {
        CanExecuteChanged?.Invoke(this, EventArgs.Empty);
    }
}
```

### 进度指示器

```xaml
<!-- 在MainWindow.xaml中添加进度指示器 -->
<Grid>
    <!-- 原有内容 -->
    
    <!-- 进度指示器覆盖层 -->
    <Grid Background="#80000000" 
          Visibility="{Binding IsOperationInProgress, Converter={StaticResource BoolToVisibilityConverter}}">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <dxe:ProgressBarEdit Value="{Binding OperationProgress}" 
                                Maximum="100" 
                                Width="300" 
                                Height="20"/>
            <TextBlock Text="{Binding OperationStatus}" 
                      Foreground="White" 
                      HorizontalAlignment="Center" 
                      Margin="0,10,0,0"/>
        </StackPanel>
    </Grid>
</Grid>
```

## 4. 错误处理优化示例

### 全局异常处理器

```csharp
public class GlobalExceptionHandler
{
    private readonly ILogger _logger;
    private readonly INotificationService _notificationService;
    
    public GlobalExceptionHandler(ILogger logger, INotificationService notificationService)
    {
        _logger = logger;
        _notificationService = notificationService;
        
        // 注册全局异常处理
        Application.Current.DispatcherUnhandledException += OnDispatcherUnhandledException;
        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
    }
    
    private void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
    {
        HandleException(e.Exception, "UI线程异常");
        e.Handled = true; // 防止应用程序崩溃
    }
    
    public void HandleException(Exception ex, string context)
    {
        // 记录详细的技术日志
        _logger.LogError(ex, "异常发生在 {Context}", context);
        
        // 显示用户友好的错误消息
        var userMessage = GetUserFriendlyMessage(ex);
        var recoveryAction = GetRecoveryAction(ex);
        
        _notificationService.ShowError(userMessage, recoveryAction);
        
        // 尝试自动恢复
        if (CanAutoRecover(ex))
        {
            AttemptAutoRecovery(ex, context);
        }
    }
    
    private string GetUserFriendlyMessage(Exception ex)
    {
        return ex switch
        {
            CommunicationException => "设备通信失败，请检查连接线缆和设备电源",
            FileNotFoundException => "配置文件丢失，系统将使用默认设置",
            UnauthorizedAccessException => "权限不足，请以管理员身份运行程序",
            TimeoutException => "操作超时，请检查网络连接或稍后重试",
            _ => "系统遇到未知错误，请联系技术支持"
        };
    }
    
    private RecoveryAction GetRecoveryAction(Exception ex)
    {
        return ex switch
        {
            CommunicationException => new RecoveryAction("重新连接", () => RetryConnection()),
            FileNotFoundException => new RecoveryAction("重置配置", () => ResetConfiguration()),
            _ => new RecoveryAction("重启程序", () => RestartApplication())
        };
    }
}

public class RecoveryAction
{
    public string Description { get; }
    public Action Action { get; }
    
    public RecoveryAction(string description, Action action)
    {
        Description = description;
        Action = action;
    }
}
```

## 5. 配置管理优化示例

### 统一配置服务

```csharp
// 新的配置服务接口
public interface IConfigurationService
{
    T GetValue<T>(string key, T defaultValue = default);
    void SetValue<T>(string key, T value);
    void Save();
    event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;
}

// 配置服务实现
public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly IMemoryCache _cache;
    private readonly string _configFilePath;
    
    public ConfigurationService()
    {
        _configFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), 
                                      "ServoStudio", "appsettings.json");
        
        var builder = new ConfigurationBuilder()
            .SetBasePath(Path.GetDirectoryName(_configFilePath))
            .AddJsonFile(Path.GetFileName(_configFilePath), optional: true, reloadOnChange: true);
            
        _configuration = builder.Build();
        _cache = new MemoryCache(new MemoryCacheOptions());
        
        // 监听配置文件变化
        ChangeToken.OnChange(() => _configuration.GetReloadToken(), OnConfigurationReloaded);
    }
    
    public T GetValue<T>(string key, T defaultValue = default)
    {
        return _cache.GetOrCreate(key, factory =>
        {
            factory.SetAbsoluteExpirationRelativeToNow(TimeSpan.FromMinutes(5));
            return _configuration.GetValue(key, defaultValue);
        });
    }
    
    public void SetValue<T>(string key, T value)
    {
        _configuration[key] = value?.ToString();
        _cache.Remove(key);
        
        ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs(key, value));
    }
    
    public void Save()
    {
        // 保存配置到文件
        var configObject = new Dictionary<string, object>();
        foreach (var section in _configuration.GetChildren())
        {
            configObject[section.Key] = section.Value;
        }
        
        var json = JsonSerializer.Serialize(configObject, new JsonSerializerOptions { WriteIndented = true });
        File.WriteAllText(_configFilePath, json);
    }
    
    private void OnConfigurationReloaded()
    {
        _cache.Clear();
        ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs("*", null));
    }
}
```

这些代码示例展示了如何具体实施ServoStudio的优化方案，每个示例都包含了优化前后的对比，可以直接应用到项目中。