﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using System.Collections.ObjectModel;
using System.ComponentModel;
using ServoStudio;
using System.Data;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Collections.Generic;
using ServoStudio.Models;
using System.Windows.Threading;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class InertiaIdentificationParameterSelfTunningViewModel
    {
        #region 私有字段
        private static bool IsEncoderSet = false;
        public bool IsParameterSelfTunningInitialized = true;
        public bool IsClosed = false;//是否结束 
        public bool IsUnloaded = false;//是否结束 
        public bool IsParameterSelfTunningSucceed = false;//参数成功是否成功
        private static bool IsInitialized = true;
        public bool IsInitialized_ForMotorInertiaIdentification = true;
        public bool IsInitialized_ForParameterSelfTunningIdentification = true;
        private static bool IsEvaluationAll = true;
        private DispatcherTimer Timer_System = new DispatcherTimer();//系统时间  

        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 服务
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        protected virtual IDialogService DialogService { get { return this.GetService<IDialogService>(); } }
        #endregion

        #region 提示信息
        public virtual string InertiaIdentificationInformation { get; set; }//电机负载惯量辨识提示信息 
        public virtual string ParameterSelfTunningInformation { get; set; }//电机参数自整定提示信息 
        #endregion

        #region 属性
        public virtual string ParameterSelfTunningSwitchHint { get; set; }//电机参数自整定使能、禁能提示
        public virtual int HintVisibility { get; set; }//参数识别信息提示是否可见
        public virtual int VibrationSuppressionOption_Color { get; set; }//A型抑振控制选择字体颜色
        public virtual int ModelFollowingControlSwitch_Color { get; set; }//模型追踪控制开关字体颜色
        public virtual int PositionLoopGain_Color { get; set; }//位置环增益字体颜色
        public virtual int SpeedLoopGain_Color { get; set; }//速度环增益字体颜色
        public virtual int SpeedLoopTimeConstant_Color { get; set; }//速度环积分时间常数字体颜色
        public virtual int FirstTrqcmdFilterTime_Color { get; set; }//第一转矩指令滤波时间参数字体颜色
        public virtual bool IsEdit { get; set; }//是否可编辑
        #region 电机
        public virtual ObservableCollection<string> MotorType { get; set; }//电机类型
        public virtual string SelectedMotorType { get; set; }//选中的电机类型

        public virtual ObservableCollection<string> MotorID { get; set; }//电机型号       
        public virtual string SelectedMotorID { get; set; }//选中的电机型号
        public virtual string MotorRatedPower { get; set; }//电机额定功率
        public virtual string MotorRatedFrequency { get; set; }//电机额定频率
        public virtual string MotorRatedVoltage { get; set; }//电机额定电压
        public virtual string MotorRatedCurrent { get; set; }//电机额定电流
        public virtual string MotorRatedTorque { get; set; }//电机额定转矩
        public virtual string MotorRatedSpeed { get; set; }//电机额定转速

        public virtual string MotorLineUVWSequence { get; set; }//电机动力线相序
        public virtual string MotorMaxTorque { get; set; }//电机最大转矩
        public virtual string MotorMaxSpeed { get; set; }//电机最大转速

        public virtual string MotorPolePairsNumber { get; set; }//电机极对数
        public virtual string MotorWindingResistance { get; set; }//电机线电阻
        public virtual string MotorWindingInductance { get; set; }//电机线电感
        public virtual string AbsEncoderOffset { get; set; }//绝对值编码器偏置
        public virtual string MotorInductanceLd { get; set; }//电机D轴线电感
        public virtual string MotorInductanceLq { get; set; }//电机Q轴线电感
        public virtual string MotorBackEMF { get; set; }//电机线反电势系数数
        public virtual string MotorTorqueConstant { get; set; }//电机转矩系数
        public virtual string MotorMechanicalConstant { get; set; }//电机机械系数
        public virtual string LinearMotorPitch { get; set; }//直线电机节距
        public virtual string OverSpeedValue { get; set; }//超速预警阈值

        public virtual int IsRotatingMotorPageEnabled { get; set; }//是否旋转型无刷电机界面可以使用
        public virtual int IsSpeedModePageEnabled { get; set; }//是否速度模式界面可以使用

        public virtual string MotorWindingResistance_AutoLearnIdentification { get; set; }//识别线电阻
        public virtual string MotorInductanceLd_AutoLearnIdentification { get; set; }//识别D轴线电感
        public virtual string MotorInductanceLq_AutoLearnIdentification { get; set; }//识别Q轴线电感

        public virtual int MotorWindingResistance_Color { get; set; }//线电阻
        public virtual int MotorInductanceLd_Color { get; set; }//D轴线电感
        public virtual int MotorInductanceLq_Color { get; set; }//Q轴线电感

        //public virtual bool TextBoxEnabled { get; set; }//TextBox的使能

        #endregion

        #region 负载惯量辨识
        public virtual string InertiaIdetificationStartValue { get; set; }//惯量辨识起始值
        public virtual string LoadInertiaRatio { get; set; }//负载惯量比
        #endregion

        #region 参数自整定
        public virtual ObservableCollection<string> AutoTunningModeType { get; set; }//自整定应用模式类型
        public virtual string SelectedAutoTunningModeType { get; set; }//选中的自整定应用模式类型
        public virtual string AutoTunningDistance { get; set; }//自称定移动距离
        public virtual ObservableCollection<string> AutoTunningRigidityLevelType { get; set; }//自整定刚性等级
        public virtual string SelectedAutoTunningRigidityLevelType { get; set; }//选中的自整定刚性等级
        //public virtual string AutoTunningRigidityLevel { get; set; }//自整定刚性等级
        public virtual string PositionCompletionWidth { get; set; }//定位完成宽度
        #endregion

        #region 参数自整定显示内容
        public virtual string PositionLoopGain { get; set; }//位置环增益
        public virtual string SpeedLoopGain { get; set; }//速度环增益
        public virtual string SpeedLoopTimeConstant { get; set; }//速度环积分时间常数
        public virtual string FirstTrqcmdFilterTime { get; set; }//第一转矩指令滤波时间
        public virtual ObservableCollection<string> VibrationSuppressionOption { get; set; }//A型抑振控制选择
        public virtual string SelectedVibrationSuppressionOption { get; set; }//选中的A型抑振控制
        public virtual string VibsupFreq { get; set; }//A型抑振频率
        public virtual string VibsupGainComp { get; set; }//A型抑振增益补偿
        public virtual string VibsupDampingGain { get; set; }//A型抑振阻尼增益
        public virtual ObservableCollection<string> ModelFollowingControlSwitch { get; set; }//模型追踪控制开关       
        public virtual string SelectedModelFollowingControlSwitch { get; set; }//选中的模型追踪控制开关
        public virtual string ModelFollowingControlGain { get; set; }//模型追踪控制增益
        public virtual string MFCGainCorrection { get; set; }//模型追踪控制增益补偿
        public virtual string VibrationSuppressionFrequencyA { get; set; }//振动抑制1频率A  

        public virtual string PositionLoopGain_Identification { get; set; }//位置环增益
        public virtual string SpeedLoopGain_Identification { get; set; }//速度环增益
        public virtual string SpeedLoopTimeConstant_Identification { get; set; }//速度环积分时间常数
        public virtual string FirstTrqcmdFilterTime_Identification { get; set; }//第一转矩指令滤波时间
        #endregion

        #region 编码器
        public virtual ObservableCollection<string> EncoderType { get; set; }//编码器类型
        public virtual string SelectedEncoderType { get; set; }//选中的编码器类型

        public virtual string ABSEncoderSingleTurnBit { get; set; }//编码器单圈分辨率
        public virtual string ABSEncoderMultiTurnBit { get; set; }//编码器多圈分辨率
        public virtual string ABSEncoderOffset { get; set; }//绝对式编码器偏置
        public virtual string ABZEncoderPulses { get; set; }//ABZ编码器脉冲数

        public virtual string PitchMotorEncoderLines { get; set; }//ABZ编码器脉冲数
        #endregion

        #endregion

        #region 构造函数
        public InertiaIdentificationParameterSelfTunningViewModel()
        {
            ViewModelSet.InertiaIdentificationParameterSelfTunning = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：InertiaIdentificationParameterSelfTunningLoaded
        //函数功能：InertiaIdentificationParameterSelfTunning控件Loaded
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void InertiaIdentificationParameterSelfTunningLoaded()
        {
            int iRet = -1;

            try
            {
                IsEdit = false;

                //TextBoxEnabled = false;  //由Lilbert添加TextBox使能
                //下拉列表初始化
                ComboBoxInitialize();

                //时钟初始化
                TimerInitialize();

                RefreshParameterSelfTunningDriectionFlag(Status: "ParameterSelfTunningInitialize");

                //电机负载惯量辨识和参数自整定标志位初始化
                RefreshParameterInertiaIdentificationParameterSelfTunningIdentificationFlag(Status: "Initialized");

                //读识别前参数
                ReadMotorInertiaIdentificationParameterSelfTunningIdentification(IsListen: false);
              
                if (IsInitialized == true)
                {
                    //标志位更新
                    IsInitialized = false;
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadInertiaIdentificationParameterSelfTunningParameter("All");
                    }
                    else
                    {
                        GetDefaultMotorInertiaIdentificationParameterSelfTunningParameter("All");
                    }                                                    
                }
                else
                {
                    //标志位更新
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadInertiaIdentificationParameterSelfTunningParameter("All");
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }
                }               
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.INERTIALIDENTIFICATIONPARAMETERSELFTUNNING_LOADED, "InertiaIdentificationParameterSelfTunningLoaded", ex);
            }          
        }

        //*************************************************************************
        //函数名称：InertiaIdentificationParameterSelfTunningUnloaded
        //函数功能：界面退出
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.23
        //*************************************************************************
        public void InertiaIdentificationParameterSelfTunningUnloaded()
        {

            IsUnloaded = true;

            RefreshParameterInertiaIdentificationParameterSelfTunningIdentificationFlag(Status: "Completed");
        }

        //*************************************************************************
        //函数名称：MotorInertiaIdentificationStart
        //函数功能：电机负载惯量辨识
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void MotorInertiaIdentificationStart()
        {
            int iRet = -1;
            bool bEEPROM = false;

            string strStatusWord = OthersHelper.GetCurrentValueOfIndex("0x604100");//获取状态字
            if (SoftwareStateParameterSet.ServoStatus == ServoStatus.ERROR || OthersHelper.GetCurrentValueOfIndex("0x604100") == "520" || OthersHelper.GetCurrentValueOfIndex("0x604100") == "536")
            {
                ViewModelSet.Main?.ShowHintInfo("伺服故障，请清除伺服故障...");
            }
            else
            {
                if (IsInitialized_ForMotorInertiaIdentification)
                {
                    //判断串口状态
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(1000);
                        return;
                    }

                    //返回初始位置-为了MessageBox弹窗
                    if (!OthersHelper.GetWindowsStartupPosition())
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }

                    //判断是否写入EEPROM
                    if (MessageBoxService.ShowMessage("这将会使能电机并正反转运动...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                    {
                        bEEPROM = true;
                        OthersHelper.OperatingControl("Auto Tunning", "1", TaskName.ParameterInertiaIdentificationOperatingMode, PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING, bEEPROM);
                    }
                }
                else
                {
                    OthersHelper.OperatingControl("Auto Tunning", "0", TaskName.ParameterIdentificationOperatingOperatingEnd, PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING);
                }
            }
           
        }

        //*************************************************************************
        //函数名称：MotorParameterSelfTunningStart
        //函数功能：电机参数自整定
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.17
        //*************************************************************************
        public void MotorParameterSelfTunningStart()
        {
            int iRet = -1;
            bool bEEPROM = false;

            string strStatusWord = OthersHelper.GetCurrentValueOfIndex("0x604100");//获取状态字
            if (SoftwareStateParameterSet.ServoStatus == ServoStatus.ERROR || OthersHelper.GetCurrentValueOfIndex("0x604100") == "520" || OthersHelper.GetCurrentValueOfIndex("0x604100") == "536")
            {
                ViewModelSet.Main?.ShowHintInfo("伺服故障，请清除伺服故障...");
            }
            else
            {
                if (IsInitialized_ForParameterSelfTunningIdentification)
                {
                    //判断串口状态
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(1000);
                        return;
                    }

                    //返回初始位置-为了MessageBox弹窗
                    if (!OthersHelper.GetWindowsStartupPosition())
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }

                    //判断是否写入EEPROM
                    if (MessageBoxService.ShowMessage("这将会使能电机并正反转运动...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                    {
                        bEEPROM = true;
                        //OthersHelper.OperatingControl("Auto Tunning", "2", TaskName.ParameterSelfTunningOperatingMode, PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING, bEEPROM);
                        OthersHelper.OperatingControl("Auto Tunning", "2", TaskName.ParameterSelfTunningOperatingMode, PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING, bEEPROM);
                    }
                }
                else
                {
                    OthersHelper.OperatingControl("Auto Tunning", "0", TaskName.ParameterSelfTunningOperatingEnd, PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING);
                    RefreshParameterSelfTunningDriectionFlag("ParameterSelfTunningSwitch");
                }
            }
           
        }

        //*************************************************************************
        //函数名称：WriteInertiaIdentificationParameter
        //函数功能：写电机负载惯量辨识参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void WriteInertiaIdentificationParameter(string strCategory)
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary_InertiaIdentification(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                //iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                iRet = OthersHelper.CheckInputParametersCorrected_For_MotorFeedback(ref lstParameterInfo_ForWrite);     //由Lilbert于2022.11.24更改提示信息方式
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING, TaskName.MotorInertiaIdentification, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.INERTIALIDENTIFICATION_WRITE_PARAMETER, "WriteInertiaIdentificationParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：WriteParameterSelfTunningParameter
        //函数功能：写电机参数自整定参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void WriteParameterSelfTunningParameter(string strCategory)
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary_ParameterSelfTunning(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                //iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                iRet = OthersHelper.CheckInputParametersCorrected_For_MotorFeedback(ref lstParameterInfo_ForWrite);     //由Lilbert于2022.11.24更改提示信息方式
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING, TaskName.MotorParameterSelfTunning, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERSELFTUNNING_WRITE_PARAMETER, "WriteParameterSelfTunningParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：WriteMotorFeedbackAutoLearnParameter
        //函数功能：写电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.01
        //*************************************************************************
        public void WriteMotorFeedbackAutoLearnParameter(string strCategory)
        {
            int iRet = -1;
            bool bEEPROM = false;          
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MOTORFEEDBACKAUTOLEARN, TaskName.MotorFeedbackAutoLearn, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACKAUTOLEARN_WRITE_PARAMETER, "WriteMotorFeedbackAutoLearnParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：RefreshParameterInertiaIdentificationParameterSelfTunningIdentificationFlag
        //函数功能：更新ParameterIdentification电机负载惯量辨识和参数自整定控制标志位
        //
        //输入参数：string Status    当前状态
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void RefreshParameterInertiaIdentificationParameterSelfTunningIdentificationFlag(string Status)
        {
            if (Status == "Initialized")//初始化
            {
                //CheckBoxInitialized();
                Timer_System.IsEnabled = false;//时钟关

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_InertiaIdentification = false;//监听回调关
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_InertiaIdentification = false;//没有完成参数辨识 

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_ParameterSelfTunning = false;//监听回调关
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_ParameterSelfTunning = false;//没有完成参数辨识   

                //ModifyParameterButtonEnabled = false;
                //HintVisibility = ControlVisibility.Hidden;
                //ParameterIdentificationSwitchHint = "辨识开始";
            }
            else if (Status == "Switched")//辨识开始关闭
            {
                //string strActualEnable = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Actual Enable", "Index"));
                Timer_System.IsEnabled = true;//时钟开

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_InertiaIdentification = true;//监听回调开
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_InertiaIdentification = false;//没有完成参数辨识

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_ParameterSelfTunning = true;//监听回调开
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_ParameterSelfTunning = false;//没有完成参数辨识                 
            }
            else if (Status == "Completed")
            {
                Timer_System.IsEnabled = false;//时钟关

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_InertiaIdentification = false;//监听回调关
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_InertiaIdentification = true;//完成参数辨识

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_ParameterSelfTunning = false;//监听回调关
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_ParameterSelfTunning = true;//完成参数辨识   

                //ModifyParameterButtonEnabled = true;
                //HintVisibility = ControlVisibility.Hidden;
                //ParameterIdentificationSwitchHint = "辨识开始";

                if (IsUnloaded)
                {

                }
                else
                {
                    IsUnloaded = false;
                    ShowNotification_CrossThread(2006);
                }
            }
            else if (Status == "Modified")
            {
                //ModifyParameterButtonEnabled = false;
                //HintVisibility = ControlVisibility.Hidden;
                //ParameterIdentificationSwitchHint = "辨识开始";

                ShowNotification_CrossThread(2007);
            }
            else if (Status == "Unloaded")
            {
                Timer_System.IsEnabled = false;
            }
        }

        //*************************************************************************
        //函数名称：RefreshParameterMotorParameterSelfTunningFlag
        //函数功能：更新ParameterIdentification电机参数自整定控制标志位
        //
        //输入参数：string Status    当前状态
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.21
        //*************************************************************************
        public void RefreshParameterMotorParameterSelfTunningFlag(string Status)
        {
            if (Status == "Initialized")//初始化
            {
                //CheckBoxInitialized();
                Timer_System.IsEnabled = false;//时钟关

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_ParameterSelfTunning = false;//监听回调关
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_ParameterSelfTunning = false;//没有完成参数辨识   

                //ModifyParameterButtonEnabled = false;
                //HintVisibility = ControlVisibility.Hidden;
                //ParameterIdentificationSwitchHint = "辨识开始";
            }
            else if (Status == "Switched")//辨识开始关闭
            {
                //string strActualEnable = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Actual Enable", "Index"));
                Timer_System.IsEnabled = true;//时钟开

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_ParameterSelfTunning = true;//监听回调开
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_ParameterSelfTunning = false;//没有完成参数辨识                 
            }
            else if (Status == "Completed")
            {
                Timer_System.IsEnabled = false;//时钟关

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_ParameterSelfTunning = false;//监听回调关
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_ParameterSelfTunning = true;//完成参数辨识   

                //ModifyParameterButtonEnabled = true;
                //HintVisibility = ControlVisibility.Hidden;
                //ParameterIdentificationSwitchHint = "辨识开始";

                //ShowNotification_CrossThread(3022);
            }
            else if (Status == "Failed")
            {
                Timer_System.IsEnabled = false;//时钟关

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_ParameterSelfTunning = false;//监听回调关
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_ParameterSelfTunning = true;//完成参数辨识   

                //ModifyParameterButtonEnabled = true;
                //HintVisibility = ControlVisibility.Hidden;
                //ParameterIdentificationSwitchHint = "辨识开始";

                //ShowNotification_CrossThread(3028);
            }
            else if (Status == "Modified")
            {
                //ModifyParameterButtonEnabled = false;
                //HintVisibility = ControlVisibility.Hidden;
                //ParameterIdentificationSwitchHint = "辨识开始";

                ShowNotification_CrossThread(2007);
            }
            else if (Status == "Unloaded")
            {
                Timer_System.IsEnabled = false;
            }
        }

        //*************************************************************************
        //函数名称：RefreshParameterMotorLineUVWSequenceIdentificationFlag
        //函数功能：更新ParameterIdentification电机负载惯量辨识控制标志位
        //
        //输入参数：string Status    当前状态
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.21
        //*************************************************************************
        public void RefreshParameterMotorInertiaIdentificationFlag(string Status)
        {
            if (Status == "Initialized")//初始化
            {
                //CheckBoxInitialized();
                Timer_System.IsEnabled = false;//时钟关

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_InertiaIdentification = false;//监听回调关
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_InertiaIdentification = false;//没有完成参数辨识   

                //ModifyParameterButtonEnabled = false;
                //HintVisibility = ControlVisibility.Hidden;
                //ParameterIdentificationSwitchHint = "辨识开始";
            }
            else if (Status == "Switched")//辨识开始关闭
            {
                //string strActualEnable = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Actual Enable", "Index"));
                Timer_System.IsEnabled = true;//时钟开

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_InertiaIdentification = true;//监听回调开
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_InertiaIdentification = false;//没有完成参数辨识                 
            }
            else if (Status == "Completed")
            {
                Timer_System.IsEnabled = false;//时钟关

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_InertiaIdentification = false;//监听回调关
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_InertiaIdentification = true;//完成参数辨识   

                //ModifyParameterButtonEnabled = true;
                //HintVisibility = ControlVisibility.Hidden;
                //ParameterIdentificationSwitchHint = "辨识开始";

                //ShowNotification_CrossThread(3023);
            }
            else if (Status == "Failed")
            {
                Timer_System.IsEnabled = false;//时钟关

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_InertiaIdentification = false;//监听回调关
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_InertiaIdentification = true;//完成参数辨识   

                //ModifyParameterButtonEnabled = true;
                //HintVisibility = ControlVisibility.Hidden;
                //ParameterIdentificationSwitchHint = "辨识开始";

                //ShowNotification_CrossThread(3027);
            }
            else if (Status == "Modified")
            {
                //ModifyParameterButtonEnabled = false;
                //HintVisibility = ControlVisibility.Hidden;
                //ParameterIdentificationSwitchHint = "辨识开始";

                ShowNotification_CrossThread(2007);
            }
            else if (Status == "Unloaded")
            {
                Timer_System.IsEnabled = false;
            }
        }

        public void CallBackForListenInertiaIdentification()
        {
            if (MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_InertiaIdentification)//监听回调开
            {
                //是否完成参数识别
                string strAatState = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatState", "Index"));
                switch (strAatState)
                {
                    //case "0":
                    //    //初始状态
                    //    InertiaIdentificationInformation = "提示:初始状态";
                    //    break;                   
                    //case "80":
                    //    //电机负载惯量辨识中
                    //    InertiaIdentificationInformation = "提示:电机负载惯量辨识中";
                    //    break;
                    case "33":
                        //电机负载惯量报警
                        InertiaIdentificationInformation = "提示:电机负载惯量报警";
                        RefreshParameterMotorInertiaIdentificationFlag(Status: "Completed");

                        //获取参数辨识参数
                        ReadMotorParameterMotorInertiaIdentification(IsListen: false);
                        break;
                    case "34":
                        //电机负载惯量异常中断
                        InertiaIdentificationInformation = "提示:电机负载惯量异常中断";
                        RefreshParameterMotorInertiaIdentificationFlag(Status: "Completed");

                        //获取参数辨识参数
                        ReadMotorParameterMotorInertiaIdentification(IsListen: false);
                        break;
                    case "1":
                        //电机负载惯量辨识成功
                        InertiaIdentificationInformation = "提示:电机负载惯量辨识成功";
                        RefreshParameterMotorInertiaIdentificationFlag(Status: "Completed");

                        //获取参数辨识参数
                        ReadMotorParameterMotorInertiaIdentification(IsListen: false);

                        break;
                    case "49":
                        //电机负载惯量辨识失败
                        InertiaIdentificationInformation = "提示:负载惯量辨识失败";
                        IsInitialized_ForMotorInertiaIdentification = true;

                        RefreshParameterMotorInertiaIdentificationFlag(Status: "Failed");

                        //获取参数辨识参数
                        ReadMotorParameterMotorInertiaIdentification(IsListen: false);
                        break;
                    default:
                        //电机负载惯量辨识进行中
                        InertiaIdentificationInformation = "提示:电机负载惯量辨识进行中";
                        break;
                }                
            }           
        }

        public void CallBackForListenParameterSelfTunningIdentification()
        {
            if (MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_ParameterSelfTunning)//监听回调开
            {
                //是否完成参数识别
                string strAatState = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatState", "Index"));
                switch (strAatState)
                {
                    //case "0":
                    //    //初始状态
                    //    ParameterSelfTunningInformation = "提示:初始状态";
                    //    break;                  
                    case "33":
                        //电机参数自整定报警
                        ParameterSelfTunningInformation = "提示:电机参数自整定报警";
                        ParameterSelfTunningSwitchHint = "开始参数自整定";
                        RefreshParameterMotorParameterSelfTunningFlag(Status: "Completed");

                        //获取参数辨识参数
                        ReadMotorParameterMotorParameterSelfTunning(IsListen: false);
                        break;
                    case "34":
                        //电机参数自整定异常中断
                        ParameterSelfTunningInformation = "提示:电机参数自整定异常中断";
                        ParameterSelfTunningSwitchHint = "开始参数自整定";
                        RefreshParameterMotorParameterSelfTunningFlag(Status: "Completed");

                        //获取参数辨识参数
                        ReadMotorParameterMotorParameterSelfTunning(IsListen: false);
                        break;
                    case "1":
                        //电机参数自整定成功
                        ParameterSelfTunningInformation = "提示:电机参数自整定成功";
                        ParameterSelfTunningSwitchHint = "开始参数自整定";
                        RefreshParameterMotorParameterSelfTunningFlag(Status: "Completed");

                        //获取参数辨识参数
                        ReadMotorParameterMotorParameterSelfTunning(IsListen: false);

                        break;
                    case "17":
                        //电机参数自整定失败
                        ParameterSelfTunningInformation = "提示:电机参数自整定失败";
                        ParameterSelfTunningSwitchHint = "开始参数自整定";
                        IsInitialized_ForParameterSelfTunningIdentification = true;

                        RefreshParameterMotorParameterSelfTunningFlag(Status: "Failed");

                        //获取参数辨识参数
                        ReadMotorParameterMotorParameterSelfTunning(IsListen: false);
                        break;
                    default:
                        //电机参数自整定进行中
                        ParameterSelfTunningInformation = "提示:电机参数自整定进行中";
                        break;
                }
            }
        }

        //*************************************************************************
        //函数名称：EvaluationMotorParameterInertiaIdentificationParameterSelfTunningIdentification
        //函数功能：回调函数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.21
        //*************************************************************************
        public void EvaluationMotorParameterInertiaIdentificationParameterSelfTunningIdentification()
        {
            if (!MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_InertiaIdentification) //没有参数识别
            {
                //识别前赋值                
                InertiaIdetificationStartValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Inertia Idetification Start Value", "Index"));//电机负载惯量辨识起始值   
                LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//电机负载惯量比

                SelectedAutoTunningModeType = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatMode", "Index"));//自整定应用模式  
                AutoTunningDistance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatDistance", "Index"));//自整定移动距离  
                SelectedAutoTunningRigidityLevelType = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Index"));//自整定刚性等级  
                PositionCompletionWidth = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Completion Width", "Index"));//定位完成宽度 

                //参数自整定显示内容
                PositionLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//位置环增益
                SpeedLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//速度环增益
                SpeedLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//速度环积分时间常数
                FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩指令滤波时间
                SelectedVibrationSuppressionOption = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Index"));//A型抑振控制选择
                VibsupFreq = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Index"));//A型抑振频率  
                VibsupGainComp = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Index"));//A型抑振增益补偿
                VibsupDampingGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Index"));//A型抑振阻尼增益
                SelectedModelFollowingControlSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Index"));//模型追踪控制开关
                ModelFollowingControlGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Index"));//模型追踪控制增益
                MFCGainCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Index"));//模型追踪控制增益补偿
                VibrationSuppressionFrequencyA = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Index"));//振动抑制1频率A 

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.InertiaIdetificationStartValue = InertiaIdetificationStartValue;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.LoadInertiaRatio = LoadInertiaRatio;

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SelectedAutoTunningModeType = SelectedAutoTunningModeType;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.AutoTunningDistance = AutoTunningDistance;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SelectedAutoTunningRigidityLevelType = SelectedAutoTunningRigidityLevelType;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.PositionCompletionWidth = PositionCompletionWidth;

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.PositionLoopGain = PositionLoopGain;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SpeedLoopGain = SpeedLoopGain;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SpeedLoopTimeConstant = SpeedLoopTimeConstant;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.FirstTrqcmdFilterTime = FirstTrqcmdFilterTime;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SelectedVibrationSuppressionOption = SelectedVibrationSuppressionOption;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.VibsupFreq = VibsupFreq;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.VibsupGainComp = VibsupGainComp;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.VibsupDampingGain = VibsupDampingGain;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SelectedModelFollowingControlSwitch = SelectedModelFollowingControlSwitch;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.ModelFollowingControlGain = ModelFollowingControlGain;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.MFCGainCorrection = MFCGainCorrection;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.VibrationSuppressionFrequencyA = VibrationSuppressionFrequencyA;


            }
            else//参数识别
            {
                //识别后赋值与字体颜色控制                   
                InertiaIdetificationStartValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Inertia Idetification Start Value", "Index"));//电机负载惯量辨识起始值     
                LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//电机负载惯量辨识起始值

                SelectedAutoTunningModeType = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatMode", "Index"));//自整定应用模式  
                AutoTunningDistance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatDistance", "Index"));//自整定移动距离  
                SelectedAutoTunningRigidityLevelType = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Index"));//自整定刚性等级  
                PositionCompletionWidth = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Completion Width", "Index"));//定位完成宽度 

                //参数自整定显示内容
                PositionLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//位置环增益
                SpeedLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//速度环增益
                SpeedLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//速度环积分时间常数
                FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩指令滤波时间
                SelectedVibrationSuppressionOption = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Index"));//A型抑振控制选择
                VibsupFreq = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Index"));//A型抑振频率  
                VibsupGainComp = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Index"));//A型抑振增益补偿
                VibsupDampingGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Index"));//A型抑振阻尼增益
                SelectedModelFollowingControlSwitch = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Index"));//模型追踪控制开关
                ModelFollowingControlGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Index"));//模型追踪控制增益
                MFCGainCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Index"));//模型追踪控制增益补偿
                VibrationSuppressionFrequencyA = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Index"));//振动抑制1频率A                                         

                //字体颜色提示
                //ColorControl();

                //识别标志位初始
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_InertiaIdentification = false;
            }

        }

        //*************************************************************************
        //函数名称：EvaluationMotorInertiaIdentification
        //函数功能：回调函数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.21
        //*************************************************************************
        public void EvaluationMotorInertiaIdentification()
        {

            if (!MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_InertiaIdentification) //没有参数识别
            {
                //识别前赋值 
                InertiaIdetificationStartValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Inertia Idetification Start Value", "Index"));//电机负载惯量辨识起始值    
                LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//电机负载惯量比                

                //MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.InertiaIdetificationStartValue = InertiaIdetificationStartValue;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.InertiaIdetificationStartValue = InertiaIdetificationStartValue;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.LoadInertiaRatio = LoadInertiaRatio;
              

            }
            else//参数识别
            {
                //识别后赋值与字体颜色控制                                   
                InertiaIdetificationStartValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Inertia Idetification Start Value", "Index"));//电机负载惯量辨识起始值  
                LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//电机负载惯量比                     

                //字体颜色提示
                //ColorControl();

                //识别标志位初始
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_InertiaIdentification = false;
            }
        }

        //*************************************************************************
        //函数名称：EvaluationMotorParameterSelfTunningIdentification
        //函数功能：回调函数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.21
        //*************************************************************************
        public void EvaluationMotorParameterSelfTunningIdentification()
        {
            string strAutoTunningModeTypeValue = null;
            string strAutoTunningRigidityLevelTypeValue = null;

            string strVibrationSuppressionOptionValue = null;
            string strModelFollowingControlSwitchValue = null;

            if (!MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_ParameterSelfTunning) //没有参数识别
            {
                //识别前赋值
                //SelectedAutoTunningModeType = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatMode", "Index"));//自整定应用模式  
                AutoTunningDistance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatDistance", "Index"));//自整定移动距离  
                //SelectedAutoTunningRigidityLevelType = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Index"));//自整定刚性等级  
                PositionCompletionWidth = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Completion Width", "Index"));//定位完成宽度 

                strAutoTunningRigidityLevelTypeValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Index"));//自整定刚性等级 
                switch (strAutoTunningRigidityLevelTypeValue)
                {
                    case "1":
                        SelectedAutoTunningRigidityLevelType = "一级";
                        break;
                    case "2":
                        SelectedAutoTunningRigidityLevelType = "二级";
                        break;
                    case "3":
                        SelectedAutoTunningRigidityLevelType = "三级";
                        break;
                    default:
                        break;
                }

                strAutoTunningModeTypeValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatMode", "Index"));//自整定应用模式
                switch (strAutoTunningModeTypeValue)
                {
                    case "1":
                        SelectedAutoTunningModeType = "标准模式";
                        break;
                    case "2":
                        SelectedAutoTunningModeType = "定位用途模式";
                        break;
                    case "3":
                        SelectedAutoTunningModeType = "不超调的定位模式";
                        break;
                    case "4":
                        SelectedAutoTunningModeType = "速度模式";
                        break;
                    default:
                        break;
                }

                //参数自整定显示内容
                PositionLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//位置环增益
                SpeedLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//速度环增益
                SpeedLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//速度环积分时间常数
                FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩指令滤波时间

                VibsupFreq = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Index"));//A型抑振频率  
                VibsupGainComp = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Index"));//A型抑振增益补偿
                VibsupDampingGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Index"));//A型抑振阻尼增益

                ModelFollowingControlGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Index"));//模型追踪控制增益
                MFCGainCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Index"));//模型追踪控制增益补偿
                VibrationSuppressionFrequencyA = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Index"));//振动抑制1频率A

                strVibrationSuppressionOptionValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Index"));//A型抑振控制选择
                switch (strVibrationSuppressionOptionValue)
                {
                    case "16":
                        SelectedVibrationSuppressionOption = "关";
                        VibrationSuppressionOption_Color = BackgroundState.Black;
                        break;
                    case "17":
                        SelectedVibrationSuppressionOption = "开";
                        VibrationSuppressionOption_Color = BackgroundState.Red;
                        break;
                    default:
                        break;
                }

                strModelFollowingControlSwitchValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Index"));//模型追踪控制开关
                switch (strModelFollowingControlSwitchValue)
                {
                    case "256":
                        SelectedModelFollowingControlSwitch = "关";
                        ModelFollowingControlSwitch_Color = BackgroundState.Black;
                        break;
                    case "257":
                        SelectedModelFollowingControlSwitch = "开";
                        ModelFollowingControlSwitch_Color = BackgroundState.Red;
                        break;
                    default:
                        break;
                }

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SelectedAutoTunningModeType = SelectedAutoTunningModeType;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.AutoTunningDistance = AutoTunningDistance;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SelectedAutoTunningRigidityLevelType = SelectedAutoTunningRigidityLevelType;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.PositionCompletionWidth = PositionCompletionWidth;

                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.PositionLoopGain = PositionLoopGain;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SpeedLoopGain = SpeedLoopGain;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SpeedLoopTimeConstant = SpeedLoopTimeConstant;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.FirstTrqcmdFilterTime = FirstTrqcmdFilterTime;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SelectedVibrationSuppressionOption = SelectedVibrationSuppressionOption;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.VibsupFreq = VibsupFreq;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.VibsupGainComp = VibsupGainComp;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.VibsupDampingGain = VibsupDampingGain;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.SelectedModelFollowingControlSwitch = SelectedModelFollowingControlSwitch;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.ModelFollowingControlGain = ModelFollowingControlGain;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.MFCGainCorrection = MFCGainCorrection;
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.VibrationSuppressionFrequencyA = VibrationSuppressionFrequencyA;

            }
            else//参数识别
            {
                //识别后赋值与字体颜色控制                                   
                //SelectedAutoTunningModeType = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatMode", "Index"));//自整定应用模式  
                AutoTunningDistance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatDistance", "Index"));//自整定移动距离  
                //AutoTunningRigidityLevel = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Index"));//自整定刚性等级  
                PositionCompletionWidth = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Completion Width", "Index"));//定位完成宽度  

                strAutoTunningRigidityLevelTypeValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Index"));//自整定刚性等级 
                switch (strAutoTunningRigidityLevelTypeValue)
                {
                    case "1":
                        SelectedAutoTunningRigidityLevelType = "一级";
                        break;
                    case "2":
                        SelectedAutoTunningRigidityLevelType = "二级";
                        break;
                    case "3":
                        SelectedAutoTunningRigidityLevelType = "三级";
                        break;                  
                    default:
                        break;
                }

                strAutoTunningModeTypeValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatMode", "Index"));//自整定应用模式
                switch (strAutoTunningModeTypeValue)
                {
                    case "1":
                        SelectedAutoTunningModeType = "标准模式";
                        break;
                    case "2":
                        SelectedAutoTunningModeType = "定位用途模式";
                        break;
                    case "3":
                        SelectedAutoTunningModeType = "不超调的定位模式";
                        break;
                    case "4":
                        SelectedAutoTunningModeType = "速度模式";
                        break;
                    default:
                        break;
                }

                //参数自整定显示内容
                //PositionLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//位置环增益
                //SpeedLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//速度环增益
                //SpeedLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//速度环积分时间常数
                //FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩指令滤波时间
                PositionLoopGain_Identification = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//位置环增益
                SpeedLoopGain_Identification = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//速度环增益
                SpeedLoopTimeConstant_Identification = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//速度环积分时间常数
                FirstTrqcmdFilterTime_Identification = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩指令滤波时间

                VibsupFreq = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Index"));//A型抑振频率  
                VibsupGainComp = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Index"));//A型抑振增益补偿
                VibsupDampingGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Index"));//A型抑振阻尼增益

                ModelFollowingControlGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Index"));//模型追踪控制增益
                MFCGainCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Index"));//模型追踪控制增益补偿
                VibrationSuppressionFrequencyA = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Index"));//振动抑制1频率A

                strVibrationSuppressionOptionValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Index"));//A型抑振控制选择
                switch (strVibrationSuppressionOptionValue)
                {
                    case "16":
                        SelectedVibrationSuppressionOption = "关";
                        VibrationSuppressionOption_Color = BackgroundState.Black;
                        break;
                    case "17":
                        SelectedVibrationSuppressionOption = "开";
                        VibrationSuppressionOption_Color = BackgroundState.Red;
                        break;
                    default:
                        break;
                }

                strModelFollowingControlSwitchValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Index"));//模型追踪控制开关
                switch (strModelFollowingControlSwitchValue)
                {
                    case "256":
                        SelectedModelFollowingControlSwitch = "关";
                        ModelFollowingControlSwitch_Color = BackgroundState.Black;
                        break;
                    case "257":
                        SelectedModelFollowingControlSwitch = "开";
                        ModelFollowingControlSwitch_Color = BackgroundState.Red;
                        break;
                    default:
                        break;
                }

                //字体颜色提示
                ColorControl();

                //识别标志位初始
                MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsIndentification_ParameterSelfTunning = false;
            }
        }

        //*************************************************************************
        //函数名称：ColorControl
        //函数功能：颜色管理
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.02.14
        //*************************************************************************
        private void ColorControl()
        {
            if (PositionLoopGain == PositionLoopGain_Identification)
            {
                PositionLoopGain = PositionLoopGain_Identification;
                //MotorWindingResistance_Color = BackgroundState.Green;
            }
            else
            {
                PositionLoopGain = PositionLoopGain_Identification;
                PositionLoopGain_Color = BackgroundState.Red;
            }

            if (SpeedLoopGain == SpeedLoopGain_Identification)
            {
                SpeedLoopGain = SpeedLoopGain_Identification;
                //MotorInductanceLd_Color = BackgroundState.Green;
            }
            else
            {
                SpeedLoopGain = SpeedLoopGain_Identification;
                SpeedLoopGain_Color = BackgroundState.Red;
            }

            if (SpeedLoopTimeConstant == SpeedLoopTimeConstant_Identification)
            {
                SpeedLoopTimeConstant = SpeedLoopTimeConstant_Identification;
                //MotorInductanceLq_Color = BackgroundState.Green;
            }
            else
            {
                SpeedLoopTimeConstant = SpeedLoopTimeConstant_Identification;
                SpeedLoopTimeConstant_Color = BackgroundState.Red;
            }

            if (FirstTrqcmdFilterTime == FirstTrqcmdFilterTime_Identification)
            {
                FirstTrqcmdFilterTime = FirstTrqcmdFilterTime_Identification;
                //MotorInductanceLq_Color = BackgroundState.Green;
            }
            else
            {
                FirstTrqcmdFilterTime = FirstTrqcmdFilterTime_Identification;
                FirstTrqcmdFilterTime_Color = BackgroundState.Red;
            }

        }

        //*************************************************************************
        //函数名称：ReadInertiaIdentificationParameterSelfTunningParameter
        //函数功能：读电机负载惯量辨识和参数自整定参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void ReadInertiaIdentificationParameterSelfTunningParameter(string strCategory)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING, TaskName.ParameterInertiaIdentificationParameterSelfTunningIdentification, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORRINERTIALIDENTIFICATIONPARAMETERSELFTUINNING_READ_PARAMETER, "ReadInertiaIdentificationParameterSelfTunningParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadParameterSelfTunningParameter
        //函数功能：读参数自整定参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void ReadParameterSelfTunningParameter(string strCategory)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary_ParameterSelfTunning(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING, TaskName.MotorParameterSelfTunning, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORPARAMETERSELFTUNNING_READ_PARAMETER, "ReadParameterSelfTunningParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadInertiaIdentificationParameter
        //函数功能：读负载惯量辨识参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void ReadInertiaIdentificationParameter(string strCategory)
        {
            int iRet = -1;                
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary_InertiaIdentification(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
              
                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING, TaskName.MotorInertiaIdentification, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORINERTIALIDENTIFICATION_READ_PARAMETER, "ReadInertiaIdentificationParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultMotorInertiaIdentificationParameterSelfTunningParameter
        //函数功能：获取电机负载惯量辨识和参数自整定的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.17
        //*************************************************************************
        public void GetDefaultMotorInertiaIdentificationParameterSelfTunningParameter(string strCategory)
        {
            string strAutoTunningModeValue = null;
            string strAutoTunningRigidityLevelTypeValue = null;
            string strVibrationSuppressionOptionValue = null;
            string strModelFollowingControlSwitchValue = null;

            try
            {
                if (strCategory == "All")
                {                   
                    InertiaIdetificationStartValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Inertia Idetification Start Value", "Default");//惯量辨识起始值
                    LoadInertiaRatio = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Default");//负载惯量比

                    AutoTunningDistance = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatDistance", "Default"); //自整定移动距离                
                    //AutoTunningRigidityLevel = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Default"); //自整定刚性等级
                    PositionCompletionWidth = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position completion width", "Default"); //定位完成宽度

                    strAutoTunningRigidityLevelTypeValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Default"); //自整定刚性等级
                    if (strAutoTunningRigidityLevelTypeValue == "1")
                    {
                        SelectedAutoTunningRigidityLevelType = "一级";
                    }
                    else if (strAutoTunningRigidityLevelTypeValue == "2")
                    {
                        SelectedAutoTunningRigidityLevelType = "二级";
                    }
                    else if (strAutoTunningRigidityLevelTypeValue == "3")
                    {
                        SelectedAutoTunningRigidityLevelType = "三级";
                    }

                    strAutoTunningModeValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatMode", "Default");//自整定应用模式
                    if (strAutoTunningModeValue == "1")
                    {
                        SelectedAutoTunningModeType = "标准模式";
                    }
                    else if (strAutoTunningModeValue == "2")
                    {
                        SelectedAutoTunningModeType = "定位用途模式";
                    }
                    else if (strAutoTunningModeValue == "3")
                    {
                        SelectedAutoTunningModeType = "不超调的定位模式";
                    }
                    else if (strAutoTunningModeValue == "4")
                    {
                        SelectedAutoTunningModeType = "速度模式";
                    }

                    //参数自整定显示内容
                    PositionLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Default");//位置环增益
                    SpeedLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Default");//速度环增益
                    SpeedLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Default");//速度环积分时间常数
                    FirstTrqcmdFilterTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Default");//第一转矩指令滤波时间

                    VibsupFreq = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Default");//A型抑振频率  
                    VibsupGainComp = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Default");//A型抑振增益补偿
                    VibsupDampingGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Default");//A型抑振阻尼增益

                    ModelFollowingControlGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Default");//模型追踪控制增益
                    MFCGainCorrection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Default");//模型追踪控制增益补偿
                    VibrationSuppressionFrequencyA = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Default");//振动抑制1频率A

                    strVibrationSuppressionOptionValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Default");//A型抑振控制选择
                    if (strVibrationSuppressionOptionValue == "16")
                    {
                        SelectedVibrationSuppressionOption = "关";
                        VibrationSuppressionOption_Color = BackgroundState.Black;
                    }
                    else if (strVibrationSuppressionOptionValue == "17")
                    {
                        SelectedVibrationSuppressionOption = "开";
                        VibrationSuppressionOption_Color = BackgroundState.Red;
                    }

                    strModelFollowingControlSwitchValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Default");//模型追踪控制开关
                    if (strModelFollowingControlSwitchValue == "256")
                    {
                        SelectedModelFollowingControlSwitch = "关";
                        ModelFollowingControlSwitch_Color = BackgroundState.Black;
                    }
                    else if (strModelFollowingControlSwitchValue == "257")
                    {
                        SelectedModelFollowingControlSwitch = "开";
                        ModelFollowingControlSwitch_Color = BackgroundState.Red;
                    }
                }
                else
                {
                    InertiaIdetificationStartValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Inertia Idetification Start Value", "Default");//惯量辨识起始值
                    LoadInertiaRatio = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Default");//负载惯量比

                    AutoTunningDistance = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatDistance", "Default"); //自整定移动距离                
                    //AutoTunningRigidityLevel = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Default"); //自整定刚性等级
                    PositionCompletionWidth = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position completion width", "Default"); //定位完成宽度

                    strAutoTunningRigidityLevelTypeValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Default"); //自整定刚性等级
                    if (strAutoTunningRigidityLevelTypeValue == "1")
                    {
                        SelectedAutoTunningRigidityLevelType = "一级";
                    }
                    else if (strAutoTunningRigidityLevelTypeValue == "2")
                    {
                        SelectedAutoTunningRigidityLevelType = "二级";
                    }
                    else if (strAutoTunningRigidityLevelTypeValue == "3")
                    {
                        SelectedAutoTunningRigidityLevelType = "三级";
                    }
                    else
                    {
                        SelectedAutoTunningRigidityLevelType = "一级";
                    }

                    strAutoTunningModeValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatMode", "Default");//自整定应用模式
                    if (strAutoTunningModeValue == "1")
                    {
                        SelectedAutoTunningModeType = "标准模式";
                    }
                    else if (strAutoTunningModeValue == "2")
                    {
                        SelectedAutoTunningModeType = "定位用途模式";
                    }
                    else if (strAutoTunningModeValue == "3")
                    {
                        SelectedAutoTunningModeType = "不超调的定位模式";
                    }
                    else if (strAutoTunningModeValue == "4")
                    {
                        SelectedAutoTunningModeType = "速度模式";
                    }
                    else
                    {
                        SelectedAutoTunningModeType = "标准模式";
                    }

                    //参数自整定显示内容
                    PositionLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Default");//位置环增益
                    SpeedLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Default");//速度环增益
                    SpeedLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Default");//速度环积分时间常数
                    FirstTrqcmdFilterTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Default");//第一转矩指令滤波时间

                    VibsupFreq = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Default");//A型抑振频率  
                    VibsupGainComp = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Default");//A型抑振增益补偿
                    VibsupDampingGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Default");//A型抑振阻尼增益

                    ModelFollowingControlGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Default");//模型追踪控制增益
                    MFCGainCorrection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Default");//模型追踪控制增益补偿
                    VibrationSuppressionFrequencyA = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Default");//振动抑制1频率A

                    strVibrationSuppressionOptionValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Default");//A型抑振控制选择
                    if (strVibrationSuppressionOptionValue == "16")
                    {
                        SelectedVibrationSuppressionOption = "关";
                        VibrationSuppressionOption_Color = BackgroundState.Black;
                    }
                    else if (strVibrationSuppressionOptionValue == "17")
                    {
                        SelectedVibrationSuppressionOption = "开";
                        VibrationSuppressionOption_Color = BackgroundState.Red;
                    }
                    else
                    {
                        SelectedVibrationSuppressionOption = "关";
                        VibrationSuppressionOption_Color = BackgroundState.Black;
                    }

                    strModelFollowingControlSwitchValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Default");//模型追踪控制开关
                    if (strModelFollowingControlSwitchValue == "256")
                    {
                        SelectedModelFollowingControlSwitch = "关";
                        ModelFollowingControlSwitch_Color = BackgroundState.Black;
                    }
                    else if (strModelFollowingControlSwitchValue == "257")
                    {
                        SelectedModelFollowingControlSwitch = "开";
                        ModelFollowingControlSwitch_Color = BackgroundState.Red;
                    }
                    else
                    {
                        SelectedModelFollowingControlSwitch = "关";
                        ModelFollowingControlSwitch_Color = BackgroundState.Black;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.INERTIALIDENTIFICATIONPARAMETERSELFTUNNING_GET_DEFAULT_PARAMETER, "GetDefaultMotorInertiaIdentificationParameterSelfTunningParameter", ex);
            }
        }


        //*************************************************************************
        //函数名称：SaveMotorConfigFile
        //函数功能：保存电机配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.23
        //*************************************************************************
        public void SaveMotorConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            string strFileName = null;
            string strDateTime = null;

            try
            {
                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否保存到电机参数库中...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    strDateTime = DateTime.Now.ToString();
                    strFileName = "电机反馈_配置文件" + strDateTime.Replace("/", "").Replace(":", "").Replace(" ", "_");
                    strFilePath = FilePath.MotorLibrary + strFileName + ".xlsx";

                    //写入参数库日志
                    iRet = ExcelHelper.WriteIntoExcel(FilePath.MotorLibraryLog, GetMotorLogToDataTable(strDateTime, strFileName), ExcelType.MotorLibraryLog);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }

                    //写入参数库
                    iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetMotorConfigToDataTable(), ExcelType.MotorConfig);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(RET.ERROR);
                    }
                    else
                    {
                        ShowNotification(2002);
                    }
                }
                else
                {
                    //选择配置文件存放位置
                    iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.MotorConfig);    
                    if (iRet == RET.NO_EFFECT)
                    {
                        return;
                    }

                    //生成配置文件
                    iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetMotorConfigToDataTable(), ExcelType.MotorConfig);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ShowNotification(2002);
                    }
                    else
                    {
                        ShowNotification(2003);
                    }
                }             
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_SAVE_MOTOR_CONFIG_FILE, "SaveMotorConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：TimerInitialize
        //函数功能：时钟初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.14
        //*************************************************************************
        private void TimerInitialize()
        {
            Timer_System.Interval = TimeSpan.FromMilliseconds(TimerPeriod.InertiaIdentificationParameterSelfTunning);
            Timer_System.Tick += Timer_System_Tick;
            Timer_System.IsEnabled = false;
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary_MotorLineUVWSequenceAbsEncoderOffset
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        private void AddParameterInfoDictionary_InertiaIdentificationParameterSelfTunning(bool IsListen, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            if (IsListen)
            {
                dicParameterInfo.Add("AatState", null);//电机负载惯量辨识和参数自整定状态
            }
            else
            {                
                dicParameterInfo.Add("Inertia Identification Start Value", null);
                dicParameterInfo.Add("Load Inertia Ratio", null);

                dicParameterInfo.Add("AatDistance", null);
                dicParameterInfo.Add("AatLoadType", null);
                dicParameterInfo.Add("Position completion width", null);
                dicParameterInfo.Add("AatMode", null);

                dicParameterInfo.Add("Position Loop Gain", null);
                dicParameterInfo.Add("Speed Loop Gain", null);
                dicParameterInfo.Add("Speed Loop Time Constant", null);
                dicParameterInfo.Add("First Trqcmd Filter Time", null);
                dicParameterInfo.Add("Vibration Suppression Option", null);
                dicParameterInfo.Add("Vibsup Freq", null);
                dicParameterInfo.Add("Vibsup Gain Comp", null);
                dicParameterInfo.Add("Vibsup Damping Gain", null);
                dicParameterInfo.Add("Model Following Control Switch", null);
                dicParameterInfo.Add("Model Following Control Gain", null);
                dicParameterInfo.Add("MFC Gain Correction", null);
                dicParameterInfo.Add("Vibration Suppression 1 Frequency A", null);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary_ParameterSelfTunning
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.21
        //*************************************************************************
        private void AddParameterInfoDictionary_ParameterSelfTunning(bool IsListen, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            if (IsListen)
            {
                dicParameterInfo.Add("AatState", null);//电机参数自学习辨识状态
            }
            else
            {
                dicParameterInfo.Add("AatMode", null);
                dicParameterInfo.Add("AatDistance", null);
                dicParameterInfo.Add("AatLoadType", null);
                dicParameterInfo.Add("Position completion width", null);

                dicParameterInfo.Add("Position Loop Gain", null);
                dicParameterInfo.Add("Speed Loop Gain", null);
                dicParameterInfo.Add("Speed Loop Time Constant", null);
                dicParameterInfo.Add("First Trqcmd Filter Time", null);
                dicParameterInfo.Add("Vibration Suppression Option", null);
                dicParameterInfo.Add("Vibsup Freq", null);
                dicParameterInfo.Add("Vibsup Gain Comp", null);
                dicParameterInfo.Add("Vibsup Damping Gain", null);
                dicParameterInfo.Add("Model Following Control Switch", null);
                dicParameterInfo.Add("Model Following Control Gain", null);
                dicParameterInfo.Add("MFC Gain Correction", null);
                dicParameterInfo.Add("Vibration Suppression 1 Frequency A", null);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary_InertiaIdentification
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.21
        //*************************************************************************
        private void AddParameterInfoDictionary_InertiaIdentification(bool IsListen, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            if (IsListen)
            {
                dicParameterInfo.Add("AatState", null);//电机负载惯量辨识状态
            }
            else
            {
                dicParameterInfo.Add("Inertia Idetification Start Value", null);
                dicParameterInfo.Add("Load Inertia Ratio", null);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.14
        //*************************************************************************
        private void AddParameterInfoDictionary(bool IsListen, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            if (IsListen)
            {
                dicParameterInfo.Add("MotEstState", null);//电机参数自学习辨识状态
            }
            else
            {
                dicParameterInfo.Add("Motor Winding Resistance", null);
                dicParameterInfo.Add("Ld", null);
                dicParameterInfo.Add("Lq", null);                
            }
        }

        //*************************************************************************
        //函数名称：RefreshParameterSelfTunningFlag
        //函数功能：更新ParameterSelfTunning控制标志位
        //
        //输入参数：string Status    当前Jog状态
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.21
        //*************************************************************************
        public void RefreshParameterSelfTunningDriectionFlag(string Status)
        {
            string strAutoTunningCommand = null;

            if (Status == "ParameterSelfTunningInitialize")
            {
                //JogDirectionSet.Direction = null;
                //JogDirectionSet.IsContinuous = false;

                ParameterSelfTunningSwitchHint = "开始参数自整定";
                //IsJogButtonEnabled = false;
            }
            else if (Status == "ParameterSelfTunningSwitch")
            {
                ParameterSelfTunningSwitchHint = "停止参数自整定";
                strAutoTunningCommand = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Auto Tunning", "Index"));
                if (strAutoTunningCommand == "2")
                {
                    if (IsClosed)
                    {
                        ParameterSelfTunningSwitchHint = "开始参数自整定";
                        IsInitialized_ForParameterSelfTunningIdentification = true;
                        MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_InertiaIdentification = false;
                        MotorParameterInertiaIdentificationParameterSelfTunningIdentificationSet.IsCallBackOn_ParameterSelfTunning = false;

                        //OthersHelper.OperatingControl("Motor Jog Command", "16", TaskName.JogDriectionSwitch, PageName.MOTORDRIECTIONJOGPAGE);

                    }
                    else
                    {
                        ParameterSelfTunningSwitchHint = "停止参数自整定";
                        //IsClosed = true;
                        //IsJogButtonEnabled = true;
                        //IsInitialized_ForParameterSelfTunningIdentification = true;

                        //ShowNotification_CrossThread(2004);
                    }
                }
                else
                {
                    if (IsClosed)
                    {
                        //OthersHelper.OperatingControl("Motor Jog Command", "0", TaskName.JogDriectionOperatingEnd, PageName.MOTORDRIECTIONJOGPAGE);
                    }
                    else
                    {
                        ParameterSelfTunningSwitchHint = "开始参数自整定";
                        //IsJogButtonEnabled = false;
                        //IsJogDriectionInitialized = true;
                        IsInitialized_ForParameterSelfTunningIdentification = true;

                        //ShowNotification_CrossThread(2005);
                    }
                }
            }                     
        }

        //*************************************************************************
        //函数名称：GetMotorConfigFile
        //函数功能：获取电机配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void GetMotorConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            DataTable dt = null;

            try
            {
                if (MessageBoxService.ShowMessage("是否从电机参数库中加载并下载...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    MotorLibraryNavigation();
                    return;   
                }

                //获取配置文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(strFilePath, ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //判断是否导入正确的配置文件
                if (dt.Columns[3].ColumnName != FileInterface.MOTORFEEDBACK)
                {
                    ShowNotification(2001);
                    return;
                }
                 
                //导入参数          
                iRet = GetMotorConfigFromDataTable(dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //是否下载  
                OthersHelper.GetWindowsStartupPosition();
                if (MessageBoxService.ShowMessage("是否下载当前导入的全部电机反馈参数...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    WriteMotorFeedbackAutoLearnParameter("All");
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_GET_MOTOR_CONFIG_FILE, "GetMotorConfigFile", ex);
            }
        }       

        //*************************************************************************
        //函数名称：EvaluationMotorInertiaIdentificationParameterSelfTunningParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void EvaluationMotorInertiaIdentificationParameterSelfTunningParameter()
        {
            string strAutoTunningModeTypeValue = null;
            string strAutoTunningRigidityLevelTypeValue = null;
            string strVibrationSuppressionOptionValue = null;
            string strModelFollowingControlSwitchValue = null;

            if (IsEvaluationAll)
            {
                //标志位更新
                IsEvaluationAll = false;

                //赋值
                                
                InertiaIdetificationStartValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Inertia Idetification Start Value", "Index"));//惯量辨识起始值
                LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//负载惯量比

                AutoTunningDistance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatDistance", "Index"));//自整定移动距离
                //AutoTunningRigidityLevel = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Index"));//自整定刚性等级
                PositionCompletionWidth = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position completion width", "Index"));//定位完成宽度

                strAutoTunningRigidityLevelTypeValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Default"); //自整定刚性等级
                if (strAutoTunningRigidityLevelTypeValue == "1")
                {
                    SelectedAutoTunningRigidityLevelType = "一级";
                }
                else if (strAutoTunningRigidityLevelTypeValue == "2")
                {
                    SelectedAutoTunningRigidityLevelType = "二级";
                }
                else if (strAutoTunningRigidityLevelTypeValue == "3")
                {
                    SelectedAutoTunningRigidityLevelType = "三级";
                }

                strAutoTunningModeTypeValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatMode", "Index"));//自整定应用模式
                switch (strAutoTunningModeTypeValue)
                {
                    case "1":
                        SelectedAutoTunningModeType = "标准模式";
                        break;
                    case "2":
                        SelectedAutoTunningModeType = "定位用途模式";
                        break;
                    case "3":
                        SelectedAutoTunningModeType = "不超调的定位模式";
                        break;
                    case "4":
                        SelectedAutoTunningModeType = "速度模式";
                        break;
                    default:
                        break;
                }

                //参数自整定显示内容
                PositionLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//位置环增益
                SpeedLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//速度环增益
                SpeedLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//速度环积分时间常数
                FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩指令滤波时间

                VibsupFreq = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Index"));//A型抑振频率  
                VibsupGainComp = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Index"));//A型抑振增益补偿
                VibsupDampingGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Index"));//A型抑振阻尼增益

                ModelFollowingControlGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Index"));//模型追踪控制增益
                MFCGainCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Index"));//模型追踪控制增益补偿
                VibrationSuppressionFrequencyA = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Index"));//振动抑制1频率A

                strVibrationSuppressionOptionValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Index"));//A型抑振控制选择
                if (strVibrationSuppressionOptionValue == "16")
                {
                    SelectedVibrationSuppressionOption = "关";
                    VibrationSuppressionOption_Color = BackgroundState.Black;
                }
                else if (strVibrationSuppressionOptionValue == "17")
                {
                    SelectedVibrationSuppressionOption = "开";
                    VibrationSuppressionOption_Color = BackgroundState.Red;
                }

                strModelFollowingControlSwitchValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Index"));//模型追踪控制开关
                if (strModelFollowingControlSwitchValue == "256")
                {
                    SelectedModelFollowingControlSwitch = "关";
                    ModelFollowingControlSwitch_Color = BackgroundState.Black;
                }
                else if (strModelFollowingControlSwitchValue == "257")
                {
                    SelectedModelFollowingControlSwitch = "开";
                    ModelFollowingControlSwitch_Color = BackgroundState.Red;
                }
            }
            else
            {
                
                InertiaIdetificationStartValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Inertia Idetification Start Value", "Index"));//惯量辨识起始值
                LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//负载惯量比

                AutoTunningDistance = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatDistance", "Index"));//自整定移动距离
                //AutoTunningRigidityLevel = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Index"));//自整定刚性等级
                PositionCompletionWidth = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position completion width", "Index"));//定位完成宽度

                strAutoTunningRigidityLevelTypeValue = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatLoadType", "Default"); //自整定刚性等级
                if (strAutoTunningRigidityLevelTypeValue == "1")
                {
                    SelectedAutoTunningRigidityLevelType = "一级";
                }
                else if (strAutoTunningRigidityLevelTypeValue == "2")
                {
                    SelectedAutoTunningRigidityLevelType = "二级";
                }
                else if (strAutoTunningRigidityLevelTypeValue == "3")
                {
                    SelectedAutoTunningRigidityLevelType = "三级";
                }

                strAutoTunningModeTypeValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "AatMode", "Index"));//自整定应用模式
                switch (strAutoTunningModeTypeValue)
                {
                    case "1":
                        SelectedAutoTunningModeType = "标准模式";
                        break;
                    case "2":
                        SelectedAutoTunningModeType = "定位用途模式";
                        break;
                    case "3":
                        SelectedAutoTunningModeType = "不超调的定位模式";
                        break;
                    case "4":
                        SelectedAutoTunningModeType = "速度模式";
                        break;
                    default:
                        break;
                }

                //参数自整定显示内容
                PositionLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//位置环增益
                SpeedLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//速度环增益
                SpeedLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//速度环积分时间常数
                FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩指令滤波时间

                VibsupFreq = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Freq", "Index"));//A型抑振频率  
                VibsupGainComp = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Gain Comp", "Index"));//A型抑振增益补偿
                VibsupDampingGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibsup Damping Gain", "Index"));//A型抑振阻尼增益

                ModelFollowingControlGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Gain", "Index"));//模型追踪控制增益
                MFCGainCorrection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "MFC Gain Correction", "Index"));//模型追踪控制增益补偿
                VibrationSuppressionFrequencyA = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression 1 Frequency A", "Index"));//振动抑制1频率A

                strVibrationSuppressionOptionValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Vibration Suppression Option", "Index"));//A型抑振控制选择
                if (strVibrationSuppressionOptionValue == "16")
                {
                    SelectedVibrationSuppressionOption = "关";
                    VibrationSuppressionOption_Color = BackgroundState.Black;
                }
                else if (strVibrationSuppressionOptionValue == "17")
                {
                    SelectedVibrationSuppressionOption = "开";
                    VibrationSuppressionOption_Color = BackgroundState.Red;
                }

                strModelFollowingControlSwitchValue = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Model Following Control Switch", "Index"));//模型追踪控制开关
                if (strModelFollowingControlSwitchValue == "256")
                {
                    SelectedModelFollowingControlSwitch = "关";
                    ModelFollowingControlSwitch_Color = BackgroundState.Black;
                }
                else if (strModelFollowingControlSwitchValue == "257")
                {
                    SelectedModelFollowingControlSwitch = "开";
                    ModelFollowingControlSwitch_Color = BackgroundState.Red;
                }

            }
        }

        //*************************************************************************
        //函数名称：JogDriectionDebug_For_AddInterface
        //函数功能：JOG调试和电机方向导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2022.11.18
        //*************************************************************************
        public void JogDriectionDebug_For_AddInterface()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.MOTORDRIECTIONJOGPAGE;
                NavigationService.Navigate("MotorDriectionJogView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：UnitNavigation
        //函数功能：单位设置导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.24
        //*************************************************************************
        public void UnitNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                if (!IsEncoderSet)
                {
                    ShowHintInfo("请留意【编码器】配置");
                    IsEncoderSet = true;
                }
                
                SoftwareStateParameterSet.CurrentPageName = PageName.UNIT;
                NavigationService.Navigate("UnitView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：MotorLibraryNavigation
        //函数功能：电机参数库导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.26
        //*************************************************************************
        public void MotorLibraryNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.MOTORLIBRARY;
                NavigationService.Navigate("MotorLibraryView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：ParameterTunningNavigation
        //函数功能：手动参数调优界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.23
        //*************************************************************************
        public void ParameterTunningNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                //SoftwareStateParameterSet.CurrentPageName = PageName.PARAMETERTUNNING;
                //NavigationService.Navigate("OscilloscopeView", null, ViewModelSet.Main);
                SoftwareStateParameterSet.CurrentPageName = PageName.PARAMETERTUNNING;
                SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.ParameterTunning;
                NavigationService.Navigate("OscilloscopeView", null, this);
            }
        }

        //*************************************************************************
        //函数名称：EncoderAlreadySet
        //函数功能：编码器已经设置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.24
        //*************************************************************************
        public void EncoderAlreadySet()
        {
            IsEncoderSet = true;
        }

        //*************************************************************************
        //函数名称：CommunicationSetNavigation
        //函数功能：通信配置导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void CommunicationSetNavigation()
        {
            SoftwareStateParameterSet.CurrentPageName = PageName.COMMUNICATIONSET;
            ViewModelSet.Main?.CommunicationSetNavigation();
        }

        //*************************************************************************
        //函数名称：ChangeMotorParameterIdentification
        //函数功能：是否更改电机参数识别
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.27
        //*************************************************************************
        public void MotorParameterIdentification()
        {
            int iRet = -1;
            
            try
            {
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return;
                }

                if (ViewModelSet.MotorParameterIdentification == null)
                {
                    ViewModelSet.MotorParameterIdentification = new MotorParameterIdentificationViewModel();
                }
           
                UICommand cancelCommand = new UICommand()
                {
                    Caption = "返回",
                    IsCancel = true,
                };

                UICommand result  = DialogService.ShowDialog(new List<UICommand>() { cancelCommand }, "电机参数辨识", ViewModelSet.MotorParameterIdentification);                                       
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_MOTOR_PARAMETER_IDENTIFICATION, "MotorParameterIdentification", ex);
            }  
        }
      
        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void OnInertiaIdetificationStartValueChanged() { GlobalCurrentInput.InertiaIdetificationStartValue = InertiaIdetificationStartValue; }//惯量辨识起始值
        public void OnLoadInertiaRatioChanged() { GlobalCurrentInput.LoadInertiaRatio = LoadInertiaRatio; }//负载惯量比
        public void OnAutoTunningDistanceChanged() { GlobalCurrentInput.AutoTunningDistance = AutoTunningDistance; }//自整定移动距离
        //public void OnAutoTunningRigidityLevelChanged() { GlobalCurrentInput.AutoTunningRigidityLevel = AutoTunningRigidityLevel; }//自整定刚性等级

        public void OnSelectedAutoTunningModeTypeChanged()//自整定应用类型
        {
            GlobalCurrentInput.SelectedAutoTunningModeType = SelectedAutoTunningModeType;

            if (SelectedAutoTunningModeType == "速度模式")
            {
                IsSpeedModePageEnabled = ControlVisibility.Collapsed;
            }            
            else
            {                
                IsSpeedModePageEnabled = ControlVisibility.Visible;
            }
        }
        public void OnSelectedAutoTunningRigidityLevelTypeChanged() { GlobalCurrentInput.SelectedAutoTunningRigidityLevelType = SelectedAutoTunningRigidityLevelType; }//自整定刚性等级
      
        public void OnPositionLoopGainChanged() { GlobalCurrentInput.PositionLoopGain = PositionLoopGain; }//位置环增益
        public void OnSpeedLoopGainChanged() { GlobalCurrentInput.SpeedLoopGain = SpeedLoopGain; }//速度环增益
        public void OnSpeedLoopTimeConstantChanged() { GlobalCurrentInput.SpeedLoopTimeConstant = SpeedLoopTimeConstant; }//速度环积分时间常数
        public void OnFirstTrqcmdFilterTimeChanged() { GlobalCurrentInput.FirstTrqcmdFilterTime = FirstTrqcmdFilterTime; }//第一转矩指令滤波时间参数
        public void OnSelectedVibrationSuppressionOptionChanged() { GlobalCurrentInput.SelectedVibrationSuppressionOption = SelectedVibrationSuppressionOption; }//A型抑振控制选择
        public void OnVibsupFreqChanged() { GlobalCurrentInput.VibsupFreq = VibsupFreq; }//A型抑振频率
        public void OnVibsupGainCompChanged() { GlobalCurrentInput.VibsupGainComp = VibsupGainComp; }//A型抑振增益补偿
        public void OnVibsupDampingGainChanged() { GlobalCurrentInput.VibsupDampingGain = VibsupDampingGain; }//A型抑振阻尼增益
        public void OnSelectedModelFollowingControlSwitchChanged() { GlobalCurrentInput.SelectedModelFollowingControlSwitch = SelectedModelFollowingControlSwitch; }//模型追踪控制开关
        public void OnModelFollowingControlGainChanged() { GlobalCurrentInput.ModelFollowingControlGain = ModelFollowingControlGain; }//模型追踪控制增益
        public void OnMFCGainCorrectionChanged() { GlobalCurrentInput.MFCGainCorrection = MFCGainCorrection; }//模型追踪控制增益补偿
        public void OnVibrationSuppressionFrequencyAChanged() { GlobalCurrentInput.VibrationSuppressionFrequencyA = VibrationSuppressionFrequencyA; }//振动抑制1频率A
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：GetMotorConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.10.23&2022.10.21&2022.11.18
        //*************************************************************************
        private DataTable GetMotorConfigToDataTable()
        {
            DataTable dt = new DataTable();

            try
            {              
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Inertia Idetification Start Value", "转动惯量辨识开始值", LoadInertiaRatio, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Load Inertia Ratio", "负载惯量比", LoadInertiaRatio, "", ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "AatDistance", "自整定移动距离", AutoTunningDistance, "0.1Rev", ref dt);
                //OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "AatLoadType", "自整定负载类型", AutoTunningRigidityLevel, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "AatLoadType", "自整定负载类型", SelectedAutoTunningRigidityLevelType, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Position completion width", "定位完成窗口", PositionCompletionWidth, "cnt", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "AatMode", "自整定应用模式", SelectedAutoTunningModeType, "", ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Position Loop Gain", "位置环增益", PositionLoopGain, "0.1Hz", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Speed Loop Gain", "速度环增益", SpeedLoopGain, "0.1Hz", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Speed Loop Time Constant", "速度环积分时间常数", SpeedLoopTimeConstant, "0.01ms", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "First Trqcmd Filter Time", "第一转矩指令滤波时间参数", FirstTrqcmdFilterTime, "0.01ms", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Vibration Suppression Option", "A型抑振控制选择", SelectedVibrationSuppressionOption, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Vibsup Freq", "A型抑振频率", VibsupFreq, "0.1Hz", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Vibsup Gain Comp", "A型抑振增益补偿", VibsupGainComp, "0.01", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Vibsup Damping Gain", "A型抑振阻尼增益", VibsupDampingGain, "0.01", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Model Following Control Switch", "模型追踪控制开关", SelectedModelFollowingControlSwitch, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Model Following Control Gain", "模型追踪控制增益", ModelFollowingControlGain, "0.1Hz", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "MFC Gain Correction", "模型追踪控制增益补偿", MFCGainCorrection, "0.1%", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING, "Vibration Suppression 1 Frequency A", "振动抑制1频率A", VibrationSuppressionFrequencyA, "0.1Hz", ref dt);

                return dt;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_GET_MOTOR_CONFIF_TO_DATATABLE,"GetMotorConfigToDataTable", ex);
                return null;
            }
        }
        private DataTable GetMotorLogToDataTable(string DateTime, string FileName)
        {
            DataTable dtMotorLibraryLog = new DataTable();
            ExcelHelper.ReadFromExcel(FilePath.MotorLibraryLog, ref dtMotorLibraryLog);

            if (dtMotorLibraryLog.Columns.Count == 0)
            {
                DataColumnCollection columns = dtMotorLibraryLog.Columns;
                columns.Add("Name", typeof(String));
                columns.Add("MotorType", typeof(String));
                columns.Add("EncoderType", typeof(String));
                columns.Add("Author", typeof(String));
                columns.Add("DateTime", typeof(String));
                columns.Add("Comment", typeof(String));
                columns.Add("Valid", typeof(String));
            }

            DataRow clsDataRow = dtMotorLibraryLog.NewRow();
            clsDataRow["Name"] = FileName;
            clsDataRow["MotorType"] = "Unknown";
            clsDataRow["EncoderType"] = SelectedEncoderType;
            clsDataRow["Author"] = "ServoStudio";
            clsDataRow["DateTime"] = DateTime;
            clsDataRow["Comment"] = "";
            clsDataRow["Valid"] = "True";
            dtMotorLibraryLog.Rows.Add(clsDataRow);
          
            return dtMotorLibraryLog;
        }

        //*************************************************************************
        //函数名称：GetMotorConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.13&2022.10.21
        //*************************************************************************
        private int GetMotorConfigFromDataTable(DataTable dt)
        {
            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                InertiaIdetificationStartValue = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Inertia Idetification Start Value", "Default");
                LoadInertiaRatio = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Load Inertia Ratio", "Default");

                AutoTunningDistance = OthersHelper.GetCellValueFromDataTable(dt, "Name", "AatDistance", "Default");
                //AutoTunningRigidityLevel = OthersHelper.GetCellValueFromDataTable(dt, "Name", "AatLoadType", "Default");
                SelectedAutoTunningRigidityLevelType = OthersHelper.GetCellValueFromDataTable(dt, "Name", "AatLoadType", "Default");
                PositionCompletionWidth = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Position completion width", "Default");
                SelectedAutoTunningModeType = OthersHelper.GetCellValueFromDataTable(dt, "Name", "AatMode", "Default");

                //参数自整定显示内容
                PositionLoopGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Position Loop Gain", "Default");//位置环增益
                SpeedLoopGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Loop Gain", "Default");//速度环增益
                SpeedLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Speed Loop Time Constant", "Default");//速度环积分时间常数
                FirstTrqcmdFilterTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "First Trqcmd Filter Time", "Default");//第一转矩指令滤波时间

                VibsupFreq = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibsup Freq", "Default");//A型抑振频率  
                VibsupGainComp = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibsup Gain Comp", "Default");//A型抑振增益补偿
                VibsupDampingGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibsup Damping Gain", "Default");//A型抑振阻尼增益

                ModelFollowingControlGain = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Model Following Control Gain", "Default");//模型追踪控制增益
                MFCGainCorrection = OthersHelper.GetCellValueFromDataTable(dt, "Name", "MFC Gain Correction", "Default");//模型追踪控制增益补偿
                VibrationSuppressionFrequencyA = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibration Suppression 1 Frequency A", "Default");//振动抑制1频率A

                SelectedVibrationSuppressionOption = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Vibration Suppression Option", "Default");//A型抑振控制选择

                SelectedModelFollowingControlSwitch = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Model Following Control Switch", "Default");//模型追踪控制开关

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_GET_MOTOR_CONFIG_FROM_DATATABLE, "GetMotorConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：属性初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.04.15&2021.10.10&2022.10.21
        //*************************************************************************
        private void ComboBoxInitialize()
        {
            try
            {
                AutoTunningModeType = new ObservableCollection<string>() { "标准模式", "定位用途模式", "不超调的定位模式","速度模式" };   //自整定模式
                SelectedAutoTunningModeType = "标准模式";

                AutoTunningRigidityLevelType = new ObservableCollection<string>() { "一级", "二级", "三级" };   //自整定刚性等级
                SelectedAutoTunningRigidityLevelType = "一级";

                VibrationSuppressionOption = new ObservableCollection<string>() { "关", "开" };   //A型抑振控制选择
                SelectedVibrationSuppressionOption = "关";

                ModelFollowingControlSwitch = new ObservableCollection<string>() { "关", "开" };   //模型追踪控制开关
                SelectedModelFollowingControlSwitch = "开";

                MotorID = new ObservableCollection<string>() { "电机型号1" };
                MotorType = new ObservableCollection<string>() { "旋转型无刷电机", "直线电机" };  //由Lilbert增加直线电机

                //目前不需要
                SelectedMotorID = "电机型号1";
                SelectedMotorType = "旋转型无刷电机";              
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_PROPERTY_INITIALIZE, "ComboBoxInitialize", ex);
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.18&2022.10.21&2022.11.18
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                InertiaIdetificationStartValue = GlobalCurrentInput.InertiaIdetificationStartValue;//惯量辨识起始值
                LoadInertiaRatio = GlobalCurrentInput.LoadInertiaRatio;//负载惯量比

                AutoTunningDistance = GlobalCurrentInput.AutoTunningDistance;//自整定移动距离
                //AutoTunningRigidityLevel = GlobalCurrentInput.AutoTunningRigidityLevel;//自整定刚性等级
                SelectedAutoTunningRigidityLevelType = GlobalCurrentInput.SelectedAutoTunningRigidityLevelType;//自整定刚性等级
                PositionCompletionWidth = GlobalCurrentInput.PositionCompletionWidth;//定位完成宽度
                SelectedAutoTunningModeType = GlobalCurrentInput.SelectedAutoTunningModeType;//自整定应用模式

                PositionLoopGain = GlobalCurrentInput.PositionLoopGain;//位置环增益
                SpeedLoopGain = GlobalCurrentInput.SpeedLoopGain;//速度环增益
                SpeedLoopTimeConstant = GlobalCurrentInput.SpeedLoopTimeConstant;//速度环积分时间常数
                FirstTrqcmdFilterTime = GlobalCurrentInput.FirstTrqcmdFilterTime;//第一转矩指令滤波时间参数
                SelectedVibrationSuppressionOption = GlobalCurrentInput.SelectedVibrationSuppressionOption;//A型抑振控制选择
                VibsupFreq = GlobalCurrentInput.VibsupFreq;//A型抑振频率
                VibsupGainComp = GlobalCurrentInput.VibsupGainComp;//A型抑振增益补偿
                VibsupDampingGain = GlobalCurrentInput.VibsupDampingGain;//A型抑振阻尼增益
                SelectedModelFollowingControlSwitch = GlobalCurrentInput.SelectedModelFollowingControlSwitch;//模型追踪控制开关
                ModelFollowingControlGain = GlobalCurrentInput.ModelFollowingControlGain;//模型追踪控制增益
                MFCGainCorrection = GlobalCurrentInput.MFCGainCorrection;//模型追踪控制增益补偿
                VibrationSuppressionFrequencyA = GlobalCurrentInput.VibrationSuppressionFrequencyA;//振动抑制1频率A
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary_ParameterSelfTunning
        //函数功能：添加要读写的电机参数自整定参数字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        private int AddParameterInfoDictionary_ParameterSelfTunning(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            string strAutoTunningModeTypeValue = null;
            string strAutoTunningRigidityLevelTypeValue = null;

            string strVibrationSuppressionOptionValue = null;
            string strModelFollowingControlSwitchValue = null;
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                switch (SelectedAutoTunningRigidityLevelType)
                {
                    case "一级":
                        strAutoTunningRigidityLevelTypeValue = "1";
                        break;
                    case "二级":
                        strAutoTunningRigidityLevelTypeValue = "2";
                        break;
                    case "三级":
                        strAutoTunningRigidityLevelTypeValue = "3";
                        break;
                    default:
                        break;
                }

                switch (SelectedAutoTunningModeType)
                {
                    case "标准模式":
                        strAutoTunningModeTypeValue = "1";
                        break;
                    case "定位用途模式":
                        strAutoTunningModeTypeValue = "2";
                        break;
                    case "不超调的定位模式":
                        strAutoTunningModeTypeValue = "3";
                        break;
                    case "速度模式":
                        strAutoTunningModeTypeValue = "4";
                        break;
                    default:
                        break;
                }

                switch (SelectedVibrationSuppressionOption)
                {
                    case "关":
                        strVibrationSuppressionOptionValue = "16";
                        VibrationSuppressionOption_Color = BackgroundState.Black;
                        break;
                    case "开":
                        strVibrationSuppressionOptionValue = "17";
                        VibrationSuppressionOption_Color = BackgroundState.Red;
                        break;
                    default:
                        break;
                }
                switch (SelectedModelFollowingControlSwitch)
                {
                    case "关":
                        strModelFollowingControlSwitchValue = "256";
                        ModelFollowingControlSwitch_Color = BackgroundState.Black;
                        break;
                    case "开":
                        strModelFollowingControlSwitchValue = "257";
                        ModelFollowingControlSwitch_Color = BackgroundState.Red;
                        break;
                    default:
                        break;
                }

                switch (strCategory)
                {
                    case "0":

                        dicParameterInfo.Add("AatDistance", AutoTunningDistance);
                        //dicParameterInfo.Add("AatLoadType", AutoTunningRigidityLevel);
                        dicParameterInfo.Add("AatLoadType", strAutoTunningRigidityLevelTypeValue);
                        dicParameterInfo.Add("Position completion width", PositionCompletionWidth);
                        dicParameterInfo.Add("AatMode", strAutoTunningModeTypeValue);

                        dicParameterInfo.Add("Position Loop Gain", PositionLoopGain);
                        dicParameterInfo.Add("Speed Loop Gain", SpeedLoopGain);
                        dicParameterInfo.Add("Speed Loop Time Constant", SpeedLoopTimeConstant);
                        dicParameterInfo.Add("First Trqcmd Filter Time", FirstTrqcmdFilterTime);
                        dicParameterInfo.Add("Vibration Suppression Option", strVibrationSuppressionOptionValue);
                        dicParameterInfo.Add("Vibsup Freq", VibsupFreq);
                        dicParameterInfo.Add("Vibsup Gain Comp", VibsupGainComp);
                        dicParameterInfo.Add("Vibsup Damping Gain", VibsupDampingGain);
                        dicParameterInfo.Add("Model Following Control Switch", strModelFollowingControlSwitchValue);
                        dicParameterInfo.Add("Model Following Control Gain", ModelFollowingControlGain);
                        dicParameterInfo.Add("MFC Gain Correction", MFCGainCorrection);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency A", VibrationSuppressionFrequencyA);
                        break;
                    //case "1":
                    //    dicParameterInfo.Add("Encoder Type", strEncoderValue);
                    //    dicParameterInfo.Add("Abs Encoder Single-Turn Bit", ABSEncoderSingleTurnBit);
                    //    dicParameterInfo.Add("Abs Encoder Multi-Turn Bit", ABSEncoderMultiTurnBit);
                    //    dicParameterInfo.Add("Abs Encoder Offset", ABSEncoderOffset);
                    //    dicParameterInfo.Add("Abz Encoder Pulses", ABZEncoderPulses);

                    //    dicParameterInfo.Add("Pitch Motor EncoderLines", PitchMotorEncoderLines);  //由Lilbert增加每转线数
                    //    break;
                    default:

                        dicParameterInfo.Add("AatDistance", AutoTunningDistance);
                        //dicParameterInfo.Add("AatLoadType", AutoTunningRigidityLevel);
                        dicParameterInfo.Add("AatLoadType", strAutoTunningRigidityLevelTypeValue);
                        dicParameterInfo.Add("Position completion width", PositionCompletionWidth);
                        dicParameterInfo.Add("AatMode", strAutoTunningModeTypeValue);

                        dicParameterInfo.Add("Position Loop Gain", PositionLoopGain);
                        dicParameterInfo.Add("Speed Loop Gain", SpeedLoopGain);
                        dicParameterInfo.Add("Speed Loop Time Constant", SpeedLoopTimeConstant);
                        dicParameterInfo.Add("First Trqcmd Filter Time", FirstTrqcmdFilterTime);
                        dicParameterInfo.Add("Vibration Suppression Option", strVibrationSuppressionOptionValue);
                        dicParameterInfo.Add("Vibsup Freq", VibsupFreq);
                        dicParameterInfo.Add("Vibsup Gain Comp", VibsupGainComp);
                        dicParameterInfo.Add("Vibsup Damping Gain", VibsupDampingGain);
                        dicParameterInfo.Add("Model Following Control Switch", strModelFollowingControlSwitchValue);
                        dicParameterInfo.Add("Model Following Control Gain", ModelFollowingControlGain);
                        dicParameterInfo.Add("MFC Gain Correction", MFCGainCorrection);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency A", VibrationSuppressionFrequencyA);

                        break;
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERSELFTUNNING_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary_ParameterSelfTunning", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary_InertiaIdentification
        //函数功能：添加要读写的负载惯量辨识字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        private int AddParameterInfoDictionary_InertiaIdentification(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            string strAutoTunningModeType = null;
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                switch (SelectedAutoTunningModeType)
                {
                    case "标准模式":
                        strAutoTunningModeType = "1";
                        break;
                    case "定位用途模式":
                        strAutoTunningModeType = "2";
                        break;
                    case "不超调的定位模式":
                        strAutoTunningModeType = "3";
                        break;
                    case "速度模式":
                        strAutoTunningModeType = "4";
                        break;
                    default:
                        break;
                }

                switch (strCategory)
                {
                    case "0":

                        dicParameterInfo.Add("Inertia Idetification Start Value", InertiaIdetificationStartValue);
                        dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);

                        break;
                    //case "1":
                    //    dicParameterInfo.Add("Encoder Type", strEncoderValue);
                    //    dicParameterInfo.Add("Abs Encoder Single-Turn Bit", ABSEncoderSingleTurnBit);
                    //    dicParameterInfo.Add("Abs Encoder Multi-Turn Bit", ABSEncoderMultiTurnBit);
                    //    dicParameterInfo.Add("Abs Encoder Offset", ABSEncoderOffset);
                    //    dicParameterInfo.Add("Abz Encoder Pulses", ABZEncoderPulses);

                    //    dicParameterInfo.Add("Pitch Motor EncoderLines", PitchMotorEncoderLines);  //由Lilbert增加每转线数
                    //    break;
                    default:

                        //dicParameterInfo.Add("Inertia Idetification Start Value", InertiaIdetificationStartValue);
                        dicParameterInfo.Add("Inertia Idetification Start Value", InertiaIdetificationStartValue);
                        dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);

                        break;
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.INERTIALIDENTIFICATION_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary_InertiaIdentification", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.02&2021.10.10&2022.10.21
        //*************************************************************************
        private int AddParameterInfoDictionary(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            string strAutoTunningModeTypeValue = null;
            string strAutoTunningRigidityLevelTypeValue = null;
            string strVibrationSuppressionOptionValue = null;
            string strModelFollowingControlSwitchValue = null;
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                switch (SelectedAutoTunningRigidityLevelType)
                {
                    case "一级":
                        strAutoTunningRigidityLevelTypeValue = "1";
                        break;
                    case "二级":
                        strAutoTunningRigidityLevelTypeValue = "2";
                        break;
                    case "三级":
                        strAutoTunningRigidityLevelTypeValue = "3";
                        break;
                    default:
                        break;
                }

                switch (SelectedAutoTunningModeType)
                {
                    case "标准模式":
                        strAutoTunningModeTypeValue = "1";
                        break;
                    case "定位用途模式":
                        strAutoTunningModeTypeValue = "2";
                        break;
                    case "不超调的定位模式":
                        strAutoTunningModeTypeValue = "3";
                        break;
                    case "速度模式":
                        strAutoTunningModeTypeValue = "4";
                        break;
                    default:
                        break;
                }

                switch (SelectedVibrationSuppressionOption)
                {
                    case "关":
                        strVibrationSuppressionOptionValue = "16";
                        VibrationSuppressionOption_Color = BackgroundState.Black;
                        break;
                    case "开":
                        strVibrationSuppressionOptionValue = "17";
                        VibrationSuppressionOption_Color = BackgroundState.Red;
                        break;
                    default:
                        break;
                }
                switch (SelectedModelFollowingControlSwitch)
                {
                    case "关":
                        strModelFollowingControlSwitchValue = "256";
                        ModelFollowingControlSwitch_Color = BackgroundState.Black;
                        break;
                    case "开":
                        strModelFollowingControlSwitchValue = "257";
                        ModelFollowingControlSwitch_Color = BackgroundState.Red;
                        break;
                    default:
                        break;
                }

                switch (strCategory)
                {
                    case "0":
                        //dicParameterInfo.Add("InertiaIdetificationStartValue", InertiaIdetificationStartValue);
                        dicParameterInfo.Add("Inertia Idetification Start Value", InertiaIdetificationStartValue);
                        dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);

                        dicParameterInfo.Add("AatDistance", AutoTunningDistance);
                        //dicParameterInfo.Add("AatLoadType", AutoTunningRigidityLevel);
                        dicParameterInfo.Add("AatLoadType", strAutoTunningRigidityLevelTypeValue);
                        dicParameterInfo.Add("Position completion width", PositionCompletionWidth);
                        dicParameterInfo.Add("AatMode", strAutoTunningModeTypeValue);

                        dicParameterInfo.Add("Position Loop Gain", PositionLoopGain);
                        dicParameterInfo.Add("Speed Loop Gain", SpeedLoopGain);
                        dicParameterInfo.Add("Speed Loop Time Constant", SpeedLoopTimeConstant);
                        dicParameterInfo.Add("First Trqcmd Filter Time", FirstTrqcmdFilterTime);
                        dicParameterInfo.Add("Vibration Suppression Option", strVibrationSuppressionOptionValue);
                        dicParameterInfo.Add("Vibsup Freq", VibsupFreq);
                        dicParameterInfo.Add("Vibsup Gain Comp", VibsupGainComp);
                        dicParameterInfo.Add("Vibsup Damping Gain", VibsupDampingGain);
                        dicParameterInfo.Add("Model Following Control Switch", strModelFollowingControlSwitchValue);
                        dicParameterInfo.Add("Model Following Control Gain", ModelFollowingControlGain);
                        dicParameterInfo.Add("MFC Gain Correction", MFCGainCorrection);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency A", VibrationSuppressionFrequencyA);
                        break;
                    //case "1":
                    //    dicParameterInfo.Add("Encoder Type", strEncoderValue);
                    //    dicParameterInfo.Add("Abs Encoder Single-Turn Bit", ABSEncoderSingleTurnBit);
                    //    dicParameterInfo.Add("Abs Encoder Multi-Turn Bit", ABSEncoderMultiTurnBit);
                    //    dicParameterInfo.Add("Abs Encoder Offset", ABSEncoderOffset);
                    //    dicParameterInfo.Add("Abz Encoder Pulses", ABZEncoderPulses);

                    //    dicParameterInfo.Add("Pitch Motor EncoderLines", PitchMotorEncoderLines);  //由Lilbert增加每转线数
                    //    break;
                    default:

                        dicParameterInfo.Add("Inertia Idetification Start Value", InertiaIdetificationStartValue);
                        dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);

                        dicParameterInfo.Add("AatDistance", AutoTunningDistance);
                        //dicParameterInfo.Add("AatLoadType", AutoTunningRigidityLevel);
                        dicParameterInfo.Add("AatLoadType", strAutoTunningRigidityLevelTypeValue);
                        dicParameterInfo.Add("Position completion width", PositionCompletionWidth);
                        dicParameterInfo.Add("AatMode", strAutoTunningModeTypeValue);

                        dicParameterInfo.Add("Position Loop Gain", PositionLoopGain);
                        dicParameterInfo.Add("Speed Loop Gain", SpeedLoopGain);
                        dicParameterInfo.Add("Speed Loop Time Constant", SpeedLoopTimeConstant);
                        dicParameterInfo.Add("First Trqcmd Filter Time", FirstTrqcmdFilterTime);
                        dicParameterInfo.Add("Vibration Suppression Option", strVibrationSuppressionOptionValue);
                        dicParameterInfo.Add("Vibsup Freq", VibsupFreq);
                        dicParameterInfo.Add("Vibsup Gain Comp", VibsupGainComp);
                        dicParameterInfo.Add("Vibsup Damping Gain", VibsupDampingGain);
                        dicParameterInfo.Add("Model Following Control Switch", strModelFollowingControlSwitchValue);
                        dicParameterInfo.Add("Model Following Control Gain", ModelFollowingControlGain);
                        dicParameterInfo.Add("MFC Gain Correction", MFCGainCorrection);
                        dicParameterInfo.Add("Vibration Suppression 1 Frequency A", VibrationSuppressionFrequencyA);
                        break;
                }
               
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowHintInfo(string strInfo)
        {
            ViewModelSet.Main?.ShowHintInfo(strInfo);
        }
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        private void ShowNotification_CrossThread(int In_iType)
        {
            WindowSet.clsMainWindow?.ShowNotification(In_iType);
        }

        //*************************************************************************
        //函数名称：ReadMotorInertiaIdentificationParameterSelfTunningIdentification
        //函数功能：读电机负载惯量辨识和参数自整定任务
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        private int ReadMotorInertiaIdentificationParameterSelfTunningIdentification(bool IsListen)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                AddParameterInfoDictionary_InertiaIdentificationParameterSelfTunning(IsListen, ref dicParameterInfo);

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    return RET.ERROR;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                if (IsListen)
                {
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.ParameterInertiaIdentificationParameterSelfTunningIdentificationListen, lstTransmittingDataInfo);
                }
                else
                {
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.ParameterInertiaIdentificationParameterSelfTunningIdentification, lstTransmittingDataInfo);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.INERTIALIDENTIFICATIONPARAMETERSELFTUNNING_IDENTIFICATION_READ, "ReadMotorInertiaIdentificationParameterSelfTunningIdentification", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReadMotorParameterMotorParameterSelfTunning
        //函数功能：读电机参数自整定任务
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.21
        //*************************************************************************
        private int ReadMotorParameterMotorParameterSelfTunning(bool IsListen)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                AddParameterInfoDictionary_ParameterSelfTunning(IsListen, ref dicParameterInfo);

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    return RET.ERROR;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                if (IsListen)
                {
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.ParameterSelTunningListen, lstTransmittingDataInfo);
                }
                else
                {
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.ParameterMotorSelfTunning, lstTransmittingDataInfo);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERSELFTUNNING_IDENTIFICATION_READ, "ReadMotorParameterMotorParameterSelfTunning", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReadMotorParameterMotorInertiaIdentification
        //函数功能：读电机负载惯量辨识任务
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.21
        //*************************************************************************
        private int ReadMotorParameterMotorInertiaIdentification(bool IsListen)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                AddParameterInfoDictionary_InertiaIdentification(IsListen, ref dicParameterInfo);

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    return RET.ERROR;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                if (IsListen)
                {
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.ParameterInertiaIdentificationListen, lstTransmittingDataInfo);
                }
                else
                {
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.ParameterMotorInertiaIdentification, lstTransmittingDataInfo);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.INERTIALIDENTIFICATION_IDENTIFICATION_READ, "ReadMotorParameterMotorInertiaIdentification", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：Timer_System_Tick
        //函数功能：时钟
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.14
        //*************************************************************************
        private void Timer_System_Tick(object sender, EventArgs e)
        {
            //监听是否参数辨识完成
            ReadMotorInertiaIdentificationParameterSelfTunningIdentification(IsListen: true);
        }
        #endregion
    }
}