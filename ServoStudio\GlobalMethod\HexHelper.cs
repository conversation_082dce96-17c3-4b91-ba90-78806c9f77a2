using ServoStudio.GlobalConstant;
using ServoStudio.Models;
using ServoStudio.ViewModels;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows;

namespace ServoStudio.GlobalMethod
{
    public static class HexHelper
    {
        //*************************************************************************
        //函数名称：TransmittingData
        //函数功能：发送数据
        //
        //输入参数：
        //         
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.12.16
        //*************************************************************************
        public static int TransmittingData(Byte[] data)
        {
            int iRet = -1;

            try
            {
                iRet = CheckSerialPortStatus();
                if (iRet == RET.SUCCEEDED)
                {
                    CommunicationSet.SerialPortInfo.Write(data, 0, data.Length);
                    SoftwareStateParameterSet.DisconnectionTimes = 0;

                    iRet = RET.SUCCEEDED;
                }
                else
                {
                    iRet =  RET.NO_EFFECT;
                }              
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("TransmittingData", ex);
                SoftwareStateParameterSet.DisconnectionTimes++;

                iRet = RET.NO_EFFECT;
            }
            finally
            {
                if (SoftwareStateParameterSet.DisconnectionTimes > LimitValue.Disconnection)
                {
                    iRet = RET.ERROR;                            
                }
            }

            return iRet;
        }

        //*************************************************************************
        //函数名称：ReceivingData
        //函数功能：接收数据
        //
        //输入参数：
        //         
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.12.16
        //*************************************************************************
        public static int ReceivingData()
        {
            int iRet = -1;
            int iBytesToRead = 0;
           
            byte[] buffer = null;

            try
            {
                if (AddSlaveAxisIDSet.Action == "ScanSlaveAxisID")//由Lilbert于2023.02.08添加扫描从站地址选择
                {
                    AddSlaveAxisIDSet.Abc = true;
                }

                if (SoftwareStateParameterSet.lstMessage == null)
                {
                    SoftwareStateParameterSet.lstMessage = new List<byte>();
                }

                iRet = CheckSerialPortStatus();
                if (iRet == RET.SUCCEEDED)
                {
                    iBytesToRead = CommunicationSet.SerialPortInfo.BytesToRead;
                    if (iBytesToRead < 1)
                    {
                        return RET.NO_EFFECT;
                    }

                    buffer = new byte[iBytesToRead];
                    CommunicationSet.SerialPortInfo.Read(buffer, 0, buffer.Length);
                                   
                    SoftwareStateParameterSet.lstMessage.AddRange(buffer);

                    //Console.WriteLine("接收指令：-------------------------");
                    //Console.WriteLine("");
                    //Console.WriteLine(BytesToHexString(buffer));
                    //Console.WriteLine("完成接收指令!-------------------------");
                    //Console.WriteLine("");
                    //Console.WriteLine("");

                    return RET.SUCCEEDED;
                }
                else
                {
                    return RET.NO_EFFECT;
                }            
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("ReceivingData", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingData
        //函数功能：接收数据
        //
        //输入参数：string strTaskName   任务名称
        //         
        //输出参数：-1：方法错误，直接停止升级
        //          0: 方法无效
        //          1: 报文成功
        //        
        //编码作者：Ryan
        //更新时间：2020.05.08
        //*************************************************************************
        public static int ReceivingData(string strTaskName, ref byte[] bData, ref string strInfo)
        {
            int iRet = -1;
            int iBytesToRead = 0;
            bData = null;
            strInfo = null;

            try
            {
                //判断串口状态
                iRet = CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    return RET.ERROR;
                }

                //接收数据
                iBytesToRead = CommunicationSet.SerialPortInfo.BytesToRead;
                if (iBytesToRead < 1)
                {
                    return RET.NO_EFFECT;
                }
                else
                {
                    bData = new byte[iBytesToRead];
                    CommunicationSet.SerialPortInfo.Read(bData, 0, bData.Length);
                }

                //判断任务状态
                if (strTaskName == FirmwareUpdateProcess.ASSIGNMENT)
                {
                    #region 判断是否是C、NO_C
                    iRet = bData.FindIndex(item => item == YModemHelper.C);
                    if (iRet != -1)
                    {
                        strInfo = "C";                      
                    }
                    else
                    {
                        strInfo = "NO_C";                       
                    }

                    return RET.SUCCEEDED;
                    #endregion
                }
                else if (strTaskName == FirmwareUpdateProcess.INITIAL)
                {
                    #region 判断是否有ACK、NO_ACK
                    iRet = bData.FindIndex(item => item == YModemHelper.ACK);
                    if (iRet != -1)
                    {
                        strInfo = "ACK";                       
                    }
                    else
                    {
                        strInfo = "NO_ACK";
                    }

                    return RET.SUCCEEDED;
                    #endregion
                }
                else if (strTaskName == FirmwareUpdateProcess.DATA || strTaskName == FirmwareUpdateProcess.EOT)
                {
                    #region 判断是否有ACK、CA
                    iRet = bData.FindIndex(item => item == YModemHelper.ACK);
                    if (iRet != -1)//有ACK
                    {
                        strInfo = "ACK";
                        return RET.SUCCEEDED;
                    }

                    //是否有CA
                    iRet = bData.FindIndex(item => item == YModemHelper.CA);
                    if (iRet != -1)
                    {
                        strInfo = "CA";
                        return RET.SUCCEEDED;
                    }
                    #endregion
                }              
                else if (strTaskName == FirmwareUpdateProcess.EMPTY)
                {
                    strInfo = "ACK";
                    return RET.SUCCEEDED;
                }

                return RET.NO_EFFECT;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("ReceivingData", ex);
                return RET.ERROR;
            }           
        }

        //*************************************************************************
        //函数名称：HexStringToBytes
        //函数功能：16进制字符串转换成Byte
        //
        //输入参数：None
        //         
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.12.12
        //*************************************************************************
        public static byte[] HexStringToBytes(string hexStr)
        {
            byte[] buffer = null;

            try
            {
                if (string.IsNullOrEmpty(hexStr))
                    return null;

                if (hexStr.StartsWith("0x"))
                    hexStr = hexStr.Remove(0, 2);

                hexStr = hexStr.Replace(" ", "").Replace("-", "").Replace("/", "");

                if (hexStr.Length % 2 == 1)//不是完整的字节后面加上空格 
                    hexStr += "20";

                buffer = new byte[hexStr.Length / 2];
                for (int i = 0; i < buffer.Length; i++)
                {
                    var tempBytes = Byte.Parse(hexStr.Substring(2 * i, 2), System.Globalization.NumberStyles.HexNumber);
                    buffer[i] = tempBytes;
                }

                return buffer;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_HEX_STRING_TO_BYTES, "HexStringToBytes", ex);
                return null;
            }          
        }

        //*************************************************************************
        //函数名称：BytesToHexString
        //函数功能：Byte转换成16进制
        //
        //输入参数：None
        //         
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.12.12
        //*************************************************************************
        public static string BytesToHexString(byte[] buffer)
        {
            string hexString = "";

            try
            {
                if (buffer == null)
                    return null;
            
                for (int i = 0; i < buffer.Length; i++)
                {
                    byte[] temp = new byte[] { buffer[i] };
                    hexString += BitConverter.ToString(temp);
                }

                return hexString;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_BYTES_TO_HEX_STRING, "BytesToHexString", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：CRC16
        //函数功能：CRC校验
        //
        //输入参数：byte[] data
        //         
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.12.12
        //*************************************************************************
        public static string CRC16(string hexStr)
        {
            byte[] data = null;
            ushort uCRC = 0xFFFF;

            try
            {
                data = HexStringToBytes(hexStr);

                if (data == null) return null;
                if (data.Length == 0) return null;

                for (int i = 0; i < data.Length; i++)
                {
                    uCRC = (ushort)(uCRC ^ (data[i]));
                    for (int j = 0; j < 8; j++)
                    {
                        uCRC = (uCRC & 1) != 0 ? (ushort)((uCRC >> 1) ^ 0xA001) : (ushort)(uCRC >> 1);
                    }
                }

                byte hi = (byte)((uCRC & 0xFF00) >> 8);  //高位置
                byte lo = (byte)(uCRC & 0x00FF);         //低位置
         
                return Convert.ToString(hi * 0x100 + lo, 16).ToUpper().PadLeft(4, '0');
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_CRC16, "CRC16", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：CheckStringIsLegal
        //函数功能：检测字符串是否合法
        //
        //输入参数：string hexStr
        //         
        //输出参数：0：No_EFFECT
        //         1: SUCCEED
        //        -1: ERROR
        //        
        //编码作者：Ryan
        //更新时间：2019.12.13
        //*************************************************************************
        public static int CheckStringIsLegal(string hexStr)
        {
            try
            {
                //不允许为空
                if (string.IsNullOrEmpty(hexStr))
                    return RET.NO_EFFECT;

                //字符串要去掉标点
                hexStr = hexStr.Replace(" ", "").Replace("-", "").Replace("/", "");

                //完整字符
                if (hexStr.Length % 2 == 1)
                    return RET.ERROR;
                else
                    return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_CHECK_STRING_IS_LEGAL, "CheckStringIsLegal", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ConvertHexStringEndian
        //函数功能：16进制字符串字节序转换
        //
        //输入参数：string hexStr
        //         
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.12.12
        //*************************************************************************
        public static string ConvertHexStringEndian(string hexStr)
        {
            try
            {
                if (hexStr == null) return null;

                hexStr = hexStr.Replace("-", "");
                if (hexStr.Length % 2 == 1) return null;

                if (hexStr.Length == 2)
                {
                    return hexStr;
                }
                else
                {
                    string strTemp = "";
                    for (int i = hexStr.Length / 2 - 1; i >= 0 ; i--)
                    {
                        strTemp += hexStr.Substring(2 * i, 2);
                    }

                    return strTemp;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_CONVERT_HEX_STRING_ENDIAN, "ConvertHexStringEndian", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：MessageLengthToLittleEndian
        //函数功能：报文长度
        //
        //输入参数：string hexStr
        //         
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.12.12
        //*************************************************************************
        public static string MessageLengthToLittleEndian(string hexStr)
        {
            short iLength = 6;

            try
            {
                if (hexStr == null) return null;
                if (hexStr.Length % 2 == 1) return null;

                iLength += Convert.ToInt16(hexStr.Length / 2);
                return BitConverter.ToString(BitConverter.GetBytes(iLength)).Replace("-", "");
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_CONVERT_HEX_STRING_ENDIAN, "ConvertHexStringEndian", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：TransmittingMessageToBytes
        //函数功能：发送字符串的拼接
        //
        //输入参数：string strStationID                  从站ID
        //         string strAxisID                     转轴ID
        //         string strFunctionCode               功能码
        //         string strAcquisitionExecutedCode    数据采集执行事项
        //         List<string> lstData                 发送数据
        //         ref byte[] bMessage                  报文
        //         
        //输出参数：0：No_EFFECT
        //         1: SUCCEED
        //        -1: ERROR
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.12.13&2023.04.03
        //*************************************************************************
        public static int TransmittingMessageToBytes(string strStationID, string strAxisID, string strFunctionCode, string strAcquisitionExecutedCode, List<TransmitingDataInfoSet> TransmittingDataInfo, ref byte[] bMessage)
        {           
            string strLength = "";//长度
            string strData = "";//数据
            string strMessage = "";//报文
            string strNumber = "";//参数个数
     
            try
            {
                //发送数据小端转换
                foreach (var item in TransmittingDataInfo)
                {
                    if (strFunctionCode == FunctionCode.PARAMETER_WRITE)
                    {
                        int iLength = item.Content.Length;
                        string strAddresstemp = ConvertHexStringEndian(item.Content.Substring(0, 8));
                        string strValuetemp = ConvertHexStringEndian(item.Content.Substring(8, iLength - 8));
                        strData += strAddresstemp + strValuetemp;
                    }
                    else if (strFunctionCode == FunctionCode.PARAMETER_READ || strFunctionCode == FunctionCode.PARAMETER_WRITE)
                    {
                        strData += ConvertHexStringEndian(item.Content);
                    }
                    else if (strFunctionCode == FunctionCode.TEST_COMMUNICATION)
                    {
                        strData = ConvertHexStringEndian(item.Content);
                    }
                    else if (strFunctionCode == FunctionCode.ACQUISITION && strAcquisitionExecutedCode == AcquisitionExecutedCode.ACQUISITION)
                    {
                        strData = strAxisID + AcquisitionExecutedCode.ACQUISITION + item.Content;
                    }
                    else if (strFunctionCode == FaultFunctionCode.ACQUISITION && strAcquisitionExecutedCode == FaultAcquisitionExecutedCode.ACQUISITION)//由Lilbert于20230403添加故障数据采集
                    {
                        strData = strAxisID + FaultAcquisitionExecutedCode.ACQUISITION + item.Content;
                    }
                    else if (strFunctionCode == FunctionCode.UPDATE)
                    {
                        strData = ConvertHexStringEndian(item.Content);
                    }
                }
                    
                //参数个数与参数长度
                if (strFunctionCode == FunctionCode.TEST_COMMUNICATION || (strFunctionCode == FunctionCode.ACQUISITION && strAcquisitionExecutedCode == AcquisitionExecutedCode.ACQUISITION))
                {
                    strLength = MessageLengthToLittleEndian(strData);
                }
                else if (strFunctionCode == FunctionCode.TEST_COMMUNICATION || (strFunctionCode == FaultFunctionCode.ACQUISITION && strAcquisitionExecutedCode == FaultAcquisitionExecutedCode.ACQUISITION))//由Lilbert于20230403添加故障数据采集
                {
                    strLength = MessageLengthToLittleEndian(strData);
                }
                else if (strFunctionCode == FunctionCode.PARAMETER_READ || strFunctionCode == FunctionCode.PARAMETER_WRITE)
                {
                    strNumber = BitConverter.ToString(BitConverter.GetBytes((short)TransmittingDataInfo.Count)).Replace("-", "");
                    strLength = MessageLengthToLittleEndian(strNumber + strData);
                }

                //CRC校验报文拼接
                switch (strFunctionCode)
                {
                    case FunctionCode.TEST_COMMUNICATION:
                        strMessage = strLength + strStationID + strFunctionCode + strData;
                        break;
                    case FunctionCode.PARAMETER_READ:
                        strMessage = strLength + strStationID + strFunctionCode + strNumber + strData;
                        break;
                    case FunctionCode.PARAMETER_WRITE:
                        strMessage = strLength + strStationID + strFunctionCode + strNumber + strData;
                        break;
                    case FunctionCode.HARDWAREALARM:
                        strMessage = "0700" + strStationID + FunctionCode.HARDWAREALARM + strAxisID;
                        break;
                    case FunctionCode.UPDATE:
                        strMessage = "0700" + strStationID + FunctionCode.UPDATE + strData;
                        break;
                    case FunctionCode.ACQUISITION:
                        if (strAcquisitionExecutedCode == AcquisitionExecutedCode.ACQUISITION)
                        {
                            strMessage = strLength + strStationID + FunctionCode.ACQUISITION + strData;
                        }
                        else
                        {
                            strMessage = "0800" + strStationID + FunctionCode.ACQUISITION + strAxisID + strAcquisitionExecutedCode;
                        }                                    
                        break;
                    case FaultFunctionCode.ACQUISITION:
                        if (strAcquisitionExecutedCode == FaultAcquisitionExecutedCode.ACQUISITION)
                        {
                            strMessage = strLength + strStationID + FaultFunctionCode.ACQUISITION + strData;
                        }
                        else
                        {
                            strMessage = "0800" + strStationID + FaultFunctionCode.ACQUISITION + strAxisID + strAcquisitionExecutedCode;
                        }
                        break;
                    default:
                        break;
                }

                string temp = MessageFormat.HEAD + strMessage + CRC16(strMessage) + MessageFormat.END;
                //最终报文拼接                       
                bMessage = HexStringToBytes(MessageFormat.HEAD + strMessage + CRC16(strMessage) + MessageFormat.END);

                //Console.WriteLine("发送指令：-------------------------");
                //Console.WriteLine("");
                //Console.WriteLine(BytesToHexString(bMessage));
                //Console.WriteLine("");
                //Console.WriteLine("完成发送指令!-------------------------");
                //Console.WriteLine("");
                //Console.WriteLine("");

                if (bMessage == null)
                {
                    return RET.NO_EFFECT;
                }
                else if (bMessage.Length == 0)
                {
                    return RET.NO_EFFECT;
                }
                else
                {
                    return RET.SUCCEEDED;
                }                        
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_TRANSMITTING_MESSAGE_TO_BYTES, "TransmittingMessageToBytes", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：LittleEndianHexStringToNormalString
        //函数功能：小端16进制转换正常字符串
        //
        //输入参数：string HexString     16进制小端字符串
        //       
        //输出参数：转换后的字符串
        //        
        //编码作者：Ryan
        //更新时间：2019.12.16
        //*************************************************************************
        public static string LittleEndianHexStringToNormalString(string HexString)
        {
            try
            {
                byte[] bytes = new byte[HexString.Length / 2];
                for (int i = 0; i < bytes.Length; i++)
                {
                    bytes[i] = byte.Parse(HexString.Substring(i * 2, 2), System.Globalization.NumberStyles.HexNumber);
                }

                Array.Reverse(bytes);
                return BitConverter.ToString(bytes).Replace("-", "");
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_LITTLE_ENDIAN_HEX_STRING_TO_NORMAL_STRING, "LittleEndianHexStringToNormalString", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：AddSerialPortTask
        //函数功能：添加串口任务
        //
        //输入参数：string strCurrentPage
        //         string strStationID 
        //         string strAxisID     
        //         string strTaskName
        //         string strFunctionCode
        //         string strAcquisitionExecutedCode
        //         List<TransmitingDataInfoSet> lstData
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Ryan
        //更新时间：2020.01.18
        //*************************************************************************
        public static int AddSerialPortTask(string strCurrentPage, string strSlaveID, string strAxisID, string strTaskName, string strFunctionCode, string strAcquisitionExecutedCode, List<TransmitingDataInfoSet> lstData)
        {
            try
            {
                if (lstData == null)
                {
                    return RET.ERROR;
                }

                if (SerialPortTask.TaskManagement == null)
                {
                    SerialPortTask.TaskManagement = new List<TaskManagementSet>();
                }
                    
                TaskManagementSet clsTaskManagementSet = new TaskManagementSet();
                clsTaskManagementSet.TaskName = strTaskName;
                //clsTaskManagementSet.StationID = strStationID;
                clsTaskManagementSet.SlaveID = strSlaveID;
                clsTaskManagementSet.AxisID = strAxisID;
                clsTaskManagementSet.FunctionCode = strFunctionCode;
                clsTaskManagementSet.AcquisitionExecutedCode = strAcquisitionExecutedCode;
                clsTaskManagementSet.CurrentPageName = strCurrentPage;
                lstData.ForEach(item => clsTaskManagementSet.TransmittingDataInfo.Add(item));

                //若为间隔刷新-IntervalRefresh任务，则要求把任务优先集降到最低，放置到任务栈底
                if (strTaskName == TaskName.IntervalRefresh)
                {
                    SerialPortTask.TaskManagement.Insert(0, clsTaskManagementSet);
                }
                //else if (strTaskName == TaskName.AskingAcquisitionState || strTaskName == TaskName.UploadingAcquisition)
                //{
                //    //若有IntervalRefresh任务，把当前任务插到IntervalRefresh上一个任务栈中
                //    //若当前没有IntervalRefresh任务，把当前任务插到栈底
                //    int iRet = SerialPortTask.TaskManagement.FindLastIndex(item => item.TaskName == TaskName.IntervalRefresh);
                //    SerialPortTask.TaskManagement.Insert(iRet + 1, clsTaskManagementSet);
                //}
                else if (strTaskName == TaskName.AskingAcquisitionState || strTaskName == TaskName.UploadingAcquisition)
                {
                    int iIntervalRefreshIndex = SerialPortTask.TaskManagement.FindLastIndex(item => item.TaskName == TaskName.IntervalRefresh);

                    if (iIntervalRefreshIndex == -1)//若不存在IntervalRefresh任务，把AskingAcquisitionState任务放置到栈底
                    {
                        SerialPortTask.TaskManagement.Insert(0, clsTaskManagementSet);
                        SoftwareStateParameterSet.PutAcquisitionStateBottom = false;
                    }
                    else//若存在IntervalRefresh任务，交替放入栈底
                    {
                        if (SoftwareStateParameterSet.PutAcquisitionStateBottom)
                        {
                            SerialPortTask.TaskManagement.Insert(0, clsTaskManagementSet);
                            SoftwareStateParameterSet.PutAcquisitionStateBottom = false;
                        }
                        else
                        {
                            SerialPortTask.TaskManagement.Insert(iIntervalRefreshIndex + 1, clsTaskManagementSet);
                            SoftwareStateParameterSet.PutAcquisitionStateBottom = true;
                        }
                    }
                }
                else
                {
                    SerialPortTask.TaskManagement.Add(clsTaskManagementSet);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_ADD_SERIAL_PORT_TASK, "AddSerialPortTask", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AddSerialPortTask_ForImportConfig
        //函数功能：添加串口任务
        //
        //输入参数：string strCurrentPage
        //         string strStationID 
        //         string strAxisID     
        //         string strTaskName
        //         string strFunctionCode
        //         string strAcquisitionExecutedCode
        //         List<TransmitingDataInfoSet> lstData
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public static int AddSerialPortTask_ForImportConfig(string strCurrentPage, string strSlaveID, string strAxisID, string strTaskName, string strFunctionCode, string strAcquisitionExecutedCode, List<TransmitingDataInfoSet> lstData)
        {
            try
            {
                if (lstData == null)
                {
                    return RET.ERROR;
                }

                if (SerialPortTask.TaskManagement == null)
                {
                    SerialPortTask.TaskManagement = new List<TaskManagementSet>();
                }

                TaskManagementSet clsTaskManagementSet = new TaskManagementSet();
                clsTaskManagementSet.TaskName = strTaskName;
                //clsTaskManagementSet.StationID = strStationID;
                clsTaskManagementSet.SlaveID = strSlaveID;
                clsTaskManagementSet.AxisID = strAxisID;
                clsTaskManagementSet.FunctionCode = strFunctionCode;
                clsTaskManagementSet.AcquisitionExecutedCode = strAcquisitionExecutedCode;
                clsTaskManagementSet.CurrentPageName = strCurrentPage;
                lstData.ForEach(item => clsTaskManagementSet.TransmittingDataInfo.Add(item));

                //若为间隔刷新-IntervalRefresh任务，则要求把任务优先集降到最低，放置到任务栈底
                if (strTaskName == TaskName.IntervalRefresh)
                {
                    SerialPortTask.TaskManagement.Insert(0, clsTaskManagementSet);
                }
                //else if (strTaskName == TaskName.AskingAcquisitionState || strTaskName == TaskName.UploadingAcquisition)
                //{
                //    //若有IntervalRefresh任务，把当前任务插到IntervalRefresh上一个任务栈中
                //    //若当前没有IntervalRefresh任务，把当前任务插到栈底
                //    int iRet = SerialPortTask.TaskManagement.FindLastIndex(item => item.TaskName == TaskName.IntervalRefresh);
                //    SerialPortTask.TaskManagement.Insert(iRet + 1, clsTaskManagementSet);
                //}
                else if (strTaskName == TaskName.AskingAcquisitionState || strTaskName == TaskName.UploadingAcquisition)
                {
                    int iIntervalRefreshIndex = SerialPortTask.TaskManagement.FindLastIndex(item => item.TaskName == TaskName.IntervalRefresh);

                    if (iIntervalRefreshIndex == -1)//若不存在IntervalRefresh任务，把AskingAcquisitionState任务放置到栈底
                    {
                        SerialPortTask.TaskManagement.Insert(0, clsTaskManagementSet);
                        SoftwareStateParameterSet.PutAcquisitionStateBottom = false;
                    }
                    else//若存在IntervalRefresh任务，交替放入栈底
                    {
                        if (SoftwareStateParameterSet.PutAcquisitionStateBottom)
                        {
                            SerialPortTask.TaskManagement.Insert(0, clsTaskManagementSet);
                            SoftwareStateParameterSet.PutAcquisitionStateBottom = false;
                        }
                        else
                        {
                            SerialPortTask.TaskManagement.Insert(iIntervalRefreshIndex + 1, clsTaskManagementSet);
                            SoftwareStateParameterSet.PutAcquisitionStateBottom = true;
                        }
                    }
                }
                else
                {
                    SerialPortTask.TaskManagement.Add(clsTaskManagementSet);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_ADD_SERIAL_PORT_TASK, "AddSerialPortTask", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AddSerialPortTask_For_Fault
        //函数功能：添加串口任务-故障数据采集
        //
        //输入参数：string strCurrentPage
        //         string strStationID 
        //         string strAxisID     
        //         string strTaskName
        //         string strFunctionCode
        //         string strAcquisitionExecutedCode
        //         List<TransmitingDataInfoSet> lstData
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.03
        //*************************************************************************
        public static int AddSerialPortTask_For_Fault(string strCurrentPage, string strSlaveID, string strAxisID, string strTaskName, string strFunctionCode, string strAcquisitionExecutedCode, List<TransmitingDataInfoSet> lstData)
        {
            try
            {
                if (lstData == null)
                {
                    return RET.ERROR;
                }

                if (SerialPortTask.TaskManagement == null)
                {
                    SerialPortTask.TaskManagement = new List<TaskManagementSet>();
                }

                TaskManagementSet clsTaskManagementSet = new TaskManagementSet();
                clsTaskManagementSet.TaskName = strTaskName;
                //clsTaskManagementSet.StationID = strStationID;
                clsTaskManagementSet.SlaveID = strSlaveID;
                clsTaskManagementSet.AxisID = strAxisID;
                clsTaskManagementSet.FunctionCode = strFunctionCode;
                clsTaskManagementSet.AcquisitionExecutedCode = strAcquisitionExecutedCode;
                clsTaskManagementSet.CurrentPageName = strCurrentPage;
                lstData.ForEach(item => clsTaskManagementSet.TransmittingDataInfo.Add(item));

                //若为间隔刷新-IntervalRefresh任务，则要求把任务优先集降到最低，放置到任务栈底
                if (strTaskName == TaskName.IntervalRefresh)
                {
                    SerialPortTask.TaskManagement.Insert(0, clsTaskManagementSet);
                }
                //else if (strTaskName == TaskName.AskingAcquisitionState || strTaskName == TaskName.UploadingAcquisition)
                //{
                //    //若有IntervalRefresh任务，把当前任务插到IntervalRefresh上一个任务栈中
                //    //若当前没有IntervalRefresh任务，把当前任务插到栈底
                //    int iRet = SerialPortTask.TaskManagement.FindLastIndex(item => item.TaskName == TaskName.IntervalRefresh);
                //    SerialPortTask.TaskManagement.Insert(iRet + 1, clsTaskManagementSet);
                //}
                else if (strTaskName == TaskName.FaultAskingAcquisitionState || strTaskName == TaskName.UploadingAcquisitionFault)
                {
                    int iIntervalRefreshIndex = SerialPortTask.TaskManagement.FindLastIndex(item => item.TaskName == TaskName.IntervalRefresh);

                    if (iIntervalRefreshIndex == -1)//若不存在IntervalRefresh任务，把AskingAcquisitionState任务放置到栈底
                    {
                        SerialPortTask.TaskManagement.Insert(0, clsTaskManagementSet);
                        SoftwareStateParameterSet.PutAcquisitionStateBottom = false;
                    }
                    else//若存在IntervalRefresh任务，交替放入栈底
                    {
                        if (SoftwareStateParameterSet.PutAcquisitionStateBottom)
                        {
                            SerialPortTask.TaskManagement.Insert(0, clsTaskManagementSet);
                            SoftwareStateParameterSet.PutAcquisitionStateBottom = false;
                        }
                        else
                        {
                            SerialPortTask.TaskManagement.Insert(iIntervalRefreshIndex + 1, clsTaskManagementSet);
                            SoftwareStateParameterSet.PutAcquisitionStateBottom = true;
                        }
                    }
                }
                else
                {
                    SerialPortTask.TaskManagement.Add(clsTaskManagementSet);
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_ADD_SERIAL_PORT_TASK, "AddSerialPortTask", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForTestCommunication
        //函数功能：接收数据——用于通信测试
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //         1: SUCCEED
        //        -1: ERROR
        //        
        //编码作者：Ryan
        //更新时间：2019.12.13
        //*************************************************************************
        public static int ReceivingMessage_ForTestCommunication()
        {
            try
            {
                //获取接收参数
                if (CommunicationSet.Transmiting == null) return RET.ERROR;
                if (CommunicationSet.Receiving == null) return RET.ERROR;

                //发送接收帧长度必须相同
                if (CommunicationSet.Transmiting.Length != CommunicationSet.Receiving.Length) return RET.ERROR;

                //发送接收帧每一个字节都要相同
                for (int i = 0; i < CommunicationSet.Transmiting.Length; i++)
                {
                    if (CommunicationSet.Transmiting[i] != CommunicationSet.Receiving[i])
                        return RET.ERROR;
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_TEST_COMMUNICATION, "ReceivingMessage_ForTestCommunication", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForParameterRead
        //函数功能：接收数据——用于参数读取
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //         1: SUCCEED
        //        -1: ERROR
        //        
        //编码作者：Ryan
        //更新时间：2019.12.13
        //*************************************************************************
        public static int ReceivingMessage_ForParameterRead()
        {
            int iIndex = 0;//List索引号
            int iLength_Cut = 0;//截取字符串长度
            int iLength_Receiving = 0;//接收帧长度
            int iParameterNumber = 0;//参数个数

            string strMessage_Receiving = null;//接收帧
            string strCRC_Receiving = null;//接收帧CRC
            string strCRC_Check = null;//接收检测帧CRC            
            string strParameterNumber = null;//参数个数

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.ERROR;
                }

                //判断接收帧的长度不可以小于10个字节
                iLength_Receiving = strMessage_Receiving.Length;
                if (iLength_Receiving < 20)
                {
                    return RET.ERROR;
                }

                //判断起始位和结束位
                if (strMessage_Receiving.Substring(0, 2) != MessageFormat.HEAD || strMessage_Receiving.Substring(iLength_Receiving - 2, 2) != MessageFormat.END)
                {
                    return RET.ERROR;
                }

                //判断CRC
                strCRC_Receiving = strMessage_Receiving.Substring(iLength_Receiving - 6, 4);
                strCRC_Check = CRC16(strMessage_Receiving.Substring(2, iLength_Receiving - 8));
                if (strCRC_Check != strCRC_Receiving)
                {
                    return RET.ERROR;
                }

                //判断参数个数
                strParameterNumber = LittleEndianHexStringToNormalString(strMessage_Receiving.Substring(10, 4));
                iParameterNumber = System.Int32.Parse(strParameterNumber, System.Globalization.NumberStyles.HexNumber);
                if (iParameterNumber != CommunicationSet.TransmittingDataInfo.Count)
                {
                    return RET.ERROR;
                }

                //截断字符串，抽离地址与数值
                for (int iBeginning_Cut = 14; iBeginning_Cut < iLength_Receiving - 6;)
                {
                    string ContentTemp = LittleEndianHexStringToNormalString(strMessage_Receiving.Substring(iBeginning_Cut, 8));
                    switch (ContentTemp.Substring(6, 2))
                    {
                        case "20":
                            iLength_Cut = 8;
                            break;
                        case "10":
                            iLength_Cut = 4;
                            break;
                        case "08":
                            iLength_Cut = 2;
                            break;
                        default:
                            break;
                    }

                    iBeginning_Cut += 8;
                    string ValueTemp = LittleEndianHexStringToNormalString(strMessage_Receiving.Substring(iBeginning_Cut, iLength_Cut));

                    iIndex = CommunicationSet.TransmittingDataInfo.FindIndex(Item => Item.Content == ContentTemp);
                    if (iIndex != -1)
                    {
                        CommunicationSet.TransmittingDataInfo[iIndex].Value = ValueTemp;
                    }

                    iBeginning_Cut += iLength_Cut;
                }

                //参数值解析
                for (int i = 0; i < CommunicationSet.TransmittingDataInfo.Count; i++)
                {
                    if (string.IsNullOrEmpty(CommunicationSet.TransmittingDataInfo[i].Value)) continue;
               
                    if (CommunicationSet.TransmittingDataInfo[i].DataType.ToUpper() == "INT32")
                    {
                        int i32temp = Convert.ToInt32(CommunicationSet.TransmittingDataInfo[i].Value, 16);
                        CommunicationSet.TransmittingDataInfo[i].Value = i32temp.ToString();
                    }
                    else if (CommunicationSet.TransmittingDataInfo[i].DataType.ToUpper() == "UINT32")
                    {
                        UInt32 u32temp = Convert.ToUInt32(CommunicationSet.TransmittingDataInfo[i].Value, 16);
                        CommunicationSet.TransmittingDataInfo[i].Value = u32temp.ToString();
                    }
                    else if (CommunicationSet.TransmittingDataInfo[i].DataType.ToUpper() == "INT16")
                    {
                        short i16temp = Convert.ToInt16(CommunicationSet.TransmittingDataInfo[i].Value, 16);
                        CommunicationSet.TransmittingDataInfo[i].Value = i16temp.ToString();
                    }
                    else if (CommunicationSet.TransmittingDataInfo[i].DataType.ToUpper() == "UINT16")
                    {
                        ushort u16temp = Convert.ToUInt16(CommunicationSet.TransmittingDataInfo[i].Value, 16);
                        CommunicationSet.TransmittingDataInfo[i].Value = u16temp.ToString();
                    }
                    else if (CommunicationSet.TransmittingDataInfo[i].DataType.ToUpper() == "UINT8")
                    {
                        byte b8temp = Convert.ToByte(CommunicationSet.TransmittingDataInfo[i].Value, 16);
                        CommunicationSet.TransmittingDataInfo[i].Value = b8temp.ToString();
                    }
                    else if (CommunicationSet.TransmittingDataInfo[i].DataType.ToUpper() == "INT8")
                    {
                        sbyte sb8temp = Convert.ToSByte(CommunicationSet.TransmittingDataInfo[i].Value, 16);
                        CommunicationSet.TransmittingDataInfo[i].Value = sb8temp.ToString();
                    }                                  
                }

                //更新全局数据
                if (CommunicationSet.TaskName == TaskName.AxisStatusA)
                {
                    ARMStatus.AxisValueA = CommunicationSet.TransmittingDataInfo[0].Value;
                }
                else if (CommunicationSet.TaskName == TaskName.AxisStatusB)
                {
                    ARMStatus.AxisValueB = CommunicationSet.TransmittingDataInfo[0].Value;
                }
                else
                {
                    for (int i = 0; i < CommunicationSet.TransmittingDataInfo.Count; i++)
                    {
                        if (OthersHelper.SelectAxisNumber() == AxisNumber.A)
                        {
                            if (CommunicationSet.CurrentPointValue_AxisA.ContainsKey(CommunicationSet.TransmittingDataInfo[i].Address))
                            {
                                CommunicationSet.CurrentPointValue_AxisA[CommunicationSet.TransmittingDataInfo[i].Address] = CommunicationSet.TransmittingDataInfo[i].Value;
                            }
                        }
                        else
                        {
                            if (CommunicationSet.CurrentPointValue_AxisB.ContainsKey(CommunicationSet.TransmittingDataInfo[i].Address))
                            {
                                CommunicationSet.CurrentPointValue_AxisB[CommunicationSet.TransmittingDataInfo[i].Address] = CommunicationSet.TransmittingDataInfo[i].Value;
                            }
                        }
                    }
                }
                         
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_READ, "ReceivingMessage_ForParameterRead", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForParameterReadString
        //函数功能：接收数据——用于参数读取字符串
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //         1: SUCCEED
        //        -1: ERROR
        //        
        //编码作者：Ryan
        //更新时间：2019.12.13
        //*************************************************************************
        public static int ReceivingMessage_ForParameterReadString()
        {
            int iIndex = 0;//List索引号
            int iLength_Cut = 0;//截取字符串长度
            int iLength_Receiving = 0;//接收帧长度
            int iParameterNumber = 0;//参数个数

            string strMessage_Receiving = null;//接收帧
            string strCRC_Receiving = null;//接收帧CRC
            string strCRC_Check = null;//接收检测帧CRC            
            string strParameterNumber = null;//参数个数

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.ERROR;
                }

                //判断接收帧的长度不可以小于10个字节
                iLength_Receiving = strMessage_Receiving.Length;
                if (iLength_Receiving < 20)
                {
                    return RET.ERROR;
                }

                //判断起始位和结束位
                if (strMessage_Receiving.Substring(0, 2) != MessageFormat.HEAD || strMessage_Receiving.Substring(iLength_Receiving - 2, 2) != MessageFormat.END)
                {
                    return RET.ERROR;
                }

                //判断CRC
                strCRC_Receiving = strMessage_Receiving.Substring(iLength_Receiving - 6, 4);
                strCRC_Check = CRC16(strMessage_Receiving.Substring(2, iLength_Receiving - 8));
                if (strCRC_Check != strCRC_Receiving)
                {
                    return RET.ERROR;
                }

                //判断参数个数
                strParameterNumber = LittleEndianHexStringToNormalString(strMessage_Receiving.Substring(10, 4));
                iParameterNumber = System.Int32.Parse(strParameterNumber, System.Globalization.NumberStyles.HexNumber);
                if (iParameterNumber != CommunicationSet.TransmittingDataInfo.Count)
                {
                    return RET.ERROR;
                }

                //截断字符串，抽离地址与数值
                for (int iBeginning_Cut = 14; iBeginning_Cut < iLength_Receiving - 6;)
                {
                    string ContentTemp = LittleEndianHexStringToNormalString(strMessage_Receiving.Substring(iBeginning_Cut, 8));
                    iLength_Cut = Convert.ToInt32(ContentTemp.Substring(6, 2), 16) * 2;

                    iBeginning_Cut += 8;
                    string ValueTemp = strMessage_Receiving.Substring(iBeginning_Cut, iLength_Cut);

                    iIndex = CommunicationSet.TransmittingDataInfo.FindIndex(Item => Item.Content == ContentTemp.Substring(0, 6) + "00");
                    if (iIndex != -1)
                    {
                        CommunicationSet.TransmittingDataInfo[iIndex].Value = ValueTemp;
                    }

                    iBeginning_Cut += iLength_Cut;
                }

                //参数值解析
                for (int i = 0; i < CommunicationSet.TransmittingDataInfo.Count; i++)
                {
                    if (string.IsNullOrEmpty(CommunicationSet.TransmittingDataInfo[i].Value)) continue;
               
                    if (CommunicationSet.TransmittingDataInfo[i].DataType.ToUpper() == "STRING")
                    {
                        byte[] buffer = HexStringToBytes(CommunicationSet.TransmittingDataInfo[i].Value);
                        if (buffer != null)
                        {
                            CommunicationSet.TransmittingDataInfo[i].Value = Encoding.Default.GetString(buffer);
                        }
                    }                 
                }

                //更新全局数据
                for (int i = 0; i < CommunicationSet.TransmittingDataInfo.Count; i++)
                {
                    if (OthersHelper.SelectAxisNumber() == AxisNumber.A)
                    {
                        if (CommunicationSet.CurrentPointValue_AxisA.ContainsKey(CommunicationSet.TransmittingDataInfo[i].Address))
                        {
                            CommunicationSet.CurrentPointValue_AxisA[CommunicationSet.TransmittingDataInfo[i].Address] = CommunicationSet.TransmittingDataInfo[i].Value;
                        }
                    }
                    else
                    {
                        if (CommunicationSet.CurrentPointValue_AxisB.ContainsKey(CommunicationSet.TransmittingDataInfo[i].Address))
                        {
                            CommunicationSet.CurrentPointValue_AxisB[CommunicationSet.TransmittingDataInfo[i].Address] = CommunicationSet.TransmittingDataInfo[i].Value;
                        }
                    } 
                    
                    switch (CommunicationSet.TransmittingDataInfo[i].Address)
                    {
                        case "100800":
                            SoftwareStateParameterSet.ServoName = CommunicationSet.TransmittingDataInfo[i].Value;
                            break;
                        case "100900":
                            SoftwareStateParameterSet.ServoHardwareVersion = CommunicationSet.TransmittingDataInfo[i].Value;
                            break;
                        case "100A00":
                            SoftwareStateParameterSet.ServoSoftwareVersion = CommunicationSet.TransmittingDataInfo[i].Value;
                            break;
                        default:
                            break;
                    }                  
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_READ_STRING, "ReceivingMessage_ForParameterReadString", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForParameterWrite
        //函数功能：接收数据——用于参数写入
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //        -1：ERROR
        //         1：OK
        //        
        //编码作者：Ryan
        //更新时间：2019.12.22
        //*************************************************************************
        public static int ReceivingMessage_ForParameterWrite()
        {
            string strMessage_Receiving = null;//实际收到的数据帧
            string strMessage_Check = null;//应该接到的数据帧
            string strLength_Check = null;//应该接收到的帧长度
            string strNumber_Check = null;//应该接收到的参数个数
            string strCRC_Check = null;//应该接收到的数据帧的CRC

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.NO_EFFECT;
                }

                strLength_Check = ConvertHexStringEndian("0008");
                strNumber_Check = BitConverter.ToString(BitConverter.GetBytes((short)CommunicationSet.TransmittingDataInfo.Count)).Replace("-", "");
                //strMessage_Check = strLength_Check + CommunicationSet.StationID + CommunicationSet.FunctionCode + strNumber_Check;
                strMessage_Check = strLength_Check + CommunicationSet.SlaveID + CommunicationSet.FunctionCode + strNumber_Check;
                strCRC_Check = CRC16(strMessage_Check);

                strMessage_Check = MessageFormat.HEAD + strMessage_Check + strCRC_Check + MessageFormat.END;
                if (strMessage_Check != strMessage_Receiving)
                {
                    return RET.NO_EFFECT;
                }

                //更新全局数据
                for (int i = 0; i < CommunicationSet.TransmittingDataInfo.Count; i++)
                {
                    if (OthersHelper.SelectAxisNumber() == AxisNumber.A)
                    {
                        if (CommunicationSet.CurrentPointValue_AxisA.ContainsKey(CommunicationSet.TransmittingDataInfo[i].Address))
                        {
                            CommunicationSet.CurrentPointValue_AxisA[CommunicationSet.TransmittingDataInfo[i].Address] = CommunicationSet.TransmittingDataInfo[i].Value;
                        }
                    }
                    else
                    {
                        if (CommunicationSet.CurrentPointValue_AxisB.ContainsKey(CommunicationSet.TransmittingDataInfo[i].Address))
                        {
                            CommunicationSet.CurrentPointValue_AxisB[CommunicationSet.TransmittingDataInfo[i].Address] = CommunicationSet.TransmittingDataInfo[i].Value;
                        }

                    }
                }
             
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_WRITE, "ReceivingMessage_ForParameterWrite", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForParameterAcquisition
        //函数功能：接收数据——用于数据采集
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //        -1：ERROR
        //         1：OK
        //        
        //编码作者：Ryan
        //更新时间：2019.12.25
        //*************************************************************************
        public static int ReceivingMessage_ForAcquisition()
        {   
            string strMessage_Receiving = null;//实际收到的数据帧
            string strMessage_Check = null;//应该接到的数据帧
            string strLength_Check = null;//应该接收到的帧长度
            string strReturn_Check = null;//应该接收到的参数个数
            string strCRC_Check = null;//应该接收到的数据帧的CRC

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.NO_EFFECT;
                }

                strLength_Check = ConvertHexStringEndian("000A");
                strReturn_Check = ConvertHexStringEndian("4B4F");
                //strMessage_Check = strLength_Check + CommunicationSet.StationID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ACQUISITION + strReturn_Check;
                strMessage_Check = strLength_Check + CommunicationSet.SlaveID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ACQUISITION + strReturn_Check;
                strCRC_Check = CRC16(strMessage_Check);

                strMessage_Check = MessageFormat.HEAD + strMessage_Check + strCRC_Check + MessageFormat.END;

                if (strMessage_Check == strMessage_Receiving)
                {
                    return RET.SUCCEEDED;
                }
                else
                {
                    return RET.NO_EFFECT;
                }               
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_ACQUISITION, "ReceivingMessage_ForParameterAcquisition", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForFaultAcquisition
        //函数功能：接收数据——用于故障数据采集
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //        -1：ERROR
        //         1：OK
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.03
        //*************************************************************************
        public static int ReceivingMessage_ForFaultAcquisition()
        {
            string strMessage_Receiving = null;//实际收到的数据帧
            string strMessage_Check = null;//应该接到的数据帧
            string strLength_Check = null;//应该接收到的帧长度
            string strReturn_Check = null;//应该接收到的参数个数
            string strCRC_Check = null;//应该接收到的数据帧的CRC

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.NO_EFFECT;
                }

                strLength_Check = ConvertHexStringEndian("000A");
                strReturn_Check = ConvertHexStringEndian("4B4F");
                //strMessage_Check = strLength_Check + CommunicationSet.StationID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ACQUISITION + strReturn_Check;
                strMessage_Check = strLength_Check + CommunicationSet.SlaveID + FaultFunctionCode.ACQUISITION + CommunicationSet.AxisID + FaultAcquisitionExecutedCode.ACQUISITION + strReturn_Check;
                strCRC_Check = CRC16(strMessage_Check);

                strMessage_Check = MessageFormat.HEAD + strMessage_Check + strCRC_Check + MessageFormat.END;

                if (strMessage_Check == strMessage_Receiving)
                {
                    return RET.SUCCEEDED;
                }
                else
                {
                    return RET.NO_EFFECT;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PAULT_ACQUISITION, "ReceivingMessage_ForFaultAcquisition", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForAskingAcquisitionState
        //函数功能：接收数据——用于数据采集
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //        -1：ERROR
        //         1：OK
        //        
        //编码作者：Ryan
        //更新时间：2019.12.25
        //*************************************************************************
        public static int ReceivingMessage_ForAskingAcquisitionState()
        {
            string strMessage_Receiving = null;//实际收到的数据帧
            string strMessage_Doing_Check = null;//应该接到的数据帧
            string strMessage_Done_Check = null;//应该接收到的帧长度
            string strMessage_NoReceiving_Check = null;//应该接收到的帧长度

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.NO_EFFECT;
                }

                //strMessage_Doing_Check = "0A00" + CommunicationSet.StationID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ASK_ACQUISITION + "0100";
                strMessage_Doing_Check = "0A00" + CommunicationSet.SlaveID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ASK_ACQUISITION + "0100";
                strMessage_Doing_Check += CRC16(strMessage_Doing_Check);
                strMessage_Doing_Check = MessageFormat.HEAD + strMessage_Doing_Check + MessageFormat.END;

                //strMessage_Done_Check = "0A00" + CommunicationSet.StationID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ASK_ACQUISITION + "0200";
                strMessage_Done_Check = "0A00" + CommunicationSet.SlaveID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ASK_ACQUISITION + "0200";
                strMessage_Done_Check += CRC16(strMessage_Done_Check);
                strMessage_Done_Check = MessageFormat.HEAD + strMessage_Done_Check + MessageFormat.END;

                //strMessage_NoReceiving_Check = "0A00" + CommunicationSet.StationID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ASK_ACQUISITION + "0000";
                strMessage_NoReceiving_Check = "0A00" + CommunicationSet.SlaveID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ASK_ACQUISITION + "0000";
                strMessage_NoReceiving_Check += CRC16(strMessage_NoReceiving_Check);
                strMessage_NoReceiving_Check = MessageFormat.HEAD + strMessage_NoReceiving_Check + MessageFormat.END;

                if (strMessage_Doing_Check == strMessage_Receiving)
                {
                    return 100;
                }
                else if (strMessage_Done_Check == strMessage_Receiving)
                {
                    return 101;
                }
                else if (strMessage_NoReceiving_Check == strMessage_Receiving)
                {
                    return 102;
                }
                else
                {
                    return 103;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_ACQUISITION, "ReceivingMessage_ForParameterAcquisition", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForFaultAskingAcquisitionState
        //函数功能：接收数据——用于故障数据采集
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //        -1：ERROR
        //         1：OK
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.03
        //*************************************************************************
        public static int ReceivingMessage_ForFaultAskingAcquisitionState()
        {
            string strMessage_Receiving = null;//实际收到的数据帧
            string strMessage_Doing_Check = null;//应该接到的数据帧
            //string strMessage_Done_Check = null;//应该接收到的帧长度
            string strMessage_NoReceiving_Check = null;//应该接收到的帧长度

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.NO_EFFECT;
                }

                //strMessage_Doing_Check = "0A00" + CommunicationSet.StationID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ASK_ACQUISITION + "0100";
                strMessage_Doing_Check = "0A00" + CommunicationSet.SlaveID + FaultFunctionCode.ACQUISITION + CommunicationSet.AxisID + FaultAcquisitionExecutedCode.ASK_ACQUISITION + "0100"; //采集完毕
                strMessage_Doing_Check += CRC16(strMessage_Doing_Check);
                strMessage_Doing_Check = MessageFormat.HEAD + strMessage_Doing_Check + MessageFormat.END;

                //strMessage_Done_Check = "0A00" + CommunicationSet.StationID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ASK_ACQUISITION + "0200";
                //strMessage_Done_Check = "0A00" + CommunicationSet.SlaveID + FaultFunctionCode.ACQUISITION + CommunicationSet.AxisID + FaultAcquisitionExecutedCode.ASK_ACQUISITION + "0200"; //采集完毕
                //strMessage_Done_Check += CRC16(strMessage_Done_Check);
                //strMessage_Done_Check = MessageFormat.HEAD + strMessage_Done_Check + MessageFormat.END;

                //strMessage_NoReceiving_Check = "0A00" + CommunicationSet.StationID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.ASK_ACQUISITION + "0000";
                strMessage_NoReceiving_Check = "0A00" + CommunicationSet.SlaveID + FaultFunctionCode.ACQUISITION + CommunicationSet.AxisID + FaultAcquisitionExecutedCode.ASK_ACQUISITION + "0000";  //未收到采样指令
                strMessage_NoReceiving_Check += CRC16(strMessage_NoReceiving_Check);
                strMessage_NoReceiving_Check = MessageFormat.HEAD + strMessage_NoReceiving_Check + MessageFormat.END;

                if (strMessage_Doing_Check == strMessage_Receiving) //采集完毕
                {
                    return 101;
                }
                //else if (strMessage_Done_Check == strMessage_Receiving) //采集完毕
                //{
                //    return 101;
                //}
                else if (strMessage_NoReceiving_Check == strMessage_Receiving) //未收到采样指令
                {
                    return 102;
                }
                else
                {
                    return 103; //接收数据帧错误
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_ACQUISITION, "ReceivingMessage_ForParameterAcquisition", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForUploading
        //函数功能：接收数据——用于数据上传
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //        -1：ERROR
        //         1：OK
        //        
        //编码作者：Ryan
        //更新时间：2019.12.25
        //*************************************************************************
        public static int ReceivingMessage_ForUploading()
        {
            int iLength_Receiving = 0;//接收帧长度
            string strMessage_Receiving = null;//接收帧
            string strCRC_Receiving = null;//接收帧CRC
            string strCRC_Check = null;//接收检测帧CRC            
            List<string> lstGroupData = new List<string>();//组数据

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.NO_EFFECT;
                }

                //判断接收帧的长度不可以小于10个字节
                iLength_Receiving = strMessage_Receiving.Length;
                if (iLength_Receiving < 20)
                {
                    return RET.ERROR;
                }

                //判断起始位和结束位
                if (strMessage_Receiving.Substring(0, 2) != MessageFormat.HEAD || strMessage_Receiving.Substring(iLength_Receiving - 2, 2) != MessageFormat.END)
                {
                    return RET.ERROR;
                }

                //判断CRC
                strCRC_Receiving = strMessage_Receiving.Substring(iLength_Receiving - 6, 4);
                strCRC_Check = CRC16(strMessage_Receiving.Substring(2, iLength_Receiving - 8));
                if (strCRC_Check != strCRC_Receiving)
                {
                    return RET.ERROR;
                }
              
                //获取采样数据
                for (int i = 18; i < iLength_Receiving - 6; i += 8)
                {
                    string strtemp = ConvertHexStringEndian(strMessage_Receiving.Substring(i, 8));
                    int itemp = System.Int32.Parse(strtemp, System.Globalization.NumberStyles.HexNumber);
                    AcquisitionInfoSet.lstReceiving[AcquisitionInfoSet.ChannelNumberOfCurrentMessage].Add(itemp);
                }

                //判断是否是FFFF帧
                if (ConvertHexStringEndian(strMessage_Receiving.Substring(14, 4)) == "FFFF")
                {
                    // 使用通道映射来正确分配数据到对应的UI通道
                    int currentDataIndex = AcquisitionInfoSet.ChannelNumberOfCurrentMessage;
                    if (currentDataIndex < AcquisitionInfoSet.lstChannelMapping.Count)
                    {
                        int uiChannelIndex = AcquisitionInfoSet.lstChannelMapping[currentDataIndex];

                        switch (uiChannelIndex)
                        {
                            case 1:
                                AcquisitionData.Channel1 = new List<int>();
                                AcquisitionInfoSet.lstReceiving[currentDataIndex].ForEach(item => AcquisitionData.Channel1.Add(item));
                                break;
                            case 2:
                                AcquisitionData.Channel2 = new List<int>();
                                AcquisitionInfoSet.lstReceiving[currentDataIndex].ForEach(item => AcquisitionData.Channel2.Add(item));
                                break;
                            case 3:
                                AcquisitionData.Channel3 = new List<int>();
                                AcquisitionInfoSet.lstReceiving[currentDataIndex].ForEach(item => AcquisitionData.Channel3.Add(item));
                                break;
                            case 4:
                                AcquisitionData.Channel4 = new List<int>();
                                AcquisitionInfoSet.lstReceiving[currentDataIndex].ForEach(item => AcquisitionData.Channel4.Add(item));
                                break;
                            default:
                                break;
                        }
                    }

                    if (AcquisitionInfoSet.ChannelNumberOfCurrentMessage + 1 == AcquisitionInfoSet.lstChannel.Count)
                    {
                        AcquisitionInfoSet.ChannelNumberOfCurrentMessage = 0;
                        AcquisitionInfoSet.CurrentMessageNumber = 0;
                        return 100;
                    }
                    else
                    {
                        AcquisitionInfoSet.ChannelNumberOfCurrentMessage++;
                        AcquisitionInfoSet.CurrentMessageNumber++;
                        return 101;
                    }
                }
                else
                {
                    AcquisitionInfoSet.CurrentMessageNumber++;
                    return 101;
                }             
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_ACQUISITION, "ReceivingMessage_ForParameterAcquisition", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForUploadingFault
        //函数功能：接收数据——用于数据上传
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //        -1：ERROR
        //         1：OK
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.03
        //*************************************************************************
        public static int ReceivingMessage_ForUploadingFault()
        {
            int iLength_Receiving = 0;//接收帧长度
            string strMessage_Receiving = null;//接收帧
            string strCRC_Receiving = null;//接收帧CRC
            string strCRC_Check = null;//接收检测帧CRC            
            List<string> lstGroupData = new List<string>();//组数据

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.NO_EFFECT;
                }

                //判断接收帧的长度不可以小于10个字节
                iLength_Receiving = strMessage_Receiving.Length;
                if (iLength_Receiving < 20)
                {
                    return RET.ERROR;
                }

                //判断起始位和结束位
                if (strMessage_Receiving.Substring(0, 2) != MessageFormat.HEAD || strMessage_Receiving.Substring(iLength_Receiving - 2, 2) != MessageFormat.END)
                {
                    return RET.ERROR;
                }

                //判断CRC
                strCRC_Receiving = strMessage_Receiving.Substring(iLength_Receiving - 6, 4);
                strCRC_Check = CRC16(strMessage_Receiving.Substring(2, iLength_Receiving - 8));
                if (strCRC_Check != strCRC_Receiving)
                {
                    return RET.ERROR;
                }

                //获取采样数据
                for (int i = 18; i < iLength_Receiving - 6; i += 8)
                {
                    string strtemp = ConvertHexStringEndian(strMessage_Receiving.Substring(i, 8));
                    int itemp = System.Int32.Parse(strtemp, System.Globalization.NumberStyles.HexNumber);
                    FaultAcquisitionInfoSet.lstReceiving[FaultAcquisitionInfoSet.ChannelNumberOfCurrentMessage].Add(itemp);
                }

                //判断是否是FFFF帧
                if (ConvertHexStringEndian(strMessage_Receiving.Substring(14, 4)) == "FFFF")
                {
                    switch (FaultAcquisitionInfoSet.ChannelNumberOfCurrentMessage)
                    {
                        case 0:
                            FaultAcquisitionData.Channel1 = new List<int>();
                            FaultAcquisitionInfoSet.lstReceiving[0].ForEach(item => FaultAcquisitionData.Channel1.Add(item));
                            break;
                        case 1:
                            FaultAcquisitionData.Channel2 = new List<int>();
                            FaultAcquisitionInfoSet.lstReceiving[1].ForEach(item => FaultAcquisitionData.Channel2.Add(item));
                            break;
                        case 2:
                            FaultAcquisitionData.Channel3 = new List<int>();
                            FaultAcquisitionInfoSet.lstReceiving[2].ForEach(item => FaultAcquisitionData.Channel3.Add(item));
                            break;
                        case 3:
                            FaultAcquisitionData.Channel4 = new List<int>();
                            FaultAcquisitionInfoSet.lstReceiving[3].ForEach(item => FaultAcquisitionData.Channel4.Add(item));
                            break;
                        case 4:
                            FaultAcquisitionData.Channel5 = new List<int>();
                            FaultAcquisitionInfoSet.lstReceiving[4].ForEach(item => FaultAcquisitionData.Channel5.Add(item));
                            break;
                        case 5:
                            FaultAcquisitionData.Channel6 = new List<int>();
                            FaultAcquisitionInfoSet.lstReceiving[5].ForEach(item => FaultAcquisitionData.Channel6.Add(item));
                            break;
                        case 6:
                            FaultAcquisitionData.Channel7 = new List<int>();
                            FaultAcquisitionInfoSet.lstReceiving[6].ForEach(item => FaultAcquisitionData.Channel7.Add(item));
                            break;
                        case 7:
                            FaultAcquisitionData.Channel8 = new List<int>();
                            FaultAcquisitionInfoSet.lstReceiving[7].ForEach(item => FaultAcquisitionData.Channel8.Add(item));
                            break;
                        default:
                            break;
                    }

                    if (FaultAcquisitionInfoSet.ChannelNumberOfCurrentMessage + 1 == FaultAcquisitionInfoSet.lstChannel.Count)//结束上传
                    {
                        FaultAcquisitionInfoSet.ChannelNumberOfCurrentMessage = 0;
                        FaultAcquisitionInfoSet.CurrentMessageNumber = 0;
                        return 100;
                    }
                    else//接续上传
                    {
                        FaultAcquisitionInfoSet.ChannelNumberOfCurrentMessage++;
                        FaultAcquisitionInfoSet.CurrentMessageNumber++;
                        return 101;
                    }
                }
                else
                {
                    FaultAcquisitionInfoSet.CurrentMessageNumber++;
                    return 101;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_ACQUISITION, "ReceivingMessage_ForParameterAcquisition", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForStopAcquisition
        //函数功能：接收数据——用于停止数据采集
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //        -1：ERROR
        //         1：OK
        //        
        //编码作者：Ryan
        //更新时间：2020.01.14
        //*************************************************************************
        public static int ReceivingMessage_ForStopAcquisition()
        {
            string strMessage_Receiving = null;//实际收到的数据帧
            string strMessage_Check = null;//应该接到的数据帧
           
            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.NO_EFFECT;
                }

                //strMessage_Check = "0A00" + CommunicationSet.StationID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.STOP_ACQUISITION + "4F4B";
                strMessage_Check = "0A00" + CommunicationSet.SlaveID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.STOP_ACQUISITION + "4F4B";
                strMessage_Check += CRC16(strMessage_Check);
                strMessage_Check = MessageFormat.HEAD + strMessage_Check + MessageFormat.END;

                if (strMessage_Check == strMessage_Receiving)
                {
                    return RET.SUCCEEDED;
                }
                else
                {
                    return RET.NO_EFFECT;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_STOP_ACQUISITION, "ReceivingMessage_ForStopAcquisition", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForStopFaultAcquisition
        //函数功能：接收数据——用于停止故障数据采集
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //        -1：ERROR
        //         1：OK
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.03
        //*************************************************************************
        public static int ReceivingMessage_ForStopFaultAcquisition()
        {
            string strMessage_Receiving = null;//实际收到的数据帧
            string strMessage_Check = null;//应该接到的数据帧

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.NO_EFFECT;
                }

                //strMessage_Check = "0A00" + CommunicationSet.StationID + FunctionCode.ACQUISITION + CommunicationSet.AxisID + AcquisitionExecutedCode.STOP_ACQUISITION + "4F4B";
                strMessage_Check = "0A00" + CommunicationSet.SlaveID + FaultFunctionCode.ACQUISITION + CommunicationSet.AxisID + FaultAcquisitionExecutedCode.STOP_ACQUISITION + "4F4B";
                strMessage_Check += CRC16(strMessage_Check);
                strMessage_Check = MessageFormat.HEAD + strMessage_Check + MessageFormat.END;

                if (strMessage_Check == strMessage_Receiving)
                {
                    return RET.SUCCEEDED;
                }
                else
                {
                    return RET.NO_EFFECT;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_STOP_ACQUISITION, "ReceivingMessage_ForStopAcquisition", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForHardwareAlarm
        //函数功能：接收硬件报警信息
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //        -1：ERROR
        //         1：OK
        //        
        //编码作者：Ryan
        //更新时间：2020.02.19
        //*************************************************************************
        public static int ReceivingMessage_ForHardwareAlarm()
        {
            int iLength_Receiving = 0;//接收帧长度
            UInt32 uDateTime = 0;//报警时间
            string strMessage_Receiving = null;//接收帧
            string strCRC_Receiving = null;//接收帧CRC
            string strCRC_Check = null;//接收检测帧CRC
            string strIndex = null;//报警索引号
            string strDateTime = null;//报警时间            

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.ERROR;
                }

                //接收帧的长度
                iLength_Receiving = strMessage_Receiving.Length;
                if (iLength_Receiving != 138)
                {
                    return RET.ERROR;
                }

                //判断起始位和结束位
                if (strMessage_Receiving.Substring(0, 2) != MessageFormat.HEAD || strMessage_Receiving.Substring(iLength_Receiving - 2, 2) != MessageFormat.END)
                {
                    return RET.ERROR;
                }

                //判断CRC
                strCRC_Receiving = strMessage_Receiving.Substring(iLength_Receiving - 6, 4);
                strCRC_Check = CRC16(strMessage_Receiving.Substring(2, iLength_Receiving - 8));
                if (strCRC_Check != strCRC_Receiving)
                {
                    return RET.ERROR;
                }

                //获取报警信息
                CommunicationSet.HardwareAlarmValue = new List<HardwareAlarm>();
                for (int i = 12; i < iLength_Receiving - 6; i+= 12)
                {
                    strIndex = LittleEndianHexStringToNormalString(strMessage_Receiving.Substring(i, 4));
                    strDateTime = LittleEndianHexStringToNormalString(strMessage_Receiving.Substring(i + 4, 8));

                    uDateTime = Convert.ToUInt32(strDateTime, 16);

                    if (strIndex != "0000")
                    {
                        CommunicationSet.HardwareAlarmValue.Add(new HardwareAlarm() { Index = strIndex, DateTime = uDateTime });
                    }                  
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_HARDWAREALARM, "ReceivingMessage_ForHardwareAlarm", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReceivingMessage_ForParameterMistake
        //函数功能：接收参数读写错误信息
        //
        //输入参数：NONE
        //         
        //输出参数：0：No_EFFECT
        //               -1：ERROR
        //                1：OK
        //        
        //编码作者：Ryan
        //更新时间：2020.09.23
        //*************************************************************************
        public static int ReceivingMessage_ForParameterMistake(ref string strMistakeInfo)
        {
            int iLength_Receiving = 0;//接收帧长度   
            string strMessage_Receiving = null;//接收帧
            string strCRC_Receiving = null;//接收帧CRC
            string strCRC_Check = null;//接收检测帧CRC 
            string strAddress = null;//错误地址
            string strMistakeCode = null;//错误代码

            try
            {
                //Byte转换为16进制字符串
                strMessage_Receiving = BytesToHexString(CommunicationSet.Receiving);
                if (string.IsNullOrEmpty(strMessage_Receiving))
                {
                    return RET.ERROR;
                }

                //接收帧的长度
                iLength_Receiving = strMessage_Receiving.Length;
                if (iLength_Receiving != 26)
                {
                    return RET.ERROR;
                }

                //判断起始位和结束位
                if (strMessage_Receiving.Substring(0, 2) != MessageFormat.HEAD || strMessage_Receiving.Substring(iLength_Receiving - 2, 2) != MessageFormat.END)
                {
                    return RET.ERROR;
                }

                //判断CRC
                strCRC_Receiving = strMessage_Receiving.Substring(iLength_Receiving - 6, 4);
                strCRC_Check = CRC16(strMessage_Receiving.Substring(2, iLength_Receiving - 8));
                if (strCRC_Check != strCRC_Receiving)
                {
                    return RET.ERROR;
                }

                //获取错误信息              
                strAddress = LittleEndianHexStringToNormalString(strMessage_Receiving.Substring(10, 8));
                if (!string.IsNullOrEmpty(strAddress))
                {
                    if (strAddress.Length == 8)
                    {
                        strAddress = strAddress.Remove(strAddress.Length - 2, 2);

                        if (OthersHelper.SelectAxisNumber() == AxisNumber.B)
                        {
                            strAddress = OthersHelper.AddressRestore(strAddress);
                        }

                        if (string.IsNullOrEmpty(strAddress))
                        {
                            return RET.ERROR;
                        }
                    }
                    else
                    {
                        return RET.ERROR;
                    }
                }
                else
                {
                    return RET.ERROR;
                }
            
                if (strAddress.Substring(0,2) == "F0")
                {
                    strMistakeInfo = "指令无法执行";
                    return RET.NO_EFFECT;
                }
                else
                {
                    strMistakeCode = LittleEndianHexStringToNormalString(strMessage_Receiving.Substring(18, 2));
                    switch (strMistakeCode)
                    {
                        case "08":
                            strMistakeInfo = "0x" + strAddress + "不存在";
                            break;
                        case "0E":
                            strMistakeInfo = "0x" + strAddress + "长度错误";
                            break;
                        case "12":
                            strMistakeInfo = "0x" + strAddress + "数值超限";
                            break;
                        case "1D":
                            strMistakeInfo = "因当前伺服状态，0x" + strAddress + "不能写入，请稍后重试";
                            break;
                        case "17":
                            strMistakeInfo = "0x" + strAddress + "未明确错误";
                            break;
                        case "19":
                            strMistakeInfo = "暂无写入控制权";
                            break;
                        default:
                            strMistakeInfo = "0x" + strAddress + "存在未知报警";
                            break;
                    }

                    return RET.SUCCEEDED;
                }                            
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_MISTAKE, "ReceivingMessage_ForParameterMistake", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：CheckIsOnlyMessageHead
        //函数功能：判断是否只有报头
        //
        //输入参数：byte[] bReceivingMessage     待检测的数据帧
        //         
        //输出参数：0：No_EFFECT
        //        -1：ERROR
        //         1：OK
        //        
        //编码作者：Ryan
        //更新时间：2020.01.02
        //*************************************************************************
        public static int CheckIsOnlyMessageHead(byte[] bReceivingMessage)
        {
            int iResult = 0;
            if (bReceivingMessage == null) return RET.ERROR;
       
            for (int i = 1; i < bReceivingMessage.Length; i++)
            {
                if (bReceivingMessage[i] == 0)
                {
                    iResult++;
                }
            }

            if (iResult == bReceivingMessage.Length - 1)
            {
                return RET.NO_EFFECT;
            }
            else
            {
                return RET.SUCCEEDED;
            }
        }

        //*************************************************************************
        //函数名称：CheckIsExistMessageEnd
        //函数功能：检索是否有帧尾
        //
        //输入参数：byte[] bReceivingMessage     检查的数据帧
        //         ref int iIndex               报尾的索引号
        //         
        //输出参数：0：No_EFFECT
        //         1：OK
        //        
        //编码作者：Ryan
        //更新时间：2002.01.02
        //*************************************************************************
        public static int CheckIsExistMessageEnd(byte[] bReceivingMessage, ref int iIndex)
        {
            for (int i = 0; i < bReceivingMessage.Length; i++)
            {
                if (bReceivingMessage[i] != 85)
                {
                    continue;
                }

                int iResult = 0;
                for (int j = i + 1; j <= i + 50; j++)
                {
                    if (bReceivingMessage[j] == 0)
                    {
                        iResult++;
                    }
                    else
                    {
                        break;
                    }
                }

                if (iResult == 50)
                {
                    iIndex = i;
                    return RET.SUCCEEDED;
                }
            }

            return RET.NO_EFFECT;
        }

        //*************************************************************************
        //函数名称：CombineMessage
        //函数功能：把丢失报头的报文补上
        //
        //输入参数：NONE
        //         
        //输出参数：byte[]   加上报头以后的报文
        //        
        //编码作者：Ryan
        //更新时间：2020.01.02
        //*************************************************************************
        public static byte[] CombineMessage(byte[] bReceivingMessage)
        {
            byte[] bTarger = new byte[bReceivingMessage.Length + 1];

            bTarger[0] = 170;
            for (int i = 0; i < bReceivingMessage.Length; i++)
            {
                bTarger[i + 1] = bReceivingMessage[i];
            }

            return bTarger; 
        }

        //*************************************************************************
        //函数名称：FindCompleteMessage
        //函数功能：查找完整数据帧
        //
        //输入参数：NONE
        //         
        //输出参数：1: OK
        //        -1: ERROR
        //         0: NO_EFFECT
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.01.02&2023.04.13
        //*************************************************************************
        public static int FindCompleteMessage(ref byte[] buffer)
        {
            int iLength = 0;//帧长度
            int iHeadIndex = 0;//AA索引地址
            int iEndIndex = 0;//55索引地址

            try
            {
                if (SoftwareStateParameterSet.lstMessage == null)
                {
                    SoftwareStateParameterSet.lstMessage = new List<byte>();
                }
                    
                if (SoftwareStateParameterSet.lstMessage.Count == 0)
                {
                    return RET.NO_EFFECT;
                }                  

                //判断报头
                iHeadIndex = SoftwareStateParameterSet.lstMessage.FindIndex(o => o == 0xAA);
                if (iHeadIndex == -1)//没找到AA
                { 
                                      
                    SoftwareStateParameterSet.lstMessage.Clear();

                    //if (SoftwareStateParameterSet.CurrentPageName == PageName.FAULTDATAOSCILLOSCOPE && SoftwareStateParameterSet.IsFaultUpload)
                    //{
                    //    SoftwareStateParameterSet.IsRetransmission = true;  //由Lilbert于2023.04.13添加重传机制
                    //    SoftwareStateParameterSet.IsFaultUpload = false;
                    //}

                    return RET.NO_EFFECT; 
                }
                else if (iHeadIndex > 1)//AA不为开头移掉之前的字节
                {
                    SoftwareStateParameterSet.lstMessage.RemoveRange(0, iHeadIndex);
                }

                //判断帧长度
                iLength = GetMessageLength();
                if (SoftwareStateParameterSet.lstMessage.Count < iLength)
                {
                    return RET.NO_EFFECT;
                }

                //判断报尾
                iEndIndex = SoftwareStateParameterSet.lstMessage.FindLastIndex(o => o == 0x55);
                if (iEndIndex == -1)
                {
                    SoftwareStateParameterSet.lstMessage.Clear();
                    return RET.NO_EFFECT;
                }
                else
                {
                    if (iEndIndex + 1 == iLength)
                    {
                        //buffer = new byte[SoftwareStateParameterSet.lstMessage.Count];
                        buffer = new byte[iLength];
                        SoftwareStateParameterSet.lstMessage.CopyTo(0, buffer, 0, buffer.Length);
                        SoftwareStateParameterSet.lstMessage.Clear();

                        return RET.SUCCEEDED;
                    }
                    else
                    {
                        SoftwareStateParameterSet.lstMessage.Clear();
                        return RET.NO_EFFECT;
                    }
                }            
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("FindMessage", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetMessageLength
        //函数功能：获取帧长度
        //
        //输入参数：NONE
        //         
        //输出参数：1: OK
        //        -1: ERROR
        //         0: NO_EFFECT
        //        
        //编码作者：Ryan
        //更新时间：2020.01.02
        //*************************************************************************
        public static int GetMessageLength()
        {
            byte[] bLength = new byte[2];
            string strLength = null;

            try
            {
                if (SoftwareStateParameterSet.lstMessage.Count < 3)
                {
                    return 999999;
                }
                else
                {
                    SoftwareStateParameterSet.lstMessage.CopyTo(1, bLength, 0, bLength.Length);
                    strLength = ConvertHexStringEndian(BytesToHexString(bLength));
                }
               
                if (string.IsNullOrEmpty(strLength))
                    return 999999;
                else
                    return (Convert.ToUInt16(strLength, 16) + 2);
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("GetMessageLength", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ChangeSerialPortTaskToExecuted
        //函数功能：更新串口任务为完成
        //
        //输入参数：NONE
        //         
        //输出参数：1：SUCCEED    
        //        -1: ERROR
        //        
        //编码作者：Ryan
        //更新时间：2019.12.20
        //*************************************************************************
        public static int ChangeSerialPortTaskToExecuted()
        {
            try
            {
                if (SerialPortTask.TaskManagement == null)
                {
                    return RET.ERROR;
                }

                if (SerialPortTask.TaskManagement.Count == 0)
                {
                    return RET.SUCCEEDED;
                }

                for (int i = SerialPortTask.TaskManagement.Count - 1; i >= 0; i--)
                {
                    //若不是正在执行的任务，直接跳过不考虑
                    if (SerialPortTask.TaskManagement[i].ExecutionState != TaskState.EXECUTING)
                    {
                        continue;
                    }

                    //发送信息的长度不相等，直接跳过不考虑
                    if (CommunicationSet.TransmittingDataInfo.Count != SerialPortTask.TaskManagement[i].TransmittingDataInfo.Count)
                    {
                        continue;
                    }

                    //轴地址不相同，直接跳过不考虑
                    //if (SerialPortTask.TaskManagement[i].StationID != CommunicationSet.StationID || SerialPortTask.TaskManagement[i].AxisID != CommunicationSet.AxisID)
                    if (SerialPortTask.TaskManagement[i].SlaveID != CommunicationSet.SlaveID || SerialPortTask.TaskManagement[i].AxisID != CommunicationSet.AxisID)
                    {
                        continue;
                    }

                    //任务名称不相同，直接跳过不考虑
                    if (SerialPortTask.TaskManagement[i].TaskName != CommunicationSet.TaskName)
                    {
                        continue;
                    }

                    //功能码不相同，直接跳过不考虑
                    if (SerialPortTask.TaskManagement[i].FunctionCode != CommunicationSet.FunctionCode || SerialPortTask.TaskManagement[i].AcquisitionExecutedCode != CommunicationSet.AcquisitionExecutedCode)
                    {
                        continue;
                    }

                    //更新任务状态
                    SerialPortTask.TaskManagement[i].ExecutionState = TaskState.EXECUTED;

                    //更新状态栏
                    if (CommunicationSet.FunctionCode != FunctionCode.ACQUISITION)
                    {
                        //ViewModelSet.Main?.RefreshSerialPortWorkState(TaskName.Empty);
                        if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                        {
                            ViewModelSet.Main?.RefreshSerialPortWorkState(TaskName.Empty);                            
                        }
                        else
                        {
                            ViewModelSet.Main?.RefreshSerialPortWorkState(TaskName.Empty1);
                        }
                    }
                    if (CommunicationSet.FunctionCode != FaultFunctionCode.ACQUISITION)
                    {
                        //ViewModelSet.Main?.RefreshSerialPortWorkState(TaskName.Empty);
                        if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                        {
                            ViewModelSet.Main?.RefreshSerialPortWorkState(TaskName.Empty);
                        }
                        else
                        {
                            ViewModelSet.Main?.RefreshSerialPortWorkState(TaskName.Empty1);
                        }
                    }
                }

                //开启写控制字任务
                if (CommunicationSet.TaskName == TaskName.AbsolutePositionAction || CommunicationSet.TaskName == TaskName.RelativePositionAction || CommunicationSet.TaskName == TaskName.StopPositionAction ||
                    CommunicationSet.TaskName == TaskName.SpeedAction|| CommunicationSet.TaskName == TaskName.TorqueAction || CommunicationSet.TaskName == TaskName.SeekZero ||
                    CommunicationSet.TaskName == TaskName.FunctionGenerator || CommunicationSet.TaskName == TaskName.OfflineInertiaIdentification)
                {
                    if (ControlWordSet.ListValue != null)
                    {
                        if (ControlWordSet.ListValue.Count != 0)
                        {
                            ControlWordSet.WriteSwitch = true;
                        }
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("ChangeSerialPortTaskToExecuted", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：StopContinuousAcquisition
        //函数功能：连续采集暂停
        //
        //输入参数：NONE
        //         
        //输出参数：1：暂停    
        //         0: 不暂停
        //        
        //编码作者：Ryan
        //更新时间：2019.12.20
        //*************************************************************************
        public static int StopContinuousAcquisition()
        {
            if (!AcquisitionInfoSet.AcquisitionSwitch && AcquisitionInfoSet.IsContinuous)
            {
                return RET.SUCCEEDED;
            }
            else
            {
                return RET.NO_EFFECT;
            }
        }

        //*************************************************************************
        //函数名称：StopFaultContinuousAcquisition
        //函数功能：连续采集暂停
        //
        //输入参数：NONE
        //         
        //输出参数：1：暂停    
        //         0: 不暂停
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.03
        //*************************************************************************
        public static int StopFaultContinuousAcquisition()
        {
            if (!FaultAcquisitionInfoSet.AcquisitionSwitch && FaultAcquisitionInfoSet.IsContinuous)
            {
                return RET.SUCCEEDED;
            }
            else
            {
                return RET.NO_EFFECT;
            }
        }

        //*************************************************************************
        //函数名称：CheckSerialPortStatus
        //函数功能：判断窗口状态
        //
        //输入参数：NONE
        //         
        //输出参数：1：连接    
        //         0: 没有连接
        //        -1: 异常
        //        
        //编码作者：Ryan
        //更新时间：2019.12.20
        //*************************************************************************
        public static int CheckSerialPortStatus()
        {
            if (CommunicationSet.SerialPortInfo == null)
            {
                CommunicationSet.SerialPortInfo = new SerialPort();
                return RET.ERROR;
            }
            else if (!CommunicationSet.SerialPortInfo.IsOpen)
            {
                return RET.NO_EFFECT;
            }
            else
            {
                return RET.SUCCEEDED;
            }
        }

        //*************************************************************************
        //函数名称：GetStatusWord_HighEightBit
        //函数功能：获取状态字-高8位
        //
        //输入参数：NONE     状态字数据-未截取
        //         
        //输出参数：string   返回状态字-已截取并转换为16进制
        //        
        //编码作者：Ryan
        //更新时间：2019.12.20
        //*************************************************************************
        public static string GetStatusWord_HighEightBit(int iIndex, UInt16 uValueTemp)
        {
            string strValueTemp = null;

            try
            {
                strValueTemp = Convert.ToString(uValueTemp, 2).PadLeft(16, '0');

                if (iIndex == 0)
                {
                    strValueTemp = strValueTemp.Substring(1,1);

                    return Convert.ToUInt32(strValueTemp, 2).ToString();
                }
                else if (iIndex == 1)
                {
                    strValueTemp = strValueTemp.Substring(2, 3);
                    return strValueTemp;
                }
                else
                {
                    return null;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_GET_STATUS_WORD_HIGHEIGHTBIT, "GetStatusWord_HighEightBit", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetStatusWord
        //函数功能：获取状态字-低7位
        //
        //输入参数：NONE     状态字数据-未截取
        //         
        //输出参数：string   返回状态字-已截取并转换为16进制
        //        
        //编码作者：Ryan
        //更新时间：2019.12.20
        //*************************************************************************
        public static string GetStatusWord(int iIndex, UInt16 uValueTemp)
        {
            string strValueTemp = null;

            try
            {
                strValueTemp = Convert.ToString(uValueTemp, 2).PadLeft(16, '0');

                if (iIndex == 0)
                {
                    strValueTemp = strValueTemp.Substring(strValueTemp.Length - 7);

                    return Convert.ToUInt32(strValueTemp, 2).ToString();
                }
                else if (iIndex == 1)
                {                    
                    strValueTemp = strValueTemp.Substring(2, 3);
                    return strValueTemp;
                }
                else
                {
                    return null;
                }                   
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.HEXHELPER_GET_STATUS_WORD, "GetStatusWord", ex);
                return null;
            }          
        }
    }
}
