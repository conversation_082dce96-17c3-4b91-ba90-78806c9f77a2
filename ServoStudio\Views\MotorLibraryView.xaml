﻿<UserControl x:Class="ServoStudio.Views.MotorLibraryView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:dxrt="http://schemas.devexpress.com/winfx/2008/xaml/ribbon/themekeys"             
             xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
             xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"

             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
             xmlns:dxn="http://schemas.devexpress.com/winfx/2008/xaml/navbar"
             xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"  
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:Views="clr-namespace:ServoStudio.Views"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:MotorLibraryViewModel}"
             d:DesignHeight="850" d:DesignWidth="1400">

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding MotorLibraryLoadedCommand}"/>

        <dxwui:WinUIDialogService x:Name="MotorLibraryDetails" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:MotorLibraryDetailsView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>
    </dxmvvm:Interaction.Behaviors>

    <Grid Margin="5,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <dxb:BarContainerControl Grid.Row="0">
            <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                <dxb:BarButtonItem Content="  添加参数表  " Command="{Binding MotorLibraryDetailsCommand}" CommandParameter="Insert" Glyph="{dx:DXImageOffice2013 Image=AddNewDataSource_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
            </dxb:ToolBarControl>

            <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                <dxb:BarButtonItem Content="  修改参数表  " Command="{Binding MotorLibraryDetailsCommand}" CommandParameter="Update" Glyph="{dx:DXImageOffice2013 Image=EditDataSource_16x16.png}" BarItemDisplayMode="ContentAndGlyph" IsEnabled="{Binding ButtonEnabled}"/>
            </dxb:ToolBarControl>

            <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                <dxb:BarButtonItem Content="  删除参数表  " Command="{Binding MotorLibraryDetailsCommand}" CommandParameter="Delete" Glyph="{dx:DXImageOffice2013 Image=DeleteDataSource_16x16.png}" BarItemDisplayMode="ContentAndGlyph" IsEnabled="{Binding ButtonEnabled}"/>
            </dxb:ToolBarControl>

            <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                <dxb:BarButtonItem Content="  查看参数表  " Command="{Binding MotorLibraryDetailsCommand}" CommandParameter="Select"  Glyph="{dx:DXImageOffice2013 Image=Database_16x16.png}" BarItemDisplayMode="ContentAndGlyph" IsEnabled="{Binding ButtonEnabled}" />
            </dxb:ToolBarControl>

            <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                <dxb:BarButtonItem Content="  下载参数表  " Command="{Binding MotorLibraryDetailsCommand}" CommandParameter="Download" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" BarItemDisplayMode="ContentAndGlyph" IsEnabled="{Binding ButtonEnabled}"/>
            </dxb:ToolBarControl>

            <dxb:ToolBarControl Width="Auto" AllowHide="False" AllowQuickCustomization="False" AllowCustomizationMenu="False" AllowRename="False" ShowDragWidget="False">
                <dxb:BarButtonItem Content="  返回电机反馈页  " Command="{Binding MotorFeedbackNavigationCommand}" Glyph="{dx:DXImageOffice2013 Image=DoublePrev_16x16.png}" BarItemDisplayMode="ContentAndGlyph"/>
            </dxb:ToolBarControl>
        </dxb:BarContainerControl>

        <dxg:GridControl  Grid.Row="1" SelectionMode="Row" ItemsSource="{Binding MotorLibrary}" AutoGenerateColumns="AddNew" >
            <dxg:GridControl.View>
                <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" SearchPanelHorizontalAlignment="Stretch">
                </dxg:TableView>
            </dxg:GridControl.View>

            <dxg:GridColumn FieldName="IsChoiced" Header="选中" IsSmart="True" ReadOnly="False">
                <dxg:GridColumn.CellTemplate>
                    <DataTemplate>
                        <dxe:CheckEdit VerticalAlignment="Center" HorizontalAlignment="Center" IsChecked="{Binding RowData.Row.IsChoiced, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                            <dxmvvm:Interaction.Behaviors>
                                <dxmvvm:EventToCommand EventName="Checked" Command="{Binding Path=DataContext.CheckIsMultipleChoiceCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}"/>
                                <dxmvvm:EventToCommand EventName="Unchecked" Command="{Binding Path=DataContext.CheckIsMultipleChoiceCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}"/>
                            </dxmvvm:Interaction.Behaviors>
                        </dxe:CheckEdit>
                    </DataTemplate>
                </dxg:GridColumn.CellTemplate>
            </dxg:GridColumn>

            <dxg:GridColumn FieldName="Name" Header="参数表名称" IsSmart="True" Width="2*"/>
            <dxg:GridColumn FieldName="MotorType" Header="电机类型" IsSmart="True" Width="*"/>
            <dxg:GridColumn FieldName="EncoderType" Header="编码器类型" IsSmart="True" Width="*"/>
            <dxg:GridColumn FieldName="Author" Header="作者" IsSmart="True" Width="*"/>
            <dxg:GridColumn FieldName="DateTime" Header="更新时间" IsSmart="True" Width="*"/>
            <dxg:GridColumn FieldName="Comment" Header="备注" IsSmart="True" Width="1.8*"/>
            <dxg:GridColumn FieldName="Valid" Visible="False"/>
        </dxg:GridControl>

        <ScrollViewer Grid.Row="2" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

            <Grid Grid.Row="2" ScrollViewer.HorizontalScrollBarVisibility="Auto" ScrollViewer.VerticalScrollBarVisibility="Auto" ScrollViewer.CanContentScroll="True" >
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>

                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="2" Content="2.电机参数" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="4" Content="3.电机反馈" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="6" Content="4.参数学习" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="8" Content="5.参数辨识" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="9" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="10" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="10" Content="6.JOG调试" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="11" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="12" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="12" Content="7.辨识整定" Foreground="Gray" Style="{StaticResource LabelStyle}"/>

                <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="2" Content="2.电机反馈" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="4" Content="3.单位设置" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="6" Content="4.限幅保护" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="8" Content="5.一般设定" Foreground="Gray" Style="{StaticResource LabelStyle}"/>-->

                <!--<dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="12" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=SortAsc_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding MotorLibraryNavigationCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="13" FontSize="12" Content="参数库" Style="{StaticResource LabelStyle}" />

            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="14" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetMotorConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="15" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" />

            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="16" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SaveMotorConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="17" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}"/>-->

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="18" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Backward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding CommunicationSetNavigationCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="19" FontSize="12" Content="上页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="20" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Forward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding MotorFeedbackNavigationCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="21" FontSize="12" Content="下页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>
            </Grid>
            
        </ScrollViewer>
        
        
    </Grid>

</UserControl>
