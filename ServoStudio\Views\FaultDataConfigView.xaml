﻿<UserControl x:Class="ServoStudio.Views.FaultDataConfigView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking" 
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:FaultDataConfigViewModel}"
             d:DesignHeight="800" d:DesignWidth="1400">
    
    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding FaultDataConfigLoadedCommand}"/>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields" UseWin8NotificationsIfAvailable="True"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
        <Grid>
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="170"/>
                        <ColumnDefinition Width="90"/>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="180"/>
                        <ColumnDefinition Width="80"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="通道设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                    <Grid Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="9">

                        <Grid.Resources>
                            <DataTemplate x:Key="headerTemplate1">
                                <TextBlock Text="{Binding}" Foreground="Red" TextDecorations="Underline"/>
                            </DataTemplate>
                            <DataTemplate x:Key="headerTemplate2">
                                <TextBlock Text="{Binding}" Foreground="Orange" TextDecorations="Underline"/>
                            </DataTemplate>
                            <DataTemplate x:Key="headerTemplate3">
                                <TextBlock Text="{Binding}" Foreground="Blue" TextDecorations="Underline"/>
                            </DataTemplate>
                            <DataTemplate x:Key="headerTemplate4">
                                <TextBlock Text="{Binding}" Foreground="Green" TextDecorations="Underline"/>
                            </DataTemplate>
                            <DataTemplate x:Key="headerTemplate5">
                                <TextBlock Text="{Binding}" Foreground="Black" TextDecorations="Underline"/>
                            </DataTemplate>
                            <DataTemplate x:Key="headerTemplate6">
                                <TextBlock Text="{Binding}" Foreground="Purple" TextDecorations="Underline"/>
                            </DataTemplate>
                            <DataTemplate x:Key="headerTemplate7">
                                <TextBlock Text="{Binding}" Foreground="Brown" TextDecorations="Underline"/>
                            </DataTemplate>
                            <DataTemplate x:Key="headerTemplate8">
                                <TextBlock Text="{Binding}" Foreground="Coral" TextDecorations="Underline"/>
                            </DataTemplate>
                        </Grid.Resources>

                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="68"/>
                            <ColumnDefinition Width="170"/>
                            <ColumnDefinition Width="90"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="68"/>
                            <ColumnDefinition Width="180"/>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Label Content="通道-1" Grid.Row="1" Grid.Column="1" Margin="10,9" Style="{StaticResource LabelStyle}" Foreground="Red"/>
                        <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo1}"  SelectedIndex="{Binding SelectedSampleChannel1IndexConfig,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"  IsEnabled="{Binding Channel1Enabled}" AllowCollectionView="True"  DisplayMember="ItemName" >
                            <dxe:ComboBoxEdit.GroupStyle>
                                <GroupStyle HidesIfEmpty="True">
                                    <GroupStyle.ContainerStyle>
                                        <Style TargetType="{x:Type GroupItem}">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate>
                                                        <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate1}" IsExpanded="False" Margin="5,2">
                                                            <ItemsPresenter Margin="25,0,0,0"/>
                                                        </Expander>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </GroupStyle.ContainerStyle>
                                </GroupStyle>
                            </dxe:ComboBoxEdit.GroupStyle>
                        </dxe:ComboBoxEdit>

                        <Label Content="通道-2" Grid.Row="1" Grid.Column="5" Margin="10,9" Style="{StaticResource LabelStyle}" Foreground="Orange"/>
                        <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo2}" SelectedIndex="{Binding SelectedSampleChannel2IndexConfig,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding Channel2Enabled}" AllowCollectionView="True"  DisplayMember="ItemName" >
                            <dxe:ComboBoxEdit.GroupStyle>
                                <GroupStyle HidesIfEmpty="True">
                                    <GroupStyle.ContainerStyle>
                                        <Style TargetType="{x:Type GroupItem}">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate>
                                                        <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate2}" IsExpanded="False" Margin="5,2">
                                                            <ItemsPresenter Margin="25,0,0,0"/>
                                                        </Expander>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </GroupStyle.ContainerStyle>
                                </GroupStyle>
                            </dxe:ComboBoxEdit.GroupStyle>
                        </dxe:ComboBoxEdit>

                        <Label Content="通道-3" Grid.Row="2" Grid.Column="1" Margin="10,9" Style="{StaticResource LabelStyle}" Foreground="Blue"/>
                        <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo3}" SelectedIndex="{Binding SelectedSampleChannel3IndexConfig,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding Channel3Enabled}" AllowCollectionView="True"  DisplayMember="ItemName" >
                            <dxe:ComboBoxEdit.GroupStyle>
                                <GroupStyle HidesIfEmpty="True">
                                    <GroupStyle.ContainerStyle>
                                        <Style TargetType="{x:Type GroupItem}">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate>
                                                        <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate3}" IsExpanded="False" Margin="5,2">
                                                            <ItemsPresenter Margin="25,0,0,0"/>
                                                        </Expander>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </GroupStyle.ContainerStyle>
                                </GroupStyle>
                            </dxe:ComboBoxEdit.GroupStyle>
                        </dxe:ComboBoxEdit>

                        <Label Content="通道-4" Grid.Row="2" Grid.Column="5" Margin="10,9" Style="{StaticResource LabelStyle}" Foreground="Green"/>
                        <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo4}" SelectedIndex="{Binding SelectedSampleChannel4IndexConfig,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding Channel4Enabled}" AllowCollectionView="True"  DisplayMember="ItemName" >
                            <dxe:ComboBoxEdit.GroupStyle>
                                <GroupStyle HidesIfEmpty="True">
                                    <GroupStyle.ContainerStyle>
                                        <Style TargetType="{x:Type GroupItem}">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate>
                                                        <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate4}" IsExpanded="False" Margin="5,2">
                                                            <ItemsPresenter Margin="25,0,0,0"/>
                                                        </Expander>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </GroupStyle.ContainerStyle>
                                </GroupStyle>
                            </dxe:ComboBoxEdit.GroupStyle>
                        </dxe:ComboBoxEdit>

                        <Label Content="通道-5" Grid.Row="3" Grid.Column="1" Margin="10,9" Style="{StaticResource LabelStyle}" Foreground="Black"/>
                        <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo5}" SelectedIndex="{Binding SelectedSampleChannel5IndexConfig,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding Channel5Enabled}" AllowCollectionView="True"  DisplayMember="ItemName" >
                            <dxe:ComboBoxEdit.GroupStyle>
                                <GroupStyle HidesIfEmpty="True">
                                    <GroupStyle.ContainerStyle>
                                        <Style TargetType="{x:Type GroupItem}">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate>
                                                        <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate5}" IsExpanded="False" Margin="5,2">
                                                            <ItemsPresenter Margin="25,0,0,0"/>
                                                        </Expander>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </GroupStyle.ContainerStyle>
                                </GroupStyle>
                            </dxe:ComboBoxEdit.GroupStyle>
                        </dxe:ComboBoxEdit>

                        <Label Content="通道-6" Grid.Row="3" Grid.Column="5" Margin="10,9" Style="{StaticResource LabelStyle}" Foreground="Purple"/>
                        <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo6}" SelectedIndex="{Binding SelectedSampleChannel6IndexConfig,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding Channel6Enabled}" AllowCollectionView="True"  DisplayMember="ItemName" >
                            <dxe:ComboBoxEdit.GroupStyle>
                                <GroupStyle HidesIfEmpty="True">
                                    <GroupStyle.ContainerStyle>
                                        <Style TargetType="{x:Type GroupItem}">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate>
                                                        <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate6}" IsExpanded="False" Margin="5,2">
                                                            <ItemsPresenter Margin="25,0,0,0"/>
                                                        </Expander>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </GroupStyle.ContainerStyle>
                                </GroupStyle>
                            </dxe:ComboBoxEdit.GroupStyle>
                        </dxe:ComboBoxEdit>

                        <Label Content="通道-7" Grid.Row="4" Grid.Column="1" Margin="10,9" Style="{StaticResource LabelStyle}" Foreground="Brown"/>
                        <dxe:ComboBoxEdit Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo7}" SelectedIndex="{Binding SelectedSampleChannel7IndexConfig,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding Channel7Enabled}" AllowCollectionView="True"  DisplayMember="ItemName" >
                            <dxe:ComboBoxEdit.GroupStyle>
                                <GroupStyle HidesIfEmpty="True">
                                    <GroupStyle.ContainerStyle>
                                        <Style TargetType="{x:Type GroupItem}">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate>
                                                        <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate7}" IsExpanded="False" Margin="5,2">
                                                            <ItemsPresenter Margin="25,0,0,0"/>
                                                        </Expander>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </GroupStyle.ContainerStyle>
                                </GroupStyle>
                            </dxe:ComboBoxEdit.GroupStyle>
                        </dxe:ComboBoxEdit>

                        <Label Content="通道-8" Grid.Row="4" Grid.Column="5" Margin="10,9" Style="{StaticResource LabelStyle}" Foreground="Coral"/>
                        <dxe:ComboBoxEdit Grid.Row="4" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding GroupedSampleChannelInfo8}" SelectedIndex="{Binding SelectedSampleChannel8IndexConfig,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding Channel8Enabled}" AllowCollectionView="True"  DisplayMember="ItemName" >
                            <dxe:ComboBoxEdit.GroupStyle>
                                <GroupStyle HidesIfEmpty="True">
                                    <GroupStyle.ContainerStyle>
                                        <Style TargetType="{x:Type GroupItem}">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate>
                                                        <Expander Header="{Binding Path=Name}" HeaderTemplate="{StaticResource headerTemplate8}" IsExpanded="False" Margin="5,2">
                                                            <ItemsPresenter Margin="25,0,0,0"/>
                                                        </Expander>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </GroupStyle.ContainerStyle>
                                </GroupStyle>
                            </dxe:ComboBoxEdit.GroupStyle>
                        </dxe:ComboBoxEdit>

                    </Grid>

                    <Label Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="采样设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                    <Grid Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="9">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="170"/>
                            <ColumnDefinition Width="90"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="180"/>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Label Content="采样周期" Grid.Row="0" Grid.Column="1" Margin="10,9" Style="{StaticResource LabelStyle}"/>
                        <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding SamplingPeriodConfig}" SelectedItem="{Binding SelectedSamplingPeriodConfig,Mode=TwoWay}"/>

                        <!--<Label Content="采样时长" Grid.Row="0" Grid.Column="5" Margin="10,9" Style="{StaticResource LabelStyle}"/>
                    <dxe:ComboBoxEdit Grid.Row="0" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource ComboBoxStyle}" ItemsSource="{Binding SamplingDurationConfig}" SelectedItem="{Binding SelectedSamplingDurationConfig,Mode=TwoWay}" SelectedIndex="{Binding SelectedSamplingDurationIndexConfig}"/>-->

                    </Grid>

                    <Label Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                    <StackPanel Grid.Row="8" Grid.Column="0" Grid.ColumnSpan="9" HorizontalAlignment="Right" Orientation="Horizontal">
                        <!--<dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadLimitAmplitudeParameterCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                        <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                    </dx:SimpleButton>-->

                        <!--<dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultLimitAmplitudeParameterCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                        <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                    </dx:SimpleButton>-->

                        <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" Command="{Binding SaveFaultDataConfigFileCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                            <Label Content="保存参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                        </dx:SimpleButton>
                    </StackPanel>
                </Grid>

                <!--<TabControl Name="tabControl" SelectedIndex="{Binding SelectedTabIndex}" Grid.Row="0" Padding="0" BorderBrush="LightGray" Background="{x:Null}" BorderThickness="0,1,0,0">
                -->
                <!--<TabItem Header="速度限定" TabIndex="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="170"/>
                            <ColumnDefinition Width="90"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="180"/>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="使能时速度限制" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="使能时速度&#x0a;限制值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}"  Text="{Binding ServoOnSpeedLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="1" Grid.Column="3" Text="rpm" Style="{StaticResource TextBoxStyle_Unit}" />

                        <Label Grid.Row="1" Grid.Column="5" Margin="10,9" Content="转矩控制时&#x0a;速度限制值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding TrqctrlSpeedLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="1" Grid.Column="7" Text="rpm" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="最大速度限制" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Label Grid.Row="3" Grid.Column="1" Margin="10,9" Content="最大轮廓速度" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding MaxProfileVelocity,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="3" Grid.Column="3" Text="{Binding SpeedUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="3" Grid.Column="5" Margin="10,9" Content="最大加速度" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="3" Grid.Column="6" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding MaxAcceleration,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="3" Grid.Column="7" Text="{Binding AccelerateUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="4" Grid.Column="1" Margin="10,9" Content="最大减速度" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding MaxDeceleration,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="4" Grid.Column="3" Text="{Binding AccelerateUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="速度到达限制" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Label Grid.Row="6" Grid.Column="1" Margin="10,9" Content="速度到达阈值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="6" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding VelocityWindow,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="6" Grid.Column="3" Text="{Binding SpeedUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="6" Grid.Column="5" Margin="10,9" Content="速度到达窗口&#x0a;时间" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="6" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding VelocityWindowTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="6" Grid.Column="7" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <StackPanel Grid.Row="8" Grid.Column="0" Grid.ColumnSpan="9" HorizontalAlignment="Right" Orientation="Horizontal">
                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadLimitAmplitudeParameterCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                                <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultLimitAmplitudeParameterCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                                <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" Command="{Binding WriteLimitAmplitudeParameterCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                                <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>
                        </StackPanel>
                    </Grid>
                </TabItem>-->

                <!--<TabItem Header="位置限定" TabIndex="1">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding PositionAlreadySetCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="165"/>
                            <ColumnDefinition Width="95"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="180"/>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="偏差过大提示" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="位置偏差过大警告值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding PosErrWarnLevel,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="1" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="1" Grid.Column="5" Margin="10,9" Content="位置偏差过大报警值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding PosErrAlarmLevel,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="1" Grid.Column="7" Text="{Binding PositionUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="使能时位置偏差过大&#x0a;警告值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding SvonPosErrWarnLevel,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="2" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="2" Grid.Column="5" Margin="10,9" Content="使能时位置偏差过大&#x0a;报警值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding SvonPosErrAlarmLevel,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="2" Grid.Column="7" Text="{Binding PositionUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="位置跟踪" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Label Grid.Row="4" Grid.Column="1" Margin="10,9" Content="位置跟踪误差阈值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding FollowingErrorWindow,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="4" Grid.Column="3" Text="{Binding PositionUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="4" Grid.Column="5" Margin="10,9" Content="位置跟踪误差过大&#x0a;判定时间" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="4" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding FollowingErrorTimeout,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="4" Grid.Column="7" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="位置到达" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Label Grid.Row="6" Grid.Column="1" Margin="10,9" Content="位置到达阈值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="6" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding PositionWindow,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="6" Grid.Column="3" Text="{Binding PositionUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="6" Grid.Column="5" Margin="10,9" Content="位置到达窗口时间" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="6" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding PositionWindowTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="6" Grid.Column="7" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="软限位" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Label Grid.Row="8" Grid.Column="1" Margin="10,9" Content="软件限位最小值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="8" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding MinSoftwarePositionLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="8" Grid.Column="3" Text="{Binding PositionUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="8" Grid.Column="5" Margin="10,9" Content="软件限位最大值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="8" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding MaxSoftwarePositionLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="8" Grid.Column="7" Text="{Binding PositionUnit}" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="9" Grid.Column="0" Grid.ColumnSpan="9" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <StackPanel Grid.Row="10" Grid.Column="0" Grid.ColumnSpan="9" HorizontalAlignment="Right" Orientation="Horizontal">
                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadLimitAmplitudeParameterCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                                <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultLimitAmplitudeParameterCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                                <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Download_16x16.png}" Command="{Binding WriteLimitAmplitudeParameterCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                                <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>
                        </StackPanel>
                    </Grid>
                </TabItem>-->

                <!--<TabItem Header="转矩限定" TabIndex="2">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseUp" Command="{Binding TorqueAlreadySetCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="5" Margin="3,10" Style="{StaticResource LabelStyle}" Content="转矩限制" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="正转内部转矩限制值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}"  Text="{Binding ForwardInternalTorqueLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="1" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="反转内部转矩限制值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ReverseInternalTorqueLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="2" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="3" Grid.Column="1" Margin="10,9" Content="正转外部转矩限制值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ForwardExternalTorqueLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="3" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="4" Grid.Column="1" Margin="10,9" Content="反转外部转矩限制值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding ReverseExternalTorqueLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="4" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="5" Grid.Column="1" Margin="10,9" Content="紧急停止转矩限制值" Style="{StaticResource LabelStyle}" />
                        <TextBox Grid.Row="5" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding EmergencyStopTorqueLimit,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <TextBox Grid.Row="5" Grid.Column="3" Text="0.01" Style="{StaticResource TextBoxStyle_Unit}"/>

                        <Label Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="5" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

                        <StackPanel Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="5" HorizontalAlignment="Right" Orientation="Horizontal">
                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadLimitAmplitudeParameterCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                                <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultLimitAmplitudeParameterCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                                <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>

                            <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Download_16x16.png}" Command="{Binding WriteLimitAmplitudeParameterCommand}" CommandParameter="{Binding SelectedIndex,ElementName=tabControl}">
                                <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                            </dx:SimpleButton>
                        </StackPanel>
                    </Grid>
                </TabItem>-->
                <!--
            </TabControl>-->

                <!--<Grid Grid.Row="4">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>

                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                -->
                <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="0" Content="1.单位设置" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="2" Content="2.限幅保护" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="4" Content="3.一般设定" Style="{StaticResource LabelStyle}" Foreground="Gray"/>
                <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="6" Content="4.数字IO" Foreground="Gray" Style="{StaticResource LabelStyle}"/>-->

                <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="2" Content="2.电机反馈" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="4" Content="3.单位设置" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="6" Content="4.限幅保护" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="8" Content="5.一般设定" Foreground="Gray" Style="{StaticResource LabelStyle}"/>-->
                <!--

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="12" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetLimitAmplitudeConfigFileCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="13" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2" />

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="14" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SaveLimitAmplitudeConfigFileCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="16" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="17" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Backward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding UnitNavigationCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="18" FontSize="12" Content="上页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="19" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Forward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding NormalSettingNavigationCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="20" FontSize="12" Content="下页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>
            </Grid>-->
            </Grid>
        </Grid>
    </ScrollViewer>


   
</UserControl>
