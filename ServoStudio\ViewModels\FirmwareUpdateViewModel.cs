﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using System.Text;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class FirmwareUpdateViewModel
    {
        #region 私有字段
        private bool IsFirstUpdate = true;
        #endregion

        #region 服务      
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 属性
        public virtual string FileAddress { get; set; }//文件路径
        //public virtual bool ARM1 { get; set; }//选择1芯片升级
        //public virtual bool ARM2 { get; set; }//选择2芯片升级
        //public virtual bool ARM3 { get; set; }//选择3芯片升级
        public virtual string ARMCheckedHint { get; set; }//选中芯片提示
        public virtual bool CheckBoxEnabled { get; set; }//CheckBox的使能

        public virtual ObservableCollection<string> SlaveID { get; set; }//从站ID
        public virtual string SelectedSlaveID { get; set; }//选中的从站ID
        #endregion

        #region 构造函数
        public FirmwareUpdateViewModel()
        {
            ViewModelSet.FirmwareUpdate = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.FIRMWAREUPDATE;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：FirmwareUpdateLoaded
        //函数功能：载入
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.23
        //*************************************************************************
        public void FirmwareUpdateLoaded()
        {
            //芯片选中初始化      
            //ARM1 = false;
            //ARM2 = false;
            //ARM3 = false;

            SlaveInitialize();

            CheckBoxEnabled = true;
            ARMStatus.IsFirmUpdate = false;
            ARMCheckedHint = "提示：当前无从站升级";

            //固件升级信息集合初始化
            OthersHelper.FirmwareUpdateSetInitialize();
        }

        public void SlaveInitialize()
        {
            SlaveID = new ObservableCollection<string>();

            //多合一伺服
            #region 2合一伺服
            if (ConfigServo.SelectConfigSlaveID == 2 && ConfigServo.SelectConfigAxisID == 2)
            {
                for (int i = 0; i < ConfigServo.SelectConfigAxisID / 2; i++)
                {
                    SlaveID.Add("SLAVE-" + (i + 1).ToString());
                }
            }
            #endregion
            #region 4合一伺服
            if (ConfigServo.SelectConfigSlaveID == 2 && ConfigServo.SelectConfigAxisID == 4)
            {
                for (int i = 0; i < ConfigServo.SelectConfigAxisID / 2; i++)
                {
                    SlaveID.Add("SLAVE-" + (i + 1).ToString());
                }
            }
            #endregion
            #region 6合一伺服
            if (ConfigServo.SelectConfigSlaveID == 3 && ConfigServo.SelectConfigAxisID == 6)
            {
                for (int i = 0; i < ConfigServo.SelectConfigAxisID / 2; i++)
                {
                    SlaveID.Add("SLAVE-" + (i + 1).ToString());
                }
            }
            #endregion

            //单轴伺服
            #region 单轴伺服
            if ((ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 1) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 2) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 3) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 4) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 5) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 6) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 7) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 8) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 9) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 10) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 11) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 12) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 13) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 14) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 15) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 16) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 17) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 18) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 19) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 20) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 21) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 22) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 23) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 24) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 25) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 26) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 27) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 28) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 29) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 30) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 31) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 32) )
            {
                for (int i = 0; i < ConfigServo.SelectConfigAxisID; i++)
                {
                    SlaveID.Add("SLAVE-" + (i + 1).ToString());
                }
            }
            #endregion

            //if (GlobalCurrentInput.SelectedServoName == "6合一伺服")
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2", "SLAVE-3" };
            //}
            //else if (GlobalCurrentInput.SelectedServoName == "4合一伺服")
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2" };
            //}
            //else if (GlobalCurrentInput.SelectedServoName == "MD4伺服")
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1", "SLAVE-2" };
            //}
            //else if (GlobalCurrentInput.SelectedServoName == "2合一伺服")
            //{
            //    SlaveID = new ObservableCollection<string>() { "SLAVE-1" };
            //}
            //else
            //{
            //    if (AddSlaveAxisIDSet.GetSlaveIDList == null)//不添加从站ID时，默认显示SLAVE-1
            //    {
            //        SlaveID = new ObservableCollection<string>() { "SLAVE-1" };
            //    }
            //    else
            //    {
            //        SlaveID = AddSlaveAxisIDSet.GetSlaveIDList;
            //    }
            //}
        }

        //*************************************************************************
        //函数名称：ImportFirmwareUpdateFile
        //函数功能：导入升级文件
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.04.23&2022.05.23
        //*************************************************************************
        public void ImportFirmwareUpdateFile()
        {
            int iRet = -1;
            string strFilePath = null;
            string ServoName = null;

            try
            {
                //获取文件路径
                iRet = ExcelHelper.GetReadPath_ForUpdate(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }
                else
                {
                    FileAddress = strFilePath;
                }

                //获取固件升级信息集合
                iRet = YModemHelper.GetFirmwareUpdateSet(strFilePath);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2007);
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2028);
                    return;
                }
                else
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取伺服驱动器名称

                ViewModelSet.Main?.GetEditionInfo_For_Softwareversion();    //获取伺服名称

                ServoName = SoftwareStateParameterSet.ServoName;

                //清空固件升级信息集合
                OthersHelper.ClearFirmwareUpdateSet();

                ////获取伺服驱动器名称                
                //ViewModelSet.Main?.GetEditionInfo();    //获取伺服名称

                //ServoName = SoftwareStateParameterSet.ServoName;

            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FIRMWAREUPDATE_IMPORT_UPDATE_FILE, "ImportUpdateFile", ex);
            }
        }
        public bool CanImportFirmwareUpdateFile()
        {
            if (!ARMStatus.IsFirmUpdate)
            {
                return false;
            }

            if (SelectedSlaveID != null)
            {
                return true;
            }

            //if (!ARM1 && !ARM2 && !ARM3)
            //{
            //    return false;
            //}

            if (FirmwareUpdateSet.Process == FirmwareUpdateProcess.NONE || FirmwareUpdateSet.Process == FirmwareUpdateProcess.SUCCEED)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        //*************************************************************************
        //函数名称：FirmwareUpdateStart_For_NameVerification
        //函数功能：升级开始
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.05.23&2023.10.27
        //*************************************************************************
        public void FirmwareUpdateStart_For_NameVerification()
        {
            string ServoName = null;
            string[] ServoFileName = null;
                      
            //检查串口状态
            int iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(1000);
                return;
            }

            //伺服驱动器名称
            ServoName = SoftwareStateParameterSet.ServoName;

            ServoFileName = FirmwareUpdateSet.FileName.Split('_');

            //文件名相同且伺服驱动器名称时，才能固件升级
            if (ServoFileName[0].Equals(ServoName))
            {
                //判断是否有文件载入
                if (string.IsNullOrEmpty(FirmwareUpdateSet.FileName))
                {
                    ShowNotification(2015);
                    return;
                }

                //信息提示
                if (IsFirstUpdate)
                {
                    IsFirstUpdate = false;
                    OthersHelper.GetWindowsStartupPosition();
                    if (MessageBoxService.ShowMessage("升级功能必须在非使能状态下操作" + "\r\n" + "升级完成伺服会自动重启，是否进行升级操作...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.No)
                    {
                        return;
                    }
                }

                //清空固件升级信息集合
                OthersHelper.ClearFirmwareUpdateSet();

                //更新升级状态                
                FirmwareUpdateSet.IsUpdate = true;
                FirmwareUpdateSet.Process = FirmwareUpdateProcess.ASSIGNMENT;

                //发送升级指令
                //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_Update(FirmwareUpdateSet.ARM[FirmwareUpdateSet.ARMIndex]);
                //多合一伺服
                #region 2合一伺服
                if (ConfigServo.SelectConfigSlaveID == 2 && ConfigServo.SelectConfigAxisID == 2)
                {
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_Update(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                } 
                #endregion
                #region 4合一伺服
                if (ConfigServo.SelectConfigSlaveID == 2 && ConfigServo.SelectConfigAxisID == 4)
                {
                    if (SelectedSlaveID == "SLAVE-1")
                    {
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_Update(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                    }
                    else
                    {
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_Update(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                    }
                } 
                #endregion
                #region 6合一伺服
                if (ConfigServo.SelectConfigSlaveID == 3 && ConfigServo.SelectConfigAxisID == 6)
                {
                    if (SelectedSlaveID == "SLAVE-1")
                    {
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_Update(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                    }
                    else if (SelectedSlaveID == "SLAVE-2")
                    {
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_Update(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                    }
                    else
                    {
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_Update(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                    }
                }
                #endregion

                //单轴伺服
                #region 单轴伺服
                if ((ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 1) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 2) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 3) ||
                            (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 4) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 5) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 6) ||
                            (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 7) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 8) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 9) ||
                            (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 10) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 11) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 12) ||
                            (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 13) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 14) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 15) ||
                            (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 16) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 17) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 18) ||
                            (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 19) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 20) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 21) ||
                            (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 22) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 23) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 24) ||
                            (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 25) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 26) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 27) ||
                            (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 28) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 29) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 30) ||
                            (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 31) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 32))
                {
                    if (SelectedSlaveID.Substring(6) != GlobalCurrentInput.SelectedAxisID.Substring(5))
                    {
                        ViewModelSet.Main?.ShowHintInfo("所选从站地址与当前轴地址不同...");
                        return;
                    }
                    else
                    {
                        ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_Update(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                    }
                } 
                #endregion

                //所有的界面都不能再切换
                ViewModelSet.Main.IsAllPageEnabled = false;

                //CheckBox不能再使用
                CheckBoxEnabled = false;
            }
            else
            {
                //ViewModelSet.CommunicationSet?.ShowNotification("文件名和伺服驱动器名不相同，请重新选择...");
                //信息提示
                ViewModelSet.Main?.ShowHintInfo("文件名和伺服驱动器名不相同，请重新选择...");
                return;
            }

            ////判断是否有文件载入
            //if (string.IsNullOrEmpty(FirmwareUpdateSet.FileName))
            //{
            //    ShowNotification(2015);
            //    return;
            //}

            ////信息提示
            //if (IsFirstUpdate)
            //{
            //    IsFirstUpdate = false;
            //    OthersHelper.GetWindowsStartupPosition();
            //    if (MessageBoxService.ShowMessage("升级功能必须在非使能状态下操作" + "\r\n" + "升级完成伺服会自动重启，是否进行升级操作...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.No)
            //    {
            //        return;
            //    }
            //}

            ////清空固件升级信息集合
            //OthersHelper.ClearFirmwareUpdateSet();

            ////更新升级状态                
            //FirmwareUpdateSet.IsUpdate = true;
            //FirmwareUpdateSet.Process = FirmwareUpdateProcess.ASSIGNMENT;

            ////发送升级指令
            //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_Update(FirmwareUpdateSet.ARM[FirmwareUpdateSet.ARMIndex]);

            ////所有的界面都不能再切换
            //ViewModelSet.Main.IsAllPageEnabled = false;

            ////CheckBox不能再使用
            //CheckBoxEnabled = false;
        }
        public bool CanFirmwareUpdateStart_For_NameVerification()
        {
            if (!ARMStatus.IsFirmUpdate)
            {
                return false;
            }

            //if (!ARM1 && !ARM2 && !ARM3)
            //{
            //    return false;
            //}

            if (FirmwareUpdateSet.Process == FirmwareUpdateProcess.NONE || FirmwareUpdateSet.Process == FirmwareUpdateProcess.SUCCEED)
            {
                //判断是否有文件载入
                if (string.IsNullOrEmpty(FirmwareUpdateSet.FileName))
                {
                    return false;
                }
                else
                {
                    return true;
                }
                //return true;
            }
            else
            {
                return false;
            }
        }

        //*************************************************************************
        //函数名称：FirmwareUpdateStart
        //函数功能：升级开始
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.26
        //*************************************************************************
        public void FirmwareUpdateStart()
        {
            //检查串口状态
            int iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(1000);
                return;
            }

            //判断是否有文件载入
            if (string.IsNullOrEmpty(FirmwareUpdateSet.FileName))
            {
                ShowNotification(2015);
                return;
            }

            //信息提示
            if (IsFirstUpdate)
            {
                IsFirstUpdate = false;
                OthersHelper.GetWindowsStartupPosition();
                if (MessageBoxService.ShowMessage("升级功能必须在非使能状态下操作" + "\r\n" + "升级完成伺服会自动重启，是否进行升级操作...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.No)
                {
                    return;
                }
            }

            //清空固件升级信息集合
            OthersHelper.ClearFirmwareUpdateSet();

            //更新升级状态                
            FirmwareUpdateSet.IsUpdate = true;
            FirmwareUpdateSet.Process = FirmwareUpdateProcess.ASSIGNMENT;

            //发送升级指令
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_Update(FirmwareUpdateSet.ARM[FirmwareUpdateSet.ARMIndex]);

            //所有的界面都不能再切换
            ViewModelSet.Main.IsAllPageEnabled = false;

            //CheckBox不能再使用
            CheckBoxEnabled = false;
        }
        public bool CanFirmwareUpdateStart()
        {
            if (!ARMStatus.IsFirmUpdate)
            {
                return false;
            }

            //if (!ARM1 && !ARM2 && !ARM3)
            //{
            //    return false;
            //}

            if (FirmwareUpdateSet.Process == FirmwareUpdateProcess.NONE || FirmwareUpdateSet.Process == FirmwareUpdateProcess.SUCCEED)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        //*************************************************************************
        //函数名称：FirmwareUpdateAbort
        //函数功能：升级放弃
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.26
        //*************************************************************************
        public void FirmwareUpdateAbort()
        {
            int iRet = -1;

            try
            {
                //检查串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2016);
                    return;
                }

                //所有的界面都能再切换
                ViewModelSet.Main.IsAllPageEnabled = true;

                //CheckBox使用
                CheckBoxEnabled = true;

                //下发终止升级命令
                CommunicationSet.SerialPortInfo.Write("a");

                //清空升级任务
                OthersHelper.ClearFirmwareUpdateTask();

                //清空固件升级信息集合
                OthersHelper.ClearFirmwareUpdateSet();
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.YMODEM_FIRMWARE_UPDATE_ABORT, "FirmwareUpdateAbort", ex);
            }
        }
        public bool CanFirmwareUpdateAbort()
        {
            if (FirmwareUpdateSet.Process == FirmwareUpdateProcess.NONE || FirmwareUpdateSet.Process == FirmwareUpdateProcess.SUCCEED)
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.04.29&2023.02.06
        //*************************************************************************
        public void OnSelectedSlaveIDChanged()
        {
            GlobalCurrentInput.SelectedSlaveID = SelectedSlaveID;

            RefreshUpdateARMSet();

            ARMStatus.IsFirmUpdate = true;
        }

        //public void OnARM1Changed()
        //{
        //    if (ARM1)
        //    {
        //        ARM2 = false;
        //        ARM3 = false;

        //        CheckARMServoStatus();
        //    }

        //    RefreshUpdateARMSet();          
        //}
        //public void OnARM2Changed()
        //{
        //    if (ARM2)
        //    {
        //        ARM1 = false;
        //        ARM3 = false;

        //        CheckARMServoStatus();
        //    }

        //    RefreshUpdateARMSet();          
        //}
        //public void OnARM3Changed()
        //{
        //    if (ARM3)
        //    {
        //        ARM1 = false;
        //        ARM2 = false;

        //        CheckARMServoStatus();
        //    }

        //    RefreshUpdateARMSet();         
        //}
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：RefreshUpdateARMSet
        //函数功能：更新升级ARM编号集合
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.05.07
        //*************************************************************************
        private void RefreshUpdateARMSet()
        {
            if (FirmwareUpdateSet.ARM == null)
            {
                FirmwareUpdateSet.ARM = new List<int>();
            }
            else
            {
                FirmwareUpdateSet.ARM.Clear();
            }

            ARMCheckedHint = "提示：当前选中从站" + SelectedSlaveID + "升级";

            //if (ARM1 && !ARM2 && !ARM3)
            //{
            //    ARMCheckedHint = "提示：当前选中从站1升级";

            //    FirmwareUpdateSet.ARM.Add(1);
            //}
            //else if (!ARM1 && ARM2 && !ARM3)
            //{
            //    ARMCheckedHint = "提示：当前选中从站2升级";

            //    FirmwareUpdateSet.ARM.Add(2);
            //}
            //else if (!ARM1 && !ARM2 && ARM3)
            //{
            //    ARMCheckedHint = "提示：当前选中从站3升级";

            //    FirmwareUpdateSet.ARM.Add(3);
            //}
            //else if (!ARM1 && !ARM2 && !ARM3)
            //{
            //    ARMCheckedHint = "提示：当前无从站升级";

            //    FirmwareUpdateSet.ARM.Clear();
            //}

            FirmwareUpdateSet.ARMIndex = 0;
        }

        //*************************************************************************
        //函数名称：CheckARMServoStatus
        //函数功能：判断ARM对应的轴的伺服状态
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.09.14&2023.10.27
        //*************************************************************************
        private void CheckARMServoStatus()
        {
            //判断串口状态
            int iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                return;
            }

            //获取对应的轴状态
            #region 2合一伺服
            if (ConfigServo.SelectConfigSlaveID == 2 && ConfigServo.SelectConfigAxisID == 2)
            {
                OthersHelper.RefreshAxisSet(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, "SLAVE-1");
            }
            #endregion
            #region 4合一伺服
            if (ConfigServo.SelectConfigSlaveID == 2 && ConfigServo.SelectConfigAxisID == 4)
            {
                if (SelectedSlaveID == "SLAVE-1")
                {
                    OthersHelper.RefreshAxisSet(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                }
                else
                {
                    OthersHelper.RefreshAxisSet(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                }
            }
            #endregion
            #region 6合一伺服
            if (ConfigServo.SelectConfigSlaveID == 3 && ConfigServo.SelectConfigAxisID == 6)
            {
                if (SelectedSlaveID == "SLAVE-1")
                {
                    OthersHelper.RefreshAxisSet(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                }
                else if (SelectedSlaveID == "SLAVE-2")
                {
                    OthersHelper.RefreshAxisSet(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                }
                else
                {
                    OthersHelper.RefreshAxisSet(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
                }
            }
            #endregion

            //单轴伺服
            #region 单轴伺服
            if ((ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 1) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 2) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 3) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 4) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 5) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 6) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 7) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 8) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 9) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 10) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 11) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 12) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 13) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 14) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 15) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 16) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 17) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 18) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 19) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 20) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 21) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 22) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 23) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 24) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 25) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 26) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 27) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 28) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 29) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 30) ||
                (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 31) || (ConfigServo.SelectConfigSlaveID == 1 && ConfigServo.SelectConfigAxisID == 32) )
            {
                OthersHelper.RefreshAxisSet(ConfigServo.SelectConfigSlaveID, ConfigServo.SelectConfigAxisID, SelectedSlaveID);
            }
            #endregion
           
            //if (ARM1)
            //{
            //    OthersHelper.RefreshAxisSet(ARMIndex: "1");
            //}
            //else if (ARM2)
            //{
            //    OthersHelper.RefreshAxisSet(ARMIndex: "2");
            //}
            //else
            //{
            //    OthersHelper.RefreshAxisSet(ARMIndex: "3");
            //}

            //清空信息提示
            FirmwareUpdateSet.ProcessNotification = new StringBuilder();

            //下达获取轴状态任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_AxisStatus(AxisIndex: "A");
            //if (GlobalCurrentInput.SelectedServoName == "6合一伺服" || GlobalCurrentInput.SelectedServoName == "4合一伺服" || GlobalCurrentInput.SelectedServoName == "2合一伺服")
            //{
            //    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_AxisStatus(AxisIndex: "A");
            //}
            //else
            //{
            //    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_AxisStatus();
            //}
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }
}