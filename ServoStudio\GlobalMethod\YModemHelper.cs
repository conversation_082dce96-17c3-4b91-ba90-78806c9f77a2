﻿using ServoStudio.GlobalConstant;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace ServoStudio.GlobalMethod
{
    public static class YModemHelper
    {
        public static byte STX = 0X02;
        public static byte ETX = 0X03;
        public static byte EOT = 0X04;
        public static byte ACK = 0X06;
        public static byte NAK = 0X15;//21
        public static byte CA = 0X18;//24
        public static byte C = 0X43;//67

        public static int PACKET_HEADER = 3;
        public static int PACKET_CRC = 2;
        public static int PACKET_SIZE = 1024;
      
        //*************************************************************************
        //函数名称：GetFirmwareUpdateSet
        //函数功能：获取固件升级信息集合
        //
        //输入参数：string strPath           文件地址
        //
        //输出参数：1:OK
        //         0:No_Effect
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2020.04.24
        //*************************************************************************
        public static int GetFirmwareUpdateSet(string strPath)
        {
            FileStream fileStream = null;

            try
            {
                if (string.IsNullOrEmpty(strPath))
                {
                    return RET.ERROR;
                }

                //获取文件名称
                FirmwareUpdateSet.FileName = Path.GetFileName(strPath);
                if (string.IsNullOrEmpty(FirmwareUpdateSet.FileName))
                {
                    return RET.ERROR;
                }

                //文件是否有汉字
                if (OthersHelper.IsChineseCharacter(FirmwareUpdateSet.FileName))
                {
                    return RET.NO_EFFECT;
                }

                //获取文件信息
                fileStream = new FileStream(strPath, FileMode.Open);
                if (fileStream == null)
                {
                    return RET.ERROR;
                }
              
                //固件信息赋值               
                FirmwareUpdateSet.FileSize = (int)fileStream.Length;
                FirmwareUpdateSet.FileContent = new byte[fileStream.Length];               
                fileStream.Read(FirmwareUpdateSet.FileContent, 0, FirmwareUpdateSet.FileSize);

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.YMODEM_GET_FIRMWARE_UPDATE_FILE, "GetFirmwareUpdateFile", ex);
                return RET.ERROR;
            }
            finally
            {
                if (fileStream != null)
                {
                    fileStream.Close();
                }           
            }
        }

        //*************************************************************************
        //函数名称：GetInitialPacket
        //函数功能：获取第一帧报文
        //
        //输入参数：NONE
        //
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.04.24
        //*************************************************************************
        public static byte[] GetInitialPacket()
        {
            byte[] bCRC = new byte[2];
            byte[] bData = new byte[PACKET_HEADER + PACKET_SIZE + PACKET_CRC];
            
            try
            {
                if (string.IsNullOrEmpty(FirmwareUpdateSet.FileName))
                {
                    return null;
                }

                //报头设置
                bData[0] = STX;
                bData[1] = 0x00;
                bData[2] = 0xFF;

                //文件名
                byte[] bFileName = Encoding.GetEncoding("GB18030").GetBytes(FirmwareUpdateSet.FileName);//简体中文
                bFileName.CopyTo(bData, PACKET_HEADER);

                //文件大小
                byte[] bFileSize = Encoding.ASCII.GetBytes(Convert.ToString(FirmwareUpdateSet.FileSize));
                bFileSize.CopyTo(bData, bFileName.Length + PACKET_HEADER + 1);

                //CRC   
                bCRC = Cal_CRC16(bData, PACKET_HEADER, PACKET_SIZE);
                bCRC.CopyTo(bData, PACKET_HEADER + PACKET_SIZE);

                string a = HexHelper.BytesToHexString(bData);

                return bData;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.YMODEM_INITIAL_PACKET, "InitialPacket", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetDataPacket
        //函数功能：获取数据报文
        //
        //输入参数：byte[] bRaw          源文件
        //         int iOffset          起始位置的检索号
        //         int iPackNumber      数据包编号
        //         ref int iDataSize    实际数据包大小
        //
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.04.28
        //*************************************************************************
        public static byte[] GetDataPacket(byte[] bRaw, int iOffset, int iPacketNumber, ref int iDataSize)
        {
            int iRemainingSize = 0;//剩余的报文大小
            byte[] bCRC = new byte[2];
            byte[] bData = new byte[PACKET_HEADER + PACKET_SIZE + PACKET_CRC];

            try
            {
                if (bRaw == null)
                {
                    return null;
                }

                //剩余的报文大小             
                iRemainingSize = bRaw.Length - iOffset;
                if (iRemainingSize > PACKET_SIZE)
                {
                    iDataSize = PACKET_SIZE;
                }
                else
                {
                    iDataSize = iRemainingSize;
                }

                //报头设置               
                bData[0] = STX;
                bData[1] = (byte)(iPacketNumber & 0xFF);
                bData[2] = (byte)((~iPacketNumber) & 0xFF);

                //数据
                Array.Copy(bRaw, iOffset, bData, PACKET_HEADER, iDataSize);

                //补0xFF数据
                for (int iIndex = PACKET_HEADER + iDataSize; iIndex < PACKET_HEADER + PACKET_SIZE; iIndex++)
                {
                    bData[iIndex] = 0xFF;
                }

                //CRC
                bCRC = Cal_CRC16(bData, PACKET_HEADER, PACKET_SIZE);
                bCRC.CopyTo(bData, PACKET_HEADER + PACKET_SIZE);

                return bData;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.YMODEM_GET_DATA_PACKET, "GetDataPacket", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetEmptyPacket
        //函数功能：获取空报文
        //
        //输入参数：NONE
        //
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.04.24
        //*************************************************************************
        public static byte[] GetEmptyPacket()
        {
            byte[] bData = new byte[PACKET_HEADER + PACKET_SIZE + PACKET_CRC];
         
            try
            {
                //报头
                bData[0] = STX;
                bData[1] = 0x00;
                bData[2] = 0xFF;

                return bData;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.YMODEM_GET_EMPTY_PACKET, "GetEmptyPacket", ex);
                return null;
            }          
        }

        //*************************************************************************
        //函数名称：Cal_CRC16
        //函数功能：CRC校验
        //
        //输入参数：NONE
        //
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.04.24
        //*************************************************************************
        public static byte[] Cal_CRC16(byte[] data, int start, int length)
        {
            int crc = 0, index = 0, iRet = 0;
            byte[] buffer = new byte[2];
            while (index < length)
            {
                crc = UpdateCRC16(crc, data[start + index]);
                index++;
            }

            crc = UpdateCRC16(crc, 0);
            crc = UpdateCRC16(crc, 0);
            iRet = crc & 0xffff;

            buffer[0] = (byte)((iRet >> 8) & 0xff);
            buffer[1] = (byte)((iRet) & 0xff);

            return buffer;
        }
        private static int UpdateCRC16(int crcIn, byte b)
        {
            int crc = crcIn;
            int input = b | 0x100;
            do
            {
                crc <<= 1;
                input <<= 1;
                if ((input & 0x100) != 0)
                    ++crc;
                if ((crc & 0x10000) != 0)
                    crc ^= 0x1021;
            }
            while ((input & 0x10000) == 0);
            return crc & 0xffff;
        }
    }   
}
