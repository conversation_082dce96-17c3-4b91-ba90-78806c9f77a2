<?xml version="1.0"?>
<doc>
    <assembly>
        <name>GalaSoft.MvvmLight.Platform</name>
    </assembly>
    <members>
        <member name="T:GalaSoft.MvvmLight.Threading.DispatcherHelper">
            <summary>
            Helper class for dispatcher operations on the UI thread.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.UIDispatcher">
            <summary>
            Gets a reference to the UI thread's dispatcher, after the
            <see cref="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.Initialize" /> method has been called on the UI thread.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.CheckBeginInvokeOnUI(System.Action)">
            <summary>
            Executes an action on the UI thread. If this method is called
            from the UI thread, the action is executed immendiately. If the
            method is called from another thread, the action will be enqueued
            on the UI thread's dispatcher and executed asynchronously.
            <para>For additional operations on the UI thread, you can get a
            reference to the UI thread's dispatcher thanks to the property
            <see cref="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.UIDispatcher" /></para>.
            </summary>
            <param name="action">The action that will be executed on the UI
            thread.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.RunAsync(System.Action)">
            <summary>
            Invokes an action asynchronously on the UI thread.
            </summary>
            <param name="action">The action that must be executed.</param>
            <returns>The object that provides handlers for the completed async event dispatch.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.Initialize">
            <summary>
            This method should be called once on the UI thread to ensure that
            the <see cref="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.UIDispatcher" /> property is initialized.
            <para>In a Silverlight application, call this method in the
            Application_Startup event handler, after the MainPage is constructed.</para>
            <para>In WPF, call this method on the static App() constructor.</para>
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.Reset">
            <summary>
            Resets the class by deleting the <see cref="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.UIDispatcher"/>
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Views.DialogService">
            <summary>
            An implementation of <see cref="T:GalaSoft.MvvmLight.Views.IDialogService"/> allowing
            to display simple dialogs to the user. Note that this class
            uses the built in Android dialogs which may or may not
            be sufficient for your needs. Using this class is easy
            but feel free to develop your own IDialogService implementation
            if needed.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowError(System.String,System.String,System.String,System.Action)">
            <summary>
            Displays information about an error.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowError(System.Exception,System.String,System.String,System.Action)">
            <summary>
            Displays information about an error.
            </summary>
            <param name="error">The exception of which the message must be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessage(System.String,System.String)">
            <summary>
            Displays information to the user. The dialog box will have only
            one button with the text "OK".
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessage(System.String,System.String,System.String,System.Action)">
            <summary>
            Displays information to the user. The dialog box will have only
            one button.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessage(System.String,System.String,System.String,System.String,System.Action{System.Boolean})">
            <summary>
            Displays information to the user. The dialog box will have only
            one button.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonConfirmText">The text shown in the "confirm" button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="buttonCancelText">The text shown in the "cancel" button
            in the dialog box. If left null, the text "Cancel" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user. The callback method will get a boolean
            parameter indicating if the "confirm" button (true) or the "cancel" button
            (false) was pressed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessageBox(System.String,System.String)">
            <summary>
            Displays information to the user in a simple dialog box. The dialog box will have only
            one button with the text "OK". This method should be used for debugging purposes.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
        </member>
        <member name="T:GalaSoft.MvvmLight.Views.NavigationService">
            <summary>
            Windows 8 and Windows Phone Application 8.1 implementation of <see cref="T:GalaSoft.MvvmLight.Views.INavigationService"/>.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Views.NavigationService.RootPageKey">
            <summary>
            The key that is returned by the <see cref="P:GalaSoft.MvvmLight.Views.NavigationService.CurrentPageKey"/> property
            when the current Page is the root page.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Views.NavigationService.UnknownPageKey">
            <summary>
            The key that is returned by the <see cref="P:GalaSoft.MvvmLight.Views.NavigationService.CurrentPageKey"/> property
            when the current Page is not found.
            This can be the case when the navigation wasn't managed by this NavigationService,
            for example when it is directly triggered in the code behind, and the
            NavigationService was not configured for this page type.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Views.NavigationService.CurrentPageKey">
            <summary>
            The key corresponding to the currently displayed page.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.GoBack">
            <summary>
            If possible, discards the current page and displays the previous page
            on the navigation stack.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String)">
            <summary>
            Displays a new page corresponding to the given key. 
            Make sure to call the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Type)"/>
            method first.
            </summary>
            <param name="pageKey">The key corresponding to the page
            that should be displayed.</param>
            <exception cref="T:System.ArgumentException">When this method is called for 
            a key that has not been configured earlier.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String,System.Object)">
            <summary>
            Displays a new page corresponding to the given key,
            and passes a parameter to the new page.
            Make sure to call the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Type)"/>
            method first.
            </summary>
            <param name="pageKey">The key corresponding to the page
            that should be displayed.</param>
            <param name="parameter">The parameter that should be passed
            to the new page.</param>
            <exception cref="T:System.ArgumentException">When this method is called for 
            a key that has not been configured earlier.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Type)">
            <summary>
            Adds a key/page pair to the navigation service.
            </summary>
            <param name="key">The key that will be used later
            in the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String)"/> or <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String,System.Object)"/> methods.</param>
            <param name="pageType">The type of the page corresponding to the key.</param>
        </member>
    </members>
</doc>
