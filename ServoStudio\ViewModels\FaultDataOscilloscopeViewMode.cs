﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using DevExpress.Mvvm.POCO;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using ServoStudio.Models;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.Threading;
using System.ComponentModel;
using System.Windows.Data;
using System.Linq;
using System.Windows.Controls;
using System.Data;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class FaultDataOscilloscopeViewModel
    {
        #region 字段
        private static bool IsInitialized = true;//是否初次载入，初始化
        private static bool IsEvaluationAll = true;//是否要全部赋值
        private static bool IsLoadedAgain = false;//是否是多次载入  

        public double dLastTime = 0;//获取上一次X坐标-时间
        public int iIndex = 0;//获取上一个检索号
        public double dLastCH1Value = 0;//获取上一次CH1值
        public double dLastCH2Value = 0;//获取上一次CH2值
        public double dLastCH3Value = 0;//获取上一次CH3值
        public double dLastCH4Value = 0;//获取上一次CH4值  
        public double dLastCH5Value = 0;//获取上一次CH5值
        public double dLastCH6Value = 0;//获取上一次CH6值
        public double dLastCH7Value = 0;//获取上一次CH7值
        public double dLastCH8Value = 0;//获取上一次CH8值 

        public string SelectedSamplingChannel1;
        public string SelectedSamplingChannel2;
        public string SelectedSamplingChannel3;
        public string SelectedSamplingChannel4;
        public string SelectedSamplingChannel5;
        public string SelectedSamplingChannel6;
        public string SelectedSamplingChannel7;
        public string SelectedSamplingChannel8;

        private Dictionary<string, string> dicDefaultUnit;
        #endregion

        #region 服务
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }

        [ServiceProperty(Key = "TorqueLegend")]
        protected virtual IDialogService DialogService_TorqueLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "PositionLegend")]
        protected virtual IDialogService DialogService_PositionLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "SlopeLegend")]
        protected virtual IDialogService DialogService_SlopeLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "JerkFreeLegend")]
        protected virtual IDialogService DialogService_JerkFreeLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "SquareLegend")]
        protected virtual IDialogService DialogService_SquareLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "SinLegend")]
        protected virtual IDialogService DialogService_SinLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "StepLegend")]
        protected virtual IDialogService DialogService_StepLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "SlopeAscLegend")]
        protected virtual IDialogService DialogService_SlopeAscLegend { get { return this.GetService<IDialogService>(); } }
        #endregion

        #region 示波器属性  

        #region 采样参数
        public virtual string Unit { get; set; }//采样单位
        public virtual string SampleChannel1 { get; set; }//采样通道1
        public virtual string SampleChannel2 { get; set; }//采样通道2
        public virtual string SampleChannel3 { get; set; }//采样通道3
        public virtual string SampleChannel4 { get; set; }//采样通道4
        public virtual string SampleChannel5 { get; set; }//采样通道5
        public virtual string SampleChannel6 { get; set; }//采样通道6
        public virtual string SampleChannel7 { get; set; }//采样通道7
        public virtual string SampleChannel8 { get; set; }//采样通道8

        //采样参数
        public virtual string SamplingPeriod { get; set; }//采样周期
        public virtual string SamplingDuration { get; set; }//采样时长
        #endregion

        #region 采样通道 
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo1 { get; set; }
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo1_Display { get; set; }//由Lilbert添加SampleChannelInfo1_Display用于展示
        public ICollectionView GroupedSampleChannelInfo1 { get; set; }
       
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo2 { get; set; }
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo2_Display { get; set; }//由Lilbert添加SampleChannelInfo2_Display用于展示
        public ICollectionView GroupedSampleChannelInfo2 { get; set; }
       
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo3 { get; set; }
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo3_Display { get; set; }//由Lilbert添加SampleChannelInfo3_Display用于展示
        public ICollectionView GroupedSampleChannelInfo3 { get; set; }  
       
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo4 { get; set; }
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo4_Display { get; set; }//由Lilbert添加SampleChannelInfo4_Display用于展示
        public ICollectionView GroupedSampleChannelInfo4 { get; set; }

        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo5 { get; set; }
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo5_Display { get; set; }//由Lilbert添加SampleChannelInfo5_Display用于展示
        public ICollectionView GroupedSampleChannelInfo5 { get; set; }

        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo6 { get; set; }
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo6_Display { get; set; }//由Lilbert添加SampleChannelInfo6_Display用于展示
        public ICollectionView GroupedSampleChannelInfo6 { get; set; }
 
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo7 { get; set; }
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo7_Display { get; set; }//由Lilbert添加SampleChannelInfo7_Display用于展示
        public ICollectionView GroupedSampleChannelInfo7 { get; set; }

        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo8 { get; set; }
        public virtual List<FaultDataSampleChannelInfoSet> SampleChannelInfo8_Display { get; set; }//由Lilbert添加SampleChannelInfo8_Display用于展示
        public ICollectionView GroupedSampleChannelInfo8 { get; set; }
        #endregion

        #region 其他
        public virtual bool ContinuousAcquisitionEnabled { get; set; }//连续采样使能
        public virtual bool AcquisitionStartButtonEnabled { get; set; }//开始采集按钮使能
        public virtual bool OthersButtonEnabled { get; set; }//其他关于波形按钮使能
        public virtual ObservableCollection<string> DisplayMethod { get; set; }//展示方式
        public virtual string SelectedDisplayMethod { get; set; }//选择的展示方式
        public virtual ObservableCollection<FaultDataOscilloscopePropertySet> OscilloscopeProperty { get; set; }//示波器属性集合
        public virtual ObservableCollection<FaultDataOsilloscopeCalculateSet> OsilloscopeCalculate { get; set; }//数据计算
        #endregion

        #endregion

        #region 函数发生器与三环调试与运动调试属性       
        public virtual string PositionLoopGain { get; set; }//位置环增益
        public virtual string LoadInertiaRatio { get; set; }//负载惯量比      由Lilbert增加负载惯量比
        public virtual string FirstTrqcmdFilterTime { get; set; }//第一转矩滤波时间   由Lilbert增加第一转矩滤波时间
        public virtual string SpeedLoopGain { get; set; }//速度环增益          
        public virtual string SpeedLoopTimeConstant { get; set; }//速度环积分时间常数
        public virtual string CurrentLoopGain { get; set; }//电流环增益
        public virtual string CurrentLoopTimeConstant { get; set; }//电流环积分时间常数
       
        public virtual string PositionUnit { get; set; }//位置单位
        public virtual string TorqueUnit { get; set; }//转矩单位
        public virtual string SpeedUnit { get; set; }//速度单位
        public virtual string AccelerationUnit { get; set; }//加速单位

        //故障数据配置参数
        public virtual string AlarmCacheChennalSettingOne { get; set; }//故障数据配置参数1
        public virtual string AlarmCacheChennalSettingTwo { get; set; }//故障数据配置参数2
        public virtual string AlarmCacheChennalTimeSetting { get; set; }//故障数据配置通道时间
        #endregion


        #region 构造函数
        public FaultDataOscilloscopeViewModel()
        {
            ViewModelSet.FaultDataOscilloscope = this;
        }
        #endregion

        #region 公有方法

        #region 示波器
        //*************************************************************************
        //函数名称：FaultDataOscilloscopeLoaded
        //函数功能：FaultDataOscilloscope控件Loaded
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public void FaultDataOscilloscopeLoaded()
        {
            int iRet = -1;

            try
            {
                #region 初始化   

                GroupSampleChannelInitialize();

                //string SimpleTime = OthersHelper.GetCurrentValueOfIndex("0x200617");//故障数据配置通道时间

                //加载示波器预配置文件
                //GetFaultDataOscilloscopeConfigFile();

                if (SampleChannel1 != "停用")
                {
                    //数据采集启动按钮状态
                    AcquisitionStartButtonEnabled = true;
                }                

                //采样周期和采样时长
                //GetSamplingDuration(SimpleTime);

                //波形控制与波形计算初始化
                WaveControl_WaveCalculateInitialize();

                //事件注册
                EventRegisterInitialize();               

                //按钮使能初始化
                ButtonEnableInitialize();

                //默认单位集合
                DefaultUnitDictionaryInitialize();

                //侧边栏属性缩回
                ViewModelSet.Main?.SlidePanelDisplayControl(true, true);

                //ReadProgramJogParameter();
                #endregion

                #region 赋值
                if (IsInitialized == true)
                {
                    IsInitialized = false;
                    IsEvaluationAll = true;

                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadAdjustmentParameter("All");
                        //EvalutionAdjustmentParameter();
                        GlobalChanged();
                    }
                    else
                    {
                        GetDefaultAdjustmentParameter("All");
                    }                    
                }
                else
                {
                    IsEvaluationAll = true;
                    IsLoadedAgain = true;

                    InterfaceEvaluationFromGlobalVariable();

                    //EvalutionAdjustmentParameter();
                    GlobalChanged();

                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadAdjustmentParameter("All");
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }
                   
                }
                #endregion
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_LOADED, "OscilloscopeLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：FaultDataOscilloscopeUnloaded
        //函数功能：FaultDataOscilloscope控件Unloaded
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        public void FaultDataOscilloscopeUnloaded()
        {
            //预配置写入配置文件
            //OthersHelper.WriteSelectedOscilloscopePresetIntoFile();
        }
                   
        //*************************************************************************
        //函数名称：ParameterAcquisition
        //函数功能：参数采集开启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        public void ParameterAcquisitionStart()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //判断当前是否有采样任务，系统只能有一个采样任务                
                if (FaultAcquisitionInfoSet.IsExistTask)
                {
                    ShowNotification(2036);
                    return;
                }

                //判断当前波形是否画完
                if (!FaultAcquisitionInfoSet.IsDrawingCompleted)
                {
                    WindowSet.clsMainWindow?.ShowNotification(3016);
                    return;
                }

                //判断倍乘输入是否有效
                for (int i = 0; i < OscilloscopeProperty.Count; i++)
                {
                    if (!OthersHelper.IsInputNumber(OscilloscopeProperty[i].Doubling))
                    {
                        OscilloscopeProperty[i].Doubling = "1";
                    }
                }

                //更新示波器采样信息集合里面的List(采样通道，采样数据，采样单位，采样单位换算)
                RefreshAcquisitionList();

                //获取报文内容-已经转换了小端
                iRet = GetTransmittingContent(ref lstTransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(iRet);
                    return;
                }
                   
                //下达采样任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterFaultAcquisition(PageName.FAULTDATAOSCILLOSCOPE, TaskName.AssigningFaultAcquisition, lstTransmittingDataInfo);

                //按钮使能
                OthersButtonEnabled = false;
                ViewModelSet.Main.IsUnitExchangedEnabled = false;

                //记录采样参数
                RecordLastSamplingParameter();

                //计算数据重置
                ResetLastWavaCalculate();
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_PARAMETER_ACQUISITION, "ParameterAcquisitionStart", ex);
            }
        }

        //*************************************************************************
        //函数名称：LoopParameterAcquisitionStart
        //函数功能：连续循环参数采集开启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        public void LoopParameterAcquisitionStart()
        {
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取报文内容-已经转换了小端
            GetTransmittingContent(ref lstTransmittingDataInfo);

            //下达采样任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterFaultAcquisition(PageName.FAULTDATAOSCILLOSCOPE, TaskName.AssigningFaultAcquisition, lstTransmittingDataInfo);
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionStop
        //函数功能：故障数据采集停止
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        public void ParameterAcquisitionStop()
        {
            //循环采样停止
            FaultAcquisitionInfoSet.AcquisitionSwitch = false;

            //关闭线程
            OthersHelper.CloseAllThread();

            //连续采集下系统界面允许切换
            ViewModelSet.Main.IsAllPageEnabled = true;

            //清空最后一条历史记录
            OfflineFaultAcquisition.Last = new FaultAcquisitionParameterSet();

            //信息提示
            ViewModelSet.Main?.ShowHintInfo("故障数据采集停止");
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionExport
        //函数功能：参数采集导出
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        public void ParameterAcquisitionExport()
        {
            int iRet = -1;
            string strFilePath = "";

            try
            {
                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return;
                }

                //信息确认并获取数据
                if (MessageBoxService.ShowMessage(MessageForConfirm("Export"), "请确定", MessageButton.YesNo) == MessageResult.Yes)
                {
                    //写入到离线文件
                    InterfaceEvaluationToFile();

                    //获取文件保存路径
                    iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.FaultAcquisition);
                    if (iRet == RET.ERROR)
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }
                    else if (iRet == RET.NO_EFFECT)
                    {
                        return;
                    }

                    //更新Excel配置文件
                    iRet = ExcelHelper.WriteIntoExcel_For_FaultAcquisition(strFilePath);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ShowNotification(2002);
                    }
                    else
                    {
                        ShowNotification(2003);
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_PARAMETER_ACQUISITION_EXPORT, "ParameterAcquisitionExport", ex);
            }
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionImport
        //函数功能：参数采集导入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.01.14
        //*************************************************************************
        public void ParameterAcquisitionImport()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                //获取文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel_For_FaultAcquisition(strFilePath);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2001);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    ShowNotification(2000);
                    return;
                }

                //接口赋值
                InterfaceEvaluationFromFile();

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return;
                }

                //信息确认
                if (MessageBoxService.ShowMessage(MessageForConfirm("Import"), "请确定", MessageButton.YesNo) == MessageResult.Yes)
                {
                    //载入数据
                    ViewModelSet.FaultDataOscilloscopeView?.DisplayOscilloscopeImport();

                    //记录本次采样数据
                    FaultDataOscilloscopeModel.EvaluateLastWaveDataFromCurrent();

                    //记录采样参数
                    RecordLastSamplingParameter();

                    //计算数据重置
                    ResetLastWavaCalculate();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_PARAMETER_ACQUISITION_EXPORT, "ParameterAcquisitionExport", ex);
            }
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionClear
        //函数功能：波形信息清除
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.06
        //*************************************************************************
        public void ParameterAcquisitionClear()
        {
            ViewModelSet.FaultDataOscilloscopeView?.ClearOscilloscopeData();

            //波形控制与波形计算初始化
            WaveControl_WaveCalculateInitialize();
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionLast_For_IsHidden
        //函数功能：点击隐藏时示波器自动隐藏相应波形数据
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2021.07.20
        //*************************************************************************
        public void ParameterAcquisitionLast_For_IsHidden()
        {
            int iRet = -1;

            try
            {
                //接口赋值
                iRet = InterfaceEvaluationFromLast();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2013);
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return;
                }
                
                ViewModelSet.FaultDataOscilloscopeView?.DisplayOscilloscopeImport();

            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_PARAMTER_ACQUISITION_LAST, "ParameterAcquisitionLast_For_IsHidden", ex);
            }
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionLast
        //函数功能：载入上一条波形数据
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.18
        //*************************************************************************
        //public void ParameterAcquisitionLast()
        //{
        //    int iRet = -1;

        //    try
        //    {
        //        //接口赋值
        //        iRet = InterfaceEvaluationFromLast();
        //        if (iRet != RET.SUCCEEDED)
        //        {
        //            ShowNotification(2013);
        //            return;
        //        }

        //        //返回初始位置-为了MessageBox弹窗
        //        if (!OthersHelper.GetWindowsStartupPosition())
        //        {
        //            return;
        //        }

        //        //信息确认并载入数据
        //        if (MessageBoxService.ShowMessage(MessageForConfirm("Last"), "请确定", MessageButton.YesNo) == MessageResult.Yes)
        //        {
        //            ViewModelSet.FaultDataOscilloscopeView?.DisplayOscilloscopeImport();
        //        }
        //    }
        //    catch (System.Exception ex)
        //    {
        //        SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_PARAMTER_ACQUISITION_LAST, "ParameterAcquisitionLast", ex);
        //    }
        //}
       
        //*************************************************************************
        //函数名称：ButtonEnableInitialize
        //函数功能：按钮使能初始化
        //
        //输入参数：NONE
        //         
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        public void ButtonEnableInitialize()
        {
            if (FaultAcquisitionInfoSet.IsExistTask)
            {
                OthersButtonEnabled = false;
            }
            else
            {
                OthersButtonEnabled = true;
            }
        }

        //*************************************************************************
        //函数名称：ChangeWaveControlProperty_For_IsHidden
        //函数功能：波形展示属性控制
        //
        //输入参数：NONE
        //         
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.10
        //*************************************************************************
        public void ChangeWaveControlProperty_For_IsHidden(FaultDataOscilloscopePropertySet clsProperty)
        {
            if (clsProperty == null || OscilloscopeProperty == null)
            {
                return;
            }

            if (OscilloscopeProperty.Count == 0)
            {
                return;
            }

            if (clsProperty.ChannelNumber == "通道-1")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[0].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[0].Doubling = "1";
                }

                OscilloscopeProperty[0].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-2")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[1].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[1].Doubling = "1";
                }

                OscilloscopeProperty[1].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-3")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[2].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[2].Doubling = "1";
                }

                OscilloscopeProperty[2].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-4")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[3].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[3].Doubling = "1";
                }

                OscilloscopeProperty[3].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-5")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[4].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[4].Doubling = "1";
                }

                OscilloscopeProperty[4].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-6")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[5].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[5].Doubling = "1";
                }

                OscilloscopeProperty[5].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-7")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[6].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[6].Doubling = "1";
                }

                OscilloscopeProperty[6].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-8")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[7].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[7].Doubling = "1";
                }

                OscilloscopeProperty[7].IsHidden = clsProperty.IsHidden;
            }
        }

        //*************************************************************************
        //函数名称：ChangeWaveControlProperty
        //函数功能：波形展示属性控制
        //
        //输入参数：NONE
        //         
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.10
        //*************************************************************************
        public void ChangeWaveControlProperty(FaultDataOscilloscopePropertySet clsProperty)
        {
            if (clsProperty == null || OscilloscopeProperty == null)
            {
                return;
            }

            if (OscilloscopeProperty.Count == 0)
            {
                return;
            }

            if (clsProperty.ChannelNumber == "通道-1")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[0].Doubling = Convert.ToString(clsProperty.Doubling);
                }
                else
                {
                    OscilloscopeProperty[0].Doubling = "1";
                }

                OscilloscopeProperty[0].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-2")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[1].Doubling = Convert.ToString(clsProperty.Doubling);
                }
                else
                {
                    OscilloscopeProperty[1].Doubling = "1";
                }

                OscilloscopeProperty[1].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-3")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[2].Doubling = Convert.ToString(clsProperty.Doubling);
                }
                else
                {
                    OscilloscopeProperty[2].Doubling = "1";
                }

                OscilloscopeProperty[2].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-4")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[3].Doubling = Convert.ToString(clsProperty.Doubling);
                }
                else
                {
                    OscilloscopeProperty[3].Doubling = "1";
                }

                OscilloscopeProperty[3].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-5")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[4].Doubling = Convert.ToString(clsProperty.Doubling);                   
                }
                else
                {
                    OscilloscopeProperty[4].Doubling = "1";
                }

                OscilloscopeProperty[4].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-6")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[5].Doubling = Convert.ToString(clsProperty.Doubling);
                }
                else
                {
                    OscilloscopeProperty[5].Doubling = "1";
                }

                OscilloscopeProperty[5].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-7")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[6].Doubling = Convert.ToString(clsProperty.Doubling);
                }
                else
                {
                    OscilloscopeProperty[6].Doubling = "1";
                }

                OscilloscopeProperty[6].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-8")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[7].Doubling = Convert.ToString(clsProperty.Doubling);
                }
                else
                {
                    OscilloscopeProperty[7].Doubling = "1";
                }

                OscilloscopeProperty[7].IsHidden = clsProperty.IsHidden;
            }
        }
        #endregion 
        #endregion  

        #region 其他

        //*************************************************************************
        //函数名称：ReadAdjustmentParameter
        //函数功能：读发生器与三环参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.14
        //*************************************************************************
        public void ReadAdjustmentParameter(string strCategory)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.FAULTDATAOSCILLOSCOPE, TaskName.FaultDataOscilloscopeConfig, lstTransmittingDataInfo);//由Lilbert添加手动参数调优
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_ADJUSTMENT_PARAMETER, "ReadAdjustmentParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultAdjustmentParameter
        //函数功能：获取调试默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.11&2021.07.15&2022.11.08
        //*************************************************************************
        public void GetDefaultAdjustmentParameter(string strCategory)
        {
            try
            {
                //获取默认单位
                OthersHelper.GetCurrentUnit(bDefault: true);
                PositionUnit = DefaultUnit.PositionUnit;
                TorqueUnit = DefaultUnit.TorqueUnit;
                SpeedUnit = DefaultUnit.SpeedUnit;
                AccelerationUnit = DefaultUnit.AccelerationUnit;

                //获取配置文件数值
                switch (strCategory)
                {
                    case "0":
                        break;
                    case "1":
                        PositionLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Default");//位置环增益
                        LoadInertiaRatio = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Default");//负载惯量比    由Lilbert增加负载惯量比
                        FirstTrqcmdFilterTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Default");//第一转矩滤波时间    由Lilbert增加第一转矩滤波时间
                        SpeedLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Default");//速度环增益          
                        SpeedLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Default");//速度环积分时间常数
                        CurrentLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Gain", "Default");//电流环增益
                        CurrentLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Time Constant", "Default");//电流环积分时间常数

                        AlarmCacheChennalSettingOne = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 1 setting", "Default");//故障数据配置参数1
                        AlarmCacheChennalSettingTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 2 setting", "Default");//故障数据配置参数2

                        AlarmCacheChennalTimeSetting = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal Time setting", "Default");//故障数据配置通道时间
                        break;
                   
                    default:
                        PositionLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Default");//位置环增益
                        LoadInertiaRatio = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Default");//负载惯量比    由Lilbert增加负载惯量比
                        FirstTrqcmdFilterTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Default");//第一转矩滤波时间    由Lilbert增加第一转矩滤波时间
                        SpeedLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Default");//速度环增益          
                        SpeedLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Default");//速度环积分时间常数
                        CurrentLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Gain", "Default");//电流环增益
                        CurrentLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Time Constant", "Default");//电流环积分时间常数

                        AlarmCacheChennalSettingOne = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 1 setting", "Default");//故障数据配置参数1
                        AlarmCacheChennalSettingTwo = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 2 setting", "Default");//故障数据配置参数2

                        AlarmCacheChennalTimeSetting = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal Time setting", "Default");//故障数据配置通道时间
                        break;
                }

                GetFaultDataOscilloscopeConfig();
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_GET_DEFAULT_ADJUSTMENT_PARAMETER, "GetDefaultAdjustmentParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvalutionAdjustmentParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.11&2021.07.15&2022.11.25
        //*************************************************************************
        public void EvalutionAdjustmentParameter()
        {
            //获取当前的单位
            OthersHelper.GetCurrentUnit(bDefault: false);
            PositionUnit = CurrentUnit.Position;
            TorqueUnit = CurrentUnit.Torque;
            SpeedUnit = CurrentUnit.Speed;
            AccelerationUnit = CurrentUnit.Acceleration;

            //参数赋值
            if (IsEvaluationAll)
            {
                //标志位更新
                IsEvaluationAll = false;

                #region 三环
                PositionLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//额定功率   
                LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//负载惯量比    由Lilbert增减负载惯量比
                FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩滤波时间     由Lilbert增加第一转矩滤波时间         
                SpeedLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//额定电压
                SpeedLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//时间常数
                CurrentLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Gain", "Index"));//额定转矩
                CurrentLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Time Constant", "Index"));//时间常数
                #endregion

                #region 故障参数配置参数
                AlarmCacheChennalSettingOne = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 1 setting", "Index"));//故障数据配置参数1   
                AlarmCacheChennalSettingTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 2 setting", "Index"));//故障数据配置参数2         
                AlarmCacheChennalTimeSetting = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal Time setting", "Index"));//故障数据配置通道时间
                #endregion

            }
            else
            {
                #region 三环
                PositionLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//额定功率   
                LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//负载惯量比    由Lilbert增减负载惯量比
                FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩滤波时间     由Lilbert增加第一转矩滤波时间         
                SpeedLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//额定电压
                SpeedLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//时间常数
                CurrentLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Gain", "Index"));//额定转矩
                CurrentLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Time Constant", "Index"));//时间常数
                #endregion

                #region 故障参数配置参数
                AlarmCacheChennalSettingOne = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 1 setting", "Index"));//故障数据配置参数1   
                AlarmCacheChennalSettingTwo = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal 2 setting", "Index"));//故障数据配置参数2         
                AlarmCacheChennalTimeSetting = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Cache chennal Time setting", "Index"));//故障数据配置通道时间
                #endregion

            }

            GetFaultDataOscilloscopeConfig();
        }

        //*************************************************************************
        //函数名称：ExpandDisplay
        //函数功能：扩大波形展示界面
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.10
        //*************************************************************************
        public void ExpandDisplay()
        {
            if (ViewModelSet.Main != null)
            {
                ViewModelSet.Main.IsRibbonMinimized = true;
            }
        }

        //*************************************************************************
        //函数名称：NormalDisplay
        //函数功能：正常波形展示界面
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.10
        //*************************************************************************
        public void NormalDisplay()
        {
            if (ViewModelSet.Main != null)
            {
                ViewModelSet.Main.IsRibbonMinimized = false;
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.18&2022.11.25
        //************************************************************************* 
        public void GlobalChanged()
        {
            GlobalCurrentInput.PositionLoopGain = PositionLoopGain;
            GlobalCurrentInput.LoadInertiaRatio = LoadInertiaRatio;
            GlobalCurrentInput.FirstTrqcmdFilterTime = FirstTrqcmdFilterTime;
            GlobalCurrentInput.SpeedLoopGain = SpeedLoopGain;
            GlobalCurrentInput.SpeedLoopTimeConstant = SpeedLoopTimeConstant;
            GlobalCurrentInput.CurrentLoopGain = CurrentLoopGain;
            GlobalCurrentInput.CurrentLoopTimeConstant = CurrentLoopTimeConstant;
        }
        #endregion

        #region 私有方法       
        //*************************************************************************
        //函数名称：GetOscilloscopePresetConfigFile
        //函数功能：获取示波器配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.12
        //*************************************************************************
        public void GetFaultDataOscilloscopeConfigFile()
        {
            int iRet = -1;
            DataTable dt = null;

            try
            {
                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(FilePath.FaultDataLibrary + "配置文件.xlsx", ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //导入参数    
                iRet = GetFaultDataOscilloscopeConfigFromDataTable(dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_GET_CONFIG_FILE, "GetFaultDataOscilloscopeConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetFaultDataOscilloscopeConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.12
        //*************************************************************************
        private int GetFaultDataOscilloscopeConfigFromDataTable(DataTable dt)
        {
            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                SampleChannel1 = OthersHelper.GetCellValueFromDataTable(dt, "FaultDataConfig", "FaultDataConfig", "SelectedSampleChannel1IndexConfig");
                if (SampleChannel1 != "停用")
                {
                    SelectedSamplingChannel1 = SampleChannel1.Substring(0, SampleChannel1.IndexOf("["));
                }

                SampleChannel2 = OthersHelper.GetCellValueFromDataTable(dt, "FaultDataConfig", "FaultDataConfig", "SelectedSampleChannel2IndexConfig");
                if (SampleChannel2 != "停用")
                {
                    SelectedSamplingChannel2 = SampleChannel2.Substring(0, SampleChannel2.IndexOf("["));
                }
                
                SampleChannel3 = OthersHelper.GetCellValueFromDataTable(dt, "FaultDataConfig", "FaultDataConfig", "SelectedSampleChannel3IndexConfig");
                if (SampleChannel3 != "停用")
                {
                    SelectedSamplingChannel3 = SampleChannel3.Substring(0, SampleChannel3.IndexOf("["));
                }
                
                SampleChannel4 = OthersHelper.GetCellValueFromDataTable(dt, "FaultDataConfig", "FaultDataConfig", "SelectedSampleChannel4IndexConfig");
                if (SampleChannel4 != "停用")
                {
                   SelectedSamplingChannel4 = SampleChannel4.Substring(0, SampleChannel4.IndexOf("["));
                }
                
                SampleChannel5 = OthersHelper.GetCellValueFromDataTable(dt, "FaultDataConfig", "FaultDataConfig", "SelectedSampleChannel5IndexConfig");
                if (SampleChannel5 != "停用")
                {
                    SelectedSamplingChannel5 = SampleChannel5.Substring(0, SampleChannel5.IndexOf("["));
                }
                
                SampleChannel6 = OthersHelper.GetCellValueFromDataTable(dt, "FaultDataConfig", "FaultDataConfig", "SelectedSampleChannel6IndexConfig");
                if (SampleChannel6 != "停用")
                {
                    SelectedSamplingChannel6 = SampleChannel6.Substring(0, SampleChannel6.IndexOf("["));
                }
                
                SampleChannel7 = OthersHelper.GetCellValueFromDataTable(dt, "FaultDataConfig", "FaultDataConfig", "SelectedSampleChannel7IndexConfig");
                if (SampleChannel7 != "停用")
                {
                    SelectedSamplingChannel7 = SampleChannel7.Substring(0, SampleChannel7.IndexOf("["));
                }
                
                SampleChannel8 = OthersHelper.GetCellValueFromDataTable(dt, "FaultDataConfig", "FaultDataConfig", "SelectedSampleChannel8IndexConfig");
                if (SampleChannel8 != "停用")
                {
                    SelectedSamplingChannel8 = SampleChannel8.Substring(0, SampleChannel8.IndexOf("["));
                }
                
                SamplingPeriod = OthersHelper.GetCellValueFromDataTable(dt, "FaultDataConfig", "FaultDataConfig", "SelectedSamplingPeriodConfig");

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_GET_AMPLITUDE_CONFIG_FROM_DATATABLE, "GetFaultDataOscilloscopeConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }       

        //*************************************************************************
        //函数名称：GetFaultDataOscilloscopeConfig
        //函数功能：获取故障数据示波器配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.12
        //*************************************************************************
        private int GetFaultDataOscilloscopeConfig()
        {
            try
            {
               
                SampleChannel1 = GetSampleNameByAddress(int.Parse(AlarmCacheChennalSettingOne).ToString("X8").Substring(6, 2));
                if (SampleChannel1 != "停用")
                {
                    SelectedSamplingChannel1 = SampleChannel1.Substring(0, SampleChannel1.IndexOf("["));
                }

                SampleChannel2 = GetSampleNameByAddress(int.Parse(AlarmCacheChennalSettingOne).ToString("X8").Substring(4, 2));
                if (SampleChannel2 != "停用")
                {
                    SelectedSamplingChannel2 = SampleChannel2.Substring(0, SampleChannel2.IndexOf("["));
                }

                SampleChannel3 = GetSampleNameByAddress(int.Parse(AlarmCacheChennalSettingOne).ToString("X8").Substring(2, 2));
                if (SampleChannel3 != "停用")
                {
                    SelectedSamplingChannel3 = SampleChannel3.Substring(0, SampleChannel3.IndexOf("["));
                }

                SampleChannel4 = GetSampleNameByAddress(int.Parse(AlarmCacheChennalSettingOne).ToString("X8").Substring(0, 2));
                if (SampleChannel4 != "停用")
                {
                    SelectedSamplingChannel4 = SampleChannel4.Substring(0, SampleChannel4.IndexOf("["));
                }

                SampleChannel5 = GetSampleNameByAddress(int.Parse(AlarmCacheChennalSettingTwo).ToString("X8").Substring(6, 2));
                if (SampleChannel5 != "停用")
                {
                    SelectedSamplingChannel5 = SampleChannel5.Substring(0, SampleChannel5.IndexOf("["));
                }

                SampleChannel6 = GetSampleNameByAddress(int.Parse(AlarmCacheChennalSettingTwo).ToString("X8").Substring(4, 2));
                if (SampleChannel6 != "停用")
                {
                    SelectedSamplingChannel6 = SampleChannel6.Substring(0, SampleChannel6.IndexOf("["));
                }

                SampleChannel7 = GetSampleNameByAddress(int.Parse(AlarmCacheChennalSettingTwo).ToString("X8").Substring(2, 2));
                if (SampleChannel7 != "停用")
                {
                    SelectedSamplingChannel7 = SampleChannel7.Substring(0, SampleChannel7.IndexOf("["));
                }

                SampleChannel8 = GetSampleNameByAddress(int.Parse(AlarmCacheChennalSettingTwo).ToString("X8").Substring(0, 2));
                if (SampleChannel8 != "停用")
                {
                    SelectedSamplingChannel8 = SampleChannel8.Substring(0, SampleChannel8.IndexOf("["));
                }

                if (int.Parse(AlarmCacheChennalTimeSetting) * 62.5 < 1000)
                {
                    SamplingPeriod = (int.Parse(AlarmCacheChennalTimeSetting) * 62.5).ToString() + "μs";
                }
                else
                {
                    SamplingPeriod = ((int.Parse(AlarmCacheChennalTimeSetting) * 62.5) / 1000).ToString() + "ms";
                }

                //采样时长
                GetSamplingDuration(AlarmCacheChennalTimeSetting);

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.LIMITAMPLITUDE_GET_AMPLITUDE_CONFIG_FROM_DATATABLE, "GetFaultDataOscilloscopeConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetSampleNameByAddress
        //函数功能：通过地址获取采样名称
        //
        //输入参数：string Address  采样地址
        //         
        //输出参数：int Index    采样索引号
        //        
        //编码作者：Lilbert
        //更新时间：2024.03.13
        //*************************************************************************
        private string GetSampleNameByAddress(string Address)
        {
            var query = SampleChannelInfo1_Display.Where(o => o.Address == Address).FirstOrDefault<FaultDataSampleChannelInfoSet>();
            if (query != null)
            {
                return Convert.ToString(query.ItemName);
            }
            else
            {
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GroupSampleChannelInitialize
        //函数功能：ComboBox分组初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        private void GroupSampleChannelInitialize()
        {
            SampleChannelInfo1_Display = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                //new SampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},           //由Lilbert在通道后面添加单位，并去掉示波器标签显示
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},              //由lilbert于2022年8月5日添加位置增量
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo1 = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},                //由lilbert于2022年8月5日添加位置增量
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo2_Display = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo2 = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo3_Display = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo3 = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo4_Display = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo4 = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo5_Display = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo5 = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo6_Display = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo6 = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo7_Display = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo7 = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            SampleChannelInfo8_Display = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量"+"["+Convert.ToString(SelectUnit.Position)+"]",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令"+"["+"‰"+"]",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令"+"["+"‰"+"]",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈"+"["+"‰"+"]",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流"+"["+"mA"+"]",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流"+"["+"mA"+"]",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流"+"["+"mA"+"]",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流"+"["+"mA"+"]",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流"+"["+"mA"+"]",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流"+"["+"mA"+"]",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令"+"["+"mA"+"]",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流"+"["+"mA"+"]",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈"+"["+Convert.ToString(SelectUnit.Speed)+"]",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈"+"["+"‰"+"]",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度"+"["+"Pulse"+"]",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度"+"["+"Pulse"+"]",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值"+"["+"Pulse"+"]",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值"+"["+"Pulse"+"]",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压"+"["+"V"+"]",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流"+"["+"mA"+"]",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位"+"["+"1"+"]",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位"+"["+"1"+"]",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位"+"["+"1"+"]",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字"+"["+"1"+"]",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字"+"["+"1"+"]",Address="23"}
            };

            SampleChannelInfo8 = new List<FaultDataSampleChannelInfoSet>()
            {
                new FaultDataSampleChannelInfoSet() {GroupName="停用 (1)",ID = 0,ItemName="停用",Address="" },
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=1,ItemName="位置指令",Address="03"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=2,ItemName="滤波后位置指令",Address="1A"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=3,ItemName="位置反馈",Address="04"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=4,ItemName="位置差值",Address="05"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=5,ItemName="控制器下发位置指令",Address="24"},
                new FaultDataSampleChannelInfoSet() {GroupName="位置 (5)",ID=6,ItemName="位置增量",Address="25"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=7,ItemName="位置环输出速度指令",Address="06"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=8,ItemName="速度指令",Address="07"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=9,ItemName="前馈后速度指令",Address="1B"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=10,ItemName="速度反馈",Address="08"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=11,ItemName="编码器输出速度",Address="18"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=12,ItemName="观测器输出速度",Address="19"},
                new FaultDataSampleChannelInfoSet() {GroupName="速度 (7)",ID=13,ItemName="速度差值",Address="09"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=14,ItemName="速度环输出转矩指令",Address="0A"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=15,ItemName="转矩指令",Address="0B"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=16,ItemName="转矩反馈",Address="0C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=17,ItemName="U相电流",Address="0D"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=18,ItemName="V相电流",Address="0E"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=19,ItemName="W相电流",Address="0F"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=20,ItemName="D轴指令电流",Address="10"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=21,ItemName="D轴反馈电流",Address="12"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=22,ItemName="Q轴指令电流",Address="11"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=23,ItemName="陷波后Q轴电流指令",Address="1C"},
                new FaultDataSampleChannelInfoSet() {GroupName="转矩 (11)",ID=24,ItemName="Q轴反馈电流",Address="13"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=25,ItemName="速度前馈",Address="16"},
                new FaultDataSampleChannelInfoSet() {GroupName="前馈 (2)",ID=26,ItemName="转矩前馈",Address="17"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=27,ItemName="机械角度",Address="14"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=28,ItemName="电角度",Address="15"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=29,ItemName="单圈编码器值",Address="1D"},
                new FaultDataSampleChannelInfoSet() {GroupName="编码器 (4)",ID=30,ItemName="多圈编码器值",Address="1E"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=31,ItemName="母线电压",Address="01"},
                new FaultDataSampleChannelInfoSet() {GroupName="母线 (2)",ID=32,ItemName="母线电流",Address="02"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=33,ItemName="故障标志位",Address="1F"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=34,ItemName="警告标志位",Address="20"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=35,ItemName="抱闸控制位",Address="21"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=36,ItemName="控制字",Address="22"},
                new FaultDataSampleChannelInfoSet() {GroupName="标志 (5)",ID=37,ItemName="状态字",Address="23"}
            };

            GroupedSampleChannelInfo1 = CollectionViewSource.GetDefaultView(SampleChannelInfo1_Display);     //由Lilbert添加SampleChannelInfo1_Display由于展示
            GroupedSampleChannelInfo1.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo2 = CollectionViewSource.GetDefaultView(SampleChannelInfo2_Display);
            GroupedSampleChannelInfo2.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo3 = CollectionViewSource.GetDefaultView(SampleChannelInfo3_Display);
            GroupedSampleChannelInfo3.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo4 = CollectionViewSource.GetDefaultView(SampleChannelInfo4_Display);
            GroupedSampleChannelInfo4.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo5 = CollectionViewSource.GetDefaultView(SampleChannelInfo5_Display);     //由Lilbert添加SampleChannelInfo1_Display由于展示
            GroupedSampleChannelInfo5.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo6 = CollectionViewSource.GetDefaultView(SampleChannelInfo6_Display);
            GroupedSampleChannelInfo6.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo7 = CollectionViewSource.GetDefaultView(SampleChannelInfo7_Display);
            GroupedSampleChannelInfo7.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo8 = CollectionViewSource.GetDefaultView(SampleChannelInfo8_Display);
            GroupedSampleChannelInfo8.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));
          
        }

        //*************************************************************************
        //函数名称：EventRegisterInitialize
        //函数功能：事件注册初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        private void EventRegisterInitialize()
        {
            if (PthreadStatement.MicrosecondsOscilloscopeDrawing != null)
            {
                PthreadStatement.MicrosecondsOscilloscopeDrawing.evtCheckAllThreadClosed += AllThreadClosedDispose;
            }

            if (PthreadStatement.SerialPortTransmiting != null)
            {
                PthreadStatement.SerialPortTransmiting.evtCheckAllThreadClosed += AllThreadClosedDispose;
            }

            if (ViewModelSet.CommunicationSet != null)
            {
                ViewModelSet.CommunicationSet.evtFaultDataOscilliscopeButtonEnabled += ButtonEnableInitialize;
            }
        }

        //*************************************************************************
        //函数名称：WaveControl_WaveCalculateInitialize
        //函数功能：波形控制与波形计算初始化
        //
        //输入参数：NONE
        //         
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        private void WaveControl_WaveCalculateInitialize()
        {
            //波形控制(倍乘、隐藏）
            OscilloscopeProperty = new ObservableCollection<FaultDataOscilloscopePropertySet>();
            for (int i = 0; i < 8; i++)
            {
                FaultDataOscilloscopePropertySet clsTemp = new FaultDataOscilloscopePropertySet();
                clsTemp.ChannelNumber = "通道-" + (i + 1);
                clsTemp.Doubling = "1";
                clsTemp.IsHidden = false;
                OscilloscopeProperty.Add(clsTemp);
            }

            //波形计算
            OsilloscopeCalculate = new ObservableCollection<FaultDataOsilloscopeCalculateSet>();
            for (int i = 0; i <= 7; i++)
            {
                FaultDataOsilloscopeCalculateSet clsTemp = new FaultDataOsilloscopeCalculateSet();
                switch (i)
                {
                    case 0:
                        clsTemp.Title = "当前光标";
                        break;
                    case 1:
                        clsTemp.Title = "上一个光标";
                        break;
                    case 2:
                        clsTemp.Title = "光标间距";
                        break;
                    case 3:
                        clsTemp.Title = "区间最大值";
                        break;
                    case 4:
                        clsTemp.Title = "区间最小值";
                        break;
                    case 5:
                        clsTemp.Title = "区间峰峰值";
                        break;
                    case 6:
                        clsTemp.Title = "区间平均值";
                        break;
                    case 7:
                        clsTemp.Title = "区间均方根";
                        break;
                    default:
                        break;
                }

                OsilloscopeCalculate.Add(clsTemp);
            }
        }
        #endregion

        #region 前台交互控件状态                   

        //*************************************************************************
        //函数名称：GetSamplingDuration
        //函数功能：更新触发总长
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.11
        //*************************************************************************
        private void GetSamplingDuration(string strLastDuration)
        {

            double dSamplingDuration = 0;
            double dSamplingPeriod = 0;
            string sSamplingPeriod = null;

            dSamplingPeriod = 62.5 * Convert.ToDouble(strLastDuration);
            if (dSamplingPeriod < 1000)
            {
                sSamplingPeriod = dSamplingPeriod.ToString() + "μs";
            }
            else
            {
                sSamplingPeriod = (dSamplingPeriod/1000).ToString() + "ms";
            }

            dSamplingDuration = 1024 * dSamplingPeriod;

            SamplingDuration = (dSamplingDuration/1000).ToString() + "ms";
          
        }

        //*************************************************************************
        //函数名称：MessageForConfirm
        //函数功能：波形信息提示
        //
        //输入参数：NONE
        //         
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        private string MessageForConfirm(string strIndex)
        {
            string strHint_Channel = null;
            string strHint_All = null;
            string strFunction = null;

            switch (strIndex)
            {
                case "Export":
                    strFunction = "是否要保存当前故障数据波形 ：" + "\r\n";
                    break;
                case "Import":
                    strFunction = "是否要载入选中的故障数据波形 ：" + "\r\n";
                    break;
                case "Last":
                    strFunction = "是否要展示最后一次采集的故障数据波形 ：" + "\r\n";
                    break;
                default:
                    break;
            }

            if (SampleChannel1 != "停用")
                strHint_Channel = "通道-1 ：" + SampleChannel1 + "\r\n";

            if (SampleChannel2 != "停用")
                strHint_Channel += "通道-2 ：" + SampleChannel2 + "\r\n";

            if (SampleChannel3 != "停用")
                strHint_Channel += "通道-3 ：" + SampleChannel3 + "\r\n";

            if (SampleChannel4 != "停用")
                strHint_Channel += "通道-4 ：" + SampleChannel4 + "\r\n";

            if (SampleChannel5 != "停用")
                strHint_Channel += "通道-5 ：" + SampleChannel5 + "\r\n";

            if (SampleChannel6 != "停用")
                strHint_Channel += "通道-6 ：" + SampleChannel6 + "\r\n";

            if (SampleChannel7 != "停用")
                strHint_Channel += "通道-7 ：" + SampleChannel7 + "\r\n";

            if (SampleChannel8 != "停用")
                strHint_Channel += "通道-8 ：" + SampleChannel8 + "\r\n";

            strHint_All = strFunction + strHint_Channel +
                          "采样周期 ：" + SamplingPeriod + "\r\n" +
                          "采样时长 ：" + SamplingDuration + "\r\n";

            return strHint_All;
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        private void ShowNotification_CrossThread(int In_iType)
        {
            WindowSet.clsMainWindow?.ShowNotification(In_iType);
        }
        #endregion

        #region 参数赋值(文件导入导出)
        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            #region 示波器
            //SamplingPeriod = GlobalCurrentInput.SelectedSamplingPeriod_Fault;
            //SamplingDuration = GlobalCurrentInput.SelectedSamplingDuration_Fault;

            //SampleChannel1 = GlobalCurrentInput.SelectedSampleChannel1Index_Fault;
            //SampleChannel2 = GlobalCurrentInput.SelectedSampleChannel2Index_Fault;
            //SampleChannel3 = GlobalCurrentInput.SelectedSampleChannel3Index_Fault;
            //SampleChannel4 = GlobalCurrentInput.SelectedSampleChannel4Index_Fault;
            //SampleChannel5 = GlobalCurrentInput.SelectedSampleChannel5Index_Fault;
            //SampleChannel6 = GlobalCurrentInput.SelectedSampleChannel6Index_Fault;
            //SampleChannel7 = GlobalCurrentInput.SelectedSampleChannel7Index_Fault;
            //SampleChannel8 = GlobalCurrentInput.SelectedSampleChannel8Index_Fault;
           
            OscilloscopeProperty.Clear();
            for (int j = 0; j < 8; j++)
            {
                FaultDataOscilloscopePropertySet clsTemp = new FaultDataOscilloscopePropertySet();
                clsTemp.ChannelNumber = "通道-" + (j + 1);
                clsTemp.IsHidden = false;
                if (!string.IsNullOrEmpty(GlobalCurrentInput.DoublingChannel_Fault[j]) && OthersHelper.IsInputNumber(GlobalCurrentInput.DoublingChannel_Fault[j]))
                {
                    clsTemp.Doubling = GlobalCurrentInput.DoublingChannel_Fault[j];
                }
                else
                {
                    clsTemp.Doubling = "1";
                }

                OscilloscopeProperty.Add(clsTemp);
            }

            #endregion
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalFile
        //函数功能：赋值接口写入离线文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        private void InterfaceEvaluationToFile()
        {
            OfflineFaultAcquisition.Export = new FaultAcquisitionParameterSet();
            OfflineFaultAcquisition.Export.Channel1Name = SampleChannel1;
            OfflineFaultAcquisition.Export.Channel2Name = SampleChannel2;
            OfflineFaultAcquisition.Export.Channel3Name = SampleChannel3;
            OfflineFaultAcquisition.Export.Channel4Name = SampleChannel4;
            OfflineFaultAcquisition.Export.Channel5Name = SampleChannel5;
            OfflineFaultAcquisition.Export.Channel6Name = SampleChannel6;
            OfflineFaultAcquisition.Export.Channel7Name = SampleChannel7;
            OfflineFaultAcquisition.Export.Channel8Name = SampleChannel8;

            OfflineFaultAcquisition.Export.Period = SamplingPeriod;
            OfflineFaultAcquisition.Export.Duration = SamplingDuration;
            OfflineFaultAcquisition.Export.Date = DateTime.Now.ToString();

            if (OscilloscopeProperty != null)
            {
                if (OscilloscopeProperty.Count == 8)
                {
                    OfflineFaultAcquisition.Export.Channel1Doubling = OscilloscopeProperty[0].Doubling;
                    OfflineFaultAcquisition.Export.Channel2Doubling = OscilloscopeProperty[1].Doubling;
                    OfflineFaultAcquisition.Export.Channel3Doubling = OscilloscopeProperty[2].Doubling;
                    OfflineFaultAcquisition.Export.Channel4Doubling = OscilloscopeProperty[3].Doubling;
                    OfflineFaultAcquisition.Export.Channel5Doubling = OscilloscopeProperty[4].Doubling;
                    OfflineFaultAcquisition.Export.Channel6Doubling = OscilloscopeProperty[5].Doubling;
                    OfflineFaultAcquisition.Export.Channel7Doubling = OscilloscopeProperty[6].Doubling;
                    OfflineFaultAcquisition.Export.Channel8Doubling = OscilloscopeProperty[7].Doubling;
                }
                else
                {
                    OfflineFaultAcquisition.Export.Channel1Doubling = "1";
                    OfflineFaultAcquisition.Export.Channel2Doubling = "1";
                    OfflineFaultAcquisition.Export.Channel3Doubling = "1";
                    OfflineFaultAcquisition.Export.Channel4Doubling = "1";
                    OfflineFaultAcquisition.Export.Channel5Doubling = "1";
                    OfflineFaultAcquisition.Export.Channel6Doubling = "1";
                    OfflineFaultAcquisition.Export.Channel7Doubling = "1";
                    OfflineFaultAcquisition.Export.Channel8Doubling = "1";
                }
            }
            else
            {
                OfflineFaultAcquisition.Export.Channel1Doubling = "1";
                OfflineFaultAcquisition.Export.Channel2Doubling = "1";
                OfflineFaultAcquisition.Export.Channel3Doubling = "1";
                OfflineFaultAcquisition.Export.Channel4Doubling = "1";
                OfflineFaultAcquisition.Export.Channel5Doubling = "1";
                OfflineFaultAcquisition.Export.Channel6Doubling = "1";
                OfflineFaultAcquisition.Export.Channel7Doubling = "1";
                OfflineFaultAcquisition.Export.Channel8Doubling = "1";
            }

            OfflineFaultAcquisition.Export.lstChannel1 = new List<int>();
            OfflineFaultAcquisition.Export.lstChannel2 = new List<int>();
            OfflineFaultAcquisition.Export.lstChannel3 = new List<int>();
            OfflineFaultAcquisition.Export.lstChannel4 = new List<int>();
            OfflineFaultAcquisition.Export.lstChannel5 = new List<int>();
            OfflineFaultAcquisition.Export.lstChannel6 = new List<int>();
            OfflineFaultAcquisition.Export.lstChannel7 = new List<int>();
            OfflineFaultAcquisition.Export.lstChannel8 = new List<int>();

            FaultAcquisitionData.Channel1?.ForEach(item => OfflineFaultAcquisition.Export.lstChannel1.Add(item));
            FaultAcquisitionData.Channel2?.ForEach(item => OfflineFaultAcquisition.Export.lstChannel2.Add(item));
            FaultAcquisitionData.Channel3?.ForEach(item => OfflineFaultAcquisition.Export.lstChannel3.Add(item));
            FaultAcquisitionData.Channel4?.ForEach(item => OfflineFaultAcquisition.Export.lstChannel4.Add(item));
            FaultAcquisitionData.Channel5?.ForEach(item => OfflineFaultAcquisition.Export.lstChannel5.Add(item));
            FaultAcquisitionData.Channel6?.ForEach(item => OfflineFaultAcquisition.Export.lstChannel6.Add(item));
            FaultAcquisitionData.Channel7?.ForEach(item => OfflineFaultAcquisition.Export.lstChannel7.Add(item));
            FaultAcquisitionData.Channel8?.ForEach(item => OfflineFaultAcquisition.Export.lstChannel8.Add(item));

            OfflineFaultAcquisition.Export.lstUnit = new List<string>();
            OfflineFaultAcquisition.Export.lstExchangeValue = new List<double>();

            FaultAcquisitionInfoSet.lstUnit?.ForEach(item => OfflineFaultAcquisition.Export.lstUnit.Add(item));
            FaultAcquisitionInfoSet.lstExchangeValue?.ForEach(item => OfflineFaultAcquisition.Export.lstExchangeValue.Add(item));
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalFile
        //函数功能：从离线文件赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.10
        //*************************************************************************
        private void InterfaceEvaluationFromFile()
        {
            try
            {
                SamplingPeriod = OfflineFaultAcquisition.Import.Period;

                SampleChannel1 = OfflineFaultAcquisition.Import.Channel1Name;
                SampleChannel2 = OfflineFaultAcquisition.Import.Channel2Name;
                SampleChannel3 = OfflineFaultAcquisition.Import.Channel3Name;
                SampleChannel4 = OfflineFaultAcquisition.Import.Channel4Name;
                SampleChannel5 = OfflineFaultAcquisition.Import.Channel5Name;
                SampleChannel6 = OfflineFaultAcquisition.Import.Channel6Name;
                SampleChannel7 = OfflineFaultAcquisition.Import.Channel7Name;
                SampleChannel8 = OfflineFaultAcquisition.Import.Channel8Name;

                SamplingDuration = OfflineFaultAcquisition.Import.Duration;
                SelectedDisplayMethod = "连续采样-静态展示";
           
                OscilloscopeProperty.Clear();
                for (int j = 0; j < 8; j++)
                {
                    FaultDataOscilloscopePropertySet clsTemp = new FaultDataOscilloscopePropertySet();
                    clsTemp.ChannelNumber = "通道-" + (j + 1);

                    switch (j)
                    {
                        case 0:
                            clsTemp.Doubling = OfflineFaultAcquisition.Import.Channel1Doubling;
                            break;
                        case 1:
                            clsTemp.Doubling = OfflineFaultAcquisition.Import.Channel2Doubling;
                            break;
                        case 2:
                            clsTemp.Doubling = OfflineFaultAcquisition.Import.Channel3Doubling;
                            break;
                        case 3:
                            clsTemp.Doubling = OfflineFaultAcquisition.Import.Channel4Doubling;
                            break;
                        case 4:
                            clsTemp.Doubling = OfflineFaultAcquisition.Import.Channel5Doubling;
                            break;
                        case 5:
                            clsTemp.Doubling = OfflineFaultAcquisition.Import.Channel6Doubling;
                            break;
                        case 6:
                            clsTemp.Doubling = OfflineFaultAcquisition.Import.Channel7Doubling;
                            break;
                        case 7:
                            clsTemp.Doubling = OfflineFaultAcquisition.Import.Channel8Doubling;
                            break;
                        default:
                            break;
                    }

                    clsTemp.IsHidden = false;
                    OscilloscopeProperty.Add(clsTemp);
                }

                FaultAcquisitionData.Channel1 = new List<int>();
                FaultAcquisitionData.Channel2 = new List<int>();
                FaultAcquisitionData.Channel3 = new List<int>();
                FaultAcquisitionData.Channel4 = new List<int>();
                FaultAcquisitionData.Channel5 = new List<int>();
                FaultAcquisitionData.Channel6 = new List<int>();
                FaultAcquisitionData.Channel7 = new List<int>();
                FaultAcquisitionData.Channel8 = new List<int>();

                OfflineFaultAcquisition.Import.lstChannel1?.ForEach(item => FaultAcquisitionData.Channel1.Add(item));
                OfflineFaultAcquisition.Import.lstChannel2?.ForEach(item => FaultAcquisitionData.Channel2.Add(item));
                OfflineFaultAcquisition.Import.lstChannel3?.ForEach(item => FaultAcquisitionData.Channel3.Add(item));
                OfflineFaultAcquisition.Import.lstChannel4?.ForEach(item => FaultAcquisitionData.Channel4.Add(item));
                OfflineFaultAcquisition.Import.lstChannel5?.ForEach(item => FaultAcquisitionData.Channel5.Add(item));
                OfflineFaultAcquisition.Import.lstChannel6?.ForEach(item => FaultAcquisitionData.Channel6.Add(item));
                OfflineFaultAcquisition.Import.lstChannel7?.ForEach(item => FaultAcquisitionData.Channel7.Add(item));
                OfflineFaultAcquisition.Import.lstChannel8?.ForEach(item => FaultAcquisitionData.Channel8.Add(item));

                FaultAcquisitionInfoSet.lstUnit = new List<string>();
                FaultAcquisitionInfoSet.lstExchangeValue = new List<double>();

                OfflineFaultAcquisition.Import.lstUnit.ForEach(item => FaultAcquisitionInfoSet.lstUnit.Add(item));
                OfflineFaultAcquisition.Import.lstExchangeValue.ForEach(item => FaultAcquisitionInfoSet.lstExchangeValue.Add(item));
            
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_INTERFACE_EVALUATION_FROM_FILE, "InterfaceEvaluationFromFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromLast
        //函数功能：载入上一条参数数据
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.10
        //*************************************************************************
        private int InterfaceEvaluationFromLast()
        {
            try
            {
                if (OfflineFaultAcquisition.Last == null)
                {
                    return RET.NO_EFFECT;
                }

                //如果上一次采集终止，将不会载入波形
                if (string.IsNullOrEmpty(OfflineFaultAcquisition.Last.Period) || string.IsNullOrEmpty(OfflineFaultAcquisition.Last.Duration) || string.IsNullOrEmpty(OfflineFaultAcquisition.Last.Channel1Name))
                {
                    return RET.NO_EFFECT;
                }

                //判断倍乘输入是否有效
                for (int i = 0; i < OscilloscopeProperty.Count; i++)
                {
                    if (!OthersHelper.IsInputNumber(OscilloscopeProperty[i].Doubling))
                    {
                        OscilloscopeProperty[i].Doubling = "1";
                    }
                }

                SamplingPeriod = OfflineFaultAcquisition.Last.Period;
                SamplingDuration = OfflineFaultAcquisition.Last.Duration;

                SampleChannel1 = OfflineFaultAcquisition.Last.Channel1Name;
                SampleChannel2 = OfflineFaultAcquisition.Last.Channel2Name;
                SampleChannel3 = OfflineFaultAcquisition.Last.Channel3Name;
                SampleChannel4 = OfflineFaultAcquisition.Last.Channel4Name;
                SampleChannel5 = OfflineFaultAcquisition.Last.Channel5Name;
                SampleChannel6 = OfflineFaultAcquisition.Last.Channel6Name;
                SampleChannel7 = OfflineFaultAcquisition.Last.Channel7Name;
                SampleChannel8 = OfflineFaultAcquisition.Last.Channel8Name;

                SelectedDisplayMethod = "连续采样-静态展示";

                FaultAcquisitionData.Channel1 = new List<int>();
                FaultAcquisitionData.Channel2 = new List<int>();
                FaultAcquisitionData.Channel3 = new List<int>();
                FaultAcquisitionData.Channel4 = new List<int>();
                FaultAcquisitionData.Channel5 = new List<int>();
                FaultAcquisitionData.Channel6 = new List<int>();
                FaultAcquisitionData.Channel7 = new List<int>();
                FaultAcquisitionData.Channel8 = new List<int>();

                OfflineFaultAcquisition.Last.lstChannel1?.ForEach(item => FaultAcquisitionData.Channel1.Add(item));
                OfflineFaultAcquisition.Last.lstChannel2?.ForEach(item => FaultAcquisitionData.Channel2.Add(item));
                OfflineFaultAcquisition.Last.lstChannel3?.ForEach(item => FaultAcquisitionData.Channel3.Add(item));
                OfflineFaultAcquisition.Last.lstChannel4?.ForEach(item => FaultAcquisitionData.Channel4.Add(item));
                OfflineFaultAcquisition.Last.lstChannel5?.ForEach(item => FaultAcquisitionData.Channel5.Add(item));
                OfflineFaultAcquisition.Last.lstChannel6?.ForEach(item => FaultAcquisitionData.Channel6.Add(item));
                OfflineFaultAcquisition.Last.lstChannel7?.ForEach(item => FaultAcquisitionData.Channel7.Add(item));
                OfflineFaultAcquisition.Last.lstChannel8?.ForEach(item => FaultAcquisitionData.Channel8.Add(item));

                FaultAcquisitionInfoSet.lstUnit = new List<string>();
                FaultAcquisitionInfoSet.lstExchangeValue = new List<double>();

                OfflineFaultAcquisition.Last.lstUnit?.ForEach(item => FaultAcquisitionInfoSet.lstUnit.Add(item));
                OfflineFaultAcquisition.Last.lstExchangeValue?.ForEach(item => FaultAcquisitionInfoSet.lstExchangeValue.Add(item));

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_INTERFACE_EVALUATION_FROM_LAST, "InterfaceEvaluationFromLast", ex);
                return RET.ERROR;
            }
        }
        #endregion

        #region 数据采集内部方法
        //*************************************************************************
        //函数名称：RefreshAcquisitionList
        //函数功能：更新采样List
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        private void RefreshAcquisitionList()
        {
            string strUnit = null;
            FaultAcquisitionInfoSet.lstChannel = new List<string>();
            FaultAcquisitionInfoSet.lstReceiving = new List<List<int>>();
            FaultAcquisitionInfoSet.lstUnit = new List<string>();
            FaultAcquisitionInfoSet.lstExchangeValue = new List<double>();

            if (SelectedSamplingChannel1 != "停用")
            {
                FaultAcquisitionInfoSet.lstChannel.Add("通道-1");
                FaultAcquisitionInfoSet.lstReceiving.Add(new List<int>());

                SoftwareStateParameterSet.dicAcquisitionUint.TryGetValue(ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel1, out strUnit);
                if (!string.IsNullOrEmpty(strUnit))
                {
                    FaultAcquisitionInfoSet.lstUnit.Add(strUnit);
                    FaultAcquisitionInfoSet.lstExchangeValue.Add(OthersHelper.GetExchangeValueByCurrentUnit_FaultAcquisition(true, strUnit));
                }
            }

            if (SelectedSamplingChannel2 != "停用")
            {
                FaultAcquisitionInfoSet.lstChannel.Add("通道-2");
                FaultAcquisitionInfoSet.lstReceiving.Add(new List<int>());

                SoftwareStateParameterSet.dicAcquisitionUint.TryGetValue(ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel2, out strUnit);
                if (!string.IsNullOrEmpty(strUnit))
                {
                    FaultAcquisitionInfoSet.lstUnit.Add(strUnit);
                    FaultAcquisitionInfoSet.lstExchangeValue.Add(OthersHelper.GetExchangeValueByCurrentUnit_FaultAcquisition(true, strUnit));
                }
            }

            if (SelectedSamplingChannel3 != "停用")
            {
                FaultAcquisitionInfoSet.lstChannel.Add("通道-3");
                FaultAcquisitionInfoSet.lstReceiving.Add(new List<int>());

                SoftwareStateParameterSet.dicAcquisitionUint.TryGetValue(ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel3, out strUnit);
                if (!string.IsNullOrEmpty(strUnit))
                {
                    FaultAcquisitionInfoSet.lstUnit.Add(strUnit);
                    FaultAcquisitionInfoSet.lstExchangeValue.Add(OthersHelper.GetExchangeValueByCurrentUnit_FaultAcquisition(true, strUnit));
                }
            }

            if (SelectedSamplingChannel4 != "停用")
            {
                FaultAcquisitionInfoSet.lstChannel.Add("通道-4");
                FaultAcquisitionInfoSet.lstReceiving.Add(new List<int>());

                SoftwareStateParameterSet.dicAcquisitionUint.TryGetValue(ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel4, out strUnit);
                if (!string.IsNullOrEmpty(strUnit))
                {
                    FaultAcquisitionInfoSet.lstUnit.Add(strUnit);
                    FaultAcquisitionInfoSet.lstExchangeValue.Add(OthersHelper.GetExchangeValueByCurrentUnit_FaultAcquisition(true, strUnit));
                }
            }

            if (SelectedSamplingChannel5 != "停用")
            {
                FaultAcquisitionInfoSet.lstChannel.Add("通道-5");
                FaultAcquisitionInfoSet.lstReceiving.Add(new List<int>());

                SoftwareStateParameterSet.dicAcquisitionUint.TryGetValue(ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel5, out strUnit);
                if (!string.IsNullOrEmpty(strUnit))
                {
                    FaultAcquisitionInfoSet.lstUnit.Add(strUnit);
                    FaultAcquisitionInfoSet.lstExchangeValue.Add(OthersHelper.GetExchangeValueByCurrentUnit_FaultAcquisition(true, strUnit));
                }
            }

            if (SelectedSamplingChannel6 != "停用")
            {
                FaultAcquisitionInfoSet.lstChannel.Add("通道-6");
                FaultAcquisitionInfoSet.lstReceiving.Add(new List<int>());

                SoftwareStateParameterSet.dicAcquisitionUint.TryGetValue(ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel6, out strUnit);
                if (!string.IsNullOrEmpty(strUnit))
                {
                    FaultAcquisitionInfoSet.lstUnit.Add(strUnit);
                    FaultAcquisitionInfoSet.lstExchangeValue.Add(OthersHelper.GetExchangeValueByCurrentUnit_FaultAcquisition(true, strUnit));
                }
            }

            if (SelectedSamplingChannel7 != "停用")
            {
                FaultAcquisitionInfoSet.lstChannel.Add("通道-7");
                FaultAcquisitionInfoSet.lstReceiving.Add(new List<int>());

                SoftwareStateParameterSet.dicAcquisitionUint.TryGetValue(ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel7, out strUnit);
                if (!string.IsNullOrEmpty(strUnit))
                {
                    FaultAcquisitionInfoSet.lstUnit.Add(strUnit);
                    FaultAcquisitionInfoSet.lstExchangeValue.Add(OthersHelper.GetExchangeValueByCurrentUnit_FaultAcquisition(true, strUnit));
                }
            }

            if (SelectedSamplingChannel8 != "停用")
            {
                FaultAcquisitionInfoSet.lstChannel.Add("通道-8");
                FaultAcquisitionInfoSet.lstReceiving.Add(new List<int>());

                SoftwareStateParameterSet.dicAcquisitionUint.TryGetValue(ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel8, out strUnit);
                if (!string.IsNullOrEmpty(strUnit))
                {
                    FaultAcquisitionInfoSet.lstUnit.Add(strUnit);
                    FaultAcquisitionInfoSet.lstExchangeValue.Add(OthersHelper.GetExchangeValueByCurrentUnit_FaultAcquisition(true, strUnit));
                }
            }
        }

        //*************************************************************************
        //函数名称：GetTransmittingContent
        //函数功能：获取发送报文信息
        //
        //输入参数：ref List<TransmitingDataInfoSet> lstTransmittingDataInfo    报文
        //         
        //输出参数：0：NO_EFFECT
        //         1: SUCCEED
        //        -1: ERROR
        //       100: INVALID_INPUT
        //       101: INVALID_LENGTH_OF_INPUT
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        private int GetTransmittingContent(ref List<TransmitingDataInfoSet> lstTransmittingDataInfo)
        {
            byte[] bChannelNumber = new byte[1];//通道个数
            short sSamplingPeriod = 0;//采样周期
            int iSampingDuration = 0;//采样时间
            short sSampingLength = 0;//采样总长
            byte[] bTriggerMode = new byte[1];//触发模式
            byte[] bTriggerChannel = new byte[1];//触发通道

            string strChannelNumber = null;
            string strSamplingPeriod = null;
            string strSamplingLength = null;
            string strChannel = null;
            string strMessage = null;

            lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取通道个数
                if (SelectedSamplingChannel1 != "停用") bChannelNumber[0]++;
                if (SelectedSamplingChannel2 != "停用") bChannelNumber[0]++;
                if (SelectedSamplingChannel3 != "停用") bChannelNumber[0]++;
                if (SelectedSamplingChannel4 != "停用") bChannelNumber[0]++;
                if (SelectedSamplingChannel5 != "停用") bChannelNumber[0]++;
                if (SelectedSamplingChannel6 != "停用") bChannelNumber[0]++;
                if (SelectedSamplingChannel7 != "停用") bChannelNumber[0]++;
                if (SelectedSamplingChannel8 != "停用") bChannelNumber[0]++;
                if (bChannelNumber[0] == 0)
                {
                    return RET.ERROR;
                }

                strChannel = GetSampleAdrressByName(SelectedSamplingChannel1) +
                                        GetSampleAdrressByName(SelectedSamplingChannel2) +
                                        GetSampleAdrressByName(SelectedSamplingChannel3) +
                                        GetSampleAdrressByName(SelectedSamplingChannel4) +
                                        GetSampleAdrressByName(SelectedSamplingChannel5) +
                                        GetSampleAdrressByName(SelectedSamplingChannel6) +
                                        GetSampleAdrressByName(SelectedSamplingChannel7) +
                                        GetSampleAdrressByName(SelectedSamplingChannel8);

                iSampingDuration = Convert.ToInt32(Convert.ToDouble(SamplingDuration.Replace("ms", "")) * 1000);

                //采样周期与采样点个数
                if (SamplingPeriod.IndexOf("μ") != -1)
                {
                    sSamplingPeriod = Convert.ToInt16(Convert.ToDouble(SamplingPeriod.Replace("μs", "")) / 62.5);
                    sSampingLength = Convert.ToInt16(iSampingDuration / (Convert.ToDouble(SamplingPeriod.Replace("μs", ""))));
                }
                else
                {
                    sSamplingPeriod = Convert.ToInt16(Convert.ToDouble(SamplingPeriod.Replace("ms", "")) * 1000 / 62.5);
                    sSampingLength = Convert.ToInt16(iSampingDuration / ((Convert.ToDouble(SamplingPeriod.Replace("ms", ""))) * 1000));
                }

                //10进制转换16进制字符串
                strChannelNumber = BitConverter.ToString(bChannelNumber).Replace("-", "");
                strSamplingPeriod = BitConverter.ToString(BitConverter.GetBytes(sSamplingPeriod)).Replace("-", "");
                strSamplingLength = BitConverter.ToString(BitConverter.GetBytes(sSampingLength)).Replace("-", "");
               
                strMessage = strChannelNumber + strSamplingPeriod + strSamplingLength + strChannel;
                lstTransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = strMessage });

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_GET_TRANSMITING_CONTENT, "GetTransmittingContent-TriggerLevel: ", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：RecordLastSamplingParameter
        //函数功能：上一个采样参数记录
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        private void RecordLastSamplingParameter()
        {
            OfflineFaultAcquisition.Last.Period = SamplingPeriod;

            OfflineFaultAcquisition.Last.Channel1Name = SampleChannel1;
            OfflineFaultAcquisition.Last.Channel2Name = SampleChannel2;
            OfflineFaultAcquisition.Last.Channel3Name = SampleChannel3;
            OfflineFaultAcquisition.Last.Channel4Name = SampleChannel4;
            OfflineFaultAcquisition.Last.Channel5Name = SampleChannel5;
            OfflineFaultAcquisition.Last.Channel6Name = SampleChannel6;
            OfflineFaultAcquisition.Last.Channel7Name = SampleChannel7;
            OfflineFaultAcquisition.Last.Channel8Name = SampleChannel8;

            OfflineFaultAcquisition.Last.Duration = SamplingDuration;

            for (int i = 0; i < 8; i++)
            {
                GlobalCurrentInput.DoublingChannel_Fault[i] = OscilloscopeProperty[i].Doubling;
            }
        }

        //*************************************************************************
        //函数名称：ResetLastWavaCalculate
        //函数功能：上一条波形计算信息重置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        private void ResetLastWavaCalculate()
        {
            dLastTime = 0;//获取上一次X坐标-时间
            iIndex = 0;//获取上一个检索号
            dLastCH1Value = 0;//获取上一次CH1值
            dLastCH2Value = 0;//获取上一次CH2值
            dLastCH3Value = 0;//获取上一次CH3值
            dLastCH4Value = 0;//获取上一次CH4值
            dLastCH5Value = 0;//获取上一次CH5值
            dLastCH6Value = 0;//获取上一次CH6值
            dLastCH7Value = 0;//获取上一次CH7值
            dLastCH8Value = 0;//获取上一次CH8值
        }

        //*************************************************************************
        //函数名称：GetSampleAddressByIndex
        //函数功能：通过索引号获取采样地址
        //
        //输入参数：int Index   索引号
        //         
        //输出参数：string Name  采样地址
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        private string GetSampleAddressByIndex(int Index)
        {
            var query = SampleChannelInfo1.Where(o => o.ID == Index).FirstOrDefault<FaultDataSampleChannelInfoSet>();
            if (query != null)
            {
                return Convert.ToString(query.Address);
            }
            else
            {
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetSampleNameByIndex
        //函数功能：通过索引号获取采样名称
        //
        //输入参数：int Index   索引号
        //         
        //输出参数：string Name  采样名称
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        private string GetSampleNameByIndex(int Index)
        {
            var query = SampleChannelInfo1.Where(o => o.ID == Index).FirstOrDefault<FaultDataSampleChannelInfoSet>();
            if (query != null)
            {
                return Convert.ToString(query.ItemName);
            }
            else
            {
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetSampleIndexByName
        //函数功能：通过名称获取采样索引号
        //
        //输入参数：string Name  采样名称
        //         
        //输出参数：int Index    采样索引号
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        private int GetSampleIndexByName(string Name)
        {
            var query = SampleChannelInfo1.Where(o => o.ItemName == Name).FirstOrDefault<FaultDataSampleChannelInfoSet>();
            if (query != null)
            {
                return Convert.ToInt32(query.ID);
            }
            else
            {
                return 0;
            }
        }

        //*************************************************************************
        //函数名称：GetSampleIndexByName
        //函数功能：通过名称获取采样地址
        //
        //输入参数：string Name  采样名称
        //         
        //输出参数：int Index    采样索引号
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        private string GetSampleAdrressByName(string Name)
        {
            var query = SampleChannelInfo1_Display.Where(o => o.ItemName == Name).FirstOrDefault<FaultDataSampleChannelInfoSet>();
            if (query != null)
            {
                return Convert.ToString(query.Address);
            }
            else
            {
                return null;
            }
        }
        #endregion

        #region 其他
        //*************************************************************************
        //函数名称：AllThreadClosedDispose
        //函数功能：所有线程全部关闭
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.10
        //*************************************************************************
        private void AllThreadClosedDispose()
        {
            List<TransmitingDataInfoSet> lstTransmitingDataInfoSet = new List<TransmitingDataInfoSet>();

            //清空所有采样任务
            OthersHelper.ClearAllFaultAcquisitionTask();

            //状态栏提示
            ViewModelSet.Main?.RefreshSerialPortWorkState(FaultAcquisitionInfoSet.CurrentProcess);

            //按钮状态更改
            OthersButtonEnabled = true;

            //是否故障数据
            //SoftwareStateParameterSet.IsStopAcquisitionFault = true;

            //画图已经完成
            FaultAcquisitionInfoSet.IsDrawingCompleted = true;

            //连续采样次数
            FaultAcquisitionInfoSet.ContinuousAcquisitionTimes = 0;

            //停止采样任务 
            if (SoftwareStateParameterSet.IsClearAcquisitionFault)
            {
                //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_StopAcquisitionFault(null, TaskName.ClearAcquisitionFault, lstTransmitingDataInfoSet);
                SoftwareStateParameterSet.IsClearAcquisitionFault = false;
            }
            else
            {
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_StopAcquisitionFault(null, TaskName.StopAcquisitionFault, lstTransmitingDataInfoSet);
            }
            //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_StopAcquisitionFault(null, TaskName.StopAcquisitionFault, lstTransmitingDataInfoSet);
        }
      
        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.02&2021.07.05&2022.11.09
        //*************************************************************************
        private int AddParameterInfoDictionary(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                switch (strCategory)
                {
                    case "0":
                        break;
                    case "1":
                        dicParameterInfo.Add("Position Loop Gain", PositionLoopGain);
                        dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);//负载惯量比     由Lilbert增加负载惯量比
                        dicParameterInfo.Add("First Trqcmd Filter Time", FirstTrqcmdFilterTime);//第一转矩滤波时间     由Lilbert增加第一转矩滤波时间
                        dicParameterInfo.Add("Speed Loop Gain", SpeedLoopGain);
                        dicParameterInfo.Add("Speed Loop Time Constant", SpeedLoopTimeConstant);
                        dicParameterInfo.Add("Current Loop Gain", CurrentLoopGain);
                        dicParameterInfo.Add("Current Loop Time Constant", CurrentLoopTimeConstant);

                        //故障数据配置参数
                        dicParameterInfo.Add("Alarm Cache chennal 1 setting", AlarmCacheChennalSettingOne);//故障数据配置参数1
                        dicParameterInfo.Add("Alarm Cache chennal 2 setting", AlarmCacheChennalSettingTwo);//故障数据配置参数2
                        dicParameterInfo.Add("Alarm Cache chennal Time setting", AlarmCacheChennalTimeSetting);//故障数据配置通道时间
                        break;
                    
                    default:
                        dicParameterInfo.Add("Position Loop Gain", PositionLoopGain);
                        dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);//负载惯量比     由Lilbert增加负载惯量比
                        dicParameterInfo.Add("First Trqcmd Filter Time", FirstTrqcmdFilterTime);//第一转矩滤波时间     由Lilbert增加第一转矩滤波时间
                        dicParameterInfo.Add("Speed Loop Gain", SpeedLoopGain);
                        dicParameterInfo.Add("Speed Loop Time Constant", SpeedLoopTimeConstant);
                        dicParameterInfo.Add("Current Loop Gain", CurrentLoopGain);
                        dicParameterInfo.Add("Current Loop Time Constant", CurrentLoopTimeConstant);

                        //故障数据配置参数
                        dicParameterInfo.Add("Alarm Cache chennal 1 setting", AlarmCacheChennalSettingOne);//故障数据配置参数1
                        dicParameterInfo.Add("Alarm Cache chennal 2 setting", AlarmCacheChennalSettingTwo);//故障数据配置参数2
                        dicParameterInfo.Add("Alarm Cache chennal Time setting", AlarmCacheChennalTimeSetting);//故障数据配置通道时间
                        break;
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：DefaultUnitDictionaryInitialize
        //函数功能：默认单位集合
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.07
        //*************************************************************************
        private void DefaultUnitDictionaryInitialize()
        {
            dicDefaultUnit = new Dictionary<string, string>();
            dicDefaultUnit.Add("位置指令", "cnt");
            dicDefaultUnit.Add("位置反馈", "cnt");
            dicDefaultUnit.Add("位置差值", "cnt");
            dicDefaultUnit.Add("滤波后位置指令", "cnt");
            dicDefaultUnit.Add("位置增量", "cnt");//由lilbert于2022年8月5日添加位置增量
            dicDefaultUnit.Add("位置环输出速度指令", "cnt/s");
            dicDefaultUnit.Add("速度指令", "cnt/s");
            dicDefaultUnit.Add("速度反馈", "cnt/s");
            dicDefaultUnit.Add("速度差值", "cnt/s");
            dicDefaultUnit.Add("速度前馈", "cnt/s");
            dicDefaultUnit.Add("编码器输出速度", "cnt/s");
            dicDefaultUnit.Add("观测器输出速度", "cnt/s");
            dicDefaultUnit.Add("前馈后速度指令", "cnt/s");

        }

        #endregion

    }

    public class FaultDataOscilloscopePropertySet
    {
        public string ChannelNumber { get; set; }//通道编号
        public string Doubling { get; set; }//倍乘系数
        public bool IsHidden { get; set; }//波形是否隐藏
    }

    public class FaultDataOsilloscopeCalculateSet
    {
        public string Title { get; set; }
        public string Number { get; set; }
        public string AcquisitionTime { get; set; }
        public string Channel1Value { get; set; }
        public string Channel2Value { get; set; }
        public string Channel3Value { get; set; }
        public string Channel4Value { get; set; }
        public string Channel5Value { get; set; }
        public string Channel6Value { get; set; }
        public string Channel7Value { get; set; }
        public string Channel8Value { get; set; }
    }

    public class FaultDataSampleChannelInfoSet
    {
        public string GroupName { get; set; }
        public int ID { get; set; }
        public string ItemName { get; set; }
        public string Address { get; set; }
    }
}