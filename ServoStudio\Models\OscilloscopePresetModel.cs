﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ServoStudio.Models
{
    public class OscilloscopePresetModel
    {
        public string Id { get; set; }//Id号
        public int SelectedSampleChannel1Index { get; set; }//采样通道1索引号
        public int SelectedSampleChannel2Index { get; set; }//采样通道2索引号
        public int SelectedSampleChannel3Index { get; set; }//采样通道3索引号
        public int SelectedSampleChannel4Index { get; set; }//采样通道4索引号
        public string SamplingPeriod { get; set; }//采样周期
        public string SamplingDuration { get; set; }//采样时长
        public string ContinuousSampling { get; set; }//连续采样
        public string TriggerClockEdge { get; set; }//触发模式
        public string TriggerChannel { get; set; }//触发通道
        public string PreTrigger { get; set; }//预先触发
        public string TriggerLevel { get; set; }//触发水平
    }
}
