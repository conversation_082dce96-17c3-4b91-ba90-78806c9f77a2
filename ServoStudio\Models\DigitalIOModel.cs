﻿using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace ServoStudio.Models
{
    public static class DigitalIOModel
    {
        //*************************************************************************
        //函数名称：MakeValueToDataTable
        //函数功能：生成DataTable数据表
        //
        //输入参数：string In_ReportName     报表内的名称
        //         string In_InterfaceName  接口名称
        //         string In_PropertyName   属性名称
        //         DataTable Out_dt         数据表
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2019.11.14
        //*************************************************************************
        public static void MakeValueToDataTable(string In_ReportName, string In_InterfaceName, string In_PropertyName, ref DataTable Out_dt)
        {
            try
            {
                if (Out_dt == null)
                {
                    Out_dt = new DataTable();
                }

                if (!Out_dt.Columns.Contains("IOInterface"))
                {
                    Out_dt.Columns.Clear();
                    Out_dt.Columns.Add("ID", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add("Index", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add("Name", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add("IOInterface", System.Type.GetType("System.String"));
                    Out_dt.Columns.Add("Default", System.Type.GetType("System.String"));
                }

                DataRow row = Out_dt.NewRow();
                row["ID"] = Convert.ToString(Out_dt.Rows.Count + 1);
                row["Name"] = In_ReportName;
                row["IOInterface"] = In_InterfaceName;
                row["Default"] = In_PropertyName;
                row["Index"] = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", In_ReportName, "Index");

                Out_dt.Rows.Add(row);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.DIGITALIO_MAKE_VALUE_TO_DATATABLE, "DigitalIOModel.MakeValueToDataTable", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReflectIndexToItem
        //函数功能：用过索引号映射名称
        //
        //输入参数：string strKinds  类型
        //         string strIndex  索引号
        //       
        //输出参数：string strItem   索引号对应的名称
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.04&2023.01.05
        //*************************************************************************
        public static string ReflectIndexToItem(string strKinds, string strIndex)
        {            
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                if (string.IsNullOrEmpty(strKinds) || string.IsNullOrEmpty(strIndex))
                {
                    return "";
                }

                if (strKinds == IOOperation.FUNCTION_DI)
                {
                    switch (strIndex)
                    {
                        case FunctionSelectIndex.NO_CONFIG:
                            return "无配置";
                        case FunctionSelectIndex.NEGATIVE_LIMIT:
                            return "负限位";
                        case FunctionSelectIndex.POSITIVE_LIMIT:
                            return "正限位";                        
                        case FunctionSelectIndex.ORIGINAL_SWITCH:
                            return "回零";
                        case FunctionSelectIndex.DISABLED:
                            return "禁能";
                        case FunctionSelectIndex.ENABLED:
                            return "使能";
                        case FunctionSelectIndex.EMERGENCY_STOP:
                            return "使能";
                        case FunctionSelectIndex.CLEAR_FAULT:
                            return "故障清除";
                        //case FunctionSelectIndex.MOTOR_OVER_TEMPERATURE:
                        //    return "电机过温";
                        //case FunctionSelectIndex.START:
                        //    return "启动";
                        //case FunctionSelectIndex.NORMAL_STOP:
                        //    return "正常停止";
                        //case FunctionSelectIndex.QUICK_STOP:
                        //    return "快速停止";
                        //case FunctionSelectIndex.POSITIVE_ACTION:
                        //    return "正向点动";
                        //case FunctionSelectIndex.NEGATIVE_ACTION:
                        //    return "反向点动";
                        //case FunctionSelectIndex.CLEAR_FAULT:
                        //    return "清除故障";
                        //case FunctionSelectIndex.RESET:
                        //    return "重置";
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.FUNCTION_DO)
                {
                    switch (strIndex)
                    {
                        //case FunctionSelectIndex.GENERAL_DO:
                        //    return "通用DO";
                        case FunctionSelectIndex.NO_CONFIG_DO:
                            return "无配置";
                        case FunctionSelectIndex.GPIO_DO:
                            return "GPIO";
                        case FunctionSelectIndex.FAULT:
                            return "故障";
                        case FunctionSelectIndex.BRAKE:
                            return "抱闸";
                        case FunctionSelectIndex.REACH_POSITION:
                            return "位置到达";
                        case FunctionSelectIndex.ECAT_BUS_CONTROL:
                            return "Ecat总线控制";
                        //case FunctionSelectIndex.REACH_TARGET:
                        //    return "目标到达";
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.LOGIC_DI)
                {
                    switch (strIndex)
                    {
                        case LogicSelectIndex.LOW:
                            return "低电平有效";
                        case LogicSelectIndex.HIGH:
                            return "高电平有效";
                        case LogicSelectIndex.RISING_EDGE:
                            return "上升沿有效";
                        case LogicSelectIndex.FALLING_EDGE:
                            return "下降沿有效";
                        case LogicSelectIndex.DOUBLE_EDGE:
                            return "双边沿有效";
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.LOGIC_DO)
                {
                    switch (strIndex)
                    {
                        case LogicSelectIndex.LOW_IF_VALID:
                            return "有效时输出低电平";
                        case LogicSelectIndex.HIGN_IF_VALID:
                            return "有效时输出高电平";
                        default:
                            return "";
                    }
                }
                else
                {
                    return "";
                }
            }
            else
            {
                if (string.IsNullOrEmpty(strKinds) || string.IsNullOrEmpty(strIndex))
                {
                    return "";
                }

                if (strKinds == IOOperation.FUNCTION_DI)
                {
                    switch (strIndex)
                    {
                        case FunctionSelectIndex.NO_CONFIG:
                            return "NoConfig";
                        case FunctionSelectIndex.NEGATIVE_LIMIT:
                            return "NegativeLimit";
                        case FunctionSelectIndex.POSITIVE_LIMIT:
                            return "PositiveLimit";                       
                        case FunctionSelectIndex.ORIGINAL_SWITCH:
                            return "ZeroReturnSwitch";
                        case FunctionSelectIndex.DISABLED:
                            return "Disabled";
                        case FunctionSelectIndex.ENABLED:
                            return "Enabled";
                        case FunctionSelectIndex.EMERGENCY_STOP:
                            return "EmergencyStop";
                        case FunctionSelectIndex.CLEAR_FAULT:
                            return "ClearFault";
                        //case FunctionSelectIndex.MOTOR_OVER_TEMPERATURE:
                        //    return "MotorOverTemperature";
                        //case FunctionSelectIndex.START:
                        //    return "StartUp";
                        //case FunctionSelectIndex.NORMAL_STOP:
                        //    return "NormalStop";
                        //case FunctionSelectIndex.QUICK_STOP:
                        //    return "QuickStop";
                        //case FunctionSelectIndex.POSITIVE_ACTION:
                        //    return "PositiveInching";
                        //case FunctionSelectIndex.NEGATIVE_ACTION:
                        //    return "ReverseInching";
                        //case FunctionSelectIndex.CLEAR_FAULT:
                        //    return "ClearFault";
                        //case FunctionSelectIndex.RESET:
                        //    return "Reset";
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.FUNCTION_DO)
                {
                    switch (strIndex)
                    {
                        case FunctionSelectIndex.NO_CONFIG_DO:
                            return "NoConfig";
                        case FunctionSelectIndex.GPIO_DO:
                            return "GPIO";
                        //case FunctionSelectIndex.GENERAL_DO:
                        //    return "GeneralDO";
                        case FunctionSelectIndex.FAULT:
                            return "Fault";
                        case FunctionSelectIndex.BRAKE:
                            return "BandBrake";
                        case FunctionSelectIndex.REACH_POSITION:
                            return "PositionArrival";
                        case FunctionSelectIndex.ECAT_BUS_CONTROL:
                            return "EcatBusControl";
                        //case FunctionSelectIndex.REACH_TARGET:
                        //    return "TargetArrival";
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.LOGIC_DI)
                {
                    switch (strIndex)
                    {
                        case LogicSelectIndex.LOW:
                            return "ActiveLow";
                        case LogicSelectIndex.HIGH:
                            return "ActiveHigh";
                        case LogicSelectIndex.RISING_EDGE:
                            return "ActiveRisingEdge";
                        case LogicSelectIndex.FALLING_EDGE:
                            return "ActiveFallingEdge";
                        case LogicSelectIndex.DOUBLE_EDGE:
                            return "ActiveDoubleEdge";
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.LOGIC_DO)
                {
                    switch (strIndex)
                    {
                        case LogicSelectIndex.LOW_IF_VALID:
                            return "Output low level when valid";
                        case LogicSelectIndex.HIGN_IF_VALID:
                            return "Output high level when valid";
                        default:
                            return "";
                    }
                }
                else
                {
                    return "";
                }
            }
        }

        //*************************************************************************
        //函数名称：ReflectItemToIndex
        //函数功能：用名称映射索引号
        //
        //输入参数：string strKinds  类型
        //         string strIndex  名称
        //       
        //输出参数：string strItem   名称对应的索引号
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.04&2023.01.05
        //*************************************************************************
        public static string ReflectItemToIndex(string strKinds, string strItem)
        {
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                if (string.IsNullOrEmpty(strKinds) || string.IsNullOrEmpty(strItem))
                {
                    return "";
                }

                if (strKinds == IOOperation.FUNCTION_DI)
                {
                    switch (strItem)
                    {
                        case "无配置":
                            return FunctionSelectIndex.NO_CONFIG;
                        case "负限位":
                            return FunctionSelectIndex.NEGATIVE_LIMIT;
                        case "正限位":
                            return FunctionSelectIndex.POSITIVE_LIMIT;                        
                        case "回零":
                            return FunctionSelectIndex.ORIGINAL_SWITCH;
                        case "禁能":
                            return FunctionSelectIndex.DISABLED;
                        case "使能":
                            return FunctionSelectIndex.ENABLED;
                        case "急停":
                            return FunctionSelectIndex.EMERGENCY_STOP;
                        case "故障清除":
                            return FunctionSelectIndex.CLEAR_FAULT;
                        //case "电机过温":
                        //    return FunctionSelectIndex.MOTOR_OVER_TEMPERATURE;
                        //case "启动":
                        //    return FunctionSelectIndex.START;
                        //case "正常停止":
                        //    return FunctionSelectIndex.NORMAL_STOP;
                        //case "快速停止":
                        //    return FunctionSelectIndex.QUICK_STOP;
                        //case "正向点动":
                        //    return FunctionSelectIndex.POSITIVE_ACTION;
                        //case "反向点动":
                        //    return FunctionSelectIndex.NEGATIVE_ACTION;
                        //case "清除故障":
                        //    return FunctionSelectIndex.CLEAR_FAULT;
                        //case "重置":
                        //    return FunctionSelectIndex.RESET;
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.FUNCTION_DO)
                {
                    switch (strItem)
                    {
                        case "无配置":
                            return FunctionSelectIndex.NO_CONFIG_DO;
                        case "GPIO":
                            return FunctionSelectIndex.GPIO_DO;
                        //case "通用DO":
                        //    return FunctionSelectIndex.GENERAL_DO;
                        case "故障":
                            return FunctionSelectIndex.FAULT;
                        case "抱闸":
                            return FunctionSelectIndex.BRAKE;
                        case "位置到达":
                            return FunctionSelectIndex.REACH_POSITION;
                        case "Ecat总线控制":
                            return FunctionSelectIndex.ECAT_BUS_CONTROL;
                        //case "目标到达":
                        //    return FunctionSelectIndex.REACH_TARGET;
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.LOGIC_DI)
                {
                    switch (strItem)
                    {
                        case "低电平有效":
                            return LogicSelectIndex.LOW;
                        case "高电平有效":
                            return LogicSelectIndex.HIGH;
                        case "上升沿有效":
                            return LogicSelectIndex.RISING_EDGE;
                        case "下降沿有效":
                            return LogicSelectIndex.FALLING_EDGE;
                        case "双边沿有效":
                            return LogicSelectIndex.DOUBLE_EDGE;
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.LOGIC_DO)
                {
                    switch (strItem)
                    {
                        case "有效时输出低电平":
                            return LogicSelectIndex.LOW_IF_VALID;
                        case "有效时输出高电平":
                            return LogicSelectIndex.HIGN_IF_VALID;
                        default:
                            return "";
                    }
                }
                else
                {
                    return "";
                }
            }
            else
            {
                if (string.IsNullOrEmpty(strKinds) || string.IsNullOrEmpty(strItem))
                {
                    return "";
                }

                if (strKinds == IOOperation.FUNCTION_DI)
                {
                    switch (strItem)
                    {
                        case "NoCofig":
                            return FunctionSelectIndex.NO_CONFIG;
                        case "NegativeLimit":
                            return FunctionSelectIndex.NEGATIVE_LIMIT;
                        case "PositiveLimit":
                            return FunctionSelectIndex.POSITIVE_LIMIT;                        
                        case "ZeroReturnSwitch":
                            return FunctionSelectIndex.ORIGINAL_SWITCH;
                        case "Disabled":
                            return FunctionSelectIndex.DISABLED;
                        case "Enabled":
                            return FunctionSelectIndex.ENABLED;
                        case "EmergencyStop":
                            return FunctionSelectIndex.EMERGENCY_STOP;
                        case "ClearFault":
                            return FunctionSelectIndex.CLEAR_FAULT;
                        //case "MotorOverTemperature":                        
                        //    return FunctionSelectIndex.MOTOR_OVER_TEMPERATURE;
                        //case "StartUp":
                        //    return FunctionSelectIndex.START;
                        //case "NormalStop":
                        //    return FunctionSelectIndex.NORMAL_STOP;
                        //case "QuickStop":
                        //    return FunctionSelectIndex.QUICK_STOP;
                        //case "PositiveInching":
                        //    return FunctionSelectIndex.POSITIVE_ACTION;
                        //case "ReverseInching":
                        //    return FunctionSelectIndex.NEGATIVE_ACTION;
                        //case "ClearFault":
                        //    return FunctionSelectIndex.CLEAR_FAULT;
                        //case "Reset":
                        //    return FunctionSelectIndex.RESET;
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.FUNCTION_DO)
                {
                    switch (strItem)
                    {
                        case "NoCofig":
                            return FunctionSelectIndex.NO_CONFIG_DO;
                        case "GPIO":
                            return FunctionSelectIndex.GPIO_DO;
                        //case "GeneralDO":
                        //    return FunctionSelectIndex.GENERAL_DO;
                        case "Fault":
                            return FunctionSelectIndex.FAULT;
                        case "BandBrake":
                            return FunctionSelectIndex.BRAKE;
                        case "PositionArrival":
                            return FunctionSelectIndex.REACH_POSITION;
                        case "EcatBusControl":
                            return FunctionSelectIndex.ECAT_BUS_CONTROL;
                        //case "TargetArrival":
                        //    return FunctionSelectIndex.REACH_TARGET;
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.LOGIC_DI)
                {
                    switch (strItem)
                    {
                        case "ActiveLow":
                            return LogicSelectIndex.LOW;
                        case "ActiveHigh":
                            return LogicSelectIndex.HIGH;
                        case "ActiveRisingEdge":
                            return LogicSelectIndex.RISING_EDGE;
                        case "ActiveFallingEdge":
                            return LogicSelectIndex.FALLING_EDGE;
                        case "ActiveDoubleEdge":
                            return LogicSelectIndex.DOUBLE_EDGE;
                        default:
                            return "";
                    }
                }
                else if (strKinds == IOOperation.LOGIC_DO)
                {
                    switch (strItem)
                    {
                        case "Output low level when valid":
                            return LogicSelectIndex.LOW_IF_VALID;
                        case "Output high level when valid":
                            return LogicSelectIndex.HIGN_IF_VALID;
                        default:
                            return "";
                    }
                }
                else
                {
                    return "";
                }
            }
        }
    }
}
