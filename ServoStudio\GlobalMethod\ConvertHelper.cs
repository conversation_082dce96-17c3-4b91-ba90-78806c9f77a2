﻿using ServoStudio.GlobalConstant;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;

namespace ServoStudio.GlobalMethod
{
    public static class ConvertHelper
    {
        //*************************************************************************
        //函数名称：DataTableToList
        //函数功能：DataTable转换
        //
        //输入参数：DataTable dt      
        //         ref List<T> list
        //
        //输出参数：1:OK
        //         0:No_Effect
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2019.10.28
        //*************************************************************************
        public static int DataTableToList<T>(DataTable dt, ref List<T> list) where T : new()
        {
            int iRet = -1;
            list = new List<T>();

            try
            {
                if (dt == null)
                {
                    return RET.NO_EFFECT;
                }
                else if (dt.Rows.Count < 0)
                {
                    return RET.NO_EFFECT;
                }

                foreach (DataRow row in dt.Rows)
                {
                    DataRow myRow = row;//获取当前行 

                    T t = new T();
                    foreach (PropertyInfo item in t.GetType().GetProperties())//遍历公共属性  
                    {
                        string tempName = item.Name;
                        if (dt.Columns.Contains(tempName))
                        {
                            if (!item.CanWrite)//该属性不可写，直接跳出 
                            {
                                continue;
                            }

                            if (!item.PropertyType.IsGenericType)//判断是否是泛型
                            {
                                item.SetValue(t, Convert.ChangeType(myRow[tempName], item.PropertyType), null);
                            }
                            else
                            {
                                item.SetValue(t, Convert.ChangeType(myRow[tempName], Nullable.GetUnderlyingType(item.PropertyType)), null);
                            }
                        }
                    }

                    list.Add(t);
                }

                iRet = RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                iRet = RET.ERROR;
                SoftwareErrorHelper.CatchDispose(ERROR.CONVERTHELPER_DATASET_TO_LIST, "DataTableToList", ex);
            }

            return iRet;        
        }

        //*************************************************************************
        //函数名称：DataTableToObservableCollection
        //函数功能：DataTable转换
        //
        //输入参数：DataTable dt      
        //         ref ObservableCollection<T> obs
        //
        //输出参数：1:OK
        //         0:No_Effect
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2019.10.28
        //*************************************************************************
        public static int DataTableToObservableCollection<T>(DataTable dt, ref ObservableCollection<T> obs) where T : new()
        {
            int iRet = -1;
            obs = new ObservableCollection<T>();

            try
            {
                if (dt == null)
                {
                    return RET.NO_EFFECT;
                }
                else if (dt.Rows.Count <= 0)
                {
                    return RET.NO_EFFECT;
                }

                foreach (DataRow row in dt.Rows)
                {
                    DataRow myRow = row;//获取当前行 

                    T t = new T();
                    foreach (PropertyInfo item in t.GetType().GetProperties())//遍历公共属性  
                    {
                        string tempName = item.Name;
                        if (dt.Columns.Contains(tempName))
                        {
                            if (!item.CanWrite)//该属性不可写，直接跳出 
                            {
                                continue;
                            }

                            if (!item.PropertyType.IsGenericType)//判断是否是泛型
                            {
                                item.SetValue(t, Convert.ChangeType(myRow[tempName], item.PropertyType), null);
                            }
                            else
                            {
                                item.SetValue(t, Convert.ChangeType(myRow[tempName], Nullable.GetUnderlyingType(item.PropertyType)), null);
                            }
                        }
                    }

                    obs.Add(t);
                }

                iRet = RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                iRet = RET.ERROR;
                SoftwareErrorHelper.CatchDispose(ERROR.CONVERTHELPER_DATASET_TO_OBSERVABLECOLLECTION, "DataTableTooObservableCollection", ex);
            }

            return iRet;
        }

        //*************************************************************************
        //函数名称：DataTableToObservableCollection
        //函数功能：DataTable转换
        //
        //输入参数：DataTable dt      
        //         ref ObservableCollection<T> obs
        //
        //输出参数：1:OK
        //         0:No_Effect
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public static int DataTableToObservableCollection_For_Compare<T>(DataTable dt, ref ObservableCollection<T> obs) where T : new()
        {
            int iRet = -1;
            obs = new ObservableCollection<T>();

            try
            {
                if (dt == null)
                {
                    return RET.NO_EFFECT;
                }
                else if (dt.Rows.Count <= 0)
                {
                    return RET.NO_EFFECT;
                }

                foreach (DataRow row in dt.Rows)
                {
                    DataRow myRow = row;//获取当前行 

                    T t = new T();
                    foreach (PropertyInfo item in t.GetType().GetProperties())//遍历公共属性  
                    {
                        string tempName = item.Name;
                        if (dt.Columns.Contains(tempName))
                        {
                            if (!item.CanWrite)//该属性不可写，直接跳出 
                            {
                                continue;
                            }

                            if (!item.PropertyType.IsGenericType)//判断是否是泛型
                            {
                                item.SetValue(t, Convert.ChangeType(myRow[tempName], item.PropertyType), null);
                            }
                            else
                            {
                                item.SetValue(t, Convert.ChangeType(myRow[tempName], Nullable.GetUnderlyingType(item.PropertyType)), null);
                            }
                        }
                    }

                    obs.Add(t);
                }

                iRet = RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                iRet = RET.ERROR;
                SoftwareErrorHelper.CatchDispose(ERROR.CONVERTHELPER_DATASET_TO_OBSERVABLECOLLECTION, "DataTableTooObservableCollection", ex);
            }

            return iRet;
        }

        //*************************************************************************
        //函数名称：ChangeInt
        //函数功能：将数字String转换为整型
        //
        //输入参数：sVal      
        //         
        //
        //输出参数：iRet
        //         
        //       
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.24
        //*************************************************************************
        public static int ChangeInt(this string sVal)
        {
            int iRet;
            try
            {
                iRet = Convert.ToInt32(sVal);
            }
            catch (Exception)
            {
                iRet = 0;
            }
            return iRet;
        }
        //*************************************************************************
        //函数名称：ChangeInt
        //函数功能：将object转为整型
        //
        //输入参数：sVal      
        //         
        //
        //输出参数：iRet
        //         
        //       
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.26
        //*************************************************************************
        public static int ChangeInt(this object sVal)
        {
            int iRet;
            try
            {
                iRet = Convert.ToInt32(sVal);
            }
            catch (Exception)
            {
                iRet = 0;
            }
            return iRet;
        }
    }
}
