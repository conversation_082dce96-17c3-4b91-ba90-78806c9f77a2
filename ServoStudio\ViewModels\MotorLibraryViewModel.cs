﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using System.Collections.ObjectModel;
using System.ComponentModel;
using ServoStudio;
using System.Data;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Collections.Generic;
using ServoStudio.Models;
using System.IO;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class MotorLibraryViewModel
    {
        #region 字段
        private bool IsFileExist;
        private DataTable dtMotorLibaray;
        private ObservableCollection<MotorLibrarySet> obsMotorLibrary;//参数监控集合   
        #endregion

        #region 属性
        public virtual ObservableCollection<MotorLibrarySet> MotorLibrary { get; set; }//参数监控集合   
        public virtual bool ButtonEnabled { get; set; }//按钮使能
        #endregion

        #region 服务
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        [ServiceProperty(Key = "MotorLibraryDetails")]
        protected virtual IDialogService DialogService_MotorLibraryDetails { get { return this.GetService<IDialogService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 构造函数
        public MotorLibraryViewModel()
        {
            ViewModelSet.MotorLibrary = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.MOTORLIBRARY;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：MotorLibraryLoaded
        //函数功能：载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.26
        //*************************************************************************
        public void MotorLibraryLoaded()
        {
            int iRet = -1;
            MotorLibrary = new ObservableCollection<MotorLibrarySet>();

            iRet = ExcelHelper.ReadFromExcel(FilePath.MotorLibraryLog, ref dtMotorLibaray);
            if (iRet == RET.ERROR)
            {
                ShowNotification(RET.ERROR);
                return;
            }
            else
            {
                if (dtMotorLibaray.Rows.Count == 0)
                {
                    return;
                } 
            }

            iRet = ConvertHelper.DataTableToObservableCollection(dtMotorLibaray, ref obsMotorLibrary);
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(RET.ERROR);
                return;
            }

            foreach (var item in obsMotorLibrary)
            {
                if (Convert.ToBoolean(item.Valid))
                {
                    MotorLibrary.Add(item);
                }               
            }
        }

        //*************************************************************************
        //函数名称：CommunicationSetNavigation
        //函数功能：通信配置导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.01
        //*************************************************************************
        public void CommunicationSetNavigation()
        {
            //SoftwareStateParameterSet.CurrentPageName = PageName.COMMUNICATIONSET;
            //ViewModelSet.Main?.CommunicationSetNavigation();
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.COMMUNICATIONSET;
                ViewModelSet.Main?.CommunicationSetNavigation();
            }
        }

        //*************************************************************************
        //函数名称：MotorFeedbackNavigation
        //函数功能：电机反馈界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.01
        //*************************************************************************
        public void MotorFeedbackNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.MOTORFEEDBACK;
                NavigationService.Navigate("MotorFeedbackView", null, ViewModelSet.Main);
            }
        }

        ////*************************************************************************
        ////函数名称：MotorFeedbackNavigation
        ////函数功能：电机反馈导航
        ////
        ////输入参数：None
        ////         
        ////输出参数：None
        ////        
        ////编码作者：Ryan
        ////更新时间：2020.07.24
        ////*************************************************************************
        //public void MotorFeedbackNavigation()
        //{
        //    if (ViewModelSet.Main != null)
        //    {
        //        SoftwareStateParameterSet.CurrentPageName = PageName.MOTORFEEDBACK;
        //        NavigationService.Navigate("MotorFeedbackView", null, ViewModelSet.Main);
        //    }
        //}

        //*************************************************************************
        //函数名称：CheckIsMultipleChoice
        //函数功能：判断是否多选
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.26
        //*************************************************************************
        public void CheckIsMultipleChoice()
        {                       
            int choicedTimes = 0;
            foreach (var item in MotorLibrary)
            {
                if (item.IsChoiced == true)
                {
                    choicedTimes++;
                    MotorLibraryDetailSet.FileName = item.Name.Trim();
                    MotorLibraryDetailSet.Author = item.Author;
                    MotorLibraryDetailSet.Comment = item.Comment;
                    MotorLibraryDetailSet.MotorType = item.MotorType;
                    MotorLibraryDetailSet.EncoderType = item.EncoderType;
                }
            }

            if (choicedTimes == 0)
            {
                ButtonEnabled = false;
            }
            else if (choicedTimes == 1)
            {
                ButtonEnabled = true;
            }
            else
            {
                ButtonEnabled = false;
                ShowNotification(2032);
            }         
        }

        //*************************************************************************
        //函数名称：MotorLibraryDetails
        //函数功能：电机参数表详细
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.26
        //*************************************************************************
        public void MotorLibraryDetails(string Action)
        {
            bool bEEPROM = false;

            try
            {
                if (ViewModelSet.MotorLibraryDetails == null)
                {
                    ViewModelSet.MotorLibraryDetails = new MotorLibraryDetailsViewModel();
                }

                OthersHelper.GetWindowsStartupPosition();
                MotorLibraryDetailSet.Action = Action;               
                IsFileExist = File.Exists(FilePath.MotorLibrary + MotorLibraryDetailSet.FileName + ".xlsx");

                UICommand registerCommand = new UICommand() { Caption = "确认", IsCancel = false, };
                UICommand cancelCommand = new UICommand() { Caption = "返回", IsCancel = true, };
                            
                if (Action == "Select")
                {
                    if (IsFileExist)
                    {
                        UICommand result = DialogService_MotorLibraryDetails.ShowDialog(new List<UICommand>() { cancelCommand }, "查看电机参数表", ViewModelSet.MotorLibraryDetails);
                    }
                    else
                    {
                        ShowNotification(2006);
                    }                   
                }
                else if (Action == "Insert")
                {
                    UICommand result = DialogService_MotorLibraryDetails.ShowDialog(new List<UICommand>() { registerCommand, cancelCommand }, "添加电机参数表", ViewModelSet.MotorLibraryDetails);               
                    if (registerCommand == result)
                    {
                        ViewModelSet.MotorLibraryDetails?.AddMotorLibrary();
                        MotorLibraryLoaded();
                    }
                }
                else if (Action == "Update")
                {
                    if (IsFileExist)
                    {
                        UICommand result = DialogService_MotorLibraryDetails.ShowDialog(new List<UICommand>() { registerCommand, cancelCommand }, "修改电机参数表", ViewModelSet.MotorLibraryDetails);
                        if (registerCommand == result)
                        {
                            ViewModelSet.MotorLibraryDetails?.ModifyMotorLibrary();
                            MotorLibraryLoaded();
                        }
                    }
                    else
                    {
                        ShowNotification(2006);
                    }                                 
                }
                else if (Action == "Delete")
                {
                    if (IsFileExist)
                    {
                        if (MessageBoxService.ShowMessage("是否删除选中的参数表...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                        {
                            ViewModelSet.MotorLibraryDetails.MotorLibraryDetailsLoaded();
                            ViewModelSet.MotorLibraryDetails?.DeleteMotorLibrary();
                            MotorLibraryLoaded();
                        }                    
                    }
                    else
                    {
                        ShowNotification(2006);
                    }
                }
                else
                {
                    if (IsFileExist)
                    {
                        if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                        {
                            bEEPROM = true;
                        }

                        ViewModelSet.MotorLibraryDetails.MotorLibraryDetailsLoaded();
                        ViewModelSet.MotorLibraryDetails?.DownloadMotorLibrary(bEEPROM);
                    }
                    else
                    {
                        ShowNotification(2006);
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_MOTOR_LIBRARY_DETAILS, "MotorLibraryDetails", ex);
            }
        }
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowHintInfo(string strInfo)
        {
            ViewModelSet.Main?.ShowHintInfo(strInfo);
        }
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }


    public class MotorLibrarySet
    {
        public bool IsChoiced { get; set; }//是否被选中
        public string Name { get; set; }//名称
        public string MotorType { get; set; }//电机类型
        public string EncoderType { get; set; }//编码器类型
        public string Author { get; set; }//作者
        public string DateTime { get; set; }//更新时间
        public string Comment { get; set; }//备注
        public bool Valid { get; set; }//是否有效
    }
}