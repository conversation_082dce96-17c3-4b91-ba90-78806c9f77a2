﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:ServoStudio.Language"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!--向导-->
    <system:String x:Key="Caption_Wizard">向导</system:String>
    <system:String x:Key="Caption_SetupWizard">设置向导</system:String>

    <system:String x:Key="BarButtonItem_CommunicationSet">通信配置</system:String>
    <system:String x:Key="BarButtonItem_MotorFeedback">电机反馈</system:String>
    <system:String x:Key="BarButtonItem_MotorFeedbackAutoLearn">电机参数辨识</system:String>
    <system:String x:Key="BarButtonItem_MotorParameterIdentification_AddInterface">电机寻相</system:String>
    <system:String x:Key="BarButtonItem_JogDriectionDebug">Jog调试</system:String>
    <system:String x:Key="BarButtonItem_InertiaIdentificationParameterSelf_Tunning">参数自学习</system:String>

    <!--配置-->
    <system:String x:Key="Caption_Config">配置</system:String>
    <system:String x:Key="Caption_SystemConfig">系统配置</system:String>

    <system:String x:Key="BarButtonItem_UnitSet">单位设置</system:String>
    <system:String x:Key="BarButtonItem_LimitAmplitude">限幅保护</system:String>
    <system:String x:Key="BarButtonItem_NormalSetting">一般设定</system:String>
    <system:String x:Key="BarButtonItem_DigitalIO">数字IO</system:String>

    <system:String x:Key="Caption_ThreeLoopConfig">三环配置</system:String>

    <system:String x:Key="BarButtonItem_CurrentLoop">电流环</system:String>
    <system:String x:Key="BarButtonItem_SpeedLoop">速度环</system:String>
    <system:String x:Key="BarButtonItem_PositionLoop">位置环</system:String>

    <!--调试-->
    <system:String x:Key="Caption_Debug">调试</system:String>
    <system:String x:Key="Caption_ManualDebug">手动调试</system:String>

    <system:String x:Key="BarButtonItem_Oscilloscope">示波器应用</system:String>
    <system:String x:Key="BarButtonItem_ThreeLoop">三环调试</system:String>
    <system:String x:Key="BarButtonItem_FunctionGenerator">函数发生器</system:String>
    <system:String x:Key="BarButtonItem_Action">运动调试</system:String>
    <system:String x:Key="BarButtonItem_ParameterTunning">参数调优</system:String>

    <!--参数-->
    <system:String x:Key="Caption_Parameter">参数</system:String>
    <system:String x:Key="Caption_ParameterOnLine">参数在线</system:String>

    <system:String x:Key="BarButtonItem_ParameterReadWrite">参数读写</system:String>
    <system:String x:Key="BarButtonItem_RefreshParameterReadAndWrite">实时刷新</system:String>

    <system:String x:Key="Caption_ParameterFactory">参数出厂</system:String>

    <system:String x:Key="BarButtonItem_FactoryReset">恢复出厂值</system:String>

    <system:String x:Key="Caption_ImportExport">导入导出</system:String>

    <system:String x:Key="BarButtonItem_ImportConfigFile">参数导入</system:String>
    <system:String x:Key="BarButtonItem_ExportConfigFile">参数导出</system:String>

    <system:String x:Key="Caption_EEPROM">EEPROM</system:String>

    <system:String x:Key="BarButtonItem_SaveRAMtoEEPROM">参数保存到&#x0a;&#160;EEPROM</system:String>
    <system:String x:Key="BarButtonItem_ReadEEPROMtoRAM">从EEPROM&#x0a;&#160;&#160;读取参数</system:String>

    <system:String x:Key="Caption_ParameterMonitoring">参数监控</system:String>

    <system:String x:Key="BarButtonItem_ParameterMonitor">监控设置</system:String>
    <system:String x:Key="BarButtonItem_ExportMoniotor">监控保存</system:String>

    <system:String x:Key="Caption_ParameterLibrary">参数库</system:String>

    <system:String x:Key="BarButtonItem_MotorLibrary">电机参数</system:String>

    <!--帮助-->
    <system:String x:Key="Caption_Help">帮助</system:String>
    <system:String x:Key="Caption_SoftwareMaintenance">软件维护</system:String>

    <system:String x:Key="BarButtonItem_SystemRestart">&#160;软件重启&#160;</system:String>

    <system:String x:Key="Caption_HardwareMaintenance">硬件维护</system:String>

    <system:String x:Key="BarButtonItem_FirmwareUpdate">&#160;固件升级&#160;</system:String>

    <system:String x:Key="Caption_DeviceInformation">设备信息</system:String>

    <system:String x:Key="BarButtonItem_GetEditionInfo">&#160;版本校验&#160;</system:String>

    <system:String x:Key="Caption_Instructions">使用说明</system:String>

    <system:String x:Key="BarButtonItem_OpenManualBook">&#160;用户手册&#160;</system:String>
    <system:String x:Key="BarButtonItem_OpenServoMaintenanceManualBook">&#160;伺服维护手册&#160;</system:String>

    <system:String x:Key="Caption_PathSetting">路径设置</system:String>

    <system:String x:Key="BarButtonItem_ParameterPath">&#160;参数路径&#160;</system:String>

    <system:String x:Key="Caption_PasswordSetting">密码设置</system:String>

    <system:String x:Key="BarButtonItem_ModifyPassword">&#160;权限密码&#160;</system:String>

    <system:String x:Key="Caption_LanguageSwitch">语言切换</system:String>

    <system:String x:Key="BarButtonItem_LanguageSwitch">&#160;语言切换&#160;</system:String>

    <!--报警-->
    <system:String x:Key="Caption_Alarm">报警</system:String>
    <system:String x:Key="Caption_AlarmHistory">报警历史</system:String>

    <system:String x:Key="BarButtonItem_HardwareAlarmHistory">&#160;历史查询&#160;</system:String>
    <system:String x:Key="BarButtonItem_DeleteElectronicErrorHistory">&#160;历史清除&#160;</system:String>

    <system:String x:Key="Caption_AlarmMode">报警方式</system:String>

    <system:String x:Key="BarButtonItem_SidebarAlarm">侧边栏报警</system:String>

    <system:String x:Key="Caption_AlarmAnalysis">报警分析</system:String>

    <system:String x:Key="BarButtonItem_AlarmReason">&#160;报警原因&#160;</system:String>

    <!--出厂-->
    <system:String x:Key="Caption_Factory">出厂</system:String>
    <system:String x:Key="Caption_OnekeyFactory">一键出厂</system:String>

    <system:String x:Key="BarButtonItem_AllFactoryReset">&#160;全部初始&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllSystemReset">&#160;全部重启&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllConfigStop">&#160;配置急停&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllAxisAction">&#160;全部运动&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllServoEnabled">&#160;全部使能&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllAxisStop">&#160;全部停止&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllServoDisabled">&#160;全部禁能&#160;</system:String>
    <system:String x:Key="BarButtonItem_AllFaultReset">&#160;全部故障清除&#160;</system:String>

    <!--管理-->
    <system:String x:Key="Caption_Manage">管理</system:String>
    <system:String x:Key="Caption_ThemeSetting">主题设置</system:String>

    <system:String x:Key="Caption_SoftwareException">软件异常</system:String>

    <system:String x:Key="BarButtonItem_SoftwareErrorLog">&#160;查询日志&#160;</system:String>
    <system:String x:Key="BarButtonItem_DeleteSoftwareErrorHistory">&#160;删除日志&#160;</system:String>
    <system:String x:Key="BarButtonItem_OpenLogFile">&#160;日志链接&#160;</system:String>

    <system:String x:Key="Caption_FactoryMode">出厂模式</system:String>

    <system:String x:Key="BarButtonItem_SetOneKeyShortcut">&#160;出厂设置&#160;</system:String>


    <!--报警信息-->
    <system:String x:Key="Caption_AlarmInformation">报警信息</system:String>
    <system:String x:Key="Caption_layoutPanel_ErrorAlarm">报警信息</system:String>

    <system:String x:Key="Header_SlideAlarm0">实时报警1</system:String>

    <system:String x:Key="Label_SlideAlarm0_Description">报警描述</system:String>
    <system:String x:Key="Label_SlideAlarm0_ID">报警编号</system:String>
    <system:String x:Key="Label_SlideAlarm0_Level">报警等级</system:String>
    <system:String x:Key="Label_SlideAlarm0_Time">报警时间</system:String>
    <system:String x:Key="Label_SlideAlarm0_Measures">处理措施</system:String>

    <system:String x:Key="Header_SlideAlarm1">实时报警2</system:String>

    <system:String x:Key="Label_SlideAlarm1_Description">报警描述</system:String>
    <system:String x:Key="Label_SlideAlarm1_ID">报警编号</system:String>
    <system:String x:Key="Label_SlideAlarm1_Level">报警等级</system:String>
    <system:String x:Key="Label_SlideAlarm1_Time">报警时间</system:String>
    <system:String x:Key="Label_SlideAlarm1_Measures">处理措施</system:String>

    <system:String x:Key="Header_SlideAlarm2">实时报警3</system:String>

    <system:String x:Key="Label_SlideAlarm2_Description">报警描述</system:String>
    <system:String x:Key="Label_SlideAlarm2_ID">报警编号</system:String>
    <system:String x:Key="Label_SlideAlarm2_Level">报警等级</system:String>
    <system:String x:Key="Label_SlideAlarm2_Time">报警时间</system:String>
    <system:String x:Key="Label_SlideAlarm2_Measures">处理措施</system:String>

    <system:String x:Key="Header_SlideAlarm3">实时报警4</system:String>

    <system:String x:Key="Label_SlideAlarm3_Description">报警描述</system:String>
    <system:String x:Key="Label_SlideAlarm3_ID">报警编号</system:String>
    <system:String x:Key="Label_SlideAlarm3_Level">报警等级</system:String>
    <system:String x:Key="Label_SlideAlarm3_Time">报警时间</system:String>
    <system:String x:Key="Label_SlideAlarm3_Measures">处理措施</system:String>

    <system:String x:Key="Header_SlideAlarm4">实时报警5</system:String>

    <system:String x:Key="Label_SlideAlarm4_Description">报警描述</system:String>
    <system:String x:Key="Label_SlideAlarm4_ID">报警编号</system:String>
    <system:String x:Key="Label_SlideAlarm4_Level">报警等级</system:String>
    <system:String x:Key="Label_SlideAlarm4_Time">报警时间</system:String>
    <system:String x:Key="Label_SlideAlarm4_Measures">处理措施</system:String>

    <!--监控数据-->
    <system:String x:Key="Caption_layoutPanel_ParameterMonitoring">监控数据</system:String>
    <system:String x:Key="Header_MonitoringData">监控数据显示</system:String>

    <system:String x:Key="Header_FieldName_ParameterName">参数名</system:String>
    <system:String x:Key="Header_FieldName_Value">参数值</system:String>
    <system:String x:Key="Header_FieldName_Unit">单位</system:String>

    <!--快捷设置-->
    <system:String x:Key="Header_Setting">快捷设置</system:String>

    <system:String x:Key="NavBarItem_SwitchAxis">轴切换</system:String>
    <system:String x:Key="NavBarItem_FaultReset">故障清除</system:String>
    <system:String x:Key="NavBarItem_SystemReset">伺服重启</system:String>

    <!--状态栏-->
    <system:String x:Key="Label_CommunicationStatus">通信状态:</system:String>

    <system:String x:Key="Label_StatusWord">伺服状态:</system:String>

    <system:String x:Key="Label_Information">信息提示:</system:String>



    



    <!--管理员-->
    <system:String x:Key="Label_AdministratorKey">管理员密码</system:String>



    <!--通信设置-->
    <system:String x:Key="Label_CommunicationSet">通信设置</system:String>

    <system:String x:Key="Label_SerialPortNum">串口号</system:String>
    <system:String x:Key="Label_BaudRate">波特率</system:String>
    <system:String x:Key="Label_DataBit">数据位</system:String>
    <system:String x:Key="Label_EndBit">停止位</system:String>
    <system:String x:Key="Label_CheckBit">检验位</system:String>


    <!--伺服驱动器选择-->
    <system:String x:Key="Label_ServoSelect">伺服驱动器选择</system:String>

    <system:String x:Key="Label_Servo">驱动器</system:String>

    <!--地址设置-->
    <system:String x:Key="Label_AddressSet">地址设置</system:String>

    <system:String x:Key="Label_StationAddress">从站地址</system:String>
    <system:String x:Key="Label_AxisAddress">轴地址</system:String>
    <system:String x:Key="Label_Add">添加</system:String>
    <system:String x:Key="Label_Scan">扫描</system:String>

    <!--设置执行-->
    <system:String x:Key="Label_Setup">设置执行</system:String>

    <system:String x:Key="Label_RefreshSerialPortNum">刷新端口</system:String>
    <system:String x:Key="Label_GetSerialPortConnection">通信连接</system:String>
    <system:String x:Key="Label_CloseSerialPortConnection">通信断开</system:String>
    <system:String x:Key="Label_EchoTest">回传测试</system:String>


    <system:String x:Key="Label_CommunicationSet1">1.通信配置</system:String>
    <system:String x:Key="Label_MotorFeedback">2.电机反馈</system:String>
    <system:String x:Key="Label_ParameterLearning">3.参数学习</system:String>
    <system:String x:Key="Label_ParameterIdentification">4.参数辨识</system:String>
    <system:String x:Key="Label_JogDebug">5.JOG调试</system:String>
    <system:String x:Key="Label_ParameterSelf-learning">6.辨识整定</system:String>

    <system:String x:Key="Label_PageDown">下页</system:String>



    <!--电流环-->
    <system:String x:Key="Label_Legend">电流环图示 — 点击图例可设定相应参数</system:String>

    <system:String x:Key="Label_FirstOrderLow-passFilter">一阶低通滤波</system:String>
    <system:String x:Key="Label_NotchFilter">陷波滤波器</system:String>
    <system:String x:Key="Label_TorqueLimit">转矩限制</system:String>
    <system:String x:Key="Label_PIController">PI控制器</system:String>
    <system:String x:Key="Label_VoltageCommand">电压指令</system:String>
    <system:String x:Key="Label_TorqueCommand">转矩指令</system:String>
    <system:String x:Key="Label_CurrentFeedback">电流反馈</system:String>
    <system:String x:Key="Label_TorqueFeedforward">转矩前馈</system:String>
    <system:String x:Key="Label_SecondOrderLow-passFilter">二阶低通滤波</system:String>

    <system:String x:Key="Header_LowPassFilter">低通滤波器</system:String>
    <system:String x:Key="Label_FirstTrqcmdFilterTime">第一转矩指令滤波时间参数</system:String>
    <system:String x:Key="Label_SecondTrqcmdFilterFreq">第二转矩指令滤波器频率</system:String>
    <system:String x:Key="Label_SecondTrqcmdFilterQ">第二转矩指令滤波器Q值</system:String>
    <system:String x:Key="Label_Prompt_SecondTrqcmdFilterFreq">提示：第二转矩指令滤波器频率为5000，表示不使用第二转矩</system:String>

    <system:String x:Key="Header_NotchFilter">陷波滤波器</system:String>
    <system:String x:Key="Label_NotchFilterFrequency1">第一陷波&#x0a;滤波器频率</system:String>
    <system:String x:Key="Label_NotchFilterQFactor1">第一陷波&#x0a;滤波器Q值</system:String>
    <system:String x:Key="Label_NotchFilterDepth1">第一陷波&#x0a;滤波器深度</system:String>
    <system:String x:Key="Label_NotchFilterFrequency2">第二陷波&#x0a;滤波器频率</system:String>
    <system:String x:Key="Label_NotchFilterQFactor2">第二陷波&#x0a;滤波器Q值</system:String>
    <system:String x:Key="Label_NotchFilterDepth2">第二陷波&#x0a;滤波器深度</system:String>
    <system:String x:Key="Label_NotchFilterFrequency3">第三陷波&#x0a;滤波器频率</system:String>
    <system:String x:Key="Label_NotchFilterQFactor3">第三陷波&#x0a;滤波器Q值</system:String>
    <system:String x:Key="Label_NotchFilterDepth3">第三陷波&#x0a;滤波器深度</system:String>
    <system:String x:Key="Label_NotchFilterFrequency4">第四陷波&#x0a;滤波器频率</system:String>
    <system:String x:Key="Label_NotchFilterQFactor4">第四陷波&#x0a;滤波器Q值</system:String>
    <system:String x:Key="Label_NotchFilterDepth4">第四陷波&#x0a;滤波器深度</system:String>

    <system:String x:Key="Header_TorqueLimit">转矩限制</system:String>
    <system:String x:Key="Label_ForwardInternalTorqueLimit">正转内部转矩限制值</system:String>
    <system:String x:Key="Label_ReverseInternalTorqueLimit">反转内部转矩限制值</system:String>
    <system:String x:Key="Label_ForwardExternalTorqueLimit">正转外部转矩限制值</system:String>
    <system:String x:Key="Label_ReverseExternalTorqueLimit">反转外部转矩限制值</system:String>

    <system:String x:Key="Label_ReadCurrentLoopParameter">刷新参数</system:String>
    <system:String x:Key="Label_GetDefaultCurrentLoopParameter">默认参数</system:String>
    <system:String x:Key="Label_WriteCurrentLoopParameter">下载参数</system:String>



    <!--数字IO-->
    <system:String x:Key="Label_DigitalInput">数字输入</system:String>

    <system:String x:Key="Label_PortNumber">端口号</system:String>
    <system:String x:Key="Label_LogicalSelect">逻辑选择</system:String>
    <system:String x:Key="Label_FunctionSelect">功能选择</system:String>
    <system:String x:Key="Label_State">状态</system:String>

    <system:String x:Key="Label_DI1_State">正常</system:String>
    <system:String x:Key="Label_DI2_State">正常</system:String>
    <system:String x:Key="Label_DI3_State">正常</system:String>
    <system:String x:Key="Label_DI4_State">正常</system:String>
    <system:String x:Key="Label_DI5_State">正常</system:String>
    <system:String x:Key="Label_DI6_State">正常</system:String>

    <system:String x:Key="Label_DigitalOutput">数字输出</system:String>

    <system:String x:Key="Label_DO1_State">正常</system:String>
    <system:String x:Key="Label_DO2_State">正常</system:String>
    <system:String x:Key="Label_DO3_State">正常</system:String>
    <system:String x:Key="Label_DO4_State">正常</system:String>
    <system:String x:Key="Label_DO5_State">正常</system:String>
    <system:String x:Key="Label_DO6_State">正常</system:String>

    <system:String x:Key="Label_ReadDigitalIOParameter">刷新参数</system:String>
    <system:String x:Key="Label_GetDefaultDigitalIOParameter">默认参数</system:String>
    <system:String x:Key="Label_WriteDigitalIOParameter">下载参数</system:String>

    <system:String x:Key="Label_UnitSet">1.单位设置</system:String>
    <system:String x:Key="Label_LimitProtection">2.限幅保护</system:String>
    <system:String x:Key="Label_NormalSet">3.一般设定</system:String>
    <system:String x:Key="Label_DigitalIO">4.数字IO</system:String>

    <system:String x:Key="Label_Load">加载</system:String>
    <system:String x:Key="Label_Save">保存</system:String>
    <system:String x:Key="Label_PageUp">上页</system:String>





    <!--<system:String x:Key="Caption_SoftwareException">软件异常</system:String>

    <system:String x:Key="BarButtonItem_SoftwareErrorLog">&#160;查询日志&#160;</system:String>
    <system:String x:Key="BarButtonItem_DeleteSoftwareErrorHistory">&#160;删除日志&#160;</system:String>
    <system:String x:Key="BarButtonItem_OpenLogFile">&#160;日志链接&#160;</system:String>-->













    <!--<system:String x:Key="MotorFeedbackAutoLearn">电机参数辨识</system:String>-->
</ResourceDictionary>