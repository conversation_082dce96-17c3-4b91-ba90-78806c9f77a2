﻿using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using ServoStudio.Models;
using ServoStudio.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace ServoStudio.GlobalPthread
{   
    public class SerialPortTransmitingPthread
    {
        public event CheckAllThreadClosed evtCheckAllThreadClosed;
        public event EvaluationSerialPortWorkProcess evtEvaluationSerialPortWorkProcess;
        public bool PthreadSwitch { get; set; }
        public bool PthreadPause { get; set; }
        public bool PthreadWorking { get; set; }
        public int PthreadInterval { get; set; }
     
        //*************************************************************************
        //函数名称：Run
        //函数功能：线程运行
        //
        //输入参数：object obj   线程池传输参数
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.21
        //*************************************************************************
        public void Run(object obj)
        {
            try
            {       
                while (PthreadSwitch)
                {
                    Thread.Sleep(PthreadInterval);

                    if (!PthreadPause && PthreadSwitch)
                    {
                        TaskManagementAndTransmiting();
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PTHREAD_SERIAL_PORT_TRANSMITING_RUN, "SerialPortTransmitingPthread.Run", ex);
            }
            finally
            {
                PthreadWorking = false;

                if (OthersHelper.CheckIsAllThreadClosed() && evtCheckAllThreadClosed != null)
                {
                    evtCheckAllThreadClosed();
                }                
            }
        }

        //*************************************************************************
        //函数名称：TaskManagementAndTransmiting
        //函数功能：串口任务管理与报文发送
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.21&2023.04.03
        //*************************************************************************
        private void TaskManagementAndTransmiting()
        {
            int iRet = -1;
            
            try
            {
                //判断是否存在任务
                iRet = IsExistSerialPortTask();
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //删除执行完成的任务
                iRet = DeleteSerialPortTaskExecuted();
                if (iRet == RET.ERROR)
                {
                    return;
                }

                //删除执行了多次无响应,状态是执行中
                iRet = DeleteExecutingTaskRepeatedly();
                if (iRet == RET.ERROR)
                {
                    return;
                }

                //任务数量管理
                iRet = TaskNumberManagement();
                if (iRet == RET.ERROR)
                {
                    return;
                }

                //判断是否添加采样状态问询、数据上传任务-放到整个任务队列的最后面
                iRet = AddAskingOrUploadingTask();
                if (iRet == RET.ERROR)
                {
                    return;
                }

                //判断是否添加故障数据采样状态问询、故障数据上传任务-放到整个任务队列的最后面     由Lilbert于2023.04.03添加故障数据采样状态问询、故障数据上传任务
                iRet = AddAskingOrUploadingTask_ForFault();
                if (iRet == RET.ERROR)
                {
                    return;
                }

                //判断是否添加写控制字任务管理-放到这个任务队列的最前面
                iRet = AddControlWordTask();
                if (iRet == RET.ERROR)
                {
                    return;
                }

                //选中优先级最高的任务
                iRet = GetHighestLevelTask();
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //拼接报文,并转换成Bytes
                iRet = HexHelper.TransmittingMessageToBytes(CommunicationSet.SlaveID, CommunicationSet.AxisID, CommunicationSet.FunctionCode, CommunicationSet.AcquisitionExecutedCode, CommunicationSet.TransmittingDataInfo, ref CommunicationSet.Transmiting);
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                //串口数据发送,更新当前任务
                iRet = HexHelper.TransmittingData(CommunicationSet.Transmiting);
                if (iRet == RET.NO_EFFECT)
                {
                    ViewModelSet.Main?.ShowHintInfo("串口通信异常，报文发送失败");
                }         
                else if (iRet == RET.ERROR)
                {
                    //更新状态栏
                    //ViewModelSet.Main?.RefreshSerialPortWorkState(TaskName.Empty);
                    //ViewModelSet.Main?.RefreshSerialPortConnectState(ConnectState.Explaination_NotConnect);
                    if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
                    {
                        ViewModelSet.Main?.RefreshSerialPortWorkState(TaskName.Empty);

                        ViewModelSet.Main?.RefreshSerialPortConnectState(ConnectState.Explaination_NotConnect);
                    }
                    else
                    {
                        ViewModelSet.Main?.RefreshSerialPortWorkState(TaskName.Empty1);

                        ViewModelSet.Main?.RefreshSerialPortConnectState(ConnectState.Explaination_NotConnect1);
                    }

                    WindowSet.clsMainWindow.ShowNotification(3017);

                    //关闭所有线程
                    OthersHelper.CloseAllThread();

                    //任务栈更新清零
                    SerialPortTask.TaskManagement = new List<TaskManagementSet>();

                    //接收的信息清空
                    SoftwareStateParameterSet.lstMessage = new List<byte>();

                    //通信连接状态
                    SoftwareStateParameterSet.IsConnected = false;

                    //串口关闭
                    if (CommunicationSet.SerialPortInfo != null)
                    {
                        if (CommunicationSet.SerialPortInfo.IsOpen)
                        {
                            CommunicationSet.SerialPortInfo.Close();
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("TaskManagementAndTransmiting", ex);
            }
        }

        //*************************************************************************
        //函数名称：IsExistSerialPortTask
        //函数功能：是否存在任务
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.01.07
        //*************************************************************************
        private int IsExistSerialPortTask()
        {
            if (SerialPortTask.TaskManagement == null)
            {
                SerialPortTask.TaskManagement = new List<TaskManagementSet>();
            }
               
            if (SerialPortTask.TaskManagement.Count == 0 && (AcquisitionInfoSet.CurrentProcess == TaskName.Empty || string.IsNullOrEmpty(AcquisitionInfoSet.CurrentProcess)) && (!ControlWordSet.WriteSwitch))
            {
                return RET.NO_EFFECT;
            }                
            else
            {                  
                return RET.SUCCEEDED;
            }   
        }

        //*************************************************************************
        //函数名称：DeleteSerialPortTaskExecuted
        //函数功能：删除已经执行完成的任务
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.21
        //*************************************************************************
        private int DeleteSerialPortTaskExecuted()
        {
            try
            {
                for (int i = 0; i < SerialPortTask.TaskManagement.Count;)
                {
                    if (SerialPortTask.TaskManagement[i].ExecutionState == TaskState.EXECUTED)
                    {
                        SerialPortTask.TaskManagement.RemoveAt(i);
                    }
                    else
                    {
                        i++;
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("DeleteTaskExecuted", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：DeleteExecutingTaskRepeatedly
        //函数功能：删除执行多次都无响应的任务
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.11.05&2023.02.08
        //*************************************************************************
        private int DeleteExecutingTaskRepeatedly()
        {
            try
            {
                for (int i = 0; i < SerialPortTask.TaskManagement.Count;)
                {
                    if ((SerialPortTask.TaskManagement[i].TaskName == TaskName.SystemReset || SerialPortTask.TaskManagement[i].TaskName == TaskName.AllAxisSystemReset) && SerialPortTask.TaskManagement[i].ExecuteTimes == 1)
                    {
                        SerialPortTask.TaskManagement.RemoveAt(i);
                    }
                    else if (SerialPortTask.TaskManagement[i].ExecuteTimes >= LimitValue.TaskExecutionTimes && SerialPortTask.TaskManagement[i].ExecutionState == TaskState.EXECUTING)
                    {
                        if (evtEvaluationSerialPortWorkProcess != null)
                        {
                            evtEvaluationSerialPortWorkProcess(TaskName.Empty);
                        }
   
                        switch (SerialPortTask.TaskManagement[i].TaskName)
                        {
                            case TaskName.Test:
                                //if (AddSlaveAxisIDSet.Action == "ScanSlaveAxisID")//由Lilbert于2023.02.08添加扫描从站地址选择
                                //{
                                //    AddSlaveAxisIDSet.Abc = true;
                                //    ViewModelSet.Main?.ShowHintInfo("从站扫描回送测试无响应");                                  
                                //}
                                //else
                                //{
                                //    ViewModelSet.Main?.ShowHintInfo("回送测试无响应");
                                //}
                                ViewModelSet.Main?.ShowHintInfo("回送测试无响应");

                                if (SoftwareStateParameterSet.IsFirstEchoTest)
                                {
                                    WindowSet.clsMainWindow.ShowNotification(1003);
                                }
                                else
                                {
                                    //if (AddSlaveAxisIDSet.Action == "ScanSlaveAxisID")//由Lilbert于2023.02.08添加扫描从站地址选择
                                    //{
                                    //    ViewModelSet.Main?.ShowHintInfo("从站扫描回送测试无响应");
                                    //}
                                    //else
                                    //{
                                    //    WindowSet.clsMainWindow.ShowNotification(1002);
                                    //}
                                    WindowSet.clsMainWindow.ShowNotification(1002);
                                }       
                                break;
                            case TaskName.Advanced:
                                ViewModelSet.Main?.ShowHintInfo("高级配置无响应");
                                break;
                            case TaskName.Auxiliary:
                                ViewModelSet.Main?.ShowHintInfo("辅助参数无响应");
                                break;
                            case TaskName.Basic:
                                ViewModelSet.Main?.ShowHintInfo("基本配置无响应");
                                break;
                            case TaskName.CIA402:
                                ViewModelSet.Main?.ShowHintInfo("CIA402无响应");
                                break;
                            case TaskName.Common:
                                ViewModelSet.Main?.ShowHintInfo("轴共同参数无响应");
                                break;
                            case TaskName.Control:
                                ViewModelSet.Main?.ShowHintInfo("运控参数无响应");
                                break;
                            case TaskName.DI:
                                ViewModelSet.Main?.ShowHintInfo("DI无响应");
                                break;
                            case TaskName.DO:
                                ViewModelSet.Main?.ShowHintInfo("DO无响应");
                                break;
                            case TaskName.FaultAndProtection:
                                ViewModelSet.Main?.ShowHintInfo("故障与保护无响应");
                                break;
                            case TaskName.Motor:
                                ViewModelSet.Main?.ShowHintInfo("电机参数无响应");
                                break;
                            case TaskName.AssigningAcquisition:
                                ViewModelSet.Main?.ShowHintInfo("数据采集无响应");
                                OthersHelper.ClearAcquisitionInfoSet();
                                break;
                            case TaskName.AskingAcquisitionState:
                                ViewModelSet.Main?.ShowHintInfo("数据采集问询无响应");
                                OthersHelper.ClearAcquisitionInfoSet();                                  
                                break;
                            case TaskName.UploadingAcquisition:
                                ViewModelSet.Main?.ShowHintInfo("数据采集上传无响应");
                                OthersHelper.ClearAcquisitionInfoSet();
                                break;
                            case TaskName.HardwareAlarm:
                                ViewModelSet.Main?.ShowHintInfo("报警信息获取无响应");
                                break;         
                            case TaskName.FunctionGenerator:
                                ViewModelSet.Main?.ShowHintInfo("函数发生器无响应");
                                break;
                            case TaskName.ThreeLoop:
                                ViewModelSet.Main?.ShowHintInfo("三环调试无响应");
                                break;
                            case TaskName.MotorFeedback:
                                ViewModelSet.Main?.ShowHintInfo("电机反馈无响应");
                                break;
                            case TaskName.LimitAmplitude:
                                ViewModelSet.Main?.ShowHintInfo("限幅保护无响应");
                                break;
                            case TaskName.DigitalIO:
                                ViewModelSet.Main?.ShowHintInfo("数字IO无响应");
                                break;
                            case TaskName.NormalSetting:
                                ViewModelSet.Main?.ShowHintInfo("一般设置无响应");
                                break;
                            case TaskName.CurrentLoop:
                                ViewModelSet.Main?.ShowHintInfo("电流环无响应");
                                break;
                            case TaskName.SpeedLoop:
                                ViewModelSet.Main?.ShowHintInfo("速度环无响应");
                                break;
                            case TaskName.PositionLoop:
                                ViewModelSet.Main?.ShowHintInfo("位置环无响应");
                                break;
                            case TaskName.SpeedAction:
                                ViewModelSet.Main?.ShowHintInfo("速度运动模式无响应");
                                break;
                            case TaskName.TorqueAction:
                                ViewModelSet.Main?.ShowHintInfo("转矩运动模式无响应");
                                break;
                            case TaskName.PositionAction:
                                ViewModelSet.Main?.ShowHintInfo("位置运动模式无响应");
                                break;
                            case TaskName.AbsolutePositionAction:
                                ViewModelSet.Main?.ShowHintInfo("绝对位置运动模式无响应");
                                break;
                            case TaskName.RelativePositionAction:
                                ViewModelSet.Main?.ShowHintInfo("相对位置运动模式无响应");
                                break;
                            case TaskName.ControlWord:
                                ViewModelSet.Main?.ShowHintInfo("控制字设置无响应");
                                OthersHelper.ClearControlWordSet();
                                break;                           
                            case TaskName.OnlineInertiaIdentification:
                                ViewModelSet.Main?.ShowHintInfo("在线惯量识别无响应");
                                break;
                            case TaskName.OfflineInertiaIdentification:
                                ViewModelSet.Main?.ShowHintInfo("离线惯量识别无响应");
                                break;
                            case TaskName.OfflineInertiaIdentificationContinuously:
                                ViewModelSet.Main?.ShowHintInfo("离线惯量识别持续运行无响应");
                                break;
                            case TaskName.OfflineInertiaIdentificationModify:
                                ViewModelSet.Main?.ShowHintInfo("离线惯量识别修改无响应");
                                break;
                            case TaskName.OfflineInertiaIdentificationOperatingEnd:
                                ViewModelSet.Main?.ShowHintInfo("离线惯量识别结束无响应");
                                break;
                            case TaskName.OfflineInertiaIdentificationOperatingMode:
                                ViewModelSet.Main?.ShowHintInfo("离线惯量识别模式无响应");
                                break;
                            case TaskName.OfflineInertiaIdentificationSwitch:
                                ViewModelSet.Main?.ShowHintInfo("离线惯量识别使能禁能无响应");
                                break;
                            case TaskName.OfflineInertiaIdentificationUnload:
                                ViewModelSet.Main?.ShowHintInfo("离线惯量识别退出无响应");
                                break;
                            case TaskName.ParameterIdentification:
                                ViewModelSet.Main?.ShowHintInfo("电机参数辨识无响应");
                                break;
                            case TaskName.ParameterIdentificationListen:
                                ViewModelSet.Main?.ShowHintInfo("电机参数辨识监听无响应");
                                break;
                            case TaskName.ParameterIdentificationModify:
                                ViewModelSet.Main?.ShowHintInfo("电机参数修改无响应");
                                break;
                            case TaskName.ParameterIdentificationOperatingEnd:
                                ViewModelSet.Main?.ShowHintInfo("电机参数辨识运行结束无响应");
                                break;
                            case TaskName.ParameterIdentificationOperatingMode:
                                ViewModelSet.Main?.ShowHintInfo("电机参数辨识运行模式无响应");
                                break;
                            case TaskName.ParameterIdentificationSwitch:
                                ViewModelSet.Main?.ShowHintInfo("电机参数辨识使能禁能无响应");
                                break;
                            case TaskName.ParameterIdentificationUnload:
                                ViewModelSet.Main?.ShowHintInfo("电机参数辨识退出无响应");
                                break;
                            case TaskName.ProgramJog:
                                ViewModelSet.Main?.ShowHintInfo("程序JOG调试无响应");
                                break;
                            case TaskName.ProgramJogContinuously:
                                ViewModelSet.Main?.ShowHintInfo("程序JOG运行无响应");
                                break;
                            case TaskName.ProgramJogOperatingEnd:
                                ViewModelSet.Main?.ShowHintInfo("程序JOG运行结束设置无响应");
                                break;
                            case TaskName.ProgramJogOperatingMode:
                                ViewModelSet.Main?.ShowHintInfo("程序JOG运行模式设置无响应");
                                break;
                            case TaskName.ProgramJogSwitch:
                                ViewModelSet.Main?.ShowHintInfo("程序JOG模块使能/禁能无响应");
                                break;
                            case TaskName.FunctionGenerator + TaskName.ThreeLoop + TaskName.Action:
                                ViewModelSet.Main?.ShowHintInfo("调试参数刷新无响应");
                                break;
                            case TaskName.JogOperatingMode:
                                ViewModelSet.Main?.ShowHintInfo("JOG运行模式设置无响应");
                                break;
                            case TaskName.JogOperatingEnd:
                                ViewModelSet.Main?.ShowHintInfo("JOG运行结束设置无响应");
                                break;
                            case TaskName.JogSwitch:
                                ViewModelSet.Main?.ShowHintInfo("JOG模块使能/禁能无响应");
                                break;
                            case TaskName.JogContinuously:
                                ViewModelSet.Main?.ShowHintInfo("JOG运行无响应");
                                break;
                            case TaskName.UnitExchanged:
                                ViewModelSet.Main?.ShowHintInfo("单位换算无响应");
                                break;
                            case TaskName.SystemParameterInitialize:
                                ViewModelSet.Main?.ShowHintInfo("系统参数初始化无响应");
                                break;
                            default:                               
                                break;
                        }

                        //接收的信息清空
                        SoftwareStateParameterSet.lstMessage = new List<byte>();  //由lilbert于2023.04.03添加接收信息清空

                        SerialPortTask.TaskManagement.RemoveAt(i);                      
                    }
                    else
                    {
                        i++;
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("DeleteExecutingTaskRepeatedly", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：TaskNumberManagement
        //函数功能：删除重复的任务，不考虑当前任务状态，写任务最多3个
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.23
        //*************************************************************************
        private int TaskNumberManagement()
        {          
            int iMinus = 0;
            int iHighestLevel = SerialPortTask.TaskManagement.Count - 1;

            try
            {
                // 没有任务或只有1个任务不需要任务管理
                if (SerialPortTask.TaskManagement.Count <= 1)
                {
                    return RET.SUCCEEDED;
                }

                //清除重复任务-除读写任务
                while (iHighestLevel > 0)              
                {
                    TaskManagementSet clsTemp = new TaskManagementSet();
                    CopyTaskManagermentItem(SerialPortTask.TaskManagement[iHighestLevel], ref clsTemp);

                    for (int j = 0; j < SerialPortTask.TaskManagement.Count - 1 - iMinus;)
                    {
                        if (CheckTaskManagementItemIsEqual(clsTemp, SerialPortTask.TaskManagement[j]))//相等
                        {
                            SerialPortTask.TaskManagement.RemoveAt(j);
                        }
                        else
                        {
                            j++;
                        }
                    }

                    iMinus++;
                    iHighestLevel = (SerialPortTask.TaskManagement.Count - 1) - iMinus;
                }

                //读写任务最大保留
                ReadTaskOrWriteTaskRemain();

                return RET.SUCCEEDED; 
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("TaskNumberManagement", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：CopyTaskManagermentItem
        //函数功能：复制任务
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.21
        //*************************************************************************
        private void CopyTaskManagermentItem(TaskManagementSet clsSource, ref TaskManagementSet clsTarget)
        {
            try
            {
                clsTarget = new TaskManagementSet();
                clsTarget.TaskName = clsSource.TaskName;
                clsTarget.FunctionCode = clsSource.FunctionCode;
                clsTarget.AcquisitionExecutedCode = clsSource.AcquisitionExecutedCode;
                //clsTarget.StationID = clsSource.StationID;
                clsTarget.SlaveID = clsSource.SlaveID;
                clsTarget.AxisID = clsSource.AxisID;
                clsTarget.CurrentPageName = clsSource.CurrentPageName;

                foreach (var item in clsSource.TransmittingDataInfo)
                {
                    clsTarget.TransmittingDataInfo.Add(new TransmitingDataInfoSet() { Address = item.Address, Content = item.Content, DataType = item.DataType });
                }
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("CopyTaskManagermentItem", ex);
            }
        }

        //*************************************************************************
        //函数名称：CheckTaskManagementItemIsEqual
        //函数功能：判断任务是否相同,写不考虑
        //
        //输入参数：TaskManagementSet clsTarget1     任务信息集合1
        //         TaskManagementSet clsTarget2     任务信息集合2
        //         
        //输出参数：true：   任务重复
        //         false:   任务不重复
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.21&2023.04.03
        //*************************************************************************
        private bool CheckTaskManagementItemIsEqual(TaskManagementSet clsTarget1, TaskManagementSet clsTarget2)
        {
            try
            {               
                if (clsTarget1.FunctionCode == FunctionCode.TEST_COMMUNICATION || clsTarget1.FunctionCode == FunctionCode.HARDWAREALARM || clsTarget1.FunctionCode == FunctionCode.ACQUISITION) //回送测试，报警日志，数据采集任务
                {
                    //if (clsTarget1.FunctionCode == clsTarget2.FunctionCode && clsTarget1.StationID == clsTarget2.StationID && clsTarget1.AxisID == clsTarget2.AxisID)

                    if (clsTarget1.FunctionCode == clsTarget2.FunctionCode && clsTarget1.SlaveID == clsTarget2.SlaveID && clsTarget1.AxisID == clsTarget2.AxisID)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }  
                else if (clsTarget1.FunctionCode == FaultFunctionCode.ACQUISITION) //由Lilbert于20230403添加故障数据采集任务
                {
                    //if (clsTarget1.FunctionCode == clsTarget2.FunctionCode && clsTarget1.StationID == clsTarget2.StationID && clsTarget1.AxisID == clsTarget2.AxisID)

                    if (clsTarget1.FunctionCode == clsTarget2.FunctionCode && clsTarget1.SlaveID == clsTarget2.SlaveID && clsTarget1.AxisID == clsTarget2.AxisID)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else if (clsTarget1.FunctionCode == FunctionCode.PARAMETER_WRITE) //参数写入任务，只去掉重复的任务
                {
                    //if (clsTarget2.FunctionCode == FunctionCode.PARAMETER_WRITE && clsTarget1.TaskName == clsTarget2.TaskName && clsTarget1.StationID == clsTarget2.StationID && clsTarget1.AxisID == clsTarget2.AxisID)

                    if (clsTarget2.FunctionCode == FunctionCode.PARAMETER_WRITE && clsTarget1.TaskName == clsTarget2.TaskName && clsTarget1.SlaveID == clsTarget2.SlaveID && clsTarget1.AxisID == clsTarget2.AxisID)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }              
               else if (clsTarget1.FunctionCode == FunctionCode.PARAMETER_READ) //参数读取任务，只去掉重复的任务
                {

                    //if (clsTarget2.FunctionCode == FunctionCode.PARAMETER_READ && clsTarget1.TaskName == clsTarget2.TaskName && clsTarget1.StationID == clsTarget2.StationID && clsTarget1.AxisID == clsTarget2.AxisID)

                    if (clsTarget2.FunctionCode == FunctionCode.PARAMETER_READ && clsTarget1.TaskName == clsTarget2.TaskName && clsTarget1.SlaveID == clsTarget2.SlaveID && clsTarget1.AxisID == clsTarget2.AxisID)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return true;
                }                        
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("CheckTaskManagementItemIsEqual", ex);
                return false;
            }                           
        }

        //*************************************************************************
        //函数名称：ReadTaskOrWriteTaskRemain
        //函数功能：读写任务最多保留
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.01
        //*************************************************************************
        private void ReadTaskOrWriteTaskRemain()
        {
            int iWriteTaskNumber = 0;
            int iReadTaskNumber = 0;
            int iWriteTaskDeleteNumber = 0;
            int iReadTaskDeleteNumber = 0;

            #region 获取任务栈里面分别有几个读写任务
            foreach (var item in SerialPortTask.TaskManagement)
            {
                if (item.FunctionCode == FunctionCode.PARAMETER_WRITE)
                {
                    iWriteTaskNumber++;
                }
                else if (item.FunctionCode == FunctionCode.PARAMETER_READ)
                {
                    iReadTaskNumber++;
                }
            }
            #endregion

            #region 写任务最多保留
            if (iWriteTaskNumber > LimitValue.WriteTask)
            {
                for (int j = 0; j < SerialPortTask.TaskManagement.Count;)
                {
                    if (SerialPortTask.TaskManagement[j].FunctionCode != FunctionCode.PARAMETER_WRITE)
                    {
                        j++;
                    }
                    else
                    {
                        SerialPortTask.TaskManagement.RemoveAt(j);
                        iWriteTaskDeleteNumber++;
                    }

                    if (iWriteTaskDeleteNumber >= iWriteTaskNumber - LimitValue.WriteTask)
                    {
                        break;
                    }
                }
            }
            #endregion

            #region 读任务最多保留
            if (iReadTaskNumber > LimitValue.ReadTask)
            {
                for (int j = 0; j < SerialPortTask.TaskManagement.Count;)
                {
                    if (SerialPortTask.TaskManagement[j].FunctionCode != FunctionCode.PARAMETER_READ)
                    {
                        j++;
                    }
                    else
                    {
                        SerialPortTask.TaskManagement.RemoveAt(j);
                        iReadTaskDeleteNumber++;
                    }

                    if (iReadTaskDeleteNumber >= iReadTaskNumber - LimitValue.ReadTask)
                    {
                        break;
                    }
                }
            }
            #endregion
        }

        //*************************************************************************
        //函数名称：AddAskingOrUploadingTask
        //函数功能：添加采集状态问询任务或数据上传任务，并放到任务队列的最下面
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.26
        //*************************************************************************
        private int AddAskingOrUploadingTask()
        {
            int iRet = -1;

            try
            {
                #region 判断流程是否到采样问询
                if (AcquisitionInfoSet.IsExistTask && (AcquisitionInfoSet.CurrentProcess == TaskName.Acquiring || AcquisitionInfoSet.CurrentProcess == TaskName.AskingAcquisitionState))
                {
                    //遍历是否有采样问询任务
                    iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.AskingAcquisitionState);
                    if (iRet != -1)
                    {
                        return RET.SUCCEEDED;
                    }

                    //添加采样问询任务
                    //iRet = HexHelper.AddSerialPortTask(PageName.OSCILLOSCOPE, SoftwareStateParameterSet.StationID, SoftwareStateParameterSet.AxisID, TaskName.AskingAcquisitionState, FunctionCode.ACQUISITION, AcquisitionExecutedCode.ASK_ACQUISITION, new List<TransmitingDataInfoSet>());
                    iRet = HexHelper.AddSerialPortTask(PageName.OSCILLOSCOPE, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.AskingAcquisitionState, FunctionCode.ACQUISITION, AcquisitionExecutedCode.ASK_ACQUISITION, new List<TransmitingDataInfoSet>());
                    if (iRet != RET.SUCCEEDED)
                    {
                        return RET.ERROR;
                    }                             
                }
                #endregion

                #region 判断流程是否到数据上传
                if (AcquisitionInfoSet.IsExistTask && AcquisitionInfoSet.CurrentProcess == TaskName.UploadingAcquisition)
                {
                    //遍历是否有数据上传任务
                    iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.UploadingAcquisition);
                    if (iRet != -1)
                    {
                        return RET.SUCCEEDED;
                    }

                    //添加采样问询任务
                    //iRet = HexHelper.AddSerialPortTask(PageName.OSCILLOSCOPE, SoftwareStateParameterSet.StationID, SoftwareStateParameterSet.AxisID, TaskName.UploadingAcquisition, FunctionCode.ACQUISITION, AcquisitionExecutedCode.UPLOAD_ACQUISITION, new List<TransmitingDataInfoSet>());
                    iRet = HexHelper.AddSerialPortTask(PageName.OSCILLOSCOPE, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.UploadingAcquisition, FunctionCode.ACQUISITION, AcquisitionExecutedCode.UPLOAD_ACQUISITION, new List<TransmitingDataInfoSet>());
                    if (iRet != RET.SUCCEEDED)
                    {
                        return RET.ERROR;
                    }
                }
                #endregion

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("AddAskingOrUploadingTask", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AddAskingOrUploadingTask_ForFault
        //函数功能：添加采集状态问询任务或数据上传任务，并放到任务队列的最下面—故障数据采集
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.03
        //*************************************************************************
        private int AddAskingOrUploadingTask_ForFault()
        {
            int iRet = -1;

            try
            {
                #region 判断流程是否到采样问询
                if (FaultAcquisitionInfoSet.IsExistTask && (FaultAcquisitionInfoSet.CurrentProcess == TaskName.FaultAcquiring || FaultAcquisitionInfoSet.CurrentProcess == TaskName.FaultAskingAcquisitionState))
                {
                    //遍历是否有采样问询任务
                    iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.FaultAskingAcquisitionState);
                    if (iRet != -1)
                    {
                        return RET.SUCCEEDED;
                    }

                    //添加采样问询任务
                    //iRet = HexHelper.AddSerialPortTask(PageName.OSCILLOSCOPE, SoftwareStateParameterSet.StationID, SoftwareStateParameterSet.AxisID, TaskName.AskingAcquisitionState, FunctionCode.ACQUISITION, AcquisitionExecutedCode.ASK_ACQUISITION, new List<TransmitingDataInfoSet>());
                    iRet = HexHelper.AddSerialPortTask_For_Fault(PageName.FAULTDATAOSCILLOSCOPE, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.FaultAskingAcquisitionState, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.ASK_ACQUISITION, new List<TransmitingDataInfoSet>());
                    if (iRet != RET.SUCCEEDED)
                    {
                        return RET.ERROR;
                    }
                }
                #endregion

                #region 判断流程是否到数据上传
                if (FaultAcquisitionInfoSet.IsExistTask && FaultAcquisitionInfoSet.CurrentProcess == TaskName.UploadingAcquisitionFault)
                {
                    //遍历是否有数据上传任务
                    iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.UploadingAcquisitionFault);
                    if (iRet != -1)
                    {
                        return RET.SUCCEEDED;
                    }

                    //添加采样问询任务
                    //iRet = HexHelper.AddSerialPortTask(PageName.OSCILLOSCOPE, SoftwareStateParameterSet.StationID, SoftwareStateParameterSet.AxisID, TaskName.UploadingAcquisition, FunctionCode.ACQUISITION, AcquisitionExecutedCode.UPLOAD_ACQUISITION, new List<TransmitingDataInfoSet>());
                    iRet = HexHelper.AddSerialPortTask_For_Fault(PageName.FAULTDATAOSCILLOSCOPE, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.UploadingAcquisitionFault, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.UPLOAD_ACQUISITION, new List<TransmitingDataInfoSet>());
                    //if (SoftwareStateParameterSet.IsRetransmission)
                    //{
                    //    iRet = HexHelper.AddSerialPortTask_For_Fault(PageName.FAULTDATAOSCILLOSCOPE, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.UploadingAcquisitionFault, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.RETRANSMISSION_ACQUISITION, new List<TransmitingDataInfoSet>());
                    //    SoftwareStateParameterSet.IsRetransmission = false;//由Lilbert于2023.04.13添加重传机制
                    //    SoftwareStateParameterSet.IsFaultUpload = false;
                    //}
                    //else
                    //{
                    //    iRet = HexHelper.AddSerialPortTask_For_Fault(PageName.FAULTDATAOSCILLOSCOPE, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.UploadingAcquisitionFault, FaultFunctionCode.ACQUISITION, FaultAcquisitionExecutedCode.UPLOAD_ACQUISITION, new List<TransmitingDataInfoSet>());
                    //}

                    if (iRet != RET.SUCCEEDED)
                    {
                        return RET.ERROR;
                    }
                }
                #endregion

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("AddAskingOrUploadingTask_ForFault", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AddControlWordTask
        //函数功能：添加写控制字任务，并放到任务队列的最前面
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.20
        //*************************************************************************
        private int AddControlWordTask()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (ControlWordSet.WriteSwitch && ControlWordSet.ListValue.Count != 0)
                {
                    //遍历是否有写控制字任务
                    iRet = SerialPortTask.TaskManagement.FindIndex(item => item.TaskName == TaskName.ControlWord);
                    if (iRet != -1)
                    {
                        return RET.SUCCEEDED;
                    }

                    dicParameterInfo.Add("Control Word", Convert.ToString(ControlWordSet.ListValue[ControlWordSet.IndexOfList]));
                    iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                    if (iRet != RET.SUCCEEDED)
                    {
                        return RET.ERROR;
                    }
                 
                    ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);
                    //iRet = HexHelper.AddSerialPortTask(PageName.MAIN, SoftwareStateParameterSet.StationID, SoftwareStateParameterSet.AxisID, TaskName.ControlWord, FunctionCode.PARAMETER_WRITE, null, lstTransmittingDataInfo);
                    iRet = HexHelper.AddSerialPortTask(PageName.MAIN, SoftwareStateParameterSet.SlaveID, SoftwareStateParameterSet.AxisID, TaskName.ControlWord, FunctionCode.PARAMETER_WRITE, null, lstTransmittingDataInfo);
                    if (iRet != RET.SUCCEEDED)
                    {
                        return RET.ERROR;
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("AddControlWordTask", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetHighestLevelTask
        //函数功能：获取优先级最高的任务
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.20
        //*************************************************************************
        private int GetHighestLevelTask()
        {
            try
            {
                if (PthreadPause)
                {
                    return RET.NO_EFFECT;
                }

                if (SerialPortTask.TaskManagement.Count == 0)
                {
                    return RET.NO_EFFECT;
                }

                SerialPortTask.TaskManagement[SerialPortTask.TaskManagement.Count - 1].ExecutionState = TaskState.EXECUTING;
                SerialPortTask.TaskManagement[SerialPortTask.TaskManagement.Count - 1].ExecuteTimes++;
               
                CommunicationSet.FunctionCode = SerialPortTask.TaskManagement[SerialPortTask.TaskManagement.Count - 1].FunctionCode;
                CommunicationSet.AcquisitionExecutedCode = SerialPortTask.TaskManagement[SerialPortTask.TaskManagement.Count - 1].AcquisitionExecutedCode;

                //CommunicationSet.StationID = SerialPortTask.TaskManagement[SerialPortTask.TaskManagement.Count - 1].StationID;
                CommunicationSet.SlaveID = SerialPortTask.TaskManagement[SerialPortTask.TaskManagement.Count - 1].SlaveID;
                CommunicationSet.AxisID = SerialPortTask.TaskManagement[SerialPortTask.TaskManagement.Count - 1].AxisID;

                CommunicationSet.TaskName = SerialPortTask.TaskManagement[SerialPortTask.TaskManagement.Count - 1].TaskName;
                CommunicationSet.CurrentPageName = SerialPortTask.TaskManagement[SerialPortTask.TaskManagement.Count - 1].CurrentPageName;

                CommunicationSet.TransmittingDataInfo = new List<TransmitingDataInfoSet>();
                SerialPortTask.TaskManagement[SerialPortTask.TaskManagement.Count - 1].TransmittingDataInfo.ForEach(item => CommunicationSet.TransmittingDataInfo.Add(item));

                if (CommunicationSet.TaskName != TaskName.UploadingAcquisition && CommunicationSet.TaskName != TaskName.IntervalRefresh && CommunicationSet.TaskName != TaskName.BatchWrite &&
                    CommunicationSet.TaskName != TaskName.Motor && CommunicationSet.TaskName != TaskName.Basic &&
                    CommunicationSet.TaskName != TaskName.Control && CommunicationSet.TaskName != TaskName.Advanced &&
                    CommunicationSet.TaskName != TaskName.DI && CommunicationSet.TaskName != TaskName.DO &&
                    CommunicationSet.TaskName != TaskName.FaultAndProtection && CommunicationSet.TaskName != TaskName.Auxiliary &&
                    CommunicationSet.TaskName != TaskName.CIA402)
                {
                    evtEvaluationSerialPortWorkProcess(CommunicationSet.TaskName);
                }

                return RET.SUCCEEDED;             
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("GetHighestLevelTask", ex);
                return RET.ERROR;
            }           
        }       
    }
}
