﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Windows.Data;

namespace Converter
{
    public class AlarmLevelConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                switch (value.ToString())
                {
                    case "高级":
                        return "Red";
                    case "中级":
                        return "Orange";
                    case "低级":
                        return "Yellow";
                    default:
                        return "Transparent";
                }
            }
            catch
            {
                return "Red";
            }          
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
