﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Collections.Generic;
using System.Text;
using System.Reflection;
using log4net;

namespace ServoStudio.GlobalMethod
{
    public class LogHelper
    {
        public static readonly log4net.ILog loginfo = log4net.LogManager.GetLogger("loginfo");
        public static readonly log4net.ILog logerror = log4net.LogManager.GetLogger("logerror");

        public static void InfoLog(string info)
        {
            try
            {
                if (!string.IsNullOrEmpty(info))
                {
                    loginfo.Info("信  息 : " + info.ToString());
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static void ErrorLog(string info, Exception ex)
        {
            try
            {
                if (!string.IsNullOrEmpty(info) && ex == null)
                {
                    logerror.ErrorFormat("【附加信息】：{0}<br>", new object[] { info });
                }
                else if (!string.IsNullOrEmpty(info) && ex != null)
                {
                    string errorMsg = BeautyErrorMsg(ex);
                    logerror.ErrorFormat("【附加信息】：{0}<br>{1}", new object[] { info, errorMsg });
                }
                else if (string.IsNullOrEmpty(info) && ex != null)
                {
                    string errorMsg = BeautyErrorMsg(ex);
                    logerror.Error(errorMsg);
                }
            }
            catch (Exception e)
            {
                throw e;
            }
        }

        private static string BeautyErrorMsg(Exception ex)
        {
            string errorMsg = string.Format("【异常类型】：{0} <br>【异常信息】：{1} <br>【详细信息】：{2}<br>【堆栈调用】：{3}", new object[] { ex.GetType().Name, ex.Message, ex.InnerException, ex.StackTrace });
            errorMsg = errorMsg.Replace("\r\n", "<br>");
            errorMsg = errorMsg.Replace("位置", "<strong style=\"color:red\">位置</strong>");
            return errorMsg;
        }
    }
}

