<?xml version="1.0"?>
<doc>
    <assembly>
        <name>GalaSoft.MvvmLight.Platform</name>
    </assembly>
    <members>
        <member name="T:GalaSoft.MvvmLight.Helpers.Binding">
            <summary>
            Base class for bindings in Xamarin.iOS and Xamarin.Android.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Helpers.Binding.TopSource">
            <summary>
            The source at the "top" of the property chain.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Helpers.Binding.TopTarget">
            <summary>
            The target at the "top" of the property chain.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.Binding.Mode">
            <summary>
            The mode of the binding. OneTime means that the target property will be set once (when the binding is
            created) but that subsequent changes will be ignored. OneWay means that the target property will be set, and
            if the PropertyChanged event is raised by the source, the target property will be updated. TwoWay means that the source
            property will also be updated if the target raises the PropertyChanged event. Default means OneWay if only the source
            implements INPC, and TwoWay if both the source and the target implement INPC.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.Binding.Source">
            <summary>
            Gets the source object for the binding.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.Binding.Target">
            <summary>
            Gets the target object for the binding.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding.Detach">
            <summary>
            Instructs the Binding instance to stop listening to value changes and to
            remove all listeneners.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding.ForceUpdateValueFromSourceToTarget">
            <summary>
            Forces the Binding's value to be reevaluated. The target value will
            be set to the source value.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding.ForceUpdateValueFromTargetToSource">
            <summary>
            Forces the Binding's value to be reevaluated. The source value will
            be set to the target value.
            </summary>
        </member>
        <member name="E:GalaSoft.MvvmLight.Helpers.Binding.ValueChanged">
            <summary>
            Occurs when the value of the databound property changes.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.Binding`2">
            <summary>
            Creates a binding between two properties. If the source implements INotifyPropertyChanged, the source property raises the PropertyChanged event
            and the BindingMode is OneWay or TwoWay, the target property will be synchronized with the source property. If
            the target implements INotifyPropertyChanged, the target property raises the PropertyChanged event and the BindingMode is
            TwoWay, the source property will also be synchronized with the target property.
            </summary>
            <typeparam name="TSource">The type of the source property that is being databound.</typeparam>
            <typeparam name="TTarget">The type of the target property that is being databound. If the source type
            is not the same as the target type, an automatic conversion will be attempted. However only
            simple types can be converted. For more complex conversions, use the <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertSourceToTarget(System.Func{`0,`1})"/>
            and <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertTargetToSource(System.Func{`1,`0})"/> methods to define custom converters.</typeparam>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.Binding`2.FallbackValue">
            <summary>
            Gets or sets the value to use when the binding is unable to return a value. This can happen if one of the
            items on the Path (except the source property itself) is null, or if the Converter throws an exception.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.Binding`2.TargetNullValue">
            <summary>
            Gets of sets the value used when the source property is null (or equals to default(TSource)).
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.Binding`2.Value">
            <summary>
            Gets the current value of the binding.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.#ctor(System.Object,System.String,System.Object,System.String,GalaSoft.MvvmLight.Helpers.BindingMode,`0,`0)">
            <summary>
            Initializes a new instance of the Binding class for which the source and target properties
            are located in different objects.
            </summary>
            <param name="source">The source of the binding. If this object implements INotifyPropertyChanged and the
            BindingMode is OneWay or TwoWay, the target will be notified of changes to the target property.</param>
            <param name="sourcePropertyName">The name of the source property for the binding.</param>
            <param name="target">The target of the binding. If this object implements INotifyPropertyChanged and the
            BindingMode is TwoWay, the source will be notified of changes to the source property.</param>
            <param name="targetPropertyName">The name of the target property for the binding.</param>
            <param name="mode">The mode of the binding. OneTime means that the target property will be set once (when the binding is
            created) but that subsequent changes will be ignored. OneWay means that the target property will be set, and
            if the PropertyChanged event is raised by the source, the target property will be updated. TwoWay means that the source
            property will also be updated if the target raises the PropertyChanged event. Default means OneWay if only the source
            implements INPC, and TwoWay if both the source and the target implement INPC.</param>
            <param name="fallbackValue">Tthe value to use when the binding is unable to return a value. This can happen if one of the
            items on the Path (except the source property itself) is null, or if the Converter throws an exception.</param>
            <param name="targetNullValue">The value to use when the binding is unable to return a value. This can happen if one of the
            items on the Path (except the source property itself) is null, or if the Converter throws an exception.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.#ctor(System.Object,System.Linq.Expressions.Expression{System.Func{`0}},System.Object,System.Linq.Expressions.Expression{System.Func{`1}},GalaSoft.MvvmLight.Helpers.BindingMode,`0,`0)">
            <summary>
            Initializes a new instance of the Binding class for which the source and target properties
            are located in different objects.
            </summary>
            <param name="source">The source of the binding. If this object implements INotifyPropertyChanged and the
            BindingMode is OneWay or TwoWay, the target will be notified of changes to the target property.</param>
            <param name="sourcePropertyExpression">An expression pointing to the source property. It can be
            a simple expression "() => [source].MyProperty" or a composed expression "() => [source].SomeObject.SomeOtherObject.SomeProperty".</param>
            <param name="target">The target of the binding. If this object implements INotifyPropertyChanged and the
            BindingMode is TwoWay, the source will be notified of changes to the source property.</param>
            <param name="targetPropertyExpression">An expression pointing to the target property. It can be
            a simple expression "() => [target].MyProperty" or a composed expression "() => [target].SomeObject.SomeOtherObject.SomeProperty".</param>
            <param name="mode">The mode of the binding. OneTime means that the target property will be set once (when the binding is
            created) but that subsequent changes will be ignored. OneWay means that the target property will be set, and
            if the PropertyChanged event is raised by the source, the target property will be updated. TwoWay means that the source
            property will also be updated if the target raises the PropertyChanged event. Default means OneWay if only the source
            implements INPC, and TwoWay if both the source and the target implement INPC.</param>
            <param name="fallbackValue">Tthe value to use when the binding is unable to return a value. This can happen if one of the
            items on the Path (except the source property itself) is null, or if the Converter throws an exception.</param>
            <param name="targetNullValue">The value to use when the binding is unable to return a value. This can happen if one of the
            items on the Path (except the source property itself) is null, or if the Converter throws an exception.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertSourceToTarget(System.Func{`0,`1})">
            <summary>
            Defines a custom conversion method for a binding. To be used when the
            binding's source property is of a different type than the binding's
            target property, and the conversion cannot be done automatically (simple
            values).
            </summary>
            <param name="convert">A func that will be called with the source
            property's value, and will return the target property's value.
             IMPORTANT: Note that closures are not supported at the moment
            due to the use of WeakActions (see http://stackoverflow.com/questions/25730530/). </param>
            <returns>The Binding instance.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertTargetToSource(System.Func{`1,`0})">
            <summary>
            Defines a custom conversion method for a two-way binding. To be used when the
            binding's target property is of a different type than the binding's
            source property, and the conversion cannot be done automatically (simple
            values).
            </summary>
            <param name="convertBack">A func that will be called with the source
            property's value, and will return the target property's value.
             IMPORTANT: Note that closures are not supported at the moment
            due to the use of WeakActions (see http://stackoverflow.com/questions/25730530/). </param>
            <returns>The Binding instance.</returns>
            <remarks>This method is inactive on OneTime or OneWay bindings.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.Detach">
            <summary>
            Instructs the Binding instance to stop listening to value changes and to
            remove all listeneners.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ForceUpdateValueFromSourceToTarget">
            <summary>
            Forces the Binding's value to be reevaluated. The target value will
            be set to the source value.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ForceUpdateValueFromTargetToSource">
            <summary>
            Forces the Binding's value to be reevaluated. The source value will
            be set to the target value.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveSourceEvent(System.String)">
            <summary>
            Define when the binding should be evaluated when the bound source object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <param name="eventName">The name of the event that should be observed
            to update the binding's value.</param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime binding. Such bindings cannot be updated. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
            <exception cref="T:System.ArgumentNullException">When the eventName parameter is null
            or is an empty string.</exception>
            <exception cref="T:System.ArgumentException">When the requested event does not exist on the
            source control.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveSourceEvent``1(System.String)">
            <summary>
            Define when the binding should be evaluated when the bound source object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <remarks>Use this method when the event requires a specific EventArgs type
            instead of the standard EventHandler.</remarks>
            <typeparam name="TEventArgs">The type of the EventArgs used by this control's event.</typeparam>
            <param name="eventName">The name of the event that should be observed
            to update the binding's value.</param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime binding. Such bindings cannot be updated. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
            <exception cref="T:System.ArgumentNullException">When the eventName parameter is null
            or is an empty string.</exception>
            <exception cref="T:System.ArgumentException">When the requested event does not exist on the
            source control.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveTargetEvent(System.String)">
            <summary>
            Define when the binding should be evaluated when the bound target object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <param name="eventName">The name of the event that should be observed
            to update the binding's value.</param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime or a OneWay binding. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
            <exception cref="T:System.ArgumentNullException">When the eventName parameter is null
            or is an empty string.</exception>
            <exception cref="T:System.ArgumentException">When the requested event does not exist on the
            target control.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveTargetEvent``1(System.String)">
            <summary>
            Define when the binding should be evaluated when the bound target object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <remarks>Use this method when the event requires a specific EventArgs type
            instead of the standard EventHandler.</remarks>
            <typeparam name="TEventArgs">The type of the EventArgs used by this control's event.</typeparam>
            <param name="eventName">The name of the event that should be observed
            to update the binding's value.</param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime or OneWay binding. This exception can
            also be thrown when the target object is null or has already been
            garbage collected before this method is called.</exception>
            <exception cref="T:System.ArgumentNullException">When the eventName parameter is null
            or is an empty string.</exception>
            <exception cref="T:System.ArgumentException">When the requested event does not exist on the
            target control.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.WhenSourceChanges(System.Action)">
            <summary>
            Defines an action that will be executed every time that the binding value
            changes.
            </summary>
            <param name="callback">The action that will be executed when the binding changes.
            IMPORTANT: Note that closures are not supported at the moment
            due to the use of WeakActions (see http://stackoverflow.com/questions/25730530/). </param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When WhenSourceChanges is called on
            a binding which already has a target property set.</exception>
        </member>
        <member name="E:GalaSoft.MvvmLight.Helpers.Binding`2.ValueChanged">
            <summary>
            Occurs when the value of the databound property changes.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.Binding`2.PropertyChangedEventListener.InstanceReference">
            <summary>
            Gets a reference to the instance that this listener listens to.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.UpdateSourceTrigger(System.String)">
            <summary>
            Define when the binding should be evaluated when the bound source object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <param name="eventName">The name of the event that should be observed
            to update the binding's value.</param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime binding. Such bindings cannot be updated. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
            <exception cref="T:System.ArgumentNullException">When the eventName parameter is null
            or is an empty string.</exception>
            <exception cref="T:System.ArgumentException">When the requested event does not exist on the
            source control.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.UpdateSourceTrigger``1(System.String)">
            <summary>
            Define when the binding should be evaluated when the bound source object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <remarks>Use this method when the event requires a specific EventArgs type
            instead of the standard EventHandler.</remarks>
            <typeparam name="TEventArgs">The type of the EventArgs used by this control's event.</typeparam>
            <param name="eventName">The name of the event that should be observed
            to update the binding's value.</param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime binding. Such bindings cannot be updated. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
            <exception cref="T:System.ArgumentNullException">When the eventName parameter is null
            or is an empty string.</exception>
            <exception cref="T:System.ArgumentException">When the requested event does not exist on the
            source control.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.UpdateTargetTrigger(System.String)">
            <summary>
            Define when the binding should be evaluated when the bound target object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <param name="eventName">The name of the event that should be observed
            to update the binding's value.</param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime or a OneWay binding. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
            <exception cref="T:System.ArgumentNullException">When the eventName parameter is null
            or is an empty string.</exception>
            <exception cref="T:System.ArgumentException">When the requested event does not exist on the
            target control.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.UpdateTargetTrigger``1(System.String)">
            <summary>
            Define when the binding should be evaluated when the bound target object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <remarks>Use this method when the event requires a specific EventArgs type
            instead of the standard EventHandler.</remarks>
            <typeparam name="TEventArgs">The type of the EventArgs used by this control's event.</typeparam>
            <param name="eventName">The name of the event that should be observed
            to update the binding's value.</param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime or OneWay binding. This exception can
            also be thrown when the target object is null or has already been
            garbage collected before this method is called.</exception>
            <exception cref="T:System.ArgumentNullException">When the eventName parameter is null
            or is an empty string.</exception>
            <exception cref="T:System.ArgumentException">When the requested event does not exist on the
            target control.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.UpdateSourceTrigger">
            <summary>
            Define that the binding should be evaluated when the bound control's source property changes. 
            Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to specify that the binding must be updated when the property changes.
            </summary>
            <remarks>At this point, this method is inactive on iOS. Use
            <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveSourceEvent(System.String)"/> instead.</remarks>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime binding. Such bindings cannot be updated. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.UpdateSourceTrigger(GalaSoft.MvvmLight.Helpers.UpdateTriggerMode)">
            <summary>
            Define when the binding should be evaluated when the bound source object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <param name="mode">Defines the binding's update mode. Use 
            <see cref="F:GalaSoft.MvvmLight.Helpers.UpdateTriggerMode.LostFocus"/> to update the binding when
            the source control loses the focus. You can also use
            <see cref="F:GalaSoft.MvvmLight.Helpers.UpdateTriggerMode.PropertyChanged"/> to update the binding
            when the source control's property changes.
            NOTE: At this time the PropertyChanged mode is inactive on iOS. Use
            <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveSourceEvent(System.String)"/> instead.
            </param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime binding. Such bindings cannot be updated. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.UpdateTargetTrigger">
            <summary>
            Define that the binding should be evaluated when the bound control's target property changes. 
            Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to specify that the binding must be updated when the property changes.
            </summary>
            <remarks>At this point, this method is inactive on iOS. Use
            <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveTargetEvent(System.String)"/> instead.</remarks>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime or a OneWay binding. This exception can
            also be thrown when the target object is null or has already been
            garbage collected before this method is called.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.UpdateTargetTrigger(GalaSoft.MvvmLight.Helpers.UpdateTriggerMode)">
            <summary>
            Define when the binding should be evaluated when the bound target object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <param name="mode">Defines the binding's update mode. Use 
            <see cref="F:GalaSoft.MvvmLight.Helpers.UpdateTriggerMode.LostFocus"/> to update the binding when
            the source control loses the focus. You can also use
            <see cref="F:GalaSoft.MvvmLight.Helpers.UpdateTriggerMode.PropertyChanged"/> to update the binding
            when the source control's property changes.
            NOTE: At this time the PropertyChanged mode is inactive on iOS. Use
            <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveTargetEvent(System.String)"/> instead.
            </param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime or a OneWay binding. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveSourceEvent">
            <summary>
            Define that the binding should be evaluated when the bound control's source property changes. 
            Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to specify that the binding must be updated when the property changes.
            </summary>
            <remarks>At this point, this method is inactive on iOS. Use
            <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveSourceEvent(System.String)"/> instead.</remarks>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime binding. Such bindings cannot be updated. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveSourceEvent(GalaSoft.MvvmLight.Helpers.UpdateTriggerMode)">
            <summary>
            Define when the binding should be evaluated when the bound source object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <param name="mode">Defines the binding's update mode. Use 
            <see cref="F:GalaSoft.MvvmLight.Helpers.UpdateTriggerMode.LostFocus"/> to update the binding when
            the source control loses the focus. You can also use
            <see cref="F:GalaSoft.MvvmLight.Helpers.UpdateTriggerMode.PropertyChanged"/> to update the binding
            when the source control's property changes.
            NOTE: At this time the PropertyChanged mode is inactive on iOS. Use
            <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveSourceEvent(System.String)"/> instead.
            </param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime binding. Such bindings cannot be updated. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveTargetEvent">
            <summary>
            Define that the binding should be evaluated when the bound control's target property changes. 
            Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to specify that the binding must be updated when the property changes.
            </summary>
            <remarks>At this point, this method is inactive on iOS. Use
            <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveTargetEvent(System.String)"/> instead.</remarks>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime or a OneWay binding. This exception can
            also be thrown when the target object is null or has already been
            garbage collected before this method is called.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveTargetEvent(GalaSoft.MvvmLight.Helpers.UpdateTriggerMode)">
            <summary>
            Define when the binding should be evaluated when the bound target object
            is a control. Because Xamarin controls are not DependencyObjects, the
            bound property will not automatically update the binding attached to it. Instead,
            use this method to define which of the control's events should be observed.
            </summary>
            <param name="mode">Defines the binding's update mode. Use 
            <see cref="F:GalaSoft.MvvmLight.Helpers.UpdateTriggerMode.LostFocus"/> to update the binding when
            the source control loses the focus. You can also use
            <see cref="F:GalaSoft.MvvmLight.Helpers.UpdateTriggerMode.PropertyChanged"/> to update the binding
            when the source control's property changes.
            NOTE: At this time the PropertyChanged mode is inactive on iOS. Use
            <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ObserveTargetEvent(System.String)"/> instead.
            </param>
            <returns>The Binding instance.</returns>
            <exception cref="T:System.InvalidOperationException">When this method is called
            on a OneTime or a OneWay binding. This exception can
            also be thrown when the source object is null or has already been
            garbage collected before this method is called.</exception>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.BindingMode">
            <summary>
            The mode of the <see cref="T:GalaSoft.MvvmLight.Helpers.Binding`2"/>.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Helpers.BindingMode.Default">
            <summary>
            A default binding is a one way binding.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Helpers.BindingMode.OneTime">
            <summary>
            A one time binding. The binding's value will be set when the
            binding is created but subsequent changes will be ignored/
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Helpers.BindingMode.OneWay">
            <summary>
            A one way binding, where the changes to the source
            property will update the target property, but changes to the
            target property don't affect the source property.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Helpers.BindingMode.TwoWay">
            <summary>
            A two way binding, where the changes to the source
            property will update the target property, and vice versa.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.Extensions">
            <summary>
            Defines extension methods used to add data bindings and commands between Xamarin
            Android and iOS elements.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetBinding``2(System.Object,System.Linq.Expressions.Expression{System.Func{``0}},System.Object,System.Linq.Expressions.Expression{System.Func{``1}},GalaSoft.MvvmLight.Helpers.BindingMode,``0,``0)">
            <summary>
            Sets a data binding between two properties. If the source implements INotifyPropertyChanged, the source property raises the PropertyChanged event
            and the BindingMode is OneWay or TwoWay, the target property will be synchronized with the source property. If
            the target implements INotifyPropertyChanged, the target property raises the PropertyChanged event and the BindingMode is
            TwoWay, the source property will also be synchronized with the target property.
            </summary>
            <remarks>This class allows for a different TSource and TTarget and is able to perform simple
            type conversions automatically. This is useful if the source property and the target
            property are of different type.
            If the type conversion is complex, please use the <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertSourceToTarget(System.Func{`0,`1})"/>
            and <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertTargetToSource(System.Func{`1,`0})"/> methods to configure the binding.
            It is very possible that TSource and TTarget are the same type in which case no conversion occurs.</remarks>
            <typeparam name="TSource">The type of the property that is being databound before conversion.</typeparam>
            <typeparam name="TTarget">The type of the property that is being databound after conversion.</typeparam>
            <param name="target">The target of the binding. If this object implements INotifyPropertyChanged and the
            BindingMode is TwoWay, the source will be notified of changes to the source property.</param>
            <param name="targetPropertyExpression">An expression pointing to the target property. It can be
            a simple expression "() => [target].MyProperty" or a composed expression "() => [target].SomeObject.SomeOtherObject.SomeProperty".</param>
            <param name="source">The source of the binding. If this object implements INotifyPropertyChanged and the
            BindingMode is OneWay or TwoWay, the target will be notified of changes to the target property.</param>
            <param name="sourcePropertyExpression">An expression pointing to the source property. It can be
            a simple expression "() => [source].MyProperty" or a composed expression "() => [source].SomeObject.SomeOtherObject.SomeProperty".</param>
            <param name="mode">The mode of the binding. OneTime means that the target property will be set once (when the binding is
            created) but that subsequent changes will be ignored. OneWay means that the target property will be set, and
            if the PropertyChanged event is raised by the source, the target property will be updated. TwoWay means that the source
            property will also be updated if the target raises the PropertyChanged event. Default means OneWay if only the source
            implements INPC, and TwoWay if both the source and the target implement INPC.</param>
            <param name="fallbackValue">The value to use when the binding is unable to return a value. This can happen if one of the
            items on the Path (except the source property itself) is null, or if the Converter throws an exception.</param>
            <param name="targetNullValue">The value used when the source property is null (or equals to default(TSource)).</param>
            <returns>The new Binding instance.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetBinding``1(System.Object,System.Linq.Expressions.Expression{System.Func{``0}},GalaSoft.MvvmLight.Helpers.BindingMode,``0,``0)">
            <summary>
            Creates a <see cref="T:GalaSoft.MvvmLight.Helpers.Binding`2"/> with a source property but without a target.
            This type of bindings is useful for the <see cref="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``1(System.Object,System.String,GalaSoft.MvvmLight.Command.RelayCommand{``0},GalaSoft.MvvmLight.Helpers.Binding)"/>,
            <see cref="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``1(System.Object,GalaSoft.MvvmLight.Command.RelayCommand{``0},GalaSoft.MvvmLight.Helpers.Binding)"/>,
            <see cref="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``2(System.Object,System.String,GalaSoft.MvvmLight.Command.RelayCommand{``0},GalaSoft.MvvmLight.Helpers.Binding)"/>
            and <see cref="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``2(System.Object,GalaSoft.MvvmLight.Command.RelayCommand{``0},GalaSoft.MvvmLight.Helpers.Binding)"/> methods, to use as CommandParameter
            binding.
            </summary>
            <param name="source">The source of the binding. If this object implements INotifyPropertyChanged and the
            BindingMode is OneWay or TwoWay, the target will be notified of changes to the target property.</param>
            <param name="sourcePropertyExpression">An expression pointing to the source property. It can be
            a simple expression "() => [source].MyProperty" or a composed expression "() => [source].SomeObject.SomeOtherObject.SomeProperty".</param>
            <param name="mode">The mode of the binding. OneTime means that the target property will be set once (when the binding is
            created) but that subsequent changes will be ignored. OneWay means that the target property will be set, and
            if the PropertyChanged event is raised by the source, the target property will be updated. TwoWay means that the source
            property will also be updated if the target raises the PropertyChanged event. Default means OneWay if only the source
            implements INPC, and TwoWay if both the source and the target implement INPC.</param>
            <param name="fallbackValue">The value to use when the binding is unable to return a value. This can happen if one of the
            items on the Path (except the source property itself) is null, or if the Converter throws an exception.</param>
            <param name="targetNullValue">The value used when the source property is null (or equals to default(TSource)).</param>
            <typeparam name="TSource">The type of the bound property.</typeparam>
            <returns>The created binding instance.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetBinding``2(System.Object,System.Linq.Expressions.Expression{System.Func{``0}},System.Linq.Expressions.Expression{System.Func{``1}},GalaSoft.MvvmLight.Helpers.BindingMode,``0,``0)">
            <summary>
            Sets a data binding between two properties of the same object. If the source implements INotifyPropertyChanged, has observable properties
            and the BindingMode is OneWay or TwoWay, the target property will be notified of changes to the source property. If
            the target implements INotifyPropertyChanged, has observable properties and the BindingMode is
            TwoWay, the source will also be notified of changes to the target's properties.
            </summary>
            <typeparam name="TSource">The type of the source property that is being databound.</typeparam>
            <typeparam name="TTarget">The type of the target property that is being databound. If the source type
            is not the same as the target type, an automatic conversion will be attempted. However only
            simple types can be converted. For more complex conversions, use the <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertSourceToTarget(System.Func{`0,`1})"/>
            and <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertTargetToSource(System.Func{`1,`0})"/> methods to define custom converters.</typeparam>
            <param name="targetPropertyExpression">An expression pointing to the target property. It can be
            a simple expression "() => [target].MyProperty" or a composed expression "() => [target].SomeObject.SomeOtherObject.SomeProperty".</param>
            <param name="source">The source of the binding. If this object implements INotifyPropertyChanged and the
            BindingMode is OneWay or TwoWay, the target will be notified of changes to the target property.</param>
            <param name="sourcePropertyExpression">An expression pointing to the source property. It can be
            a simple expression "() => [source].MyProperty" or a composed expression "() => [source].SomeObject.SomeOtherObject.SomeProperty".</param>
            <param name="mode">The mode of the binding. OneTime means that the target property will be set once (when the binding is
            created) but that subsequent changes will be ignored. OneWay means that the target property will be set, and
            if the PropertyChanged event is raised by the source, the target property will be updated. TwoWay means that the source
            property will also be updated if the target raises the PropertyChanged event. Default means OneWay if only the source
            implements INPC, and TwoWay if both the source and the target implement INPC.</param>
            <param name="fallbackValue">The value to use when the binding is unable to return a value. This can happen if one of the
            items on the Path (except the source property itself) is null, or if the Converter throws an exception.</param>
            <param name="targetNullValue">The value used when the source property is null (or equals to default(TSource)).</param>
            <returns>The new Binding instance.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetBinding``2(System.Object,System.String,System.Object,System.String,GalaSoft.MvvmLight.Helpers.BindingMode,``0,``0)">
            <summary>
            Sets a data binding between two properties. If the source implements INotifyPropertyChanged, the source property raises the PropertyChanged event
            and the BindingMode is OneWay or TwoWay, the target property will be synchronized with the source property. If
            the target implements INotifyPropertyChanged, the target property raises the PropertyChanged event and the BindingMode is
            TwoWay, the source property will also be synchronized with the target property.
            </summary>
            <typeparam name="TSource">The type of the source property that is being databound.</typeparam>
            <typeparam name="TTarget">The type of the target property that is being databound. If the source type
            is not the same as the target type, an automatic conversion will be attempted. However only
            simple types can be converted. For more complex conversions, use the <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertSourceToTarget(System.Func{`0,`1})"/>
            and <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertTargetToSource(System.Func{`1,`0})"/> methods to define custom converters.</typeparam>
            <param name="target">The target of the binding. If this object implements INotifyPropertyChanged and the
            BindingMode is TwoWay, the source will be notified of changes to the source property.</param>
            <param name="targetPropertyName">The name of the target property. This must be a simple name, without dots.</param>
            <param name="source">The source of the binding. If this object implements INotifyPropertyChanged and the
            BindingMode is OneWay or TwoWay, the target will be notified of changes to the target property.</param>
            <param name="sourcePropertyName">The name of the source property. This must be a simple name, without dots.</param>
            <param name="mode">The mode of the binding. OneTime means that the target property will be set once (when the binding is
            created) but that subsequent changes will be ignored. OneWay means that the target property will be set, and
            if the PropertyChanged event is raised by the source, the target property will be updated. TwoWay means that the source
            property will also be updated if the target raises the PropertyChanged event. Default means OneWay if only the source
            implements INPC, and TwoWay if both the source and the target implement INPC.</param>
            <param name="fallbackValue">The value to use when the binding is unable to return a value. This can happen if one of the
            items on the Path (except the source property itself) is null, or if the Converter throws an exception.</param>
            <param name="targetNullValue">The value used when the source property is null (or equals to default(TSource)).</param>
            <returns>The new Binding instance.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetBinding``2(System.Object,System.String,System.String,GalaSoft.MvvmLight.Helpers.BindingMode,``0,``0)">
            <summary>
            Sets a data binding between two properties of the same object. If the source implements INotifyPropertyChanged, has observable properties
            and the BindingMode is OneWay or TwoWay, the target property will be notified of changes to the source property. If
            the target implements INotifyPropertyChanged, has observable properties and the BindingMode is
            TwoWay, the source will also be notified of changes to the target's properties.
            </summary>
            <typeparam name="TSource">The type of the source property that is being databound.</typeparam>
            <typeparam name="TTarget">The type of the target property that is being databound. If the source type
            is not the same as the target type, an automatic conversion will be attempted. However only
            simple types can be converted. For more complex conversions, use the <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertSourceToTarget(System.Func{`0,`1})"/>
            and <see cref="M:GalaSoft.MvvmLight.Helpers.Binding`2.ConvertTargetToSource(System.Func{`1,`0})"/> methods to define custom converters.</typeparam>
            <param name="targetPropertyName">The name of the target property. This must be a simple name, without dots.</param>
            <param name="source">The source of the binding. If this object implements INotifyPropertyChanged and the
            BindingMode is OneWay or TwoWay, the target will be notified of changes to the target property.</param>
            <param name="sourcePropertyName">The name of the source property. This must be a simple name, without dots.</param>
            <param name="mode">The mode of the binding. OneTime means that the target property will be set once (when the binding is
            created) but that subsequent changes will be ignored. OneWay means that the target property will be set, and
            if the PropertyChanged event is raised by the source, the target property will be updated. TwoWay means that the source
            property will also be updated if the target raises the PropertyChanged event. Default means OneWay if only the source
            implements INPC, and TwoWay if both the source and the target implement INPC.</param>
            <param name="fallbackValue">The value to use when the binding is unable to return a value. This can happen if one of the
            items on the Path (except the source property itself) is null, or if the Converter throws an exception.</param>
            <param name="targetNullValue">The value used when the source property is null (or equals to default(TSource)).</param>
            <returns>The new Binding instance.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``1(System.Object,System.String,GalaSoft.MvvmLight.Command.RelayCommand{``0},GalaSoft.MvvmLight.Helpers.Binding)">
            <summary>
            Sets a generic RelayCommand to an object and actuates the command when a specific event is raised. This method
            can only be used when the event uses a standard EventHandler. 
            </summary>
            <typeparam name="T">The type of the CommandParameter that will be passed to the RelayCommand.</typeparam>
            <param name="element">The element to which the command is added.</param>
            <param name="eventName">The name of the event that will be subscribed to to actuate the command.</param>
            <param name="command">The command that must be added to the element.</param>
            <param name="commandParameterBinding">A <see cref="T:GalaSoft.MvvmLight.Helpers.Binding`2">Binding</see> instance subscribed to
            the CommandParameter that will passed to the RelayCommand. Depending on the Binding, the CommandParameter
            will be observed and changes will be passed to the command, for example to update the CanExecute.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``2(System.Object,System.String,GalaSoft.MvvmLight.Command.RelayCommand{``0},GalaSoft.MvvmLight.Helpers.Binding)">
            <summary>
            Sets a generic RelayCommand to an object and actuates the command when a specific event is raised. This method
            should be used when the event uses an EventHandler&lt;TEventArgs&gt;.
            </summary>
            <typeparam name="T">The type of the CommandParameter that will be passed to the RelayCommand.</typeparam>
            <typeparam name="TEventArgs">The type of the event's arguments.</typeparam>
            <param name="element">The element to which the command is added.</param>
            <param name="command">The command that must be added to the element.</param>
            <param name="eventName">The name of the event that will be subscribed to to actuate the command.</param>
            <param name="commandParameterBinding">A <see cref="T:GalaSoft.MvvmLight.Helpers.Binding`2">Binding</see> instance subscribed to
            the CommandParameter that will passed to the RelayCommand. Depending on the Binding, the CommandParameter
            will be observed and changes will be passed to the command, for example to update the CanExecute.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand(System.Object,System.String,System.Windows.Input.ICommand)">
            <summary>
            Sets a non-generic RelayCommand to an object and actuates the command when a specific event is raised. This method
            can only be used when the event uses a standard EventHandler. 
            </summary>
            <param name="element">The element to which the command is added.</param>
            <param name="eventName">The name of the event that will be subscribed to to actuate the command.</param>
            <param name="command">The command that must be added to the element.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``1(System.Object,System.String,System.Windows.Input.ICommand)">
            <summary>
            Sets a non-generic RelayCommand to an object and actuates the command when a specific event is raised. This method
            should be used when the event uses an EventHandler&lt;TEventArgs&gt;.
            </summary>
            <typeparam name="TEventArgs">The type of the event's arguments.</typeparam>
            <param name="element">The element to which the command is added.</param>
            <param name="eventName">The name of the event that will be subscribed to to actuate the command.</param>
            <param name="command">The command that must be added to the element.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``1(System.Object,System.String,GalaSoft.MvvmLight.Command.RelayCommand{``0},``0)">
            <summary>
            Sets a generic RelayCommand to an object and actuates the command when a specific event is raised. This method
            can only be used when the event uses a standard EventHandler. 
            </summary>
            <typeparam name="T">The type of the CommandParameter that will be passed to the RelayCommand.</typeparam>
            <param name="element">The element to which the command is added.</param>
            <param name="command">The command that must be added to the element.</param>
            <param name="eventName">The name of the event that will be subscribed to to actuate the command.</param>
            <param name="commandParameter">The command parameter that will be passed to the RelayCommand when it
            is executed. This is a fixed value. To pass an observable value, use one of the SetCommand
            overloads that uses a Binding as CommandParameter.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``2(System.Object,System.String,GalaSoft.MvvmLight.Command.RelayCommand{``0},``0)">
            <summary>
            Sets a generic RelayCommand to an object and actuates the command when a specific event is raised. This method
            should be used when the event uses an EventHandler&lt;TEventArgs&gt;.
            </summary>
            <typeparam name="T">The type of the CommandParameter that will be passed to the RelayCommand.</typeparam>
            <typeparam name="TEventArgs">The type of the event's arguments.</typeparam>
            <param name="element">The element to which the command is added.</param>
            <param name="command">The command that must be added to the element.</param>
            <param name="eventName">The name of the event that will be subscribed to to actuate the command.</param>
            <param name="commandParameter">The command parameter that will be passed to the RelayCommand when it
            is executed. This is a fixed value. To pass an observable value, use one of the SetCommand
            overloads that uses a Binding as CommandParameter.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``1(System.Object,GalaSoft.MvvmLight.Command.RelayCommand{``0},GalaSoft.MvvmLight.Helpers.Binding)">
            <summary>
            Sets a generic RelayCommand to an object and actuates the command when a specific event is raised. This method
            can only be used when the event uses a standard EventHandler.
            This method does not specify the observed event explicitly. The following events are used:
            - For UIBarButtonItem: Clicked.
            - For UIButton: TouchUpInside.
            - At the moment, no other controls are supported. For other controls, use another SetCommand overload
            and specify the eventName parameter explicitly.
            </summary>
            <typeparam name="T">The type of the CommandParameter that will be passed to the RelayCommand.</typeparam>
            <param name="element">The element to which the command is added.</param>
            <param name="command">The command that must be added to the element.</param>
            <param name="commandParameterBinding">A <see cref="T:GalaSoft.MvvmLight.Helpers.Binding`2">Binding</see> instance subscribed to
            the CommandParameter that will passed to the RelayCommand. Depending on the Binding, the CommandParameter
            will be observed and changes will be passed to the command, for example to update the CanExecute.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``2(System.Object,GalaSoft.MvvmLight.Command.RelayCommand{``0},GalaSoft.MvvmLight.Helpers.Binding)">
            <summary>
            Sets a generic RelayCommand to an object and actuates the command when a specific event is raised. This method
            should be used when the event uses an EventHandler&lt;TEventArgs&gt;.
            This method does not specify the observed event explicitly. The following events are used:
            - For UIBarButtonItem: Clicked.
            - For UIButton: TouchUpInside.
            - At the moment, no other controls are supported. For other controls, use another SetCommand overload
            and specify the eventName parameter explicitly.
            </summary>
            <typeparam name="T">The type of the CommandParameter that will be passed to the RelayCommand.</typeparam>
            <typeparam name="TEventArgs">The type of the event's arguments.</typeparam>
            <param name="element">The element to which the command is added.</param>
            <param name="command">The command that must be added to the element.</param>
            <param name="commandParameterBinding">A <see cref="T:GalaSoft.MvvmLight.Helpers.Binding`2">Binding</see> instance subscribed to
            the CommandParameter that will passed to the RelayCommand. Depending on the Binding, the CommandParameter
            will be observed and changes will be passed to the command, for example to update the CanExecute.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand(System.Object,System.Windows.Input.ICommand)">
            <summary>
            Sets an ICommand to an object and actuates the command when a specific event is raised. This method
            can only be used when the event uses a standard EventHandler. 
            This method does not specify the observed event explicitly. The following events are used:
            - For UIBarButtonItem: Clicked.
            - For UIButton: TouchUpInside.
            - At the moment, no other controls are supported. For other controls, use another SetCommand overload
            and specify the eventName parameter explicitly.
            </summary>
            <param name="element">The element to which the command is added.</param>
            <param name="command">The command that must be added to the element.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``1(System.Object,System.Windows.Input.ICommand)">
            <summary>
            Sets an ICommand to an object and actuates the command when a specific event is raised. This method
            should be used when the event uses an EventHandler&lt;TEventArgs&gt;.
            This method does not specify the observed event explicitly. The following events are used:
            - For UIBarButtonItem: Clicked.
            - For UIButton: TouchUpInside.
            - At the moment, no other controls are supported. For other controls, use another SetCommand overload
            and specify the eventName parameter explicitly.
            </summary>
            <typeparam name="TEventArgs">The type of the event's arguments.</typeparam>
            <param name="element">The element to which the command is added.</param>
            <param name="command">The command that must be added to the element.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``1(System.Object,GalaSoft.MvvmLight.Command.RelayCommand{``0},``0)">
            <summary>
            Sets a generic RelayCommand to an object and actuates the command when a specific event is raised. This method
            can only be used when the event uses a standard EventHandler.
            This method does not specify the observed event explicitly. The following events are used:
            - For UIBarButtonItem: Clicked.
            - For UIButton: TouchUpInside.
            - At the moment, no other controls are supported. For other controls, use another SetCommand overload
            and specify the eventName parameter explicitly.
            </summary>
            <typeparam name="T">The type of the CommandParameter that will be passed to the RelayCommand.</typeparam>
            <param name="element">The element to which the command is added.</param>
            <param name="command">The command that must be added to the element.</param>
            <param name="commandParameter">The command parameter that will be passed to the RelayCommand when it
            is executed. This is a fixed value. To pass an observable value, use one of the SetCommand
            overloads that uses a Binding as CommandParameter.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.Extensions.SetCommand``2(System.Object,GalaSoft.MvvmLight.Command.RelayCommand{``0},``0)">
            <summary>
            Sets a generic RelayCommand to an object and actuates the command when a specific event is raised. This method
            should be used when the event uses an EventHandler&lt;TEventArgs&gt;.
            This method does not specify the observed event explicitly. The following events are used:
            - For UIBarButtonItem: Clicked.
            - For UIButton: TouchUpInside.
            - At the moment, no other controls are supported. For other controls, use another SetCommand overload
            and specify the eventName parameter explicitly.
            </summary>
            <typeparam name="T">The type of the CommandParameter that will be passed to the RelayCommand.</typeparam>
            <typeparam name="TEventArgs">The type of the event's arguments.</typeparam>
            <param name="element">The element to which the command is added.</param>
            <param name="command">The command that must be added to the element.</param>
            <param name="commandParameter">The command parameter that will be passed to the RelayCommand when it
            is executed. This is a fixed value. To pass an observable value, use one of the SetCommand
            overloads that uses a Binding as CommandParameter.</param>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.UpdateTriggerMode">
            <summary>
            Defines how a <see cref="T:GalaSoft.MvvmLight.Helpers.Binding"/> is updated by a source control.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Helpers.UpdateTriggerMode.LostFocus">
            <summary>
            Defines that the binding should be updated when the control
            loses the focus.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Helpers.UpdateTriggerMode.PropertyChanged">
            <summary>
            Defines that the binding should be updated when the control's 
            bound property changes.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.ExtensionsApple">
            <summary>
            Defines extension methods for iOS only.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ExtensionsApple.GetCollectionViewSource``2(System.Collections.Generic.IList{``0},System.Action{``1,``0,Foundation.NSIndexPath},System.Func{Foundation.NSString,Foundation.NSIndexPath,UIKit.UICollectionReusableView},System.String,System.Func{GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource{``0,``1}})">
            <summary>
            Creates a new <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2"/> for a given <see cref="T:System.Collections.Generic.IList`1"/>.
            Note that if the IList doesn't implement INotifyCollectionChanged, the associated UICollectionView won't be 
            updated when the IList changes.
            </summary>
            <typeparam name="TItem">The type of the items in the IList.</typeparam>
            <typeparam name="TCell">The type of cells in the CollectionView associated to this ObservableCollectionViewSource.</typeparam>
            <param name="list">The IList that should be represented in the associated UICollectionView</param>
            <param name="bindCellDelegate">A delegate to a method taking a <see cref="T:UIKit.UICollectionViewCell"/>
            and setting its elements' properties according to the item passed as second parameter.</param>
            <param name="getSupplementaryViewDelegate">A delegate to a method returning a <see cref="T:UIKit.UICollectionReusableView"/>
            and used to set supplementary views on the UICollectionView.</param>
            <param name="reuseId">An ID used for optimization and cell reuse.</param>
            <param name="factory">An optional delegate returning an instance of a class deriving from
            <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2"/>. This can be used if you need to implement
            specific features in addition to the built-in features of ObservableCollectionViewSource.</param>
            <returns>The new instance of ObservableCollectionViewSource.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ExtensionsApple.GetCollectionViewSource``2(System.Collections.ObjectModel.ObservableCollection{``0},System.Action{``1,``0,Foundation.NSIndexPath},System.Func{Foundation.NSString,Foundation.NSIndexPath,UIKit.UICollectionReusableView},System.String,System.Func{GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource{``0,``1}})">
            <summary>
            Creates a new <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2"/> for a given <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/>.
            The associated UICollectionView will be updated when the ObservableCollection changes.
            </summary>
            <typeparam name="TItem">The type of the items in the IList.</typeparam>
            <typeparam name="TCell">The type of cells in the CollectionView associated to this ObservableCollectionViewSource.</typeparam>
            <param name="list">The ObservableCollection that should be represented in the associated UICollectionView</param>
            <param name="bindCellDelegate">A delegate to a method taking a <see cref="T:UIKit.UICollectionViewCell"/>
            and setting its elements' properties according to the item passed as second parameter.</param>
            <param name="getSupplementaryViewDelegate">A delegate to a method returing a <see cref="T:UIKit.UICollectionReusableView"/>
            and used to set supplementary views on the UICollectionView.</param>
            <param name="reuseId">An ID used for optimization and cell reuse.</param>
            <param name="factory">An optional delegate returning an instance of a class deriving from
            <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2"/>. This can be used if you need to implement
            specific features in addition to the built-in features of ObservableCollectionViewSource.</param>
            <returns>The new instance of ObservableCollectionViewSource.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ExtensionsApple.GetController``1(System.Collections.ObjectModel.ObservableCollection{``0},System.Func{Foundation.NSString,UIKit.UITableViewCell},System.Action{UIKit.UITableViewCell,``0,Foundation.NSIndexPath},System.String)">
            <summary>
            Creates a new <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1"/> for a given <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/>.
            </summary>
            <typeparam name="TItem">The type of the items contained in the collection.</typeparam>
            <param name="collection">The collection that the adapter will be created for.</param>
            <param name="createCellDelegate">A delegate to a method creating or reusing a <see cref="T:UIKit.UITableViewCell"/>.
            The cell will then be passed to the bindCellDelegate delegate to set the elements' properties.
            If you use a reuseId, you can pass null for the createCellDelegate.</param>
            <param name="bindCellDelegate">A delegate to a method taking a <see cref="T:UIKit.UITableViewCell"/>
            and setting its elements' properties according to the item passed as second parameter.
            The cell must be created first in the createCellDelegate delegate, unless a
            reuseId is passed to the method.</param>
            <param name="reuseId">A reuse identifier for the TableView's cells.</param>
            <returns>A controller adapted to the collection passed in parameter.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ExtensionsApple.GetController``1(System.Collections.Generic.IList{``0},System.Func{Foundation.NSString,UIKit.UITableViewCell},System.Action{UIKit.UITableViewCell,``0,Foundation.NSIndexPath},System.String)">
            <summary>
            Creates a new <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1"/> for a given <see cref="T:System.Collections.Generic.IList`1"/>.
            </summary>
            <typeparam name="TItem">The type of the items contained in the list.</typeparam>
            <param name="list">The list that the adapter will be created for.</param>
            <param name="createCellDelegate">A delegate to a method creating or reusing a <see cref="T:UIKit.UITableViewCell"/>.
            The cell will then be passed to the bindCellDelegate delegate to set the elements' properties.
            If you use a reuseId, you can pass null for the createCellDelegate.</param>
            <param name="bindCellDelegate">A delegate to a method taking a <see cref="T:UIKit.UITableViewCell"/>
            and setting its elements' properties according to the item passed as second parameter.
            The cell must be created first in the createCellDelegate delegate, unless a reuseId is 
            passed to the method.</param>
            <param name="reuseId">A reuse identifier for the TableView's cells.</param>
            <returns>A controller adapted to the collection passed in parameter.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ExtensionsApple.GetTableViewSource``1(System.Collections.Generic.IList{``0},System.Action{UIKit.UITableViewCell,``0,Foundation.NSIndexPath},System.String,System.Func{GalaSoft.MvvmLight.Helpers.ObservableTableViewSource{``0}})">
            <summary>
            Creates a new <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1"/> for a given <see cref="T:System.Collections.Generic.IList`1"/>.
            Note that if the IList doesn't implement INotifyCollectionChanged, the associated UITableView won't be 
            updated when the IList changes.
            </summary>
            <typeparam name="TItem">The type of the items in the IList.</typeparam>
            <param name="list">The IList that should be represented in the associated UITableView</param>
            <param name="bindCellDelegate">A delegate to a method taking a <see cref="T:UIKit.UITableViewCell"/>
            and setting its elements' properties according to the item passed as second parameter.</param>
            <param name="reuseId">An ID used for optimization and cell reuse.</param>
            <param name="factory">An optional delegate returning an instance of a class deriving from
            <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1"/>. This can be used if you need to implement
            specific features in addition to the built-in features of ObservableTableViewSource.</param>
            <returns>The new instance of ObservableTableViewSource.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ExtensionsApple.GetTableViewSource``1(System.Collections.ObjectModel.ObservableCollection{``0},System.Action{UIKit.UITableViewCell,``0,Foundation.NSIndexPath},System.String,System.Func{GalaSoft.MvvmLight.Helpers.ObservableTableViewSource{``0}})">
            <summary>
            Creates a new <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1"/> for a given <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/>.
            The associated UITableView will be updated when the ObservableCollection changes.
            </summary>
            <typeparam name="TItem">The type of the items in the IList.</typeparam>
            <param name="list">The ObservableCollection that should be represented in the associated UITableView</param>
            <param name="bindCellDelegate">A delegate to a method taking a <see cref="T:UIKit.UITableViewCell"/>
            and setting its elements' properties according to the item passed as second parameter.</param>
            <param name="reuseId">An ID used for optimization and cell reuse.</param>
            <param name="factory">An optional delegate returning an instance of a class deriving from
            <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1"/>. This can be used if you need to implement
            specific features in addition to the built-in features of ObservableTableViewSource.</param>
            <returns>The new instance of ObservableTableViewSource.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ExtensionsApple.GetTableViewSource``1(System.Collections.Generic.IList{``0},System.Func{Foundation.NSString,UIKit.UITableViewCell},System.Action{UIKit.UITableViewCell,``0,Foundation.NSIndexPath},System.String,System.Func{GalaSoft.MvvmLight.Helpers.ObservableTableViewSource{``0}})">
            <summary>
            Creates a new <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1"/> for a given <see cref="T:System.Collections.Generic.IList`1"/>.
            Note that if the IList doesn't implement INotifyCollectionChanged, the associated UITableView won't be 
            updated when the IList changes.
            </summary>
            <typeparam name="TItem">The type of the items in the IList.</typeparam>
            <param name="list">The IList that should be represented in the associated UITableView</param>
            <param name="createCellDelegate">A delegate to a method creating or reusing a <see cref="T:UIKit.UITableViewCell"/>.
            The cell will then be passed to the bindCellDelegate delegate to set the elements' properties.
            Use this method only if you don't want to register with the UITableView.RegisterClassForCellReuse method
            for cell reuse.</param>
            <param name="bindCellDelegate">A delegate to a method taking a <see cref="T:UIKit.UITableViewCell"/>
            and setting its elements' properties according to the item passed as second parameter.</param>
            <param name="reuseId">An ID used for optimization and cell reuse.</param>
            <param name="factory">An optional delegate returning an instance of a class deriving from
            <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1"/>. This can be used if you need to implement
            specific features in addition to the built-in features of ObservableTableViewSource.</param>
            <returns>The new instance of ObservableTableViewSource.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ExtensionsApple.GetTableViewSource``1(System.Collections.ObjectModel.ObservableCollection{``0},System.Func{Foundation.NSString,UIKit.UITableViewCell},System.Action{UIKit.UITableViewCell,``0,Foundation.NSIndexPath},System.String,System.Func{GalaSoft.MvvmLight.Helpers.ObservableTableViewSource{``0}})">
            <summary>
            Creates a new <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1"/> for a given <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/>.
            The associated UITableView will be updated when the ObservableCollection changes.
            </summary>
            <typeparam name="TItem">The type of the items in the IList.</typeparam>
            <param name="list">The ObservableCollection that should be represented in the associated UITableView</param>
            <param name="bindCellDelegate">A delegate to a method taking a <see cref="T:UIKit.UITableViewCell"/>
            and setting its elements' properties according to the item passed as second parameter.</param>
            <param name="createCellDelegate">A delegate to a method creating or reusing a <see cref="T:UIKit.UITableViewCell"/>.
            The cell will then be passed to the bindCellDelegate delegate to set the elements' properties.
            Use this method only if you don't want to register with the UITableView.RegisterClassForCellReuse method
            for cell reuse.</param>
            <param name="reuseId">An ID used for optimization and cell reuse.</param>
            <param name="factory">An optional delegate returning an instance of a class deriving from
            <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1"/>. This can be used if you need to implement
            specific features in addition to the built-in features of ObservableTableViewSource.</param>
            <returns>The new instance of ObservableTableViewSource.</returns>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1">
            <summary>
            A <see cref="T:UIKit.UITableViewController"/> that can be used as an iOS view. After setting
            the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.DataSource"/> and the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.BindCellDelegate"/> and <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.CreateCellDelegate"/> 
            properties, the controller can be loaded. If the DataSource is an <see cref="T:System.Collections.Specialized.INotifyCollectionChanged"/>,
            changes to the collection will be observed and the UI will automatically be updated.
            </summary>
            <remarks>Credits go to Frank A Krueger for the initial idea and the inspiration
            for this class. Frank gracefully accepted to let me add his code (with a few changes)
            to MVVM Light.
            <para>https://gist.github.com/praeclarum/10024108</para>
            </remarks>
            <typeparam name="T">The type of the items contained in the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.DataSource"/>.</typeparam>
        </member>
        <member name="F:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.SelectedItemPropertyName">
            <summary>
            The <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.SelectedItem" /> property's name.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.AddAnimation">
            <summary>
            When set, specifies which animation should be used when rows change.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.BindCellDelegate">
            <summary>
            A delegate to a method taking a <see cref="T:UIKit.UITableViewCell"/>
            and setting its elements' properties according to the item
            passed as second parameter.
            The cell must be created first in the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.CreateCellDelegate"/>
            delegate.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.CreateCellDelegate">
            <summary>
            A delegate to a method creating or reusing a <see cref="T:UIKit.UITableViewCell"/>.
            The cell will then be passed to the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.BindCellDelegate"/>
            delegate to set the elements' properties.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.DataSource">
            <summary>
            The data source of this list controller.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.DeleteAnimation">
            <summary>
            When set, specifieds which animation should be used when a row is deleted.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.GetHeightForFooterDelegate">
            <summary>
            When set, returns the height of the view that will be used for the TableView's footer.
            </summary>
            <seealso cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.GetViewForFooterDelegate"/>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.GetHeightForHeaderDelegate">
            <summary>
            When set, returns the height of the view that will be used for the TableView's header.
            </summary>
            <seealso cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.GetViewForHeaderDelegate"/>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.GetViewForFooterDelegate">
            <summary>
            When set, returns a view that can be used as the TableView's footer.
            </summary>
            <seealso cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.GetHeightForFooterDelegate"/>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.GetViewForHeaderDelegate">
            <summary>
            When set, returns a view that can be used as the TableView's header.
            </summary>
            <seealso cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.GetHeightForHeaderDelegate"/>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ReuseId">
            <summary>
            A reuse identifier for the TableView's cells.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.SelectedItem">
            <summary>
            Gets the TableView's selected item.
            Changes to that property's value raise the PropertyChanged event. 
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.TableSource">
            <summary>
            The source of the TableView.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.TableView">
            <summary>
            Overrides <see cref="P:UIKit.UITableViewController.TableView"/>.
            Sets or gets the controllers TableView. If you use a TableView
            placed in the UI manually, use this property's setter to assign
            your TableView to this controller.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.#ctor">
            <summary>
            Initializes a new instance of this class with a plain style.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.#ctor(UIKit.UITableViewStyle)">
            <summary>
            Initializes a new instance of this class with a specific style.
            </summary>
            <param name="tableStyle">The style that will be used for this controller.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ViewDidLoad">
            <summary>
            Overrides the <see cref="M:UIKit.UIViewController.ViewDidLoad"/> method.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.BindCell(UIKit.UITableViewCell,System.Object,Foundation.NSIndexPath)">
            <summary>
            Binds a <see cref="T:UIKit.UITableViewCell"/> to an item's properties.
            If a <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.BindCellDelegate"/> is available, this delegate will be used.
            If not, a simple text will be shown.
            </summary>
            <param name="cell">The cell that will be prepared.</param>
            <param name="item">The item that should be used to set the cell up.</param>
            <param name="indexPath">The <see cref="T:Foundation.NSIndexPath"/> for this cell.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.CreateCell(Foundation.NSString)">
            <summary>
            Creates a <see cref="T:UIKit.UITableViewCell"/> corresponding to the reuseId.
            If it is set, the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.CreateCellDelegate"/> delegate will be used.
            </summary>
            <param name="reuseId">A reuse identifier for the cell.</param>
            <returns>The created cell.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.CreateSource">
            <summary>
            Created the ObservableTableSource for this controller.
            </summary>
            <returns>The created ObservableTableSource.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.OnRowSelected(System.Object,Foundation.NSIndexPath)">
            <summary>
            Called when a row gets selected. Raises the SelectionChanged event.
            </summary>
            <param name="item">The selected item.</param>
            <param name="indexPath">The NSIndexPath for the selected row.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.RaisePropertyChanged(System.String)">
            <summary>
            Raises the <see cref="E:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.PropertyChanged"/> event.
            </summary>
            <param name="propertyName">The name of the property that changed.</param>
        </member>
        <member name="E:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.PropertyChanged">
            <summary>
            Occurs when a property of this instance changes.
            </summary>
        </member>
        <member name="E:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.SelectionChanged">
            <summary>
            Occurs when a new item gets selected in the list.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ObservableTableSource`1">
            <summary>
            A <see cref="T:UIKit.UITableViewSource"/> that handles changes to the underlying
            data source if this data source is an <see cref="T:System.Collections.Specialized.INotifyCollectionChanged"/>.
            </summary>
            <typeparam name="T2">The type of the items that the data source contains.</typeparam>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ObservableTableSource`1.ReuseId">
            <summary>
            A reuse identifier for the TableView's cells.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ObservableTableSource`1.#ctor(GalaSoft.MvvmLight.Helpers.ObservableTableViewController{`1})">
            <summary>
            Initializes an instance of this class.
            </summary>
            <param name="controller">The controller associated to this instance.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ObservableTableSource`1.GetCell(UIKit.UITableView,Foundation.NSIndexPath)">
            <summary>
            Attempts to dequeue or create a cell for the list.
            </summary>
            <param name="tableView">The TableView that is the cell's parent.</param>
            <param name="indexPath">The NSIndexPath for the cell.</param>
            <returns>The created or recycled cell.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ObservableTableSource`1.GetHeightForFooter(UIKit.UITableView,System.nint)">
            <summary>
            When called, checks if the ObservableTableViewController{T}.GetHeightForFooter
            delegate has been set. If yes, calls that delegate to get the TableView's footer height.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="section">The section index.</param>
            <returns>The footer's height.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ObservableTableSource`1.GetHeightForHeader(UIKit.UITableView,System.nint)">
            <summary>
            When called, checks if the ObservableTableViewController{T}.GetHeightForHeader
            delegate has been set. If yes, calls that delegate to get the TableView's header height.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="section">The section index.</param>
            <returns>The header's height.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ObservableTableSource`1.GetViewForFooter(UIKit.UITableView,System.nint)">
            <summary>
            When called, checks if the ObservableTableViewController{T}.GetViewForFooter
            delegate has been set. If yes, calls that delegate to get the TableView's footer.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="section">The section index.</param>
            <returns>The UIView that should appear as the section's footer.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ObservableTableSource`1.GetViewForHeader(UIKit.UITableView,System.nint)">
            <summary>
            When called, checks if the ObservableTableViewController{T}.GetViewForHeader
            delegate has been set. If yes, calls that delegate to get the TableView's header.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="section">The section index.</param>
            <returns>The UIView that should appear as the section's header.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ObservableTableSource`1.NumberOfSections(UIKit.UITableView)">
            <summary>
            Overrides the <see cref="M:UIKit.UITableViewSource.NumberOfSections(UIKit.UITableView)"/> method.
            </summary>
            <param name="tableView">The active TableView.</param>
            <returns>The number of sections of the UITableView.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ObservableTableSource`1.RowSelected(UIKit.UITableView,Foundation.NSIndexPath)">
            <summary>
            Overrides the <see cref="M:UIKit.UITableViewSource.RowSelected(UIKit.UITableView,Foundation.NSIndexPath)"/> method
            and notifies the associated <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1"/>
            that a row has been selected, so that the corresponding events can be raised.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="indexPath">The row's NSIndexPath.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewController`1.ObservableTableSource`1.RowsInSection(UIKit.UITableView,System.nint)">
            <summary>
            Overrides the <see cref="M:UIKit.UITableViewSource.RowsInSection(UIKit.UITableView,System.nint)"/> method
            and returns the number of rows in the associated data source.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="section">The active section.</param>
            <returns>The number of rows in the data source.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2">
            <summary>
            A <see cref="T:UIKit.UICollectionViewSource"/> that automatically updates the associated <see cref="T:UIKit.UICollectionView"/> when its 
            data source changes. Note that the changes are only observed if the data source 
            implements <see cref="T:System.Collections.Specialized.INotifyCollectionChanged"/>.
            </summary>
            <typeparam name="TItem">The type of the items in the data source.</typeparam>
            <typeparam name="TCell">The type of the <see cref="T:UIKit.UICollectionViewCell"/> used in the CollectionView.
            This can either be UICollectionViewCell or a derived type.</typeparam>
        </member>
        <member name="F:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.SelectedItemPropertyName">
            <summary>
            The <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.SelectedItem" /> property's name.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.BindCellDelegate">
            <summary>
            A delegate to a method taking a <see cref="T:UIKit.UICollectionViewCell"/>
            and setting its elements' properties according to the item
            passed as second parameter.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.DataSource">
            <summary>
            The data source of this list controller.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.GetSupplementaryViewDelegate">
            <summary>
            A delegate to a method returning a <see cref="T:UIKit.UICollectionReusableView"/>
            and used to set supplementary views on the UICollectionView.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.ReuseId">
            <summary>
            A reuse identifier for the UICollectionView's cells.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.SelectedItem">
            <summary>
            Gets the UICollectionView's selected item. You can use one-way databinding on this property.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.#ctor">
            <summary>
            Creates and initializes a new instance of <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2"/>
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.GetCell(UIKit.UICollectionView,Foundation.NSIndexPath)">
            <summary>
            Overrides the <see cref="M:UIKit.UICollectionViewSource.GetCell(UIKit.UICollectionView,Foundation.NSIndexPath)"/> method.
            Creates and returns a cell for the UICollectionView. Where needed, this method will
            optimize the reuse of cells for a better performance.
            </summary>
            <param name="collectionView">The UICollectionView associated to this source.</param>
            <param name="indexPath">The NSIndexPath pointing to the item for which the cell must be returned.</param>
            <returns>The created and initialised <see cref="T:UIKit.UICollectionViewCell"/>.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.GetItem(Foundation.NSIndexPath)">
            <summary>
            Gets the item selected by the NSIndexPath passed as parameter.
            </summary>
            <param name="indexPath">The NSIndexPath pointing to the desired item.</param>
            <returns>The item selected by the NSIndexPath passed as parameter.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.GetItemsCount(UIKit.UICollectionView,System.nint)">
            <summary>
            Overrides the <see cref="M:UIKit.UICollectionViewSource.GetItemsCount(UIKit.UICollectionView,System.nint)"/> method.
            Gets the number of items in the data source.
            </summary>
            <param name="collectionView">The UICollectionView associated to this source.</param>
            <param name="section">The section for which the count is needed. In the current
            implementation, only one section is supported.</param>
            <returns>The number of items in the data source.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.GetViewForSupplementaryElement(UIKit.UICollectionView,Foundation.NSString,Foundation.NSIndexPath)">
            <summary>
            Overrides the <see cref="M:UIKit.UICollectionViewSource.GetViewForSupplementaryElement(UIKit.UICollectionView,Foundation.NSString,Foundation.NSIndexPath)"/> method.
            When called, checks if the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.GetSupplementaryViewDelegate"/>
            delegate has been set. If yes, calls that delegate to get a supplementary view for the UICollectionView.
            </summary>
            <param name="collectionView">The UICollectionView associated to this source.</param>
            <param name="elementKind">The kind of supplementary element.</param>
            <param name="indexPath">The NSIndexPath pointing to the element.</param>
            <returns>A supplementary view for the UICollectionView.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.ItemDeselected(UIKit.UICollectionView,Foundation.NSIndexPath)">
            <summary>
            Overrides the <see cref="M:UIKit.UICollectionViewSource.ItemDeselected(UIKit.UICollectionView,Foundation.NSIndexPath)"/> method.
            Called when an item is deselected in the UICollectionView.
            <remark>If you subclass ObservableCollectionViewSource, you may override this method
            but you may NOT call base.ItemDeselected(...) in your overriden method, as this causes an exception
            in iOS. Because of this, you must take care of resetting the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.SelectedItem"/> property 
            yourself by calling SelectedItem = default(TItem);</remark>
            </summary>
            <param name="collectionView">The UICollectionView associated to this source.</param>
            <param name="indexPath">The NSIndexPath pointing to the element.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.ItemSelected(UIKit.UICollectionView,Foundation.NSIndexPath)">
            <summary>
            Overrides the <see cref="M:UIKit.UICollectionViewSource.ItemSelected(UIKit.UICollectionView,Foundation.NSIndexPath)"/> method.
            Called when an item is selected in the UICollectionView.
            <remark>If you subclass ObservableCollectionViewSource, you may override this method
            but you may NOT call base.ItemSelected(...) in your overriden method, as this causes an exception
            in iOS. Because of this, you must take care of setting the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.SelectedItem"/> property 
            yourself by calling var item = GetItem(indexPath); SelectedItem = item;</remark>
            </summary>
            <param name="collectionView">The UICollectionView associated to this source.</param>
            <param name="indexPath">The NSIndexPath pointing to the element.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.NumberOfSections(UIKit.UICollectionView)">
            <summary>
            Overrides the <see cref="M:UIKit.UICollectionViewSource.NumberOfSections(UIKit.UICollectionView)"/> method.
            The number of sections in this UICollectionView. In the current implementation,
            only one section is supported.
            </summary>
            <param name="collectionView">The UICollectionView associated to this source.</param>
            <returns></returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.BindCell(UIKit.UICollectionViewCell,System.Object,Foundation.NSIndexPath)">
            <summary>
            Sets a <see cref="T:UIKit.UICollectionViewCell"/>'s elements according to an item's properties.
            If a <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.BindCellDelegate"/> is available, this delegate will be used.
            If not, a simple text will be shown.
            </summary>
            <param name="cell">The cell that will be prepared.</param>
            <param name="item">The item that should be used to set the cell up.</param>
            <param name="indexPath">The <see cref="T:Foundation.NSIndexPath"/> for this cell.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.RaisePropertyChanged(System.String)">
            <summary>
            Raises the <see cref="E:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.PropertyChanged"/> event.
            </summary>
            <param name="propertyName">The name of the property that changed.</param>
        </member>
        <member name="E:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.PropertyChanged">
            <summary>
            Occurs when a property of this instance changes.
            </summary>
        </member>
        <member name="E:GalaSoft.MvvmLight.Helpers.ObservableCollectionViewSource`2.SelectionChanged">
            <summary>
            Occurs when a new item gets selected in the UICollectionView.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1">
            <summary>
            A <see cref="T:UIKit.UITableViewSource"/> that automatically updates the associated <see cref="T:UIKit.UITableView"/> when its 
            data source changes. Note that the changes are only observed if the data source 
            implements <see cref="T:System.Collections.Specialized.INotifyCollectionChanged"/>.
            </summary>
            <typeparam name="TItem">The type of the items in the data source.</typeparam>
        </member>
        <member name="F:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.SelectedItemPropertyName">
            <summary>
            The <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.SelectedItem" /> property's name.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.AddAnimation">
            <summary>
            When set, specifies which animation should be used when rows are added.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.BindCellDelegate">
            <summary>
            A delegate to a method taking a <see cref="T:UIKit.UITableViewCell"/>
            and setting its elements' properties according to the item
            passed as second parameter.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.CreateCellDelegate">
            <summary>
            A delegate to a method creating or reusing a <see cref="T:UIKit.UITableViewCell"/>.
            The cell will then be passed to the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.BindCellDelegate"/>
            delegate to set the elements' properties. Note that this delegate is only
            used if you didn't register with a ReuseID using the UITableView.RegisterClassForCell method.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.DataSource">
            <summary>
            The data source of this list controller.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.DeleteAnimation">
            <summary>
            When set, specifieds which animation should be used when a row is deleted.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetHeightForFooterDelegate">
            <summary>
            When set, returns the height of the view that will be used for the TableView's footer.
            </summary>
            <seealso cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetViewForFooterDelegate"/>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetHeightForHeaderDelegate">
            <summary>
            When set, returns the height of the view that will be used for the TableView's header.
            </summary>
            <seealso cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetViewForHeaderDelegate"/>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetViewForFooterDelegate">
            <summary>
            When set, returns a view that can be used as the TableView's footer.
            </summary>
            <seealso cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetHeightForFooterDelegate"/>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetViewForHeaderDelegate">
            <summary>
            When set, returns a view that can be used as the TableView's header.
            </summary>
            <seealso cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetHeightForHeaderDelegate"/>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.ReuseId">
            <summary>
            A reuse identifier for the TableView's cells.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.SelectedItem">
            <summary>
            Gets the UITableView's selected item. You can use one-way databinding on this property.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.#ctor">
            <summary>
            Constructs and initializes an instance of <see cref="T:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1"/>
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetCell(UIKit.UITableView,Foundation.NSIndexPath)">
            <summary>
            Creates and returns a cell for the UITableView. Where needed, this method will
            optimize the reuse of cells for a better performance.
            </summary>
            <param name="view">The UITableView associated to this source.</param>
            <param name="indexPath">The NSIndexPath pointing to the item for which the cell must be returned.</param>
            <returns>The created and initialised <see cref="T:UIKit.UITableViewCell"/>.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetHeightForFooter(UIKit.UITableView,System.nint)">
            <summary>
            When called, checks if the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetHeightForFooterDelegate"/>has been set. 
            If yes, calls that delegate to get the TableView's footer height.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="section">The section index.</param>
            <returns>The footer's height.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetHeightForHeader(UIKit.UITableView,System.nint)">
            <summary>
            When called, checks if the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetHeightForHeaderDelegate"/>
            delegate has been set. If yes, calls that delegate to get the TableView's header height.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="section">The section index.</param>
            <returns>The header's height.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetItem(Foundation.NSIndexPath)">
            <summary>
            Gets the item selected by the NSIndexPath passed as parameter.
            </summary>
            <param name="indexPath">The NSIndexPath pointing to the desired item.</param>
            <returns>The item selected by the NSIndexPath passed as parameter.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetViewForFooter(UIKit.UITableView,System.nint)">
            <summary>
            When called, checks if the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetViewForFooterDelegate"/>
            delegate has been set. If yes, calls that delegate to get the TableView's footer.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="section">The section index.</param>
            <returns>The UIView that should appear as the section's footer.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetViewForHeader(UIKit.UITableView,System.nint)">
            <summary>
            When called, checks if the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.GetViewForHeaderDelegate"/>
            delegate has been set. If yes, calls that delegate to get the TableView's header.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="section">The section index.</param>
            <returns>The UIView that should appear as the section's header.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.NumberOfSections(UIKit.UITableView)">
            <summary>
            Overrides the <see cref="M:UIKit.UITableViewSource.NumberOfSections(UIKit.UITableView)"/> method.
            </summary>
            <param name="tableView">The active TableView.</param>
            <returns>The number of sections of the UITableView.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.RowDeselected(UIKit.UITableView,Foundation.NSIndexPath)">
            <summary>
            Overrides the <see cref="M:UIKit.UITableViewSource.RowDeselected(UIKit.UITableView,Foundation.NSIndexPath)"/> method. When called, sets the
            <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.SelectedItem"/> property to null and raises the PropertyChanged and the SelectionChanged events.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="indexPath">The row's NSIndexPath.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.RowSelected(UIKit.UITableView,Foundation.NSIndexPath)">
            <summary>
            Overrides the <see cref="M:UIKit.UITableViewSource.RowSelected(UIKit.UITableView,Foundation.NSIndexPath)"/> method. When called, sets the
            <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.SelectedItem"/> property and raises the PropertyChanged and the SelectionChanged events.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="indexPath">The row's NSIndexPath.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.RowsInSection(UIKit.UITableView,System.nint)">
            <summary>
            Overrides the <see cref="M:UIKit.UITableViewSource.RowsInSection(UIKit.UITableView,System.nint)"/> method
            and returns the number of rows in the associated data source.
            </summary>
            <param name="tableView">The active TableView.</param>
            <param name="section">The active section.</param>
            <returns>The number of rows in the data source.</returns>
            <remarks>In the current implementation, only one section is supported.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.BindCell(UIKit.UITableViewCell,System.Object,Foundation.NSIndexPath)">
            <summary>
            Binds a <see cref="T:UIKit.UITableViewCell"/> to an item's properties.
            If a <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.BindCellDelegate"/> is available, this delegate will be used.
            If not, a simple text will be shown.
            </summary>
            <param name="cell">The cell that will be prepared.</param>
            <param name="item">The item that should be used to set the cell up.</param>
            <param name="indexPath">The <see cref="T:Foundation.NSIndexPath"/> for this cell.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.CreateCell(Foundation.NSString)">
            <summary>
            Creates a <see cref="T:UIKit.UITableViewCell"/> corresponding to the reuseId.
            If it is set, the <see cref="P:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.CreateCellDelegate"/> delegate will be used.
            </summary>
            <param name="reuseId">A reuse identifier for the cell.</param>
            <returns>The created cell.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.RaisePropertyChanged(System.String)">
            <summary>
            Raises the <see cref="E:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.PropertyChanged"/> event.
            </summary>
            <param name="propertyName">The name of the property that changed.</param>
        </member>
        <member name="E:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.PropertyChanged">
            <summary>
            Occurs when a property of this instance changes.
            </summary>
        </member>
        <member name="E:GalaSoft.MvvmLight.Helpers.ObservableTableViewSource`1.SelectionChanged">
            <summary>
            Occurs when a new item gets selected in the list.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Threading.DispatcherHelper">
            <summary>
            Helper class for dispatcher operations on the UI thread in Android.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.MainThreadContext">
            <summary>
            Gets a reference to a NSObject running on the UI thread, after the
            <see cref="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.Initialize(Foundation.NSObject)" /> method has been called on that thread.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.CheckBeginInvokeOnUI(System.Action)">
            <summary>
            Executes an action on the UI thread. If this method is called
            from the UI thread, the action is executed immendiately. If the
            method is called from another thread, the action will be enqueued
            on the UI thread's dispatcher and executed asynchronously.
            <para>For additional operations on the UI thread, you can get a
            reference to the UI thread's dispatcher thanks to the property
            <see cref="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.MainThreadContext" /></para>.
            </summary>
            <param name="action">The action that will be executed on the UI
            thread.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.Initialize(Foundation.NSObject)">
            <summary>
            This method should be called once on the UI thread to ensure that
            the <see cref="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.MainThreadContext" /> property is initialized.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Threading.DispatcherHelper.Reset">
            <summary>
            Resets the class by deleting the <see cref="P:GalaSoft.MvvmLight.Threading.DispatcherHelper.MainThreadContext"/>
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Views.ControllerBase">
            <summary>
            A base class to be used with the <see cref="T:GalaSoft.MvvmLight.Views.NavigationService" /> when
            using Storyboards.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Views.ControllerBase.NavigationParameter">
            <summary>
            The parameter passed to this controller by the
            <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String,System.Object)"/>  method.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.ControllerBase.#ctor(Foundation.NSCoder)">
            <summary>
            A constructor that initializes the object from the data stored in the unarchiver
            object.
            </summary>
            <param name="coder">The unarchiver object.</param>
            <remarks>Check the remarks on <see cref="M:UIKit.UIViewController.#ctor(Foundation.NSCoder)"/></remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.ControllerBase.#ctor(Foundation.NSObjectFlag)">
            <summary>
            Constructor to call on derived classes to skip initialization and merely
            allocate the object.
            </summary>
            <param name="t">Unused sentinel value, pass NSObjectFlag.Empty.</param>
            <remarks>Check the remarks on <see cref="M:UIKit.UIViewController.#ctor(Foundation.NSObjectFlag)"/></remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.ControllerBase.#ctor(System.String,Foundation.NSBundle)">
            <summary>
            Initializes an instance of this class.
            </summary>
            <param name="nibName">The NIB name, or null.
            This parameter can be null.</param>
            <param name="bundle">The bundle where the search for the NIB takes place,
            if you pass null, this searches for the NIB on the main bundle.
            This parameter can be null.</param>
            <remarks>Check the remarks on <see cref="M:UIKit.UIViewController.#ctor(System.String,Foundation.NSBundle)"/></remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.ControllerBase.#ctor">
            <summary>
            Initializes an instance of this class.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.ControllerBase.#ctor(System.IntPtr)">
            <summary>
            A constructor used when creating managed representations of unmanaged objects;
            Called by the runtime.
            </summary>
            <param name="handle">Pointer (handle) to the unmanaged object.</param>
            <remarks>Check the remarks on <see cref="M:UIKit.UIViewController.#ctor(System.IntPtr)"/></remarks>
        </member>
        <member name="T:GalaSoft.MvvmLight.Views.DialogService">
            <summary>
            An implementation of <see cref="T:GalaSoft.MvvmLight.Views.IDialogService"/> allowing
            to display simple dialogs to the user. Note that this class
            uses the built in Windows Phone dialogs which may or may not
            be sufficient for your needs. Using this class is easy
            but feel free to develop your own IDialogService implementation
            if needed.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowError(System.String,System.String,System.String,System.Action)">
            <summary>
            Displays information about an error.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
            <remarks>Displaying dialogs in iOS is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowError(System.Exception,System.String,System.String,System.Action)">
            <summary>
            Displays information about an error.
            </summary>
            <param name="error">The exception of which the message must be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
            <remarks>Displaying dialogs in iOS is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessage(System.String,System.String)">
            <summary>
            Displays information to the user. The dialog box will have only
            one button with the text "OK".
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
            <remarks>Displaying dialogs in Android is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessage(System.String,System.String,System.String,System.Action)">
            <summary>
            Displays information to the user. The dialog box will have only
            one button.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
            <remarks>Displaying dialogs in Android is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessage(System.String,System.String,System.String,System.String,System.Action{System.Boolean})">
            <summary>
            Displays information to the user. The dialog box will have only
            one button.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonConfirmText">The text shown in the "confirm" button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="buttonCancelText">The text shown in the "cancel" button
            in the dialog box. If left null, the text "Cancel" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user. The callback method will get a boolean
            parameter indicating if the "confirm" button (true) or the "cancel" button
            (false) was pressed by the user.</param>
            <returns>A Task allowing this async method to be awaited. The task will return
            true or false depending on the dialog result.</returns>
            <remarks>Displaying dialogs in Android is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.DialogService.ShowMessageBox(System.String,System.String)">
            <summary>
            Displays information to the user in a simple dialog box. The dialog box will have only
            one button with the text "OK". This method should be used for debugging purposes.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
            <remarks>Displaying dialogs in Android is synchronous. As such,
            this method will be executed synchronously even though it can be awaited
            for cross-platform compatibility purposes.</remarks>
        </member>
        <member name="T:GalaSoft.MvvmLight.Views.NavigationService">
            <summary>
            Xamarin iOS implementation of <see cref="T:GalaSoft.MvvmLight.Views.INavigationService"/>.
            This implementation can be used in Xamarin iOS applications (not Xamarin Forms).
            </summary>
            <remarks>For this navigation service to work properly, it should be initialized
            using the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.Initialize(UIKit.UINavigationController)"/> method, with the application's
            <see cref="T:UIKit.UINavigationController"/>.</remarks>
        </member>
        <member name="F:GalaSoft.MvvmLight.Views.NavigationService.RootPageKey">
            <summary>
            The key that is returned by the <see cref="P:GalaSoft.MvvmLight.Views.NavigationService.CurrentPageKey"/> property
            when the current UIViewController is the root controller.
            </summary>
        </member>
        <member name="F:GalaSoft.MvvmLight.Views.NavigationService.UnknownPageKey">
            <summary>
            The key that is returned by the <see cref="P:GalaSoft.MvvmLight.Views.NavigationService.CurrentPageKey"/> property
            when the current UIViewController is not found.
            This can be the case when the navigation wasn't managed by this NavigationService,
            for example when it is directly triggered in the Storyboard.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Views.NavigationService.CurrentPageKey">
            <summary>
            The key corresponding to the currently displayed page.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Views.NavigationService.NavigationController">
            <summary>
            Gets the NavigationController that was passed in the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.Initialize(UIKit.UINavigationController)"/> method.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Type)">
            <summary>
            Adds a key/page pair to the navigation service.
            This method will create a new controller on demand, using
            reflection. You can use <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Func{System.Object,UIKit.UIViewController})"/>
            if you need more fine-grained control over the controller's creation.
            </summary>
            <param name="key">The key that will be used later
            in the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String)"/> or <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String,System.Object)"/> methods.</param>
            <param name="controllerType">The type of the controller corresponding to the key.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Func{System.Object,UIKit.UIViewController})">
            <summary>
            Adds a key/page pair to the navigation service.
            This method allows the caller to have fine grained control over the controller's
            creation.
            </summary>
            <param name="key">The key that will be used later
            in the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String)"/> or <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String,System.Object)"/> methods.</param>
            <param name="createAction">A Func returning the controller corresponding
            to the given key.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.String)">
            <summary>
            Adds a key/page pair to the navigation service.
            This method should be used when working with Storyboard for the UI.
            </summary>
            <param name="key">The key that will be used later
            in the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String)"/> or <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String,System.Object)"/> methods.</param>
            <param name="storyboardId">The idea of the UIViewController
            in the Storyboard. Use the storyboardIdentifier/restorationIdentifier property
            in the *.storyboard document.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.GetAndRemoveParameter(UIKit.UIViewController)">
            <summary>
            Allows a caller to get the navigation parameter corresponding 
            to the Intent parameter.
            </summary>
            <param name="controller">The <see cref="T:UIKit.UIViewController"/> that was navigated to.</param>
            <returns>The navigation parameter. If no parameter is found,
            returns null.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.GoBack">
            <summary>
            If possible, discards the current page and displays the previous page
            on the navigation stack.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.Initialize(UIKit.UINavigationController)">
            <summary>
            Initialized the navigation service. This method must be called
            before the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String)"/> or
            <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String,System.Object)"/> methods are called.
            </summary>
            <param name="navigation">The application's navigation controller.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String)">
            <summary>
            Displays a new page corresponding to the given key. 
            Make sure to call the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Type)"/>
            or <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Func{System.Object,UIKit.UIViewController})"/>
            method first.
            </summary>
            <param name="pageKey">The key corresponding to the page
            that should be displayed.</param>
            <exception cref="T:System.ArgumentException">When this method is called for 
            a key that has not been configured earlier.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.NavigationService.NavigateTo(System.String,System.Object)">
            <summary>
            Displays a new page corresponding to the given key,
            and passes a parameter to the new page's constructor.
            Make sure to call the <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Type)"/>
            or <see cref="M:GalaSoft.MvvmLight.Views.NavigationService.Configure(System.String,System.Func{System.Object,UIKit.UIViewController})"/>
            method first.
            </summary>
            <param name="pageKey">The key corresponding to the page
            that should be displayed.</param>
            <param name="parameter">The parameter that should be passed
            to the new page's constructor.</param>
            <exception cref="T:System.ArgumentException">When this method is called for 
            a key that has not been configured earlier.</exception>
            <exception cref="T:System.InvalidOperationException">When this method is called for 
            a page that doesn't have a suitable constructor (i.e.
            a constructor with a parameter corresponding to the
            navigation parameter's type).</exception>
        </member>
        <member name="T:System.Windows.IWeakEventListener">
            <summary>
            Provides event listening support for classes that expect to receive events 
            through the WeakEvent pattern and a WeakEventManager.
            </summary>
        </member>
        <member name="P:System.Windows.IWeakEventListener.InstanceReference">
            <summary>
            Gets the WeakReference holding the instance that raised the event.
            </summary>
        </member>
        <member name="M:System.Windows.IWeakEventListener.ReceiveWeakEvent(System.Type,System.Object,System.EventArgs)">
            <summary>
            Receives events from the centralized event manager. 
            </summary>
            <param name="managerType">The type of the WeakEventManager calling this method.</param>
            <param name="sender">Object that originated the event.</param>
            <param name="e">Event data.</param>
            <returns>true if the listener handled the event. It is considered an error by the 
            WeakEventManager handling in WPF to register a listener for an event that the 
            listener does not handle. Regardless, the method should return false if it receives 
            an event that it does not recognize or handle.
            </returns>
        </member>
        <member name="T:System.Windows.PropertyChangedEventManager">
            <summary>
            Provides an implementation so that you can use the 
            "weak event listener" pattern to attach listeners
            for the <see cref="M:System.Windows.PropertyChangedEventManager.PropertyChanged(System.Object,System.ComponentModel.PropertyChangedEventArgs)" /> event. 
            </summary>
        </member>
        <member name="P:System.Windows.PropertyChangedEventManager.Instance">
            <summary>
            Get the current instance of <see cref="T:System.Windows.PropertyChangedEventManager"/>
            </summary>
        </member>
        <member name="M:System.Windows.PropertyChangedEventManager.AddListener(System.ComponentModel.INotifyPropertyChanged,System.Windows.IWeakEventListener,System.String)">
            <summary>
            Adds the specified listener to the list of listeners on the specified source. 
            </summary>
            <param name="source">The object with the event.</param>
            <param name="listener">The object to add as a listener.</param>
            <param name="propertyName">The name of the property that exists on
            source upon which to listen for changes.</param>
        </member>
        <member name="M:System.Windows.PropertyChangedEventManager.RemoveListener(System.Windows.IWeakEventListener)">
            <summary>
            Removes the specified listener from the list of listeners on the 
            specified source. 
            </summary>
            <param name="listener">The object to remove as a listener.</param>
        </member>
        <member name="M:System.Windows.PropertyChangedEventManager.PrivateAddListener(System.ComponentModel.INotifyPropertyChanged,System.Windows.IWeakEventListener,System.String)">
            <summary>
            Private method to add the specified listener to the list of listeners 
            on the specified source. 
            </summary>
            <param name="source">The object with the event.</param>
            <param name="listener">The object to add as a listener.</param>
            <param name="propertyName">The name of the property that exists 
            on source upon which to listen for changes.</param>
        </member>
        <member name="M:System.Windows.PropertyChangedEventManager.PrivateRemoveListener(System.Windows.IWeakEventListener)">
            <summary>
            Private method to remove the specified listener from the list of listeners 
            on the specified source. 
            </summary>
            <param name="listener">The object to remove as a listener.</param>
        </member>
        <member name="M:System.Windows.PropertyChangedEventManager.PropertyChanged(System.Object,System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            The method that handles the <see cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged"/> event.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">A <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> that 
            contains the event data.</param>
        </member>
        <member name="M:System.Windows.PropertyChangedEventManager.StartListening(System.ComponentModel.INotifyPropertyChanged)">
            <summary>
            Begin listening for the <see cref="M:System.Windows.PropertyChangedEventManager.PropertyChanged(System.Object,System.ComponentModel.PropertyChangedEventArgs)"/> event on 
            the provided source.
            </summary>
            <param name="source">The object on which to start listening 
            for <see cref="M:System.Windows.PropertyChangedEventManager.PropertyChanged(System.Object,System.ComponentModel.PropertyChangedEventArgs)"/>.</param>
        </member>
        <member name="M:System.Windows.PropertyChangedEventManager.StopListening(System.ComponentModel.INotifyPropertyChanged)">
            <summary>
            Stop listening for the <see cref="M:System.Windows.PropertyChangedEventManager.PropertyChanged(System.Object,System.ComponentModel.PropertyChangedEventArgs)"/> event on the 
            provided source.
            </summary>
            <param name="source">The object on which to start listening for 
            <see cref="M:System.Windows.PropertyChangedEventManager.PropertyChanged(System.Object,System.ComponentModel.PropertyChangedEventArgs)"/>.</param>
        </member>
    </members>
</doc>
