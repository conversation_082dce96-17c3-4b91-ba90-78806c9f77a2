﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using System.Data;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using ServoStudio.Models;
using System.IO;
using System.Collections.Generic;
using DevExpress.Mvvm.POCO;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class MotorLibraryDetailsViewModel
    {
        #region 字段
        private bool IsSameFileName;
        private DataTable dtMotorLibarayDetails;
        private DataTable dtMotorLibarayLog;
        private ObservableCollection<LibraryDetailsSet> obsMotorLibraryDetails;//参数监控集合   
        private ObservableCollection<ParameterReadWriteSet> obsMotorParameter;
        #endregion

        #region 属性
        public virtual ObservableCollection<LibraryDetailsSet> MotorLibraryDetails { get; set; }//参数监控集合   
        public virtual ObservableCollection<string> EncoderType { get; set; }
        public virtual string SelectedEncoderType { get; set; }
        public virtual string FileName { get; set; }
        public virtual string Author { get; set; }
        public virtual string MotorType { get; set; }   
        public virtual string Comment { get; set; }
        public virtual bool ReadOnly { get; set; }//只读属性
        #endregion

        #region 服务
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 构造函数
        public MotorLibraryDetailsViewModel()
        {
            ViewModelSet.MotorLibraryDetails = this;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：MotorLibraryDetailsLoaded
        //函数功能：载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.26
        //*************************************************************************
        public void MotorLibraryDetailsLoaded()
        {
            int iRet = -1;
            dtMotorLibarayDetails = new DataTable();
            dtMotorLibarayLog = new DataTable();
            obsMotorParameter = new ObservableCollection<ParameterReadWriteSet>();
            MotorLibraryDetails = new ObservableCollection<LibraryDetailsSet>();

            //只读属性与控件初始化
            ComboBoxInitialize();
            ControlReadOnlyProperty();

            //获取日志
            iRet = ExcelHelper.ReadFromExcel(FilePath.MotorLibraryLog, ref dtMotorLibarayLog);
            if (iRet == RET.ERROR)
            {
                ShowNotification(RET.ERROR);
                return;
            }

            //非添加任务
            if (MotorLibraryDetailSet.Action != "Insert")
            {              
                //获取参数表
                iRet = ExcelHelper.ReadFromExcel(FilePath.MotorLibrary + MotorLibraryDetailSet.FileName + ".xlsx", ref dtMotorLibarayDetails);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                iRet = ConvertHelper.DataTableToObservableCollection(dtMotorLibarayDetails, ref obsMotorLibraryDetails);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
            }
            else//添加任务
            {              
                iRet = ParameterReadWriteModel.RetrieveDataTable(TaskName.Motor, ref obsMotorParameter);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }        
            }
           
            //参数赋值
            ParameterEvaluation(MotorLibraryDetailSet.Action);
        }

        //*************************************************************************
        //函数名称：CheckIsNameExist
        //函数功能：判断文件是否存在
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.27
        //*************************************************************************
        public void CheckIsNameExist()
        {
            string strFilePath = FilePath.MotorLibrary + FileName + ".xlsx";
            if (File.Exists(strFilePath) && (MotorLibraryDetailSet.Action == "Insert" || MotorLibraryDetailSet.Action == "Update"))
            {
                ShowNotification(2033);
                IsSameFileName = true;
            }
            else
            {
                IsSameFileName = false;
            }
        }

        //*************************************************************************
        //函数名称：AddMotorLibrary
        //函数功能：添加电机参数库
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.27
        //*************************************************************************
        public void AddMotorLibrary()
        {
            int iRet = -1;

            //写入参数库
            iRet = ExcelHelper.WriteIntoExcel(GetMotorLibraryFilePath(), GetMotorDetailsToDataTable(), ExcelType.MotorConfig);
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(RET.ERROR);
                return;
            }

            //写入参数库日志
            iRet = ExcelHelper.WriteIntoExcel(FilePath.MotorLibraryLog, GetMotorLogToDataTable(Method: "Add", IsSameFileName: false), ExcelType.MotorLibraryLog);
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(RET.ERROR);
            }
            else
            {
                ShowNotification(2002);
            }
        }

        //*************************************************************************
        //函数名称：ModifyMotorLibrary
        //函数功能：更新电机参数库
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.27
        //*************************************************************************
        public void ModifyMotorLibrary()
        {
            int iRet = -1;

            try
            {
                //删除原重名文件
                if (File.Exists(FilePath.MotorLibrary + MotorLibraryDetailSet.FileName + ".xlsx"))
                {
                    File.Delete(FilePath.MotorLibrary + MotorLibraryDetailSet.FileName + ".xlsx");
                }

                //写入参数库
                iRet = ExcelHelper.WriteIntoExcel(GetMotorLibraryFilePath(), GetMotorDetailsToDataTable(), ExcelType.MotorConfig);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //写入参数库日志
                iRet = ExcelHelper.WriteIntoExcel(FilePath.MotorLibraryLog, GetMotorLogToDataTable(Method: "Modify", IsSameFileName: IsSameFileName), ExcelType.MotorLibraryLog);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                }
                else
                {
                    ShowNotification(2002);
                }
            }
            catch
            {

            }           
        }

        //*************************************************************************
        //函数名称：DeleteMotorLibrary
        //函数功能：删除电机参数库
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.27
        //*************************************************************************
        public void DeleteMotorLibrary()
        {
            int iRet = -1;

            //删除参数库
            string strFilePath = FilePath.MotorLibrary + FileName + ".xlsx";
            if (File.Exists(strFilePath))
            {
                File.Delete(strFilePath);
            }
            else
            {
                //获取日志
                iRet = ExcelHelper.ReadFromExcel(FilePath.MotorLibraryLog, ref dtMotorLibarayLog);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
            }

            //写入参数库日志
            iRet = ExcelHelper.WriteIntoExcel(FilePath.MotorLibraryLog, GetMotorLogToDataTable(Method: "Delete", IsSameFileName: false), ExcelType.MotorLibraryLog);
            if (iRet != RET.SUCCEEDED)
            {
                ShowNotification(RET.ERROR);
            }
            else
            {
                ShowNotification(2034);
            }          
        }

        //*************************************************************************
        //函数名称：DownloadMotorLibrary
        //函数功能：写电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.01
        //*************************************************************************
        public void DownloadMotorLibrary(bool bEEPROM)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();
            List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取要写入的数据字典并获取参数详细信息
                AddParameterInfoDictionary(ref dicParameterInfo);
                OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MOTORFEEDBACK, TaskName.MotorFeedback, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_WRITE_PARAMETER, "WriteMotorFeedbackParameter", ex);
            }
        }
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：属性初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2021.01.28&2021.10.10
        //*************************************************************************
        private void ComboBoxInitialize()
        {
            //EncoderType = new ObservableCollection<string>() { "增量式编码器", "多摩川编码器", "尼康编码器4M", "尼康编码器2.5M", "旋转式编码器" };   //由Lilbert添加旋转式编码器
            EncoderType = new ObservableCollection<string>() { "增量式编码器", "多摩川编码器", "尼康编码器4M", "尼康编码器2.5M", "旋转式编码器", "通信型增量式编码器", "BISS-C绝对式编码器", "ABZ增量式编码器+HALL", "HALL-UVW", "SSI编码器" };
            
            SelectedEncoderType = "增量式编码器";                 
        }

        //*************************************************************************
        //函数名称：ControlReadOnlyProperty
        //函数功能：属性初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.27
        //*************************************************************************
        private void ControlReadOnlyProperty()
        {
            if (MotorLibraryDetailSet.Action == "Select" || MotorLibraryDetailSet.Action == "Download" || MotorLibraryDetailSet.Action == "Delete")
            {
                ReadOnly = true;      
            }
            else
            {
                ReadOnly = false;            
            }
        }

        //*************************************************************************
        //函数名称：ParameterEvaluation
        //函数功能：参数赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.27
        //*************************************************************************
        private void ParameterEvaluation(string Action)
        {
            if (MotorLibraryDetailSet.Action == "Insert")
            {
                FileName = "";
                Author = "";
                MotorType = "";
                SelectedEncoderType = "增量式编码器";
                Comment = "";

                foreach (var item in obsMotorParameter)
                {
                    if (item.Name.ToUpper() != "MOTOR TYPE" && item.Name.ToUpper() != "MOTOR ID" && item.Name.ToUpper() != "ENCODER TYPE")
                    {
                        LibraryDetailsSet libraryDetailsSet = new LibraryDetailsSet();
                        libraryDetailsSet.Name = item.Name;
                        libraryDetailsSet.MotorFeedbackInterface = item.Description;
                        libraryDetailsSet.Default = "";
                        libraryDetailsSet.Unit = item.Unit;
                        MotorLibraryDetails.Add(libraryDetailsSet);
                    }
                }
            }
            else
            {
                FileName = MotorLibraryDetailSet.FileName;
                Author = MotorLibraryDetailSet.Author;
                MotorType = MotorLibraryDetailSet.MotorType;
                SelectedEncoderType = MotorLibraryDetailSet.EncoderType;
                Comment = MotorLibraryDetailSet.Comment;

                foreach (var item in obsMotorLibraryDetails)
                {
                    if (item.Name.ToUpper() != "MOTOR TYPE" && item.Name.ToUpper() != "MOTOR ID" && item.Name.ToUpper() != "ENCODER TYPE")
                    {
                        MotorLibraryDetails.Add(item);
                    }
                }
            }  
        }

        //*************************************************************************
        //函数名称：GetMotorDetailsToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.23
        //*************************************************************************
        private DataTable GetMotorDetailsToDataTable()
        {
            DataTable dt = new DataTable();

            try
            {
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Rated Power", "额定功率", MotorLibraryDetails[0].Default, "0.01KW", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Rated Voltage", "额定电压", MotorLibraryDetails[1].Default, "V", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Rated Current", "额定电流", MotorLibraryDetails[2].Default, "0.1A", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Rated Torque", "额定转矩", MotorLibraryDetails[3].Default, "0.01Nm", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Rated Speed", "额定转速", MotorLibraryDetails[4].Default, "rpm", ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Max Current", "最大电流", MotorLibraryDetails[5].Default, "0.1A", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Max Torque", "最大转矩", MotorLibraryDetails[6].Default, "0.01Nm", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Max Speed", "最大转速", MotorLibraryDetails[7].Default, "rpm", ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Pole Pairs Number", "极对数", MotorLibraryDetails[8].Default, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Winding Resistance", "线电阻", MotorLibraryDetails[9].Default, "0.001Ohm", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Winding Inductance", "线电感", MotorLibraryDetails[10].Default, "0.01mH", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Rotor Inertia", "转动惯量", MotorLibraryDetails[11].Default, "0.01kg*cm2", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Back EMF", "电机反电势常数", MotorLibraryDetails[12].Default, "0.01mV/rpm", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Torque Constant", "转矩常数", MotorLibraryDetails[13].Default, "0.01N.m/A", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Motor Mechanical Constant", "机械常数", MotorLibraryDetails[14].Default, "0.01ms", ref dt);

                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "Encoder Type", "编码器类型", SelectedEncoderType, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "ABS Encoder Multi-Turn Bit", "多圈值分辨率位数", MotorLibraryDetails[15].Default, "bit", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "ABS Encoder Single-Turn Bit", "单圈值分辨率位数", MotorLibraryDetails[16].Default, "bit", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "ABS Encoder Offset", "绝对式编码器偏置", MotorLibraryDetails[17].Default, "", ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.MOTORFEEDBACK, "ABZ Encoder Pulses", "ABZ编码器脉冲数", MotorLibraryDetails[18].Default, "1P/Rev", ref dt);

                return dt;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MOTORFEEDBACK_GET_MOTOR_CONFIF_TO_DATATABLE, "GetMotorConfigToDataTable", ex);
                return null;
            }
        }
        private DataTable GetMotorLogToDataTable(string Method, bool IsSameFileName)
        {
            if (IsSameFileName)
            {
                foreach (var item in dtMotorLibarayLog.Select("Name = '" + FileName + "'"))
                {
                    item["Valid"] = "False";
                }
            }

            if (Method == "Add")
            {
                foreach (var item in dtMotorLibarayLog.Select("Name = '" + FileName + "'"))
                {
                    item["Valid"] = "False";
                }
            }
            else
            {            
                foreach (var item in dtMotorLibarayLog.Select("Name = '" + MotorLibraryDetailSet.FileName + "'"))
                {
                    item["Valid"] = "False";
                }                           
            }

            if (dtMotorLibarayLog == null)
            {
                dtMotorLibarayLog = new DataTable();
                DataColumnCollection columns = dtMotorLibarayLog.Columns;
                columns.Add("Name", typeof(String));
                columns.Add("MotorType", typeof(String));
                columns.Add("EncoderType", typeof(String));
                columns.Add("Author", typeof(String));
                columns.Add("DateTime", typeof(String));
                columns.Add("Comment", typeof(String));
                columns.Add("Valid", typeof(String));
            }
                
            if (MotorLibraryDetailSet.Action == "Update" || MotorLibraryDetailSet.Action == "Insert")
            {
                DataRow clsDataRow = dtMotorLibarayLog.NewRow();
                clsDataRow["Name"] = FileName;
                clsDataRow["MotorType"] = MotorType;
                clsDataRow["EncoderType"] = SelectedEncoderType;
                clsDataRow["Author"] = Author;
                clsDataRow["DateTime"] = DateTime.Now.ToString();
                clsDataRow["Comment"] = Comment;
                clsDataRow["Valid"] = "True";
                dtMotorLibarayLog.Rows.Add(clsDataRow);
            }
        
            return dtMotorLibarayLog;
        }

        //*************************************************************************
        //函数名称：GetMotorLibraryFilePath
        //函数功能：获取参数库文件路径
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.10.23
        //*************************************************************************
        private string GetMotorLibraryFilePath()
        {
            string strFilePath = null;
            string strDateTime = null;

            if (string.IsNullOrEmpty(FileName))
            {
                strDateTime = DateTime.Now.ToString();
                FileName = "电机反馈_配置文件" + strDateTime.Replace("/", "").Replace(":", "").Replace(" ", "_");
                strFilePath = FilePath.MotorLibrary + FileName + ".xlsx";
            }
            else
            {
                strFilePath = FilePath.MotorLibrary + FileName + ".xlsx";
            }
         
            return strFilePath;
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.02&2021.06.15&2021.10.10
        //*************************************************************************
        private void AddParameterInfoDictionary(ref Dictionary<string, string> dicParameterInfo)
        {
            string strEncoderValue = null;         
            switch (SelectedEncoderType)
            {
                case "增量式编码器":
                    strEncoderValue = "1";
                    break;
                case "多摩川编码器":
                    strEncoderValue = "2";
                    break;
                case "尼康编码器4M":
                    strEncoderValue = "3";
                    break;
                case "尼康编码器2.5M":
                    strEncoderValue = "4";
                    break;
                case "旋转式编码器":
                    strEncoderValue = "5";
                    break;
                case "通信型增量式编码器":
                    strEncoderValue = "6";
                    break;
                case "BISS-C绝对式编码器":
                    strEncoderValue = "7";
                    break;
                case "BISS-C增量式编码器":
                    strEncoderValue = "8";
                    break;
                case "ABZ增量式编码器+HALL":
                    strEncoderValue = "9";
                    break;
                case "HALL-UVW":
                    strEncoderValue = "10";
                    break;
                case "SSI编码器":
                    strEncoderValue = "11";
                    break;
                default:
                    break;
            }

            dicParameterInfo = new Dictionary<string, string>();
            dicParameterInfo.Add("Motor Rated Power", MotorLibraryDetails[0].Default);
            dicParameterInfo.Add("Motor Rated Voltage", MotorLibraryDetails[1].Default);
            dicParameterInfo.Add("Motor Rated Current", MotorLibraryDetails[2].Default);
            dicParameterInfo.Add("Motor Rated Torque", MotorLibraryDetails[3].Default);
            dicParameterInfo.Add("Motor Rated Speed", MotorLibraryDetails[4].Default);
            dicParameterInfo.Add("Motor Max Current", MotorLibraryDetails[5].Default);
            dicParameterInfo.Add("Motor Max Torque", MotorLibraryDetails[6].Default);
            dicParameterInfo.Add("Motor Max Speed", MotorLibraryDetails[7].Default);

            dicParameterInfo.Add("Motor Pole Pairs Number", MotorLibraryDetails[8].Default);
            dicParameterInfo.Add("Motor Winding Resistance", MotorLibraryDetails[9].Default);
            dicParameterInfo.Add("Motor Winding Inductance", MotorLibraryDetails[10].Default);
            dicParameterInfo.Add("Motor Rotor Inertia", MotorLibraryDetails[11].Default);
            dicParameterInfo.Add("Motor Back EMF", MotorLibraryDetails[12].Default);
            dicParameterInfo.Add("Motor Torque Constant", MotorLibraryDetails[13].Default);
            dicParameterInfo.Add("Motor Mechanical Constant", MotorLibraryDetails[14].Default);

            dicParameterInfo.Add("Encoder Type", strEncoderValue);
            dicParameterInfo.Add("Abs Encoder Multi-Turn Bit", MotorLibraryDetails[15].Default);   //由Lilbert于2021.06.15更改单圈值与多圈值行号
            dicParameterInfo.Add("Abs Encoder Single-Turn Bit", MotorLibraryDetails[16].Default);
            //dicParameterInfo.Add("Abs Encoder Multi-Turn Bit", MotorLibraryDetails[16].Default);
            dicParameterInfo.Add("Abs Encoder Offset", MotorLibraryDetails[17].Default);
            dicParameterInfo.Add("Abz Encoder Pulses", MotorLibraryDetails[18].Default);                                
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowHintInfo(string strInfo)
        {
            ViewModelSet.Main?.ShowHintInfo(strInfo);
        }
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }

    public class LibraryDetailsSet
    {
        public string Name { get; set; }
        public string MotorFeedbackInterface { get; set; }
        public string Default { get; set; }
        public string Unit { get; set; }
    }
}