﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using DevExpress.Mvvm.POCO;
using System.Collections.Generic;
using System.Data;
using ServoStudio.Models;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class UnitViewModel
    {
        #region 私有字段
        private static bool IsGearRatioSet = false;
        private static bool IsInitialized = true;
        public bool IsClosed = false;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 服务
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 属性
        public virtual ObservableCollection<string> PositionUnit { get; set; }//位置单位
        public virtual string SelectedPositionUnit { get; set; }//选中位置单位

        public virtual ObservableCollection<string> TorqueUnit { get; set; }//位置单位
        public virtual string SelectedTorqueUnit { get; set; }//选中位置单位

        public virtual ObservableCollection<string> SpeedUnit { get; set; }//位置单位
        public virtual string SelectedSpeedUnit { get; set; }//选中位置单位

        public virtual ObservableCollection<string> AccelerationUnit { get; set; }//位置单位
        public virtual string SelectedAccelerationUnit { get; set; }//选中位置单位

        public virtual string MotorRevolutions { get; set; }//电机分辨率
        public virtual string LoadShaftRevolutions { get; set; }//负载轴分辨率
        public virtual double GearRatioHint { get; set; }//电子齿轮比提示
        #endregion

        #region 构造函数
        public UnitViewModel()
        {
            ViewModelSet.Unit = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.UNIT;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：UnitLoaded
        //函数功能：Unit载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.06.10&2023.02.08
        //*************************************************************************
        public void UnitLoaded()
        {
            int iRet = -1;

            try
            {
                if (OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Type", "Index")) == "0")//由Lilbert于2023.02.08添加读取电机类型
                {
                    GlobalCurrentInput.SelectedMotorType = "旋转电机";
                }
                else
                {
                    GlobalCurrentInput.SelectedMotorType = "直线电机";
                }

                RefreshComboBox();

                //赋值
                if (IsInitialized == true)
                {
                    //标志位更新
                    IsInitialized = false;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadUnitParameter();
                    }
                    else
                    {
                        GetDefaultUnitParameter();
                    }
                }
                else
                {
                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadUnitParameter();
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.UNIT_LOADED, "UnitLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：UnitUnloaded
        //函数功能：单位设置界面退出
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2023.01.04
        //*************************************************************************
        public void UnitUnloaded()
        {
            if (GlobalCurrentInput.SelectedMotorType == "旋转电机")
            {
                SelectedPositionUnit = "cnt";
                SelectedTorqueUnit = "0.1%额定扭矩";
                SelectedSpeedUnit = "cnt/s";
                SelectedAccelerationUnit = "cnt/s^2";
            }
            else
            {
                SelectedPositionUnit = "mm";
                SelectedTorqueUnit = "0.1%额定扭矩";
                SelectedSpeedUnit = "mm/s";
                SelectedAccelerationUnit = "mm/s^2";
            }

            //获取默认参数单位
            OthersHelper.GetSelectDefaultUnit();

            //写入所选单位设置
            OthersHelper.WriteSelectedUnitIntoFile();
            IsClosed = true;
        }

        //*************************************************************************
        //函数名称：WriteUnitParameter
        //函数功能：写一般设置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void WriteUnitParameter()
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要写入的数据字典
                AddParameterInfoDictionary(ref dicParameterInfo);

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                OthersHelper.GetWindowsStartupPosition();
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.NORMALSET, TaskName.GearRatio, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_WRITE_PARAMETER, "WriteUnitParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadUnitParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void ReadUnitParameter()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                AddParameterInfoDictionary(ref dicParameterInfo);

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.NORMALSET, TaskName.GearRatio, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_READ_PARAMETER, "ReadUnitParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultUnitParameter
        //函数功能：获取限幅保护的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void GetDefaultUnitParameter()
        {         
            MotorRevolutions = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Revolutions", "Default");
            LoadShaftRevolutions = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Shaft Revolutions", "Default");                            
        }

        //*************************************************************************
        //函数名称：SaveUnitConfigFile
        //函数功能：保存限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void SaveUnitConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                //选择配置文件存放位置
                iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.GearRatio);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //生成配置文件
                iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetUnitConfigToDataTable(), ExcelType.GearRatio);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2002);
                }
                else
                {
                    ShowNotification(2003);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_SAVE_CONFIG_FILE, "SaveUnitConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetUnitConfigFile
        //函数功能：获取限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void GetUnitConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            DataTable dt = null;

            try
            {
                //获取配置文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(strFilePath, ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //判断是否导入正确的配置文件
                if (dt.Columns[3].ColumnName != FileInterface.GEARRATIO)
                {
                    ShowNotification(2001);
                    return;
                }

                //导入参数  
                iRet = GetUnitConfigFromDataTable(dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //是否下载
                OthersHelper.GetWindowsStartupPosition();
                if (MessageBoxService.ShowMessage("是否下载当前导入的电子齿轮比参数...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    WriteUnitParameter();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_GET_CONFIG_FILE, "GetUnitConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：SetDefaultUnit
        //函数功能：设置默认单位
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void SetDefaultUnit()
        {
            if (GlobalCurrentInput.SelectedMotorType == "旋转电机")
            {
                SelectedPositionUnit = "cnt";
                SelectedTorqueUnit = "0.1%额定扭矩";
                SelectedSpeedUnit = "cnt/s";
                SelectedAccelerationUnit = "cnt/s^2";
            }
            else
            {
                SelectedPositionUnit = "mm";
                SelectedTorqueUnit = "0.1%额定扭矩";
                SelectedSpeedUnit = "mm/s";
                SelectedAccelerationUnit = "mm/s^2";
            }

            int iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                //设置单位公式
                OthersHelper.GetDefaultUnit();
            }

             //获取默认参数单位
            OthersHelper.GetSelectDefaultUnit();

            //获取默认示波器单位
            OthersHelper.GetOscilloscopeParameterUnitSet();

            //写入配置文件
            OthersHelper.WriteSelectedUnitIntoFile();

            //信息提示
            ShowNotification(2023);
        }

        //*************************************************************************
        //函数名称：SetSelectedUnit
        //函数功能：设置选中的单位
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void SetSelectedUnit()
        {
            int iRet = -1;

            iRet = HexHelper.CheckSerialPortStatus();
            if (iRet != RET.SUCCEEDED)
            {
                //获取默认参数单位
                OthersHelper.GetDefaultUnit();
                OthersHelper.GetSelectDefaultUnit();

                //获取默认示波器单位
                OthersHelper.GetOscilloscopeParameterUnitSet();

                //信息提示
                ShowNotification(2020);
            }
            else
            {
                //下达单位换算任务   
                CurrentUnit.bInitialized = false;
                OthersHelper.AssignUnitExchangeTask();
            }
        }
        
        //*************************************************************************
        //函数名称：EvaluationUnitParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.15
        //*************************************************************************
        public void EvaluationUnitParameter()
        {
            MotorRevolutions = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Revolutions", "Index"));//电机分辨率
            LoadShaftRevolutions = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Shaft Revolutions", "Index"));//负载轴分辨率    
        }

        //*************************************************************************
        //函数名称：LimitAmplitudeNavigation
        //函数功能：限幅与保护导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void LimitAmplitudeNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                if (!IsGearRatioSet)
                {
                    ShowHintInfo("请留意【电子齿轮比】配置");
                    IsGearRatioSet = true;
                }

                SoftwareStateParameterSet.CurrentPageName = PageName.LIMITAMPLITUDE;
                NavigationService.Navigate("LimitAmplitudeView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：GearRatioAlreadySet
        //函数功能：电子齿轮比已经设置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.24
        //*************************************************************************
        public void GearRatioAlreadySet()
        {
            IsGearRatioSet = true;
        }

        //*************************************************************************
        //函数名称：MotorFeedbackNavigation
        //函数功能：电机反馈导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.24
        //*************************************************************************
        public void MotorFeedbackNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.MOTORFEEDBACK;
                NavigationService.Navigate("MotorFeedbackView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void OnMotorRevolutionsChanged()//电机分辨率
        {
            GlobalCurrentInput.MotorRevolutions = MotorRevolutions;
            GetGearRatioCalculated();
        }
        public void OnLoadShaftRevolutionsChanged()//负载轴分辨率
        {
            GlobalCurrentInput.LoadShaftRevolutions = LoadShaftRevolutions;
            GetGearRatioCalculated();
        }   
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：GetUnitConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private DataTable GetUnitConfigToDataTable()
        {
            DataTable dt = new DataTable();

            OthersHelper.MakeValueToDataTable(FileInterface.GEARRATIO, "Motor Revolutions", "电机分辨率", MotorRevolutions, ref dt);
            OthersHelper.MakeValueToDataTable(FileInterface.GEARRATIO, "Load Shaft Revolutions", "负载轴分辨率", LoadShaftRevolutions, ref dt);
            
            return dt;         
        }

        //*************************************************************************
        //函数名称：GetUnitConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int GetUnitConfigFromDataTable(DataTable dt)
        {
            if (dt == null)
            {
                return RET.ERROR;
            }

            MotorRevolutions = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Motor Revolutions", "Default");
            LoadShaftRevolutions = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Load Shaft Revolutions", "Default");

            return RET.SUCCEEDED;         
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            MotorRevolutions = GlobalCurrentInput.MotorRevolutions;//电机分辨率
            LoadShaftRevolutions = GlobalCurrentInput.LoadShaftRevolutions;//负载轴分辨率                    
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private void AddParameterInfoDictionary(ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            dicParameterInfo.Add("Motor Revolutions", MotorRevolutions);
            dicParameterInfo.Add("Load Shaft Revolutions", LoadShaftRevolutions);            
        }

        //*************************************************************************
        //函数名称：GetGearRatioCalculated
        //函数功能：齿轮比计算
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.18
        //*************************************************************************
        private void GetGearRatioCalculated()
        {
            double dMotor = 0;
            double dDrive = 0;

            try
            {
                if (OthersHelper.IsInputInteger(MotorRevolutions))
                {
                    dMotor = Convert.ToDouble(MotorRevolutions);
                }
                else
                {
                    GearRatioHint = 0;
                    return;
                }

                if (OthersHelper.IsInputInteger(LoadShaftRevolutions))
                {
                    dDrive = Convert.ToDouble(LoadShaftRevolutions);

                    if (dDrive == 0)
                    {
                        GearRatioHint = 0;
                        return;
                    }
                }
                else
                {
                    GearRatioHint = 0;
                    return;
                }

                GearRatioHint = Math.Round(dMotor / dDrive, 2);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_GET_GEAR_RATIO_CALCULATE, "GetGearRatioCalculate", ex);
            }
        }

        //*************************************************************************
        //函数名称：RefreshComboBox
        //函数功能：更新下拉列表
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.07.23&2023.01.04
        //*************************************************************************
        private void RefreshComboBox()
        {
            //PositionUnit = new ObservableCollection<string>() { "cnt", "revs", "deg", "mm" };
            //TorqueUnit = new ObservableCollection<string>() { "0.1%额定扭矩", "Nm" };
            //SpeedUnit = new ObservableCollection<string>() { "cnt/s", "rpm", "rps", "deg/s", "mm/s" };
            //AccelerationUnit = new ObservableCollection<string>() { "cnt/s^2", "rpm/s", "rps/s", "deg/s^2", "mm/s^2" };
            if (GlobalCurrentInput.SelectedMotorType == "旋转电机")
            {
                PositionUnit = new ObservableCollection<string>() { "cnt", "revs", "deg" };
                TorqueUnit = new ObservableCollection<string>() { "0.1%额定扭矩", "Nm" };
                SpeedUnit = new ObservableCollection<string>() { "cnt/s", "rpm", "rps", "deg/s" };
                AccelerationUnit = new ObservableCollection<string>() { "cnt/s^2", "rpm/s", "rps/s", "deg/s^2" };
            }
            else
            {
                PositionUnit = new ObservableCollection<string>() { "cnt", "mm" };
                TorqueUnit = new ObservableCollection<string>() { "0.1%额定扭矩", "Nm" };
                SpeedUnit = new ObservableCollection<string>() { "cnt/s", "mm/s" };
                AccelerationUnit = new ObservableCollection<string>() { "cnt/s^2", "mm/s^2" };
            }


            SelectedPositionUnit = CurrentUnit.Position;
            SelectedTorqueUnit = CurrentUnit.Torque;
            SelectedSpeedUnit = CurrentUnit.Speed;
            SelectedAccelerationUnit = CurrentUnit.Acceleration;    
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowHintInfo(string strInfo)
        {
            ViewModelSet.Main?.ShowHintInfo(strInfo);
        }
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }
}