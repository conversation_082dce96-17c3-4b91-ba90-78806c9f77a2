﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace ServoStudio.GlobalConstant
{  
    //软件版本
    public static class SoftwareInfo
    {
        public const string VERSION = "00.04.04.04.2024.05.29";
        public const string RELEASED_DATE = "2024.05.29";
    }
    //示波器参数
    public static class Oscilloscope
    {
        public const int Collection = 12000;
        public const int ChannelNumber = 4;
        public const int QueueLength = 1000;
        public const int DynamicDisplayPoint = 300;
        public const int Vacant = 5;
        public const int CUPCore = 3;
    }

    //故障数据示波器参数
    public static class FaultDataOscilloscope
    {
        public const int Collection = 12000;
        public const int ChannelNumber = 8;
        public const int QueueLength = 1000;
        public const int DynamicDisplayPoint = 300;
        public const int Vacant = 5;
        public const int CUPCore = 3;
    }

    //默认载入配置参数路径
    public static class FilePath
    {
        public static string Ini = System.Environment.CurrentDirectory + "\\Config\\InitialConfig.ini";
        public static string Manual = System.Environment.CurrentDirectory + "\\Config\\Manual.pdf";

        public static string MaintenanceManual = System.Environment.CurrentDirectory + "\\Config\\MaintenanceManual.pdf";    //由Lilbert于2022.05.19添加伺服维护手册
        public static string XmlPath = System.Environment.CurrentDirectory + "\\Config\\OscilloscopePresetConfigs.xml";
        public static string XmlOptionsPath = System.Environment.CurrentDirectory + "\\Xml\\OscilloscopeOptions.xml";
        //public static string XmlPath = System.Environment.CurrentDirectory;

        public static string Parameter = System.Environment.CurrentDirectory + "\\Config\\";
        public static string MotorAndServoParameter = System.Environment.CurrentDirectory + "\\Config\\";
        public static string Monitor = System.Environment.CurrentDirectory + "\\Config\\Monitor.xlsx";
        public static string HardwareExplanation = System.Environment.CurrentDirectory + "\\Config\\HardwareExplanation.xlsx";
        public static string SoftwareErrorLog = System.Environment.CurrentDirectory + "\\Config\\SoftwareErrorLog.xlsx";
        public static string MotorLibraryLog = System.Environment.CurrentDirectory + "\\Config\\MotorLibraryLog.xlsx";
        public static string MotorLibrary = System.Environment.CurrentDirectory + "\\MotorLibrary\\";

        public static string ParameterLibrary = System.Environment.CurrentDirectory + "\\ParameterLibrary\\";//由Lilbert于2023.05.17添加参数库
        public static string FaultDataLibrary = System.Environment.CurrentDirectory + "\\FaultDataLibrary\\";//由Lilbert于2023.05.17添加故障数据配置参数库

        public static string ConfigServoName = System.Environment.CurrentDirectory + "\\Config\\ConfigServoName.txt";
        public static string ConfigServo = System.Environment.CurrentDirectory + "\\Config\\ServoConfigs.xml";
        //public static string ConfigServoName = System.Environment.CurrentDirectory + "\\Config\\ConfigServoName.xlsx";

    }

    public static class CompanyPath
    {
        public static string CompanyInformation = System.Environment.CurrentDirectory + "\\CompanyInformation\\CompanyInformation.ini";//由Lilbert于2023.09.28添加公司信息文件夹

    }

    public static class IconPath
    {
        public static string ServoEnabled = "pack://application:,,,/ServoStudio;component/Icon/伺服使能.png";
        public static string ServoDisabled = "pack://application:,,,/ServoStudio;component/Icon/伺服禁能.png";

        public static string AlarmClose = "pack://application:,,,/ServoStudio;component/Icon/报警展示开.png";
        public static string AlarmOpen = "pack://application:,,,/ServoStudio;component/Icon/报警展示.png";

        public static string MonitorClose = "pack://application:,,,/ServoStudio;component/Icon/数据监控开.png";
        public static string MonitorOpen = "pack://application:,,,/ServoStudio;component/Icon/数据监控.png";

        public static string SlideServoEnabled = "pack://application:,,,/ServoStudio;component/Icon/使能.png";
        public static string SlideServoDisabled = "pack://application:,,,/ServoStudio;component/Icon/禁能.png";
        public static string SlideServoFault = "pack://application:,,,/ServoStudio;component/Icon/故障.png";

        public static string ComputerControl = "pack://application:,,,/ServoStudio;component/Icon/上位机控制.png";
        public static string EcatControl = "pack://application:,,,/ServoStudio;component/Icon/Ecat控制.png";
    }

    //固件升级进程名称
    public static class FirmwareUpdateProcess
    {
        public static string ASSIGNMENT = "固件升级任务下达";
        public static string INITIAL = "起始帧";
        public static string DATA = "数据帧";
        public static string EOT = "结束符";
        public static string EMPTY = "空帧";

        public static string NONE = "无固件升级任务";
        public static string SUCCEED = "固件升级成功";
    }

    //返回值信息
    public class RET
    {
        public const int SUCCEEDED = 1;
        public const int NO_EFFECT = 0;
        public const int ERROR = -1;

        public const int FILE_OCCUPIED = -10;
        public const int OPEN_WORKBOOK_ERROR = -11;
        public const int OPEN_SHEET_ERROR = -12;
    }

    //Excel文件内容
    public class ExcelType
    {
        public const int EtherCAT = 10;
        public const int SoftwareErrorLog = 20;
        public const int MotorConfig = 30;
        public const int AmplitudeConfig = 31;
        public const int IOConfig = 32;
        public const int BrakeConfig = 33;
        public const int FilterConfig = 34;
        public const int CurrentLoop = 35;
        public const int PositionLoop = 36;
        public const int SpeedLoop = 37;
        public const int NormalSetting = 38;
        public const int GearRatio = 39;
        public const int Monitor = 40;
        public const int Oscilloscope = 50;
        public const int HardwareExplanation = 60;
        public const int Position = 70;
        public const int Speed = 71;
        public const int Torque = 72;
        public const int OfflineInertiaIdentification = 73;
        public const int SeekZero = 74;
        public const int MotorLibraryLog = 80;

        public const int FaultAcquisition = 90;//由Lilbert于2023.04.04添加故障采集类型
        public const int FaultDataConfig = 91;//由Lilbert于2023.06.12添加故障采集配置类型
        public const int AdvancedFeedback = 100;//由Lilbert于2023.11.16添加高级功能配置类型
    }

    //通信连接状态
    public class ConnectState
    {
        public const int NotConnect = 0;
        public const int Connect = 1;
        public const int Stop = 2;
        public const int Error = 3;

        public const string Explaination_NotConnect = "未连接";
        public const string Explaination_Connect = "连接";
        public const string Explaination_Stop = "暂停";
        public const string Explaination_Error = "异常";
        public const string Explaination_OffLine = "离线状态";

        public const string Explaination_NotConnect1 = "Unconnected";
        public const string Explaination_Connect1 = "Connect";
        public const string Explaination_Stop1 = "Stop";
        public const string Explaination_Error1 = "Error";
        public const string Explaination_OffLine1 = "OffLine";
    }

    //背景
    public class BackgroundState
    {
        public const int Selected = 2;
        public const int NotSelected = 4;

        public const int Green = 1;
        public const int Orange = 2;
        public const int Red = 3;
        public const int Black = 5;
        public const int Yellow = 6;
        public const int Blue = 8;
    }

    //控件可见属性值
    public class ControlVisibility
    {
        public const int Hidden = 0;
        public const int Visible = 1;
        public const int Collapsed = 2;
    }

    //参数分类\任务分类
    public class TaskName
    {
        public const string DiffParameters = "差异参数";

        public const string CIA402 = "CIA402";
        public const string Common = "轴共同参数";
        public const string Motor = "电机参数";
        public const string Basic = "基本配置参数";
        public const string Control = "运控参数";
        public const string Advanced = "高级配置参数";
        
        public const string DI = "端子输入参数";
        public const string DO = "端子输出参数";
        public const string FaultAndProtection = "故障与保护参数";
        public const string Auxiliary = "辅助参数";
        
        public const string Empty = "无任务";
        public const string Test = "回送测试";

        public const string AxisAddressReset = "轴地址重置";

        public const string ClearHardwareAlarm = "清除硬件报警";
        public const string HardwareAlarm = "硬件报警";
        public const string HardwareAllAlarm = "硬件所有报警";
        public const string BatchWrite = "参数批处理写入";
        public const string BatchRead = "参数批处理读取";
        public const string BatchReadForImport = "导入前参数批处理读取";

        public const string BatchWrite_ForImportConfig = "参数批处理写入为参数导入";
        public const string BatchRead_ForImportConfig = "参数批处理读取为参数导入";
        public const string ImportWriteAndRefresh = "导入参数后刷新";

        public const string Empty1 = "NoTask";
        public const string Test1 = "LoopbackTest";
        public const string ClearHardwareAlarm1 = "ClearHardwareAlarm";
        public const string HardwareAlarm1 = "HardwareAlarm";
        public const string HardwareAllAlarm1 = "HardwareAllAlarm";
        public const string BatchWrite1 = "ParameterBatchWrite";
        public const string BatchRead1 = "ParameterBatchRead";

        public const string AssigningAcquisition = "下达采集任务";
        public const string AskingAcquisitionState = "采集状态问询";
        public const string Acquiring = "正在采集";
        public const string UploadingAcquisition = "数据上传";
        public const string StopAcquisition = "停止采集";

        public const string AssigningFaultAcquisition = "下达故障数据采集任务";
        public const string FaultAskingAcquisitionState = "故障数据采集状态问询";
        public const string FaultAcquiring = "正在采集故障数据";
        public const string UploadingAcquisitionFault = "故障数据上传";
        public const string StopAcquisitionFault = "停止采集故障数据";
        public const string ClearAcquisitionFault = "清除采集故障数据";

        public const string FaultDataConfig = "故障数据配置参数";
        public const string FaultDataOscilloscopeConfig = "故障数据示波器配置参数";

        public const string FunctionGenerator = "函数发生器";
        public const string ThreeLoop = "三环调试";
        public const string Action = "运动调试";
        public const string Monitor = "监控参数";
        public const string ParameterTunning = "参数调优";
        public const string Rigidity = "刚性调试";

        public const string IntervalRefresh = "间隔刷新";
        public const string MotorFeedback = "电机反馈";
        public const string AdvancedFeedback = "高级配置参数反馈";
        public const string MotorFeedbackAutoLearn = "电机参数自学习";
        public const string MotorLineUVWSequenceIdentification = "电机动力线相序辨识";
        public const string AbsEncoderOffsetIdentification = "电机绝对值编码器偏置辨识";
        public const string MotorInertiaIdentification = "电机负载惯量辨识";
        public const string MotorParameterSelfTunning = "电机参数自整定";
        public const string LimitAmplitude = "限幅保护";
        public const string DigitalIO = "数字IO";
        public const string CurrentLoop = "电流环";
        public const string PositionLoop = "位置环";
        public const string SpeedLoop = "速度环";
        public const string NormalSetting = "一般设定";
        public const string GearRatio = "齿轮比";

        public const string SystemReset = "系统复位";
        public const string ControlWord = "控制字";
        public const string ServoEnabled = "伺服使能";
        public const string ServoDisabled = "伺服禁能";
        public const string Emergency = "急停";
        public const string PositionAction = "位置运动模式";
        public const string StopPositionAction = "停止位置运动模式";
        public const string AbsolutePositionAction = "绝对位置运动模式";
        public const string RelativePositionAction = "相对位置运动模式";
        public const string SpeedAction = "速度运动模式";
        public const string TorqueAction = "转矩运动模式";
        public const string StopAction = "运动模式停止";
        public const string PositionParameterTunning = "参数调位置优模式";
        public const string SpeedParameterTunning = "参数调速度优模式";
        public const string SeekZero = "回零模式";
        public const string StopSeekZero = "回零停止";
        public const string FaultReset = "故障清除";
        public const string AllFaultReset = "全部故障清除";

        public const string OnlineInertiaIdentification = "在线惯量识别";

        public const string ControlSourceOption = "控制权切换";//由Lilbert于2023.11.17添加控制权

        public const string FirmwareUpdate = "固件升级";       
        public const string AxisStatusA = "A轴状态";
        public const string AxisStatusB = "B轴状态";
        public const string AllAxisFactoryReset = "全部出厂值";
        public const string AllAxisSystemReset = "全部重启";
        public const string AllAxisEnabled = "全部使能";
        public const string AllAxisDisabled = "全部禁能";
        public const string AllAxisRunning = "全部运动";
        public const string AllAxisStop = "全部停止";
        public const string AllAxisConfigStop = "全部配置急停";

        public const string AllAxisTimestamp = "全部轴时间戳";     //由Lilbert与20211102添加

        public const string ABSEncoderSingleTurnBit = "编码器单圈分辨率位数";
        public const string ReadString = "读取字符串";

        public const string ReadString_ForSoftVersion = "读取版本号字符串";

        public const string UnitExchanged = "单位转换";
        public const string Timestamp = "时间戳";

        public const string Timestamp1 = "TimesTamp";

        public const string SystemParameterInitialize = "系统参数初始化";

        public const string FnDisabled = "伺服Fn禁能";

        public const string Jog = "Jog调试";
        public const string JogDriection = "Jog调试和电机旋转方向";
        public const string JogOperatingMode = "Jog运行模式";
        public const string JogDriectionOperatingMode = "Jog方向运行模式";
        public const string JogOperatingEnd = "Jog运行结束";
        public const string JogDriectionOperatingEnd = "Jog运行结束";
        public const string JogSwitch = "Jog模块使能禁能";
        public const string JogDriectionSwitch = "Jog模块使能禁能";
        public const string JogRunDriection = "Jog运转方向";
        public const string JogContinuously = "Jog持续运行";
        public const string JogDriectionContinuously = "Jog持续运行";
        public const string JogActualEnable = "Jog内部使能禁能状态";

        public const string ProgramJog = "程序Jog调试";
        public const string ProgramJogOperatingMode = "程序Jog运行模式";
        public const string ProgramJogOperatingEnd = "程序Jog运行结束";
        public const string ProgramJogSwitch = "程序Jog模块使能禁能";
        public const string ProgramJogContinuously = "程序Jog持续运行";
        public const string ProgramJogActualEnable = "程序Jog内部使能禁能状态";

        public const string ParameterIdentification = "电机参数辨识";
        public const string ParameterAutoLearnIdentification = "电机参数自学习辨识";
        public const string ParameterMotorLineUVWSequenceAbsEncoderOffsetIdentification = "电机动力线相序绝对值编码器偏置辨识";
        public const string ParameterInertiaIdentificationParameterSelfTunningIdentification = "电机负载惯量辨识和参数自整定";
        public const string ParameterMotorLineUVWSequenceIdentification = "电机动力线相序辨识";
        public const string ParameterMotorAbsEncoderOffsetIdentification = "电机绝对值编码器偏置辨识";
        public const string ParameterMotorInertiaIdentification = "电机负载惯量辨识";
        public const string ParameterMotorSelfTunning = "电机参数自整定";
        public const string ParameterIdentificationOperatingMode = "电机参数辨识运行模式";
        public const string ParameterAutoLearnIdentificationOperatingMode = "电机参数自学习辨识运行模式";
        public const string ParameterMotorLineUVWSequenceIdentificationOperatingMode = "电机动力线相序辨识运行模式";
        public const string ParameterInertiaIdentificationOperatingMode = "电机负载惯量辨识运行模式";
        public const string ParameterAbsEncoderOffsetIdentificationOperatingMode = "绝对值编码器偏置辨识运行模式";
        public const string ParameterSelfTunningOperatingMode = "电机参数自整定运行模式";
        public const string ParameterSelfTunningSwitch = "参数自整定开关";
        public const string ParameterIdentificationOperatingEnd = "电机参数辨识运行结束";
        public const string ParameterAutoLearnIdentificationOperatingEnd = "电机参数自学习辨识运行结束";
        public const string ParameterMotorLineUVWSequenceIdentificationOperatingEnd = "电机动力线相序辨识运行结束";
        public const string ParameterIdentificationOperatingOperatingEnd = "电机负载惯量辨识运行结束";
        public const string ParameterAbsEncoderOffsetIdentificationOperatingEnd = "电机动力线相序辨识运行结束";
        public const string ParameterSelfTunningOperatingEnd = "电机参数自整定运行结束";
        public const string ParameterIdentificationSwitch = "电机参数辨识使能禁能";
        public const string ParameterAutoLearnIdentificationSwitch = "电机参数自学习辨识使能禁能";
        public const string ParameterIdentificationListen = "电机参数辨识监听";
        public const string ParameterAutoLearnIdentificationListen = "电机参数自学习辨识监听";
        public const string ParameterMotorLineUVWSequenceIdentificationListen = "电机动力线相序辨识监听";
        public const string ParameterAbsEncoderOffsetIdentificationListen = "绝对值编码器偏置辨识监听";
        public const string ParameterInertiaIdentificationListen = "电机负载惯量辨识监听";
        public const string ParameterSelTunningListen = "电机参数自整定监听";
        public const string ParameterMotorLineUVWSequenceAbsEncoderOffsetIdentificationListen = "电机动力线相序绝对值编码器偏置辨识监听";
        public const string ParameterInertiaIdentificationParameterSelfTunningIdentificationListen = "电机负载惯量辨识和参数自整定监听";
        public const string ParameterIdentificationModify = "电机参数修改";
        public const string ParameterIdentificationUnload = "电机参数辨识退出";
        public const string ParameterIdentificationActualEnable = "电机参数辨识内部使能禁能状态";
        public const string ParameterAutoLearnIdentificationMotEstState = "电机参数自学习辨识内部状态";
        public const string MotorFeedbackAutoLearnLoadedUnloaded = "电机参数自学习退出";
        public const string ParameterMotorLineUVWSequenceIdentificationMotEstState = "电机动力线相序辨识内部状态";
        public const string ParameterAbsEncoderOffsetIdentificationMotEstState = "绝对值编码器偏置辨识内部状态";
        public const string ParameterInertiaIdentificationMotEstState = "电机负载惯量辨识内部状态";
        public const string ParameterSelfTunningMotEstState = "电机参数自整定内部状态";

        public const string OfflineInertiaIdentification = "离线惯量识别";
        public const string OfflineInertiaIdentificationOperatingMode = "离线惯量识别模式";
        public const string OfflineInertiaIdentificationOperatingEnd = "离线惯量识别结束";
        public const string OfflineInertiaIdentificationSwitch = "离线惯量识别使能禁能";
        public const string OfflineInertiaIdentificationContinuously = "离线惯量识别持续运行";
        public const string OfflineInertiaIdentificationModify = "离线惯量识别修改";
        public const string OfflineInertiaIdentificationUnload = "离线惯量识别退出";
        public const string OfflineInertiaIdentificationActualEnable = "离线惯量识别内部使能禁能状态";

        public const string ClearMotorEncoderMultilapsAndFaults = "清除电机编码器多圈和故障";
        public const string ClearMotorEncoderMultilaps = "清除电机编码器多圈";
        public const string ClearMotorEncoderFaults = "清除电机编码器故障";
    }

    //任务状态
    public class TaskState
    {
        public const int NO_EXECUTED = 0;
        public const int EXECUTING = 1;
        public const int EXECUTED = 2;
    }

    //功能码
    public class FunctionCode
    {
        public const string TEST_COMMUNICATION = "08";//回送测试
        public const string PARAMETER_READ = "0D";//参数读取
        public const string PARAMETER_WRITE = "0E";//参数写入
        public const string ACQUISITION = "01";//数据采集
        public const string HARDWAREALARM = "02";//获取硬件报警
        public const string UPDATE = "10";//固件升级

        public const string ERROR_TEST_COMMUNICATION = "88";//回送测试失败
        public const string ERROR_PARAMETER_READ = "8D";//参数读取失败
        public const string ERROR_PARAMETER_WRITE = "8E";//参数写入失败
        public const string ERROR_ACQUISITION = "81";//数据采集失败
    }

    public class AcquisitionExecutedCode
    {
        public const string ACQUISITION = "01";//数据采集
        public const string ASK_ACQUISITION = "02";//数据采集状态问询
        public const string UPLOAD_ACQUISITION = "03";//数据上传
        public const string STOP_ACQUISITION = "04";//停止采集
    }

    //故障数据功能码
    public class FaultFunctionCode
    {
        //public const string TEST_COMMUNICATION = "08";//回送测试
        //public const string PARAMETER_READ = "0D";//参数读取
        //public const string PARAMETER_WRITE = "0E";//参数写入
        public const string ACQUISITION = "03";//故障数据采集
        //public const string HARDWAREALARM = "02";//获取硬件报警
        //public const string UPDATE = "10";//固件升级

        //public const string ERROR_TEST_COMMUNICATION = "88";//回送测试失败
        //public const string ERROR_PARAMETER_READ = "8D";//参数读取失败
        //public const string ERROR_PARAMETER_WRITE = "8E";//参数写入失败
        public const string ERROR_ACQUISITION = "83";//数据采集失败
    }

    public class FaultAcquisitionExecutedCode
    {
        public const string ACQUISITION = "01";//数据采集
        public const string ASK_ACQUISITION = "02";//数据采集状态问询
        public const string UPLOAD_ACQUISITION = "03";//数据上传
        public const string CLEAR_ACQUISITION = "04";//故障数据采集清除
        public const string STOP_ACQUISITION = "05";//停止采集        
        public const string RETRANSMISSION_ACQUISITION = "06";//重传采集
    }

    //报文格式
    public class MessageFormat
    {
        public const string HEAD = "AA";//报头
        public const string END = "55";//报尾            
    }

    //信息提示图片
    public static class NotificationPicture
    {
        public static ImageSource NG_BULB= new BitmapImage(new Uri(@"Resource/bulb.png", UriKind.Relative));
        public static ImageSource OK_DIAMOND = new BitmapImage(new Uri(@"Resource/diamond.png", UriKind.Relative));
    }

    //时间周期
    public static class TimerPeriod
    {
        public static int PthreadTaskNormal = 300;
        public static int PthreadTaskFast = 125;
        public static int System = 1000;
        public static int HintInfo = 1000;
        public static int HintCountDown = 4;

        public static int MotorParameterIdentification = 4000;
        public static int OfflineInertiaIdentification = 2000;
        public static int Homing = 2000;
        public static int HardwareAlarm = 2000;

        public static int MotorParameterAutoLearnIdentification = 1000;
        public static int MotorLineUVWSequenceAbsEncoderOffsetIdentification = 1000;
        public static int InertiaIdentificationParameterSelfTunning = 1000;
    }

    //页面名称
    public class PageName
    {
        //系统时钟、侧边栏、参数识别等
        public const string MAIN = "主界面";
        public const string FIRMWAREUPDATE = "固件升级";
        public const string JOG = "Jog调试";
        public const string PROGRAMJOG = "程序Jog调试";
        public const string MOTORPARAMETERIDENTIFICATION = "电机参数识别";
        public const string OFFLINEINERTIAINENTIFICATION = "离线惯量辨识";

        //系统配置
        public const string COMMUNICATIONSET = "通信配置";
        public const string MOTORFEEDBACK = "电机反馈";
        public const string LIMITAMPLITUDE = "限幅保护";
        public const string NORMALSET = "一般设置";
        public const string DIGITALIO = "数字IO";
        public const string UNIT = "单位设置";
        public const string OFFLINEINENTIFICATION = "离线惯量辨识";  //由Lilbert于2021.11.02添加离线惯量辨识
        public const string JOGPAGE = "Jog调试页面";   //由Lilbert于2021.11.02添加Jog调试页面
        public const string PROGRAMJOGPAGE = "程序Jog调试页面";    //由Lilbert于2021.11.02添加程序Jog调试页面
        public const string MOTORPARAMETERIDENTIFICATIONPAGE = "电机参数识别页面";  //由Lilbert于2021.11.02添加电机参数识别页面
        public const string MOTORFEEDBACKAUTOLEARN = "电机参数自学习页面";//由Lilbert于2022.11.01添加电机参数自学习页面
        public const string MOTORLINEUVWSEQUENCEABSENCODEROFFSET = "电机动力线相序编码器初始位置辨识页面";//由Lilbert于2022.11.01添加电机动力线相序编码器初始位置辨识页面
        public const string MOTORDRIECTIONJOGPAGE = "Jog和电机旋转方向调试页面";   //由Lilbert于2022.11.02添加Jog和电机旋转方向调试页面
        public const string INERTIAINENTIFICATIONPARAMETERSELFTUNNING = "惯量辨识和参数自整定页面";  //由Lilbert于2022.11.18添加惯量辨识和参数自整定页面                                                                                         //高级功能
        public const string ADVANCEDFEEDBACK = "高级功能配置";

        //三环
        public const string SPEEDLOOP = "速度环";
        public const string CURRENTLOOP = "电流环";
        public const string POSITIONLOOP = "位置环";       

        //调试
        public const string OSCILLOSCOPE = "示波器";
        public const string FUNCTIONGENERATOR = "函数发生器";
        public const string ACTIONDEBUG = "运动调试";
        public const string PARAMETERTUNNING = "参数调优";

        //故障参数
        public const string FAULTDATAOSCILLOSCOPE = "故障数据示波器";
        public const string FAULTDATACONFIG = "故障数据示波器配置";

        //运动
        public const string SPEEDACTION = "速度模式";
        public const string POSITIONACTION = "位置模式";
        public const string TORQUEACTION = "转矩模式";
        public const string SEEKZERO = "寻零模式";

        //参数
        public const string CIA402 = "CIA402";
        public const string Common = "轴共同参数";
        public const string Motor = "电机参数";
        public const string Basic = "基本配置参数";
        public const string Control = "运控参数";
        public const string Advanced = "高级配置参数";
        public const string DI = "端子输入参数";
        public const string DO = "端子输出参数";
        public const string FaultAndProtection = "故障与保护参数";
        public const string Auxiliary = "辅助参数";

        public const string PARAMETERMONITOR = "参数监控";

        //帮助
        public const string SOFTWAREERROR = "软件报警";

        //报警
        public const string HARDWAREALARMHISTORY = "硬件历史报警";
        public const string HARDWAREALARMMEASURE = "硬件报警原因与措施";
        //public const string HISTORYDATAQUERY = "故障数据查询";

        //其他
        public const string MOTORLIBRARY = "电机参数库";
    }

    //示波器展示方式
    public class OscilloscopeDisplayMethod
    {
        public const int STATIC = 0;
        public const int DYNAMIC = 1;
    }  

    //故障数据示波器展示方式
    public class FaultDataOscilloscopeDisplayMethod
    {
        public const int STATIC = 0;
        public const int DYNAMIC = 1;
    }

    //轴编号
    public class AxisNumber
    {
        public const int A = 0;
        public const int B = 1;
    }

    //数字IO控制
    public class IOOperation
    {
        public const string FUNCTION_DI = "Function Select DI";//DI功能选择
        public const string LOGIC_DI = "Logic Select DI";//DI逻辑选择
        public const string FUNCTION_DO = "Function Select DO";//DO功能选择
        public const string LOGIC_DO = "Logic Select DO";//DO逻辑选择
    }

    //TabControl检索号
    public class TabControlIndex
    {
        public const int Calculate = 0;       
        public const int ThreeLoop = 1;
        public const int FunctionGenerator = 2;
        public const int Action = 3;
        public const int ParameterTunning = 4;
    }

    //功能选择
    public class FunctionSelectIndex
    {
        public const string NO_CONFIG = "0";//无配置
        public const string NEGATIVE_LIMIT = "1";//负限位
        public const string POSITIVE_LIMIT = "2";//正限位        
        public const string ORIGINAL_SWITCH = "3";//回零
        public const string DISABLED = "4";//禁能
        public const string ENABLED = "5";//使能
        public const string EMERGENCY_STOP = "6";//正常停止
        public const string CLEAR_FAULT = "7";//故障清除

        //public const string MOTOR_OVER_TEMPERATURE = "5";//电机过温
        //public const string START = "6";//启动
        //public const string NORMAL_STOP = "7";//正常停止
        //public const string QUICK_STOP = "8";//快速停止
        //public const string POSITIVE_ACTION = "9";//正向点动
        //public const string NEGATIVE_ACTION = "10";//反向点动
        //public const string CLEAR_FAULT = "11";//清除故障
        //public const string RESET = "12";//重置

        public const string NO_CONFIG_DO = "0";//无配置
        public const string GPIO_DO = "1";//GPIO
        public const string FAULT = "2";//故障
        public const string BRAKE = "3";//抱闸
        public const string REACH_POSITION = "4";//目标到达
        public const string ECAT_BUS_CONTROL = "5";//Ecat总线控制
    }
    
    //逻辑选择
    public class LogicSelectIndex
    {
        public const string LOW = "0";//低电平有效
        public const string HIGH = "1";//高电平有效
        public const string RISING_EDGE = "2";//上升沿有效
        public const string FALLING_EDGE = "3";//下降沿有效
        public const string DOUBLE_EDGE = "4";//双边沿有效

        public const string LOW_IF_VALID = "0";//有效时输出低电平
        public const string HIGN_IF_VALID = "1";//有效时输出高电平
    }

    //伺服状态定义
    public class ServoStatus
    {
        public const string RUNING = "使能";
        public const string PREPARATION = "禁能";
        public const string ERROR = "故障";
        public const string UNDEFINED = "未定义";

        public const string RUNING1 = "Enabled";
        public const string PREPARATION1 = "Disabled";
        public const string ERROR1 = "Fault";
        public const string UNDEFINED1 = "Undefined";
    }

    //默认单位
    public class DefaultUnit
    {
        public const string PositionUnit = "cnt";//位置单位
        public const string TorqueUnit = "0.1%额定扭矩";//转矩单位
        public const string SpeedUnit = "cnt/s";//速度单位
        public const string AccelerationUnit = "cnt/s^2";//加速度单位
    }

    //最大极限值集合
    public class LimitValue
    {
        public const int ReadTask = 2;
        public const int WriteTask = 3;
        public const int Disconnection = 10;
        public const int TaskExecutionTimes = 8;
        public const int DataExchangeRedundancy = 2;
    }

    //文件接口名称
    public class FileInterface
    {
        public const string SPEED = "SpeedModeInterface";
        public const string TORQUE = "TorqueModeInterface";
        public const string POSITION = "PositionModeInterface";
        public const string SPEEDLOOP = "SpeedLoopInterface";
        public const string CURRENTLOOP = "CurrentLoopInterface";
        public const string POSITIONLOOP = "PositionLoopInterface";
        public const string NORMALSETTING = "NormalSettingInterface";
        public const string LIMITAMPLITUDE = "LimitAmplitudeInterface";
        public const string MOTORFEEDBACK = "MotorFeedbackInterface";
        public const string MOTORFEEDBACKAUTOLEARN = "MotorFeedbackAutoLearnInterface";
        public const string MOTORLINEUVWSEQUENCEABSENCODEROFFSET = "MotorLineUVWSequenceAbsEncoderOffsetInterface";
        public const string MOTORINERTIALIDENTIFICATIONPARAMETERSELFTUNNING = "MotorInertiaIdentificationParameterSelfTunning";
        public const string OFFLINEINERTIAIDENTIFICATION = "OfflineInertiaIdentificationInterface";
        public const string SEEKZERO = "HomingInterface";
        public const string GEARRATIO = "GearRatioInterface";

        public const string FAULTDATACONFIG = "FaultDataConfig";
        public const string ADVANCED = "AdvancedInterface";
    }

    //错误代码
    //报警代码结构：View(2位）+ Level(2位）+ Function(2位）+ Code(2位）
    //报警代码名称：异常函数名
    public class ERROR
    {
        public const string CONVERTHELPER_DATASET_TO_LIST = "00020101";
        public const string CONVERTHELPER_DATASET_TO_OBSERVABLECOLLECTION = "00020102";
        public const string EXCELHELPER_GET_WHITE_PATH = "00010201";
        public const string EXCELHELPER_GET_READ_PATH = "00010202";
        public const string EXCELHELPER_GET_COLUMN_INFO = "00010203";
        public const string EXCELHELPER_GET_CONTENT_INFO = "00010204";
        public const string EXCELHELPER_GET_DATA_INTO_DATATABLE = "00010205";
        public const string EXCELHELPER_GET_DATA_INTO_DATATABLE_DEFAULT = "00010206";
        public const string EXCELHELPER_SET_EXCEL_COLUMN_INFO = "00010207";
        public const string EXCELHELPER_SET_EXCEL_CONTENT_INFO = "00010208";
        public const string EXCELHELPER_GET_DATA_INTO_EXCEL = "00010209";
        public const string EXCELHELPER_SET_EXCEL_COLUMN_STYLE = "00010210";
        public const string EXCELHELPER_SET_EXCEL_INFO_FOR_OSCILLOSCOPE = "00010211";
        public const string EXCELHELPER_WRITE_INTO_EXCEL_FOR_OSCILLOSCOPE = "00010212";
        public const string EXCELHELPER_READ_FROM_EXCEL_FOR_OSCILLOSCOPE = "00010213";
        public const string EXCELHELPER_GET_CONTENT_INFO_FOR_OSCILLOSCOPE = "00010214";

        public const string EXCELHELPER_WRITE_INTO_EXCEL_FOR_FAULT_ACQUISITION = "00010215";

        public const string OTHERSHELPER_RETRIEVE_DATATABLE = "00010301";
        public const string OTHERSHELPER_GET_CELL_VALUE_FROM_DATATABLE = "00010401";
        public const string OTHERSHELPER_DATATABLE_COPY = "00010501";
        public const string OTHERSHELPER_DATATABLE_UPDATE = "00010502";
        public const string OTHERSHELPER_CHECK_TITLE_OF_CONFIG = "00010503";
        public const string OTHERSHELPER_LIST_COPY = "00010504";
        public const string OTHERSHELPER_REFRESH_DICTIONARY = "00010505";
        public const string OTHERSHELPER_IS_OUT_OF_RANGE = "00010506";
        public const string OTHERSHELPER_GET_ACQUISITION_PERIOD = "00010507";
        public const string OTHERSHELPER_DATATABLE_EXPORT_UPDATE = "00010508";
        public const string OTHERSHELPER_DATATABLE_TO_TRANSMITING_DATA_INFO_SET = "00030509";
        public const string OTHERSHELPER_ADDRESS_DEVIATION = "00020510";
        public const string OTHERSHELPER_ADDRESS_RESTORE = "00020511";
        public const string OTHERSHELPER_GET_PARAMETER_READ_WRITE_SET = "00010512";
        public const string OTHERSHELPER_WRITE_CONTROL_WORD_SET = "00010513"; 
        public const string OTHERSHELPER_MAKE_VALUE_TO_DATATABLE = "00010514";
        public const string OTHERSHELPER_GET_RMS_VALUE = "00010515";
        public const string OTHERSHELPER_GET_ALARM_SET = "00010516";
        public const string OTHERSHELPER_GET_INITIAL_CONFIG_INFO = "00030517";
        public const string OTHERSHELPER_GET_UNIT_EXCHANGED_INFO = "00020518";
        public const string OTHERSHELPER_SYNCHRONIZATION = "00020519";
        public const string OTHERSHELPER_EXCHANGE_UNIT = "00020520";
        public const string OTHERSHELPER_GET_EXCHANGE_VALUE_BY_CURRENT_UNIT = "00020521";
        public const string OTHERSHELPER_CHECK_IS_INPUT_HEX = "00020522";
        public const string OTHERSHELPER_RETRIEVE_ERRORLOG_BY_DATETIME = "00020523";
        public const string OTHERSHELPER_DECIMAL_EXCHANGE = "00020524";

        public const string OTHERSHELPER_EXCEL_FOR_COMPARE = "00020525";
        public const string OTHERSHELPER_REMOVE_SUBSTRING = "00020526";

        public const string MICROSECONDHELPER_CONSTRUCTOR = "00020601";
        public const string MICROSECONDHELPER_START = "00010701";
        public const string MICROSECONDHELPER_RUN_TIMER = "00030702";
        public const string HEXHELPER_HEX_STRING_TO_BYTES = "00030801";
        public const string HEXHELPER_BYTES_TO_HEX_STRING = "00030802";
        public const string HEXHELPER_CRC16 = "00030803";
        public const string HEXHELPER_CONVERT_HEX_STRING_ENDIAN = "00030804";
        public const string HEXHELPER_CHECK_STRING_IS_LEGAL = "00010805";
        public const string HEXHELPER_TRANSMITTING_MESSAGE_TO_BYTES = "00010806";
        public const string HEXHELPER_RECEIVING_MESSAGE_FOR_TEST_COMMUNICATION = "00010807";
        public const string HEXHELPER_TRANSMITTING_DATA = "00010808";
        public const string HEXHELPER_RECEIVING_DATA = "00010809";
        public const string HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_READ = "00030810";
        public const string HEXHELPER_LITTLE_ENDIAN_HEX_STRING_TO_NORMAL_STRING = "00010811";
        public const string HEXHELPER_ADD_SERIAL_PORT_TASK = "00010812";
        public const string HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_WRITE = "00030813";
        public const string HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_ACQUISITION = "00030814";
        public const string HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_STOP_ACQUISITION = "00030815";
        public const string HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_HARDWAREALARM = "00030816";
        public const string HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_MISTAKE = "00030817";
        public const string HEXHELPER_TRANSMITTING_MESSAGE_FOR_SYSTEM_CONTROL = "00030818";
        public const string HEXHELPER_GET_STATUS_WORD = "00020819";
        public const string HEXHELPER_GET_STATUS_WORD_HIGHEIGHTBIT = "00020820";
        public const string HEXHELPER_WRITE_PARAMETER = "00010820";
        public const string HEXHELPER_WRITE_FIRMWARE_UPDATE_INITIAL_MESSAGE = "00030821";
        public const string HEXHELPER_RECEIVING_MESSAGE_FOR_PARAMETER_READ_STRING = "00030822";
        public const string HEXHELPER_RECEIVING_MESSAGE_FOR_PAULT_ACQUISITION = "00030823";

        public const string PTHREAD_SERIAL_PORT_TRANSMITING_RUN = "00010901";
        public const string PTHREAD_SERIAL_PORT_RECEIVING_RUN = "00010902";
        public const string PTHREAD_MICROSECONDS_OSCILLOSCOPE_DRAWING = "00010903";
        public const string PTHREAD_SERIAL_PORT_UPLOADING_RUN = "00010904";
        public const string PTHREAD_SYSTEM_CONTROL_RUN = "00010905";
        public const string YMODEM_GET_FIRMWARE_UPDATE_FILE = "00031001";
        public const string YMODEM_INITIAL_PACKET = "00031002";
        public const string YMODEM_GET_DATA_PACKET = "00031003";
        public const string YMODEM_GET_EMPTY_PACKET = "00031004";
        public const string YMODEM_FIRMWARE_UPDATE_ABORT = "00031005";
        public const string INFO_ADMINISTATION_KEY = "00011101";

        public const string MAIN_SHOW_NOTIFICATION = "01030101";
        public const string MAIN_SHOW_NOTIFICATION_MOTORFEEDBACK = "01030102";
        public const string MAIN_EXIT = "01030201";
        public const string MAIN_REFRESH_SERIAL_PORT_NUM = "01030301";
        public const string MAIN_SERIAL_PORT_PARAMETER_SET = "01030401";
        public const string MAIN_OPEN_SERIAL_PORT_CONNECTION = "01010402";
        public const string MAIN_CLOSE_SERIAL_PORT_CONNECTION = "01010501";
        public const string MAIN_INITIALIZE = "01010601";  
        public const string MAIN_IMPORT_CONFIG_FILE = "01010801";
        public const string MAIN_EXPORT_CONFIG_FILE = "01010802";
        public const string MAIN_EXPORT_MONITOR_FILE = "01010803";
        public const string MAIN_IMPORT_CONFIG_FILE_FOR_COMPARISONDIFF = "01010804";
        public const string MAIN_LOADED = "01030901";

        public const string MAIN_LOADED_CONFIG = "01030902";

        public const string MAIN_SET_DEFAULT_PARAMETER_PATH = "01021001";
        public const string MAIN_OPEN_MANUALBOOK = "01011101";
        public const string MAIN_CLOSING = "01011201";
        public const string MAIN_SYSTEM_RESTART = "01031301";
        public const string MAIN_DISPATCHER_TIMER_INITIALIZE = "01011401";
        public const string MAIN_TIMER_PARAMETER_MONITOR_TICK = "01031402";
        public const string MAIN_THREAD_POOL_SERIAL_PORT_TRANSMITTING = "01031501";
        public const string MAIN_DELETE_ELECTRONIC_ERROR_HISTORY = "01011515";
        public const string MAIN_DELETE_SOFTWARE_ERROR_HISTORY = "01011516";
        public const string MAIN_SHOW_EDITION = "01011517";
        public const string MAIN_SYSTEM_RESET = "01011518";
        public const string MAIN_FACTORY_RESET = "01011519";
        public const string MAIN_READ_EEPROM_TO_RAM = "01011520";
        public const string MAIN_SAVE_RAM_TO_EEPROM = "01011521";
        public const string MAIN_SERVO_DISABLED = "01011522";
        public const string MAIN_SERVO_ENABLED = "01011523";
        public const string MAIN_EMERGENCY_STOP = "01011524";
        public const string MAIN_FAULT_RESET = "01011525";    
        public const string MAIN_GET_ELECTRONIC_ERROR_DATA = "01011526";
        public const string MAIN_SWITCH_AXIS = "01011527";
        public const string MAIN_MOTOR_PARAMETER_IDENTIFICATION = "01011528";
        public const string MAIN_INERTIA_IDENTIFICATION = "01011529";
        public const string MAIN_SET_IS_ALARM_AUTO_EXPAND = "01011530";
        public const string MAIN_COMMUNICATION_NAVIGATION = "01031531";
        public const string MAIN_ALL_SERVO_DISABLED = "01031532";
        public const string MAIN_GET_EDITION_INFO = "01011533";
        public const string MAIN_OPEN_LOG_FILE = "01011534";
        public const string MAIN_UNIT_CONTROL = "01011535";
        public const string MAIN_MODIFY_PASSWORD = "01011536";
        public const string MAIN_SWITCH_LANGUAHE = "01011546";
        public const string MAIN_JOG_DEBUG = "01011537";
        public const string MAIN_PROGRAM_JOG_DEBUG = "01011538";
        public const string MAIN_OFFLINE_INERTIA_IDENTIFICATION = "01011539";
        public const string MAIN_MOTOR_LIBRARY_DETAILS = "01011540";
        public const string MAIN_SET_ONE_KET_SHORT_CUT = "01011541";
        public const string MAIN_ALL_FAULT_RESET = "01011545";
        public const string MAIN_ADD_SLAVE_AXIS_ID_LIBRARY_DETAILS = "01011546";

        public const string MAIN_HISTORY_DATA_QUERY = "01011547";
        public const string MAIN_HISTORY_DATA_CLEAR = "01011548";
        public const string MAIN_GET_FAULT_TRANSMITTING_CONTENT = "01011549";

        public const string MAIN_CONTROL_SOURCE_OPTION = "01011550";

        public const string SOFTWAREERRORLOG_RETRIEVE_LOG = "02030101";
        public const string SOFTWAREERRORLOG_LOADED = "02020201";
        public const string SOFTWAREERRORLOG_RETRIEVE_LOG_MAIN = "02010301";
        public const string SOFTWAREERRORLOG_GET_SOFTWARE_ERROR_DATA = "02010401";
        public const string SOFTWAREERRORLOG_SHOW_NOTIFICATION = "02010501";

        public const string PARAMETER_RETRIEVE_DATA = "03030101";
        public const string PARAMETER_LOADED = "03020201";
        public const string PARAMETER_UPDATE_DATA = "03030301";
        public const string PARAMETER_ADD_DATA = "03030401";
        public const string PARAMETER_DELETE_DATA = "03030501";
        public const string PARAMETER_SHOW_UPDATE_NOTIFICATION = "03010601";
        public const string PARAMETER_SHOW_ADD_NOTIFICATION = "03010701";
        public const string PARAMETER_SHOW_NOTIFICATION = "03010801";
        public const string PARAMETER_OVERVIEW_LOADED = "03020901";
        public const string PARAMETER_EXPORT_LOADED = "03020902";

        public const string MOTORFEEDBACK_LOADED = "04030101";
        public const string MOTORFEEDBACKAUTOLEARN_LOADED = "04030202";
        public const string MOTORLINEUVWSEQUENCEABSENCODEROFFSET_LOADED = "04030203";
        public const string INERTIALIDENTIFICATIONPARAMETERSELFTUNNING_LOADED = "04030204";
        public const string MOTORFEEDBACK_SAVE_MOTOR_CONFIG_FILE = "04020201";
        public const string MOTORFEEDBACK_GET_MOTOR_CONFIF_TO_DATATABLE = "04010301";
        public const string MOTORFEEDBACK_GET_MOTOR_CONFIG_FROM_DATATABLE = "04010302";
        public const string MOTORFEEDBACK_SHOW_NOTIFICATION = "04010401";
        public const string MOTORFEEDBACK_GET_MOTOR_CONFIG_FILE = "04020501";
        public const string MOTORFEEDBACK_MAKE_VALUE_TO_DATATABLE = "04030601";
        public const string MOTORFEEDBACK_CLEAR_MOTOR_CONFIG_VALUE = "04010701";
        public const string MOTORFEEDBACK_MAIN_EVT_MOTORFEEDBACK = "04010801";
        public const string MOTORFEEDBACK_GET_DEFAULT_PARAMETER = "04010901";
        public const string MOTORFEEDBACKAUTOLEARN_GET_DEFAULT_PARAMETER = "04010903";
        public const string MOTORLINEUVWSEQUENCEABSENCODEOFFSET_GET_DEFAULT_PARAMETER = "04010904";
        public const string INERTIALIDENTIFICATIONPARAMETERSELFTUNNING_GET_DEFAULT_PARAMETER = "04010905";
        public const string MOTORFEEDBACK_INTERFACE_EVALUATION_FROM_VARIABLE = "04010902";
        public const string MOTORFEEDBACK_PROPERTY_INITIALIZE = "04011001";
        public const string MOTORFEEDBACK_WRITE_PARAMETER = "04011101";
        public const string MOTORFEEDBACKAUTOLEARN_WRITE_PARAMETER = "04011102";
        public const string MOTORLINEUVWSEQUENCE_WRITE_PARAMETER = "04011103";
        public const string ABSENCODEROFFSET_WRITE_PARAMETER = "04011104";
        public const string INERTIALIDENTIFICATION_WRITE_PARAMETER = "04011105";
        public const string PARAMETERSELFTUNNING_WRITE_PARAMETER = "04011106";
        public const string MOTORFEEDBACK_ADD_PARAMETER_INFO_DICTIONARY = "04011201";
        public const string MOTORLINEUVWSEQUENCE_ADD_PARAMETER_INFO_DICTIONARY = "04011202";
        public const string ABSENCODEROFFSET_ADD_PARAMETER_INFO_DICTIONARY = "04011203";
        public const string INERTIALIDENTIFICATION_ADD_PARAMETER_INFO_DICTIONARY = "04011204";
        public const string PARAMETERSELFTUNNING_ADD_PARAMETER_INFO_DICTIONARY = "04011205";
        public const string MOTORFEEDBACK_READ_MOTOR_FEEDBACK_PARAMETER = "04011301";
        public const string MOTORFEEDBACKAUTOLEARN_READ_MOTOR_FEEDBACK_PARAMETER = "04011302";
        public const string MOTORINERTIALIDENTIFICATION_READ_PARAMETER = "04011306";
        public const string MOTORPARAMETERSELFTUNNING_READ_PARAMETER = "04011307";
        public const string MOTORABSENCODEEOFFSET_READ_MOTOR_FEEDBACK_PARAMETER = "04011304";
        public const string MOTORLINEUVWSEQUENCE_READ_MOTOR_FEEDBACK_PARAMETER = "04011307";
        public const string MOTORRLINEUVWSEQUENCEABSENCODEEOFFSET_READ_MOTOR_FEEDBACK_PARAMETER = "04011305";
        public const string MOTORRINERTIALIDENTIFICATIONPARAMETERSELFTUINNING_READ_PARAMETER = "04011306";
        public const string MOTORFEEDBACK_READ_MOTOR_FEEDBACK_PARAMETER_IDENTIFICATION = "04011303";
        public const string MOTORFEEDBACK_MOTOR_PARAMETER_IDENTIFICATION = "04011401";

        public const string LIMITAMPLITUDE_LOADED = "05030101";
        public const string LIMITAMPLITUDE_MAKE_VALUE_TO_DATATABLE = "05030201";
        public const string LIMITAMPLITUDE_GET_AMPLITUDE_CONFIG_TO_DATATABLE = "05010301";
        public const string LIMITAMPLITUDE_GET_AMPLITUDE_CONFIG_FROM_DATATABLE = "05010302";
        public const string LIMITAMPLITUDE_SAVE_AMPLITUDE_CONFIG_FILE = "05010401";

        public const string FAULTDATA_SAVE_FAULTDATA_CONFIG_FILE = "05010402";
        public const string FAULTDATA_GET_FAULTDATA_CONFIG_TO_DATATABLE = "05010403";
        public const string FAULTDATA_READ_ALARM_CACHE_SETTING_PARAMETER = "05010402";

        public const string LIMITAMPLITUDE_GET_AMPLITUDE_CONFIG_FILE = "05020501";
        public const string LIMITAMPLITUDE_SHOW_NOTIFICATION = "05010601";
        public const string LIMITAMPLITUDE_CLEAR_AMPLITUDE_CONFIG_VALUE = "05010701";
        public const string LIMITAMPLITUDE_MAIN_EVT_LIMITAMPLITUDE = "05010801";
        public const string LIMITAMPLITUDE_INTERFACE_EVALUATION_FROM_CONFIGFILE = "05010901";
        public const string LIMITAMPLITUDE_INTERFACE_EVALUATION_FROM_VARIABLE = "05010902";
        public const string LIMITAMPLITUDE_PROPERTY_INITIALIZE = "05011001";
        public const string LIMITAMPLITUDE_ADD_PARAMETER_INFO_DICTIONARY = "05011101";
        public const string LIMITAMPLITUDE_WRITE_PARAMETER = "05011201";
        public const string LIMITAMPLITUDE_READ_MOTOR_FEEDBACK_PARAMETER = "05011301";
        public const string LIMITAMPLITUDE_GET_DEFAULT_PARAMETER = "05011401";

        public const string DIGITALIO_LOADED = "06030101";
        public const string DIGITALIO_MAKE_VALUE_TO_DATATABLE = "06030201";
        public const string DIGITALIO_GET_IO_CONFIG_TO_DATATABLE = "06010301";
        public const string DIGITALIO_GET_IO_CONFIG_FROM_DATATABLE = "06010302";
        public const string DIGITALIO_SAVE_IO_CONFIG_FILE = "06010401";
        public const string DIGITALIO_GET_IO_CONFIG_FILE = "06020501";
        public const string DIGITALIO_SHOW_NOTIFICATION = "06010601";
        public const string DIGITALIO_CLEAR_IO_CONFIG_VALUE = "06010701";
        public const string DIGITALIO_PROPERTY_INITIALIZE = "06010801";
        public const string DIGITALIO_MAIN_EVT_DIGITAL = "06010901";
        public const string DIGITALIO_GET_DEFAULT_PARAMETER = "06011001";
        public const string DIGITALIO_INTERFACE_EVALUATION_FROM_VARIABLE = "06011002";
        public const string DIGITALIO_ADD_PARAMETER_INFO_DICTIONARY = "06011101";
        public const string DIGITALIO_WRITE_PARAMETER = "06011201";
        public const string DIGITALIO_READ_PARAMETER = "06011202";
        public const string DIGITALIO_EVENT_GET_PARAMETER = "06011301";

        public const string BRAKE_LOADED = "07030101";
        public const string BRAKE_MAKE_VALUE_TO_DATATABLE = "07030201";
        public const string BRAKE_GET_BRAKE_CONFIG_TO_DATATABLE = "07010301";
        public const string BRAKE_GET_BRAKE_CONFIG_FROM_DATATABLE = "07010302";
        public const string BRAKE_SAVE_BRAKE_CONFIG_FILE = "07010401";
        public const string BRAKE_GET_BRAKE_CONFIG_FILE = "07020501";
        public const string BRAKE_SHOW_NOTIFICATION = "07010601";
        public const string BRAKE_CLEAR_BRAKE_CONFIG_VALUE = "07010701";
        public const string BRAKE_MAIN_EVT_BRAKE = "07010801";
        public const string BRAKE_INTERFACE_EVALUATION_FROM_CONFIGFILE = "07010901";
        public const string BRAKE_INTERFACE_EVALUATION_FROM_VARIABLE = "07010902";
        public const string BRAKE_PROPERTY_INITIALIZE = "07011001";

        public const string SEEKZERO_LOADED = "09030101";
        public const string SEEKZERO_MAKE_VALUE_TO_DATATABLE = "09030201";
        public const string SEEKZERO_GET_CONFIG_TO_DATATABLE = "09010301";
        public const string SEEKZERO_GET_CONFIG_FROM_DATATABLE = "09010302";
        public const string SEEKZERO_SAVE_CONFIG_FILE = "09010401";
        public const string SEEKZERO_GET_CONFIG_FILE = "09010501";
        public const string SEEKZERO_SHOW_NOTIFICATION = "09010601";
        public const string SEEKZERO_MAIN_EVT_SEEKZERO = "09010701";
        public const string SEEKZERO_INTERFACE_EVALUATION_FROM_VARIABLE = "09010802";
        public const string SEEKZERO_PROPERTY_INITIALIZE = "09010901";
        public const string SEEKZERO_ADD_PARAMETER_INFO_DICTIONARY = "09011001";
        public const string SEEKZERO_WRITE_PARAMETER = "09011101";
        public const string SEEKZERO_READ_PARAMETER = "09011091";
        public const string SEEKZERO_GET_DEFAULT_PARAMETER = "09011301";
        public const string SEEKZERO_IS_SELECTED_DI_FUNCTION_MATCH = "09011401";

        public const string FUNCTIONGENERATOR_LOADED = "10030101";
        public const string FUNCTIONGENERATOR_SAVE_MOTOR_CONFIG_FILE = "10020201";
        public const string FUNCTIONGENERATOR_GET_MOTOR_CONFIF_TO_DATATABLE = "10010301";
        public const string FUNCTIONGENERATOR_GET_MOTOR_CONFIG_FROM_DATATABLE = "10010302";
        public const string FUNCTIONGENERATOR_SHOW_NOTIFICATION = "10010401";
        public const string FUNCTIONGENERATOR_GET_MOTOR_CONFIG_FILE = "10020501";
        public const string FUNCTIONGENERATOR_MAKE_VALUE_TO_DATATABLE = "10030601";
        public const string FUNCTIONGENERATOR_CLEAR_MOTOR_CONFIG_VALUE = "10010701";
        public const string FUNCTIONGENERATOR_MAIN_EVT_FUNCTIONGENERATOR = "10010801";
        public const string FUNCTIONGENERATOR_INTERFACE_EVALUATION_FROM_CONFIGFILE = "10010901";
        public const string FUNCTIONGENERATOR_INTERFACE_EVALUATION_FROM_VARIABLE = "10010902";
        public const string FUNCTIONGENERATOR_PROPERTY_INITIALIZE = "10011001";
        public const string FUNCTIONGENERATOR_EVALUATION = "10011101";
        public const string FUNCTIONGENERATOR_GET_INDEX_AND_DATATYPE = "10011201";
        public const string FUNCTIONGENERATOR_CREATE = "11031301";
        public const string FUNCTIONGENERATOR_CONTROL = "11031302";
        public const string FUNCTIONGENERATOR_WRITE_SPEED_LOOP_PARAMETER = "11011401";
        public const string FUNCTIONGENERATOR_WRITE_CURRENT_LOOP_PARAMETER = "11011402";
        public const string FUNCTIONGENERATOR_WRITE_POSITION_LOOP_PARAMETER = "11011403";

        public const string OSCILLOSCOPE_COMBOBOX_INITIALIZE = "11010101";
        public const string OSCILLOSCOPE_LOADED = "11010201";
        public const string OSCILLOSCOPE_INTERFACE_EVALUATION_FROM_VARIABLE = "11010301";
        public const string OSCILLOSCOPE_ACQUISITION_DATA_DISPLAY = "11030401";

        public const string FAULT_DATA_OSCILLOSCOPE_ACQUISITION_DATA_DISPLAY = "11030402";

        public const string OSCILLOSCOPE_DYNAMIC_X_START_POINT = "11010501";
        public const string OSCILLOSCOPE_DYNAMIC_Y_MAX_POINT = "11020502";

        public const string FAULT_DATA_OSCILLOSCOPE_DYNAMIC_X_START_POINT = "11010503";
        public const string FAULT_DATA_OSCILLOSCOPE_DYNAMIC_Y_MAX_POINT = "11020504";

        public const string OSCILLOSCOPE_DATA_BINDING = "11030601";

        public const string FAULT_DATA_OSCILLOSCOPE_DATA_BINDING = "11030602";

        public const string OSCILLOSCOPE_CLEAR_DATA = "11010701";

        public const string FAULT_DATA_OSCILLOSCOPE_CLEAR_DATA = "11010702";

        public const string OSCILLOSCOPE_TURN_ON = "11010801";

        public const string FAULT_DATA_OSCILLOSCOPE_TURN_ON = "11010802";

        public const string OSCILLOSCOPE_CHECK_SAMPLING_PARAMETER_CORRECTED = "11010901";

        public const string FAULT_DATA_OSCILLOSCOPE_CHECK_SAMPLING_PARAMETER_CORRECTED = "11010902";

        public const string OSCILLOSCOPE_SHOW_NOTIFICATION = "11011001";
        public const string OSCILLOSCOPE_GET_SAMPLING_PERIOD_AND_POINTS = "11011101";

        public const string FAULT_DATA_OSCILLOSCOPE_GET_SAMPLING_PERIOD_AND_POINTS = "11011102";

        public const string OSCILLOSCOPE_REFRESH_SAMPLING_DURATION_COMBO_BOX = "11011201";
        public const string OSCILLOSCOPE_ON_TRIGGER_LEVEL_CHANGED = "11011301";
        public const string OSCILLOSCOPE_PARAMETER_ACQUISITION = "11011401";
        public const string OSCILLOSCOPE_GET_TRANSMITING_CONTENT = "11021501";
        public const string OSCILLOSCOPE_DISPLAY_OSCILLOSCOPE_LOOP = "11031601";
        public const string OSCILLOSCOPE_PARAMETER_ACQUISITION_EXPORT = "11011701";
        public const string OSCILLOSCOPE_PARAMETER_ACQUISITION_IMPORT = "11011801";
        public const string OSCILLOSCOPE_INTERFACE_EVALUATION_FROM_FILE = "11011901";
        public const string OSCILLOSCOPE_LAST_SAMPLING_DATA_EVALUATION = "11012001";
        public const string OSCILLOSCOPE_LAST_SAMPLING_PARAMETER_EVALUATION = "11012002";
        public const string OSCILLOSCOPE_PARAMTER_ACQUISITION_LAST = "11012101";
        public const string OSCILLOSCOPE_INTERFACE_EVALUATION_FROM_LAST = "11012201";
        public const string OSCILLOSCOPE_STATIC_Y_MIN_POINT = "11012301";

        public const string FAULT_DATA_OSCILLOSCOPE_STATIC_Y_MIN_POINT = "11012302";

        public const string OSCILLOSCOPE_PLOTTER_MOUSE_LEFT_BUTTON_UP = "11022401";

        public const string FAULT_DATA_OSCILLOSCOPE_PLOTTER_MOUSE_LEFT_BUTTON_UP = "11022402";

        public const string OSCILLOSCOPE_GET_INDEX_OF_ARRAY_WAVE_DATA = "11022501";
        public const string OSCILLOSCOPE_GET_MAXMIN_OF_ARRAY_WAVE_DATA = "11022502";

        public const string FAULT_DATA_OSCILLOSCOPE_GET_INDEX_OF_ARRAY_WAVE_DATA = "11022503";
        public const string FAULT_DATA_OSCILLOSCOPE_GET_MAXMIN_OF_ARRAY_WAVE_DATA = "11022504";
        public const string FAULT_DATA_OSCILLOSCOPE_GET_CONFIG_FILE = "11022505";

        public const string OSCILLOSCOPE_GET_DEFAULT_ADJUSTMENT_PARAMETER = "11012601";
        public const string OSCILLOSCOPE_READ_ADJUSTMENT_PARAMETER = "11012701";
        public const string OSCILLOSCOPE_READ_ADJUSTMENT_PARAMETER_SWITCHAXIS = "11012701";
        public const string OSCILLOSCOPE_ADD_PARAMETER_INFO_DICTIONARY = "11012801";
        public const string OSCILLOSCOPE_POSITION_MODE_ACTION = "11013001";
        public const string OSCILLOSCOPE_SPEED_MODE_ACTION = "11013002";
        public const string OSCILLOSCOPE_TORQUE_MODE_ACTION = "11013003";
        public const string OSCILLOSCOPE_STOP_POSITION_MODE_ACTION = "11013004";
        public const string OSCILLOSCOPE_SPEED_MODE_PARAMETERTUNNING = "11013005";
        public const string OSCILLOSCOPE_POSITION_MODE_PARAMETERTUNNING = "11013006";
        public const string OSCILLOSCOPE_GET_AVERAGE_OF_ARRAY_WAVE_DATA = "11013101";
        public const string OSCILLOSCOPE_GET_RMS_OF_ARRAY_WAVE_DATA = "11013102";

        public const string FAULT_DATA_OSCILLOSCOPE_GET_AVERAGE_OF_ARRAY_WAVE_DATA = "11013103";
        public const string FAULT_DATA_OSCILLOSCOPE_GET_RMS_OF_ARRAY_WAVE_DATA = "11013104";

        public const string OSCILLOSCOPE_GET_TRIGGER_LEVEL_VALUE = "11013201";

        public const string OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS = "11012702";

        public const string CURRENTLOOP_LOADED = "12030101";
        public const string CURRENTLOOP_MAKE_VALUE_TO_DATATABLE = "12030201";
        public const string CURRENTLOOP_GET_CONFIG_TO_DATATABLE = "12010301";
        public const string CURRENTLOOP_GET_CONFIG_FROM_DATATABLE = "12010302";
        public const string CURRENTLOOP_SAVE_CONFIG_FILE = "12010401";
        public const string CURRENTLOOP_GET_CONFIG_FILE = "12020501";
        public const string CURRENTLOOP_SHOW_NOTIFICATION = "12010601";
        public const string CURRENTLOOP_MAIN_EVT_CURRENTLOOP = "12010701";
        public const string CURRENTLOOP_INTERFACE_EVALUATION_FROM_VARIABLE = "12010802";
        public const string CURRENTLOOP_PROPERTY_INITIALIZE = "12010901";
        public const string CURRENTLOOP_ADD_PARAMETER_INFO_DICTIONARY = "12011001";
        public const string CURRENTLOOP_WRITE_PARAMETER = "12011101";
        public const string CURRENTLOOP_READ_PARAMETER = "12011201";
        public const string CURRENTLOOP_GET_DEFAULT_PARAMETER = "12011301";
        public const string CURRENTLOOP_ANALYSE_NOTCH_FILTER_CONFIG = "12011401";

        public const string SPEEDLOOP_LOADED = "13030101";
        public const string SPEEDLOOP_MAKE_VALUE_TO_DATATABLE = "13030201";
        public const string SPEEDLOOP_GET_CONFIG_TO_DATATABLE = "13010301";
        public const string SPEEDLOOP_GET_CONFIG_FROM_DATATABLE = "13010302";
        public const string SPEEDLOOP_SAVE_CONFIG_FILE = "13010401";
        public const string SPEEDLOOP_GET_CONFIG_FILE = "13020501";
        public const string SPEEDLOOP_SHOW_NOTIFICATION = "13010601";
        public const string SPEEDLOOP_MAIN_EVT_SPEEDLOOP = "13010701";
        public const string SPEEDLOOP_INTERFACE_EVALUATION_FROM_VARIABLE = "13010802";
        public const string SPEEDLOOP_PROPERTY_INITIALIZE = "13010901";
        public const string SPEEDLOOP_ADD_PARAMETER_INFO_DICTIONARY = "13011001";
        public const string SPEEDLOOP_WRITE_PARAMETER = "13011101";
        public const string SPEEDLOOP_READ_PARAMETER = "13011201";
        public const string SPEEDLOOP_GET_DEFAULT_PARAMETER = "13011301";

        public const string POSITIONLOOP_LOADED = "14030101";
        public const string POSITIONLOOP_MAKE_VALUE_TO_DATATABLE = "14030201";
        public const string POSITIONLOOP_GET_CONFIG_TO_DATATABLE = "14010301";
        public const string POSITIONLOOP_GET_CONFIG_FROM_DATATABLE = "14010302";
        public const string POSITIONLOOP_SAVE_CONFIG_FILE = "14010401";
        public const string POSITIONLOOP_GET_CONFIG_FILE = "14020501";
        public const string POSITIONLOOP_SHOW_NOTIFICATION = "14010601";
        public const string POSITIONLOOP_MAIN_EVT_POSITIONLOOP = "14010701";
        public const string POSITIONLOOP_INTERFACE_EVALUATION_FROM_VARIABLE = "14010802";
        public const string POSITIONLOOP_PROPERTY_INITIALIZE = "14010901";
        public const string POSITIONLOOP_ADD_PARAMETER_INFO_DICTIONARY = "14011001";
        public const string POSITIONLOOP_WRITE_PARAMETER = "14011101";
        public const string POSITIONLOOP_READ_PARAMETER = "14011201";
        public const string POSITIONLOOP_GET_DEFAULT_PARAMETER = "14011301";

        public const string ELECTRONICERRORLOG_LOADED = "15020101";
        public const string ELECTRONICERRORLOG_GET_ELECTRONIC_ERROR_DATA = "15010201";
        public const string ELECTRONICERRORLOG_SHOW_NOTIFICATION = "15010301";

        public const string PARAMETERMONITOR_LOADED = "16020101";
        public const string PARAMETERMONITOR_GET_IS_MONITORED_STATUS = "16010201";
        public const string PARAMETERMONITOR_REFRESH_MONITOR_DATATABLE = "16020301";

        public const string PARAMETERREADWRITE_LOADED = "17020101";
        public const string PARAMETERREADWRITE_RETRIEVE_DATATABLE = "17020102";
        public const string PARAMETERREADWRITE_SHOW_NOTIFICATION = "17010201";
        public const string PARAMETERREADWRITE_PARAMETER_WRITE = "17030301";
        public const string PARAMETERREADWRITE_PARAMETER_WRITE_CANCEL = "17030302";
        public const string PARAMETERREADWRITE_PARAMETER_READ = "17030303";
        public const string PARAMETERREADWRITE_DOWNLOAD_BUTTON_VISIBILITY = "17030401";
        public const string PARAMETERREADWRITE_CHANGE_ALL_DOWNLOAD_BUTTON_VISIBILITY = "17030501";
        public const string PARAMETERREADWRITE_RESTART_TIMER_WORK = "17030601";
        public const string PARAMETERREADWRITE_CHECK_IS_EXIST_OTHER_PARAMETER_DOWNLOAD = "17010701";
        public const string PARAMETERREADWRITE_GET_INDEX_AND_DATATYPE = "17010801";

        public const string COMMUNICATIONSET_LOADED = "18030101";
        public const string COMMUNICATIONSET_SHOW_NOTIFICATION = "18010201";
        public const string COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_TEST_COMMUNICATION = "18030301";
        public const string COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_READ = "18030302";
        public const string COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE = "18030303";
        public const string COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_PARAMETER_WRITE_FOR_TIMESTAMP = "18030310";
        public const string COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_ACQUISITION = "18030304";
        public const string COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_STOP_ACQUISITION = "18030305";
        public const string COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_HARDWARE_ALARM = "18030306";
        public const string COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_UPDATE = "18030307";
        public const string COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_ALL_AXIS = "18030308";
        public const string COMMUNICATIONSET_SERIAL_PORT_DATA_TRANSMITING_FOR_AXIS_STATUS = "18030309";

        public const string COMMUNICATIONSET_AXIS_ADDRESS_RESET = "18030310";

        public const string NORMALSETTING_LOADED = "19030101";
        public const string NORMALSETTING_MAKE_VALUE_TO_DATATABLE = "19030201";
        public const string NORMALSETTING_GET_CONFIG_TO_DATATABLE = "19010301";
        public const string NORMALSETTING_GET_CONFIG_FROM_DATATABLE = "19010302";
        public const string NORMALSETTING_SAVE_CONFIG_FILE = "19010401";
        public const string NORMALSETTING_GET_CONFIG_FILE = "19020501";
        public const string NORMALSETTING_SHOW_NOTIFICATION = "19010601";
        public const string NORMALSETTING_MAIN_EVT_NORMALSETTING = "19010701";
        public const string NORMALSETTING_INTERFACE_EVALUATION_FROM_VARIABLE = "19010802";
        public const string NORMALSETTING_PROPERTY_INITIALIZE = "19010901";
        public const string NORMALSETTING_ADD_PARAMETER_INFO_DICTIONARY = "19011001";
        public const string NORMALSETTING_WRITE_PARAMETER = "19011101";
        public const string WRITE_MOTORDRIECTION_PARAMETER = "19011102";
        public const string NORMALSETTING_READ_PARAMETER = "19011201";
        public const string READMOTORDRIECTION_PARAMETER = "19011202";
        public const string NORMALSETTING_GET_DEFAULT_PARAMETER = "19011301";
        public const string NORMALSETTING_GET_GEAR_RATIO_CALCULATE = "19011401";

        public const string POSITION_LOADED = "20030101";
        public const string POSITION_MAKE_VALUE_TO_DATATABLE = "20030201";
        public const string POSITION_GET_CONFIG_TO_DATATABLE = "20010301";
        public const string POSITION_GET_CONFIG_FROM_DATATABLE = "20010302";
        public const string POSITION_SAVE_CONFIG_FILE = "20010401";
        public const string POSITION_GET_CONFIG_FILE = "20020501";
        public const string POSITION_SHOW_NOTIFICATION = "20010601";
        public const string POSITION_MAIN_EVT_POSITION = "20010701";
        public const string POSITION_INTERFACE_EVALUATION_FROM_VARIABLE = "20010802";
        public const string POSITION_PROPERTY_INITIALIZE = "20010901";
        public const string POSITION_ADD_PARAMETER_INFO_DICTIONARY = "20011001";
        public const string POSITION_WRITE_PARAMETER = "20011101";
        public const string POSITION_READ_PARAMETER = "20011201";
        public const string POSITION_GET_DEFAULT_PARAMETER = "20011301";

        public const string SPEED_LOADED = "21030101";
        public const string SPEED_MAKE_VALUE_TO_DATATABLE = "21030201";
        public const string SPEED_GET_CONFIG_TO_DATATABLE = "21010301";
        public const string SPEED_GET_CONFIG_FROM_DATATABLE = "21010302";
        public const string SPEED_SAVE_CONFIG_FILE = "21010401";
        public const string SPEED_GET_CONFIG_FILE = "21021501";
        public const string SPEED_SHOW_NOTIFICATION = "21010601";
        public const string SPEED_MAIN_EVT_SPEED = "21010701";
        public const string SPEED_INTERFACE_EVALUATION_FROM_VARIABLE = "21010802";
        public const string SPEED_PROPERTY_INITIALIZE = "21010901";
        public const string SPEED_ADD_PARAMETER_INFO_DICTIONARY = "21011001";
        public const string SPEED_WRITE_PARAMETER = "21011101";
        public const string SPEED_READ_PARAMETER = "21011211";
        public const string SPEED_GET_DEFAULT_PARAMETER = "21011301";

        public const string TORQUE_LOADED = "22030101";
        public const string TORQUE_MAKE_VALUE_TO_DATATABLE = "22030201";
        public const string TORQUE_GET_CONFIG_TO_DATATABLE = "22010301";
        public const string TORQUE_GET_CONFIG_FROM_DATATABLE = "22010302";
        public const string TORQUE_SAVE_CONFIG_FILE = "22010401";
        public const string TORQUE_GET_CONFIG_FILE = "22022501";
        public const string TORQUE_SHOW_NOTIFICATION = "22010601";
        public const string TORQUE_MAIN_EVT_TORQUE = "22010701";
        public const string TORQUE_INTERFACE_EVALUATION_FROM_VARIABLE = "22010802";
        public const string TORQUE_PROPERTY_INITIALIZE = "22010901";
        public const string TORQUE_ADD_PARAMETER_INFO_DICTIONARY = "22011001";
        public const string TORQUE_WRITE_PARAMETER = "22011101";
        public const string TORQUE_READ_PARAMETER = "22011221";
        public const string TORQUE_GET_DEFAULT_PARAMETER = "22011301";

        public const string IDENTIFICATION_LOADED = "23010101";
        public const string IDENTIFICATION_ADD_PARAMETER_INFO_DICTIONARY = "23010201";
        public const string IDENTIFICATION_SHOW_NOTIFICATION = "23010301";
        public const string IDENTIFICATION_READ = "23010401";
        public const string AUTOLEARN_IDENTIFICATION_READ = "23010403";
        public const string MOTORLINEUVWSEQUENCE_IDENTIFICATION_READ = "23010404";
        public const string ABSENCODEOFFSET_IDENTIFICATION_READ = "23010405";
        public const string INERTIALIDENTIFICATION_IDENTIFICATION_READ = "23010406";
        public const string PARAMETERSELFTUNNING_IDENTIFICATION_READ = "23010407";
        public const string MOTORLINEUVWSEQUENCEABSENCODEOFFSET_IDENTIFICATION_READ = "23010406";
        public const string INERTIALIDENTIFICATIONPARAMETERSELFTUNNING_IDENTIFICATION_READ = "23010407";
        public const string IDENTIFICATION_WRITE = "23010402";
        public const string IDENTIFICATION_EVENT = "23010501";
        public const string IDENTIFICATION_START_MOTOR_PARAMETER_IDENTIFICATION = "23010601";

        public const string INERTIAIDENTIFICATION_LOADED = "24030101";
        public const string INERTIAIDENTIFICATION_MAKE_VALUE_TO_DATATABLE = "24030201";
        public const string INERTIAIDENTIFICATION_GET_CONFIG_TO_DATATABLE = "24010301";
        public const string INERTIAIDENTIFICATION_GET_CONFIG_FROM_DATATABLE = "24010302";
        public const string INERTIAIDENTIFICATION_SAVE_CONFIG_FILE = "24010401";
        public const string INERTIAIDENTIFICATION_GET_CONFIG_FILE = "24021501";
        public const string INERTIAIDENTIFICATION_SHOW_NOTIFICATION = "24010601";
        public const string INERTIAIDENTIFICATION_MAIN_EVT_INERTIAIDENTIFICATION = "24010701";
        public const string INERTIAIDENTIFICATION_INTERFACE_EVALUATION_FROM_VARIABLE = "24010802";
        public const string INERTIAIDENTIFICATION_PROPERTY_INITIALIZE = "24010901";
        public const string INERTIAIDENTIFICATION_ADD_PARAMETER_INFO_DICTIONARY = "24011001";
        public const string INERTIAIDENTIFICATION_WRITE_PARAMETER = "24011101";
        public const string INERTIAIDENTIFICATION_READ_PARAMETER = "24011201";
        public const string INERTIAIDENTIFICATION_GET_DEFAULT_PARAMETER = "24011301";

        public const string FIRMWAREUPDATE_IMPORT_UPDATE_FILE = "25020101";

        public const string UNIT_LOADED = "26010101";


        public const string CLEAR_MULTILAPS_AND_FAULTS = "26010102";
        public const string NORMAL_SETTING_INITIALIZE = "26010103";

        public const string ADVANCEDFEEDBACK_PROPERTY_INITIALIZE = "28010101";
        public const string ADVANCEDFEEDBACK_ADD_PARAMETER_INFO_DICTIONARY = "28010102";
        public const string ADVANCEDFEEDBACK_GET_DEFAULT_PARAMETER = "28010103";
        public const string ADVANCEDFEEDBACK_INTERFACE_EVALUATION_FROM_VARIABLE = "28010104";
        public const string ADVANCEDFEEDBACK_LOADED = "28010105";
        public const string ADVANCEDFEEDBACK_WRITE_PARAMETER = "28010106";
        public const string ADVANCEDFEEDBACK_SAVE_CONFIG_FILE = "28010107";
        public const string ADVANCEDFEEDBACK_GET_CONFIG_TO_DATATABLE = "28010108";
        public const string ADVANCEDFEEDBACK_GET_CONFIG_FILE = "28010109";
        public const string ADVANCEDFEEDBACK_GET_CONFIG_FROM_DATATABLE = "28010110";
        public const string ADVANCEDFEEDBACK_READ_PARAMETER = "28010111";
    }
}
