﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using ServoStudio.Models;
using System.Data;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class ParameterMonitorViewModel
    {
        #region 字段
        private ObservableCollection<ParameterMonitorSet> obsParameterMonitor = new ObservableCollection<ParameterMonitorSet>();
        #endregion

        #region 属性
        public virtual ObservableCollection<ParameterMonitorSet> ParameterMonitor { get; set; }//参数监控集合   
        #endregion

        #region 构造函数
        public ParameterMonitorViewModel()
        {
            ViewModelSet.ParameterMonitor = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.PARAMETERMONITOR;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：ParameterMonitorLoaded
        //函数功能：载入
        //
        //输入参数：
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.04
        //*************************************************************************
        public void ParameterMonitorLoaded()
        {
            int iRet = -1;

            try
            {
                ParameterMonitor = new ObservableCollection<ParameterMonitorSet>();
                ParameterMonitor.Clear();

                iRet = ConvertHelper.DataTableToObservableCollection(GlobalParameterSet.dt, ref obsParameterMonitor);
                if (iRet != RET.SUCCEEDED)
                {
                    return;
                }

                foreach (var i in obsParameterMonitor)
                {
                    if (i.Classification == TaskName.Monitor)
                    {
                        if (i.Name == "Position Actual Value" || i.Name == "Velocity Actual Value" || i.Name == "Q Axis Actual Current" ||
                            i.Name == "Alarm Set0" || i.Name == "Alarm Set1" || i.Name == "Alarm Set2" || i.Name == "Alarm Set3" || 
                            i.Name == "Actual Enable" || i.Name == "Motor Parameter Identification State")//不监控这些参数
                        {
                            continue;
                        }

                        ParameterMonitor.Add(new ParameterMonitorSet()
                        {
                            Classification = i.Classification,
                            Index = i.Index,
                            Name = i.Name,
                            Description = i.Description,
                            DataType = i.DataType,
                            Unit = i.Unit,
                            IsMonitored = OthersHelper.CheckIsMonitoredStatus(i.Name)
                        });
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERMONITOR_LOADED, "ParameterMonitorLoaded", ex);
            }
        }
        
        //*************************************************************************
        //函数名称：RefreshMonitorDataTable
        //函数功能：更新监控DataTable
        //
        //输入参数：NONE
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2019.12.04
        //*************************************************************************
        public void RefreshMonitorDataTable()
        {
            try
            {
                if (GlobalParameterSet.dt_Monitor == null)
                {
                    GlobalParameterSet.dt_Monitor = new DataTable();
                    GlobalParameterSet.dt_Monitor.Columns.Add("IsMonitored", typeof(String));
                    GlobalParameterSet.dt_Monitor.Columns.Add("Name", typeof(String));
                    GlobalParameterSet.dt_Monitor.Columns.Add("Description", typeof(String));
                    GlobalParameterSet.dt_Monitor.Columns.Add("Index", typeof(String));
                    GlobalParameterSet.dt_Monitor.Columns.Add("Unit", typeof(String));
                    GlobalParameterSet.dt_Monitor.Columns.Add("DataType", typeof(String));
                }
                else
                {
                    GlobalParameterSet.dt_Monitor.Clear();
                }
               
                foreach (var item in ParameterMonitor)
                {
                    if (item.IsMonitored == true)
                    {
                        DataRow row = GlobalParameterSet.dt_Monitor.NewRow();
                        row["IsMonitored"] = true;
                        row["Name"] = item.Name;
                        row["Description"] = item.Description;
                        row["Index"] = item.Index;
                        row["Unit"] = item.Unit;
                        row["DataType"] = item.DataType;
                        GlobalParameterSet.dt_Monitor.Rows.Add(row);
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERMONITOR_REFRESH_MONITOR_DATATABLE, "RefreshMonitorDataTable", ex);
            }
        }
        #endregion
    }

    public class ParameterMonitorSet
    {
        public bool IsMonitored { get; set; }//是否需要数据监控
        public string Classification { get; set; }//索引类别
        public string Index { get; set; }//检索号码
        public string Name { get; set; }//名称
        public string Description { get; set; }//对应上位机接口名称
        public string DataType { get; set; }//数据类型
        public string Unit { get; set; }//单位
    }
}