﻿<UserControl x:Class="ServoStudio.Views.JogView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"  
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:converter="clr-namespace:Converter"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:JogViewModel}">

    <UserControl.Resources>
        <converter:BackgroundConverter x:Key="ColorConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>   
    </UserControl.Resources>

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding JogLoadedCommand}" EventName="Loaded"/>
        <dxmvvm:EventToCommand Command="{Binding JogUnloadedCommand}" EventName="Unloaded"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Separator_16x16.png}" Command="{Binding JogSwitchCommand}" Margin="0,0,6,0" Width="95">
                    <Label Content="{Binding JogSwitchHint}" Style="{StaticResource LabelStyle}" Margin="0" />
                </dx:SimpleButton>

                <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Redo_16x16.png}" Margin="0,0,6,0" Width="95" IsEnabled="{Binding IsJogButtonEnabled}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding JogRunCommand}" CommandParameter="2"/>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding JogStopCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                    <Label Content="电机正转" Style="{StaticResource LabelStyle}" Margin="0" />
                </dx:SimpleButton>

                <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Undo_16x16.png}" Margin="0,0,6,0" Width="95" IsEnabled="{Binding IsJogButtonEnabled}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding JogRunCommand}" CommandParameter="3"/>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding JogStopCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                    <Label Content="电机反转" Style="{StaticResource LabelStyle}" Margin="0" />
                </dx:SimpleButton>
            </StackPanel>

            <Border Grid.Row="1" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

            <Grid Grid.Row="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="50"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="180"/>
                    <ColumnDefinition Width="60"/>
                    <ColumnDefinition Width="10"/>
                </Grid.ColumnDefinitions>

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="3"  Grid.Column="1" Height="150" Width="Auto" Margin="0,8,0,2" HorizontalAlignment="Left" ShowMenu="False" Source="pack://application:,,,/ServoStudio;component/Resource/JOG.png" ShowBorder="False" Opacity="0.65"/>

                <Label Grid.Row="0" Grid.Column="3" Margin="10,9" Content="JOG速度" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="0" Grid.Column="4" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding JogSpeed,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="0" Grid.Column="5" Text="rpm" Style="{StaticResource TextBoxStyle_Unit}"/>

                <Label Grid.Row="1" Grid.Column="3" Margin="10,9" Content="JOG加速时间" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="1" Grid.Column="4" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding JogAccelerationTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="1" Grid.Column="5" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"/>

                <Label Grid.Row="2" Grid.Column="3" Margin="10,9" Content="JOG减速时间" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="2" Grid.Column="4" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding JogDecelerationTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="2" Grid.Column="5" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"/>
            </Grid>

            <Border Grid.Row="3" Margin="0" BorderThickness="0,1,0,0" BorderBrush="Orange"/>

            <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right">
                <Label Style="{StaticResource LabelStyle}" Content="提示：鼠标右键长按  [电机正转/电机反转]  按钮，电机转动&#x0a;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;按键松开，随即电机停止" Margin="10,9" Foreground="Red" HorizontalContentAlignment="Right"/>
            </StackPanel>

        </Grid>
    </ScrollViewer>
    
</UserControl>
