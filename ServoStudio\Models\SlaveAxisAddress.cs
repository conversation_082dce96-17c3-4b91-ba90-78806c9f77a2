﻿using DevExpress.Mvvm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ServoStudio.Models
{
    public class SlaveAxisAddress : ViewModelBase
    {
        private int id;//序号
        public int Id
        {
            get { return id; }
            set
            {
                id = value;
                RaisePropertyChanged();
                //if (this.PropertyChanged != null)
                //{
                //    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("AxisID"));
                //}
            }
        }

        private string slaveID;//从站ID
        public string SlaveID
        {
            get { return slaveID; }
            set
            {
                slaveID = value;
                RaisePropertyChanged();
                //if (this.PropertyChanged != null)
                //{
                //    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("SlaveID"));
                //}
            }
        }

        private string axisID;//轴ID
        public string AxisID
        {
            get { return axisID; }
            set
            {
                axisID = value;
                RaisePropertyChanged();
                //if (this.PropertyChanged != null)
                //{
                //    this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("AxisID"));
                //}
            }
        }

        //public event PropertyChangedEventHandler PropertyChanged;
    }
}
