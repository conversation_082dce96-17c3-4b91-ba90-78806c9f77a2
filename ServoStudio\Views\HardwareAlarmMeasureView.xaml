﻿<UserControl x:Class="ServoStudio.Views.HardwareAlarmMeasureView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:HardwareAlarmMeasureViewModel}"
             d:DesignHeight="800" d:DesignWidth="1400">
    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand EventName="Loaded" Command="{Binding HardwareAlarmMeasureLoadedCommand}"/>
    </dxmvvm:Interaction.Behaviors>

    <Grid>
        <dxg:GridControl Margin="5" SelectionMode="Row" ItemsSource="{Binding HardwareAlarmMeasure}" AutoGenerateColumns="AddNew">
            <dxg:GridControl.View>
                <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" SearchPanelHorizontalAlignment="Stretch">
                    <dxg:TableView.FormatConditions>
                        <dxg:FormatCondition Expression="[Level] = '低级'" FieldName="Level">
                            <dx:Format Background="Yellow"/>
                        </dxg:FormatCondition>
                        <dxg:FormatCondition Expression="[Level] = '中级'" FieldName="Level">
                            <dx:Format Background="Orange"/>
                        </dxg:FormatCondition>
                        <dxg:FormatCondition Expression="[Level] = '高级'" FieldName="Level">
                            <dx:Format Background="Red"/>
                        </dxg:FormatCondition>
                        <dxg:FormatCondition Expression="[Level] = '无'" FieldName="Level">
                            <dx:Format Background="Green"/>
                        </dxg:FormatCondition>
                    </dxg:TableView.FormatConditions>

                </dxg:TableView>
            </dxg:GridControl.View>

            <dxg:GridControl.DetailDescriptor>
                <dxg:TabViewDetailDescriptor>
                    <dxg:TabViewDetailDescriptor.DetailDescriptors>
                        <!--<dxg:ContentDetailDescriptor HeaderContent="可能原因">
                            <dxg:ContentDetailDescriptor.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Vertical">
                                        <TextBlock Text="{Binding Reason}" TextWrapping="Wrap" Margin="20,10" Foreground="Red"/>
                                    </StackPanel>
                                </DataTemplate>
                            </dxg:ContentDetailDescriptor.ContentTemplate>
                        </dxg:ContentDetailDescriptor>-->

                        <dxg:ContentDetailDescriptor HeaderContent="处理措施">
                            <dxg:ContentDetailDescriptor.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Vertical">
                                        <TextBlock Text="{Binding Measure}" TextWrapping="Wrap" Margin="20,10" Foreground="Green"/>
                                    </StackPanel>
                                </DataTemplate>
                            </dxg:ContentDetailDescriptor.ContentTemplate>
                        </dxg:ContentDetailDescriptor>
                    </dxg:TabViewDetailDescriptor.DetailDescriptors>
                </dxg:TabViewDetailDescriptor>
            </dxg:GridControl.DetailDescriptor>

            <dxg:GridColumn FieldName="Code" Header="报警编号" IsSmart="True" Width="80"/>
            <dxg:GridColumn FieldName="Content" Header="报警描述" IsSmart="True" Width="160"/>
            <dxg:GridColumn FieldName="Level" Header="报警等级" IsSmart="True" Width="80"/>
            <dxg:GridColumn FieldName="Reason" Header="报警原因" IsSmart="True" Width="*"/>
            <dxg:GridColumn FieldName="Measure" Visible="False" />
        </dxg:GridControl>
    </Grid>
</UserControl>
