﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Runtime.InteropServices;

namespace ServoStudio.GlobalMethod
{
    public static class IniHelper
    {
        [DllImport("kernel32")]
        private static extern long WritePrivateProfileString(string section, string key, string val, string filePath);
        [DllImport("kernel32")]
        private static extern int GetPrivateProfileString(string section, string key, string def, StringBuilder retVal, int size, string filePath);

        public static void IniWriteValue(string Section, string Key, string Value, string Path)
        {
            WritePrivateProfileString(Section, Key, Value, Path);
        }
        
        public static string IniReadValue(string Section, string Key, string Path)
        {
            StringBuilder temp = new StringBuilder(800);
            int i = GetPrivateProfileString(Section, Key, "", temp, 800, Path);
            return temp.ToString();
        }

        public static string IniReadValuePath(string Section, string Key, string Path)
        {
            StringBuilder temp = new StringBuilder(800000);
            int i = GetPrivateProfileString(Section, Key, "", temp, 800000, Path);
            return temp.ToString();
        }
    }
}
