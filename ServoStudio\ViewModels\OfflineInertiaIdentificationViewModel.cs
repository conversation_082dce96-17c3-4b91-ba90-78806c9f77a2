﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using ServoStudio.Models;
using System.Data;
using System.Windows.Threading;
using DevExpress.Mvvm.POCO;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class OfflineInertiaIdentificationViewModel
    {
        #region 字段
        public bool IsInitialized = true;
        public bool IsClosed = false;       
        private DispatcherTimer Timer_System = new DispatcherTimer();//系统时间    

        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 属性     
        public virtual string InertiaSwitchHint { get; set; }//离线惯量识别使能、禁能提示
        public virtual bool InertiaGetButtonEnabled { get; set; }//惯量比按钮属性
        public virtual bool InertiaModifyButtonEnabled { get; set; }//惯量比按钮属性
        public virtual string InertiaIdentificationAcceleration { get; set; }//离线惯量识别加速时间
        public virtual string InertiaIdentificationPosition { get; set; }//离线惯量识别运动距离
        public virtual string LoadInertiaRatio { get; set; }//负载惯量比
        public virtual string LoadInertiaRatioInitial { get; set; }//初始负载惯量比
        public virtual string PositionUnit { get; set; }//距离单位
        #endregion

        #region 构造函数
        public OfflineInertiaIdentificationViewModel()
        {
            ViewModelSet.OfflineInertiaIdentification = this;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：OfflineInertiaIdentificationLoaded
        //函数功能：OfflineInertiaIdentification界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.17
        //*************************************************************************
        public void OfflineInertiaIdentificationLoaded()
        {     
            //时钟初始化
            TimerInitialize();

            //标志位初始化
            RefreshOfflineInertiaIdentificationFlag(Status: "Initialized");

            //获取离线惯量识别参数
            ReadOfflineInertiaIdentificationParameter(Method: "Read");                       
        }

        //*************************************************************************
        //函数名称：OfflineInertialdentificationUnloaded
        //函数功能：界面离开时关闭时钟
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.17
        //*************************************************************************
        public void OfflineInertialdentificationUnloaded()
        {
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要写入的数据字典
            AddParameterInfoDictionary(ref dicParameterInfo);

            //获取参数详细信息
            OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.OFFLINEINERTIAINENTIFICATION, TaskName.OfflineInertiaIdentificationUnload, lstTransmittingDataInfo);
        }

        //*************************************************************************
        //函数名称：ReadOfflineInertiaIdentificationParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void ReadOfflineInertiaIdentificationParameter(string Method)
        {
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要读取的数据字典
            AddParameterInfoDictionary(ref dicParameterInfo, Method);

            //获取参数详细信息
            OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
              
            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.OFFLINEINERTIAINENTIFICATION, TaskName.OfflineInertiaIdentification, lstTransmittingDataInfo);                 
        }

        //*************************************************************************
        //函数名称：OfflineInertiaIdentificationSwitch
        //函数功能：离线惯量识别使能禁能
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.17
        //*************************************************************************
        public void OfflineInertiaIdentificationSwitch()
        {
            if (IsInitialized)
            {
                OthersHelper.OperatingControl("Operating Mode", "4101", TaskName.OfflineInertiaIdentificationOperatingMode, PageName.OFFLINEINERTIAINENTIFICATION);
            }
            else
            {
                OthersHelper.OperatingControl("Operating Setting", "4", TaskName.OfflineInertiaIdentificationSwitch, PageName.OFFLINEINERTIAINENTIFICATION);
            }
        }

        //*************************************************************************
        //函数名称：RefreshOfflineInertiaIdentificationFlag
        //函数功能：更新OfflineInertiaIdentification控制标志位
        //
        //输入参数：string Status    当前状态
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.17
        //*************************************************************************
        public void RefreshOfflineInertiaIdentificationFlag(string Status)
        {
            if (Status == "Initialized")//初始化
            {               
                OfflineInertiaIdentificationSet.IsContinuous = false;
                OfflineInertiaIdentificationSet.IsModified = false;

                InertiaGetButtonEnabled = false;
                InertiaModifyButtonEnabled = false;
                InertiaSwitchHint = "伺服使能";

                Timer_System.IsEnabled = false;
            }
            else if (Status == "Switched")//辨识开始关闭
            {
                string strActualEnable = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Actual Enable", "Index"));
                if (strActualEnable == "1")
                {
                    if (IsClosed)
                    {
                        OthersHelper.OperatingControl("Operating Setting", "4", TaskName.OfflineInertiaIdentificationSwitch, PageName.OFFLINEINERTIAINENTIFICATION);
                    }
                    else
                    {
                        OfflineInertiaIdentificationSet.IsContinuous = false;
                        OfflineInertiaIdentificationSet.IsModified = false;

                        InertiaGetButtonEnabled = true;
                        InertiaModifyButtonEnabled = false;
                        InertiaSwitchHint = "伺服禁能";

                        Timer_System.IsEnabled = false;

                        ShowNotification_CrossThread(2004);
                    }
                }
                else
                {
                    if (IsClosed)
                    {
                        OthersHelper.OperatingControl("Operating End", "1", TaskName.OfflineInertiaIdentificationOperatingEnd, PageName.OFFLINEINERTIAINENTIFICATION);
                    }
                    else
                    {
                        OfflineInertiaIdentificationSet.IsContinuous = false;
                        OfflineInertiaIdentificationSet.IsModified = false;

                        InertiaGetButtonEnabled = false;
                        InertiaModifyButtonEnabled = false;
                        InertiaSwitchHint = "伺服使能";

                        Timer_System.IsEnabled = false;

                        ShowNotification_CrossThread(2005);
                    }
                }
            }
            else if (Status == "Start")
            {
                OfflineInertiaIdentificationSet.IsContinuous = true;//持续发送
                OfflineInertiaIdentificationSet.IsModified = false;

                InertiaGetButtonEnabled = true;
                InertiaModifyButtonEnabled = true;
                InertiaSwitchHint = "伺服禁能";

                Timer_System.IsEnabled = true;
            }
            else if (Status == "Stop")
            {
                OfflineInertiaIdentificationSet.IsContinuous = false;//持续发送
                OfflineInertiaIdentificationSet.IsModified = false;

                InertiaGetButtonEnabled = true;
                InertiaModifyButtonEnabled = true;
                InertiaSwitchHint = "伺服禁能";

                Timer_System.IsEnabled = false;
            }
            else if (Status == "Modified")
            {
                OfflineInertiaIdentificationSet.IsContinuous = false;//持续发送
                OfflineInertiaIdentificationSet.IsModified = true;

                InertiaGetButtonEnabled = true;
                InertiaModifyButtonEnabled = false;
                InertiaSwitchHint = "伺服禁能";

                Timer_System.IsEnabled = false;

                ShowNotification_CrossThread(2007);
            }
            else if (Status == "Unloaded")
            {
                Timer_System.IsEnabled = false;
            }
        }

        //*************************************************************************
        //函数名称：EvalutionOfflineInertiaIdentificationParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void EvalutionOfflineInertiaIdentificationParameter()
        {
            PositionUnit = SelectUnit.Position;
            CurrentUnit.Position = SelectUnit.Position;
     
            if (IsInitialized)
            {
                InertiaIdentificationAcceleration = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Inertia Identification Acceleration", "Index"));//离线惯量识别加速时间
                InertiaIdentificationPosition = OthersHelper.ExchangeUnit("Inertia Identification Position", DefaultUnit.PositionUnit + "-" + CurrentUnit.Position, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Inertia Identification Position", "Index")));//离线惯量识别运动距离
                LoadInertiaRatioInitial = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//负载惯量比
            }
            else
            {            
                LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//负载惯量比
            }
        }

        //*************************************************************************
        //函数名称：OfflineInertiaIdentificationStart
        //函数功能：离线惯量识别开启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.17
        //*************************************************************************
        public void OfflineInertiaIdentificationStart()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要写入的数据字典
            AddParameterInfoDictionary(ref dicParameterInfo, Method: "Write");

            //获取参数详细信息
            OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);

            //判断输入参数是否正确
            iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
            if (iRet == RET.NO_EFFECT)
            {
                ShowNotification(2008);
                return;
            }
            else if (iRet == RET.ERROR)
            {
                return;
            }

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.OFFLINEINERTIAINENTIFICATION, TaskName.OfflineInertiaIdentificationContinuously, lstTransmittingDataInfo);

            //状态设置
            RefreshOfflineInertiaIdentificationFlag(Status: "Start");
        }

        //*************************************************************************
        //函数名称：OfflineInertiaIdentificationStop
        //函数功能：离线惯量识别结束
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.17
        //*************************************************************************
        public void OfflineInertiaIdentificationStop()
        {
            RefreshOfflineInertiaIdentificationFlag(Status: "Stop");
        }

        //*************************************************************************
        //函数名称：OfflineInertiaIdentificationModify
        //函数功能：替换负载惯量比
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.17
        //*************************************************************************
        public void OfflineInertiaIdentificationModify()
        {
            RefreshOfflineInertiaIdentificationFlag(Status: "Modified");
        }
        #endregion

        #region 私有方法   
        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.12.17
        //*************************************************************************
        private void AddParameterInfoDictionary(ref Dictionary<string, string> dicParameterInfo, string  Method)
        {
            dicParameterInfo = new Dictionary<string, string>();

            if (Method == "Listen")
            {
                dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);
            }
            else if (Method == "Read")
            {
                dicParameterInfo.Add("Inertia Identification Acceleration", null);
                dicParameterInfo.Add("Inertia Identification Position", null);
                dicParameterInfo.Add("Load Inertia Ratio", null);
            }
            else if (Method == "Write")
            {
                dicParameterInfo.Add("Inertia Identification Position", OthersHelper.ExchangeUnit("Inertia Identification Position", PositionUnit + "-" + DefaultUnit.PositionUnit, InertiaIdentificationPosition));
                dicParameterInfo.Add("Inertia Identification Acceleration", InertiaIdentificationAcceleration);               
            }
        }
        private void AddParameterInfoDictionary(ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            if (OfflineInertiaIdentificationSet.IsModified)
            {
                dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);
            }
            else
            {
                dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatioInitial);
            }
        }

        //*************************************************************************
        //函数名称：TimerInitialize
        //函数功能：时钟初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.08.07
        //*************************************************************************
        private void TimerInitialize()
        {
            Timer_System.Interval = TimeSpan.FromMilliseconds(TimerPeriod.OfflineInertiaIdentification);
            Timer_System.Tick += Timer_System_Tick;
        }

        //*************************************************************************
        //函数名称：Timer_System_Tick
        //函数功能：时钟函数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.30
        //*************************************************************************
        private void Timer_System_Tick(object sender, EventArgs e)
        {
            ReadOfflineInertiaIdentificationParameter(Method: "Listen");
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        private void ShowNotification_CrossThread(int In_iType)
        {
            WindowSet.clsMainWindow?.ShowNotification(In_iType);
        }
        #endregion
    }
}
