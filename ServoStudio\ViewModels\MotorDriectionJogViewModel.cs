﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using ServoStudio.Models;
using DevExpress.Mvvm.POCO;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class MotorDriectionJogViewModel
    {
        #region 字段
        public bool IsInitialized = true;
        public bool IsJogDriectionInitialized = true;
        private static bool IsEvaluationAll = true;
        public bool IsClosed = false;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 服务
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        //protected virtual IDialogService DialogService { get { return this.GetService<IDialogService>(); } }
        #endregion

        #region 属性
        public virtual string JogSpeed { get; set; }//Jog点动速度
        public virtual string JogAccelerationTime { get; set; }//Jog加速时间
        public virtual string JogDecelerationTime { get; set; }//Jog减速时间
        public virtual string JogTime { get; set; }//Jog运动时间
        public virtual string JogSwitchHint { get; set; }//Jog模块使能、禁能提示
        public virtual bool IsJogButtonEnabled { get; set; }//Jog按钮属性
        public virtual string Hint_RotateDirection { get; set; }//旋转方向设定

        //正反转
        public virtual bool IsCWChecked { get; set; }//以CW为正方向
        public virtual bool IsCCWChecked { get; set; }//以CCW为正方向

        //运动模式
        public virtual bool IsJogIntermittentModeChecked { get; set; }//Jog点动模式
        public virtual bool IsJogContinuousModeChecked { get; set; }//Jog连续模式

        //抱闸
        public virtual bool IsJogModeChecked { get; set; }//Jog模式
        public virtual bool IsBrakeChecked { get; set; }//抱闸开关      
        public virtual string BrakeReleaseDelayTime { get; set; }//抱闸释放延时时间
        public virtual string BrakeActiveDelayTime { get; set; }//抱闸制动延时时间
        public virtual string BrakeActiveVelocity { get; set; }//抱闸制动速度门限
        public virtual string BrakeActiveAllowedDelayTime { get; set; }//抱闸制动允许延时时间

        //提示
        public virtual string Hint_JogModeEnable { get; set; }//Jog模式使能开关
        //public virtual string Hint_BrakeEnable { get; set; }//Jog模式使能开关
        #endregion

        #region 构造函数
        public MotorDriectionJogViewModel()
        {
            ViewModelSet.JogDriection = this;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：JogLoaded
        //函数功能：Jog界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void JogDriectionLoaded()
        {
            int iRet = -1;

            //获取JOG参数
            ReadJogParameter();

            //Jog方向参数初始化
            RefreshJogDriectionFlag(Status: "JogInitialize");

            //赋值
            if (IsInitialized == true)
            {
                //标志位更新
                IsInitialized = false;
                IsEvaluationAll = true;

                //赋值
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet == RET.SUCCEEDED)
                {
                    //读取电机正反转参数
                    ReadMotorDriectionParameter("All");
                }
                else
                {
                    GetDefaultNormalSettingParameter("All");
                }
            }
            else
            {
                //标志位更新
                IsEvaluationAll = true;

                //赋值
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet == RET.SUCCEEDED)
                {
                    //读取电机正反转参数
                    ReadMotorDriectionParameter("All");
                }
                else
                {
                    InterfaceEvaluationFromGlobalVariable();
                }
            }    
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.02
        //*************************************************************************
        public void OnIsJogModeCheckedChanged()//Jog模式功能
        {
            GlobalCurrentInput.IsJogModeChecked = IsJogModeChecked;

            if (IsJogModeChecked)
            {
                Hint_JogModeEnable = "Jog持续运动模式";
            }
            else
            {
                Hint_JogModeEnable = "Jog点动模式";
            }
        }

        public void OnIsCWCheckedChanged()//CW方向为正
        {
            GlobalCurrentInput.IsCWChecked = IsCWChecked;


            if (IsCWChecked)
            {
                IsCCWChecked = false;
                Hint_RotateDirection = "当前转动方向：以CW方向为正转方向";
                int iRet = -1;

                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING)
                {
                    //判断是否写入EEPROM
                    if (MessageBoxService.ShowMessage("这将会使伺服禁能，并写入参数...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                    {
                        //先禁能，再写电机旋转方向
                        OthersHelper.OperatingControl("Fn Servo Off", "1", "伺服禁能", "");

                        ViewModelSet.JogDriection?.RefreshJogDriectionFlag("JogStopForFN");

                        //写入电机旋转方向参数
                        WriteMotorDriectionParameter("1");

                        //IsCCWChecked = false;
                        //Hint_RotateDirection = "当前转动方向：以CW方向为正转方向";
                    }
                    //else
                    //{
                    //    IsCCWChecked = true;
                    //    IsCWChecked = false;
                    //    Hint_RotateDirection = "当前转动方向：以CCW方向为正转方向";
                    //}

                }
                else
                {
                    //写入电机旋转方向参数
                    WriteMotorDriectionParameter("1");
                }

            }
            else
            {
                IsCCWChecked = true;
                Hint_RotateDirection = "当前转动方向：以CCW方向为正转方向";
            }
        }
        public void OnIsCCWCheckedChanged()//CCW方向为正  
        {
            GlobalCurrentInput.IsCCWChecked = IsCCWChecked;

            if (IsCCWChecked)
            {
                IsCWChecked = false;
                Hint_RotateDirection = "当前转动方向：以CCW方向为正转方向";
                int iRet = -1;

                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING)
                {
                    //判断是否写入EEPROM
                    if (MessageBoxService.ShowMessage("这将会使伺服禁能，并写入参数...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                    {
                        //先禁能，再写电机旋转方向
                        OthersHelper.OperatingControl("Fn Servo Off", "1", "伺服禁能", "");

                        ViewModelSet.JogDriection?.RefreshJogDriectionFlag("JogStopForFN");

                        //写入电机旋转方向参数
                        WriteMotorDriectionParameter("0");

                        //IsCWChecked = false;
                        //Hint_RotateDirection = "当前转动方向：以CCW方向为正转方向";
                    }
                    //else
                    //{
                    //    IsCWChecked = true;
                    //    IsCCWChecked = false;
                    //    Hint_RotateDirection = "当前转动方向：以CW方向为正转方向";
                    //}

                }
                else
                {
                    //写入电机旋转方向参数
                    WriteMotorDriectionParameter("0");
                }
                           
            }
            else
            {
                IsCWChecked = true;
                Hint_RotateDirection = "当前转动方向：以CW方向为正转方向";
            }
        }

        public void OnIsJogIntermittentModeCheckedChanged()//Jog点动模式
        {
            GlobalCurrentInput.IsJogIntermittentModeChecked = IsJogIntermittentModeChecked;

            if (IsJogIntermittentModeChecked)
            {
                IsJogContinuousModeChecked = false;
                IsJogButtonEnabled = true ;
                //Hint_RotateDirection = "当前转动方向：以CW方向为正转方向";

                //写入电机旋转方向参数
                //WriteMotorDriectionParameter("1");

                //写入Jog模式为点动模式
                OthersHelper.OperatingControl("Motor Jog Command", "16", TaskName.JogDriectionOperatingMode, PageName.MOTORDRIECTIONJOGPAGE);
            }
            else
            {
                IsJogContinuousModeChecked = true;
                //Hint_RotateDirection = "当前转动方向：以CCW方向为正转方向";
            }
        }
        public void OnIsJogContinuousModeCheckedChanged()//Jog连续运动模式  
        {
            GlobalCurrentInput.IsJogContinuousModeChecked = IsJogContinuousModeChecked;

            if (IsJogContinuousModeChecked)
            {
                IsJogIntermittentModeChecked = false;
                IsJogButtonEnabled = true;
                //Hint_RotateDirection = "当前转动方向：以CCW方向为正转方向";

                //写入电机旋转方向参数
                //WriteMotorDriectionParameter("1");
            }
            else
            {
                IsJogIntermittentModeChecked = true;
                //Hint_RotateDirection = "当前转动方向：以CW方向为正转方向";
            }
        }

        //*************************************************************************
        //函数名称：JogUnloaded
        //函数功能：Jog界面退出
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.08
        //*************************************************************************
        public void JogDriectionUnloaded()
        {
            //判断内部是否使能，若使能先禁能，再退出
            OthersHelper.OperatingControl("Fn Servo Off", "1", "伺服Jog禁能", "");
            IsClosed = true;          
        }

        //*************************************************************************
        //函数名称：WriteMotorDriectionParameter
        //函数功能：写电机旋转方向参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.03
        //*************************************************************************
        public void WriteMotorDriectionParameter(string strCategory)
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary_For_MotorDrietionJog(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                //if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                //{
                //    bEEPROM = true;
                //}

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MOTORDRIECTIONJOGPAGE, TaskName.JogDriection, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.WRITE_MOTORDRIECTION_PARAMETER, "WriteMotorDriectionParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultNormalSettingParameter
        //函数功能：获取限幅保护的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void GetDefaultNormalSettingParameter(string strCategory)
        {
            string strRotateDirection = null;
            string strBrake = null;

            try
            {
                if (strCategory == "0")
                {
                    BrakeReleaseDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Release Delay Time", "Default");
                    BrakeActiveDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Delay Time", "Default");
                    BrakeActiveVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Velocity", "Default");
                    BrakeActiveAllowedDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Allowed Delay Time", "Default");

                    strBrake = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Enable", "Default");
                    if (strBrake == "0")
                    {
                        IsBrakeChecked = false;
                        //Hint_BrakeEnable = "抱闸制动功能关";
                    }
                    else if (strBrake == "1")
                    {
                        IsBrakeChecked = true;
                        //Hint_BrakeEnable = "抱闸制动功能开";
                    }
                }
                else if (strCategory == "1")
                {
                    strRotateDirection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Rotate Direction", "Default");
                    if (strRotateDirection == "0")
                    {
                        IsCCWChecked = true;
                        IsCWChecked = false;
                    }
                    else if (strRotateDirection == "1")
                    {
                        IsCWChecked = true;
                        IsCCWChecked = false;
                    }
                }
                else
                {
                    BrakeReleaseDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Release Delay Time", "Default");
                    BrakeActiveDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Delay Time", "Default");
                    BrakeActiveVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Velocity", "Default");
                    BrakeActiveAllowedDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Allowed Delay Time", "Default");

                    strBrake = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Enable", "Default");
                    if (strBrake == "0")
                    {
                        IsBrakeChecked = false;
                        //Hint_BrakeEnable = "抱闸制动功能关";
                    }
                    else if (strBrake == "1")
                    {
                        IsBrakeChecked = true;
                        //Hint_BrakeEnable = "抱闸制动功能开";
                        Hint_JogModeEnable = "Jog点动模式";
                    }

                    strRotateDirection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Rotate Direction", "Default");
                    if (strRotateDirection == "0")
                    {
                        IsCCWChecked = true;
                        IsCWChecked = false;
                    }
                    else if (strRotateDirection == "1")
                    {
                        IsCWChecked = true;
                        IsCCWChecked = false;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_GET_DEFAULT_PARAMETER, "GetDefaultNormalSettingParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadMotorDriectionParameter
        //函数功能：读电机旋转方向参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.02
        //*************************************************************************
        public void ReadMotorDriectionParameter(string strCategory)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary_For_MotorDrietionJog(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MOTORDRIECTIONJOGPAGE, TaskName.JogDriection, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.READMOTORDRIECTION_PARAMETER, "ReadMotorDriectionParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.02
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                //正反转
                IsCWChecked = GlobalCurrentInput.IsCWChecked;
                IsCCWChecked = GlobalCurrentInput.IsCCWChecked;

                //Jog运动模式
                IsJogIntermittentModeChecked = GlobalCurrentInput.IsJogIntermittentModeChecked;
                IsJogContinuousModeChecked = GlobalCurrentInput.IsJogContinuousModeChecked;

                //抱闸
                IsBrakeChecked = GlobalCurrentInput.IsBrakeChecked;
                //if (IsBrakeChecked)
                //{
                //    Hint_BrakeEnable = "抱闸制动功能开";
                //}
                //else
                //{
                //    Hint_BrakeEnable = "抱闸制动功能关";
                //}

                //Jog模式
                IsJogModeChecked = GlobalCurrentInput.IsJogModeChecked;
                if (IsJogModeChecked)
                {
                    Hint_JogModeEnable = "Jog持续运动模式";
                }
                else
                {
                    Hint_JogModeEnable = "Jog点动模式";
                }

                BrakeReleaseDelayTime = GlobalCurrentInput.BrakeReleaseDelayTime;//抱闸释放延时时间
                BrakeActiveDelayTime = GlobalCurrentInput.BrakeActiveDelayTime;//抱闸制动延时时间
                BrakeActiveVelocity = GlobalCurrentInput.BrakeActiveVelocity;//抱闸制动速度门限
                BrakeActiveAllowedDelayTime = GlobalCurrentInput.BrakeActiveAllowedDelayTime;//抱闸制动允许延时时间
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary_For_MotorDrietionJog
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.02
        //*************************************************************************
        private int AddParameterInfoDictionary_For_MotorDrietionJog(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                if (strCategory == "0")
                {
                    if (IsCCWChecked && !IsCWChecked)
                    {
                        dicParameterInfo.Add("Rotate Direction", "0");
                    }
                    else if (!IsCCWChecked && IsCWChecked)
                    {
                        dicParameterInfo.Add("Rotate Direction", "1");
                    }
                    else
                    {
                        dicParameterInfo.Add("Rotate Direction", "0");
                    }
                    //if (IsBrakeChecked)
                    //{
                    //    dicParameterInfo.Add("Brake Enable", "1");
                    //}
                    //else
                    //{
                    //    dicParameterInfo.Add("Brake Enable", "0");
                    //}

                    dicParameterInfo.Add("Jog Time", JogTime);
                    dicParameterInfo.Add("Jog Acceleration Time", JogAccelerationTime);
                    dicParameterInfo.Add("Jog Deceleration Time", JogDecelerationTime);
                    dicParameterInfo.Add("Jog Speed", JogSpeed);

                    //dicParameterInfo.Add("Brake Release Delay Time", BrakeReleaseDelayTime);
                    //dicParameterInfo.Add("Brake Active Delay Time", BrakeActiveDelayTime);
                    //dicParameterInfo.Add("Brake Active Velocity", BrakeActiveVelocity);
                    //dicParameterInfo.Add("Brake Active Allowed Delay Time", BrakeActiveAllowedDelayTime);
                }
                else if (strCategory == "1")
                {
                    if (IsCCWChecked && !IsCWChecked)
                    {
                        dicParameterInfo.Add("Rotate Direction", "0");
                    }
                    else if (!IsCCWChecked && IsCWChecked)
                    {
                        dicParameterInfo.Add("Rotate Direction", "1");
                    }
                    else
                    {
                        dicParameterInfo.Add("Rotate Direction", "0");
                    }
                }
                else
                {
                    //抱闸
                    //if (IsBrakeChecked)
                    //{
                    //    dicParameterInfo.Add("Brake Enable", "1");
                    //}
                    //else
                    //{
                    //    dicParameterInfo.Add("Brake Enable", "0");
                    //}

                    dicParameterInfo.Add("Jog Time", JogTime);
                    dicParameterInfo.Add("Jog Acceleration Time", JogAccelerationTime);
                    dicParameterInfo.Add("Jog Deceleration Time", JogDecelerationTime);
                    dicParameterInfo.Add("Jog Speed", JogSpeed);

                    //dicParameterInfo.Add("Brake Release Delay Time", BrakeReleaseDelayTime);
                    //dicParameterInfo.Add("Brake Active Delay Time", BrakeActiveDelayTime);
                    //dicParameterInfo.Add("Brake Active Velocity", BrakeActiveVelocity);
                    //dicParameterInfo.Add("Brake Active Allowed Delay Time", BrakeActiveAllowedDelayTime);

                    //正反转
                    if (IsCCWChecked && !IsCWChecked)
                    {
                        dicParameterInfo.Add("Rotate Direction", "0");
                    }
                    else if (!IsCCWChecked && IsCWChecked)
                    {
                        dicParameterInfo.Add("Rotate Direction", "1");
                    }
                    else
                    {
                        dicParameterInfo.Add("Rotate Direction", "0");
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary_For_MotorDrietionJog", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ReadJogParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void ReadJogParameter()
        {
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要读取的数据字典
            AddParameterInfoDictionary(null, ref dicParameterInfo);

            //获取参数详细信息
            OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MOTORDRIECTIONJOGPAGE, TaskName.JogDriection, lstTransmittingDataInfo);      
        }

        //*************************************************************************
        //函数名称：MotorParameterIdentification_AddInterface
        //函数功能：参数辨识界面导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.17
        //*************************************************************************
        public void MotorParameterIdentification_AddInterface()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.MOTORLINEUVWSEQUENCEABSENCODEROFFSET;
                NavigationService.Navigate("MotorLineUVWSequenceAbsEncoderOffsetView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：InertiaIdentificationParameterSelf_Tunning_For_AddInterface
        //函数功能：负载惯量辨识和参数自整定
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void InertiaIdentificationParameterSelf_Tunning_For_AddInterface()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.INERTIAINENTIFICATIONPARAMETERSELFTUNNING;
                NavigationService.Navigate("InertiaIdentificationParameterSelfTunningView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationJogDriectionParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2022.11.17
        //*************************************************************************
        public void EvaluationJogDriectionParameter()
        {
            string strRotateDirection = null;

            JogSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Speed", "Index"));
            JogAccelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Acceleration Time", "Index"));
            JogDecelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Deceleration Time", "Index"));
            JogTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Time", "Index"));

            //正反转
            strRotateDirection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Rotate Direction", "Index"));//旋转方向设定
            if (strRotateDirection == "0")
            {
                IsCCWChecked = true;
                IsCWChecked = false;
            }
            else if (strRotateDirection == "1")
            {
                IsCWChecked = true;
                IsCCWChecked = false;
            }
        }

        //*************************************************************************
        //函数名称：RefreshJogFlag
        //函数功能：更新Jog控制标志位
        //
        //输入参数：string Status    当前Jog状态
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.17
        //*************************************************************************
        public void RefreshJogDriectionFlag(string Status)
        {
            string strMotorJogCommand = null;

            if (Status == "JogInitialize") 
            {
                JogDirectionSet.Direction = null;
                JogDirectionSet.IsContinuous = false;

                JogSwitchHint = "Jog使能";
                IsJogButtonEnabled = false;
            }
            else if (Status == "JogSwitch")
            {
                strMotorJogCommand = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Motor Jog Command", "Index"));
                if (strMotorJogCommand == "16")
                {               
                    if (IsClosed)
                    {
                        OthersHelper.OperatingControl("Motor Jog Command", "16", TaskName.JogDriectionSwitch, PageName.MOTORDRIECTIONJOGPAGE);
                    }
                    else
                    {
                        JogSwitchHint = "Jog禁能";
                        IsJogButtonEnabled = true;
                        //IsJogDriectionInitialized = true;

                        //ShowNotification_CrossThread(2004);
                    }
                }
                else
                {                 
                    if (IsClosed)
                    {
                        OthersHelper.OperatingControl("Motor Jog Command", "16", TaskName.JogDriectionOperatingEnd, PageName.MOTORDRIECTIONJOGPAGE);
                    }
                    else
                    {
                        JogSwitchHint = "Jog使能";
                        IsJogButtonEnabled = false;
                        IsJogDriectionInitialized = true;

                        //ShowNotification_CrossThread(2005);
                    }
                }
            }
            else if (Status == "JogStopForFN")
            {
                //JogSwitchHint = "Jog使能";
                JogDirectionSet.Direction = null;
                JogDirectionSet.IsContinuous = false;
                //OthersHelper.OperatingControl("Motor Jog Command", "0", TaskName.JogDriectionOperatingEnd, PageName.MOTORDRIECTIONJOGPAGE);

                if (IsClosed)
                {
                    //OthersHelper.OperatingControl("Motor Jog Command", "0", TaskName.JogDriectionOperatingEnd, PageName.MOTORDRIECTIONJOGPAGE);
                }
                else
                {
                    JogSwitchHint = "Jog使能";
                    IsJogButtonEnabled = false;
                    IsJogDriectionInitialized = true;

                    //ShowNotification_CrossThread(2005);
                }
                //if (IsJogIntermittentModeChecked)
                //{
                //    JogDirectionSet.IsContinuous = false;
                //    OthersHelper.OperatingControl("Motor Jog Command", "0", TaskName.JogDriectionOperatingEnd, PageName.MOTORDRIECTIONJOGPAGE);
                //}
                //else
                //{
                //    JogDirectionSet.IsContinuous = true;
                //}
            }
            else if (Status == "JogStop")
            {
                //JogSwitchHint = "Jog使能";
                JogDirectionSet.Direction = null;
                JogDirectionSet.IsContinuous = false;
                OthersHelper.OperatingControl("Motor Jog Command", "16", TaskName.JogDriectionOperatingEnd, PageName.MOTORDRIECTIONJOGPAGE);

                if (IsClosed)
                {
                    OthersHelper.OperatingControl("Motor Jog Command", "16", TaskName.JogDriectionOperatingEnd, PageName.MOTORDRIECTIONJOGPAGE);
                }
                else
                {
                    //JogSwitchHint = "Jog使能";
                    //IsJogButtonEnabled = false;
                    //IsJogDriectionInitialized = true;

                    //ShowNotification_CrossThread(2005);
                }
                //if (IsJogIntermittentModeChecked)
                //{
                //    JogDirectionSet.IsContinuous = false;
                //    OthersHelper.OperatingControl("Motor Jog Command", "0", TaskName.JogDriectionOperatingEnd, PageName.MOTORDRIECTIONJOGPAGE);
                //}
                //else
                //{
                //    JogDirectionSet.IsContinuous = true;
                //}
            }
            else if (Status == "CorotationRun")
            {
                JogDirectionSet.Direction = null;
                //if (IsJogIntermittentModeChecked)
                //{
                //    JogDirectionSet.IsContinuous = false;
                //    OthersHelper.OperatingControl("Motor Jog Command", "17", TaskName.JogRunDriection, PageName.MOTORDRIECTIONJOGPAGE);
                //}
                //else
                //{
                //    JogDirectionSet.IsContinuous = true;
                //}
                JogDirectionSet.IsContinuous = false;
                //OthersHelper.OperatingControl("Motor Jog Command", "17", TaskName.JogRunDriection, PageName.MOTORDRIECTIONJOGPAGE);
            }
            else if (Status == "ReversalRun")
            {
                JogDirectionSet.Direction = null;
                JogDirectionSet.IsContinuous = false;
                //OthersHelper.OperatingControl("Motor Jog Command", "18", TaskName.JogRunDriection, PageName.MOTORDRIECTIONJOGPAGE);
            }
            else//JogRun
            {
                //JogDirectionSet.Direction = Status;
                //JogDirectionSet.IsContinuous = true;

                if (IsJogIntermittentModeChecked)
                {
                    JogDirectionSet.IsContinuous = false;
                    OthersHelper.OperatingControl("Motor Jog Command", "17", TaskName.JogRunDriection, PageName.MOTORDRIECTIONJOGPAGE);
                    //OthersHelper.OperatingControl("Motor Jog Command", "0", TaskName.JogDriectionOperatingEnd, PageName.MOTORDRIECTIONJOGPAGE);
                }
                else
                {
                    JogDirectionSet.IsContinuous = true;
                }
            }           
        }

        //*************************************************************************
        //函数名称：JogModelSwitch
        //函数功能：JOG模块使能禁能
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.17
        //*************************************************************************
        public void JogDriectionSwitch()
        {
            if (IsJogDriectionInitialized)
            {
                if (OthersHelper.GetCurrentValueOfIndex("0x603F00") != "0")
                {
                    ViewModelSet.Main?.ShowHintInfo("伺服故障，请先清除故障...");
                }
                else
                {
                    OthersHelper.OperatingControl("Motor Jog Command", "16", TaskName.JogDriectionOperatingMode, PageName.MOTORDRIECTIONJOGPAGE);
                }
            }
            else
            {
                if (OthersHelper.GetCurrentValueOfIndex("0x603F00") != "0")
                {
                    ViewModelSet.Main?.ShowHintInfo("伺服故障，请先清除故障...");
                }
                else
                {
                    OthersHelper.OperatingControl("Motor Jog Command", "0", TaskName.JogDriectionSwitch, PageName.MOTORDRIECTIONJOGPAGE);
                }
            }
        }

        //*************************************************************************
        //函数名称：JogRun
        //函数功能：JOG开启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.25
        //*************************************************************************
        public void JogRun(string strIndex)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要写入的数据字典
            AddParameterInfoDictionary(strIndex, ref dicParameterInfo);

            //获取参数详细信息
            OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);

            //判断输入参数是否正确
            iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
            if (iRet == RET.NO_EFFECT)
            {
                ShowNotification(2008);
                return;
            }
            else if (iRet == RET.ERROR)
            {
                return;
            }

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MOTORDRIECTIONJOGPAGE, TaskName.JogContinuously, lstTransmittingDataInfo);

            //JogSet设置
            RefreshJogDriectionFlag(strIndex);
        }

        //*************************************************************************
        //函数名称：JogCorotationRun
        //函数功能：JOG正转开启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.17
        //*************************************************************************
        public void JogCorotationRun(string strIndex)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要写入的数据字典
            AddParameterInfoDictionary_For_CorotationRun(strIndex, ref dicParameterInfo);

            //获取参数详细信息
            OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);

            //判断输入参数是否正确
            iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
            if (iRet == RET.NO_EFFECT)
            {
                ShowNotification(2008);
                return;
            }
            else if (iRet == RET.ERROR)
            {
                return;
            }

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MOTORDRIECTIONJOGPAGE, TaskName.JogDriectionContinuously, lstTransmittingDataInfo);

            //JogSet设置
            RefreshJogDriectionFlag(Status: "CorotationRun");
        }

        //*************************************************************************
        //函数名称：JogReversalRun
        //函数功能：JOG反转开启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.17
        //*************************************************************************
        public void JogReversalRun(string strIndex)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要写入的数据字典
            AddParameterInfoDictionary_For_ReversalRun(strIndex, ref dicParameterInfo);

            //获取参数详细信息
            OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);

            //判断输入参数是否正确
            iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
            if (iRet == RET.NO_EFFECT)
            {
                ShowNotification(2008);
                return;
            }
            else if (iRet == RET.ERROR)
            {
                return;
            }

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MOTORDRIECTIONJOGPAGE, TaskName.JogDriectionContinuously, lstTransmittingDataInfo);

            //JogSet设置
            RefreshJogDriectionFlag(Status: "ReversalRun");
        }

        //*************************************************************************
        //函数名称：JogStop
        //函数功能：JOG关闭
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.18
        //*************************************************************************
        public void JogStop()
        {
            RefreshJogDriectionFlag(Status: "JogStop");
        }

        //*************************************************************************
        //函数名称：JogDriectionStop
        //函数功能：JOG关闭
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.17
        //*************************************************************************
        public void JogDriectionStop()
        {
            RefreshJogDriectionFlag(Status: "JogStop");
        }
        #endregion

        #region 私有方法 
        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.12&2022.11.17
        //*************************************************************************
        private void AddParameterInfoDictionary(string strDirection, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            //dicParameterInfo.Add("Jog Speed", JogSpeed);
            //dicParameterInfo.Add("Jog Acceleration Time", JogAccelerationTime);
            //dicParameterInfo.Add("Jog Deceleration Time", JogDecelerationTime);
            dicParameterInfo.Add("Jog Time", JogTime);
            dicParameterInfo.Add("Jog Acceleration Time", JogAccelerationTime);
            dicParameterInfo.Add("Jog Deceleration Time", JogDecelerationTime);
            dicParameterInfo.Add("Jog Speed", JogSpeed);

            dicParameterInfo.Add("Motor Jog Command", "17");
            //dicParameterInfo.Add("Operating Setting", strDirection);
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary_For_CorotationRun
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.12&2022.11.17
        //*************************************************************************
        private void AddParameterInfoDictionary_For_CorotationRun(string strDirection, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            //dicParameterInfo.Add("Jog Speed", JogSpeed);
            //dicParameterInfo.Add("Jog Acceleration Time", JogAccelerationTime);
            //dicParameterInfo.Add("Jog Deceleration Time", JogDecelerationTime);
            dicParameterInfo.Add("Jog Time", JogTime);
            dicParameterInfo.Add("Jog Acceleration Time", JogAccelerationTime);
            dicParameterInfo.Add("Jog Deceleration Time", JogDecelerationTime);
            dicParameterInfo.Add("Jog Speed", JogSpeed);

            dicParameterInfo.Add("Motor Jog Command", "17");
            //dicParameterInfo.Add("Operating Setting", strDirection);
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary_For_ReversalRun
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.12&2022.11.17
        //*************************************************************************
        private void AddParameterInfoDictionary_For_ReversalRun(string strDirection, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            //dicParameterInfo.Add("Jog Speed", JogSpeed);
            //dicParameterInfo.Add("Jog Acceleration Time", JogAccelerationTime);
            //dicParameterInfo.Add("Jog Deceleration Time", JogDecelerationTime);
            dicParameterInfo.Add("Jog Time", JogTime);
            dicParameterInfo.Add("Jog Acceleration Time", JogAccelerationTime);
            dicParameterInfo.Add("Jog Deceleration Time", JogDecelerationTime);
            dicParameterInfo.Add("Jog Speed", JogSpeed);

            dicParameterInfo.Add("Motor Jog Command", "18");
            //dicParameterInfo.Add("Operating Setting", strDirection);
        }


        ////*************************************************************************
        ////函数名称：AddParameterInfoDictionary
        ////函数功能：添加要读写的字段
        ////
        ////输入参数：None
        ////         
        ////输出参数：None
        ////        
        ////编码作者：Ryan&Lilbert
        ////更新时间：2020.03.12&2022.11.17
        ////*************************************************************************
        //private void AddParameterInfoDictionary(string strDirection, ref Dictionary<string, string> dicParameterInfo)
        //{
        //    dicParameterInfo = new Dictionary<string, string>();

        //    //dicParameterInfo.Add("Jog Speed", JogSpeed);
        //    //dicParameterInfo.Add("Jog Acceleration Time", JogAccelerationTime);
        //    //dicParameterInfo.Add("Jog Deceleration Time", JogDecelerationTime);
        //    dicParameterInfo.Add("Jog Time", JogTime);
        //    dicParameterInfo.Add("Jog Acceleration Time", JogAccelerationTime);
        //    dicParameterInfo.Add("Jog Deceleration Time", JogDecelerationTime);
        //    dicParameterInfo.Add("Jog Speed", JogSpeed);
        //    //dicParameterInfo.Add("Operating Setting", strDirection);
        //}

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        private void ShowNotification_CrossThread(int In_iType)
        {
            WindowSet.clsMainWindow?.ShowNotification(In_iType);
        }
        #endregion
    }
}