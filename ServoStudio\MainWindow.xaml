﻿<dxr:DXRibbonWindow  x:Class="ServoStudio.MainWindow"
         
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
        xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
        xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
        xmlns:dxrt="http://schemas.devexpress.com/winfx/2008/xaml/ribbon/themekeys"
        xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm"
        xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
        xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
        xmlns:dxnt="http://schemas.devexpress.com/winfx/2008/xaml/navbar/themekeys"
        xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
        xmlns:dxn="http://schemas.devexpress.com/winfx/2008/xaml/navbar"
        xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol" 
        xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
        xmlns:Views="clr-namespace:ServoStudio.Views"
        xmlns:converter="clr-namespace:Converter"
        xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
        DataContext="{dxmvvm:ViewModelSource Type=ViewModels:MainWindowViewModel}"
        Title="ServoStudio" Name="ServoStudioWindows" Height="850" Width="1350" 
        Icon="pack://application:,,,/ServoStudio;component/Resource/ServoStudioICO.ico" 
        WindowStartupLocation="CenterScreen" Closing="DXRibbonWindow_Closing" Loaded="DXRibbonWindow_Loaded" ResizeMode="CanResizeWithGrip" SizeChanged="ServoStudioWindows_SizeChanged" LocationChanged="ServoStudioWindows_LocationChanged">

    <dxr:DXRibbonWindow.Resources>
        <converter:BackgroundConverter x:Key="BackgroudConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>
        <converter:ContentConverter x:Key="ContentConverter"/>
        <converter:AlarmLevelConverter x:Key="AlarmLevelConverter"/>
       
        <Style x:Key="ItemVisibleStyle" TargetType="{x:Type dxn:NavBarItemControl}">
            <Setter Property="Margin" Value="0,5,0,20"/>
            <Setter Property="FontSize" Value="11.5"/>
        </Style>

        <Style x:Key="GroupStyle" TargetType="{x:Type dxn:NavBarGroup}">
            <Setter Property="FontSettings">
                <Setter.Value>
                    <dxn:FontSettings FontSize="12.5" FontWeight="Normal"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ItemStyle" TargetType="{x:Type dxn:NavBarItem}">
            <Setter Property="ImageSettings">
                <Setter.Value>
                    <dxn:ImageSettings Width="27" Height="27" Stretch="Uniform" StretchDirection="Both"/>
                </Setter.Value>
            </Setter>
            
            <Setter Property="LayoutSettings">
                <Setter.Value>
                    <dxn:LayoutSettings ImageDocking="Top" ImageHorizontalAlignment="Center" TextHorizontalAlignment="Center" ImageVerticalAlignment="Center" TextVerticalAlignment="Center" />
                </Setter.Value>
            </Setter>
        </Style>
    </dxr:DXRibbonWindow.Resources>

    <dxmvvm:Interaction.Behaviors>
        
        <dxwui:WinUIDialogService x:Name="SwitchAxis" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:SwitchAxisView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService x:Name="MotorParameterIdentification" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:MotorParameterIdentificationView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService x:Name="UnitSet" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:UnitView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService x:Name="ModifyPassword" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:ModifyPasswordView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService x:Name="Jog" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:JogView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService x:Name="ProgramJog" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:ProgramJogView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxwui:WinUIDialogService x:Name="OfflineInertiaIdentification" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:OfflineInertiaIdentificationView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <!--2023.05.18添加参数参数差异对比界面-->
        <dxwui:WinUIDialogService x:Name="ParameterImport" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:ParameterImportView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>

        <dxmvvm:CurrentWindowService/>
        <dxwui:WinUIMessageBoxService x:Name="WinUIMessageBox"/>
        <dxmvvm:EventToCommand Command="{Binding MainWindowLoadedCommand}" EventName="Loaded"/>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields" CustomNotificationPosition="BottomRight"/>
    </dxmvvm:Interaction.Behaviors>

    <dxb:BarManager Name="barManager">

        <!--窗体风格-->
        <dxb:BarManager.Items>
            <dxr:RibbonGalleryBarItem x:Name="ribbonGalleryBarItem">
                <dxmvvm:Interaction.Behaviors>
                    <dxr:RibbonGalleryItemThemeSelectorBehavior/>
                </dxmvvm:Interaction.Behaviors>
                <dxr:RibbonGalleryBarItem.Gallery>
                    <dxb:Gallery ItemGlyphSize="26,26" HoverGlyphSize="32,32"/>
                </dxr:RibbonGalleryBarItem.Gallery>
            </dxr:RibbonGalleryBarItem>
        </dxb:BarManager.Items>

        <!--内容-->
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <dxr:RibbonControl Grid.Row="0" IsEnabled="{Binding IsAllPageEnabled}" RibbonStyle="Office2010" ToolbarShowMode="Hide" AllowCustomization="False" IsMinimized="{Binding IsRibbonMinimized,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" MinimizationButtonVisibility="Collapsed">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ExpandRibbonCommand}"/>
                </dxmvvm:Interaction.Behaviors>
                <!--负一屏-->
                <dxr:RibbonControl.ApplicationMenu>
                    <dxr:BackstageViewControl>
                        <dxr:BackstageViewControl.Items>
                            <!--负一屏信息按钮-->
                            <dxr:BackstageTabItem Content="相关信息">
                                <dxr:BackstageTabItem.ControlPane>
                                    <Views:InfoView/>
                                </dxr:BackstageTabItem.ControlPane>
                            </dxr:BackstageTabItem>
                        </dxr:BackstageViewControl.Items>
                    </dxr:BackstageViewControl>
                </dxr:RibbonControl.ApplicationMenu>

                <!--Ribbon设置-->
                <dxr:RibbonDefaultPageCategory Caption="Default Category">
                    <dxr:RibbonPage Caption="向导">
                        <dxr:RibbonPageGroup Caption="设置向导" ShowCaptionButton="False">
                            <dxb:BarButtonItem Content="通信配置" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/通信设置.png" Command="{Binding CommunicationSetNavigationCommand}"/>
                            <!--<dxb:BarButtonItem Content="电机反馈" RibbonStyle="Large" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/电机反馈.png" Command="{Binding MotorFeedbackNavigationCommand}"/>-->
                            <!--<dxb:BarButtonItem Content="电机参数" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/数据库.png" Command="{Binding MotorLibraryNavigationCommand}"/>-->
                            <dxb:BarButtonItem Content="电机反馈" RibbonStyle="Large" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/电机反馈.png" Command="{Binding MotorFeedbackNavigationCommand}"/>
                            <dxb:BarButtonItem Content="电机参数辨识" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/参数自学习.png" Command="{Binding MotorFeedbackAutoLearnNavigationCommand}"/>
                            <dxb:BarButtonItem Content="电机寻相" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/参数识别.png" Command="{Binding MotorParameterIdentification_AddInterfaceCommand}"/>
                            <dxb:BarButtonItem Content="JOG调试" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/JOGDriection.png" Command="{Binding JogDriectionDebug_For_AddInterfaceCommand}"/>
                            <dxb:BarButtonItem Content="参数自学习" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/惯量辨识参数整定.png" Command="{Binding InertiaIdentificationParameterSelf_Tunning_For_AddInterfaceCommand}"/>
                            <!--<dxb:BarButtonItem Content="单位设置" RibbonStyle="Large" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/单位设置.png" Command="{Binding UnitNavigationCommand}"/>
                            <dxb:BarButtonItem Content="限幅保护" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/限幅保护.png" Command="{Binding LimitAmplitudeNavigationCommand}"/>
                            <dxb:BarButtonItem Content="一般设定" RibbonStyle="Large" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/一般设置.png" Command="{Binding NormalSettingNavigationCommand}"/>
                            <dxb:BarButtonItem Content="数字IO" RibbonStyle="Large" LargeGlyph="{dx:DXImageOffice2013 Image=Bubble_32x32.png}" Command="{Binding DigitalIONavigationCommand}"/>-->
                        </dxr:RibbonPageGroup>

                        <!--<dxr:RibbonPageGroup Caption="三环配置">
                            <dxb:BarButtonItem Content="&#160;电流环&#160;"  LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/电流环.png" Command="{Binding CurrentLoopNavigationCommand}"/>
                            <dxb:BarButtonItem Content="&#160;速度环&#160;"  LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/速度环.png" Command="{Binding SpeedLoopNavigationCommand}"/>
                            <dxb:BarButtonItem Content="&#160;位置环&#160;"  LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/位置环.png" Command="{Binding PositionLoopNavigationCommand}"/>
                        </dxr:RibbonPageGroup>-->

                        <!--<dxr:RibbonPageGroup Caption="运动配置">
                            <dxb:BarButtonItem Content="寻零模式"  LargeGlyph="{dx:DXImageOffice2013 Image=Zoom_32x32.png}" Command="{Binding SeekZeroNavigationCommand}"/>
                        </dxr:RibbonPageGroup>-->
                    </dxr:RibbonPage>

                    <dxr:RibbonPage Caption="配置">
                        <dxr:RibbonPageGroup Caption="系统配置" ShowCaptionButton="False">
                            <!--<dxb:BarButtonItem Content="通信配置" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/通信设置.png" Command="{Binding CommunicationSetNavigationCommand}"/>
                            <dxb:BarButtonItem Content="电机反馈" RibbonStyle="Large" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/电机反馈.png" Command="{Binding MotorFeedbackNavigationCommand}"/>-->
                            <dxb:BarButtonItem Content="单位设置" RibbonStyle="Large" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/单位设置.png" Command="{Binding UnitNavigationCommand}"/>
                            <dxb:BarButtonItem Content="限幅保护" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/限幅保护.png" Command="{Binding LimitAmplitudeNavigationCommand}"/>
                            <dxb:BarButtonItem Content="一般设定" RibbonStyle="Large" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/一般设置.png" Command="{Binding NormalSettingNavigationCommand}"/>
                            <dxb:BarButtonItem Content="数字IO" RibbonStyle="Large" LargeGlyph="{dx:DXImageOffice2013 Image=Bubble_32x32.png}" Command="{Binding DigitalIONavigationCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="三环配置">
                            <dxb:BarButtonItem Content="&#160;电流环&#160;"  LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/电流环.png" Command="{Binding CurrentLoopNavigationCommand}"/>
                            <dxb:BarButtonItem Content="&#160;速度环&#160;"  LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/速度环.png" Command="{Binding SpeedLoopNavigationCommand}"/>
                            <dxb:BarButtonItem Content="&#160;位置环&#160;"  LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/位置环.png" Command="{Binding PositionLoopNavigationCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="高级功能">
                            <dxb:BarButtonItem Content="&#160;高级功能&#160;"  LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/高级功能.png" Command="{Binding AdvancedNavigationCommand}"/>                            
                        </dxr:RibbonPageGroup>

                        <!--<dxr:RibbonPageGroup Caption="运动配置">
                            <dxb:BarButtonItem Content="寻零模式"  LargeGlyph="{dx:DXImageOffice2013 Image=Zoom_32x32.png}" Command="{Binding SeekZeroNavigationCommand}"/>
                        </dxr:RibbonPageGroup>-->
                    </dxr:RibbonPage>

                    <dxr:RibbonPage Caption="调试">
                        <dxr:RibbonPageGroup Caption="手动调试">
                            <dxb:BarButtonItem Content="示波器应用" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/示波器.png" Command="{Binding OscilloscopeNavigationCommand}"/>
                            <dxb:BarButtonItem Content="三环调试"  LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/三环调试.png" Command="{Binding ThreeLoopNavigationCommand}"/>
                            <dxb:BarButtonItem Content="函数发生器"  LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/函数发生器.png" Command="{Binding FunctionGeneratorNavigationCommand}"/>
                            <dxb:BarButtonItem Content="运动调试"  LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/运动调试.png" Command="{Binding ActionNavigationCommand}"/>
                            <dxb:BarButtonItem Content="参数调优" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/参数调优.png" Command="{Binding ParameterTunningNavigationCommand}"/>
                        </dxr:RibbonPageGroup>
                       
                        <!--<dxr:RibbonPageGroup Caption="惯量识别">
                            <dxb:BarButtonItem Content="离线识别" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/离线识别.png" Command="{Binding OfflineInertiaIdentification_For_AddInterfaceCommand}"/>
                            <dxb:BarButtonItem Content="在线识别" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/在线识别.png" Command="{Binding OnlineInertiaIdentificationCommand}"/>
                        </dxr:RibbonPageGroup>-->

                        <!--<dxr:RibbonPageGroup Caption="JOG">
                            <dxb:BarButtonItem Content="JOG调试" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/JOG.png" Command="{Binding JogDebug_For_AddInterfaceCommand}"/>
                            <dxb:BarButtonItem Content="程序JOG&#x0a;&#160;&#160;&#160;调试" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/程序JOG.png" Command="{Binding ProgramJogDebug_For_AddInterfaceCommand}"/>
                        </dxr:RibbonPageGroup>-->

                        <!--<dxr:RibbonPageGroup Caption="参数辨识">
                            <dxb:BarButtonItem Content=" 电机参数 " LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/参数识别.png" Command="{Binding MotorParameterIdentification_AddInterfaceCommand}"/>
                        </dxr:RibbonPageGroup>-->
                    </dxr:RibbonPage>

                    <dxr:RibbonPage Caption="参数">
                        <dxr:RibbonPageGroup Caption="参数在线">
                            <dxb:BarButtonItem Content="参数读写" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/在线读写.png" Command="{Binding ParameterReadWriteNavigationCommand}"/>
                            <dxb:BarButtonItem Content="实时刷新" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/在线刷新.png" Command="{Binding RefreshParameterReadAndWriteTabControlCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="参数出厂">
                            <dxb:BarButtonItem Content="恢复出厂值" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/恢复出厂值.png" Command="{Binding FactoryResetCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="导入导出">
                            <dxb:BarButtonItem Content="参数导入" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/参数导入.png" Command="{Binding ImportConfigFileCommand}" CommandParameter="ParameterImport"/>
                            <dxb:BarButtonItem Content="参数导出" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/参数导出.png" Command="{Binding ExportConfigFileCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="EEPROM">
                            <dxb:BarButtonItem Content="参数保存到&#x0a;&#160;EEPROM" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/保存ROM.png" Command="{Binding SaveRAMtoEEPROMCommand}"/>
                            <dxb:BarButtonItem Content="从EEPROM&#x0a;&#160;&#160;读取参数" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/ROM读取.png" Command="{Binding ReadEEPROMtoRAMCommand}"/>

                            <!--<dxb:BarButtonItem Content="{DynamicResource BarButtonItem_SaveRAMtoEEPROM}" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/保存ROM.png" Command="{Binding SaveRAMtoEEPROMCommand}"/>
                            <dxb:BarButtonItem Content="{DynamicResource BarButtonItem_ReadEEPROMtoRAM}" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/ROM读取.png" Command="{Binding ReadEEPROMtoRAMCommand}"/>-->
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="参数监控">
                            <dxb:BarButtonItem Content="监控设置" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/监控设置.png" Command="{Binding ParameterMonitorNavigationCommand}"/>
                            <dxb:BarButtonItem Content="监控保存" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/监控设置保存.png" Command="{Binding ExportMoniotorFileCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="参数库">
                            <dxb:BarButtonItem Content="电机参数" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/数据库.png" Command="{Binding MotorLibraryNavigationCommand}"/>
                        </dxr:RibbonPageGroup>
                    </dxr:RibbonPage>

                    <dxr:RibbonPage Caption="帮助">
                        <dxr:RibbonPageGroup Caption="软件维护">
                            <dxb:BarButtonItem Content="&#160;软件重启&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/软件重启.png" Command="{Binding SystemRestartCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="硬件维护">
                            <dxb:BarButtonItem Content="&#160;固件升级&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/固件升级.png" Command="{Binding FirmwareUpdateNavigationCommand}"/>
                        </dxr:RibbonPageGroup>
                        
                        <dxr:RibbonPageGroup Caption="设备信息">
                        <!--<dxr:RibbonPageGroup Caption="{DynamicResource Caption_DeviceInformation}">-->
                            <dxb:BarButtonItem Content="&#160;版本校验&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/版本信息.png" Command="{Binding GetEditionInfoCommand}"/>
                            <!--<dxb:BarButtonItem Content="{DynamicResource BarButtonItem_GetEditionInfo}" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/版本信息.png" Command="{Binding GetEditionInfoCommand}"/>-->
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="使用说明">
                            <dxb:BarButtonItem Content="&#160;用户手册&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/工作手册.png" Command="{Binding OpenManualBookCommand}"/>
                            <dxb:BarButtonItem Content="&#160;伺服维护手册&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/工作手册.png" Command="{Binding OpenServoMaintenanceManualBookCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="编码器操作">
                            <dxb:BarButtonItem Content="&#160;清除多圈和故障&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/清除多圈和故障.png" Command="{Binding ClearMotorEncoderMultilapsAndFaultsCommand}"/>
                            <dxb:BarButtonItem Content="&#160;清除多圈&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/清除多圈.png" Command="{Binding ClearMotorEncoderMultilapsCommand}"/>
                            <dxb:BarButtonItem Content="&#160;清除故障&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/清除故障.png" Command="{Binding ClearMotorEncoderFaultsCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="路径设置" >
                            <dxb:BarButtonItem Content="&#160;参数路径&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/参数路径.png" Command="{Binding SetDefaultParameterPathCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="密码设置">
                            <dxb:BarButtonItem Content="&#160;权限密码&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/权限密码.png" Command="{Binding ModifyPasswordCommand}"/>
                        </dxr:RibbonPageGroup>

                        <!--中英文切换-->
                        <!--<dxr:RibbonPageGroup Caption="{DynamicResource Caption_LanguageSwitch}">
                            <dxb:BarButtonItem Content="{DynamicResource BarButtonItem_LanguageSwitch}" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/语言切换.png" Command="{Binding LanguageSwitchCommand}"/>
                        </dxr:RibbonPageGroup>-->

                    </dxr:RibbonPage>

                    <dxr:RibbonPage Caption="报警">
                        <dxr:RibbonPageGroup Caption="报警历史">
                            <dxb:BarButtonItem Content="&#160;历史查询&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/实时报警.png" Command="{Binding HardwareAlarmHistoryNavigationCommand}"/>
                            <dxb:BarButtonItem Content="&#160;历史清除&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/历史清除.png" Command="{Binding DeleteElectronicErrorHistoryCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="报警方式">
                            <dxb:BarButtonItem Content="侧边栏报警" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/报警方式.png" Command="{Binding SetIsAlarmAutoExpandCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="报警分析">
                            <dxb:BarButtonItem Content="&#160;报警原因&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/报警原因.png" Command="{Binding HardwareAlarmMeasureNavigationCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="故障数据">
                            <dxb:BarButtonItem Content="故障数据配置" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/故障数据配置.png" Command="{Binding HistoryDataConfigNavigationCommand}"/>
                            <dxb:BarButtonItem Content="故障数据查询" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/故障数据查询.png" Command="{Binding HistoryDataQueryNavigationCommand}"/>
                            <dxb:BarButtonItem Content="故障数据清除" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/故障数据清除.png" Command="{Binding HistoryDataClearCommand}"/>                            
                        </dxr:RibbonPageGroup>

                    </dxr:RibbonPage>

                    <dxr:RibbonPage Caption="出厂" IsVisible="{Binding LevelFactoryModeVisibility,UpdateSourceTrigger=PropertyChanged}">
                        <dxr:RibbonPageGroup Caption="一键出厂">
                            <dxb:BarButtonItem Content="&#160;全部初始&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/全部初始.png" Command="{Binding AllFactoryResetCommand}"/>
                            <dxb:BarButtonItem Content="&#160;全部重启&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/全部复位.png" Command="{Binding AllSystemResetCommand}"/>
                            <dxb:BarButtonItem Content="&#160;配置急停&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/配置急停.png" Command="{Binding AllConfigStopCommand}"/>
                            <dxb:BarButtonItem Content="&#160;全部运动&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/全部运动.png" Command="{Binding AllAxisActionCommand}" CommandParameter="AllAxisRunning"/>
                            <dxb:BarButtonItem Content="&#160;全部使能&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/全部使能.png" Command="{Binding AllServoEnabledCommand}"/>
                            <dxb:BarButtonItem Content="&#160;全部停止&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/全部暂停.png" Command="{Binding AllAxisActionCommand}" CommandParameter="AllAxisStop"/>
                            <dxb:BarButtonItem Content="&#160;全部禁能&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/全部禁能.png" Command="{Binding AllServoDisabledCommand}"/>
                            <dxb:BarButtonItem Content="&#160;全部故障清除&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/全部故障清除.png" Command="{Binding AllFaultResetCommand}"/>
                            <!--dxn:NavBarItem Content="全部故障清除" ImageSource="pack://application:,,,/ServoStudio;component/Icon/故障清除2.png" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding AllFaultResetCommand}"/-->
                        </dxr:RibbonPageGroup>

                    </dxr:RibbonPage>

                    <dxr:RibbonPage Caption="管理"  IsVisible="{Binding AdministratorVisibility}">
                        <dxr:RibbonPageGroup Caption="主题设置">
                            <dxr:RibbonGalleryBarItemLink BarItemName="ribbonGalleryBarItem" >
                                <dxr:RibbonGalleryBarItemLink.CustomResources>
                                    <ResourceDictionary>
                                        <Style TargetType="{x:Type dxb:GalleryItemControl}">
                                            <Setter Property="MinWidth" Value="{Binding ActualHeight, RelativeSource={RelativeSource Self}}"/>
                                        </Style>
                                    </ResourceDictionary>
                                </dxr:RibbonGalleryBarItemLink.CustomResources>
                            </dxr:RibbonGalleryBarItemLink>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="软件异常">
                            <dxb:BarButtonItem Content="&#160;查询日志&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/查询日志.png" Command="{Binding SoftwareErrorLogNavigationCommand}"/>
                            <dxb:BarButtonItem Content="&#160;删除日志&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/删除日志.png" Command="{Binding DeleteSoftwareErrorHistoryCommand}"/>
                            <dxb:BarButtonItem Content="&#160;日志链接&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/日志链接.png" Command="{Binding OpenLogFileCommand}"/>
                        </dxr:RibbonPageGroup>

                        <dxr:RibbonPageGroup Caption="出厂模式">
                            <dxb:BarButtonItem Content="&#160;出厂设置&#160;" LargeGlyph="pack://application:,,,/ServoStudio;component/Icon/一键控制.png" Command="{Binding SetOneKeyShortcutCommand}"/>
                        </dxr:RibbonPageGroup>
                    </dxr:RibbonPage>
                </dxr:RibbonDefaultPageCategory>
            </dxr:RibbonControl>

            <dxdo:DockLayoutManager Name="dockLayoutManager" Grid.Row="1">
                <dxdo:DockLayoutManager.AutoHideGroups>
                    <dxdo:AutoHideGroup Caption="AutoHideGroup" DockType="Right">
                        <dxdo:LayoutPanel x:Name="layoutPanel_ErrorAlarm" Caption="报警信息" AllowClose="False" MaxWidth="230" AutoHidden="{Binding IsAlarmInfoPageHidden,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" AllowDrag="True" AllowFloat="True" AllowMaximize="False" AllowMinimize="False">
                            <ScrollViewer VerticalScrollBarVisibility="Auto" >
                                <Grid Name="grid_ErrorAlarm">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <dxlc:FlowLayoutControl Grid.Row="0" Padding="0">
                                        <!--<dxlc:GroupBox Name="SlideAlarm0"  Header="{DynamicResource Header_SlideAlarm0}" Style="{StaticResource SlideAlarmGroupBoxStyle}">-->
                                        <dxlc:GroupBox Name="SlideAlarm0"  Header="实时报警1" Style="{StaticResource SlideAlarmGroupBoxStyle}">
                                            <Grid Margin="2">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <!--<Label Content="{DynamicResource Label_SlideAlarm0_Description}" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="{DynamicResource Label_SlideAlarm0_ID}" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="{DynamicResource Label_SlideAlarm0_Level}" Grid.Row="2" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="{DynamicResource Label_SlideAlarm0_Time}" Grid.Row="3" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="{DynamicResource Label_SlideAlarm0_Measures}" Grid.Row="4" Grid.Column="0" Style="{StaticResource LabelStyle}"/>-->
                                                <Label Content="报警描述" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警编号" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警等级" Grid.Row="2" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警时间" Grid.Row="3" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="处理措施" Grid.Row="4" Grid.Column="0" Style="{StaticResource LabelStyle}"/>

                                                <Label Grid.Row="0" Grid.Column="1" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}">
                                                    <TextBlock Text="{Binding SlideHardwareAlarm[0].Content}" TextWrapping="Wrap"/>
                                                </Label>
                                                <Label Grid.Row="1" Grid.Column="1" Content="{Binding SlideHardwareAlarm[0].Code}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}"/>
                                                <Label Grid.Row="2" Grid.Column="1" Content="{Binding SlideHardwareAlarm[0].Level}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}" Background="{Binding SlideHardwareAlarm[0].Level, Converter={StaticResource AlarmLevelConverter}}"/>
                                                <Label Grid.Row="3" Grid.Column="1" Content="{Binding SlideHardwareAlarm[0].DateTime}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}"/>
                                                <Label Grid.Row="4" Grid.Column="1" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}">
                                                    <TextBlock Text="{Binding SlideHardwareAlarm[0].Measure}" TextWrapping="Wrap"/>
                                                </Label>
                                            </Grid>
                                        </dxlc:GroupBox>
                                    </dxlc:FlowLayoutControl>

                                    <dxlc:FlowLayoutControl Grid.Row="1" Padding="0">
                                        <dxlc:GroupBox Name="SlideAlarm1" Header="实时报警2" Style="{StaticResource SlideAlarmGroupBoxStyle}">
                                            <Grid Margin="2">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <Label Content="报警描述" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警编号" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警等级" Grid.Row="2" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警时间" Grid.Row="3" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="处理措施" Grid.Row="4" Grid.Column="0" Style="{StaticResource LabelStyle}"/>

                                                <Label Grid.Row="0" Grid.Column="1" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}">
                                                    <TextBlock Text="{Binding SlideHardwareAlarm[1].Content}" TextWrapping="Wrap"/>
                                                </Label>
                                                <Label Grid.Row="1" Grid.Column="1" Content="{Binding SlideHardwareAlarm[1].Code}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}"/>
                                                <Label Grid.Row="2" Grid.Column="1" Content="{Binding SlideHardwareAlarm[1].Level}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}" Background="{Binding SlideHardwareAlarm[1].Level, Converter={StaticResource AlarmLevelConverter}}"/>
                                                <Label Grid.Row="3" Grid.Column="1" Content="{Binding SlideHardwareAlarm[1].DateTime}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}"/>
                                                <Label Grid.Row="4" Grid.Column="1" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}">
                                                    <TextBlock Text="{Binding SlideHardwareAlarm[1].Measure}" TextWrapping="Wrap"/>
                                                </Label>
                                            </Grid>
                                        </dxlc:GroupBox>
                                    </dxlc:FlowLayoutControl>

                                    <dxlc:FlowLayoutControl Grid.Row="2" Padding="0">
                                        <dxlc:GroupBox Name="SlideAlarm2" Header="实时报警3" Style="{StaticResource SlideAlarmGroupBoxStyle}">
                                            <Grid Margin="2">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <Label Content="报警描述" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警编号" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警等级" Grid.Row="2" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警时间" Grid.Row="3" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="处理措施" Grid.Row="4" Grid.Column="0" Style="{StaticResource LabelStyle}"/>

                                                <Label Grid.Row="0" Grid.Column="1" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}">
                                                    <TextBlock Text="{Binding SlideHardwareAlarm[2].Content}" TextWrapping="Wrap"/>
                                                </Label>
                                                <Label Grid.Row="1" Grid.Column="1" Content="{Binding SlideHardwareAlarm[2].Code}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}"/>
                                                <Label Grid.Row="2" Grid.Column="1" Content="{Binding SlideHardwareAlarm[2].Level}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}" Background="{Binding SlideHardwareAlarm[2].Level, Converter={StaticResource AlarmLevelConverter}}"/>
                                                <Label Grid.Row="3" Grid.Column="1" Content="{Binding SlideHardwareAlarm[2].DateTime}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}"/>
                                                <Label Grid.Row="4" Grid.Column="1" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}">
                                                    <TextBlock Text="{Binding SlideHardwareAlarm[2].Measure}" TextWrapping="Wrap"/>
                                                </Label>
                                            </Grid>
                                        </dxlc:GroupBox>
                                    </dxlc:FlowLayoutControl>

                                    <dxlc:FlowLayoutControl Grid.Row="3" Padding="0">
                                        <dxlc:GroupBox Name="SlideAlarm3"  Header="实时报警4" Style="{StaticResource SlideAlarmGroupBoxStyle}">
                                            <Grid Margin="2">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <Label Content="报警描述" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警编号" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警等级" Grid.Row="2" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警时间" Grid.Row="3" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="处理措施" Grid.Row="4" Grid.Column="0" Style="{StaticResource LabelStyle}"/>

                                                <Label Grid.Row="0" Grid.Column="1" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}">
                                                    <TextBlock Text="{Binding SlideHardwareAlarm[3].Content}" TextWrapping="Wrap"/>
                                                </Label>
                                                <Label Grid.Row="1" Grid.Column="1" Content="{Binding SlideHardwareAlarm[3].Code}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}"/>
                                                <Label Grid.Row="2" Grid.Column="1" Content="{Binding SlideHardwareAlarm[3].Level}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}" Background="{Binding SlideHardwareAlarm[3].Level, Converter={StaticResource AlarmLevelConverter}}"/>
                                                <Label Grid.Row="3" Grid.Column="1" Content="{Binding SlideHardwareAlarm[3].DateTime}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}"/>
                                                <Label Grid.Row="4" Grid.Column="1" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}">
                                                    <TextBlock Text="{Binding SlideHardwareAlarm[3].Measure}" TextWrapping="Wrap"/>
                                                </Label>
                                            </Grid>
                                        </dxlc:GroupBox>
                                    </dxlc:FlowLayoutControl>

                                    <dxlc:FlowLayoutControl Grid.Row="4" Padding="0">
                                        <dxlc:GroupBox Name="SlideAlarm4"  Header="实时报警5" Style="{StaticResource SlideAlarmGroupBoxStyle}">
                                            <Grid Margin="2">
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <Label Content="报警描述" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警编号" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警等级" Grid.Row="2" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="报警时间" Grid.Row="3" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                                <Label Content="处理措施" Grid.Row="4" Grid.Column="0" Style="{StaticResource LabelStyle}"/>

                                                <Label Grid.Row="0" Grid.Column="1" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}">
                                                    <TextBlock Text="{Binding SlideHardwareAlarm[4].Content}" TextWrapping="Wrap"/>
                                                </Label>
                                                <Label Grid.Row="1" Grid.Column="1" Content="{Binding SlideHardwareAlarm[4].Code}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}"/>
                                                <Label Grid.Row="2" Grid.Column="1" Content="{Binding SlideHardwareAlarm[4].Level}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}" Background="{Binding SlideHardwareAlarm[4].Level, Converter={StaticResource AlarmLevelConverter}}"/>
                                                <Label Grid.Row="3" Grid.Column="1" Content="{Binding SlideHardwareAlarm[4].DateTime}" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}"/>
                                                <Label Grid.Row="4" Grid.Column="1" BorderThickness="1" BorderBrush="LightGray" Style="{StaticResource LabelStyle}">
                                                    <TextBlock Text="{Binding SlideHardwareAlarm[4].Measure}" TextWrapping="Wrap"/>
                                                </Label>
                                            </Grid>
                                        </dxlc:GroupBox>
                                    </dxlc:FlowLayoutControl>
                                </Grid>
                            </ScrollViewer>
                        </dxdo:LayoutPanel>

                        <dxdo:LayoutPanel x:Name="layoutPanel_ParameterMonitoring" Caption="监控数据" AllowClose="False" dx:ScrollBarExtensions.ScrollBarMode="TouchOverlap" MaxWidth="280" AutoHideExpandState="{Binding MonitoringPageExpandState, Mode=OneWayToSource,UpdateSourceTrigger=PropertyChanged}" AutoHidden = "{Binding IsMonitoringPageHidden,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" AllowDrag="True" AllowFloat="True" AllowMaximize="False" AllowMinimize="False">
                            <dxmvvm:Interaction.Behaviors>
                                <dxmvvm:EventToCommand EventName="IsVisibleChanged" Command="{Binding SwitchParameterMonitorCommand}" CommandParameter="{Binding Path=IsVisible,ElementName=layoutPanel_ParameterMonitoring}"/>
                            </dxmvvm:Interaction.Behaviors>
                            <dxlc:GroupBox Style="{StaticResource GroupBoxStyle}" Padding="0" Header="监控数据显示">
                                <dxg:GridControl SelectionMode="Cell" AutoGenerateColumns="AddNew" EnableSmartColumnsGeneration="True" ItemsSource="{Binding ParameterMonitoring}">
                                    <dxg:GridControl.View>
                                        <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25">
                                            <dxg:TableView.FormatConditions>
                                                <dxg:FormatCondition ApplyToRow="True" Expression="Not IsNullOrEmpty([Value])" FieldName="ParameterName">
                                                    <dx:Format Background="#FFC4E9F5"/>
                                                </dxg:FormatCondition>
                                            </dxg:TableView.FormatConditions>
                                        </dxg:TableView>
                                    </dxg:GridControl.View>

                                    <dxg:GridColumn FieldName="ParameterName" Header="参数名" IsSmart="True" Width="2.2*"/>
                                    <dxg:GridColumn FieldName="Value" Header="参数值" IsSmart="True" Width="1.5*"/>
                                    <dxg:GridColumn FieldName="Unit" Header="单位" IsSmart="True" Width="*"/>
                                </dxg:GridControl>
                            </dxlc:GroupBox>
                        </dxdo:LayoutPanel>
                    </dxdo:AutoHideGroup>

                    <dxdo:AutoHideGroup Caption="LeftAutoHideGroup" DockType="Left">
                        <dxdo:LayoutPanel ItemWidth="Auto" AllowClose="False" Caption="快捷设置" MaxWidth="66" Name="layoutPanel" AllowDrag="False" AllowFloat="False" AllowMaximize="False" AllowMinimize="False" AutoHidden = "{Binding IsQuickZoomPageHidden,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" AllowMove="False">
                            <dxn:NavBarControl>
                                <dxn:NavBarGroup DisplayMode="ImageAndText" NavigationPaneVisible="False" Style="{StaticResource GroupStyle}">
                                    <dxn:NavBarItem Content="轴切换" ImageSource="pack://application:,,,/ServoStudio;component/Icon/轴地址切换.png" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding SwitchAxisCommand}"/>
                                    <!--dxn:NavBarItem Content="{Binding ServoStatusSwitch}" ImageSource="{Binding ServoStatusIcon}" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding ServoEnabledCommand}"/-->
                                    <!--dxn:NavBarItem Content="伺服禁能" ImageSource="pack://application:,,,/ServoStudio;component/Icon/一键全停.png" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding ServoDisabledCommand}"/-->
                                    <!--dxn:NavBarItem Content="全部故障清除" ImageSource="pack://application:,,,/ServoStudio;component/Icon/全部故障清除.png" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding AllFaultResetCommand}"/-->
                                    <!--dxn:NavBarItem Content="全部禁能" ImageSource="pack://application:,,,/ServoStudio;component/Icon/一键全停.png" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding AllServoDisabledCommand}"/-->
                                    <!--<dxn:NavBarItem Content="伺服禁能" ImageSource="pack://application:,,,/ServoStudio;component/Icon/一键全停.png" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding ServoDisabledCommand}"/>-->
                                    <dxn:NavBarItem Content="{Binding ServoStatusSwitch}" ImageSource="{Binding ServoStatusIcon}" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding ServoDisabledCommand}"/>
                                    <dxn:NavBarItem Content="故障清除" ImageSource="pack://application:,,,/ServoStudio;component/Icon/故障清除.png" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding FaultResetCommand}"/>
                                    <dxn:NavBarItem Content="伺服重启" ImageSource="pack://application:,,,/ServoStudio;component/Icon/系统复位.png" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding SystemResetCommand}"/>
                                    <dxn:NavBarItem Content="{Binding SlideAlarm}" ImageSource="{Binding SlideAlarmIcon}" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding SlideAlarmDisplayControlCommand}"/>
                                    <dxn:NavBarItem Content="{Binding SlideMonitor}" ImageSource="{Binding SlideMonitorIcon}" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding SlideMonitorDisplayControlCommand}"/>
                                    <dxn:NavBarItem Content="{Binding SlideControlModel}" ImageSource="{Binding ControlModelIcon}" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}" Command="{Binding ControlSourceOptionCommand}"/>
                                    <!--<dxn:NavBarItem Content="{Binding SlideServoStatus}" ImageSource="{Binding SlideServoStatusIcon}" Style="{StaticResource ItemStyle}" VisualStyle="{StaticResource ItemVisibleStyle}"/>-->
                                </dxn:NavBarGroup>

                                <dxn:NavBarControl.View>
                                    <dxn:NavigationPaneView x:Name="navPanelView" IsOverflowPanelVisible="False" IsSplitterVisible="False" IsExpandButtonVisible="False"/>
                                </dxn:NavBarControl.View>
                            </dxn:NavBarControl>
                        </dxdo:LayoutPanel>
                    </dxdo:AutoHideGroup>
                </dxdo:DockLayoutManager.AutoHideGroups>

                <dxdo:LayoutGroup Name="layoutGroup_Root" Orientation="Horizontal">
                    
                    <!--主信息部分-->
                    <dxdo:LayoutPanel AllowClose="False" AllowRename="False" AllowSelection="True" AllowRestore="False" >
                        <dxwui:NavigationFrame Name="navigationFrame" Margin="0,-6,0,0" >
                            <dxmvvm:Interaction.Behaviors>
                                <dxwuin:FrameNavigationService Frame="{Binding ElementName=navigationFrame}"/>
                            </dxmvvm:Interaction.Behaviors>
                        </dxwui:NavigationFrame>
                    </dxdo:LayoutPanel>

                    <!--<dxdo:DocumentGroup  Name="Documents" DestroyOnClosingChildren="False" ClosePageButtonShowMode="InActiveTabPageHeader">
                        <dxdo:DocumentGroup.ItemStyle>
                            <Style TargetType="dxdo:LayoutPanel">
                                <Setter Property="Caption" Value="{Binding Caption}"/>
                                <Setter Property="IsActive" Value="{Binding IsActive, Mode=TwoWay}"/>
                            </Style>
                        </dxdo:DocumentGroup.ItemStyle>
                    </dxdo:DocumentGroup>-->
                    
                </dxdo:LayoutGroup>

            </dxdo:DockLayoutManager>

            <dxb:StatusBarControl Grid.Row="2" DockPanel.Dock="Bottom">
                
                <dxb:BarStaticItem ItemWidth="5" ShowBorder="False"/>
                <dxb:BarStaticItem Content="{Binding SerialPortConnectState}" ContentAlignment="Left" ShowBorder="False" Glyph="{dx:DXImageOffice2013 Image=ViewOnWeb_32x32.png}" GlyphSize="Small" BarItemDisplayMode="ContentAndGlyph">
                    <dxb:BarStaticItem.ContentTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Label Margin="0,-3" Content="通信状态：" HorizontalContentAlignment="Left" Foreground="{Binding DataContext.SerialPortConnectStateFontColor, RelativeSource={RelativeSource AncestorType={x:Type dxb:StatusBarControl}} ,Converter={StaticResource BackgroudConverter}}"/>
                                <Label Margin="0,-3" Content="{Binding}"   HorizontalContentAlignment="Left" Foreground="{Binding DataContext.SerialPortConnectStateFontColor, RelativeSource={RelativeSource AncestorType={x:Type dxb:StatusBarControl}} ,Converter={StaticResource BackgroudConverter}}"/>
                            </StackPanel>
                        </DataTemplate>
                    </dxb:BarStaticItem.ContentTemplate>
                </dxb:BarStaticItem>

                <dxb:BarStaticItem ItemWidth="5" ShowBorder="False"/>
                <dxb:BarStaticItem Content="{Binding StatusWord}" ContentAlignment="Left" ShowBorder="False" Glyph="{dx:DXImageOffice2013 Image=Separator_16x16.png}" GlyphSize="Small" BarItemDisplayMode="ContentAndGlyph">
                    <dxb:BarStaticItem.ContentTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Label Margin="0,-3" Content="伺服状态：" HorizontalContentAlignment="Left" Foreground="{Binding DataContext.StatusWordFontColor, RelativeSource={RelativeSource AncestorType={x:Type dxb:StatusBarControl}} ,Converter={StaticResource BackgroudConverter}}"/>
                                <Label Margin="0,-3" Content="{Binding}"   HorizontalContentAlignment="Left" Foreground="{Binding DataContext.StatusWordFontColor, RelativeSource={RelativeSource AncestorType={x:Type dxb:StatusBarControl}} ,Converter={StaticResource BackgroudConverter}}"/>
                            </StackPanel>
                        </DataTemplate>
                    </dxb:BarStaticItem.ContentTemplate>
                </dxb:BarStaticItem>

                <dxb:BarStaticItem ItemWidth="5" ShowBorder="False"/>
                <dxb:BarStaticItem Content="{Binding AxisAddress}" ContentAlignment="Left" ShowBorder="False" Glyph="{dx:DXImageOffice2013 Image=Replace_32x32.png}" GlyphSize="Small" BarItemDisplayMode="ContentAndGlyph"/>
                                        
                <dxb:BarStaticItem ItemWidth="5" ShowBorder="False"/>
                <dxb:BarStaticItem Content="{Binding SerialPortWorkState}" ContentAlignment="Left" ShowBorder="False" Glyph="{dx:DXImageOffice2013 Image=Status_32x32.png}" GlyphSize="Small" BarItemDisplayMode="ContentAndGlyph"/>

                <dxb:BarStaticItem AutoSizeMode="Fill" ShowBorder="False"/>          
                <dxb:BarStaticItem Content="{Binding HintInfo}" IsVisible="{Binding IsHintInfoEnabled}" ContentAlignment="Center" ShowBorder="False" Glyph="{dx:DXImage Image=Info_32x32.png}" GlyphSize="Small" BarItemDisplayMode="ContentAndGlyph" >
                    <dxb:BarStaticItem.ContentTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Label Margin="0,-3" Content="信息提示：" HorizontalContentAlignment="Left" Foreground="Red"/>
                                <Label Margin="0,-3" Content="{Binding}" HorizontalContentAlignment="Left" Foreground="Red"/>
                            </StackPanel>
                        </DataTemplate>
                    </dxb:BarStaticItem.ContentTemplate>
                </dxb:BarStaticItem>
                
                <dxb:BarStaticItem AutoSizeMode="Fill" ShowBorder="False"/>
                <dxb:BarStaticItem Content="{Binding CurrentFeedback}" IsVisible="{Binding IsHintMonitorEnabled}" ContentAlignment="Right" ShowBorder="False" Glyph="{dx:DXImageOffice2013 Image=ContentAutoArrange_32x32.png}" GlyphSize="Small" BarItemDisplayMode="ContentAndGlyph"/>
               
                <dxb:BarStaticItem ItemWidth="5" ShowBorder="False"/>
                <dxb:BarStaticItem Content="{Binding PositionFeedback}" IsVisible="{Binding IsHintMonitorEnabled}" ContentAlignment="Left" ShowBorder="False" Glyph="{dx:DXImage Image=GeoPoint_32x32.png}" GlyphSize="Small" BarItemDisplayMode="ContentAndGlyph" LargeGlyph="{dx:DXImage Image=GeoPoint_32x32.png}"/>
                
                <dxb:BarStaticItem ItemWidth="5" ShowBorder="False"/>
                <dxb:BarStaticItem Content="{Binding SpeedFeedback}" IsVisible="{Binding IsHintMonitorEnabled}" ContentAlignment="Right" ShowBorder="False" Glyph="{dx:DXImage Image=GaugeStyleThreeForthCircular_32x32.png}" GlyphSize="Small" BarItemDisplayMode="ContentAndGlyph"/>                           
             
                <dxb:BarStaticItem ItemWidth="5" ShowBorder="False"/>
                <dxb:BarStaticItem Content="{Binding InertiaRatio}" IsVisible="{Binding IsInertiaRatioEnabled}" ContentAlignment="Right" ShowBorder="False" Glyph="{dx:DXImageOffice2013 Image=Technology_32x32.png}" GlyphSize="Small" BarItemDisplayMode="ContentAndGlyph"/>
                <dxb:BarStaticItem ItemWidth="5" ShowBorder="False"/>
            </dxb:StatusBarControl>
        </Grid>

    </dxb:BarManager>
    
</dxr:DXRibbonWindow>
