# ServoStudio 示波器通道数据错位修复测试指南

## 🎯 测试目标

验证修复后的示波器功能能够正确处理通道数据，解决Debug参数5采集到位置增量的增量数据的问题。

## 📋 测试准备

### 1. 确认XML配置
检查 `ServoStudio\Xml\OscilloscopeOptions.xml` 文件中的相关配置：

```xml
<Channel GroupName="Debug (4)" ID="42" ItemName="Debug参数5" Address="2A" />
<Channel GroupName="位置 (5)" ID="43" ItemName="位置增量的增量" Address="2B" />
```

### 2. 编译项目
确保修复后的代码能够正常编译。

## 🧪 测试步骤

### 测试场景1：两通道测试
1. **选择通道**：
   - 通道1：选择 "Debug参数5" (ID=42, Address="2A")
   - 通道2：选择 "位置增量的增量" (ID=43, Address="2B")

2. **启动采集**：
   - 设置合适的采样周期和时长
   - 点击开始采集

3. **观察调试输出**：
   在Visual Studio的输出窗口中查看调试信息：
   ```
   === 通道映射关系 ===
   数据索引0: 通道ID=42, UI通道=1, 通道名=Debug参数5, 单位=...
   数据索引1: 通道ID=43, UI通道=2, 通道名=位置增量的增量, 单位=...
   
   === 数据分配 === 数据索引:0, UI通道:1, 数据点数:...
   分配到Channel1, 数据点数: ...
   === 数据分配 === 数据索引:1, UI通道:2, 数据点数:...
   分配到Channel2, 数据点数: ...
   ```

4. **验证结果**：
   - Debug参数5应该显示正确的数据（不是位置增量的增量的数据）
   - 位置增量的增量应该显示正确的数据

### 测试场景2：三通道测试
1. **选择通道**：
   - 通道1：选择 "Debug参数4" (ID=41, Address="29")
   - 通道2：选择 "Debug参数5" (ID=42, Address="2A")
   - 通道3：选择 "位置增量的增量" (ID=43, Address="2B")

2. **启动采集并验证**：
   - 每个通道应该显示对应地址的正确数据
   - 不应该出现数据错位现象

### 测试场景3：逆序选择测试
1. **选择通道**：
   - 通道1：选择 "位置增量的增量" (ID=43, Address="2B")
   - 通道2：选择 "Debug参数5" (ID=42, Address="2A")

2. **验证映射**：
   - 调试输出应该显示正确的ID排序和映射关系
   - 数据应该正确分配到对应的UI通道

## ✅ 预期结果

### 修复前的问题
- Debug参数5会显示位置增量的增量的数据
- 数据与通道名称不匹配

### 修复后的预期
- 每个通道显示正确对应地址的数据
- 通道映射关系正确建立
- 单位信息与数据正确匹配

## 🔧 调试信息说明

### 通道映射关系输出
```
数据索引0: 通道ID=42, UI通道=1, 通道名=Debug参数5, 单位=...
```
- **数据索引**：硬件返回数据的顺序（按ID排序）
- **通道ID**：XML配置中的ID
- **UI通道**：用户界面上的通道编号
- **通道名**：显示的通道名称

### 数据分配输出
```
=== 数据分配 === 数据索引:0, UI通道:1, 数据点数:100
分配到Channel1, 数据点数: 100
```
- 确认数据正确分配到对应的UI通道

## 🚨 故障排除

### 如果仍然出现数据错位
1. 检查调试输出中的通道映射关系是否正确
2. 确认lstChannelMapping列表的内容
3. 验证GetSampleNameByIndex方法返回的通道名称
4. 检查单位字典中是否包含对应的通道名称

### 常见问题
1. **编译错误**：确保ChannelMapping类已正确定义
2. **调试信息不显示**：确保在Debug模式下运行
3. **数据为空**：检查硬件连接和通信状态

## 📝 测试记录

请记录测试结果：

- [ ] 测试场景1通过
- [ ] 测试场景2通过  
- [ ] 测试场景3通过
- [ ] 调试信息正确显示
- [ ] 数据错位问题已解决

## 🎉 测试完成

如果所有测试场景都通过，说明修复成功。可以移除调试代码并进行最终的代码清理。
