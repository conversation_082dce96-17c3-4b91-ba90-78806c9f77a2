﻿using ServoStudio.GlobalConstant;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Windows.Data;

namespace Converter
{
    public class BackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                switch ((int)value)
                {
                    case 0:
                        return "Gray";
                    case 1:
                        return "Green";
                    case 2:
                        return "Orange";
                    case 3:
                        return "Red";
                    case 4:
                        return "White";
                    case 5:
                        return "Black";
                    case 6:
                        return "Yellow";
                    case 7:
                        return "Orange";
                    case 8:
                        return "Blue";
                    default:
                        return "Transparent";
                }
            }
            catch
            {
                return "Red";
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
