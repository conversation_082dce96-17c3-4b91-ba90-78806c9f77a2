﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.ObjectModel;
using DevExpress.Mvvm.POCO;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using ServoStudio.Models;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.Threading;
using System.ComponentModel;
using System.Windows.Data;
using System.Linq;
using System.Windows.Controls;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class OscilloscopeViewModel
    {
        #region 字段
        private static bool IsInitialized = true;//是否初次载入，初始化
        private static bool IsEvaluationAll = true;//是否要全部赋值
        private static bool IsLoadedAgain = false;//是否是多次载入  
        private static string strActionMode = null;
        private static string strParameterTunningMode = null;
        public double dLastTime = 0;//获取上一次X坐标-时间
        public int iIndex = 0;//获取上一个检索号
        public double dLastCH1Value = 0;//获取上一次CH1值
        public double dLastCH2Value = 0;//获取上一次CH2值
        public double dLastCH3Value = 0;//获取上一次CH3值
        public double dLastCH4Value = 0;//获取上一次CH4值  

        private int Id = 0;

        public string SelectedSamplingChannel1;
        public string SelectedSamplingChannel2;
        public string SelectedSamplingChannel3;
        public string SelectedSamplingChannel4;

        private Dictionary<string, string> dicDefaultUnit;
        #endregion

        #region 服务
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }

        [ServiceProperty(Key = "TorqueLegend")]
        protected virtual IDialogService DialogService_TorqueLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "PositionLegend")]
        protected virtual IDialogService DialogService_PositionLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "SlopeLegend")]
        protected virtual IDialogService DialogService_SlopeLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "JerkFreeLegend")]
        protected virtual IDialogService DialogService_JerkFreeLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "SquareLegend")]
        protected virtual IDialogService DialogService_SquareLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "SinLegend")]
        protected virtual IDialogService DialogService_SinLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "StepLegend")]
        protected virtual IDialogService DialogService_StepLegend { get { return this.GetService<IDialogService>(); } }

        [ServiceProperty(Key = "SlopeAscLegend")]
        protected virtual IDialogService DialogService_SlopeAscLegend { get { return this.GetService<IDialogService>(); } }
        #endregion

        #region 示波器属性  

        #region 采样参数
        public virtual string Unit { get; set; }//采样单位
        public virtual ObservableCollection<string> SamplingPeriod { get; set; }//采样周期
        public virtual string SelectedSamplingPeriod { get; set; }
        public virtual ObservableCollection<string> SamplingDuration { get; set; }//采样时长
        public virtual string SelectedSamplingDuration { get; set; }
        public virtual int SelectedSamplingDurationIndex { get; set; }
        public virtual string SamplingDurationUnit { get; set; }//采样时长单位
        public virtual ObservableCollection<string> ContinuousSampling { get; set; }//是否连续采样
        public virtual string SelectedContinuousSampling { get; set; }
        #endregion

        #region 预设置
        public virtual ObservableCollection<string> OscilloscopePreset { get; set; }//示波器预设置配置
        public virtual string SelectedOscilloscopePreset { get; set; }        
        #endregion
        #region 采样通道 
        public virtual List<SampleChannelInfoSet> SampleChannelInfo1 { get; set; }
        public virtual List<SampleChannelInfoSet> SampleChannelInfo1_Display { get; set; }//由Lilbert添加SampleChannelInfo1_Display用于展示
        public ICollectionView GroupedSampleChannelInfo1 { get; set; }
        public virtual int SelectedSampleChannel1Index { get; set; }
       
        public virtual List<SampleChannelInfoSet> SampleChannelInfo2 { get; set; }
        public virtual List<SampleChannelInfoSet> SampleChannelInfo2_Display { get; set; }//由Lilbert添加SampleChannelInfo2_Display用于展示
        public ICollectionView GroupedSampleChannelInfo2 { get; set; }
        public virtual int SelectedSampleChannel2Index { get; set; }
       
        public virtual List<SampleChannelInfoSet> SampleChannelInfo3 { get; set; }
        public virtual List<SampleChannelInfoSet> SampleChannelInfo3_Display { get; set; }//由Lilbert添加SampleChannelInfo3_Display用于展示
        public ICollectionView GroupedSampleChannelInfo3 { get; set; }
        public virtual int SelectedSampleChannel3Index { get; set; }
       
        public virtual List<SampleChannelInfoSet> SampleChannelInfo4 { get; set; }
        public virtual List<SampleChannelInfoSet> SampleChannelInfo4_Display { get; set; }//由Lilbert添加SampleChannelInfo4_Display用于展示
        public ICollectionView GroupedSampleChannelInfo4 { get; set; }
        public virtual int SelectedSampleChannel4Index { get; set; }     
        #endregion

        #region 触发器
        public virtual ObservableCollection<string> TriggerClockEdge { get; set; }//触发边沿
        public virtual string SelectedTriggerClockEdge { get; set; }
        public virtual ObservableCollection<string> PreTrigger { get; set; }//预触发
        public virtual string SelectedPreTrigger { get; set; }
        public virtual ObservableCollection<string> TriggerChannel { get; set; }//触发通道
        public virtual string SelectedTriggerChannel { get; set; }
        public virtual string TriggerLevel { get; set; }//触发水平
        #endregion

        #region 其他
        public virtual int SelectedTabIndex { get; set; }
        public virtual bool Channel2Enabled { get; set; }//通道2使能
        public virtual bool Channel3Enabled { get; set; }//通道3使能
        public virtual bool Channel4Enabled { get; set; }//通道4使能
        public virtual bool ContinuousAcquisitionEnabled { get; set; }//连续采样使能
        public virtual bool AcquisitionStartButtonEnabled { get; set; }//开始采集按钮使能
        public virtual bool OthersButtonEnabled { get; set; }//其他关于波形按钮使能
        public virtual ObservableCollection<string> DisplayMethod { get; set; }//展示方式
        public virtual string SelectedDisplayMethod { get; set; }//选择的展示方式
        public virtual ObservableCollection<OscilloscopePropertySet> OscilloscopeProperty { get; set; }//示波器属性集合
        public virtual ObservableCollection<OsilloscopeCalculateSet> OsilloscopeCalculate { get; set; }//数据计算
        #endregion

        #endregion

        #region 函数发生器与三环调试与运动调试属性
        //使能
        public virtual int IsSpeedPageEnabled { get; set; }//是否速度环界面可以使用
        public virtual int IsCurrentPageEnabled { get; set; }//是否电流环界面可以使用
        public virtual int IsPositionPageEnabled { get; set; }//是否位置环界面可以使用
        public virtual int IsPositionActionEnabled { get; set; }//位置模式界面可以使用
        public virtual int IsSlopeActionEnabled { get; set; }//速度模式界面可以使用
        public virtual int IsJerkFreeActionEnabled { get; set; }//JerkFree界面可以使用
        public virtual int IsTorqueActionEnabled { get; set; }//转矩模式界面可以使用

        public virtual int IsSpeedParameterTunningEnabled { get; set; }//参数调优速度模式界面可以使用
        public virtual int IsPositionParameterTunningEnabled { get; set; }//参数调优位置模式界面可以使用

        //三环调试
        public virtual ObservableCollection<string> LoopMode { get; set; }//三环模式
        public virtual string SelectedLoopMode { get; set; }//选中的模式
        public virtual string PositionLoopGain { get; set; }//位置环增益
        public virtual string LoadInertiaRatio { get; set; }//负载惯量比      由Lilbert增加负载惯量比
        public virtual string FirstTrqcmdFilterTime { get; set; }//第一转矩滤波时间   由Lilbert增加第一转矩滤波时间
        public virtual string SpeedLoopGain { get; set; }//速度环增益          
        public virtual string SpeedLoopTimeConstant { get; set; }//速度环积分时间常数
        public virtual string CurrentLoopGain { get; set; }//电流环增益
        public virtual string CurrentLoopTimeConstant { get; set; }//电流环积分时间常数
        public virtual string Rigidity { get; set; }//刚性      由Lilbert增加刚性

        //函数发生器
        public virtual string InnerSourceFrequency { get; set; }//信号频率
        public virtual string InnerSourceAmplitude { get; set; }//位置幅值
        public virtual ObservableCollection<string> InnerSourceEffect { get; set; }
        public virtual string SelectedInnerSourceEffectIndex { get; set; }//作用对象
        public virtual ObservableCollection<string> InnerSourceType { get; set; }
        public virtual string SelectedInnerSourceTypeIndex { get; set; }//函数类型
        public virtual string InnerSourceNumber { get; set; }//产生个数
        public virtual string AmplitudeUnit { get; set; }//幅值单位
        public virtual string InnerSourceGradient { get; set; }//信号斜率
        public virtual bool InnerSourceNumberEnabled { get; set; }//产生个数使能
        public virtual int InnerSourceGradientVisibility { get; set; }//信号斜率可视

        //运动调试
        public virtual ObservableCollection<string> ActionMode { get; set; }//运动模式选择
        public virtual string SelectedActionMode { get; set; }//选中的运动模式

        public virtual ObservableCollection<string> PositionMode { get; set; }//位置指令
        public virtual string SelectedPositionMode { get; set; }//选中的位置指令

        public virtual ObservableCollection<string> ProfileMode { get; set; }//规划曲线
        public virtual string SelectedProfileMode { get; set; }//选中的规划曲线

        //参数调优
        public virtual ObservableCollection<string> ProgramJogSwitch { get; set; }//程序PROGRAMJOG开关
        public virtual string SelectedProgramJogSwitchIndex { get; set; }//选中程序PROGRAMJOG开关
        public virtual string LegendAddress { get; set; }//图例地址
        public virtual ObservableCollection<string> ParameterTunningMode { get; set; }//参数调优模式选择
        public virtual string SelectedParameterTunningMode { get; set; }//选中的参数调优模式

        public virtual string ParameterTunningJogSpeed { get; set; }//Jog点动速度
        public virtual string ParameterTunningJogAccelerationTime { get; set; }//Jog加速时间
        public virtual string ParameterTunningJogDecelerationTime { get; set; }//Jog减速时间
        public virtual string ParameterTunningJogTime { get; set; }//Jog运动时间

        public virtual string ParameterTunningProgramJogMovingDistance { get; set; }//程序PROGRAMJOG移动距离
        public virtual string ParameterTunningProgramJogMovingSpeed { get; set; }//程序PROGRAMJOG移动速度
        public virtual string ParameterTunningProgramJogAccDecTime { get; set; }//程序PROGRAMJOG加减速时间
        public virtual string ParameterTunningProgramJogWaitTime { get; set; }//程序PROGRAMJOG等待时间
        public virtual string ParameterTunningProgramJogMovingNumber { get; set; }//程序PROGRAMJOG移动次数

        public virtual string TargetPosition { get; set; }//目标位置
        public virtual string ProfileVelocity { get; set; }//轮廓运行速度
        public virtual string EndProfileVelocity { get; set; }//轮廓结尾速度
        public virtual string ProfileAcceleration { get; set; }//轮廓加速度
        public virtual string ProfileDeceleration { get; set; }//轮廓减速度

        public virtual string TargetVelocity { get; set; }//目标速度
        public virtual string ProfileJerk1 { get; set; }//轮廓加加速度1
        public virtual string ProfileJerk2 { get; set; }//轮廓加加速度2
        public virtual string ProfileJerk3 { get; set; }//轮廓加加速度3
        public virtual string ProfileJerk4 { get; set; }//轮廓加加速度4

        public virtual string TargetTorque { get; set; }//目标转矩
        public virtual string TorqueSlope { get; set; }//转矩斜坡

        public virtual string PositionUnit { get; set; }//位置单位
        public virtual string TorqueUnit { get; set; }//转矩单位
        public virtual string SpeedUnit { get; set; }//速度单位
        public virtual string AccelerationUnit { get; set; }//加速单位
        #endregion

        #region 构造函数
        public OscilloscopeViewModel()
        {
            //示波器预配置文件加载
            //XmlHelper.XmlOscilloscopePresetConfigs();

            ViewModelSet.Oscilloscope = this;

            //示波器预配置文件加载
            XmlHelper.XmlOscilloscopePresetConfigs();
            XmlHelper.XmlOscilloscopeOptions();

            //采样通道分组初始化
            GroupSampleChannelInitialize();
        }
        #endregion

        #region 公有方法

        #region 示波器
        //*************************************************************************
        //函数名称：OscilloscopeLoaded
        //函数功能：Oscilloscope控件Loaded
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.20
        //*************************************************************************
        public void OscilloscopeLoaded()
        {
            int iRet = -1;

            try
            {
                #region 初始化                
                //TabControl初始化
                SelectedTabIndex = SoftwareStateParameterSet.OscilloscopeTabControlIndex;              

                //波形控制与波形计算初始化
                WaveControl_WaveCalculateInitialize();

                //事件注册
                EventRegisterInitialize();

                //ObservableCollection初始化
                ObservableCollectionInitialize();

                //示波器预配置
                SelectedOscilloscopePreset = GlobalCurrentInput.SelectedOscilloscopePreset = IniHelper.IniReadValue("ServoStudio", "SelectedOscilloscopePreset", FilePath.Ini);

                //按钮使能初始化
                ButtonEnableInitialize();

                //默认单位集合
                DefaultUnitDictionaryInitialize();

                //侧边栏属性缩回
                ViewModelSet.Main?.SlidePanelDisplayControl(true, true);

                //ReadProgramJogParameter();
                #endregion

                #region 赋值
                if (IsInitialized == true)
                {
                    IsInitialized = false;
                    IsEvaluationAll = true;

                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadAdjustmentParameter("All");                        
                    }
                    else
                    {
                        GetDefaultAdjustmentParameter("All");
                    }

                    GetDefaultOscilloscopeSetParameter();

                    GetOscilloscopePresetData();
                    GetOscilloscopePreset(SelectedOscilloscopePreset);
                }
                else
                {
                    IsEvaluationAll = true;
                    IsLoadedAgain = true;

                    GetOscilloscopePresetData();
                    InterfaceEvaluationFromGlobalVariable();

                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadAdjustmentParameter("All");
                    }
                    
                    //触发通道、采样时长重新计算
                    IsLoadedAgain = false;
                    if (TriggerChannel != null)
                    {
                        if (GlobalCurrentInput.TriggerChannelIndex < TriggerChannel.Count && GlobalCurrentInput.TriggerChannelIndex >= 0)
                        {
                            SelectedTriggerChannel = TriggerChannel[GlobalCurrentInput.TriggerChannelIndex];
                            GetTriggerLevelValue(Method: "FromGlobal");
                        }
                    }
                    
                    if (SamplingDuration != null)
                    {
                        if (GlobalCurrentInput.SamplingDurationIndex < SamplingDuration.Count && GlobalCurrentInput.SamplingDurationIndex >= 0)
                        {
                            SelectedSamplingDuration = SamplingDuration[GlobalCurrentInput.SamplingDurationIndex];
                        }
                    }                 
                }
                #endregion
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_LOADED, "OscilloscopeLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：OscilloscopeUnloaded
        //函数功能：Oscilloscope控件Unloaded
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.02.15
        //*************************************************************************
        public void OscilloscopeUnloaded()
        {
            //保存示波器配置信息
            OscilloscopeConfigExport_ForUnloaded();

            //预配置写入配置文件
            OthersHelper.WriteSelectedOscilloscopePresetIntoFile();
        }

        //*************************************************************************
        //函数名称：OnSelectedProgramJogSwitchIndexChanged
        //函数功能：更新图例地址
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.04
        //*************************************************************************
        public void OnSelectedProgramJogSwitchIndexChanged()
        {
            LegendAddress = "pack://application:,,,/ServoStudio;component/Resource/ProgramJog" + SelectedProgramJogSwitchIndex + ".png";
        }

        //*************************************************************************
        //函数名称：OscilloscopeConfigExport
        //函数功能：保存示波器预配置设置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.26
        //*************************************************************************
        public void OscilloscopeConfigExport()
        {
            int iRet = -1;
            OscilloscopePresetModel oscilloscopePresetModel = new OscilloscopePresetModel
            {
                Id = GlobalCurrentInput.SelectedOscilloscopePreset = SelectedOscilloscopePreset,
                SelectedSampleChannel1Index = GlobalCurrentInput.SelectedSampleChannel1Index = SelectedSampleChannel1Index,
                SelectedSampleChannel2Index = GlobalCurrentInput.SelectedSampleChannel2Index = SelectedSampleChannel2Index,
                SelectedSampleChannel3Index = GlobalCurrentInput.SelectedSampleChannel1Index = SelectedSampleChannel3Index,
                SelectedSampleChannel4Index = GlobalCurrentInput.SelectedSampleChannel1Index = SelectedSampleChannel4Index,
                SamplingPeriod = GlobalCurrentInput.SelectedSamplingPeriod = SelectedSamplingPeriod,
                SamplingDuration = GlobalCurrentInput.SelectedSamplingDuration = SelectedSamplingDuration,
                ContinuousSampling = GlobalCurrentInput.SelectedContinuousSampling = SelectedContinuousSampling,
                TriggerClockEdge = GlobalCurrentInput.SelectedTriggerClockEdge = SelectedTriggerClockEdge,
                TriggerChannel = GlobalCurrentInput.SelectedTriggerChannel = SelectedTriggerChannel,
                PreTrigger = GlobalCurrentInput.SelectedPreTrigger = SelectedPreTrigger,
                TriggerLevel = GlobalCurrentInput.TriggerLevel = TriggerLevel
            };
            switch (SelectedOscilloscopePreset)
            {
                //停用
                //case 0:
                //    break;
                //配置1
                case "配置1":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存成功...");
                    }
                    else
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存失败...");
                    }
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                case "配置2":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存成功...");
                    }
                    else
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存失败...");
                    }
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                case "配置3":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存成功...");
                    }
                    else
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存失败...");
                    }
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                case "配置4":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存成功...");
                    }
                    else
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存失败...");
                    }
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                case "配置5":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存成功...");
                    }
                    else
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存失败...");
                    }
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                case "配置6":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存成功...");
                    }
                    else
                    {
                        ViewModelSet.Main?.ShowHintInfo("示波器配置文档保存失败...");
                    }
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                default:
                    break;
            }           
        }

        //*************************************************************************
        //函数名称：OscilloscopeConfigExport_ForUnloaded
        //函数功能：保存示波器预配置设置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2024.02.29
        //*************************************************************************
        public void OscilloscopeConfigExport_ForUnloaded()
        {
            int iRet = -1;
            OscilloscopePresetModel oscilloscopePresetModel = new OscilloscopePresetModel
            {
                Id = GlobalCurrentInput.SelectedOscilloscopePreset = SelectedOscilloscopePreset,
                SelectedSampleChannel1Index = GlobalCurrentInput.SelectedSampleChannel1Index = SelectedSampleChannel1Index,
                SelectedSampleChannel2Index = GlobalCurrentInput.SelectedSampleChannel2Index = SelectedSampleChannel2Index,
                SelectedSampleChannel3Index = GlobalCurrentInput.SelectedSampleChannel1Index = SelectedSampleChannel3Index,
                SelectedSampleChannel4Index = GlobalCurrentInput.SelectedSampleChannel1Index = SelectedSampleChannel4Index,
                SamplingPeriod = GlobalCurrentInput.SelectedSamplingPeriod = SelectedSamplingPeriod,
                SamplingDuration = GlobalCurrentInput.SelectedSamplingDuration = SelectedSamplingDuration,
                ContinuousSampling = GlobalCurrentInput.SelectedContinuousSampling = SelectedContinuousSampling,
                TriggerClockEdge = GlobalCurrentInput.SelectedTriggerClockEdge = SelectedTriggerClockEdge,
                TriggerChannel = GlobalCurrentInput.SelectedTriggerChannel = SelectedTriggerChannel,
                PreTrigger = GlobalCurrentInput.SelectedPreTrigger = SelectedPreTrigger,
                TriggerLevel = GlobalCurrentInput.TriggerLevel = TriggerLevel
            };
            switch (SelectedOscilloscopePreset)
            {
                //停用
                //case 0:
                //    break;
                //配置1
                case "配置1":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                case "配置2":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                case "配置3":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                case "配置4":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                case "配置5":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                case "配置6":
                    iRet = XmlHelper.EditOscilloscopePresetConfigs(oscilloscopePresetModel);
                    
                    XmlHelper.GetOscilloscopePresetConfigsData();
                    break;
                default:
                    break;
            }
        }

        //*************************************************************************
        //函数名称：OscilloscopeConfigDelete
        //函数功能：示波器预配置设置参数删除
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.26
        //*************************************************************************
        public void OscilloscopeConfigDelete()
        {
            //GlobalCurrentInput.SelectedOscilloscopePresetIndex = SelectedOscilloscopePresetIndex = 0;
            OnSelectedSampleChannelIndexChanged();

            GlobalCurrentInput.SelectedSampleChannel1Index = SelectedSampleChannel1Index = 0;
            OnSelectedSampleChannel1IndexChanged();
        }

        //*************************************************************************
        //函数名称：ParameterAcquisition
        //函数功能：参数采集开启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.24
        //*************************************************************************
        public void ParameterAcquisitionStart()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //判断当前是否有采样任务，系统只能有一个采样任务
                if (AcquisitionInfoSet.IsExistTask)
                {
                    ShowNotification(2010);
                    return;
                }

                //判断当前波形是否画完
                if (!AcquisitionInfoSet.IsDrawingCompleted)
                {
                    WindowSet.clsMainWindow?.ShowNotification(3016);
                    return;
                }

                //判断倍乘输入是否有效
                for (int i = 0; i < OscilloscopeProperty.Count; i++)
                {
                    if (!OthersHelper.IsInputNumber(OscilloscopeProperty[i].Doubling))
                    {
                        OscilloscopeProperty[i].Doubling = "1";
                    }
                }

                //更新示波器采样信息集合里面的List(采样通道，采样数据，采样单位，采样单位换算)
                RefreshAcquisitionList();

                //获取报文内容-已经转换了小端
                iRet = GetTransmittingContent(ref lstTransmittingDataInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(iRet);
                    return;
                }
                   
                //下达采样任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterAcquisition(PageName.OSCILLOSCOPE, TaskName.AssigningAcquisition, lstTransmittingDataInfo);

                //按钮使能
                OthersButtonEnabled = false;
                ViewModelSet.Main.IsUnitExchangedEnabled = false;

                //记录采样参数
                RecordLastSamplingParameter();

                //计算数据重置
                ResetLastWavaCalculate();
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_PARAMETER_ACQUISITION, "ParameterAcquisitionStart", ex);
            }
        }

        //*************************************************************************
        //函数名称：LoopParameterAcquisitionStart
        //函数功能：连续循环参数采集开启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.24
        //*************************************************************************
        public void LoopParameterAcquisitionStart()
        {
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取报文内容-已经转换了小端
            GetTransmittingContent(ref lstTransmittingDataInfo);

            //下达采样任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterAcquisition(PageName.OSCILLOSCOPE, TaskName.AssigningAcquisition, lstTransmittingDataInfo);           
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionStop
        //函数功能：参数采集停止
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.24
        //*************************************************************************
        public void ParameterAcquisitionStop()
        {
            //循环采样停止
            AcquisitionInfoSet.AcquisitionSwitch = false;

            //关闭线程
            OthersHelper.CloseAllThread();

            //连续采集下系统界面允许切换
            ViewModelSet.Main.IsAllPageEnabled = true;

            //清空最后一条历史记录
            OfflineOscilloscope.Last = new OscilloscopeParameterSet();

            //信息提示
            ViewModelSet.Main?.ShowHintInfo("数据采集停止");
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionExport
        //函数功能：参数采集导出
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.01.14&2023.11.11
        //*************************************************************************
        public void ParameterAcquisitionExport()
        {
            int iRet = -1;
            string strFilePath = "";

            try
            {
                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return;
                }

                //信息确认并获取数据
                if (MessageBoxService.ShowMessage(MessageForConfirm("Export"), "请确定", MessageButton.YesNo) == MessageResult.Yes)
                {
                    //写入到离线文件
                    InterfaceEvaluationToFile();

                    //获取文件保存路径
                    iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.Oscilloscope);//由Lilbert于2023.11.11添加默认路径
                    if (iRet == RET.ERROR)
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }
                    else if (iRet == RET.NO_EFFECT)
                    {
                        return;
                    }

                    //更新Excel配置文件
                    iRet = ExcelHelper.WriteIntoExcel_For_Oscilloscope(strFilePath);
                    if (iRet == RET.SUCCEEDED)
                    {
                        ShowNotification(2002);
                    }
                    else
                    {
                        ShowNotification(2003);
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_PARAMETER_ACQUISITION_EXPORT, "ParameterAcquisitionExport", ex);
            }
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionImport
        //函数功能：参数采集导入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.01.14&2023.11.11
        //*************************************************************************
        public void ParameterAcquisitionImport()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                //获取文件路径
                //iRet = ExcelHelper.GetReadPath(ref strFilePath);
                iRet = ExcelHelper.GetReadPath_ForOscilloscope(ref strFilePath);//由Lilbert于2023.11.11添加带有默认路径
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel_For_Oscilloscope(strFilePath);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2001);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    ShowNotification(2000);
                    return;
                }

                //接口赋值
                InterfaceEvaluationFromFile();

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return;
                }

                //信息确认
                if (MessageBoxService.ShowMessage(MessageForConfirm("Import"), "请确定", MessageButton.YesNo) == MessageResult.Yes)
                {
                    //载入数据
                    ViewModelSet.OscilloscopeView?.DisplayOscilloscopeImport();

                    //记录本次采样数据
                    OscilloscopeModel.EvaluateLastWaveDataFromCurrent();

                    //记录采样参数
                    RecordLastSamplingParameter();

                    //计算数据重置
                    ResetLastWavaCalculate();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_PARAMETER_ACQUISITION_EXPORT, "ParameterAcquisitionExport", ex);
            }
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionClear
        //函数功能：波形信息清除
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.06
        //*************************************************************************
        public void ParameterAcquisitionClear()
        {
            ViewModelSet.OscilloscopeView?.ClearOscilloscopeData();

            //波形控制与波形计算初始化
            WaveControl_WaveCalculateInitialize();
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionLast_For_IsHidden
        //函数功能：点击隐藏时示波器自动隐藏相应波形数据
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2021.07.20
        //*************************************************************************
        public void ParameterAcquisitionLast_For_IsHidden()
        {
            int iRet = -1;

            try
            {
                //接口赋值
                iRet = InterfaceEvaluationFromLast();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2013);
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return;
                }

                //信息确认并载入数据
                //if (MessageBoxService.ShowMessage(MessageForConfirm("Last"), "请确定", MessageButton.YesNo) == MessageResult.Yes)
                //{
                ViewModelSet.OscilloscopeView?.DisplayOscilloscopeImport();
                //}
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_PARAMTER_ACQUISITION_LAST, "ParameterAcquisitionLast_For_IsHidden", ex);
            }
        }

        //*************************************************************************
        //函数名称：ParameterAcquisitionLast
        //函数功能：载入上一条波形数据
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.18
        //*************************************************************************
        public void ParameterAcquisitionLast()
        {
            int iRet = -1;

            try
            {
                //接口赋值
                iRet = InterfaceEvaluationFromLast();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2013);
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    return;
                }

                //信息确认并载入数据
                if (MessageBoxService.ShowMessage(MessageForConfirm("Last"), "请确定", MessageButton.YesNo) == MessageResult.Yes)
                {
                    ViewModelSet.OscilloscopeView?.DisplayOscilloscopeImport();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_PARAMTER_ACQUISITION_LAST, "ParameterAcquisitionLast", ex);
            }
        }
       
        //*************************************************************************
        //函数名称：ButtonEnableInitialize
        //函数功能：按钮使能初始化
        //
        //输入参数：NONE
        //         
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.01.13
        //*************************************************************************
        public void ButtonEnableInitialize()
        {
            if (AcquisitionInfoSet.IsExistTask)
            {
                OthersButtonEnabled = false;
            }
            else
            {
                OthersButtonEnabled = true;
            }
        }

        //*************************************************************************
        //函数名称：ChangeWaveControlProperty_For_IsHidden
        //函数功能：波形展示属性控制
        //
        //输入参数：NONE
        //         
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2021.07.20
        //*************************************************************************
        public void ChangeWaveControlProperty_For_IsHidden(OscilloscopePropertySet clsProperty)
        {
            if (clsProperty == null || OscilloscopeProperty == null)
            {
                return;
            }

            if (OscilloscopeProperty.Count == 0)
            {
                return;
            }

            if (clsProperty.ChannelNumber == "通道-1")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[0].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[0].Doubling = "1";
                }

                OscilloscopeProperty[0].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-2")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[1].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[1].Doubling = "1";
                }

                OscilloscopeProperty[1].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-3")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[2].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[2].Doubling = "1";
                }

                OscilloscopeProperty[2].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-4")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[3].Doubling = Convert.ToString(clsProperty.Doubling);
                    ParameterAcquisitionLast_For_IsHidden();  //由Lilbert增加隐藏波形
                }
                else
                {
                    OscilloscopeProperty[3].Doubling = "1";
                }

                OscilloscopeProperty[3].IsHidden = clsProperty.IsHidden;
            }
        }

        //*************************************************************************
        //函数名称：ChangeWaveControlProperty
        //函数功能：波形展示属性控制
        //
        //输入参数：NONE
        //         
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.03.09
        //*************************************************************************
        public void ChangeWaveControlProperty(OscilloscopePropertySet clsProperty)
        {
            if (clsProperty == null || OscilloscopeProperty == null)
            {
                return;
            }

            if (OscilloscopeProperty.Count == 0)
            {
                return;
            }

            if (clsProperty.ChannelNumber == "通道-1")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[0].Doubling = Convert.ToString(clsProperty.Doubling);
                }
                else
                {
                    OscilloscopeProperty[0].Doubling = "1";
                }

                OscilloscopeProperty[0].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-2")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[1].Doubling = Convert.ToString(clsProperty.Doubling);
                }
                else
                {
                    OscilloscopeProperty[1].Doubling = "1";
                }

                OscilloscopeProperty[1].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-3")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[2].Doubling = Convert.ToString(clsProperty.Doubling);
                }
                else
                {
                    OscilloscopeProperty[2].Doubling = "1";
                }

                OscilloscopeProperty[2].IsHidden = clsProperty.IsHidden;
            }

            if (clsProperty.ChannelNumber == "通道-4")
            {
                if (OthersHelper.IsInputNumber(clsProperty.Doubling))
                {
                    OscilloscopeProperty[3].Doubling = Convert.ToString(clsProperty.Doubling);
                }
                else
                {
                    OscilloscopeProperty[3].Doubling = "1";
                }

                OscilloscopeProperty[3].IsHidden = clsProperty.IsHidden;
            }
        }
        #endregion
      
        #region 函数发生器
        //*************************************************************************
        //函数名称：CreateFunctionGenerator
        //函数功能：产生函数
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.02.22&2022.12.01
        //*************************************************************************
        public void CreateFunctionGenerator()
        {
            int iRet = -1;
            FunctionGeneratorSet clsFunctionGeneratorSet = new FunctionGeneratorSet();
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取当前页面名称
                SoftwareStateParameterSet.CurrentPageName = PageName.FUNCTIONGENERATOR;

                //函数发生器赋值
                StartFunctionGeneratorEvaluation(ref clsFunctionGeneratorSet);

                //获取参数详细信息
                dicParameterInfo.Add("Modes Of Operation", clsFunctionGeneratorSet.InnerSourceEffectIndex);
                dicParameterInfo.Add("Function Generater Type", clsFunctionGeneratorSet.InnerSourceTypeIndex);
                dicParameterInfo.Add("Function Generater Frequency", clsFunctionGeneratorSet.InnerSourceFrequency);
                dicParameterInfo.Add("Function Generater Amplitude", clsFunctionGeneratorSet.InnerSourceAmplitude);
                dicParameterInfo.Add("Function Generater Number", clsFunctionGeneratorSet.InnerSourceNumber);

                if (SelectedInnerSourceTypeIndex == "3")
                {
                    dicParameterInfo.Add("Function Generater Slope", clsFunctionGeneratorSet.InnerSourceGradient);
                }
               
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                if (HexHelper.GetStatusWord_HighEightBit(0, Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x604100"))) == "1")//由Lilbert于2022.12.01增加判断状态字
                {
                    ViewModelSet.Main?.ShowHintInfo("伺服正在其他运动中，请先禁能...");
                    return;
                }
                else
                {
                    //下达控制字任务-只有在开启的时候
                    iRet = OthersHelper.WriteControlWordSet(false, -1, TaskName.FunctionGenerator);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(1001);
                        return;
                    }
                    else
                    {
                        if (ControlWordSet.ListValue.Count == 3)//6,7,15
                        {
                            OthersHelper.GetWindowsStartupPosition();
                            if (MessageBoxService.ShowMessage("是否伺服使能...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.No)
                            {
                                return;
                            }
                        }
                    }
                }

                ////下达控制字任务-只有在开启的时候
                //iRet = OthersHelper.WriteControlWordSet(false, -1, TaskName.FunctionGenerator);
                //if (iRet != RET.SUCCEEDED)
                //{
                //    ShowNotification(1001);
                //    return;
                //}
                //else
                //{
                //    if (ControlWordSet.ListValue.Count == 3)//6,7,15
                //    {
                //        OthersHelper.GetWindowsStartupPosition();
                //        if (MessageBoxService.ShowMessage("是否伺服使能...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.No)
                //        {
                //            return;
                //        }
                //    }
                //}
                
                //任务下达
                ViewModelSet.CommunicationSet.SerialPort_DataTransmiting_For_ParameterWrite(PageName.FUNCTIONGENERATOR, TaskName.FunctionGenerator, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FUNCTIONGENERATOR_CREATE, "CreateFunctionGenerator", ex);
            }
        }

        //*************************************************************************
        //函数名称：SaveRAMtoEEPROM
        //函数功能：保存RAM到EEPROM    在三环调试里增加参数下载到EEPROM
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2021.06.16
        //*************************************************************************
        public void SaveRAMtoEEPROM()
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                dicParameterInfo.Add("Save Prm To Eeprom", "1");
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                if (MessageBoxService.ShowMessage("是否保存RAM到EEPROM中...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.Yes)
                {
                    ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);
                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.MAIN, lstParameterInfo[0].Description, lstTransmittingDataInfo);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_SAVE_RAM_TO_EEPROM, "SaveRAMtoEEPROM", ex);
            }
        }

        //*************************************************************************
        //函数名称：ControlFunctionGenerator
        //函数功能：控制函数发生器
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.29
        //*************************************************************************
        public void ControlFunctionGenerator(string strParameter)
        {
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取当前页面名称
                SoftwareStateParameterSet.CurrentPageName = PageName.FUNCTIONGENERATOR;

                //获取参数详细信息
                dicParameterInfo.Add("Function Generater Start", strParameter);
                OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                //任务下达
                ViewModelSet.CommunicationSet.SerialPort_DataTransmiting_For_ParameterWrite(PageName.FUNCTIONGENERATOR, TaskName.FunctionGenerator, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FUNCTIONGENERATOR_CONTROL, "ControlFunctionGenerator", ex);
            }
        }
        #endregion

        #region 三环调试
        //*************************************************************************
        //函数名称：WriteLoopParameter
        //函数功能：环参数写入
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.10&2021.07.05
        //*************************************************************************
        public void WriteLoopParameter(string strMode)
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取参数详细信息
                switch (strMode)
                {
                    case "位置速度环":
                        dicParameterInfo.Add("Speed Loop Gain", SpeedLoopGain);
                        dicParameterInfo.Add("Speed Loop Time Constant", SpeedLoopTimeConstant);
                        dicParameterInfo.Add("Position Loop Gain", PositionLoopGain);
                        dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);//负载惯量比    由Lilbert增加负载惯量比
                        dicParameterInfo.Add("First Trqcmd Filter Time", FirstTrqcmdFilterTime);//第一转矩滤波时间     由Lilbert增加第一转矩滤波时间
                        //dicParameterInfo.Add("Rigidity", Rigidity);//刚性    由Lilbert增加刚性
                        break;
                    case "电流环":
                        dicParameterInfo.Add("Current Loop Gain", CurrentLoopGain);
                        dicParameterInfo.Add("Current Loop Time Constant", CurrentLoopTimeConstant);
                        break;
                    //case "位置环":                                                              //由Lilbert增加位置速度环，去掉位置环
                    //    dicParameterInfo.Add("Position Loop Gain", PositionLoopGain);
                    //    break;
                    default:
                        break;
                }

                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.OSCILLOSCOPE, TaskName.ThreeLoop, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FUNCTIONGENERATOR_WRITE_CURRENT_LOOP_PARAMETER, "WriteCurrentLoopParameter", ex);
            }
        }
        #endregion

        #region 刚性调试
        //*************************************************************************
        //函数名称：LoadRigidity
        //函数功能：刚性参数写入
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2024.04.18
        //*************************************************************************
        public void LoadRigidity(string strMode)
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取参数详细信息
                dicParameterInfo.Add("Rigidity", Rigidity);//刚性    由Lilbert增加刚性
                dicParameterInfo.Add("Rigidly Loaded Switch", "1");//刚性加载开关    由Lilbert增加刚性加载开关


                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.OSCILLOSCOPE, TaskName.Rigidity, lstTransmittingDataInfo);

                Thread.Sleep(500);

                ReadAdjustmentParameter("1");               
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FUNCTIONGENERATOR_WRITE_CURRENT_LOOP_PARAMETER, "LoadRigidity", ex);
            }           
        }
        #endregion

        #region 运动调试
        //*************************************************************************
        //函数名称：StartAction
        //函数功能：运动调试开始
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.26&2022.12.01
        //*************************************************************************
        public void StartAction(string strSelectedActionMode)
        {               
            switch (strSelectedActionMode)
            {
                case "位置模式":
                    //string a = HexHelper.GetStatusWord_HighEightBit(0, Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x604100")));
                    if (HexHelper.GetStatusWord_HighEightBit(0, Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x604100"))) == "1")//由Lilbert于2022.12.01增加状态字判断
                    {
                        ViewModelSet.Main?.ShowHintInfo("伺服正在其他运动中，请先禁能...");                       
                    }
                    else
                    {
                        PositionModeAction(true);
                    }
                    //PositionModeAction(true);
                    break;
                case "速度模式":
                    if (HexHelper.GetStatusWord_HighEightBit(0, Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x604100"))) == "1")
                    {
                        ViewModelSet.Main?.ShowHintInfo("伺服正在其他运动中，请先禁能...");                       
                    }
                    else
                    {
                        SpeedModeAction(true);
                    }
                    //SpeedModeAction(true);
                    break;
                case "转矩模式":
                    if (HexHelper.GetStatusWord_HighEightBit(0, Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x604100"))) == "1")
                    {
                        ViewModelSet.Main?.ShowHintInfo("伺服正在其他运动中，请先禁能...");                        
                    }
                    else
                    {
                        TorqueModeAction(true);
                    }
                    //TorqueModeAction(true);
                    break;
                default:
                    break;
            }
        }

        //*************************************************************************
        //函数名称：StopAction
        //函数功能：运动调试结束
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.26
        //*************************************************************************
        public void StopAction(string strSelectedActionMode)
        {
            switch (strSelectedActionMode)
            {
                case "位置模式":
                    PositionModeAction(false);
                    break;
                case "速度模式":
                    SpeedModeAction(false);
                    break;
                case "转矩模式":
                    TorqueModeAction(false);
                    break;
                default:
                    break;
            }
        }
        #endregion

        #region 参数调优调试
        //*************************************************************************
        //函数名称：StartParameterTunningAction
        //函数功能：参数调优调试开始
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.22
        //*************************************************************************
        public void StartParameterTunningAction(string strSelectedParameterTunningMode)
        {
            switch (strSelectedParameterTunningMode)
            {
                case "位置模式":
                    if (OthersHelper.GetCurrentValueOfIndex("0x603F00") != "0")
                    {
                        ViewModelSet.Main?.ShowHintInfo("伺服故障，请先清除故障...");
                    }
                    else
                    {
                        PositionModeParameterTunning(true);
                    }
                    break;
                case "速度模式":
                    if (OthersHelper.GetCurrentValueOfIndex("0x603F00") != "0")
                    {
                        ViewModelSet.Main?.ShowHintInfo("伺服故障，请先清除故障...");
                    }
                    else
                    {
                        SpeedModeParameterTunning(true);
                    }
                    break;
                //case "转矩模式":
                //    TorqueModeAction(true);
                //    break;
                default:
                    break;
            }
        }

        //*************************************************************************
        //函数名称：StopParameterTunningAction
        //函数功能：参数调优调试结束
        //
        //输入参数：None
        //
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.22
        //*************************************************************************
        public void StopParameterTunningAction(string strSelectedParameterTunningMode)
        {
            switch (strSelectedParameterTunningMode)
            {
                case "位置模式":
                    PositionModeParameterTunning(false);
                    break;
                case "速度模式":
                    SpeedModeParameterTunning(false);
                    break;
                //case "转矩模式":
                //    TorqueModeAction(false);
                //    break;
                default:
                    break;
            }
        }
        #endregion

        #region 其他

        //*************************************************************************
        //函数名称：ReadAdjustmentParameter
        //函数功能：读发生器与三环参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.02&2022.11.22
        //*************************************************************************
        public void ReadAdjustmentParameter(string strCategory)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.OSCILLOSCOPE, TaskName.FunctionGenerator + TaskName.Rigidity + TaskName.ThreeLoop + TaskName.Action + TaskName.ParameterTunning, lstTransmittingDataInfo);//由Lilbert添加手动参数调优
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_ADJUSTMENT_PARAMETER, "ReadAdjustmentParameter", ex);
            }
        }       

        //*************************************************************************
        //函数名称：GetDefaultAdjustmentParameter
        //函数功能：获取调试默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.11&2021.07.15&2022.11.08
        //*************************************************************************
        public void GetDefaultAdjustmentParameter(string strCategory)
        {
            try
            {
                //获取默认单位
                OthersHelper.GetCurrentUnit(bDefault: true);
                PositionUnit = DefaultUnit.PositionUnit;
                TorqueUnit = DefaultUnit.TorqueUnit;
                SpeedUnit = DefaultUnit.SpeedUnit;
                AccelerationUnit = DefaultUnit.AccelerationUnit;

                //获取配置文件数值
                switch (strCategory)
                {
                    case "0":
                        break;
                    case "1":
                        PositionLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Default");//位置环增益
                        LoadInertiaRatio = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Default");//负载惯量比    由Lilbert增加负载惯量比
                        FirstTrqcmdFilterTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Default");//第一转矩滤波时间    由Lilbert增加第一转矩滤波时间
                        SpeedLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Default");//速度环增益          
                        SpeedLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Default");//速度环积分时间常数
                        CurrentLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Gain", "Default");//电流环增益
                        CurrentLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Time Constant", "Default");//电流环积分时间常数
                        Rigidity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Rigidity", "Default");//刚性    由Lilbert增加刚性
                        break;
                    case "2":
                        OnSelectedInnerSourceEffectIndexChanged();
                        InnerSourceFrequency = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Frequency", "Default");
                        InnerSourceAmplitude = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Amplitude", "Default");
                        InnerSourceNumber = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Number", "Default");
                        InnerSourceGradient = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Slope", "Default");
                        break;
                    case "3":
                        TargetPosition = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Position", "Default");
                        ProfileVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Velocity", "Default");
                        EndProfileVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Profile Velocity", "Default");
                        ProfileAcceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Acceleration", "Default");
                        ProfileDeceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Deceleration", "Default");

                        TargetVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Velocity", "Default");
                        ProfileAcceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Acceleration", "Default");
                        ProfileDeceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Deceleration", "Default");
                        ProfileJerk1 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 1", "Default");
                        ProfileJerk2 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 2", "Default");
                        ProfileJerk3 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 3", "Default");
                        ProfileJerk4 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 4", "Default");

                        TargetTorque = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Torque", "Default");
                        TorqueSlope = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Torque Slope", "Default");
                        break;
                    case "4":
                        ParameterTunningJogTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Time", "Index"));//由Lilbert增加Jog运动时间
                        ParameterTunningJogSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Speed", "Index"));//由Lilbert增加Jog运动速度
                        ParameterTunningJogAccelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Acceleration Time", "Index"));//由Lilbert增加Jog加速时间
                        ParameterTunningJogDecelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Deceleration Time", "Index"));//由Lilbert增加Jog减速时间

                        ParameterTunningProgramJogMovingDistance = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Distance", "Default");//由Lilbert增加程序Jog移动距离
                        ParameterTunningProgramJogMovingSpeed = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Speed", "Default");//由Lilbert增加程序Jog移动速度
                        ParameterTunningProgramJogAccDecTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog AccDec Time", "Default");//由Lilbert增加程序Jog加减速时间
                        ParameterTunningProgramJogWaitTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Wait Time", "Default");//由Lilbert增加程序Jog等待时间
                        ParameterTunningProgramJogMovingNumber = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Number", "Default");//由Lilbert增加程序Jog移动次数
                        break;
                    default:
                        PositionLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Default");//位置环增益
                        LoadInertiaRatio = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Default");//负载惯量比    由Lilbert增加负载惯量比
                        FirstTrqcmdFilterTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Default");//第一转矩滤波时间    由Lilbert增加第一转矩滤波时间
                        SpeedLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Default");//速度环增益          
                        SpeedLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Default");//速度环积分时间常数
                        CurrentLoopGain = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Gain", "Default");//电流环增益
                        CurrentLoopTimeConstant = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Time Constant", "Default");//电流环积分时间常数
                        Rigidity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Rigidity", "Default");//刚性    由Lilbert增加刚性

                        InnerSourceFrequency = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Frequency", "Default");
                        InnerSourceAmplitude = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Amplitude", "Default");
                        InnerSourceNumber = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Number", "Default");
                        InnerSourceGradient = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Slope", "Default");

                        TargetPosition = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Position", "Default");
                        ProfileVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Velocity", "Default");
                        EndProfileVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Profile Velocity", "Default");
                        ProfileAcceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Acceleration", "Default");
                        ProfileDeceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Deceleration", "Default");

                        TargetVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Velocity", "Default");
                        ProfileAcceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Acceleration", "Default");
                        ProfileDeceleration = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Deceleration", "Default");
                        ProfileJerk1 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 1", "Default");
                        ProfileJerk2 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 2", "Default");
                        ProfileJerk3 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 3", "Default");
                        ProfileJerk4 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 4", "Default");

                        TargetTorque = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Torque", "Default");
                        TorqueSlope = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Torque Slope", "Default");

                        ParameterTunningJogTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Time", "Index"));//由Lilbert增加Jog运动时间
                        ParameterTunningJogSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Speed", "Index"));//由Lilbert增加Jog运动速度
                        ParameterTunningJogAccelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Acceleration Time", "Index"));//由Lilbert增加Jog加速时间
                        ParameterTunningJogDecelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Deceleration Time", "Index"));//由Lilbert增加Jog减速时间

                        ParameterTunningProgramJogMovingDistance = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Distance", "Default");//由Lilbert增加程序Jog移动距离
                        ParameterTunningProgramJogMovingSpeed = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Speed", "Default");//由Lilbert增加程序Jog移动速度
                        ParameterTunningProgramJogAccDecTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog AccDec Time", "Default");//由Lilbert增加程序Jog加减速时间
                        ParameterTunningProgramJogWaitTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Wait Time", "Default");//由Lilbert增加程序Jog等待时间
                        ParameterTunningProgramJogMovingNumber = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Number", "Default");//由Lilbert增加程序Jog移动次数
                        
                        break;
                }              
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_GET_DEFAULT_ADJUSTMENT_PARAMETER, "GetDefaultAdjustmentParameter", ex);
            }
        }       

        //*************************************************************************
        //函数名称：EvalutionAdjustmentParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.11&2021.07.15&2022.11.25
        //*************************************************************************
        public void EvalutionAdjustmentParameter()
        {
            //获取当前的单位
            OthersHelper.GetCurrentUnit(bDefault: false);
            PositionUnit = CurrentUnit.Position;
            TorqueUnit = CurrentUnit.Torque;
            SpeedUnit = CurrentUnit.Speed;
            AccelerationUnit = CurrentUnit.Acceleration;

            //参数赋值
            if (IsEvaluationAll)
            {
                //标志位更新
                IsEvaluationAll = false;

                #region 三环
                PositionLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//额定功率   
                LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//负载惯量比    由Lilbert增减负载惯量比
                FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩滤波时间     由Lilbert增加第一转矩滤波时间         
                SpeedLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//额定电压
                SpeedLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//时间常数
                CurrentLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Gain", "Index"));//额定转矩
                CurrentLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Time Constant", "Index"));//时间常数
                Rigidity = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Rigidity", "Index"));//刚性    由Lilbert增加刚性
                #endregion

                #region 函数发生器
                OnSelectedInnerSourceEffectIndexChanged();
                if (InnerSourceNumberEnabled)
                {
                    InnerSourceNumber = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Number", "Index"));
                }
                else
                {
                    InnerSourceNumber = "1";
                }

                if (SelectedInnerSourceEffectIndex == "0" )
                {
                    InnerSourceAmplitude = OthersHelper.ExchangeUnit("Function Generater Amplitude", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Amplitude", "Index")));
                    InnerSourceGradient = OthersHelper.ExchangeUnit("Function Generater Slope", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Slope", "Index")));
                }
                else if (SelectedInnerSourceEffectIndex == "1")
                {
                    InnerSourceAmplitude = OthersHelper.ExchangeUnit("Function Generater Amplitude", DefaultUnit.SpeedUnit + "-" + SpeedUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Amplitude", "Index")));
                    InnerSourceGradient = OthersHelper.ExchangeUnit("Function Generater Slope", DefaultUnit.SpeedUnit + "-" + SpeedUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Slope", "Index")));
                }
                else if (SelectedInnerSourceEffectIndex == "2")
                {
                    InnerSourceAmplitude = OthersHelper.ExchangeUnit("Function Generater Amplitude", DefaultUnit.TorqueUnit + "-" + TorqueUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Amplitude", "Index")));
                    InnerSourceGradient = OthersHelper.ExchangeUnit("Function Generater Slope", DefaultUnit.TorqueUnit + "-" + TorqueUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Slope", "Index")));

                }

                InnerSourceFrequency = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Frequency", "Index"));
                #endregion

                #region 运动调试
                TargetPosition = OthersHelper.ExchangeUnit("Target Position", DefaultUnit.PositionUnit + "-" + CurrentUnit.Position, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Position", "Index")));//目标位置                      
                TargetTorque = OthersHelper.ExchangeUnit("Target Torque", DefaultUnit.TorqueUnit + "-" + CurrentUnit.Torque, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Torque", "Index")));//目标转矩
                TorqueSlope = OthersHelper.ExchangeUnit("Torque Slope", DefaultUnit.TorqueUnit + "-" + CurrentUnit.Torque, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Torque Slope", "Index")));//转矩斜坡
                              
                ProfileVelocity = OthersHelper.ExchangeUnit("Profile Velocity", DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Velocity", "Index")));//轮廓运行速度
                EndProfileVelocity = OthersHelper.ExchangeUnit("End Profile Velocity", DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Profile Velocity", "Index")));//轮廓结尾速度
                TargetVelocity = OthersHelper.ExchangeUnit("Target Velocity", DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Velocity", "Index")));//目标速度        
             
                ProfileAcceleration = OthersHelper.ExchangeUnit("Profile Acceleration", DefaultUnit.AccelerationUnit + "-" + CurrentUnit.Acceleration, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Acceleration", "Index")));//轮廓加速度
                ProfileDeceleration = OthersHelper.ExchangeUnit("Profile Deceleration", DefaultUnit.AccelerationUnit + "-" + CurrentUnit.Acceleration, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Deceleration", "Index")));//轮廓减速度
                          
                ProfileJerk1 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 1", "Index"));//轮廓加加速度1
                ProfileJerk2 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 2", "Index"));//轮廓加加速度2
                ProfileJerk3 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 3", "Index"));//轮廓加加速度3
                ProfileJerk4 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 4", "Index"));//轮廓加加速度4 
                #endregion

                #region 参数调优调试
                ParameterTunningJogSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Speed", "Index"));
                ParameterTunningJogAccelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Acceleration Time", "Index"));
                ParameterTunningJogDecelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Deceleration Time", "Index"));
                ParameterTunningJogTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Time", "Index"));

                ParameterTunningProgramJogMovingDistance = OthersHelper.ExchangeUnit("Program Jog Moving Distance", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Distance", "Index")));//程序PROGRAMJOG移动距离
                ParameterTunningProgramJogMovingSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Speed", "Index"));//程序PROGRAMJOG移动速度

                ParameterTunningProgramJogAccDecTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog AccDec Time", "Index"));//程序PROGRAMJOG加减速时间
                ParameterTunningProgramJogWaitTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Wait Time", "Index"));//程序PROGRAMJOG等待时间
                ParameterTunningProgramJogMovingNumber = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Number", "Index"));//程序PROGRAMJOG移动次数  
                #endregion
            }
            else
            {
                if (SelectedTabIndex == TabControlIndex.ThreeLoop)
                { 
                    #region 三环
                    PositionLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Position Loop Gain", "Index"));//额定功率   
                    LoadInertiaRatio = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Load Inertia Ratio", "Index"));//负载惯量比    由Lilbert增加负载惯量比
                    FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩滤波时间     由Lilbert增加第一转矩滤波时间         
                    SpeedLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Gain", "Index"));//额定电压
                    SpeedLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Speed Loop Time Constant", "Index"));//时间常数
                    CurrentLoopGain = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Gain", "Index"));//额定转矩
                    CurrentLoopTimeConstant = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Current Loop Time Constant", "Index"));//时间常数
                    Rigidity = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Rigidity", "Index"));//刚性    由Lilbert增加刚性
                    #endregion
                }
                else if (SelectedTabIndex == TabControlIndex.FunctionGenerator)
                {
                    #region 函数发生器
                    OnSelectedInnerSourceEffectIndexChanged();
                    if (InnerSourceNumberEnabled)
                    {
                        InnerSourceNumber = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Number", "Index"));
                    }
                    else
                    {
                        InnerSourceNumber = "1";
                    }

                    if (SelectedInnerSourceEffectIndex == "0")
                    {
                        InnerSourceAmplitude = OthersHelper.ExchangeUnit("Function Generater Amplitude", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Amplitude", "Index")));
                        InnerSourceGradient = OthersHelper.ExchangeUnit("Function Generater Slope", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Slope", "Index")));
                    }
                    else if (SelectedInnerSourceEffectIndex == "1")
                    {
                        InnerSourceAmplitude = OthersHelper.ExchangeUnit("Function Generater Amplitude", DefaultUnit.SpeedUnit + "-" + SpeedUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Amplitude", "Index")));
                        InnerSourceGradient = OthersHelper.ExchangeUnit("Function Generater Slope", DefaultUnit.SpeedUnit + "-" + SpeedUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Slope", "Index")));
                    }
                    else if (SelectedInnerSourceEffectIndex == "2")
                    {
                        InnerSourceAmplitude = OthersHelper.ExchangeUnit("Function Generater Amplitude", DefaultUnit.TorqueUnit + "-" + TorqueUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Amplitude", "Index")));
                        InnerSourceGradient = OthersHelper.ExchangeUnit("Function Generater Slope", DefaultUnit.TorqueUnit + "-" + TorqueUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Slope", "Index")));
                    }

                    InnerSourceFrequency = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Function Generater Frequency", "Index"));
                    #endregion
                }
                else if (SelectedTabIndex == TabControlIndex.Action)
                {                
                    #region 运动调试
                    TargetPosition = OthersHelper.ExchangeUnit("Target Position", DefaultUnit.PositionUnit + "-" + CurrentUnit.Position, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Position", "Index")));//目标位置                      
                    TargetTorque = OthersHelper.ExchangeUnit("Target Torque", DefaultUnit.TorqueUnit + "-" + CurrentUnit.Torque, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Torque", "Index")));//目标转矩
                    TorqueSlope = OthersHelper.ExchangeUnit("Torque Slope", DefaultUnit.TorqueUnit + "-" + CurrentUnit.Torque, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Torque Slope", "Index")));//转矩斜坡

                    ProfileVelocity = OthersHelper.ExchangeUnit("Profile Velocity", DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Velocity", "Index")));//轮廓运行速度
                    EndProfileVelocity = OthersHelper.ExchangeUnit("End Profile Velocity", DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "End Profile Velocity", "Index")));//轮廓结尾速度
                    TargetVelocity = OthersHelper.ExchangeUnit("Target Velocity", DefaultUnit.SpeedUnit + "-" + CurrentUnit.Speed, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Target Velocity", "Index")));//目标速度        

                    ProfileAcceleration = OthersHelper.ExchangeUnit("Profile Acceleration", DefaultUnit.AccelerationUnit + "-" + CurrentUnit.Acceleration, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Acceleration", "Index")));//轮廓加速度
                    ProfileDeceleration = OthersHelper.ExchangeUnit("Profile Deceleration", DefaultUnit.AccelerationUnit + "-" + CurrentUnit.Acceleration, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Deceleration", "Index")));//轮廓减速度

                    ProfileJerk1 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 1", "Index"));//轮廓加加速度1
                    ProfileJerk2 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 2", "Index"));//轮廓加加速度2
                    ProfileJerk3 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 3", "Index"));//轮廓加加速度3
                    ProfileJerk4 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Profile Jerk 4", "Index"));//轮廓加加速度4 
                    #endregion
                }
                else if (SelectedTabIndex == TabControlIndex.ParameterTunning)
                {                  
                    #region 参数调优调试
                    ParameterTunningJogSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Speed", "Index"));
                    ParameterTunningJogAccelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Acceleration Time", "Index"));
                    ParameterTunningJogDecelerationTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Deceleration Time", "Index"));
                    ParameterTunningJogTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Jog Time", "Index"));

                    ParameterTunningProgramJogMovingDistance = OthersHelper.ExchangeUnit("Program Jog Moving Distance", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Distance", "Index")));//程序PROGRAMJOG移动距离
                    ParameterTunningProgramJogMovingSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Speed", "Index"));//程序PROGRAMJOG移动速度

                    ParameterTunningProgramJogAccDecTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog AccDec Time", "Index"));//程序PROGRAMJOG加减速时间
                    ParameterTunningProgramJogWaitTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Wait Time", "Index"));//程序PROGRAMJOG等待时间
                    ParameterTunningProgramJogMovingNumber = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Number", "Index"));//程序PROGRAMJOG移动次数 
                    #endregion
                }
            }          
        }

        //*************************************************************************
        //函数名称：ExpandDisplay
        //函数功能：扩大波形展示界面
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.05.27
        //*************************************************************************
        public void ExpandDisplay()
        {
            if (ViewModelSet.Main != null)
            {
                ViewModelSet.Main.IsRibbonMinimized = true;
            }
        }

        //*************************************************************************
        //函数名称：NormalDisplay
        //函数功能：正常波形展示界面
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.05.27
        //*************************************************************************
        public void NormalDisplay()
        {
            if (ViewModelSet.Main != null)
            {
                ViewModelSet.Main.IsRibbonMinimized = false;
            }
        }

        //*************************************************************************
        //函数名称：LegendDisplay
        //函数功能：运动模式图例显示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.28
        //*************************************************************************
        public void LegendDisplay(string strActionName)
        {                  
            if (!OthersHelper.GetWindowsStartupPosition())
            {
                return;
            }

            UICommand cancelCommand = new UICommand()
            {
                Caption = "返回",
            };

            if (strActionName == "位置模式")
            {
                UICommand result = DialogService_PositionLegend.ShowDialog(
                dialogCommands: new List<UICommand>() { cancelCommand },
                title: strActionMode + "运动图示",
                viewModel: null);
            }
            else if (strActionName == "速度模式")
            {
                if (strActionMode == "速度模式-斜坡曲线")
                {
                    UICommand result = DialogService_SlopeLegend.ShowDialog(
                    dialogCommands: new List<UICommand>() { cancelCommand },
                    title: strActionMode + "运动图示",
                    viewModel: null);
                }
                else
                {
                    UICommand result = DialogService_JerkFreeLegend.ShowDialog(
                    dialogCommands: new List<UICommand>() { cancelCommand },
                     title: strActionMode + "运动图示",
                    viewModel: null);
                }
            }
            else
            {
                UICommand result = DialogService_TorqueLegend.ShowDialog(
                dialogCommands: new List<UICommand>() { cancelCommand },
                title: strActionMode + "图示",
                viewModel: null);
            }            
        }

        //*************************************************************************
        //函数名称：FunctionLegendDisplay
        //函数功能：函数发生器图例显示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.29
        //*************************************************************************
        public void FunctionLegendDisplay(int iFunctionType)
        {
            if (!OthersHelper.GetWindowsStartupPosition())
            {
                return;
            }

            UICommand cancelCommand = new UICommand()
            {
                Caption = "返回",
            };

            if (iFunctionType == 0)
            {
                UICommand result = DialogService_SquareLegend.ShowDialog(
                dialogCommands: new List<UICommand>() { cancelCommand },
                title: "方波图示",
                viewModel: null);
            }
            else if (iFunctionType == 1)
            {
                UICommand result = DialogService_SinLegend.ShowDialog(
                 dialogCommands: new List<UICommand>() { cancelCommand },
                  title: "正弦波图示",
                  viewModel: null);
            }
            else if (iFunctionType == 2)
            {
                UICommand result = DialogService_StepLegend.ShowDialog(
                 dialogCommands: new List<UICommand>() { cancelCommand },
                 title: "阶跃图示",
                  viewModel: null);
            }
            else
            {
                UICommand result = DialogService_SlopeAscLegend.ShowDialog(
                dialogCommands: new List<UICommand>() { cancelCommand },
                 title: "线性上升图示",
                 viewModel: null);
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.18&2022.11.25
        //*************************************************************************
        //示波器
        public void OnSelectedTabIndexChanged()
        {
            int iRet = -1;

            switch (SelectedTabIndex)
            {
                case 0:
                    SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.Calculate;
                    SoftwareStateParameterSet.CurrentPageName = PageName.OSCILLOSCOPE;
                    break;
                case 1:
                    SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.ThreeLoop;
                    SoftwareStateParameterSet.CurrentPageName = PageName.OSCILLOSCOPE;
                    break;
                case 2:
                    SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.FunctionGenerator;
                    SoftwareStateParameterSet.CurrentPageName = PageName.FUNCTIONGENERATOR;
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadAdjustmentParameter("2");
                    }
                    else
                    {
                        GetDefaultAdjustmentParameter("2");
                    }
                    break;
                case 3:                   
                    SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.Action;
                    SoftwareStateParameterSet.CurrentPageName = PageName.ACTIONDEBUG;
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadAdjustmentParameter("3");
                    }
                    else
                    {
                        GetDefaultAdjustmentParameter("3");
                    }
                    break;
                case 4:
                    SoftwareStateParameterSet.OscilloscopeTabControlIndex = TabControlIndex.ParameterTunning;
                    SoftwareStateParameterSet.CurrentPageName = PageName.PARAMETERTUNNING;
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadAdjustmentParameter("4");
                    }
                    else
                    {
                        GetDefaultAdjustmentParameter("4");
                    }
                    break;
                default:
                    break;
            }         
        }
        public void OnSelectedSamplingPeriodChanged()
        {
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDuration);
            GlobalCurrentInput.SelectedSamplingPeriod = SelectedSamplingPeriod;
        }
        public void OnSelectedSamplingDurationChanged()
        {
            GlobalCurrentInput.SelectedSamplingDuration = SelectedSamplingDuration;
            GetSamplingDurationUnit(SelectedSamplingDuration);

            if (!IsLoadedAgain)
            {
                GlobalCurrentInput.SamplingDurationIndex = SamplingDuration.FindIndex(item => item == SelectedSamplingDuration);
            }
        }
        public void OnSelectedSamplingDurationIndexChanged()
        {
            if (SelectedSamplingDurationIndex >= 5)
            {
                ViewModelSet.Main?.ShowHintInfo("采样量较大不能设置连续采样");      
                ContinuousAcquisitionEnabled = false;
                SelectedContinuousSampling = "否";
            }
            else
            {
                ContinuousAcquisitionEnabled = true;
            }
        }
        public void OnSelectedContinuousSamplingChanged()
        {
            GlobalCurrentInput.SelectedContinuousSampling = SelectedContinuousSampling;
        }
        public void OnSelectedSampleChannel1IndexChanged()
        {
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDuration);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel1Index = SelectedSampleChannel1Index;

            //获取采样名称
            SelectedSamplingChannel1 = GetSampleNameByIndex(SelectedSampleChannel1Index);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent();
        }

        public void OnSelectedSampleChannel2IndexChanged()
        {          
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDuration);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel2Index = SelectedSampleChannel2Index;

            //获取采样名称
            SelectedSamplingChannel2 = GetSampleNameByIndex(SelectedSampleChannel2Index);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent();
        }
        public void OnSelectedSampleChannel3IndexChanged()
        {           
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDuration);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel3Index = SelectedSampleChannel3Index;

            //获取采样名称
            SelectedSamplingChannel3 = GetSampleNameByIndex(SelectedSampleChannel3Index);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent();
        }
        public void OnSelectedSampleChannel4IndexChanged()
        {           
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDuration);

            //保存到全局
            GlobalCurrentInput.SelectedSampleChannel4Index = SelectedSampleChannel4Index;

            //获取采样名称
            SelectedSamplingChannel4 = GetSampleNameByIndex(SelectedSampleChannel4Index);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            RefreshSampleChanelState_TriggerChannelContent();
        }
        public void OnSelectedSampleChannelIndexChanged()
        {
            //更新采样时长
            RefreshSamplingDuration(GlobalCurrentInput.SelectedSamplingDuration);

            //保存到全局
            //GlobalCurrentInput.SelectedSampleChannel4Index = SelectedSampleChannel4Index;

            //获取采样名称
            SelectedSamplingChannel1 = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel1Index);
            SelectedSamplingChannel2 = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel2Index);
            SelectedSamplingChannel3 = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel3Index);
            SelectedSamplingChannel4 = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel4Index);

            //更新触发通道下拉列表和采集通道下拉列表使能状态
            //RefreshSampleChanelState_TriggerChannelContent();
            RefreshSampleChanelState_TriggerChannelContent(0);
            //OnSelectedTriggerChannelChanged();

        }

        //*************************************************************************
        //函数名称：GetOscilloscopePresetData
        //函数功能：获取示波器预配置文件信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.10
        //*************************************************************************
        private void GetOscilloscopePresetData()
        {
            List<OscilloscopePresetModel> oscilloscopePresetModelList = new List<OscilloscopePresetModel>();

            ConfigOscilloscopePreset.OscilloscopePresetConfigs = oscilloscopePresetModelList = XmlHelper.GetOscilloscopePresetConfigsData();
        }

        public void GetOscilloscopePreset(string selectedOscilloscopePreset)
        {
            //List<OscilloscopePresetModel> oscilloscopePresetModel = new List<OscilloscopePresetModel>();
            //ConfigOscilloscopePreset.OscilloscopePresetConfigs = oscilloscopePresetModel = XmlHelper.GetOscilloscopePresetConfigsData();

            switch (selectedOscilloscopePreset)
            {
                //case 0:
                //    GlobalCurrentInput.SelectedSampleChannel1Index = SelectedSampleChannel1Index = 0;
                //    OnSelectedSampleChannel1IndexChanged();
                //    break;
                case "配置1":
                    if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Count != 0)
                    {
                        SelectedSampleChannel1Index = GlobalCurrentInput.SelectedSampleChannel1Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel1Index).ToList()[0];
                        SelectedSampleChannel2Index = GlobalCurrentInput.SelectedSampleChannel2Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel2Index).ToList()[0];
                        SelectedSampleChannel3Index = GlobalCurrentInput.SelectedSampleChannel3Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel3Index).ToList()[0];
                        SelectedSampleChannel4Index = GlobalCurrentInput.SelectedSampleChannel4Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel4Index).ToList()[0];
                        SelectedSamplingPeriod = GlobalCurrentInput.SelectedSamplingPeriod = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingPeriod).ToList()[0];
                        SelectedSamplingDuration = GlobalCurrentInput.SelectedSamplingDuration = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingDuration).ToList()[0];
                        SelectedContinuousSampling = GlobalCurrentInput.SelectedContinuousSampling = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.ContinuousSampling).ToList()[0];
                        SelectedTriggerClockEdge = GlobalCurrentInput.SelectedTriggerClockEdge = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerClockEdge).ToList()[0];
                        SelectedTriggerChannel = GlobalCurrentInput.SelectedTriggerChannel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerChannel).ToList()[0];
                        if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[0] == "0")
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[0];
                        }
                        else
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[0] + "%";
                        }
                        TriggerLevel = GlobalCurrentInput.TriggerLevel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerLevel).ToList()[0];
                        //OnSelectedSampleChannelIndexChanged();
                    }
                    RefreshSampleChanelState_TriggerChannelContent();
                    break;
                case "配置2":
                    if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Count != 0)
                    {
                        SelectedSampleChannel1Index = GlobalCurrentInput.SelectedSampleChannel1Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel1Index).ToList()[1];
                        SelectedSampleChannel2Index = GlobalCurrentInput.SelectedSampleChannel2Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel2Index).ToList()[1];
                        SelectedSampleChannel3Index = GlobalCurrentInput.SelectedSampleChannel3Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel3Index).ToList()[1];
                        SelectedSampleChannel4Index = GlobalCurrentInput.SelectedSampleChannel4Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel4Index).ToList()[1];
                        SelectedSamplingPeriod = GlobalCurrentInput.SelectedSamplingPeriod = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingPeriod).ToList()[1];
                        SelectedSamplingDuration = GlobalCurrentInput.SelectedSamplingDuration = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingDuration).ToList()[1];
                        SelectedContinuousSampling = GlobalCurrentInput.SelectedContinuousSampling = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.ContinuousSampling).ToList()[1];
                        SelectedTriggerClockEdge = GlobalCurrentInput.SelectedTriggerClockEdge = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerClockEdge).ToList()[1];
                        SelectedTriggerChannel = GlobalCurrentInput.SelectedTriggerChannel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerChannel).ToList()[1];
                        if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[1] == "0")
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[1];
                        }
                        else
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[1] + "%";
                        }
                        TriggerLevel = GlobalCurrentInput.TriggerLevel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerLevel).ToList()[1];
                        //OnSelectedSampleChannelIndexChanged();
                    }
                    RefreshSampleChanelState_TriggerChannelContent();
                    break;
                case "配置3":
                    if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Count != 0)
                    {
                        SelectedSampleChannel1Index = GlobalCurrentInput.SelectedSampleChannel1Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel1Index).ToList()[2];
                        SelectedSampleChannel2Index = GlobalCurrentInput.SelectedSampleChannel2Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel2Index).ToList()[2];
                        SelectedSampleChannel3Index = GlobalCurrentInput.SelectedSampleChannel3Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel3Index).ToList()[2];
                        SelectedSampleChannel4Index = GlobalCurrentInput.SelectedSampleChannel4Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel4Index).ToList()[2];
                        SelectedSamplingPeriod = GlobalCurrentInput.SelectedSamplingPeriod = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingPeriod).ToList()[2];
                        SelectedSamplingDuration = GlobalCurrentInput.SelectedSamplingDuration = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingDuration).ToList()[2];
                        SelectedContinuousSampling = GlobalCurrentInput.SelectedContinuousSampling = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.ContinuousSampling).ToList()[2];
                        SelectedTriggerClockEdge = GlobalCurrentInput.SelectedTriggerClockEdge = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerClockEdge).ToList()[2];
                        SelectedTriggerChannel = GlobalCurrentInput.SelectedTriggerChannel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerChannel).ToList()[2];
                        if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[2] == "0")
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[2];
                        }
                        else
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[2] + "%";
                        }
                        TriggerLevel = GlobalCurrentInput.TriggerLevel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerLevel).ToList()[2];
                        //OnSelectedSampleChannelIndexChanged();
                    }
                    RefreshSampleChanelState_TriggerChannelContent();
                    break;
                case "配置4":
                    if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Count != 0)
                    {
                        SelectedSampleChannel1Index = GlobalCurrentInput.SelectedSampleChannel1Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel1Index).ToList()[3];
                        SelectedSampleChannel2Index = GlobalCurrentInput.SelectedSampleChannel2Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel2Index).ToList()[3];
                        SelectedSampleChannel3Index = GlobalCurrentInput.SelectedSampleChannel3Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel3Index).ToList()[3];
                        SelectedSampleChannel4Index = GlobalCurrentInput.SelectedSampleChannel4Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel4Index).ToList()[3];
                        SelectedSamplingPeriod = GlobalCurrentInput.SelectedSamplingPeriod = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingPeriod).ToList()[3];
                        SelectedSamplingDuration = GlobalCurrentInput.SelectedSamplingDuration = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingDuration).ToList()[3];
                        SelectedContinuousSampling = GlobalCurrentInput.SelectedContinuousSampling = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.ContinuousSampling).ToList()[3];
                        SelectedTriggerClockEdge = GlobalCurrentInput.SelectedTriggerClockEdge = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerClockEdge).ToList()[3];
                        SelectedTriggerChannel = GlobalCurrentInput.SelectedTriggerChannel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerChannel).ToList()[3];
                        if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[3] == "0")
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[3];
                        }
                        else
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[3] + "%";
                        }
                        TriggerLevel = GlobalCurrentInput.TriggerLevel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerLevel).ToList()[3];
                        //OnSelectedSampleChannelIndexChanged();
                    }
                    RefreshSampleChanelState_TriggerChannelContent();
                    break;
                case "配置5":
                    if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Count != 0)
                    {
                        SelectedSampleChannel1Index = GlobalCurrentInput.SelectedSampleChannel1Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel1Index).ToList()[4];
                        SelectedSampleChannel2Index = GlobalCurrentInput.SelectedSampleChannel2Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel2Index).ToList()[4];
                        SelectedSampleChannel3Index = GlobalCurrentInput.SelectedSampleChannel3Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel3Index).ToList()[4];
                        SelectedSampleChannel4Index = GlobalCurrentInput.SelectedSampleChannel4Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel4Index).ToList()[4];
                        SelectedSamplingPeriod = GlobalCurrentInput.SelectedSamplingPeriod = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingPeriod).ToList()[4];
                        SelectedSamplingDuration = GlobalCurrentInput.SelectedSamplingDuration = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingDuration).ToList()[4];
                        SelectedContinuousSampling = GlobalCurrentInput.SelectedContinuousSampling = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.ContinuousSampling).ToList()[4];
                        SelectedTriggerClockEdge = GlobalCurrentInput.SelectedTriggerClockEdge = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerClockEdge).ToList()[4];
                        SelectedTriggerChannel = GlobalCurrentInput.SelectedTriggerChannel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerChannel).ToList()[4];
                        if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[4] == "0")
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[4];
                        }
                        else
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[4] + "%";
                        }
                        TriggerLevel = GlobalCurrentInput.TriggerLevel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerLevel).ToList()[4];
                        //OnSelectedSampleChannelIndexChanged();
                    }
                    RefreshSampleChanelState_TriggerChannelContent();
                    break;
                case "配置6":
                    if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Count != 0)
                    {
                        SelectedSampleChannel1Index = GlobalCurrentInput.SelectedSampleChannel1Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel1Index).ToList()[5];
                        SelectedSampleChannel2Index = GlobalCurrentInput.SelectedSampleChannel2Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel2Index).ToList()[5];
                        SelectedSampleChannel3Index = GlobalCurrentInput.SelectedSampleChannel3Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel3Index).ToList()[5];
                        SelectedSampleChannel4Index = GlobalCurrentInput.SelectedSampleChannel4Index = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SelectedSampleChannel4Index).ToList()[5];
                        SelectedSamplingPeriod = GlobalCurrentInput.SelectedSamplingPeriod = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingPeriod).ToList()[5];
                        SelectedSamplingDuration = GlobalCurrentInput.SelectedSamplingDuration = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.SamplingDuration).ToList()[5];
                        SelectedContinuousSampling = GlobalCurrentInput.SelectedContinuousSampling = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.ContinuousSampling).ToList()[5];
                        SelectedTriggerClockEdge = GlobalCurrentInput.SelectedTriggerClockEdge = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerClockEdge).ToList()[5];
                        SelectedTriggerChannel = GlobalCurrentInput.SelectedTriggerChannel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerChannel).ToList()[5];
                        if (ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[5] == "0")
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[5];
                        }
                        else
                        {
                            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.PreTrigger).ToList()[5] + "%";
                        }
                        TriggerLevel = GlobalCurrentInput.TriggerLevel = ConfigOscilloscopePreset.OscilloscopePresetConfigs.Select(t => t.TriggerLevel).ToList()[5];
                        //OnSelectedSampleChannelIndexChanged();
                    }
                    RefreshSampleChanelState_TriggerChannelContent();
                    break;
                default:
                    return;
            }
        }

        public void OnSelectedOscilloscopePresetChanged()
        {
            //XmlHelper.XmlOscilloscopePresetConfigs();
            GlobalCurrentInput.SelectedOscilloscopePreset = SelectedOscilloscopePreset;
            //List<OscilloscopePresetModel> oscilloscopePresetModelList = new List<OscilloscopePresetModel>();
            //oscilloscopePresetModelList = XmlHelper.GetOscilloscopePresetConfigsData();
            GetOscilloscopePresetData();

            switch (SelectedOscilloscopePreset)
            {                
                //case 0:
                //    GlobalCurrentInput.SelectedSampleChannel1Index = SelectedSampleChannel1Index = 0;
                //    OnSelectedSampleChannel1IndexChanged();
                //    break;
                case "配置1":
                    GetOscilloscopePreset("配置1");                    
                    break;
                case "配置2":
                    GetOscilloscopePreset("配置2");                  
                    break;
                case "配置3":
                    GetOscilloscopePreset("配置3");                    
                    break;
                case "配置4":
                    GetOscilloscopePreset("配置4");                    
                    break;
                case "配置5":
                    GetOscilloscopePreset("配置5");                    
                    break;
                case "配置6":
                    GetOscilloscopePreset("配置6");                    
                    break;
                default:                
                    return;
            }
        }
        public void OnSelectedTriggerClockEdgeChanged() { GlobalCurrentInput.SelectedTriggerClockEdge = SelectedTriggerClockEdge; }
        public void OnSelectedPreTriggerChanged() { GlobalCurrentInput.SelectedPreTrigger = SelectedPreTrigger; }
        public void OnSelectedTriggerChannelChanged()
        {
            string strKey = null;
            string strValue = null;

            GlobalCurrentInput.SelectedTriggerChannel = SelectedTriggerChannel;
            switch (SelectedTriggerChannel)
            {
                case "通道-1":
                    strKey = SelectedSamplingChannel1;
                    break;
                case "通道-2":
                    strKey = SelectedSamplingChannel2;
                    break;
                case "通道-3":
                    strKey = SelectedSamplingChannel3;
                    break;
                case "通道-4":
                    strKey = SelectedSamplingChannel4;
                    break;
                default:
                    return;
            }

            if (!IsLoadedAgain)          
            {
                GlobalCurrentInput.TriggerChannelIndex = TriggerChannel.FindIndex(item => item == SelectedTriggerChannel);
            }

            if (!string.IsNullOrEmpty(strKey))
            {
                SoftwareStateParameterSet.dicAcquisitionUint.TryGetValue(strKey, out strValue);
                Unit = strValue;
            }
            else
            {
                Unit = "";
            }
        }
        public void OnTriggerLevelChanged()
        {
            GetTriggerLevelValue(Method: "ToGlobal");
        }
        public void OnSelectedDisplayMethodChanged() { GlobalCurrentInput.SelectedDisplayMethod = SelectedDisplayMethod; }

        //函数发生器
        public void OnInnerSourceFrequencyChanged() { GlobalCurrentInput.InnerSourceFrequency = InnerSourceFrequency; }//信号频率
        public void OnInnerSourceAmplitudeChanged() { GlobalCurrentInput.InnerSourceAmplitude = InnerSourceAmplitude; }//位置幅值
        public void OnSelectedInnerSourceEffectIndexChanged()//作用对象
        {
            GlobalCurrentInput.SelectedInnerSourceEffectIndex = SelectedInnerSourceEffectIndex;

            switch (SelectedInnerSourceEffectIndex)
            {
                case "0":
                    AmplitudeUnit = PositionUnit;
                    break;
                case "1":
                    AmplitudeUnit = SpeedUnit;
                    break;
                case "2":
                    AmplitudeUnit = TorqueUnit;
                    break;
                default:
                    break;
            }
        }
        public void OnSelectedInnerSourceTypeIndexChanged()//函数类型
        {
            GlobalCurrentInput.SelectedInnerSourceTypeIndex = SelectedInnerSourceTypeIndex;

            if (SelectedInnerSourceTypeIndex == "2" || SelectedInnerSourceTypeIndex == "3")
            {
                InnerSourceNumberEnabled = false;
                InnerSourceNumber = "1";
                ViewModelSet.Main?.ShowHintInfo("产生数量属性为只读，默认值为1");
            }
            else
            {
                InnerSourceNumberEnabled = true;
            }

            if (SelectedInnerSourceTypeIndex == "3")
            {
                InnerSourceGradientVisibility = ControlVisibility.Visible;
            }      
            else
            {
                InnerSourceGradientVisibility = ControlVisibility.Collapsed;
            }
        }
        public void OnInnerSourceNumberChanged() { GlobalCurrentInput.InnerSourceNumber = InnerSourceNumber; }//产生个数      
        public void OnInnerSourceGradientChanged() { GlobalCurrentInput.InnerSourceGradient = InnerSourceGradient; }//信号斜率
        public void OnAmplitudeUnitChanged() { GlobalCurrentInput.AmplitudeUnit = AmplitudeUnit; }//幅值单位

        //三环调试                                    由Lilbert于2021.06.25增加位置速度环，去掉位置环
        public void OnSelectedLoopModeChanged()
        {
            GlobalCurrentInput.SelectedLoopMode = SelectedLoopMode;

            if (SelectedLoopMode == "电流环")
            {
                IsSpeedPageEnabled = ControlVisibility.Collapsed;
                IsCurrentPageEnabled = ControlVisibility.Visible;
                IsPositionPageEnabled = ControlVisibility.Collapsed;
            }
            else if (SelectedLoopMode == "位置速度环")           //由Lilbert于2021.06.25增加位置速度环，去掉位置环
            {
                IsSpeedPageEnabled = ControlVisibility.Visible;
                IsPositionPageEnabled = ControlVisibility.Visible;
                IsCurrentPageEnabled = ControlVisibility.Collapsed;
            }
            //else if (SelectedLoopMode == "位置环")
            //{
            //    IsSpeedPageEnabled = ControlVisibility.Collapsed;
            //    IsCurrentPageEnabled = ControlVisibility.Collapsed;
            //    IsPositionPageEnabled = ControlVisibility.Visible;
            //}
        }
        public void OnPositionLoopGainChanged() { GlobalCurrentInput.PositionLoopGain = PositionLoopGain; }//位置环增益
        public void OnLoadInertiaRatioChanged() { GlobalCurrentInput.LoadInertiaRatio = LoadInertiaRatio; }//负载惯量比     由Lilbert增加负载惯量比
        public void OnFirstTrqcmdFilterTimeChanged() { GlobalCurrentInput.FirstTrqcmdFilterTime = FirstTrqcmdFilterTime; }//第一转矩滤波时间     由Lilbert增加第一转矩滤波时间
        public void OnSpeedLoopGainChanged() { GlobalCurrentInput.SpeedLoopGain = SpeedLoopGain; }//速度环增益
        public void OnSpeedLoopTimeConstantChanged() { GlobalCurrentInput.SpeedLoopTimeConstant = SpeedLoopTimeConstant; }//速度环积分时间常数
        public void OnCurrentLoopGainChanged() { GlobalCurrentInput.CurrentLoopGain = CurrentLoopGain; }//电流环增益
        public void OnCurrentLoopTimeConstantChanged() { GlobalCurrentInput.CurrentLoopTimeConstant = CurrentLoopTimeConstant; }//电流环积分时间常数
        public void OnRigidityChanged() { GlobalCurrentInput.Rigidity = Rigidity; }//刚性     由Lilbert增加刚性

        //运动模式
        public void OnTargetPositionChanged() { GlobalCurrentInput.TargetPosition = TargetPosition; }//目标位置
        public void OnProfileVelocityChanged() { GlobalCurrentInput.ProfileVelocity = ProfileVelocity; }//轮廓运行速度
        public void OnEndProfileVelocityChanged() { GlobalCurrentInput.EndProfileVelocity = EndProfileVelocity; }//轮廓结尾速度
        public void OnProfileAccelerationChanged() { GlobalCurrentInput.ProfileAcceleration = ProfileAcceleration; }//轮廓加速度
        public void OnProfileDecelerationChanged() { GlobalCurrentInput.ProfileDeceleration = ProfileDeceleration; }//轮廓减速度

        public void OnTargetVelocityChanged() { GlobalCurrentInput.TargetVelocity = TargetVelocity; }//目标速度      
        public void OnProfileJerk1Changed() { GlobalCurrentInput.ProfileJerk1 = ProfileJerk1; }//轮廓加加速度1
        public void OnProfileJerk2Changed() { GlobalCurrentInput.ProfileJerk2 = ProfileJerk2; }//轮廓加加速度2
        public void OnProfileJerk3Changed() { GlobalCurrentInput.ProfileJerk3 = ProfileJerk3; }//轮廓加加速度3
        public void OnProfileJerk4Changed() { GlobalCurrentInput.ProfileJerk4 = ProfileJerk4; }//轮廓加加速度4

        public void OnTargetTorqueChanged() { GlobalCurrentInput.TargetTorque = TargetTorque; }//目标转矩
        public void OnTorqueSlopeChanged() { GlobalCurrentInput.TorqueSlope = TorqueSlope; }//转矩斜坡
        public void OnSelectedActionModeChanged()//选中的运动模式
        {
            GlobalCurrentInput.SelectedActionMode = SelectedActionMode;

            if (SelectedActionMode == "位置模式")
            {
                IsPositionActionEnabled = ControlVisibility.Visible;
                IsSlopeActionEnabled = ControlVisibility.Collapsed;
                IsJerkFreeActionEnabled = ControlVisibility.Collapsed;
                IsTorqueActionEnabled = ControlVisibility.Collapsed;
            }
            else if (SelectedActionMode == "速度模式")
            {
                IsPositionActionEnabled = ControlVisibility.Collapsed;
                IsSlopeActionEnabled = ControlVisibility.Visible;
                IsJerkFreeActionEnabled = ControlVisibility.Visible;
                IsTorqueActionEnabled = ControlVisibility.Collapsed;
            }
            else
            {
                IsPositionActionEnabled = ControlVisibility.Collapsed;
                IsSlopeActionEnabled = ControlVisibility.Collapsed;
                IsJerkFreeActionEnabled = ControlVisibility.Collapsed;
                IsTorqueActionEnabled = ControlVisibility.Visible;
            }

            GetActionMode();
        }
        public void OnSelectedParameterTunningModeChanged()//选中的参数调优模式模式
        {
            GlobalCurrentInput.SelectedParameterTunningMode = SelectedParameterTunningMode;

            if (SelectedParameterTunningMode == "速度模式")
            {
                //string strStatusWord = OthersHelper.GetCurrentValueOfIndex("0x604100");//获取状态字
                //if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING || OthersHelper.GetCurrentValueOfIndex("0x604100") == "567")
                //{
                //    OthersHelper.OperatingControl("Fn Servo Off", "1", "伺服禁能", "");
                //}

                IsSpeedParameterTunningEnabled = ControlVisibility.Visible;
                IsPositionParameterTunningEnabled = ControlVisibility.Collapsed;
            }
            else
            {
                //string strStatusWord = OthersHelper.GetCurrentValueOfIndex("0x604100");//获取状态字
                //if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING || OthersHelper.GetCurrentValueOfIndex("0x604100") == "567")
                //{
                //    OthersHelper.OperatingControl("Fn Servo Off", "1", "伺服禁能", "");
                //}

                IsSpeedParameterTunningEnabled = ControlVisibility.Collapsed;
                IsPositionParameterTunningEnabled = ControlVisibility.Visible;
            }

            GetParameterTunningMode();
        }
        public void OnSelectedPositionModeChanged() { GlobalCurrentInput.SelectedPositionMode = SelectedPositionMode; GetActionMode(); }//选中的位置指令
        public void OnSelectedProfileModeChanged() { GlobalCurrentInput.SelectedProfileMode = SelectedProfileMode; GetActionMode(); }//选中的规划曲线

        public void OnPositionUnitChanged() { GlobalCurrentInput.PositionUnit = PositionUnit; }
        public void OnTorqueUnitChanged() { GlobalCurrentInput.TorqueUnit = TorqueUnit; }
        public void OnSpeedUnitChanged() { GlobalCurrentInput.SpeedUnit = SpeedUnit; }
        public void OnAccelerationUnitChanged() { GlobalCurrentInput.AccelerationUnit = AccelerationUnit; }
        #endregion

        #endregion

        #region 私有方法

        #region 初始化
        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：下拉列表初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.12.02&2021.07.05
        //*************************************************************************
        private void ObservableCollectionInitialize()
        {
            try
            {
                #region 示波器
                //OscilloscopePreset = new ObservableCollection<string>() { "停用", "配置1", "配置2", "配置3", "配置4", "配置5", "配置6" };  //由Lilbert添加示波器预设置
                OscilloscopePreset = new ObservableCollection<string>() { "配置1", "配置2", "配置3", "配置4", "配置5", "配置6" };

                SamplingDuration = new ObservableCollection<string>();
                SamplingPeriod = new ObservableCollection<string>(XmlHelper.GetSamplingPeriods());
                ContinuousSampling = new ObservableCollection<string>() { "否", "是" };

                TriggerClockEdge = new ObservableCollection<string>() { "无", "上升沿触发", "下降沿触发", "双边沿触发", "水平触发-高", "水平触发-低" };
                PreTrigger = new ObservableCollection<string>() { "0", "10%", "20%", "30%", "50%", "70%" };

                DisplayMethod = new ObservableCollection<string>() { "连续采样-静态展示", "连续采样-动态展示" };
                #endregion

                #region 函数发生器
                InnerSourceEffect = new ObservableCollection<string> { "位置指令", "速度指令", "转矩指令" };
                SelectedInnerSourceEffectIndex = "0";
                InnerSourceType = new ObservableCollection<string> { "方波", "正弦波", "阶跃", "梯形波" };
                #endregion

                #region 三环模式
                LoopMode = new ObservableCollection<string> { "电流环", "位置速度环" };    //由Lilbert添加位置速度环，去掉位置环
                #endregion

                #region 运动模式
                ActionMode = new ObservableCollection<string> { "位置模式", "速度模式", "转矩模式" };
                PositionMode = new ObservableCollection<string> { "绝对位置模式", "相对位置模式" };
                ProfileMode = new ObservableCollection<string> { "斜坡曲线", "Jerk Free曲线" };
                #endregion

                #region 参数调优模式
                ParameterTunningMode = new ObservableCollection<string> { "位置模式", "速度模式" };// 由Lilbert添加参数调优模式
                #endregion

                #region 程序Jog模式
                //ProgramJogSwitch = new ObservableCollection<string>() { "正转", "反转", "正反转-模式A", "反正转-模式A", "正反转-模式B", "反正转-模式B" };//由Lilbert添加程序Jog
                ProgramJogSwitch = new ObservableCollection<string>() { "模式一", "模式二", "模式三", "模式四", "模式五", "模式六" };//由Lilbert于2023.02.10更改                
                SelectedProgramJogSwitchIndex = "0";
                #endregion
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_COMBOBOX_INITIALIZE, "ComboBoxInitialize", ex);
            }
        }

        //*************************************************************************
        //函数名称：GroupSampleChannelInitialize
        //函数功能：ComboBox分组初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2021.01.15&2022.01.12&2022.08.05
        //*************************************************************************
        private void GroupSampleChannelInitialize()
        {
            // Load base channels from XML
            List<SampleChannelInfoSet> baseChannels = XmlHelper.GetSampleChannels();
            if (baseChannels == null || baseChannels.Count == 0)
            {
                // Handle error or empty case
                baseChannels = new List<SampleChannelInfoSet>();
            }

            // Create a deep copy for display purposes to add units
            List<SampleChannelInfoSet> displayChannels = new List<SampleChannelInfoSet>();
            foreach (var channel in baseChannels)
            {
                var displayChannel = new SampleChannelInfoSet
                {
                    GroupName = channel.GroupName,
                    ID = channel.ID,
                    ItemName = channel.ItemName,
                    Address = channel.Address
                };

                // Append units based on group name or item name logic from original code
                if (displayChannel.ID != 0) // Don't add unit to "停用"
                {
                    string unit = "";
                    if (channel.GroupName.StartsWith("位置"))
                        unit = $"[{Convert.ToString(SelectUnit.Position)}]";
                    else if (channel.GroupName.StartsWith("速度"))
                        unit = $"[{Convert.ToString(SelectUnit.Speed)}]";
                    else if (channel.GroupName.StartsWith("转矩"))
                        unit = channel.ItemName.Contains("电流") ? "[mA]" : "[‰]";
                    else if (channel.GroupName.StartsWith("前馈"))
                        unit = channel.ItemName.Contains("速度") ? $"[{Convert.ToString(SelectUnit.Speed)}]" : "[‰]";
                    else if (channel.GroupName.StartsWith("编码器"))
                        unit = "[Pulse]";
                    else if (channel.GroupName.StartsWith("母线"))
                        unit = channel.ItemName.Contains("电压") ? "[V]" : "[mA]";
                    else if (channel.GroupName.StartsWith("标志"))
                       unit = "[1]";
                   else
                       unit = "[1]";
                   
                   displayChannel.ItemName += unit;
                }
                displayChannels.Add(displayChannel);
            }

            // Assign to properties
            // Create separate instances for each channel to avoid binding issues.
            SampleChannelInfo1 = new List<SampleChannelInfoSet>(baseChannels);
            SampleChannelInfo2 = new List<SampleChannelInfoSet>(baseChannels);
            SampleChannelInfo3 = new List<SampleChannelInfoSet>(baseChannels);
            SampleChannelInfo4 = new List<SampleChannelInfoSet>(baseChannels);

            SampleChannelInfo1_Display = new List<SampleChannelInfoSet>(displayChannels);
            SampleChannelInfo2_Display = new List<SampleChannelInfoSet>(displayChannels);
            SampleChannelInfo3_Display = new List<SampleChannelInfoSet>(displayChannels);
            SampleChannelInfo4_Display = new List<SampleChannelInfoSet>(displayChannels);

            // Setup grouped views
            GroupedSampleChannelInfo1 = CollectionViewSource.GetDefaultView(SampleChannelInfo1_Display);
            if (GroupedSampleChannelInfo1.GroupDescriptions.Count == 0)
                GroupedSampleChannelInfo1.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo2 = CollectionViewSource.GetDefaultView(SampleChannelInfo2_Display);
            if (GroupedSampleChannelInfo2.GroupDescriptions.Count == 0)
                GroupedSampleChannelInfo2.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo3 = CollectionViewSource.GetDefaultView(SampleChannelInfo3_Display);
            if (GroupedSampleChannelInfo3.GroupDescriptions.Count == 0)
                GroupedSampleChannelInfo3.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            GroupedSampleChannelInfo4 = CollectionViewSource.GetDefaultView(SampleChannelInfo4_Display);
            if (GroupedSampleChannelInfo4.GroupDescriptions.Count == 0)
                GroupedSampleChannelInfo4.GroupDescriptions.Add(new PropertyGroupDescription("GroupName"));

            SelectedSampleChannel1Index = 0;
            SelectedSampleChannel2Index = 0;
            SelectedSampleChannel3Index = 0;
            SelectedSampleChannel4Index = 0;
        }

        //*************************************************************************
        //函数名称：EventRegisterInitialize
        //函数功能：事件注册初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.02
        //*************************************************************************
        private void EventRegisterInitialize()
        {
            if (PthreadStatement.MicrosecondsOscilloscopeDrawing != null)
            {
                PthreadStatement.MicrosecondsOscilloscopeDrawing.evtCheckAllThreadClosed += AllThreadClosedDispose;
            }

            if (PthreadStatement.SerialPortTransmiting != null)
            {
                PthreadStatement.SerialPortTransmiting.evtCheckAllThreadClosed += AllThreadClosedDispose;
            }

            if (ViewModelSet.CommunicationSet != null)
            {
                ViewModelSet.CommunicationSet.evtOscilliscopeButtonEnabled += ButtonEnableInitialize;
            }
        }

        //*************************************************************************
        //函数名称：WaveControl_WaveCalculateInitialize
        //函数功能：波形控制与波形计算初始化
        //
        //输入参数：NONE
        //         
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.03.06
        //*************************************************************************
        private void WaveControl_WaveCalculateInitialize()
        {
            //波形控制(倍乘、隐藏）
            OscilloscopeProperty = new ObservableCollection<OscilloscopePropertySet>();
            for (int i = 0; i < 4; i++)
            {
                OscilloscopePropertySet clsTemp = new OscilloscopePropertySet();
                clsTemp.ChannelNumber = "通道-" + (i + 1);
                clsTemp.Doubling = "1";
                clsTemp.IsHidden = false;
                OscilloscopeProperty.Add(clsTemp);
            }

            //波形计算
            OsilloscopeCalculate = new ObservableCollection<OsilloscopeCalculateSet>();
            for (int i = 0; i <= 7; i++)
            {
                OsilloscopeCalculateSet clsTemp = new OsilloscopeCalculateSet();
                switch (i)
                {
                    case 0:
                        clsTemp.Title = "当前光标";
                        break;
                    case 1:
                        clsTemp.Title = "上一个光标";
                        break;
                    case 2:
                        clsTemp.Title = "光标间距";
                        break;
                    case 3:
                        clsTemp.Title = "区间最大值";
                        break;
                    case 4:
                        clsTemp.Title = "区间最小值";
                        break;
                    case 5:
                        clsTemp.Title = "区间峰峰值";
                        break;
                    case 6:
                        clsTemp.Title = "区间平均值";
                        break;
                    case 7:
                        clsTemp.Title = "区间均方根";
                        break;
                    default:
                        break;
                }

                OsilloscopeCalculate.Add(clsTemp);
            }
        }
        #endregion

        #region 前台交互控件状态
        //*************************************************************************
        //函数名称：RefreshSampleChanelState_TriggerChannelContent
        //函数功能：更新触发通道下拉列表和采集通道下拉列表使能状态
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.26
        //*************************************************************************
        private void RefreshSampleChanelState_TriggerChannelContent(int i)
        {
            TriggerChannel = new ObservableCollection<string>();

            #region 通道1
            if (SelectedSampleChannel1Index == 0)
            {
                Channel2Enabled = false;
                Channel3Enabled = false;
                Channel4Enabled = false;

                SelectedSampleChannel2Index = 0;
                SelectedSampleChannel3Index = 0;
                SelectedSampleChannel4Index = 0;

                //数据采集启动按钮状态
                AcquisitionStartButtonEnabled = false;

                //触发器参数
                SelectedTriggerClockEdge = "无";
                SelectedTriggerChannel = "";
                SelectedPreTrigger = "0";
                TriggerLevel = "";

                return;
            }
            else
            {
                TriggerChannel.Add("通道-1");
                Channel2Enabled = true;

                //数据采集启动按钮状态
                AcquisitionStartButtonEnabled = true;

                //触发器参数
                //SelectedTriggerChannel = TriggerChannel[0];
            }
            #endregion

            #region 通道2
            if (SelectedSampleChannel2Index == 0)
            {
                Channel3Enabled = false;
                Channel4Enabled = false;

                SelectedSampleChannel3Index = 0;
                SelectedSampleChannel4Index = 0;
                return;
            }
            else
            {
                TriggerChannel.Add("通道-2");
                Channel3Enabled = true;
            }
            #endregion

            #region 通道3
            if (SelectedSampleChannel3Index == 0)
            {
                Channel4Enabled = false;
                SelectedSampleChannel4Index = 0;
                return;
            }
            else
            {
                TriggerChannel.Add("通道-3");
                Channel4Enabled = true;
            }
            #endregion

            #region 通道4
            if (SelectedSampleChannel4Index != 0)
            {
                TriggerChannel.Add("通道-4");
            }
            #endregion          
        }
        //*************************************************************************
        //函数名称：RefreshSampleChanelState_TriggerChannelContent
        //函数功能：更新触发通道下拉列表和采集通道下拉列表使能状态
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.02
        //*************************************************************************
        private void RefreshSampleChanelState_TriggerChannelContent()
        {
            TriggerChannel = new ObservableCollection<string>();

            #region 通道1
            if (SelectedSampleChannel1Index == 0)
            {
                Channel2Enabled = false;
                Channel3Enabled = false;
                Channel4Enabled = false;

                SelectedSampleChannel2Index = 0;
                SelectedSampleChannel3Index = 0;
                SelectedSampleChannel4Index = 0;

                //数据采集启动按钮状态
                AcquisitionStartButtonEnabled = false;

                //触发器参数
                SelectedTriggerClockEdge = "无";
                SelectedTriggerChannel = "";
                SelectedPreTrigger = "0";
                TriggerLevel = "";

                return;
            }
            else
            {              
                TriggerChannel.Add("通道-1");
                Channel2Enabled = true;

                //数据采集启动按钮状态
                AcquisitionStartButtonEnabled = true;

                //触发器参数
                SelectedTriggerChannel = TriggerChannel[0];
            }
            #endregion

            #region 通道2
            if (SelectedSampleChannel2Index == 0)
            {
                Channel3Enabled = false;
                Channel4Enabled = false;

                SelectedSampleChannel3Index = 0;
                SelectedSampleChannel4Index = 0;
                return;
            }
            else
            {
                TriggerChannel.Add("通道-2");
                Channel3Enabled = true;
            }
            #endregion

            #region 通道3
            if (SelectedSampleChannel3Index == 0)
            {
                Channel4Enabled = false;
                SelectedSampleChannel4Index = 0;
                return;
            }
            else
            {
                TriggerChannel.Add("通道-3");
                Channel4Enabled = true;
            }
            #endregion

            #region 通道4
            if (SelectedSampleChannel4Index != 0)
            {
                TriggerChannel.Add("通道-4");
            }
            #endregion          
        }

        //*************************************************************************
        //函数名称：RefreshSamplingDurationComboBox
        //函数功能：更新触发总长
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.02
        //*************************************************************************
        private void RefreshSamplingDuration(string strLastDuration)
        {
            int iChannelNumber = 0;//通道数
            int iInterval = 0;//间隔数
            double dSamplingDuration = 0;
            double dBiggestTimes = 0;
            SamplingDuration = new ObservableCollection<string>();

            try
            {
                if (SelectedSampleChannel1Index != 0) iChannelNumber++;
                if (SelectedSampleChannel2Index != 0) iChannelNumber++;
                if (SelectedSampleChannel3Index != 0) iChannelNumber++;
                if (SelectedSampleChannel4Index != 0) iChannelNumber++;
                if (iChannelNumber == 0) return;

                //获取采样间隔
                switch (iChannelNumber)
                {
                    case 1:
                        dBiggestTimes = 4000;
                        iInterval = Convert.ToInt32(Math.Floor(dBiggestTimes / 10));
                        break;
                    case 2:
                        dBiggestTimes = 2000;
                        iInterval = Convert.ToInt32(Math.Floor(dBiggestTimes / 10));
                        break;
                    case 3:
                        dBiggestTimes = 1300;
                        iInterval = Convert.ToInt32(Math.Floor(dBiggestTimes / 10));
                        break;
                    case 4:
                        dBiggestTimes = 1000;
                        iInterval = Convert.ToInt32(Math.Floor(dBiggestTimes / 10));
                        break;
                    default:
                        break;
                }

                //前10个采集时长
                if (SelectedSamplingPeriod == null)
                {

                }
                else
                {
                    for (int iNumber = 1; iNumber <= 13; iNumber++)
                    {
                        //获取采样时长，单位转换成ms
                        if (SelectedSamplingPeriod.IndexOf("μ") == -1)
                        {
                            dSamplingDuration = iInterval * iNumber * Convert.ToDouble(SelectedSamplingPeriod.Replace("ms", ""));
                        }
                        else
                        {
                            dSamplingDuration = iInterval * iNumber * Convert.ToDouble(SelectedSamplingPeriod.Replace("μs", "")) / 1000;
                        }

                        SamplingDuration.Add(dSamplingDuration.ToString() + "ms");
                    }
                }  
                

                //后20个采集时长
                //for (int iNumber = 11; iNumber <= 20; iNumber++)
                //{
                //    //获取采样时长，单位转换成ms
                //    if (SelectedSamplingPeriod.IndexOf("μ") == -1)
                //    {
                //        dSamplingDuration = iInterval * iNumber * Convert.ToDouble(SelectedSamplingPeriod.Replace("ms", ""));
                //    }
                //    else
                //    {
                //        dSamplingDuration = iInterval * iNumber * Convert.ToDouble(SelectedSamplingPeriod.Replace("μs", "")) / 1000;
                //    }

                //    if (iNumber % 3 == 0)
                //    {
                //        SamplingDuration.Add(dSamplingDuration.ToString() + "ms");
                //    }
                //}

                //选中的采集时长
                if (SamplingDuration.Count > 0)
                {
                    if (string.IsNullOrEmpty(strLastDuration))
                    {
                        SelectedSamplingDuration = SamplingDuration[0];
                    }
                    else
                    {
                        if (SamplingDuration.Contains(strLastDuration))
                        {
                            SelectedSamplingDuration = strLastDuration;
                        }
                        else
                        {
                            SelectedSamplingDuration = SamplingDuration[0];
                        }
                    }
                }
                else
                {
                    SelectedSamplingDuration = "0ms";
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_REFRESH_SAMPLING_DURATION_COMBO_BOX, "RefreshSamplingDurationComboBox", ex);
            }
        }

        //*************************************************************************
        //函数名称：MessageForConfirm
        //函数功能：波形信息提示
        //
        //输入参数：NONE
        //         
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2020.02.18
        //*************************************************************************
        private string MessageForConfirm(string strIndex)
        {
            string strHint_Channel = null;
            string strHint_Trigger = null;
            string strHint_All = null;
            string strFunction = null;

            switch (strIndex)
            {
                case "Export":
                    strFunction = "是否要保存当前波形 ：" + "\r\n";
                    break;
                case "Import":
                    strFunction = "是否要载入选中的波形 ：" + "\r\n";
                    break;
                case "Last":
                    strFunction = "是否要展示最后一次采集的波形 ：" + "\r\n";
                    break;
                default:
                    break;
            }

            if (SelectedSamplingChannel1 != "停用")
                strHint_Channel = "通道-1 ：" + SelectedSamplingChannel1 + "\r\n";

            if (SelectedSamplingChannel2 != "停用")
                strHint_Channel += "通道-2 ：" + SelectedSamplingChannel2 + "\r\n";

            if (SelectedSamplingChannel3 != "停用")
                strHint_Channel += "通道-3 ：" + SelectedSamplingChannel3 + "\r\n";

            if (SelectedSamplingChannel4 != "停用")
                strHint_Channel += "通道-4 ：" + SelectedSamplingChannel4 + "\r\n";

            if (SelectedTriggerClockEdge != "无")
                strHint_Trigger = "触发模式 ：" + SelectedTriggerClockEdge + "\r\n" + "触发通道 ：" + SelectedTriggerChannel + "\r\n" + "预触发 ：" + SelectedPreTrigger + "\r\n" + "触发水平 ：" + TriggerLevel;

            strHint_All = strFunction + strHint_Channel +
                          "采样周期 ：" + SelectedSamplingPeriod + "\r\n" +
                          "采样时长 ：" + SelectedSamplingDuration + "\r\n" +
                          "采样连续 ：" + SelectedContinuousSampling + "\r\n" + strHint_Trigger;

            return strHint_All;
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        private void ShowNotification_CrossThread(int In_iType)
        {
            WindowSet.clsMainWindow?.ShowNotification(In_iType);
        }
        #endregion

        #region 参数赋值(文件导入导出)
        //*************************************************************************
        //函数名称：InterfaceEvaluationFromInitialization
        //函数功能：接口初值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.22&2022.11.22
        //*************************************************************************
        private void GetDefaultOscilloscopeSetParameter()
        {
            //示波器
            SelectedSamplingPeriod = "62.5μs";
            SelectedSamplingDuration = "0ms";
            SelectedContinuousSampling = "否";

            SelectedSampleChannel1Index = 0;
            SelectedSampleChannel2Index = 0;
            SelectedSampleChannel3Index = 0;
            SelectedSampleChannel4Index = 0;

            SelectedTriggerClockEdge = "无";
            SelectedPreTrigger = "0";
            SelectedDisplayMethod = "连续采样-静态展示";

            //三环与示波器
            SelectedLoopMode = "电流环";
            SelectedInnerSourceEffectIndex = "0";//作用对象
            SelectedInnerSourceTypeIndex = "0";//函数类型

            //运动模式
            SelectedActionMode = "位置模式";

            //函数发生器
            SelectedPositionMode = "绝对位置模式";
            SelectedProfileMode = "斜坡曲线";

            //参数调优模式
            SelectedParameterTunningMode = "位置模式";   //由Lilbert于2022.11.07添加参数调优模式
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.18&2021.07.05&2022.11.22
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            #region 示波器
            SelectedOscilloscopePreset = GlobalCurrentInput.SelectedOscilloscopePreset;
            GetOscilloscopePreset(SelectedOscilloscopePreset);

            SelectedSamplingPeriod = GlobalCurrentInput.SelectedSamplingPeriod;
            SelectedContinuousSampling = GlobalCurrentInput.SelectedContinuousSampling;
            SelectedSamplingDuration = GlobalCurrentInput.SelectedSamplingDuration;

            SelectedSampleChannel1Index = GlobalCurrentInput.SelectedSampleChannel1Index;
            SelectedSampleChannel2Index = GlobalCurrentInput.SelectedSampleChannel2Index;
            SelectedSampleChannel3Index = GlobalCurrentInput.SelectedSampleChannel3Index;
            SelectedSampleChannel4Index = GlobalCurrentInput.SelectedSampleChannel4Index;

            //SelectedOscilloscopePreset = GlobalCurrentInput.SelectedOscilloscopePreset;
            //GetOscilloscopePreset(SelectedOscilloscopePresetIndex);
            //GetOscilloscopePreset(SelectedOscilloscopePreset);

            SelectedTriggerClockEdge = GlobalCurrentInput.SelectedTriggerClockEdge;
            SelectedPreTrigger = GlobalCurrentInput.SelectedPreTrigger;
            SelectedTriggerChannel = GlobalCurrentInput.SelectedTriggerChannel;
            SelectedDisplayMethod = GlobalCurrentInput.SelectedDisplayMethod;

            OscilloscopeProperty.Clear();
            for (int j = 0; j < 4; j++)
            {
                OscilloscopePropertySet clsTemp = new OscilloscopePropertySet();
                clsTemp.ChannelNumber = "通道-" + (j + 1);
                clsTemp.IsHidden = false;
                if (!string.IsNullOrEmpty(GlobalCurrentInput.DoublingChannel[j]) && OthersHelper.IsInputNumber(GlobalCurrentInput.DoublingChannel[j]))
                {
                    clsTemp.Doubling = GlobalCurrentInput.DoublingChannel[j];
                }
                else
                {
                    clsTemp.Doubling = "1";
                }

                OscilloscopeProperty.Add(clsTemp);
            }

            #endregion

            #region 函数发生器
            InnerSourceFrequency = GlobalCurrentInput.InnerSourceFrequency;//信号频率
            InnerSourceAmplitude = GlobalCurrentInput.InnerSourceAmplitude;//位置幅值
            SelectedInnerSourceEffectIndex = GlobalCurrentInput.SelectedInnerSourceEffectIndex;//作用对象
            SelectedInnerSourceTypeIndex = GlobalCurrentInput.SelectedInnerSourceTypeIndex;//函数类型
            InnerSourceNumber = GlobalCurrentInput.InnerSourceNumber;//产生个数
            InnerSourceGradient = GlobalCurrentInput.InnerSourceGradient;//信号斜率    
            AmplitudeUnit = GlobalCurrentInput.AmplitudeUnit;
            #endregion

            #region 三环调试
            SelectedLoopMode = GlobalCurrentInput.SelectedLoopMode;//选中的模式
            PositionLoopGain = GlobalCurrentInput.PositionLoopGain;//位置环增益
            LoadInertiaRatio = GlobalCurrentInput.LoadInertiaRatio;//负载惯量比     由Lilbert增加负载惯量比
            FirstTrqcmdFilterTime = GlobalCurrentInput.FirstTrqcmdFilterTime;//第一转矩滤波时间     由Lilbert增加第一转矩滤波时间
            SpeedLoopGain = GlobalCurrentInput.SpeedLoopGain;//速度环增益
            SpeedLoopTimeConstant = GlobalCurrentInput.SpeedLoopTimeConstant;//速度环积分时间常数
            CurrentLoopGain = GlobalCurrentInput.CurrentLoopGain;//电流环增益
            CurrentLoopTimeConstant = GlobalCurrentInput.CurrentLoopTimeConstant;//电流环积分时间常数
            Rigidity = GlobalCurrentInput.Rigidity;//刚性     由Lilbert增加刚性
            #endregion

            #region 运动模式
            TargetPosition = GlobalCurrentInput.TargetPosition;//目标位置
            ProfileVelocity = GlobalCurrentInput.ProfileVelocity;//轮廓运行速度
            EndProfileVelocity = GlobalCurrentInput.EndProfileVelocity;//轮廓结尾速度
            ProfileAcceleration = GlobalCurrentInput.ProfileAcceleration;//轮廓加速度
            ProfileDeceleration = GlobalCurrentInput.ProfileDeceleration;//轮廓减速度

            TargetVelocity = GlobalCurrentInput.TargetVelocity;//目标速度             
            ProfileJerk1 = GlobalCurrentInput.ProfileJerk1;//轮廓加加速度1
            ProfileJerk2 = GlobalCurrentInput.ProfileJerk2;//轮廓加加速度2
            ProfileJerk3 = GlobalCurrentInput.ProfileJerk3;//轮廓加加速度3
            ProfileJerk4 = GlobalCurrentInput.ProfileJerk4;//轮廓加加速度4

            TargetTorque = GlobalCurrentInput.TargetTorque;//目标转矩
            TorqueSlope = GlobalCurrentInput.TorqueSlope;//转矩斜坡
            SelectedActionMode = GlobalCurrentInput.SelectedActionMode;//选中的运动模式
            SelectedPositionMode = GlobalCurrentInput.SelectedPositionMode;//选中的位置指令
            SelectedProfileMode = GlobalCurrentInput.SelectedProfileMode;//选中的规划曲线

            PositionUnit = GlobalCurrentInput.PositionUnit;
            TorqueUnit = GlobalCurrentInput.TorqueUnit;
            SpeedUnit = GlobalCurrentInput.SpeedUnit;
            AccelerationUnit = GlobalCurrentInput.AccelerationUnit;
            #endregion

            #region 参数调优
            ParameterTunningJogSpeed = GlobalCurrentInput.ParameterTunningJogSpeed;//Jog运动速度
            ParameterTunningJogAccelerationTime = GlobalCurrentInput.ParameterTunningJogAccelerationTime;//Jog加速时间
            ParameterTunningJogDecelerationTime = GlobalCurrentInput.ParameterTunningJogDecelerationTime;//Jog减速时间
            ParameterTunningJogTime = GlobalCurrentInput.ParameterTunningJogTime;//Jog运动时间
           
            SelectedParameterTunningMode = GlobalCurrentInput.SelectedParameterTunningMode;//选中的运动模式
            ParameterTunningProgramJogMovingDistance = GlobalCurrentInput.ParameterTunningProgramJogMovingDistance;//程序Jog移动距离
            ParameterTunningProgramJogMovingSpeed = GlobalCurrentInput.ParameterTunningProgramJogMovingSpeed;//程序Jog移动速度
            ParameterTunningProgramJogAccDecTime = GlobalCurrentInput.ParameterTunningProgramJogAccDecTime;//程序Jog加减速时间
            ParameterTunningProgramJogWaitTime = GlobalCurrentInput.ParameterTunningProgramJogWaitTime;//程序Jog等待时间
            ParameterTunningProgramJogMovingNumber = GlobalCurrentInput.ParameterTunningProgramJogMovingNumber;//程序Jog移动次数 

            PositionUnit = GlobalCurrentInput.PositionUnit;
            #endregion
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalFile
        //函数功能：赋值接口写入离线文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.06.18&2022.08.24
        //*************************************************************************
        private void InterfaceEvaluationToFile()
        {
            OfflineOscilloscope.Export = new OscilloscopeParameterSet();
            OfflineOscilloscope.Export.Channel1Name = GetSampleNameByIndex(SelectedSampleChannel1Index);
            OfflineOscilloscope.Export.Channel2Name = GetSampleNameByIndex(SelectedSampleChannel2Index);
            OfflineOscilloscope.Export.Channel3Name = GetSampleNameByIndex(SelectedSampleChannel3Index);
            OfflineOscilloscope.Export.Channel4Name = GetSampleNameByIndex(SelectedSampleChannel4Index);

            OfflineOscilloscope.Export.Period = SelectedSamplingPeriod;
            OfflineOscilloscope.Export.Duration = SelectedSamplingDuration;
            OfflineOscilloscope.Export.IsContinuous = SelectedContinuousSampling;
            OfflineOscilloscope.Export.Date = DateTime.Now.ToString();

            //OfflineOscilloscope.Export.OscilloscopePreset = GetSampleNameByIndex(SelectedOscilloscopePreset);

            OfflineOscilloscope.Export.TriggerMode = SelectedTriggerClockEdge;
            OfflineOscilloscope.Export.TriggerChannel = SelectedTriggerChannel;
            OfflineOscilloscope.Export.PreTrigger = SelectedPreTrigger;

            GetTriggerLevelValue(Method: "ToFile");
                 
            if (OscilloscopeProperty != null)
            {
                if (OscilloscopeProperty.Count == 4)
                {
                    OfflineOscilloscope.Export.Channel1Doubling = OscilloscopeProperty[0].Doubling;
                    OfflineOscilloscope.Export.Channel2Doubling = OscilloscopeProperty[1].Doubling;
                    OfflineOscilloscope.Export.Channel3Doubling = OscilloscopeProperty[2].Doubling;
                    OfflineOscilloscope.Export.Channel4Doubling = OscilloscopeProperty[3].Doubling;
                }
                else
                {
                    OfflineOscilloscope.Export.Channel1Doubling = "1";
                    OfflineOscilloscope.Export.Channel2Doubling = "1";
                    OfflineOscilloscope.Export.Channel3Doubling = "1";
                    OfflineOscilloscope.Export.Channel4Doubling = "1";
                }
            }
            else
            {
                OfflineOscilloscope.Export.Channel1Doubling = "1";
                OfflineOscilloscope.Export.Channel2Doubling = "1";
                OfflineOscilloscope.Export.Channel3Doubling = "1";
                OfflineOscilloscope.Export.Channel4Doubling = "1";
            }

            OfflineOscilloscope.Export.lstChannel1 = new List<int>();
            OfflineOscilloscope.Export.lstChannel2 = new List<int>();
            OfflineOscilloscope.Export.lstChannel3 = new List<int>();
            OfflineOscilloscope.Export.lstChannel4 = new List<int>();

            AcquisitionData.Channel1?.ForEach(item => OfflineOscilloscope.Export.lstChannel1.Add(item));
            AcquisitionData.Channel2?.ForEach(item => OfflineOscilloscope.Export.lstChannel2.Add(item));
            AcquisitionData.Channel3?.ForEach(item => OfflineOscilloscope.Export.lstChannel3.Add(item));
            AcquisitionData.Channel4?.ForEach(item => OfflineOscilloscope.Export.lstChannel4.Add(item));

            OfflineOscilloscope.Export.lstUnit = new List<string>();
            OfflineOscilloscope.Export.lstExchangeValue = new List<double>();

            AcquisitionInfoSet.lstUnit?.ForEach(item => OfflineOscilloscope.Export.lstUnit.Add(item));
            AcquisitionInfoSet.lstExchangeValue?.ForEach(item => OfflineOscilloscope.Export.lstExchangeValue.Add(item));
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalFile
        //函数功能：从离线文件赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.01.14
        //*************************************************************************
        private void InterfaceEvaluationFromFile()
        {
            try
            {
                SelectedSamplingPeriod = OfflineOscilloscope.Import.Period;
                SelectedContinuousSampling = OfflineOscilloscope.Import.IsContinuous;

                SelectedSampleChannel1Index = GetSampleIndexByName(OfflineOscilloscope.Import.Channel1Name);
                SelectedSampleChannel2Index = GetSampleIndexByName(OfflineOscilloscope.Import.Channel2Name);
                SelectedSampleChannel3Index = GetSampleIndexByName(OfflineOscilloscope.Import.Channel3Name);
                SelectedSampleChannel4Index = GetSampleIndexByName(OfflineOscilloscope.Import.Channel4Name);

                SelectedSamplingDuration = OfflineOscilloscope.Import.Duration;
                SelectedDisplayMethod = "连续采样-静态展示";

                //SelectedOscilloscopePreset = GetSampleIndexByName(OfflineOscilloscope.Import.OscilloscopePreset);

                SelectedTriggerClockEdge = OfflineOscilloscope.Import.TriggerMode;
                SelectedPreTrigger = OfflineOscilloscope.Import.PreTrigger;              
                SelectedTriggerChannel = OfflineOscilloscope.Import.TriggerChannel;

                GetTriggerLevelValue(Method: "FromFile");
           
                OscilloscopeProperty.Clear();
                for (int j = 0; j < 4; j++)
                {
                    OscilloscopePropertySet clsTemp = new OscilloscopePropertySet();
                    clsTemp.ChannelNumber = "通道-" + (j + 1);

                    switch (j)
                    {
                        case 0:
                            clsTemp.Doubling = OfflineOscilloscope.Import.Channel1Doubling;
                            break;
                        case 1:
                            clsTemp.Doubling = OfflineOscilloscope.Import.Channel2Doubling;
                            break;
                        case 2:
                            clsTemp.Doubling = OfflineOscilloscope.Import.Channel3Doubling;
                            break;
                        case 3:
                            clsTemp.Doubling = OfflineOscilloscope.Import.Channel4Doubling;
                            break;
                        default:
                            break;
                    }

                    clsTemp.IsHidden = false;
                    OscilloscopeProperty.Add(clsTemp);
                }

                AcquisitionData.Channel1 = new List<int>();
                AcquisitionData.Channel2 = new List<int>();
                AcquisitionData.Channel3 = new List<int>();
                AcquisitionData.Channel4 = new List<int>();

                OfflineOscilloscope.Import.lstChannel1?.ForEach(item => AcquisitionData.Channel1.Add(item));
                OfflineOscilloscope.Import.lstChannel2?.ForEach(item => AcquisitionData.Channel2.Add(item));
                OfflineOscilloscope.Import.lstChannel3?.ForEach(item => AcquisitionData.Channel3.Add(item));
                OfflineOscilloscope.Import.lstChannel4?.ForEach(item => AcquisitionData.Channel4.Add(item));

                AcquisitionInfoSet.lstUnit = new List<string>();
                AcquisitionInfoSet.lstExchangeValue = new List<double>();

                OfflineOscilloscope.Import.lstUnit.ForEach(item => AcquisitionInfoSet.lstUnit.Add(item));
                OfflineOscilloscope.Import.lstExchangeValue.ForEach(item => AcquisitionInfoSet.lstExchangeValue.Add(item));
            
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_INTERFACE_EVALUATION_FROM_FILE, "InterfaceEvaluationFromFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromLast
        //函数功能：载入上一条参数数据
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.18
        //*************************************************************************
        private int InterfaceEvaluationFromLast()
        {
            try
            {
                if (OfflineOscilloscope.Last == null)
                {
                    return RET.NO_EFFECT;
                }

                //如果上一次采集终止，将不会载入波形
                if (string.IsNullOrEmpty(OfflineOscilloscope.Last.Period) || string.IsNullOrEmpty(OfflineOscilloscope.Last.Duration) || string.IsNullOrEmpty(OfflineOscilloscope.Last.Channel1Name))
                {
                    return RET.NO_EFFECT;
                }

                //判断倍乘输入是否有效
                for (int i = 0; i < OscilloscopeProperty.Count; i++)
                {
                    if (!OthersHelper.IsInputNumber(OscilloscopeProperty[i].Doubling))
                    {
                        OscilloscopeProperty[i].Doubling = "1";
                    }
                }

                SelectedSamplingPeriod = OfflineOscilloscope.Last.Period;
                SelectedSamplingDuration = OfflineOscilloscope.Last.Duration;
                SelectedContinuousSampling = OfflineOscilloscope.Last.IsContinuous;

                SelectedSampleChannel1Index = GetSampleIndexByName(OfflineOscilloscope.Last.Channel1Name);
                SelectedSampleChannel2Index = GetSampleIndexByName(OfflineOscilloscope.Last.Channel2Name);
                SelectedSampleChannel3Index = GetSampleIndexByName(OfflineOscilloscope.Last.Channel3Name);
                SelectedSampleChannel4Index = GetSampleIndexByName(OfflineOscilloscope.Last.Channel4Name);

                //SelectedOscilloscopePresetIndex = GetSampleIndexByName(OfflineOscilloscope.Last.OscilloscopePreset);

                SelectedTriggerClockEdge = OfflineOscilloscope.Last.TriggerMode;
                SelectedPreTrigger = OfflineOscilloscope.Last.PreTrigger;
                TriggerLevel = OfflineOscilloscope.Last.TriggerLevel;

                SelectedTriggerChannel = OfflineOscilloscope.Last.TriggerChannel;
                SelectedDisplayMethod = "连续采样-静态展示";

                GetTriggerLevelValue(Method: "GetLast");

                AcquisitionData.Channel1 = new List<int>();
                AcquisitionData.Channel2 = new List<int>();
                AcquisitionData.Channel3 = new List<int>();
                AcquisitionData.Channel4 = new List<int>();

                OfflineOscilloscope.Last.lstChannel1?.ForEach(item => AcquisitionData.Channel1.Add(item));
                OfflineOscilloscope.Last.lstChannel2?.ForEach(item => AcquisitionData.Channel2.Add(item));
                OfflineOscilloscope.Last.lstChannel3?.ForEach(item => AcquisitionData.Channel3.Add(item));
                OfflineOscilloscope.Last.lstChannel4?.ForEach(item => AcquisitionData.Channel4.Add(item));

                AcquisitionInfoSet.lstUnit = new List<string>();
                AcquisitionInfoSet.lstExchangeValue = new List<double>();

                OfflineOscilloscope.Last.lstUnit?.ForEach(item => AcquisitionInfoSet.lstUnit.Add(item));
                OfflineOscilloscope.Last.lstExchangeValue?.ForEach(item => AcquisitionInfoSet.lstExchangeValue.Add(item));

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_INTERFACE_EVALUATION_FROM_LAST, "InterfaceEvaluationFromLast", ex);
                return RET.ERROR;
            }
        }
        #endregion

        #region 数据采集内部方法
        //*************************************************************************
        //函数名称：RefreshAcquisitionList
        //函数功能：更新采样List
        //
        //输入参数：None
        //
        //输出参数：None
        //
        //编码作者：Ryan
        //更新时间：2019.12.31
        //*************************************************************************
        private void RefreshAcquisitionList()
        {
            string strUnit = null;
            AcquisitionInfoSet.lstChannel = new List<string>();
            AcquisitionInfoSet.lstReceiving = new List<List<int>>();
            AcquisitionInfoSet.lstUnit = new List<string>();
            AcquisitionInfoSet.lstExchangeValue = new List<double>();
            AcquisitionInfoSet.lstChannelMapping = new List<int>(); // 清理通道映射列表

            // 创建通道ID到UI通道索引的映射，用于解决数据错位问题
            List<ChannelMapping> channelMappings = new List<ChannelMapping>();

            if (SelectedSampleChannel1Index != 0)
            {
                channelMappings.Add(new ChannelMapping { ChannelId = SelectedSampleChannel1Index, UiChannelIndex = 1 });
                AcquisitionInfoSet.lstChannel.Add("通道-1");
                AcquisitionInfoSet.lstReceiving.Add(new List<int>());
            }

            if (SelectedSampleChannel2Index != 0)
            {
                channelMappings.Add(new ChannelMapping { ChannelId = SelectedSampleChannel2Index, UiChannelIndex = 2 });
                AcquisitionInfoSet.lstChannel.Add("通道-2");
                AcquisitionInfoSet.lstReceiving.Add(new List<int>());
            }

            if (SelectedSampleChannel3Index != 0)
            {
                channelMappings.Add(new ChannelMapping { ChannelId = SelectedSampleChannel3Index, UiChannelIndex = 3 });
                AcquisitionInfoSet.lstChannel.Add("通道-3");
                AcquisitionInfoSet.lstReceiving.Add(new List<int>());
            }

            if (SelectedSampleChannel4Index != 0)
            {
                channelMappings.Add(new ChannelMapping { ChannelId = SelectedSampleChannel4Index, UiChannelIndex = 4 });
                AcquisitionInfoSet.lstChannel.Add("通道-4");
                AcquisitionInfoSet.lstReceiving.Add(new List<int>());
            }

            // 按照通道ID排序，与GetTransmittingContent中的排序保持一致
            channelMappings.Sort((x, y) => x.ChannelId.CompareTo(y.ChannelId));

            // 将排序后的映射关系存储到全局变量中，供数据接收时使用
            AcquisitionInfoSet.lstChannelMapping = channelMappings.Select(x => x.UiChannelIndex).ToList();

            // 重新按照排序后的顺序组织单位信息，确保与数据接收顺序一致
            List<string> sortedUnits = new List<string>();
            List<double> sortedExchangeValues = new List<double>();

            for (int i = 0; i < channelMappings.Count; i++)
            {
                var mapping = channelMappings[i];
                string channelName = GetSampleNameByIndex(mapping.ChannelId);

                SoftwareStateParameterSet.dicAcquisitionUint.TryGetValue(channelName, out strUnit);
                if (!string.IsNullOrEmpty(strUnit))
                {
                    sortedUnits.Add(strUnit);
                    sortedExchangeValues.Add(OthersHelper.GetExchangeValueByCurrentUnit(true, strUnit));
                }
                else
                {
                    sortedUnits.Add("");
                    sortedExchangeValues.Add(1.0);
                }
            }

            // 用排序后的单位信息替换原来的
            AcquisitionInfoSet.lstUnit = sortedUnits;
            AcquisitionInfoSet.lstExchangeValue = sortedExchangeValues;

            // 调试信息：记录通道映射关系
            System.Diagnostics.Debug.WriteLine("=== 通道映射关系 ===");
            for (int i = 0; i < channelMappings.Count; i++)
            {
                var mapping = channelMappings[i];
                string channelName = GetSampleNameByIndex(mapping.ChannelId);
                System.Diagnostics.Debug.WriteLine($"数据索引{i}: 通道ID={mapping.ChannelId}, UI通道={mapping.UiChannelIndex}, 通道名={channelName}, 单位={sortedUnits[i]}");
            }
        }

        //*************************************************************************
        //函数名称：GetTransmittingContent
        //函数功能：获取发送报文信息
        //
        //输入参数：ref List<TransmitingDataInfoSet> lstTransmittingDataInfo    报文
        //         
        //输出参数：0：NO_EFFECT
        //         1: SUCCEED
        //        -1: ERROR
        //       100: INVALID_INPUT
        //       101: INVALID_LENGTH_OF_INPUT
        //        
        //编码作者：Ryan
        //更新时间：2019.12.25
        //*************************************************************************
        private int GetTransmittingContent(ref List<TransmitingDataInfoSet> lstTransmittingDataInfo)
        {
            byte[] bChannelNumber = new byte[1];//通道个数
            short sSamplingPeriod = 0;//采样周期
            int iSampingDuration = 0;//采样时间
            short sSampingLength = 0;//采样总长
            byte[] bTriggerMode = new byte[1];//触发模式
            byte[] bTriggerChannel = new byte[1];//触发通道
            short sPreTrigger = 0;//预触发时长
            int iTriggerLevel = 0;//触发水平

            string strChannelNumber = null;
            string strSamplingPeriod = null;
            string strSamplingLength = null;
            string strTriggerMode = null;
            string strTriggerChannel = null;
            string strPreTrigger = null;
            string strTriggerLevel = null;
            string strChannel = null;
            string strMessage = null;

            lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取通道个数
                if (SelectedSampleChannel1Index != 0) bChannelNumber[0]++;
                if (SelectedSampleChannel2Index != 0) bChannelNumber[0]++;
                if (SelectedSampleChannel3Index != 0) bChannelNumber[0]++;
                if (SelectedSampleChannel4Index != 0) bChannelNumber[0]++;
                if (bChannelNumber[0] == 0)
                {
                    return RET.ERROR;
                }

                List<int> channelIds = new List<int>();
                if (SelectedSampleChannel1Index != 0) channelIds.Add(SelectedSampleChannel1Index);
                if (SelectedSampleChannel2Index != 0) channelIds.Add(SelectedSampleChannel2Index);
                if (SelectedSampleChannel3Index != 0) channelIds.Add(SelectedSampleChannel3Index);
                if (SelectedSampleChannel4Index != 0) channelIds.Add(SelectedSampleChannel4Index);
                
                channelIds.Sort();

                strChannel = string.Concat(channelIds.Select(id => GetSampleAddressByIndex(id)));

                iSampingDuration = Convert.ToInt32(Convert.ToDouble(SelectedSamplingDuration.Replace("ms", "")) * 1000);

                //采样周期与采样点个数
                if (SelectedSamplingPeriod.IndexOf("μ") != -1)
                {
                    sSamplingPeriod = Convert.ToInt16(Convert.ToDouble(SelectedSamplingPeriod.Replace("μs", "")) / 62.5);
                    sSampingLength = Convert.ToInt16(iSampingDuration / (Convert.ToDouble(SelectedSamplingPeriod.Replace("μs", ""))));
                }
                else
                {
                    sSamplingPeriod = Convert.ToInt16(Convert.ToDouble(SelectedSamplingPeriod.Replace("ms", "")) * 1000 / 62.5);
                    sSampingLength = Convert.ToInt16(iSampingDuration / ((Convert.ToDouble(SelectedSamplingPeriod.Replace("ms", ""))) * 1000));
                }

                //触发模式
                switch (SelectedTriggerClockEdge)
                {
                    case "无":
                        bTriggerMode[0] = 0;
                        break;
                    case "上升沿触发":
                        bTriggerMode[0] = 1;
                        break;
                    case "下降沿触发":
                        bTriggerMode[0] = 2;
                        break;
                    case "双边沿触发":
                        bTriggerMode[0] = 3;
                        break;
                    case "水平触发-高":
                        bTriggerMode[0] = 4;
                        break;
                    case "水平触发-低":
                        bTriggerMode[0] = 5;
                        break;
                    default:
                        bTriggerMode[0] = 0;
                        break;
                }

                //触发通道
                switch (SelectedTriggerChannel)
                {
                    case "通道-1":
                        bTriggerChannel[0] = 1;
                        break;
                    case "通道-2":
                        bTriggerChannel[0] = 2;
                        break;
                    case "通道-3":
                        bTriggerChannel[0] = 3;
                        break;
                    case "通道-4":
                        bTriggerChannel[0] = 4;
                        break;
                    default:
                        bTriggerChannel[0] = 1;
                        break;
                }

                //预触发时长
                switch (SelectedPreTrigger)
                {
                    case "0":
                        sPreTrigger = 0;
                        break;
                    case "10%":
                        sPreTrigger = Convert.ToInt16(Math.Floor(sSampingLength * 0.1));
                        break;
                    case "20%":
                        sPreTrigger = Convert.ToInt16(Math.Floor(sSampingLength * 0.2));
                        break;
                    case "30%":
                        sPreTrigger = Convert.ToInt16(Math.Floor(sSampingLength * 0.3));
                        break;
                    case "50%":
                        sPreTrigger = Convert.ToInt16(Math.Floor(sSampingLength * 0.5));
                        break;
                    case "70%":
                        sPreTrigger = Convert.ToInt16(Math.Floor(sSampingLength * 0.7));
                        break;
                    default:
                        sPreTrigger = 0;
                        break;
                }

                //触发水平
                if (SelectedTriggerClockEdge != "无")
                {
                    if (string.IsNullOrEmpty(TriggerLevel))
                    {
                        return 2012;
                    }

                    if (!OthersHelper.IsInputNumber(TriggerLevel))
                    {
                        return 2008;
                    }

                    if (Unit == "" || Unit == "V" || Unit == "mA" || Unit == "‰" || Unit == "Pulse" || Unit == "1")
                    {
                        iTriggerLevel = Convert.ToInt32(Math.Round(Convert.ToDouble(TriggerLevel), MidpointRounding.AwayFromZero));
                    }
                    else if (Unit == "cnt/s" || Unit == "rpm" || Unit == "rps" || Unit == "deg/s" || Unit == "mm/s")
                    {
                        iTriggerLevel = Convert.ToInt32(Math.Round(Convert.ToDouble(OthersHelper.ExchangeUnit("Trigger Level", Unit + "-" + DefaultUnit.SpeedUnit, TriggerLevel)), MidpointRounding.AwayFromZero));
                    }
                    else if (Unit == "cnt" || Unit == "revs" || Unit == "deg" || Unit == "mm")
                    {
                        iTriggerLevel = Convert.ToInt32(Math.Round(Convert.ToDouble(OthersHelper.ExchangeUnit("Trigger Level", Unit + "-" + DefaultUnit.PositionUnit, TriggerLevel)), MidpointRounding.AwayFromZero));
                    }
                }

                //10进制转换16进制字符串
                strChannelNumber = BitConverter.ToString(bChannelNumber).Replace("-", "");
                strSamplingPeriod = BitConverter.ToString(BitConverter.GetBytes(sSamplingPeriod)).Replace("-", "");
                strSamplingLength = BitConverter.ToString(BitConverter.GetBytes(sSampingLength)).Replace("-", "");
                strTriggerMode = BitConverter.ToString(bTriggerMode).Replace("-", "");

                if (bTriggerMode[0] == 0)
                {
                    strTriggerChannel = "00";
                    strTriggerLevel = "FFFFFFFF";
                    strPreTrigger = "0000";
                }
                else
                {
                    strTriggerChannel = BitConverter.ToString(bTriggerChannel).Replace("-", "");
                    strTriggerLevel = BitConverter.ToString(BitConverter.GetBytes(iTriggerLevel)).Replace("-", "");
                    strPreTrigger = BitConverter.ToString(BitConverter.GetBytes(sPreTrigger)).Replace("-", "");
                }

                strMessage = strChannelNumber + strSamplingPeriod + strSamplingLength + strTriggerMode + strTriggerChannel + strTriggerLevel + strPreTrigger + strChannel;
                lstTransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = strMessage });

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_GET_TRANSMITING_CONTENT, "GetTransmittingContent-TriggerLevel: " + TriggerLevel, ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：RecordLastSamplingParameter
        //函数功能：上一个采样参数记录
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.18
        //*************************************************************************
        private void RecordLastSamplingParameter()
        {
            OfflineOscilloscope.Last.Period = SelectedSamplingPeriod;
            OfflineOscilloscope.Last.IsContinuous = SelectedContinuousSampling;

            OfflineOscilloscope.Last.Channel1Name = SelectedSamplingChannel1;
            OfflineOscilloscope.Last.Channel2Name = SelectedSamplingChannel2;
            OfflineOscilloscope.Last.Channel3Name = SelectedSamplingChannel3;
            OfflineOscilloscope.Last.Channel4Name = SelectedSamplingChannel4;

            OfflineOscilloscope.Last.Duration = SelectedSamplingDuration;

            OfflineOscilloscope.Last.TriggerMode = SelectedTriggerClockEdge;
            OfflineOscilloscope.Last.PreTrigger = SelectedPreTrigger;           
            OfflineOscilloscope.Last.TriggerChannel = SelectedTriggerChannel;

            GetTriggerLevelValue(Method: "RecordLast");

            for (int i = 0; i < 4; i++)
            {
                GlobalCurrentInput.DoublingChannel[i] = OscilloscopeProperty[i].Doubling;
            }
        }

        //*************************************************************************
        //函数名称：ResetLastWavaCalculate
        //函数功能：上一条波形计算信息重置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.23
        //*************************************************************************
        private void ResetLastWavaCalculate()
        {
            dLastTime = 0;//获取上一次X坐标-时间
            iIndex = 0;//获取上一个检索号
            dLastCH1Value = 0;//获取上一次CH1值
            dLastCH2Value = 0;//获取上一次CH2值
            dLastCH3Value = 0;//获取上一次CH3值
            dLastCH4Value = 0;//获取上一次CH4值
        }

        //*************************************************************************
        //函数名称：GetSampleAddressByIndex
        //函数功能：通过索引号获取采样地址
        //
        //输入参数：int Index   索引号
        //         
        //输出参数：string Name  采样地址
        //        
        //编码作者：Ryan
        //更新时间：2021.01.15
        //*************************************************************************
        private string GetSampleAddressByIndex(int Index)
        {
            if (Index == 0) return string.Empty;
            var query = SampleChannelInfo1.FirstOrDefault(o => o.ID == Index);
            if (query != null)
            {
                return Convert.ToString(query.Address);
            }
            else
            {
                return string.Empty;
            }
        }

        //*************************************************************************
        //函数名称：GetSampleNameByIndex
        //函数功能：通过索引号获取采样名称
        //
        //输入参数：int Index   索引号
        //         
        //输出参数：string Name  采样名称
        //        
        //编码作者：Ryan
        //更新时间：2021.01.15
        //*************************************************************************
        private string GetSampleNameByIndex(int Index)
        {
            if (Index == 0) return "停用";
            var query = SampleChannelInfo1.FirstOrDefault(o => o.ID == Index);
            if (query != null)
            {
                return Convert.ToString(query.ItemName);
            }
            else
            {
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetSampleIndexByName
        //函数功能：通过名称获取采样索引号
        //
        //输入参数：string Name  采样名称
        //         
        //输出参数：int Index    采样索引号
        //        
        //编码作者：Ryan
        //更新时间：2021.01.15
        //*************************************************************************
        private int GetSampleIndexByName(string Name)
        {
            var query = SampleChannelInfo1.FirstOrDefault(o => o.ItemName == Name);
            if (query != null)
            {
                return Convert.ToInt32(query.ID);
            }
            else
            {
                return 0;
            }
        }
        #endregion

        #region 函数发生器和运动调试内部方法
        //*************************************************************************
        //函数名称：StartFunctionGeneratorEvaluation
        //函数功能：开启函数发生器赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.02.23
        //*************************************************************************
        private void StartFunctionGeneratorEvaluation(ref FunctionGeneratorSet clsFunctionGeneratorSet)
        {
            clsFunctionGeneratorSet = new FunctionGeneratorSet();

            clsFunctionGeneratorSet.InnerSourceNumber = InnerSourceNumber;
            clsFunctionGeneratorSet.InnerSourceFrequency = InnerSourceFrequency;

            if (SelectedInnerSourceEffectIndex == "0")
            {
                clsFunctionGeneratorSet.InnerSourceGradient = OthersHelper.ExchangeUnit("Function Generater Slope", CurrentUnit.Position + "-" + DefaultUnit.PositionUnit, InnerSourceGradient);
                clsFunctionGeneratorSet.InnerSourceAmplitude = OthersHelper.ExchangeUnit("Function Generater Amplitude", CurrentUnit.Position + "-" + DefaultUnit.PositionUnit, InnerSourceAmplitude);
                clsFunctionGeneratorSet.InnerSourceEffectIndex = "-1";
            }
            else if (SelectedInnerSourceEffectIndex == "1")
            {
                clsFunctionGeneratorSet.InnerSourceGradient = OthersHelper.ExchangeUnit("Function Generater Slope", CurrentUnit.Speed + "-" + DefaultUnit.SpeedUnit, InnerSourceGradient);
                clsFunctionGeneratorSet.InnerSourceAmplitude = OthersHelper.ExchangeUnit("Function Generater Amplitude", CurrentUnit.Speed + "-" + DefaultUnit.SpeedUnit, InnerSourceAmplitude);
                clsFunctionGeneratorSet.InnerSourceEffectIndex = "-3";
            }
            else
            {
                clsFunctionGeneratorSet.InnerSourceGradient = OthersHelper.ExchangeUnit("Function Generater Slope", CurrentUnit.Torque + "-" + DefaultUnit.TorqueUnit, InnerSourceGradient);
                clsFunctionGeneratorSet.InnerSourceAmplitude = OthersHelper.ExchangeUnit("Function Generater Amplitude", CurrentUnit.Torque + "-" + DefaultUnit.TorqueUnit, InnerSourceAmplitude);
                clsFunctionGeneratorSet.InnerSourceEffectIndex = "-4";
            }

            switch (SelectedInnerSourceTypeIndex)
            {
                case "0":
                    clsFunctionGeneratorSet.InnerSourceTypeIndex = "2";
                    break;
                case "1":
                    clsFunctionGeneratorSet.InnerSourceTypeIndex = "3";
                    break;
                case "2":
                    clsFunctionGeneratorSet.InnerSourceTypeIndex = "1";
                    break;
                case "3":
                    clsFunctionGeneratorSet.InnerSourceTypeIndex = "4";
                    break;
                default:
                    break;
            }
        }

        //*************************************************************************
        //函数名称：PositionModeAction
        //函数功能：位置模式运动
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.25
        //*************************************************************************
        public void PositionModeAction(bool bSwitch)
        {
            int iRet = -1;
            string strTaskName = null;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取任务名称
                if (SelectedPositionMode == "绝对位置模式")
                {
                    strTaskName = TaskName.AbsolutePositionAction;
                }
                else
                {
                    strTaskName = TaskName.RelativePositionAction;
                }

                //获取当前页面名称
                SoftwareStateParameterSet.CurrentPageName = PageName.ACTIONDEBUG;

                //位置模式开
                if (bSwitch)
                {
                    //获取参数字典-并单位转换
                    GetPositionActionParameter(ref dicParameterInfo);

                    //获取参数详细信息
                    iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(RET.ERROR);
                        return;
                    }

                    //判断输入参数是否正确
                    iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo);
                    if (iRet == RET.NO_EFFECT)
                    {
                        ShowNotification(2008);
                        return;
                    }
                    else if (iRet == RET.ERROR)
                    {
                        return;
                    }

                    //获取发送任务信息
                    ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                    //下达控制字任务                  
                    iRet = OthersHelper.WriteControlWordSet(false, -1, strTaskName);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(1001);
                        return;
                    }
                    else
                    {
                        if (ControlWordSet.ListValue.Count == 4)
                        {
                            OthersHelper.GetWindowsStartupPosition();
                            if (MessageBoxService.ShowMessage("是否伺服使能...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.No)
                            {
                                return;
                            }
                        }
                    }

                    ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.ACTIONDEBUG, strTaskName, lstTransmittingDataInfo);
                }
                else//位置模式关 
                {
                    iRet = OthersHelper.WriteControlWordSet(true, 0, TaskName.StopPositionAction);
                    if (iRet == RET.NO_EFFECT)
                    {
                        ShowNotification(RET.ERROR);
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_POSITION_MODE_ACTION, "PositionModeAction", ex);
            }
        }

        //*************************************************************************
        //函数名称：SpeedModeAction
        //函数功能：速度模式运动
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.25
        //*************************************************************************
        private void SpeedModeAction(bool bSwitch)
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取当前页面名称
                SoftwareStateParameterSet.CurrentPageName = PageName.ACTIONDEBUG;

                //获取参数字典-并单位转换
                GetSpeedActionParameter(bSwitch, ref dicParameterInfo);

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                //下达控制字任务-只有在开启的时候
                if (bSwitch)
                {
                    iRet = OthersHelper.WriteControlWordSet(false, -1, TaskName.SpeedAction);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(1001);
                        return;
                    }
                    else
                    {
                        if (ControlWordSet.ListValue.Count == 3)//6,7,15
                        {
                            OthersHelper.GetWindowsStartupPosition();
                            if (MessageBoxService.ShowMessage("是否伺服使能...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.No)
                            {
                                return;
                            }
                        }
                    }
                }

                //信息确认
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.ACTIONDEBUG, TaskName.SpeedAction, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_SPEED_MODE_ACTION, "SpeedModeAction", ex);
            }
        }

        //*************************************************************************
        //函数名称：TorqueModeAction
        //函数功能：转矩模式运动
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.25
        //*************************************************************************
        private void TorqueModeAction(bool bSwitch)
        {
            int iRet = -1;            
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {              
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取当前页面名称
                SoftwareStateParameterSet.CurrentPageName = PageName.ACTIONDEBUG;

                //获取参数字典-并单位转换
                GetTorqueActionParameter(bSwitch, ref dicParameterInfo);

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                //下达控制字任务-只有在开启的时候
                if (bSwitch)
                {
                    iRet = OthersHelper.WriteControlWordSet(false, -1, TaskName.TorqueAction);
                    if (iRet != RET.SUCCEEDED)
                    {
                        ShowNotification(1001);
                        return;
                    }
                    else
                    {
                        if (ControlWordSet.ListValue.Count == 3)//6,7,15
                        {
                            OthersHelper.GetWindowsStartupPosition();
                            if (MessageBoxService.ShowMessage("是否伺服使能...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.No)
                            {
                                return;
                            }
                        }
                    }
                }

                //信息确认
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.ACTIONDEBUG, TaskName.TorqueAction, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_TORQUE_MODE_ACTION, "TorqueModeAction", ex);
            }
        }

        //*************************************************************************
        //函数名称：SpeedModeParameterTunning
        //函数功能：速度模式参数调优
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.22
        //*************************************************************************
        private void SpeedModeParameterTunning(bool bSwitch)
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取当前页面名称
                SoftwareStateParameterSet.CurrentPageName = PageName.PARAMETERTUNNING;

                //获取参数字典-并单位转换
                GetSpeedParameterTunningParameter(bSwitch, ref dicParameterInfo);

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                //下达控制字任务-只有在开启的时候
                if (bSwitch)
                {
                    OthersHelper.GetWindowsStartupPosition();
                    if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING)
                    {
                        string a = HexHelper.GetStatusWord_HighEightBit(0, Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x604100")));
                        if (HexHelper.GetStatusWord_HighEightBit(0, Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x604100"))) == "0")
                        {
                            ViewModelSet.Main?.ShowHintInfo("伺服正在其他运动中，请先禁能...");
                            return;
                        }
                        else
                        {
                            //ViewModelSet.Main?.ShowHintInfo("伺服正在其他运动中，请先禁能...");
                        }
                    }
                    else
                    {
                        if (MessageBoxService.ShowMessage("是否伺服使能...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.No)
                        {
                            return;
                        }
                    }
                }

                //信息确认
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.PARAMETERTUNNING, TaskName.SpeedParameterTunning, lstTransmittingDataInfo);

            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_SPEED_MODE_PARAMETERTUNNING, "SpeedModeParameterTunning", ex);
            }
        }

        //*************************************************************************
        //函数名称：PositionModeParameterTunning
        //函数功能：位置模式参数调优
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.22
        //*************************************************************************
        private void PositionModeParameterTunning(bool bSwitch)
        {
            int iRet = -1;
            Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
            List<ParameterReadWriteSet> lstParameterInfo = new List<ParameterReadWriteSet>();
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {                
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取当前页面名称
                SoftwareStateParameterSet.CurrentPageName = PageName.PARAMETERTUNNING;

                //获取参数字典-并单位转换
                GetPositionParameterTunningParameter(bSwitch, ref dicParameterInfo);

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

                //下达控制字任务-只有在开启的时候
                if (bSwitch)
                {
                    OthersHelper.GetWindowsStartupPosition();
                    if (SoftwareStateParameterSet.ServoStatus == ServoStatus.RUNING)
                    {
                        string a = HexHelper.GetStatusWord_HighEightBit(0, Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x604100")));
                        if (HexHelper.GetStatusWord_HighEightBit(0, Convert.ToUInt16(OthersHelper.GetCurrentValueOfIndex("0x604100"))) == "0")
                        {
                            ViewModelSet.Main?.ShowHintInfo("伺服正在其他运动中，请先禁能...");
                            return;
                        }
                        else
                        {
                            //ViewModelSet.Main?.ShowHintInfo("伺服正在其他运动中，请先禁能...");
                        }
                    }
                    else
                    {                       
                        if (MessageBoxService.ShowMessage("是否伺服使能...", "请确定", MessageButton.YesNo, MessageIcon.Warning) == MessageResult.No)
                        {
                            return;
                        }
                    }
                }

                //信息确认
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.PARAMETERTUNNING, TaskName.PositionParameterTunning, lstTransmittingDataInfo);
                
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_POSITION_MODE_PARAMETERTUNNING, "PositionModeParameterTunning", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetPositionActionParameter
        //函数功能：获取位置运动参数
        //
        //输入参数：bool bSwitch                                                             转矩运动开关
        //                 ref Dictionary<string, string> dicParameterInfo      参数字典
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.15
        //*************************************************************************
        private void GetPositionActionParameter(ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo.Add("Target Position", OthersHelper.ExchangeUnit("Target Position", CurrentUnit.Position + "-" + DefaultUnit.PositionUnit, TargetPosition));
            dicParameterInfo.Add("Profile Velocity", OthersHelper.ExchangeUnit("Profile Velocity", CurrentUnit.Speed + "-" + DefaultUnit.SpeedUnit, ProfileVelocity));
            dicParameterInfo.Add("End Profile Velocity", OthersHelper.ExchangeUnit("End Profile Velocity", CurrentUnit.Speed + "-" + DefaultUnit.SpeedUnit, EndProfileVelocity));
            dicParameterInfo.Add("Profile Acceleration", OthersHelper.ExchangeUnit("Profile Acceleration", CurrentUnit.Acceleration + "-" + DefaultUnit.AccelerationUnit, ProfileAcceleration));
            dicParameterInfo.Add("Profile Deceleration", OthersHelper.ExchangeUnit("Profile Deceleration", CurrentUnit.Acceleration + "-" + DefaultUnit.AccelerationUnit, ProfileDeceleration));
            dicParameterInfo.Add("Modes Of Operation", "1");
        }

        //*************************************************************************
        //函数名称：GetSpeedActionParameter
        //函数功能：获取速度运动参数
        //
        //输入参数：bool bSwitch                                                             转矩运动开关
        //                 ref Dictionary<string, string> dicParameterInfo      参数字典
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.15
        //*************************************************************************
        private void GetSpeedActionParameter(bool bSwitch, ref Dictionary<string, string> dicParameterInfo)
        {
            if (bSwitch)//打开速度模式运动
            {
                if (SelectedProfileMode == "斜坡曲线")
                {
                    dicParameterInfo.Add("Target Velocity", OthersHelper.ExchangeUnit("Target Velocity", CurrentUnit.Speed + "-" + DefaultUnit.SpeedUnit, TargetVelocity));
                    dicParameterInfo.Add("Profile Acceleration", OthersHelper.ExchangeUnit("Profile Acceleration", CurrentUnit.Acceleration + "-" + DefaultUnit.AccelerationUnit, ProfileAcceleration));
                    dicParameterInfo.Add("Profile Deceleration", OthersHelper.ExchangeUnit("Profile Deceleration", CurrentUnit.Acceleration + "-" + DefaultUnit.AccelerationUnit, ProfileDeceleration));
                    dicParameterInfo.Add("Modes Of Operation", "3");
                    dicParameterInfo.Add("Motion Profile Type", "0");
                }
                else
                {
                    dicParameterInfo.Add("Target Velocity", OthersHelper.ExchangeUnit("Target Velocity", CurrentUnit.Speed + "-" + DefaultUnit.SpeedUnit, TargetVelocity));
                    dicParameterInfo.Add("Profile Acceleration", OthersHelper.ExchangeUnit("Profile Acceleration", CurrentUnit.Acceleration + "-" + DefaultUnit.AccelerationUnit, ProfileAcceleration));
                    dicParameterInfo.Add("Profile Deceleration", OthersHelper.ExchangeUnit("Profile Deceleration", CurrentUnit.Acceleration + "-" + DefaultUnit.AccelerationUnit, ProfileDeceleration));
                    dicParameterInfo.Add("Profile Jerk 1", ProfileJerk1);
                    dicParameterInfo.Add("Profile Jerk 2", ProfileJerk2);
                    dicParameterInfo.Add("Profile Jerk 3", ProfileJerk3);
                    dicParameterInfo.Add("Profile Jerk 4", ProfileJerk4);
                    dicParameterInfo.Add("Modes Of Operation", "3");
                    dicParameterInfo.Add("Motion Profile Type", "2");
                }
            }
            else//关闭速度模式运动
            {
                if (SelectedProfileMode == "斜坡曲线")
                {
                    dicParameterInfo.Add("Target Velocity", "0");
                    dicParameterInfo.Add("Profile Acceleration", OthersHelper.ExchangeUnit("Profile Acceleration", CurrentUnit.Acceleration + "-" + DefaultUnit.AccelerationUnit, ProfileAcceleration));
                    dicParameterInfo.Add("Profile Deceleration", OthersHelper.ExchangeUnit("Profile Deceleration", CurrentUnit.Acceleration + "-" + DefaultUnit.AccelerationUnit, ProfileDeceleration));
                    dicParameterInfo.Add("Modes Of Operation", "3");
                    dicParameterInfo.Add("Motion Profile Type", "0");
                }
                else
                {
                    dicParameterInfo.Add("Target Velocity", "0");
                    dicParameterInfo.Add("Profile Acceleration", OthersHelper.ExchangeUnit("Profile Acceleration", CurrentUnit.Acceleration + "-" + DefaultUnit.AccelerationUnit, ProfileAcceleration));
                    dicParameterInfo.Add("Profile Deceleration", OthersHelper.ExchangeUnit("Profile Deceleration", CurrentUnit.Acceleration + "-" + DefaultUnit.AccelerationUnit, ProfileDeceleration));
                    dicParameterInfo.Add("Profile Jerk 1", ProfileJerk1);
                    dicParameterInfo.Add("Profile Jerk 2", ProfileJerk2);
                    dicParameterInfo.Add("Profile Jerk 3", ProfileJerk3);
                    dicParameterInfo.Add("Profile Jerk 4", ProfileJerk4);
                    dicParameterInfo.Add("Modes Of Operation", "3");
                    dicParameterInfo.Add("Motion Profile Type", "2");
                }
            }
        }

        //*************************************************************************
        //函数名称：GetTorqueActionParameter
        //函数功能：获取转矩运动参数
        //
        //输入参数：bool bSwitch                                                             转矩运动开关
        //                 ref Dictionary<string, string> dicParameterInfo      参数字典
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.15
        //*************************************************************************
        private void GetTorqueActionParameter(bool bSwitch, ref Dictionary<string, string> dicParameterInfo)
        {
            if (bSwitch)
            {
                dicParameterInfo.Add("Target Torque", OthersHelper.ExchangeUnit("Target Torque", CurrentUnit.Torque + "-" + DefaultUnit.TorqueUnit, TargetTorque));
                dicParameterInfo.Add("Torque Slope", OthersHelper.ExchangeUnit("Torque Slope", CurrentUnit.Torque + "-" + DefaultUnit.TorqueUnit, TorqueSlope));
                dicParameterInfo.Add("Modes Of Operation", "4");
            }
            else
            {
                dicParameterInfo.Add("Target Torque", "0");
                dicParameterInfo.Add("Torque Slope", OthersHelper.ExchangeUnit("Torque Slope", CurrentUnit.Torque + "-" + DefaultUnit.TorqueUnit, TorqueSlope));
                dicParameterInfo.Add("Modes Of Operation", "4");
            }
        }

        //*************************************************************************
        //函数名称：GetSpeedParameterTunningParameter
        //函数功能：获取参数调优速度模式参数
        //
        //输入参数：bool bSwitch                                                             参数调优速度运动开关
        //                 ref Dictionary<string, string> dicParameterInfo      参数字典
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.22
        //*************************************************************************
        private void GetSpeedParameterTunningParameter(bool bSwitch, ref Dictionary<string, string> dicParameterInfo)
        {
            if (bSwitch)
            {
                dicParameterInfo.Add("Jog Time", ParameterTunningJogTime);                
                dicParameterInfo.Add("Jog Acceleration Time", ParameterTunningJogAccelerationTime);
                dicParameterInfo.Add("Jog Deceleration Time", ParameterTunningJogDecelerationTime);
                dicParameterInfo.Add("Jog Speed", ParameterTunningJogSpeed);

                dicParameterInfo.Add("Motor Jog Command", "17");
            }
            else
            {               
                dicParameterInfo.Add("Motor Jog Command", "16");
            }
        }

        //*************************************************************************
        //函数名称：GetPositionParameterTunningParameter
        //函数功能：获取参数调优位置模式参数
        //
        //输入参数：bool bSwitch                                                             参数调优位置运动开关
        //                 ref Dictionary<string, string> dicParameterInfo      参数字典
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.22
        //*************************************************************************
        private void GetPositionParameterTunningParameter(bool bSwitch, ref Dictionary<string, string> dicParameterInfo)
        {
            string strProgramJogSwitchIndexValue = null;

            if (bSwitch)
            {
                switch (SelectedProgramJogSwitchIndex)
                {
                    case "0":
                        strProgramJogSwitchIndexValue = "4";
                        break;
                    case "1":
                        strProgramJogSwitchIndexValue = "5";
                        break;
                    case "2":
                        strProgramJogSwitchIndexValue = "2";
                        break;
                    case "3":
                        strProgramJogSwitchIndexValue = "3";
                        break;
                    case "4":
                        strProgramJogSwitchIndexValue = "0";
                        break;
                    case "5":
                        strProgramJogSwitchIndexValue = "1";
                        break;                    
                    default:
                        break;
                }                
                dicParameterInfo.Add("Program Jog Moving Distance", OthersHelper.ExchangeUnit("Program Jog Moving Distance", PositionUnit + "-" + DefaultUnit.PositionUnit, ParameterTunningProgramJogMovingDistance));//程序JOG移动距离
                dicParameterInfo.Add("Program Jog Moving Speed", ParameterTunningProgramJogMovingSpeed);//程序JOG移动速度
                dicParameterInfo.Add("Program Jog AccDec Time", ParameterTunningProgramJogAccDecTime);//程序JOG加减速时间
                dicParameterInfo.Add("Program Jog Wait Time", ParameterTunningProgramJogWaitTime);//程序JOG等待时间
                dicParameterInfo.Add("Program Jog Moving Number", ParameterTunningProgramJogMovingNumber);//程序JOG移动次数
                //dicParameterInfo.Add("Program Jog Switch", SelectedProgramJogSwitchIndex);//程序JOG运行模式
                dicParameterInfo.Add("Program Jog Switch", strProgramJogSwitchIndexValue);//程序JOG运行模式
                dicParameterInfo.Add("Motor Program Jog Command", "17");
            }
            else
            {                
                dicParameterInfo.Add("Motor Program Jog Command", "16");
            }
        }

        //*************************************************************************
        //函数名称：GetActionMode
        //函数功能：获取运动模式
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.28
        //*************************************************************************
        private void GetActionMode()
        {
            if (SelectedActionMode == "位置模式")
            {
                if (SelectedPositionMode == "绝对位置模式")
                {
                    strActionMode = "位置模式-绝对";
                }
                else
                {
                    strActionMode = "位置模式-相对";
                }
            }
            else if (SelectedActionMode == "速度模式")
            {
                if (SelectedProfileMode == "斜坡曲线")
                {
                    strActionMode = "速度模式-斜坡曲线";
                    IsSlopeActionEnabled = ControlVisibility.Visible;
                    IsJerkFreeActionEnabled = ControlVisibility.Collapsed;
                }
                else
                {
                    strActionMode = "速度模式-Jerk Free曲线";
                    IsSlopeActionEnabled = ControlVisibility.Visible;
                    IsJerkFreeActionEnabled = ControlVisibility.Visible;
                }
            }
            else
            {
                strActionMode = "转矩模式";
            }
        }

        //*************************************************************************
        //函数名称：GetParameterTunningMode
        //函数功能：获取参数调优模式
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2022.11.22
        //*************************************************************************
        private void GetParameterTunningMode()
        {
            if (SelectedParameterTunningMode == "位置模式")
            {
                strParameterTunningMode = "位置模式";                
            }           
            else
            {
                strParameterTunningMode = "速度模式";
            }
        }
        #endregion

        #region 其他
        //*************************************************************************
        //函数名称：AllThreadClosedDispose
        //函数功能：所有线程全部关闭
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.02
        //*************************************************************************
        private void AllThreadClosedDispose()
        {
            List<TransmitingDataInfoSet> lstTransmitingDataInfoSet = new List<TransmitingDataInfoSet>();

            //清空所有采样任务
            OthersHelper.ClearAllAcquisitionTask();

            //状态栏提示
            ViewModelSet.Main?.RefreshSerialPortWorkState(AcquisitionInfoSet.CurrentProcess);

            //按钮状态更改
            OthersButtonEnabled = true;

            //画图已经完成
            AcquisitionInfoSet.IsDrawingCompleted = true;

            //连续采样次数
            AcquisitionInfoSet.ContinuousAcquisitionTimes = 0;

            //停止采样任务         
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_StopAcquisition(null, TaskName.StopAcquisition, lstTransmitingDataInfoSet);
        }

        //*************************************************************************
        //函数名称：GetSamplingDurationUnit
        //函数功能：获取单位信息
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        private void GetSamplingDurationUnit(string strDaration)
        {
            if (string.IsNullOrEmpty(strDaration))
            {
                SamplingDurationUnit = "采样时长";
            }
            else
            {
                if (strDaration.IndexOf("μ") != -1)
                {
                    SamplingDurationUnit = "采样时长" + "   " + "单位：μs";
                }
                else if (strDaration.IndexOf("m") != -1)
                {
                    SamplingDurationUnit = "采样时长" + "   " + "单位：ms";
                }
                else
                {
                    SamplingDurationUnit = "采样时长" + "   " + "单位：s";
                }
            }
        }


        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.02&2021.07.05&2022.11.09
        //*************************************************************************
        private int AddParameterInfoDictionary(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                switch (strCategory)
                {
                    case "0":
                        break;
                    case "1":
                        dicParameterInfo.Add("Position Loop Gain", PositionLoopGain);
                        dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);//负载惯量比     由Lilbert增加负载惯量比
                        dicParameterInfo.Add("First Trqcmd Filter Time", FirstTrqcmdFilterTime);//第一转矩滤波时间     由Lilbert增加第一转矩滤波时间
                        dicParameterInfo.Add("Speed Loop Gain", SpeedLoopGain);
                        dicParameterInfo.Add("Speed Loop Time Constant", SpeedLoopTimeConstant);
                        dicParameterInfo.Add("Current Loop Gain", CurrentLoopGain);
                        dicParameterInfo.Add("Current Loop Time Constant", CurrentLoopTimeConstant);
                        dicParameterInfo.Add("Rigidity", Rigidity);//刚性     由Lilbert增加刚性
                        break;
                    case "2":
                        dicParameterInfo.Add("Function Generater Number", InnerSourceNumber);
                        dicParameterInfo.Add("Function Generater Frequency", InnerSourceFrequency);
                        dicParameterInfo.Add("Function Generater Amplitude", InnerSourceAmplitude);
                        dicParameterInfo.Add("Function Generater Slope", InnerSourceGradient);
                        break;
                    case "3":
                        dicParameterInfo.Add("Target Position", TargetPosition);
                        dicParameterInfo.Add("Profile Velocity", ProfileVelocity);
                        dicParameterInfo.Add("End Profile Velocity", EndProfileVelocity);
                        dicParameterInfo.Add("Profile Acceleration", ProfileAcceleration);
                        dicParameterInfo.Add("Profile Deceleration", ProfileDeceleration);

                        dicParameterInfo.Add("Target Velocity", TargetVelocity);
                        dicParameterInfo.Add("Profile Jerk 1", ProfileJerk1);
                        dicParameterInfo.Add("Profile Jerk 2", ProfileJerk2);
                        dicParameterInfo.Add("Profile Jerk 3", ProfileJerk3);
                        dicParameterInfo.Add("Profile Jerk 4", ProfileJerk4);

                        dicParameterInfo.Add("Target Torque", TargetTorque);
                        dicParameterInfo.Add("Torque Slope", TorqueSlope);
                        break;
                    case "4":
                        dicParameterInfo.Add("Jog Speed", ParameterTunningJogSpeed);//Jog速度
                        dicParameterInfo.Add("Jog Acceleration Time", ParameterTunningJogAccelerationTime);//Jog加速时间
                        dicParameterInfo.Add("Jog Deceleration Time", ParameterTunningJogDecelerationTime);//Jog减速时间
                        dicParameterInfo.Add("Jog Time", ParameterTunningJogTime);//Jog运行时间

                        dicParameterInfo.Add("Program Jog Moving Distance", ParameterTunningProgramJogMovingDistance);//程序Jog移动距离
                        dicParameterInfo.Add("Program Jog Moving Speed", ParameterTunningProgramJogMovingSpeed);//程序Jog移动速度
                        dicParameterInfo.Add("Program Jog AccDec Time", ParameterTunningProgramJogAccDecTime);//程序Jog加减速时间
                        dicParameterInfo.Add("Program Jog Wait Time", ParameterTunningProgramJogWaitTime);//程序Jog等待时间
                        dicParameterInfo.Add("Program Jog Moving Number", ParameterTunningProgramJogMovingNumber);//程序Jog移动次数
                        break;
                    default:
                        dicParameterInfo.Add("Position Loop Gain", PositionLoopGain);
                        dicParameterInfo.Add("Load Inertia Ratio", LoadInertiaRatio);//负载惯量比     由Lilbert增加负载惯量比
                        dicParameterInfo.Add("First Trqcmd Filter Time", FirstTrqcmdFilterTime);//第一转矩滤波时间     由Lilbert增加第一转矩滤波时间
                        dicParameterInfo.Add("Speed Loop Gain", SpeedLoopGain);
                        dicParameterInfo.Add("Speed Loop Time Constant", SpeedLoopTimeConstant);
                        dicParameterInfo.Add("Current Loop Gain", CurrentLoopGain);
                        dicParameterInfo.Add("Current Loop Time Constant", CurrentLoopTimeConstant);
                        dicParameterInfo.Add("Rigidity", Rigidity);//刚性     由Lilbert增加刚性

                        dicParameterInfo.Add("Function Generater Number", InnerSourceNumber);
                        dicParameterInfo.Add("Function Generater Frequency", InnerSourceFrequency);
                        dicParameterInfo.Add("Function Generater Amplitude", InnerSourceAmplitude);
                        dicParameterInfo.Add("Function Generater Slope", InnerSourceGradient);

                        dicParameterInfo.Add("Target Position", TargetPosition);
                        dicParameterInfo.Add("Profile Velocity", ProfileVelocity);
                        dicParameterInfo.Add("End Profile Velocity", EndProfileVelocity);
                        dicParameterInfo.Add("Profile Acceleration", ProfileAcceleration);
                        dicParameterInfo.Add("Profile Deceleration", ProfileDeceleration);

                        dicParameterInfo.Add("Target Velocity", TargetVelocity);
                        dicParameterInfo.Add("Profile Jerk 1", ProfileJerk1);
                        dicParameterInfo.Add("Profile Jerk 2", ProfileJerk2);
                        dicParameterInfo.Add("Profile Jerk 3", ProfileJerk3);
                        dicParameterInfo.Add("Profile Jerk 4", ProfileJerk4);

                        dicParameterInfo.Add("Target Torque", TargetTorque);
                        dicParameterInfo.Add("Torque Slope", TorqueSlope);

                        dicParameterInfo.Add("Jog Speed", ParameterTunningJogSpeed);//Jog速度
                        dicParameterInfo.Add("Jog Acceleration Time", ParameterTunningJogAccelerationTime);//Jog加速时间
                        dicParameterInfo.Add("Jog Deceleration Time", ParameterTunningJogDecelerationTime);//Jog减速时间
                        dicParameterInfo.Add("Jog Time", ParameterTunningJogTime);//Jog运行时间

                        dicParameterInfo.Add("Program Jog Moving Distance", ParameterTunningProgramJogMovingDistance);//程序Jog移动距离
                        dicParameterInfo.Add("Program Jog Moving Speed", ParameterTunningProgramJogMovingSpeed);//程序Jog移动速度
                        dicParameterInfo.Add("Program Jog AccDec Time", ParameterTunningProgramJogAccDecTime);//程序Jog加减速时间
                        dicParameterInfo.Add("Program Jog Wait Time", ParameterTunningProgramJogWaitTime);//程序Jog等待时间
                        dicParameterInfo.Add("Program Jog Moving Number", ParameterTunningProgramJogMovingNumber);//程序Jog移动次数
                        break;
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }        

        //*************************************************************************
        //函数名称：DefaultUnitDictionaryInitialize
        //函数功能：默认单位集合
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.02&2022.08.05
        //*************************************************************************
        private void DefaultUnitDictionaryInitialize()
        {
            dicDefaultUnit = new Dictionary<string, string>();
            dicDefaultUnit.Add("位置指令", "cnt");
            dicDefaultUnit.Add("位置反馈", "cnt");
            dicDefaultUnit.Add("位置差值", "cnt");
            dicDefaultUnit.Add("滤波后位置指令", "cnt");
            dicDefaultUnit.Add("位置增量", "cnt");//由lilbert于2022年8月5日添加位置增量
            dicDefaultUnit.Add("位置环输出速度指令", "cnt/s");
            dicDefaultUnit.Add("速度指令", "cnt/s");
            dicDefaultUnit.Add("速度反馈", "cnt/s");
            dicDefaultUnit.Add("速度差值", "cnt/s");
            dicDefaultUnit.Add("速度前馈", "cnt/s");
            dicDefaultUnit.Add("编码器输出速度", "cnt/s");
            dicDefaultUnit.Add("观测器输出速度", "cnt/s");          
            dicDefaultUnit.Add("前馈后速度指令", "cnt/s");      
        }

        //*************************************************************************
        //函数名称：GetTriggerLevelValue
        //函数功能：获取TriggerLevel
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.02.24
        //*************************************************************************
        private void GetTriggerLevelValue(string Method)
        {
            double UnitRate = 0;
            string TriggerChannelName = null;
            string TriggerDefaultUnit = null;

            try
            {
                if (Method == "ToFile")
                {
                    switch (SelectedTriggerChannel)
                    {
                        case "通道-1":
                            TriggerChannelName = OfflineOscilloscope.Export.Channel1Name;
                            break;
                        case "通道-2":
                            TriggerChannelName = OfflineOscilloscope.Export.Channel2Name;
                            break;
                        case "通道-3":
                            TriggerChannelName = OfflineOscilloscope.Export.Channel3Name;
                            break;
                        case "通道-4":
                            TriggerChannelName = OfflineOscilloscope.Export.Channel4Name;
                            break;
                        default:
                            TriggerChannelName = "";
                            break;
                    }

                    dicDefaultUnit.TryGetValue(TriggerChannelName, out TriggerDefaultUnit);
                    if (string.IsNullOrEmpty(TriggerDefaultUnit) || TriggerDefaultUnit == Unit)
                    {
                        OfflineOscilloscope.Export.TriggerLevel = TriggerLevel;
                    }
                    else
                    {
                        UnitRate = OthersHelper.GetExchangeValueByCurrentUnit(false, Unit);
                        OfflineOscilloscope.Export.TriggerLevel = Math.Round(Convert.ToDouble(TriggerLevel) * UnitRate, 2).ToString();
                    }
                }
                else if (Method == "FromFile")
                {
                    switch (SelectedTriggerChannel)
                    {
                        case "通道-1":
                            TriggerChannelName = OfflineOscilloscope.Import.Channel1Name;
                            break;
                        case "通道-2":
                            TriggerChannelName = OfflineOscilloscope.Import.Channel2Name;
                            break;
                        case "通道-3":
                            TriggerChannelName = OfflineOscilloscope.Import.Channel3Name;
                            break;
                        case "通道-4":
                            TriggerChannelName = OfflineOscilloscope.Import.Channel4Name;
                            break;
                        default:
                            TriggerChannelName = "";
                            break;
                    }

                    dicDefaultUnit.TryGetValue(TriggerChannelName, out TriggerDefaultUnit);
                    if (string.IsNullOrEmpty(TriggerDefaultUnit) || TriggerDefaultUnit == Unit)
                    {
                        TriggerLevel = OfflineOscilloscope.Import.TriggerLevel;
                    }
                    else
                    {
                        UnitRate = OthersHelper.GetExchangeValueByCurrentUnit(true, Unit);
                        TriggerLevel = Math.Round(Convert.ToDouble(OfflineOscilloscope.Import.TriggerLevel) * UnitRate, 0).ToString();
                    }
                }
                else if (Method == "RecordLast")
                {
                    switch (SelectedTriggerChannel)
                    {
                        case "通道-1":
                            TriggerChannelName = OfflineOscilloscope.Last.Channel1Name;
                            break;
                        case "通道-2":
                            TriggerChannelName = OfflineOscilloscope.Last.Channel2Name;
                            break;
                        case "通道-3":
                            TriggerChannelName = OfflineOscilloscope.Last.Channel3Name;
                            break;
                        case "通道-4":
                            TriggerChannelName = OfflineOscilloscope.Last.Channel4Name;
                            break;
                        default:
                            TriggerChannelName = "";
                            break;
                    }

                    dicDefaultUnit.TryGetValue(TriggerChannelName, out TriggerDefaultUnit);
                    if (string.IsNullOrEmpty(TriggerDefaultUnit) || TriggerDefaultUnit == Unit)
                    {
                        OfflineOscilloscope.Last.TriggerLevel = TriggerLevel;
                    }
                    else
                    {
                        UnitRate = OthersHelper.GetExchangeValueByCurrentUnit(false, Unit);
                        OfflineOscilloscope.Last.TriggerLevel = Math.Round(Convert.ToDouble(TriggerLevel) * UnitRate, 2).ToString();
                    }
                }
                else if (Method == "GetLast")
                {
                    switch (SelectedTriggerChannel)
                    {
                        case "通道-1":
                            TriggerChannelName = OfflineOscilloscope.Last.Channel1Name;
                            break;
                        case "通道-2":
                            TriggerChannelName = OfflineOscilloscope.Last.Channel2Name;
                            break;
                        case "通道-3":
                            TriggerChannelName = OfflineOscilloscope.Last.Channel3Name;
                            break;
                        case "通道-4":
                            TriggerChannelName = OfflineOscilloscope.Last.Channel4Name;
                            break;
                        default:
                            TriggerChannelName = "";
                            break;
                    }

                    dicDefaultUnit.TryGetValue(TriggerChannelName, out TriggerDefaultUnit);
                    if (string.IsNullOrEmpty(TriggerDefaultUnit) || TriggerDefaultUnit == Unit)
                    {
                        TriggerLevel = OfflineOscilloscope.Last.TriggerLevel;
                    }
                    else
                    {
                        UnitRate = OthersHelper.GetExchangeValueByCurrentUnit(true, Unit);
                        TriggerLevel = Math.Round(Convert.ToDouble(OfflineOscilloscope.Last.TriggerLevel) * UnitRate, 0).ToString();
                    }
                }
                else if (Method == "ToGlobal")
                {
                    if (!OthersHelper.IsInputNumber(TriggerLevel))
                    {
                        return;
                    }

                    switch (SelectedTriggerChannel)
                    {
                        case "通道-1":
                            TriggerChannelName = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel1Index);
                            break;
                        case "通道-2":
                            TriggerChannelName = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel2Index);
                            break;
                        case "通道-3":
                            TriggerChannelName = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel3Index);
                            break;
                        case "通道-4":
                            TriggerChannelName = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel4Index);
                            break;
                        default:
                            TriggerChannelName = "";
                            break;
                    }

                    if (dicDefaultUnit == null)
                    {

                    }
                    else
                    {
                        dicDefaultUnit.TryGetValue(TriggerChannelName, out TriggerDefaultUnit);
                        if (string.IsNullOrEmpty(TriggerDefaultUnit) || TriggerDefaultUnit == Unit)
                        {
                            GlobalCurrentInput.TriggerLevel = TriggerLevel;
                        }
                        else
                        {
                            UnitRate = OthersHelper.GetExchangeValueByCurrentUnit(false, Unit);
                            GlobalCurrentInput.TriggerLevel = Math.Round(Convert.ToDouble(TriggerLevel) * UnitRate, 2).ToString();
                        }
                    }
                    
                }
                else if (Method == "FromGlobal")
                {
                    switch (SelectedTriggerChannel)
                    {
                        case "通道-1":
                            TriggerChannelName = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel1Index);
                            break;
                        case "通道-2":
                            TriggerChannelName = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel2Index);
                            break;
                        case "通道-3":
                            TriggerChannelName = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel3Index);
                            break;
                        case "通道-4":
                            TriggerChannelName = GetSampleNameByIndex(GlobalCurrentInput.SelectedSampleChannel4Index);
                            break;
                        default:
                            TriggerChannelName = "";
                            break;
                    }

                    dicDefaultUnit.TryGetValue(TriggerChannelName, out TriggerDefaultUnit);
                    if (string.IsNullOrEmpty(TriggerDefaultUnit) || TriggerDefaultUnit == Unit)
                    {
                        TriggerLevel = Math.Round(Convert.ToDouble(GlobalCurrentInput.TriggerLevel), 0).ToString();
                    }
                    else
                    {
                        UnitRate = OthersHelper.GetExchangeValueByCurrentUnit(true, Unit);
                        TriggerLevel = Math.Round(Convert.ToDouble(GlobalCurrentInput.TriggerLevel) * UnitRate, 0).ToString();
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_GET_TRIGGER_LEVEL_VALUE, "GetTriggerLevelValue", ex);
            }          
        }
        #endregion

        #endregion
    }

    public class OscilloscopePropertySet
    {
        public string ChannelNumber { get; set; }//通道编号
        public string Doubling { get; set; }//倍乘系数
        public bool IsHidden { get; set; }//波形是否隐藏
    }

    public class OsilloscopeCalculateSet
    {
        public string Title { get; set; }
        public string Number { get; set; }
        public string AcquisitionTime { get; set; }
        public string Channel1Value { get; set; }
        public string Channel2Value { get; set; }
        public string Channel3Value { get; set; }
        public string Channel4Value { get; set; }
    }

    public class SampleChannelInfoSet
    {
        public string GroupName { get; set; }
        public int ID { get; set; }
        public string ItemName { get; set; }
        public string Address { get; set; }
    }

    public class ChannelMapping
    {
        public int ChannelId { get; set; }
        public int UiChannelIndex { get; set; }
    }
}