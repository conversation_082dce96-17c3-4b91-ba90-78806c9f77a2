# 监控参数刷新机制分析报告

## 1. 概述

本文档旨在分析`ServoStudio`应用程序中侧边栏监控参数的刷新机制，并定位导致监控值始终显示为零的根本原因。

## 2. 刷新流程分析

参数监控的刷新是一个由UI定时器驱动的、周期性的“请求-响应”过程。

### 2.1. 刷新起点：`Timer_System_Tick`

- **文件**: [`ServoStudio/MainWindowViewModel.cs`](ServoStudio/MainWindowViewModel.cs)
- **核心方法**: `private void Timer_System_Tick(object sender, EventArgs e)`
- **触发时机**: 此方法由一个`DispatcherTimer`（`Timer_System`）每秒调用一次。

该方法是所有周期性UI刷新任务（包括状态栏、报警信息和参数监控）的入口点。

### 2.2. 数据请求构建

在`Timer_System_Tick`方法内部（[line 5248](ServoStudio/MainWindowViewModel.cs:5248)），执行以下步骤来构建一个包含所有待读取参数的请求列表：

1.  **初始化列表**: 创建一个`Dictionary<string, string> dicParameterInfo`来收集需要读取的参数。
2.  **添加基础状态参数**: 硬编码添加如`Position Actual Value`、`Status Word`、`Alarm Set0-3`等关键状态参数。
3.  **添加用户监控参数**:
    -   检查侧边栏监控面板是否可见（`!IsMonitoringPageHidden`）。
    -   如果可见，则调用`ParameterReadWriteModel.GetIndexAndDataType(GlobalParameterSet.dt_Monitor, ref lstTransmittingDataInfo_Monitoring)`，从全局的监控参数表`dt_Monitor`中提取用户已勾选的参数。
    -   将这些监控参数追加到总的待发送列表`lstTransmittingDataInfo_All`中。

### 2.3. 数据发送（问题根源）

在所有待读取的参数被收集并格式化为`lstTransmittingDataInfo_All`后，本应通过以下代码行将这个读取请求任务加入发送队列：

- **文件**: [`ServoStudio/MainWindowViewModel.cs`](ServoStudio/MainWindowViewModel.cs)
- **代码行**: [line 5386](ServoStudio/MainWindowViewModel.cs:5386)
- **代码**:
  ```csharp
  //ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.MAIN, TaskName.IntervalRefresh, lstTransmittingDataInfo_All);
  ```

**关键发现**：**此行代码被注释掉了。**

这意味着，尽管应用程序在每个周期都正确地构建了读取请求，但这个请求从未被实际发送出去。

### 2.4. UI更新

`Timer_System_Tick`方法的后半部分（[line 5389](ServoStudio/MainWindowViewModel.cs:5389)之后）负责UI的更新。它遍历`ParameterMonitoring`集合，并尝试从全局缓存`CommunicationSet.CurrentPointValue_AxisA/B`中通过`OthersHelper.GetCurrentValueOfIndex()`获取最新的值来填充UI。

由于第2.3步中的发送操作从未发生，硬件的实时值也从未被读回并更新到全局缓存中。因此，`GetCurrentValueOfIndex`总是返回一个初始的、无效的或为零的值，导致UI上的所有监控参数都显示为0。

## 3. 结论

监控参数值始终为零的Bug，其直接且唯一的原因是：**在`MainWindowViewModel`的`Timer_System_Tick`方法中，负责发送周期性刷新请求的核心代码行被意外注释，导致应用程序无法向硬件请求更新监控数据。**

## 4. 修复建议

取消对[`ServoStudio/MainWindowViewModel.cs`中第5386行](ServoStudio/MainWindowViewModel.cs:5386)的注释，以恢复定时刷新任务的正常发送。