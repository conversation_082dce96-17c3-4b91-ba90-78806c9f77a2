﻿using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.ComponentModel;
using System.Windows;
using Microsoft.Win32;
using ServoStudio.GlobalConstant;

namespace ServoStudio.GlobalMethod
{
    public static class ExcelHelper
    {
        #region 读操作
        //*************************************************************************
        //函数名称：GetReadPath
        //函数功能：获取读地址
        //
        //输入参数：ref string Out_strFilePath   地址信息  
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2019.10.25
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int GetReadPath(ref string Out_strFilePath)
        {
            int iRet = -1;
            OpenFileDialog openFileDialog = null;

            try
            {
                openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "(*.xlsx)|*.xlsx";
                openFileDialog.FilterIndex = 1;
                openFileDialog.InitialDirectory = System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory);
                openFileDialog.RestoreDirectory = false;
                openFileDialog.CheckFileExists = true;
                openFileDialog.CheckPathExists = true;
                openFileDialog.ValidateNames = true;

                if ((bool)openFileDialog.ShowDialog() == true)
                {
                    Out_strFilePath = openFileDialog.FileName;
                    iRet = RET.SUCCEEDED;
                }
                else
                {
                    iRet = RET.NO_EFFECT;
                }
            }
            catch
            {
                iRet = RET.ERROR;
            }

            return iRet;
        }

        //*************************************************************************
        //函数名称：GetReadPath_With_Path
        //函数功能：获取读地址(带配置文件路径)
        //
        //输入参数：ref string Out_strFilePath   地址信息  
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.10
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int GetReadPath_With_Path(ref string Out_strFilePath)
        {
            int iRet = -1;
            OpenFileDialog openFileDialog = null;

            try
            {
                openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "(*.xlsx)|*.xlsx";
                openFileDialog.FilterIndex = 1;
                openFileDialog.InitialDirectory = System.Environment.CurrentDirectory + "\\Config";
                openFileDialog.RestoreDirectory = false;
                openFileDialog.CheckFileExists = true;
                openFileDialog.CheckPathExists = true;
                openFileDialog.ValidateNames = true;

                if ((bool)openFileDialog.ShowDialog() == true)
                {
                    Out_strFilePath = openFileDialog.FileName;
                    iRet = RET.SUCCEEDED;
                }
                else
                {
                    iRet = RET.NO_EFFECT;
                }
            }
            catch
            {
                iRet = RET.ERROR;
            }

            return iRet;
        }

        public static int GetReadPath_ForOscilloscope(ref string Out_strFilePath)
        {
            int iRet = -1;
            OpenFileDialog openFileDialog = null;

            try
            {
                openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "(*.xlsx)|*.xlsx";
                openFileDialog.FilterIndex = 1;
                //openFileDialog.InitialDirectory = System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory);
                openFileDialog.InitialDirectory = IniHelper.IniReadValue("ServoStudio", "OscilloscopeWaveformPath", FilePath.Ini).Replace("\\\\", "\\");//由Lilbert于2023.11.11添加默认路径

                openFileDialog.RestoreDirectory = false;
                openFileDialog.CheckFileExists = true;
                openFileDialog.CheckPathExists = true;
                openFileDialog.ValidateNames = true;

                if ((bool)openFileDialog.ShowDialog() == true)
                {
                    Out_strFilePath = openFileDialog.FileName;
                    iRet = RET.SUCCEEDED;
                }
                else
                {
                    iRet = RET.NO_EFFECT;
                }
            }
            catch
            {
                iRet = RET.ERROR;
            }

            return iRet;
        }

        public static int GetReadPath_ForUpdate(ref string Out_strFilePath)
        {
            int iRet = -1;
            OpenFileDialog openFileDialog = null;

            try
            {
                openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "(*.bin)|*.bin";
                openFileDialog.FilterIndex = 1;
                openFileDialog.InitialDirectory = System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory);
                openFileDialog.RestoreDirectory = false;
                openFileDialog.CheckFileExists = true;
                openFileDialog.CheckPathExists = true;
                openFileDialog.ValidateNames = true;

                if ((bool)openFileDialog.ShowDialog() == true)
                {
                    Out_strFilePath = openFileDialog.FileName;
                    iRet = RET.SUCCEEDED;
                }
                else
                {
                    iRet = RET.NO_EFFECT;
                }
            }
            catch
            {
                iRet = RET.ERROR;
            }

            return iRet;
        }
        public static int GetReadPath_ForLog(ref string Out_strFilePath)
        {
            int iRet = -1;
            OpenFileDialog openFileDialog = null;

            try
            {
                openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "(*.htm)|*.htm";
                openFileDialog.FilterIndex = 1;
                openFileDialog.InitialDirectory = System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory);
                openFileDialog.RestoreDirectory = false;
                openFileDialog.CheckFileExists = true;
                openFileDialog.CheckPathExists = true;
                openFileDialog.ValidateNames = true;

                if ((bool)openFileDialog.ShowDialog() == true)
                {
                    Out_strFilePath = openFileDialog.FileName;
                    iRet = RET.SUCCEEDED;
                }
                else
                {
                    iRet = RET.NO_EFFECT;
                }
            }
            catch
            {
                iRet = RET.ERROR;
            }

            return iRet;
        }

        //*************************************************************************
        //函数名称：ReadCellValueFromExcel
        //函数功能：获取Excel中软件版本号数据
        //
        //输入参数：string Out_strFilePath           地址信息  
        //         ref DataTable Out_clsDataTable   DataTable
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2021.07.14
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int ReadCellValueFromExcel(string In_strFilePath, ref DataTable Out_clsDataTable)
        {
            int iRet = -1;
            IWorkbook workBook = null;
            ISheet sheet = null;
            FileStream fileStream = null;
            Out_clsDataTable = new DataTable();

            try
            {
                //打开工作簿
                fileStream = new FileStream(In_strFilePath, FileMode.Open, FileAccess.Read);
                workBook = new XSSFWorkbook(fileStream);
                if (workBook == null)
                {
                    return RET.OPEN_WORKBOOK_ERROR;
                }

                //打开第一个Sheet
                sheet = workBook.GetSheetAt(0);
                if (sheet == null)
                {
                    return RET.OPEN_SHEET_ERROR;
                }

                //获取列名称
                iRet = GetColumnInfo_for_Excel(sheet, ref Out_clsDataTable);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                //获取伺服软件版本号
                iRet = GetContentInfo_for_SoftwareVersion(sheet, ref Out_clsDataTable);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                //获取数据信息
                iRet = GetContentInfo_for_Excel(sheet, ref Out_clsDataTable);
                return iRet;
            }
            catch
            {
                return RET.ERROR;
            }
            finally
            {
                if (workBook != null)
                {
                    workBook.Close();
                }

                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }

        //*************************************************************************
        //函数名称：ReadFromExcel_For_dtBase
        //函数功能：获取Excel数据到DataTable
        //
        //输入参数： 
        //         ref DataTable Out_clsDataTable   DataTable
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int ReadFromExcel_For_dtBase( ref DataTable Out_clsDataTable)
        {
            int iRet = -1;
            IWorkbook workBookBase = null;
            ISheet sheetBase = null;
            FileStream fileStreamBase = null;
            Out_clsDataTable = new DataTable();

            try
            {
                //打开工作簿
                //fileStream = new FileStream(In_strFilePath, FileMode.Open, FileAccess.Read);
                fileStreamBase = new FileStream(FilePath.ParameterLibrary + "配置文件.xlsx", FileMode.Open, FileAccess.Read);
                workBookBase = new XSSFWorkbook(fileStreamBase);
                if (workBookBase == null)
                {
                    return RET.OPEN_WORKBOOK_ERROR;
                }

                //打开第一个Sheet
                sheetBase = workBookBase.GetSheetAt(0);
                if (sheetBase == null)
                {
                    return RET.OPEN_SHEET_ERROR;
                }

                //获取列名称
                iRet = GetColumnInfo(sheetBase, ref Out_clsDataTable);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                //获取数据信息
                iRet = GetContentInfo(sheetBase, ref Out_clsDataTable);
                return iRet;
            }
            catch
            {
                return RET.ERROR;
            }
            finally
            {
                if (workBookBase != null)
                {
                    workBookBase.Close();
                }

                if (fileStreamBase != null)
                {
                    fileStreamBase.Close();
                }
            }
        }        

        //*************************************************************************
        //函数名称：ReadFromExcel_For_dtImport
        //函数功能：获取Excel数据到DataTable
        //
        //输入参数：string Out_strFilePath           地址信息  
        //         ref DataTable Out_clsDataTable   DataTable
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int ReadFromExcel_For_dtImport(string In_strFilePath, ref DataTable Out_clsDataTable)
        {
            int iRet = -1;
            IWorkbook workBookImport = null;
            ISheet sheetImport = null;
            FileStream fileStreamImport = null;
            Out_clsDataTable = new DataTable();

            try
            {
                //打开工作簿
                fileStreamImport = new FileStream(In_strFilePath, FileMode.Open, FileAccess.Read);

                workBookImport = new XSSFWorkbook(fileStreamImport);
                if (workBookImport == null)
                {
                    return RET.OPEN_WORKBOOK_ERROR;
                }

                //打开第一个Sheet
                sheetImport = workBookImport.GetSheetAt(0);
                if (sheetImport == null)
                {
                    return RET.OPEN_SHEET_ERROR;
                }

                //获取列名称
                iRet = GetColumnInfo(sheetImport, ref Out_clsDataTable);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                //获取数据信息
                iRet = GetContentInfo(sheetImport, ref Out_clsDataTable);
                return iRet;
            }
            catch
            {
                return RET.ERROR;
            }
            finally
            {
                if (workBookImport != null)
                {
                    workBookImport.Close();
                }

                if (fileStreamImport != null)
                {
                    fileStreamImport.Close();
                }
            }
        }

        //*************************************************************************
        //函数名称：ReadFromExcel
        //函数功能：获取Excel数据到DataTable
        //
        //输入参数：string Out_strFilePath           地址信息  
        //         ref DataTable Out_clsDataTable   DataTable
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2019.10.25
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int ReadFromExcel(string In_strFilePath, ref DataTable Out_clsDataTable)
        {
            int iRet = -1;
            IWorkbook workBook = null;
            ISheet sheet = null;
            FileStream fileStream = null;
            Out_clsDataTable = new DataTable();

            try
            {
                //打开工作簿
                fileStream = new FileStream(In_strFilePath, FileMode.Open, FileAccess.Read);
                workBook = new XSSFWorkbook(fileStream);
                if (workBook == null)
                {
                    return RET.OPEN_WORKBOOK_ERROR;
                }

                //打开第一个Sheet
                sheet = workBook.GetSheetAt(0);
                if (sheet == null)
                {
                    return RET.OPEN_SHEET_ERROR;
                }

                //获取列名称
                iRet = GetColumnInfo(sheet, ref Out_clsDataTable);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                //获取数据信息
                iRet = GetContentInfo(sheet, ref Out_clsDataTable);
                return iRet;               
            }
            catch
            {               
                return RET.ERROR;
            }
            finally
            {
                if (workBook != null)
                {
                    workBook.Close();
                }

                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }  

        //*************************************************************************
        //函数名称：ReadFromExcel_For_Oscilloscope
        //函数功能：获取数据采样Excel数据
        //
        //输入参数：string Out_strFilePath           地址信息  
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2020.01.14
        //*************************************************************************
        public static int ReadFromExcel_For_Oscilloscope(string In_strFilePath)
        {
            int iRet = -1;
            IWorkbook workBook = null;
            ISheet sheet = null;
            FileStream fileStream = null;

            try
            {
                //打开工作簿
                fileStream = new FileStream(In_strFilePath, FileMode.Open, FileAccess.Read);
                workBook = new XSSFWorkbook(fileStream);
                if (workBook == null)
                {
                    return RET.OPEN_WORKBOOK_ERROR;
                }

                //打开第一个Sheet
                sheet = workBook.GetSheetAt(0);
                if (sheet == null)
                {
                    return RET.OPEN_SHEET_ERROR;
                }

                //获取数据信息
                iRet = GetContentInfo_For_Oscilloscope(sheet);
                return iRet;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.EXCELHELPER_READ_FROM_EXCEL_FOR_OSCILLOSCOPE, "ReadFromExcel_For_Oscilloscope", ex);
                return RET.ERROR;
            }
            finally
            {
                if (workBook != null)
                {
                    workBook.Close();
                }

                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }

        //*************************************************************************
        //函数名称：ReadFromExcel_For_Oscilloscope
        //函数功能：获取数据采样Excel数据
        //
        //输入参数：string Out_strFilePath           地址信息  
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.10
        //*************************************************************************
        public static int ReadFromExcel_For_FaultAcquisition(string In_strFilePath)
        {
            int iRet = -1;
            IWorkbook workBook = null;
            ISheet sheet = null;
            FileStream fileStream = null;

            try
            {
                //打开工作簿
                fileStream = new FileStream(In_strFilePath, FileMode.Open, FileAccess.Read);
                workBook = new XSSFWorkbook(fileStream);
                if (workBook == null)
                {
                    return RET.OPEN_WORKBOOK_ERROR;
                }

                //打开第一个Sheet
                sheet = workBook.GetSheetAt(0);
                if (sheet == null)
                {
                    return RET.OPEN_SHEET_ERROR;
                }

                //获取数据信息
                iRet = GetContentInfo_For_FaultAcquisition(sheet);
                return iRet;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.EXCELHELPER_READ_FROM_EXCEL_FOR_OSCILLOSCOPE, "ReadFromExcel_For_Oscilloscope", ex);
                return RET.ERROR;
            }
            finally
            {
                if (workBook != null)
                {
                    workBook.Close();
                }

                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }

        //*************************************************************************
        //函数名称：GetContentInfo
        //函数功能：获取内容
        //
        //输入参数：ISheet In_sheet 
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2020.01.14
        //*************************************************************************
        private static int GetContentInfo_For_Oscilloscope(ISheet In_sheet)
        {
            int iValue = 0;
            string strValue = null;
            OfflineOscilloscope.Import = new OscilloscopeParameterSet();
            OfflineOscilloscope.Import.lstChannel1 = new List<int>();
            OfflineOscilloscope.Import.lstChannel2 = new List<int>();
            OfflineOscilloscope.Import.lstChannel3 = new List<int>();
            OfflineOscilloscope.Import.lstChannel4 = new List<int>();

            try
            {
                if (In_sheet.LastRowNum < 10) return RET.NO_EFFECT;

                for (int iRowValue = 0; iRowValue < In_sheet.LastRowNum + 1; iRowValue++)
                {
                    //轮询获取每一整行信息
                    if (iRowValue == 2 || iRowValue == 5 || iRowValue == 8 || iRowValue == 11 || iRowValue == 14 || iRowValue == 17) continue;

                    IRow row = In_sheet.GetRow(iRowValue);
                    if (row == null) return RET.NO_EFFECT;

                    for (int iColumnValue = 0; iColumnValue < row.LastCellNum; iColumnValue++)
                    {
                        #region ROW 0
                        if (iRowValue == 0 && iColumnValue == 0)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "采样周期")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 0 && iColumnValue == 1)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "采样时长")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 0 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "采样连续")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region ROW 1
                        else if (iRowValue == 1 && iColumnValue == 0)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineOscilloscope.Import.Period = strValue;
                            }
                        }
                        else if (iRowValue == 1 && iColumnValue == 1)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineOscilloscope.Import.Duration = strValue;
                            }
                        }
                        else if (iRowValue == 1 && iColumnValue == 2)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineOscilloscope.Import.IsContinuous = strValue;
                                break;
                            }
                        }
                        #endregion

                        #region ROW 3
                        else if (iRowValue == 3 && iColumnValue == 0)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道1名称")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 3 && iColumnValue == 1)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道2名称")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 3 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道3名称")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 3 && iColumnValue == 3)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道4名称")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region ROW 4
                        else if (iRowValue == 4 && iColumnValue == 0)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineOscilloscope.Import.Channel1Name = strValue;
                            }
                        }
                        else if (iRowValue == 4 && iColumnValue == 1)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineOscilloscope.Import.Channel2Name = strValue;
                            }
                        }
                        else if (iRowValue == 4 && iColumnValue == 2)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineOscilloscope.Import.Channel3Name = strValue;
                            }
                        }
                        else if (iRowValue == 4 && iColumnValue == 3)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineOscilloscope.Import.Channel4Name = strValue;
                                break;
                            }
                        }
                        #endregion

                        #region ROW 6
                        else if (iRowValue == 6 && iColumnValue == 0)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "触发模式")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 6 && iColumnValue == 1)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "触发通道")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 6 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "预触发")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 6 && iColumnValue == 3)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "触发水平")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region ROW 7
                        else if (iRowValue == 7 && iColumnValue == 0)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineOscilloscope.Import.TriggerMode = strValue;
                            }
                        }
                        else if (iRowValue == 7 && iColumnValue == 1)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                if (OfflineOscilloscope.Import.TriggerMode != "无")
                                {
                                    return RET.NO_EFFECT;
                                }
                            }
                            else
                            {
                                OfflineOscilloscope.Import.TriggerChannel = strValue;
                            }
                        }
                        else if (iRowValue == 7 && iColumnValue == 2)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineOscilloscope.Import.PreTrigger = strValue;
                            }
                        }
                        else if (iRowValue == 7 && iColumnValue == 3)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                if (OfflineOscilloscope.Import.TriggerMode != "无")
                                {
                                    return RET.NO_EFFECT;
                                }
                            }
                            else
                            {
                                OfflineOscilloscope.Import.TriggerLevel = strValue;
                                break;
                            }
                        }
                        #endregion

                        #region ROW 9
                        else if (iRowValue == 9 && iColumnValue == 0)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道1倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 9 && iColumnValue == 1)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道2倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 9 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道3倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 9 && iColumnValue == 3)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道4倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region ROW 10
                        else if (iRowValue == 10 && iColumnValue == 0)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineOscilloscope.Import.Channel1Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineOscilloscope.Import.Channel1Doubling = "1";
                                }
                                else
                                {
                                    OfflineOscilloscope.Import.Channel1Doubling = strValue;
                                }          
                            }
                        }
                        else if (iRowValue == 10 && iColumnValue == 1)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineOscilloscope.Import.Channel2Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineOscilloscope.Import.Channel2Doubling = "1";
                                }
                                else
                                {
                                    OfflineOscilloscope.Import.Channel2Doubling = strValue;
                                }
                            }
                        }
                        else if (iRowValue == 10 && iColumnValue == 2)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineOscilloscope.Import.Channel3Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineOscilloscope.Import.Channel3Doubling = "1";
                                }
                                else
                                {
                                    OfflineOscilloscope.Import.Channel3Doubling = strValue;
                                }
                            }
                        }
                        else if (iRowValue == 10 && iColumnValue == 3)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineOscilloscope.Import.Channel4Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineOscilloscope.Import.Channel4Doubling = "1";
                                }
                                else
                                {
                                    OfflineOscilloscope.Import.Channel4Doubling = strValue;
                                }
                            }
                            break;
                        }
                        #endregion

                        #region ROW 12
                        else if (iRowValue == 12 && iColumnValue == 0)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道1单位")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 12 && iColumnValue == 1)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道2单位")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 12 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道3单位")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 12 && iColumnValue == 3)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道4单位")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region ROW 13
                        else if (iRowValue == 13 && iColumnValue == 0)
                        {
                            OfflineOscilloscope.Import.lstUnit = new List<string>();
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineOscilloscope.Import.lstUnit.Add(strValue);
                            }
                        }
                        else if (iRowValue == 13 && iColumnValue == 1)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineOscilloscope.Import.lstUnit.Add(strValue);
                            }
                        }
                        else if (iRowValue == 13 && iColumnValue == 2)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineOscilloscope.Import.lstUnit.Add(strValue);
                            }
                        }
                        else if (iRowValue == 13 && iColumnValue == 3)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineOscilloscope.Import.lstUnit.Add(strValue);
                            }

                            break;
                        }
                        #endregion

                        #region ROW 15
                        else if (iRowValue == 15 && iColumnValue == 0)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道1换算")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 15 && iColumnValue == 1)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道2换算")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 15 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道3换算")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 15 && iColumnValue == 3)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道4换算")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region ROW 16
                        else if (iRowValue == 16 && iColumnValue == 0)
                        {
                            OfflineOscilloscope.Import.lstExchangeValue = new List<double>();
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineOscilloscope.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }
                        }
                        else if (iRowValue == 16 && iColumnValue == 1)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineOscilloscope.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }
                        }
                        else if (iRowValue == 16 && iColumnValue == 2)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineOscilloscope.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }
                        }
                        else if (iRowValue == 16 && iColumnValue == 3)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineOscilloscope.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }

                            break;
                        }
                        #endregion

                        #region ROW 18
                        else if (iRowValue == 18 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道1数值")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 18 && iColumnValue == 3)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道2数值")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 18 && iColumnValue == 4)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道3数值")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 18 && iColumnValue == 5)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道4数值")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region Other Rows
                        else if (iRowValue >= 19)
                        {
                            if (iColumnValue == 0 || iColumnValue == 1)
                            {
                                continue;
                            }

                            strValue = Convert.ToString(row.GetCell(iColumnValue));                           
                            if (string.IsNullOrEmpty(strValue))
                            {
                                iValue = 0;
                            }  
                            else
                            {
                                iValue = Convert.ToInt32(strValue);
                            }
                                
                            switch (iColumnValue)
                            {
                                case 2:
                                    if (OfflineOscilloscope.Import.Channel1Name != "停用")
                                    {
                                        OfflineOscilloscope.Import.lstChannel1.Add(iValue);
                                    }  
                                    break;
                                case 3:
                                    if (OfflineOscilloscope.Import.Channel2Name != "停用")
                                    {
                                        OfflineOscilloscope.Import.lstChannel2.Add(iValue);
                                    }
                                    break;
                                case 4:
                                    if (OfflineOscilloscope.Import.Channel3Name != "停用")
                                    {
                                        OfflineOscilloscope.Import.lstChannel3.Add(iValue);
                                    }
                                    break;
                                case 5:
                                    if (OfflineOscilloscope.Import.Channel4Name != "停用")
                                    {
                                        OfflineOscilloscope.Import.lstChannel4.Add(iValue);
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }                      
                        #endregion
                    }
                }
                
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.EXCELHELPER_GET_CONTENT_INFO_FOR_OSCILLOSCOPE, "GetContentInfo_For_Oscilloscope", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetContentInfo
        //函数功能：获取内容
        //
        //输入参数：ISheet In_sheet 
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.10
        //*************************************************************************
        private static int GetContentInfo_For_FaultAcquisition(ISheet In_sheet)
        {
            int iValue = 0;
            string strValue = null;
            OfflineFaultAcquisition.Import = new FaultAcquisitionParameterSet();
            OfflineFaultAcquisition.Import.lstChannel1 = new List<int>();
            OfflineFaultAcquisition.Import.lstChannel2 = new List<int>();
            OfflineFaultAcquisition.Import.lstChannel3 = new List<int>();
            OfflineFaultAcquisition.Import.lstChannel4 = new List<int>();
            OfflineFaultAcquisition.Import.lstChannel5 = new List<int>();
            OfflineFaultAcquisition.Import.lstChannel6 = new List<int>();
            OfflineFaultAcquisition.Import.lstChannel7 = new List<int>();
            OfflineFaultAcquisition.Import.lstChannel8 = new List<int>();

            try
            {
                if (In_sheet.LastRowNum < 10) return RET.NO_EFFECT;

                for (int iRowValue = 0; iRowValue < In_sheet.LastRowNum + 1; iRowValue++)
                {
                    //轮询获取每一整行信息
                    if (iRowValue == 2 || iRowValue == 5 || iRowValue == 8 || iRowValue == 11 || iRowValue == 14 || iRowValue == 17) continue;

                    IRow row = In_sheet.GetRow(iRowValue);
                    if (row == null) return RET.NO_EFFECT;

                    for (int iColumnValue = 0; iColumnValue < row.LastCellNum; iColumnValue++)
                    {
                        #region ROW 0
                        if (iRowValue == 0 && iColumnValue == 0)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "采样周期")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 0 && iColumnValue == 1)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "采样时长")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        //else if (iRowValue == 0 && iColumnValue == 2)
                        //{
                        //    if (Convert.ToString(row.GetCell(iColumnValue)) != "采样连续")
                        //    {
                        //        return RET.NO_EFFECT;
                        //    }
                        //    else
                        //    {
                        //        break;
                        //    }
                        //}
                        #endregion

                        #region ROW 1
                        else if (iRowValue == 1 && iColumnValue == 0)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineFaultAcquisition.Import.Period = strValue;
                            }
                        }
                        else if (iRowValue == 1 && iColumnValue == 1)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineFaultAcquisition.Import.Duration = strValue;
                            }
                        }
                        //else if (iRowValue == 1 && iColumnValue == 2)
                        //{
                        //    strValue = Convert.ToString(row.GetCell(iColumnValue));
                        //    if (string.IsNullOrEmpty(strValue))
                        //    {
                        //        return RET.NO_EFFECT;
                        //    }
                        //    else
                        //    {
                        //        //OfflineFaultAcquisition.Import.IsContinuous = strValue;
                        //        break;
                        //    }
                        //}
                        #endregion

                        #region ROW 3
                        else if (iRowValue == 3 && iColumnValue == 0)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道1名称")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 3 && iColumnValue == 1)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道2名称")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 3 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道3名称")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 3 && iColumnValue == 3)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道4名称")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 3 && iColumnValue == 4)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道5名称")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 3 && iColumnValue == 5)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道6名称")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 3 && iColumnValue == 6)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道7名称")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 3 && iColumnValue == 7)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道8名称")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region ROW 4
                        else if (iRowValue == 4 && iColumnValue == 0)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineFaultAcquisition.Import.Channel1Name = strValue;
                            }
                        }
                        else if (iRowValue == 4 && iColumnValue == 1)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineFaultAcquisition.Import.Channel2Name = strValue;
                            }
                        }
                        else if (iRowValue == 4 && iColumnValue == 2)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineFaultAcquisition.Import.Channel3Name = strValue;
                            }
                        }
                        else if (iRowValue == 4 && iColumnValue == 3)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineFaultAcquisition.Import.Channel4Name = strValue;
                            }
                        }
                        else if (iRowValue == 4 && iColumnValue == 4)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineFaultAcquisition.Import.Channel5Name = strValue;
                            }
                        }
                        else if (iRowValue == 4 && iColumnValue == 5)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineFaultAcquisition.Import.Channel6Name = strValue;
                            }
                        }
                        else if (iRowValue == 4 && iColumnValue == 6)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineFaultAcquisition.Import.Channel7Name = strValue;
                            }
                        }
                        else if (iRowValue == 4 && iColumnValue == 7)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                OfflineFaultAcquisition.Import.Channel8Name = strValue;
                                break;
                            }
                        }
                        #endregion

                        #region ROW 6
                        //else if (iRowValue == 6 && iColumnValue == 0)
                        //{
                        //    if (Convert.ToString(row.GetCell(iColumnValue)) != "触发模式")
                        //    {
                        //        return RET.NO_EFFECT;
                        //    }
                        //}
                        //else if (iRowValue == 6 && iColumnValue == 1)
                        //{
                        //    if (Convert.ToString(row.GetCell(iColumnValue)) != "触发通道")
                        //    {
                        //        return RET.NO_EFFECT;
                        //    }
                        //}
                        //else if (iRowValue == 6 && iColumnValue == 2)
                        //{
                        //    if (Convert.ToString(row.GetCell(iColumnValue)) != "预触发")
                        //    {
                        //        return RET.NO_EFFECT;
                        //    }
                        //}
                        //else if (iRowValue == 6 && iColumnValue == 3)
                        //{
                        //    if (Convert.ToString(row.GetCell(iColumnValue)) != "触发水平")
                        //    {
                        //        return RET.NO_EFFECT;
                        //    }
                        //    else
                        //    {
                        //        break;
                        //    }
                        //}
                        #endregion

                        #region ROW 7   触发通道设置
                        //else if (iRowValue == 7 && iColumnValue == 0)
                        //{
                        //    strValue = Convert.ToString(row.GetCell(iColumnValue));
                        //    if (string.IsNullOrEmpty(strValue))
                        //    {
                        //        return RET.NO_EFFECT;
                        //    }
                        //    else
                        //    {
                        //        OfflineFaultAcquisition.Import.TriggerMode = strValue;
                        //    }
                        //}
                        //else if (iRowValue == 7 && iColumnValue == 1)
                        //{
                        //    strValue = Convert.ToString(row.GetCell(iColumnValue));
                        //    if (string.IsNullOrEmpty(strValue))
                        //    {
                        //        if (OfflineFaultAcquisition.Import.TriggerMode != "无")
                        //        {
                        //            return RET.NO_EFFECT;
                        //        }
                        //    }
                        //    else
                        //    {
                        //        OfflineFaultAcquisition.Import.TriggerChannel = strValue;
                        //    }
                        //}
                        //else if (iRowValue == 7 && iColumnValue == 2)
                        //{
                        //    strValue = Convert.ToString(row.GetCell(iColumnValue));
                        //    if (string.IsNullOrEmpty(strValue))
                        //    {
                        //        return RET.NO_EFFECT;
                        //    }
                        //    else
                        //    {
                        //        OfflineFaultAcquisition.Import.PreTrigger = strValue;
                        //    }
                        //}
                        //else if (iRowValue == 7 && iColumnValue == 3)
                        //{
                        //    strValue = Convert.ToString(row.GetCell(iColumnValue));
                        //    if (string.IsNullOrEmpty(strValue))
                        //    {
                        //        if (OfflineFaultAcquisition.Import.TriggerMode != "无")
                        //        {
                        //            return RET.NO_EFFECT;
                        //        }
                        //    }
                        //    else
                        //    {
                        //        OfflineFaultAcquisition.Import.TriggerLevel = strValue;
                        //        break;
                        //    }
                        //}
                        #endregion

                        #region ROW 9
                        else if (iRowValue == 9 && iColumnValue == 0)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道1倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 9 && iColumnValue == 1)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道2倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 9 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道3倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 9 && iColumnValue == 3)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道4倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 9 && iColumnValue == 4)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道5倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 9 && iColumnValue == 5)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道6倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 9 && iColumnValue == 6)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道7倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 9 && iColumnValue == 7)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道8倍乘")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region ROW 10
                        else if (iRowValue == 10 && iColumnValue == 0)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineFaultAcquisition.Import.Channel1Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineFaultAcquisition.Import.Channel1Doubling = "1";
                                }
                                else
                                {
                                    OfflineFaultAcquisition.Import.Channel1Doubling = strValue;
                                }
                            }
                        }
                        else if (iRowValue == 10 && iColumnValue == 1)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineFaultAcquisition.Import.Channel2Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineFaultAcquisition.Import.Channel2Doubling = "1";
                                }
                                else
                                {
                                    OfflineFaultAcquisition.Import.Channel2Doubling = strValue;
                                }
                            }
                        }
                        else if (iRowValue == 10 && iColumnValue == 2)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineFaultAcquisition.Import.Channel3Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineFaultAcquisition.Import.Channel3Doubling = "1";
                                }
                                else
                                {
                                    OfflineFaultAcquisition.Import.Channel3Doubling = strValue;
                                }
                            }
                        }
                        else if (iRowValue == 10 && iColumnValue == 3)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineFaultAcquisition.Import.Channel4Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineFaultAcquisition.Import.Channel4Doubling = "1";
                                }
                                else
                                {
                                    OfflineFaultAcquisition.Import.Channel4Doubling = strValue;
                                }
                            }
                        }
                        else if (iRowValue == 10 && iColumnValue == 4)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineFaultAcquisition.Import.Channel5Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineFaultAcquisition.Import.Channel5Doubling = "1";
                                }
                                else
                                {
                                    OfflineFaultAcquisition.Import.Channel5Doubling = strValue;
                                }
                            }
                        }
                        else if (iRowValue == 10 && iColumnValue == 5)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineFaultAcquisition.Import.Channel6Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineFaultAcquisition.Import.Channel6Doubling = "1";
                                }
                                else
                                {
                                    OfflineFaultAcquisition.Import.Channel6Doubling = strValue;
                                }
                            }
                        }
                        else if (iRowValue == 10 && iColumnValue == 6)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineFaultAcquisition.Import.Channel7Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineFaultAcquisition.Import.Channel7Doubling = "1";
                                }
                                else
                                {
                                    OfflineFaultAcquisition.Import.Channel7Doubling = strValue;
                                }
                            }
                        }
                        else if (iRowValue == 10 && iColumnValue == 7)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                OfflineFaultAcquisition.Import.Channel8Doubling = "1";
                            }
                            else
                            {
                                if (!OthersHelper.IsInputNumber(strValue))
                                {
                                    OfflineFaultAcquisition.Import.Channel8Doubling = "1";
                                }
                                else
                                {
                                    OfflineFaultAcquisition.Import.Channel8Doubling = strValue;
                                }
                            }
                            break;
                        }
                        #endregion

                        #region ROW 12
                        else if (iRowValue == 12 && iColumnValue == 0)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道1单位")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 12 && iColumnValue == 1)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道2单位")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 12 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道3单位")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 12 && iColumnValue == 3)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道4单位")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 12 && iColumnValue == 4)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道5单位")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 12 && iColumnValue == 5)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道6单位")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 12 && iColumnValue == 6)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道7单位")
                            {
                                return RET.NO_EFFECT;
                            }
                        }                        
                        else if (iRowValue == 12 && iColumnValue == 7)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道8单位")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region ROW 13
                        else if (iRowValue == 13 && iColumnValue == 0)
                        {
                            OfflineFaultAcquisition.Import.lstUnit = new List<string>();
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstUnit.Add(strValue);
                            }
                        }
                        else if (iRowValue == 13 && iColumnValue == 1)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstUnit.Add(strValue);
                            }
                        }
                        else if (iRowValue == 13 && iColumnValue == 2)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstUnit.Add(strValue);
                            }
                        }
                        else if (iRowValue == 13 && iColumnValue == 3)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstUnit.Add(strValue);
                            }
                        }
                        else if (iRowValue == 13 && iColumnValue == 4)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstUnit.Add(strValue);
                            }
                        }
                        else if (iRowValue == 13 && iColumnValue == 5)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstUnit.Add(strValue);
                            }
                        }
                        else if (iRowValue == 13 && iColumnValue == 6)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstUnit.Add(strValue);
                            }
                        }
                        else if (iRowValue == 13 && iColumnValue == 7)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstUnit.Add(strValue);
                            }
                        }
                        else if (iRowValue == 13 && iColumnValue == 8)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstUnit.Add(strValue);
                            }

                            break;
                        }
                        #endregion

                        #region ROW 15
                        else if (iRowValue == 15 && iColumnValue == 0)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道1换算")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 15 && iColumnValue == 1)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道2换算")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 15 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道3换算")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 15 && iColumnValue == 3)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道4换算")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 15 && iColumnValue == 4)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道5换算")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 15 && iColumnValue == 5)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道6换算")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 15 && iColumnValue == 6)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道7换算")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 15 && iColumnValue == 7)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道8换算")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region ROW 16
                        else if (iRowValue == 16 && iColumnValue == 0)
                        {
                            OfflineFaultAcquisition.Import.lstExchangeValue = new List<double>();
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }
                        }
                        else if (iRowValue == 16 && iColumnValue == 1)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }
                        }
                        else if (iRowValue == 16 && iColumnValue == 2)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }
                        }
                        else if (iRowValue == 16 && iColumnValue == 3)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }
                        }
                        else if (iRowValue == 16 && iColumnValue == 4)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }
                        }
                        else if (iRowValue == 16 && iColumnValue == 5)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }
                        }
                        else if (iRowValue == 16 && iColumnValue == 6)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }
                        }
                        else if (iRowValue == 16 && iColumnValue == 7)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }
                        }
                        else if (iRowValue == 16 && iColumnValue == 8)
                        {
                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (!string.IsNullOrEmpty(strValue) && strValue != "无")
                            {
                                OfflineFaultAcquisition.Import.lstExchangeValue.Add(Convert.ToDouble(strValue));
                            }

                            break;
                        }
                        #endregion

                        #region ROW 18
                        else if (iRowValue == 18 && iColumnValue == 2)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道1数值")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 18 && iColumnValue == 3)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道2数值")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 18 && iColumnValue == 4)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道3数值")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 18 && iColumnValue == 5)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道4数值")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 18 && iColumnValue == 6)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道5数值")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 18 && iColumnValue == 7)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道6数值")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 18 && iColumnValue == 8)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道7数值")
                            {
                                return RET.NO_EFFECT;
                            }
                        }
                        else if (iRowValue == 18 && iColumnValue == 9)
                        {
                            if (Convert.ToString(row.GetCell(iColumnValue)) != "通道8数值")
                            {
                                return RET.NO_EFFECT;
                            }
                            else
                            {
                                break;
                            }
                        }
                        #endregion

                        #region Other Rows
                        else if (iRowValue >= 19)
                        {
                            if (iColumnValue == 0 || iColumnValue == 1)
                            {
                                continue;
                            }

                            strValue = Convert.ToString(row.GetCell(iColumnValue));
                            if (string.IsNullOrEmpty(strValue))
                            {
                                iValue = 0;
                            }
                            else
                            {
                                iValue = Convert.ToInt32(strValue);
                            }

                            switch (iColumnValue)
                            {
                                case 2:
                                    if (OfflineFaultAcquisition.Import.Channel1Name != "停用")
                                    {
                                        OfflineFaultAcquisition.Import.lstChannel1.Add(iValue);
                                    }
                                    break;
                                case 3:
                                    if (OfflineFaultAcquisition.Import.Channel2Name != "停用")
                                    {
                                        OfflineFaultAcquisition.Import.lstChannel2.Add(iValue);
                                    }
                                    break;
                                case 4:
                                    if (OfflineFaultAcquisition.Import.Channel3Name != "停用")
                                    {
                                        OfflineFaultAcquisition.Import.lstChannel3.Add(iValue);
                                    }
                                    break;
                                case 5:
                                    if (OfflineFaultAcquisition.Import.Channel4Name != "停用")
                                    {
                                        OfflineFaultAcquisition.Import.lstChannel4.Add(iValue);
                                    }
                                    break;
                                case 6:
                                    if (OfflineFaultAcquisition.Import.Channel5Name != "停用")
                                    {
                                        OfflineFaultAcquisition.Import.lstChannel5.Add(iValue);
                                    }
                                    break;
                                case 7:
                                    if (OfflineFaultAcquisition.Import.Channel6Name != "停用")
                                    {
                                        OfflineFaultAcquisition.Import.lstChannel6.Add(iValue);
                                    }
                                    break;
                                case 8:
                                    if (OfflineFaultAcquisition.Import.Channel7Name != "停用")
                                    {
                                        OfflineFaultAcquisition.Import.lstChannel7.Add(iValue);
                                    }
                                    break;
                                case 9:
                                    if (OfflineFaultAcquisition.Import.Channel8Name != "停用")
                                    {
                                        OfflineFaultAcquisition.Import.lstChannel8.Add(iValue);
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                        #endregion
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.EXCELHELPER_GET_CONTENT_INFO_FOR_OSCILLOSCOPE, "GetContentInfo_For_Oscilloscope", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetColumnInfo_for_Excel
        //函数功能：获取列名——为导入软件版本号
        //
        //输入参数：ISheet In_sheet 
        //         ref DataTable Out_dt 
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2020.07.14
        //*************************************************************************
        private static int GetColumnInfo_for_Excel(ISheet In_sheet, ref DataTable Out_dt)
        {
            try
            {
                IRow row = In_sheet.GetRow(0);
                if (row == null)
                {
                    return RET.NO_EFFECT;
                }

                for (int iColumnValue = 0; iColumnValue < row.LastCellNum; iColumnValue++)
                {
                    Out_dt.Columns.Add(new DataColumn());
                    Out_dt.Columns[iColumnValue].ColumnName = Convert.ToString(row.GetCell(iColumnValue));
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("GetColumnInfo_for_Excel", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetColumnInfo
        //函数功能：获取列名
        //
        //输入参数：ISheet In_sheet 
        //         ref DataTable Out_dt 
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2019.10.25
        //*************************************************************************
        private static int GetColumnInfo(ISheet In_sheet, ref DataTable Out_dt)
        {    
            try
            {    
                IRow row = In_sheet.GetRow(0);
                if (row == null)
                {
                    return RET.NO_EFFECT;
                }

                for (int iColumnValue = 0; iColumnValue < row.LastCellNum; iColumnValue++)
                {
                    Out_dt.Columns.Add(new DataColumn());
                    Out_dt.Columns[iColumnValue].ColumnName = Convert.ToString(row.GetCell(iColumnValue));
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("GetColumnInfo", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetContentInfo_for_SoftwareVersion
        //函数功能：获取Excel中软件版本号
        //
        //输入参数：ISheet In_sheet 
        //         ref DataTable Out_dt 
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2020.07.14
        //*************************************************************************
        private static int GetContentInfo_for_SoftwareVersion(ISheet In_sheet, ref DataTable Out_dt)
        {
            //int iRet = -1;
            string strInfoSoft = null;
            string strInfoName = null;
            string strInfoHard = null;
            //string stringInfo = null;

            try
            {
                //获取伺服驱动器的设备名称
                strInfoName = SoftwareStateParameterSet.ServoName;
                //获取伺服驱动器的软件版本号
                strInfoSoft = SoftwareStateParameterSet.ServoSoftwareVersion;
                //获取伺服驱动器的硬件版本号
                strInfoHard = SoftwareStateParameterSet.ServoHardwareVersion;

                //获取Excel单元格第4行第11列里的软件版本号
                ICell cell = In_sheet.GetRow(4).GetCell(11);

                //stringInfo = In_sheet.GetRow(4).GetCell(11).ToString();   //软件版本号转换成字符串
                SoftwareStateParameterSet.ServoSoftwareVersion_For_Excel = In_sheet.GetRow(4).GetCell(11).ToString();   //Excel中软件版本号转换成字符串
                SoftwareStateParameterSet.ServoHardwareVersion_For_Excel = In_sheet.GetRow(3).GetCell(11).ToString();   //Excel中硬件版本号转换成字符串
                SoftwareStateParameterSet.ServoNameVersion_For_Excel = In_sheet.GetRow(2).GetCell(11).ToString();   //Excel中设备名称转换成字符串

            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("GetContentInfo_for_SoftwareVersion", ex);
                return RET.ERROR;
            }
            return RET.SUCCEEDED;
        }

        //*************************************************************************
        //函数名称：GetContentInfo_for_Excel
        //函数功能：获取内容
        //
        //输入参数：ISheet In_sheet 
        //         ref DataTable Out_dt 
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2020.07.14
        //*************************************************************************
        private static int GetContentInfo_for_Excel(ISheet In_sheet, ref DataTable Out_dt)
        {
            bool bEmpty = true;//判断DataTable是否某行全部为空 若是：跳出

            try
            {
                for (int iRowValue = 1; iRowValue < In_sheet.LastRowNum + 1; iRowValue++)
                {
                    bEmpty = true;
                    DataRow dr = Out_dt.NewRow();

                    //轮询获取每一整行信息
                    IRow row = In_sheet.GetRow(iRowValue);
                    if (row == null)
                    {
                        if (Out_dt.Rows.Count > 0)
                        {
                            return RET.SUCCEEDED;
                        }
                        else
                        {
                            return RET.NO_EFFECT;
                        }
                    }

                    for (int iColumnValue = 0; iColumnValue < row.LastCellNum; iColumnValue++)
                    {
                        if (row.GetCell(iColumnValue) == null)
                        {
                            dr[iColumnValue] = null;
                            continue;
                        }

                        if (Convert.ToString(row.GetCell(iColumnValue)) == "")
                        {
                            dr[iColumnValue] = null;
                        }
                        else
                        {
                            if (row.GetCell(iColumnValue).CellType == CellType.Formula)
                            {
                                row.GetCell(iColumnValue).SetCellType(CellType.String);
                            }

                            dr[iColumnValue] = row.GetCell(iColumnValue).ToString().Trim();

                            bEmpty = false;
                        }
                    }

                    if (bEmpty == false)
                    {
                        Out_dt.Rows.Add(dr);
                    }
                    else
                    {
                        if (Out_dt.Rows.Count > 0)
                        {
                            return RET.SUCCEEDED;
                        }
                        else
                        {
                            return RET.NO_EFFECT;
                        }
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("GetContentInfo_for_Excel", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：GetContentInfo
        //函数功能：获取内容
        //
        //输入参数：ISheet In_sheet 
        //         ref DataTable Out_dt 
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2019.10.25
        //*************************************************************************
        private static int GetContentInfo(ISheet In_sheet, ref DataTable Out_dt)
        {
            bool bEmpty = true;//判断DataTable是否某行全部为空 若是：跳出

            try
            {
                for (int iRowValue = 1; iRowValue < In_sheet.LastRowNum + 1; iRowValue++)
                {
                    bEmpty = true;
                    DataRow dr = Out_dt.NewRow();

                    //轮询获取每一整行信息
                    IRow row = In_sheet.GetRow(iRowValue);
                    if (row == null)
                    {
                        if (Out_dt.Rows.Count > 0)
                        {
                            return RET.SUCCEEDED;
                        }
                        else
                        {
                            return RET.NO_EFFECT;
                        }
                    }
                   
                    for (int iColumnValue = 0; iColumnValue < row.LastCellNum; iColumnValue++)
                    {                      
                        if (row.GetCell(iColumnValue) == null)
                        {
                            dr[iColumnValue] = null;
                            continue;
                        }

                        if (Convert.ToString(row.GetCell(iColumnValue)) == "")
                        {
                            dr[iColumnValue] = null;
                        }
                        else
                        {
                            if (row.GetCell(iColumnValue).CellType == CellType.Formula)
                            {
                                row.GetCell(iColumnValue).SetCellType(CellType.String);
                            }

                            dr[iColumnValue] = row.GetCell(iColumnValue).ToString().Trim();

                            bEmpty = false;
                        }
                    }

                    if (bEmpty == false)
                    {
                        Out_dt.Rows.Add(dr);
                    } 
                    else
                    {
                        if (Out_dt.Rows.Count > 0)
                        {
                            return RET.SUCCEEDED;
                        }
                        else
                        {
                            return RET.NO_EFFECT;
                        }
                    }                 
                }
                   
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("GetContentInfo", ex);
                return RET.ERROR;
            }
        }
        #endregion

        #region 写操作
        //*************************************************************************
        //函数名称：GetWritePath
        //函数功能：获取保存地址
        //
        //输入参数：ref string Out_strFilePath   地址信息  
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.10.25&2023.11.10
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int GetWritePath(ref string Out_strFilePath, int In_iExcelType)
        {
            int iRet = -1;
            SaveFileDialog saveFileDialog = null;

            try
            {
                saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "(*.xlsx)|*.xlsx";
                saveFileDialog.FilterIndex = 1;
                saveFileDialog.Title = "保存配置文件";
                //openFileDialog.InitialDirectory = System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory);
                saveFileDialog.InitialDirectory = IniHelper.IniReadValue("ServoStudio", "OscilloscopeWaveformPath", FilePath.Ini).Replace("\\\\","\\");//由Lilbert于2023.11.10添加读取示波器波形保存地址

                saveFileDialog.ValidateNames = false;
                saveFileDialog.CheckFileExists = true;
                saveFileDialog.CheckPathExists = false;
                saveFileDialog.CreatePrompt = false;
                saveFileDialog.OverwritePrompt = true;
                saveFileDialog.AddExtension = false;

                switch (In_iExcelType)
                {
                    case ExcelType.MotorConfig:
                        saveFileDialog.FileName = "电机反馈_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.AmplitudeConfig:
                        saveFileDialog.FileName = "限幅保护_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.IOConfig:
                        saveFileDialog.FileName = "数字IO_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.BrakeConfig:
                        saveFileDialog.FileName = "抱闸制动_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.FilterConfig:
                        saveFileDialog.FileName = "控制滤波_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.Oscilloscope:
                        saveFileDialog.FileName = "采样文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.FaultAcquisition:
                        saveFileDialog.FileName = "故障数据采样文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");//由Lilbert于2023.04.04添加故障数据采样文件
                        break;
                    case ExcelType.CurrentLoop:
                        saveFileDialog.FileName = "电流环_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.AdvancedFeedback:
                        saveFileDialog.FileName = "高级功能_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.PositionLoop:
                        saveFileDialog.FileName = "位置环_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.SpeedLoop:
                        saveFileDialog.FileName = "速度环_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.NormalSetting:
                        saveFileDialog.FileName = "一般设定_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.GearRatio:
                        saveFileDialog.FileName = "电子齿轮比_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.Position:
                        saveFileDialog.FileName = "位置模式_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.Speed:
                        saveFileDialog.FileName = "速度模式_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.Torque:
                        saveFileDialog.FileName = "转矩模式_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.OfflineInertiaIdentification:
                        saveFileDialog.FileName = "离线惯量识别_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    case ExcelType.SeekZero:
                        saveFileDialog.FileName = "回零模式_配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        break;
                    default:
                        //saveFileDialog.FileName = "配置文件" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        saveFileDialog.FileName = "配置文件" + "_" + GlobalCurrentInput.SelectedAxisID.ToString() + "_" + DateTime.Now.ToString().Replace("/", "").Replace(":", "").Replace(" ", "_");
                        
                        break;
                }
                
                if ((bool)saveFileDialog.ShowDialog())
                {
                    IniHelper.IniWriteValue("ServoStudio", "OscilloscopeWaveformPath", saveFileDialog.FileName.Replace("\\" + saveFileDialog.SafeFileName, ""), FilePath.Ini);//由Lilbert于2023.11.10添加写入示波器波形保存地址
                    
                    Out_strFilePath = saveFileDialog.FileName;
                    iRet = RET.SUCCEEDED;
                }
                else
                {
                    iRet = RET.NO_EFFECT;
                }
            }
            catch
            {
                iRet = RET.ERROR;               
            }

            return iRet;
        }

        //*************************************************************************
        //函数名称：WriteIntoExcel_For_Software
        //函数功能：写入Excel
        //
        //输入参数：string In_strFilePath         地址信息 
        //         DataTable In_clsDataTable     DataTable
        //         int In_iIndex                 文件类型
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2022.01.10
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int WriteIntoExcel_For_Software(string In_strFilePath, DataTable In_clsDataTable, int In_iIndex)
        {
            int iRet = -1;
            IWorkbook workBook = null;
            ISheet sheet = null;
            FileStream fileStream = null;

            try
            {
                workBook = new XSSFWorkbook();
                sheet = workBook.CreateSheet();

                if (File.Exists(In_strFilePath))
                {
                    File.Delete(In_strFilePath);
                }

                //设置Excel列信息
                iRet = SetExcelColumnInfo(workBook, sheet, In_clsDataTable, In_iIndex);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                //设置Excel数据信息
                iRet = SetExcelContentInfo(workBook, sheet, In_clsDataTable, In_iIndex);
                if (iRet == RET.ERROR)
                {
                    return iRet;
                }

                //Excel写软件版本号
                iRet = SetExcelContentInfo_For_Software(workBook, sheet, In_clsDataTable, In_iIndex);
                if (iRet == RET.ERROR)
                {
                    return iRet;
                }

                //写入Excel-内容可以为空，但是必有列名
                fileStream = new FileStream(In_strFilePath, FileMode.CreateNew, FileAccess.Write);
                workBook.Write(fileStream);

                //设置文件隐藏属性
                FileAttributeSet(In_strFilePath, In_iIndex);

                return RET.SUCCEEDED;
            }
            catch
            {
                return RET.FILE_OCCUPIED;
            }
            finally
            {
                if (workBook != null)
                {
                    workBook.Close();
                }

                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }

        //*************************************************************************
        //函数名称：WriteIntoExcel
        //函数功能：写入Excel
        //
        //输入参数：string In_strFilePath         地址信息 
        //         DataTable In_clsDataTable     DataTable
        //         int In_iIndex                 文件类型
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2019.10.25
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int WriteIntoExcel(string In_strFilePath, DataTable In_clsDataTable, int In_iIndex)
        {
            int iRet = -1;
            IWorkbook workBook = null;
            ISheet sheet = null;
            FileStream fileStream = null;

            try
            {               
                workBook = new XSSFWorkbook();
                sheet = workBook.CreateSheet();

                if (File.Exists(In_strFilePath))
                {
                    File.Delete(In_strFilePath);
                }

                //设置Excel列信息
                iRet = SetExcelColumnInfo(workBook, sheet, In_clsDataTable, In_iIndex);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                //设置Excel数据信息
                iRet = SetExcelContentInfo(workBook, sheet, In_clsDataTable, In_iIndex);
                if (iRet == RET.ERROR)
                {
                    return iRet;
                }

                //写入Excel-内容可以为空，但是必有列名
                fileStream = new FileStream(In_strFilePath, FileMode.CreateNew, FileAccess.Write);
                workBook.Write(fileStream);

                //设置文件隐藏属性
                FileAttributeSet(In_strFilePath, In_iIndex);

                return RET.SUCCEEDED;
            }
            catch
            {
                return RET.FILE_OCCUPIED;
            }
            finally
            {
                if (workBook != null)
                {
                    workBook.Close();
                }

                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }

        //*************************************************************************
        //函数名称：WriteIntoExcel_ForImportConfig
        //函数功能：写入Excel
        //
        //输入参数：string In_strFilePath         地址信息 
        //         DataTable In_clsDataTable     DataTable
        //         int In_iIndex                 文件类型
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2023.05.17
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int WriteIntoExcel_ForImportConfig(string In_strFilePath, DataTable In_clsDataTable, int In_iIndex)
        {
            int iRet = -1;
            IWorkbook workBook = null;
            ISheet sheet = null;
            FileStream fileStream = null;

            try
            {
                workBook = new XSSFWorkbook();
                sheet = workBook.CreateSheet();

                if (File.Exists(In_strFilePath))
                {
                    File.Delete(In_strFilePath);
                }

                //设置Excel列信息
                iRet = SetExcelColumnInfo(workBook, sheet, In_clsDataTable, In_iIndex);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                //设置Excel数据信息
                iRet = SetExcelContentInfo(workBook, sheet, In_clsDataTable, In_iIndex);
                if (iRet == RET.ERROR)
                {
                    return iRet;
                }

                //Excel写软件版本号
                iRet = SetExcelContentInfo_For_Software(workBook, sheet, In_clsDataTable, In_iIndex);
                if (iRet == RET.ERROR)
                {
                    return iRet;
                }

                //写入Excel-内容可以为空，但是必有列名
                fileStream = new FileStream(In_strFilePath, FileMode.CreateNew, FileAccess.Write);
                workBook.Write(fileStream);

                //设置文件隐藏属性
                FileAttributeSet(In_strFilePath, In_iIndex);

                return RET.SUCCEEDED;
            }
            catch
            {
                return RET.FILE_OCCUPIED;
            }
            finally
            {
                if (workBook != null)
                {
                    workBook.Close();
                }

                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }

        public static int WriteIntoExcel(string In_strFilePath, DataTable dt)
        {
            FileStream fileStream = null;
            IWorkbook workBook = null;
            ISheet sheet = null;
            IRow Row = null;
            ICellStyle cellStyle = null;

            try
            {
                if (!File.Exists(In_strFilePath))
                {
                    return RET.ERROR;
                }

                //获取文件流
                var stream = new FileStream(In_strFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                workBook = new XSSFWorkbook(stream);
                sheet = workBook.GetSheetAt(0);
                Row = sheet.CreateRow(sheet.LastRowNum + 1);
                cellStyle = SetExcelContentStyle(workBook, 0);

                for (int j = 0; j < dt.Columns.Count; j++)
                {
                    Row.CreateCell(j).SetCellValue(Convert.ToString(dt.Rows[0][j]));
                    Row.GetCell(j).CellStyle = cellStyle;//Cell风格
                }

                //写入Excel-内容可以为空，但是必有列名
                fileStream = new FileStream(In_strFilePath, FileMode.Open, FileAccess.Write, FileShare.ReadWrite);
                workBook.Write(fileStream);

                //设置文件隐藏属性
                FileAttributeSet(In_strFilePath, ExcelType.SoftwareErrorLog);
               
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("WriteIntoExcel", ex);
                return RET.FILE_OCCUPIED;
            }
            finally
            {
                if (workBook != null)
                {
                    workBook.Close();
                }

                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }

        //*************************************************************************
        //函数名称：SetExcelColumnInfo
        //函数功能：设置Excel列信息
        //
        //输入参数：IWorkbook In_workBook
        //         ISheet In_sheet 
        //         DataTable In_clsDataTable
        //         int In_iIndex                保存文件的类别
        //
        //输出参数：1:OK
        //        -1:Error
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.10.25&2023.06.12
        //*************************************************************************
        private static int SetExcelColumnInfo(IWorkbook In_workBook, ISheet In_sheet, DataTable In_clsDataTable, int In_iIndex)
        {
            try
            {
                if (In_clsDataTable == null)
                {
                    return RET.NO_EFFECT;
                }

                ICellStyle headStyle = SetExcelColumnStyle(In_workBook);
                                                     
                //设置列信息
                IRow row = In_sheet.CreateRow(0);
                for (int iColumnValue = 0; iColumnValue < In_clsDataTable.Columns.Count; iColumnValue++)
                {
                    switch (In_iIndex)
                    {
                        case ExcelType.EtherCAT:
                            if ((iColumnValue == 3) || (iColumnValue == 4))
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 20 * 256);
                            }  
                            else if (iColumnValue == 12)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 30 * 256);
                            }
                            else
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 10 * 256);
                            }                           
                            break;

                        case ExcelType.SoftwareErrorLog:
                            if (iColumnValue == 0)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 10 * 256);
                            }
                            else if (iColumnValue == 1)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 25 * 256);
                            }
                            else if (iColumnValue == 2)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 120 * 256);
                            }
                            else if (iColumnValue == 3)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 10 * 256);
                            }
                            else
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 15 * 256);
                            }
                            break;
                        case ExcelType.MotorConfig:
                        case ExcelType.AmplitudeConfig:
                        case ExcelType.IOConfig:
                        case ExcelType.BrakeConfig:
                        case ExcelType.FilterConfig:
                        case ExcelType.CurrentLoop:
                        case ExcelType.AdvancedFeedback:
                        case ExcelType.PositionLoop:
                        case ExcelType.SpeedLoop:
                        case ExcelType.GearRatio:                                
                            if (iColumnValue == 0)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 10 * 256);
                            }
                            else if (iColumnValue == 1)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 10 * 256);
                            }
                            else if (iColumnValue == 2)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 30 * 256);
                            }
                            else if (iColumnValue == 3)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 30 * 256);
                            }
                            else
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 15 * 256);
                            }
                            break;
                        case ExcelType.Speed:
                        case ExcelType.Torque:
                        case ExcelType.Position:
                        case ExcelType.NormalSetting:
                        case ExcelType.OfflineInertiaIdentification:
                            if (iColumnValue == 0)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 10 * 256);
                            }
                            else if (iColumnValue == 1)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 10 * 256);
                            }
                            else if (iColumnValue == 2)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 30 * 256);
                            }
                            else if (iColumnValue == 3)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 30 * 256);
                            }
                            else if (iColumnValue == 4)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 15 * 256);
                            }
                            else
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 15 * 256);
                            }
                            break;
                        case ExcelType.SeekZero:
                            if (iColumnValue == 0)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 10 * 256);
                            }
                            else if (iColumnValue == 1)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 10 * 256);
                            }
                            else if (iColumnValue == 2)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 30 * 256);
                            }
                            else if (iColumnValue == 3)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 30 * 256);
                            }
                            else
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 30 * 256);
                            }
                            break;
                        case ExcelType.Monitor:                            
                            if (iColumnValue == 1)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 30 * 256);
                            }
                            else if (iColumnValue == 2)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 30 * 256);
                            }
                            else
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 10 * 256);
                            }
                            break;
                        case ExcelType.MotorLibraryLog:
                            if (iColumnValue == 0)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 40 * 256);
                            }
                            else
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 20 * 256);
                            }
                            break;
                        case ExcelType.FaultDataConfig://由Lilbert于2023.06.12添加故障数据采集配置类型
                            if (iColumnValue == 0)
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 10 * 256);
                            }
                            else
                            {
                                In_sheet.SetColumnWidth(iColumnValue, 30 * 256);
                            }
                            break;
                        default:
                            break;
                    }
                   
                    row.CreateCell(iColumnValue).SetCellValue(Convert.ToString(In_clsDataTable.Columns[iColumnValue].ColumnName));
                    row.GetCell(iColumnValue).CellStyle = headStyle;
                }

                return RET.SUCCEEDED;              
            }
            catch
            {
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：SetExcelContentInfo_For_Software
        //函数功能：设置Excel内容信息
        //
        //输入参数：IWorkbook In_workBook
        //         ISheet In_sheet 
        //         DataTable In_clsDataTable
        //
        //输出参数：1:OK
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2022.01.10
        //************************************************************************* 
        private static int SetExcelContentInfo_For_Software(IWorkbook In_workBook, ISheet In_sheet, DataTable In_clsDataTable, int In_iIndex)
        {
            string strServoSoftwareVersion = null;
            string strHardwareVersion = null;
            string strServoName = null;

            try
            {
                if (In_clsDataTable == null) return RET.NO_EFFECT;
                if (In_clsDataTable.Rows.Count == 0) return RET.NO_EFFECT;

                ICellStyle cellStyle = SetExcelContentStyle(In_workBook, 0);

                //获取伺服驱动器的设备名称
                strServoName = SoftwareStateParameterSet.ServoName;
                In_sheet.GetRow(2).GetCell(11).SetCellValue(strServoName);

                //获取伺服驱动器的硬件版本号
                strHardwareVersion = SoftwareStateParameterSet.ServoHardwareVersion;
                In_sheet.GetRow(3).GetCell(11).SetCellValue(strHardwareVersion);

                //获取伺服驱动器的软件版本号
                strServoSoftwareVersion = SoftwareStateParameterSet.ServoSoftwareVersion;
                In_sheet.GetRow(4).GetCell(11).SetCellValue(strServoSoftwareVersion);

                return RET.SUCCEEDED;
            }
            catch
            {
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：SetExcelContentInfo
        //函数功能：设置Excel内容信息
        //
        //输入参数：IWorkbook In_workBook
        //         ISheet In_sheet 
        //         DataTable In_clsDataTable
        //
        //输出参数：1:OK
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2019.10.25
        //************************************************************************* 
        private static int SetExcelContentInfo(IWorkbook In_workBook, ISheet In_sheet, DataTable In_clsDataTable, int In_iIndex)
        {
            try
            {
                if (In_clsDataTable == null) return RET.NO_EFFECT;
                if (In_clsDataTable.Rows.Count == 0) return RET.NO_EFFECT;

                ICellStyle cellStyle = SetExcelContentStyle(In_workBook, 0);
              
                for (int iRowValue = 0, iRowExcel = 1; iRowValue < In_clsDataTable.Rows.Count; iRowValue++, iRowExcel++)
                {                    
                    IRow iRow = In_sheet.CreateRow(iRowExcel);
                    DataRow dataRow = In_clsDataTable.Rows[iRowValue];

                    for (int j = 0; j < dataRow.ItemArray.Length; j++)
                    {                                                    
                        if (dataRow.ItemArray[j] != null)
                        {
                            iRow.CreateCell(j).SetCellValue(Convert.ToString(dataRow.ItemArray[j]));
                            iRow.GetCell(j).CellStyle = cellStyle;//Cell风格
                        }                                                 
                    }           
                }
             
                return RET.SUCCEEDED;
            }
            catch
           {             
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：WriteIntoExcel_For_Oscilloscope
        //函数功能：写入Excel
        //
        //输入参数：string In_strFilePath         地址信息 
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Ryan
        //更新时间：2019.10.25
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int WriteIntoExcel_For_Oscilloscope(string In_strFilePath)
        {
            int iRet = -1;
            IWorkbook workBook = null;
            ISheet sheet = null;
            FileStream fileStream = null;

            try
            {
                workBook = new XSSFWorkbook();
                sheet = workBook.CreateSheet();

                if (File.Exists(In_strFilePath))
                {
                    File.Delete(In_strFilePath);
                }

                //获取数据
                iRet = SetExcelInfo_For_Oscilloscope(workBook, sheet);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                //写入Excel
                fileStream = new FileStream(In_strFilePath, FileMode.CreateNew, FileAccess.Write);
                workBook.Write(fileStream);

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.EXCELHELPER_WRITE_INTO_EXCEL_FOR_OSCILLOSCOPE, "WriteIntoExcel_For_Oscilloscope", ex);
                return RET.FILE_OCCUPIED;
            }
            finally
            {
                if (workBook != null)
                {
                    workBook.Close();
                }

                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }

        //*************************************************************************
        //函数名称：WriteIntoExcel_For_FaultAcquisition
        //函数功能：写入Excel-故障数据采集
        //
        //输入参数：string In_strFilePath         地址信息 
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //函数注释：catch里面在调用错误日志记录，可能会产生死循环   
        //*************************************************************************
        public static int WriteIntoExcel_For_FaultAcquisition(string In_strFilePath)
        {
            int iRet = -1;
            IWorkbook workBook = null;
            ISheet sheet = null;
            FileStream fileStream = null;

            try
            {
                workBook = new XSSFWorkbook();
                sheet = workBook.CreateSheet();

                if (File.Exists(In_strFilePath))
                {
                    File.Delete(In_strFilePath);
                }

                //获取数据
                iRet = SetExcelInfo_For_FaultAcquisition(workBook, sheet);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                //写入Excel
                fileStream = new FileStream(In_strFilePath, FileMode.CreateNew, FileAccess.Write);
                workBook.Write(fileStream);

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.EXCELHELPER_WRITE_INTO_EXCEL_FOR_FAULT_ACQUISITION, "WriteIntoExcel_For_FaultAcquisition", ex);
                return RET.FILE_OCCUPIED;
            }
            finally
            {
                if (workBook != null)
                {
                    workBook.Close();
                }

                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
        }

        //*************************************************************************
        //函数名称：SetExcelInfo_For_Oscilloscope
        //函数功能：设置Excel信息
        //
        //输入参数：IWorkbook In_workBook
        //         ISheet In_sheet 
        //         DataTable In_clsDataTable
        //         int In_iIndex                保存文件的类别
        //
        //输出参数：1:OK
        //        -1:Error
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.10.25&2022.08.02
        //*************************************************************************
        private static int SetExcelInfo_For_Oscilloscope(IWorkbook In_workBook, ISheet In_sheet)
        {
            int iLength = 0;

            try
            {
                if (OfflineOscilloscope.Export == null)
                {
                    return RET.NO_EFFECT;
                }

                ICellStyle headStyle = SetExcelColumnStyle(In_workBook);

                //设置cell的宽度
                for (int i = 0; i < 6; i++)
                {
                    In_sheet.SetColumnWidth(i, 18 * 256);
                }
                   

                //设置列信息
                IRow row0 = In_sheet.CreateRow(0);
                row0.CreateCell(0).SetCellValue("采样周期");
                row0.CreateCell(1).SetCellValue("采样时长");
                row0.CreateCell(2).SetCellValue("采样连续");
                row0.CreateCell(3).SetCellValue("采样日期");
                row0.CreateCell(4).SetCellValue("轴地址");
                row0.GetCell(0).CellStyle = headStyle;
                row0.GetCell(1).CellStyle = headStyle;
                row0.GetCell(2).CellStyle = headStyle;
                row0.GetCell(3).CellStyle = headStyle;
                row0.GetCell(4).CellStyle = headStyle;

                IRow row1 = In_sheet.CreateRow(1);
                row1.CreateCell(0).SetCellValue(OfflineOscilloscope.Export.Period);
                row1.CreateCell(1).SetCellValue(OfflineOscilloscope.Export.Duration);
                row1.CreateCell(2).SetCellValue(OfflineOscilloscope.Export.IsContinuous);
                row1.CreateCell(3).SetCellValue(OfflineOscilloscope.Export.Date);               
                row1.CreateCell(4).SetCellValue(GlobalCurrentInput.SelectedAxisID);
                
                IRow row2 = In_sheet.CreateRow(3);
                row2.CreateCell(0).SetCellValue("通道1名称");
                row2.CreateCell(1).SetCellValue("通道2名称");
                row2.CreateCell(2).SetCellValue("通道3名称");
                row2.CreateCell(3).SetCellValue("通道4名称");
                row2.CreateCell(4).SetCellValue("电流环增益");
                row2.CreateCell(5).SetCellValue("电流环时间积分");
                row2.GetCell(0).CellStyle = headStyle;
                row2.GetCell(1).CellStyle = headStyle;
                row2.GetCell(2).CellStyle = headStyle;
                row2.GetCell(3).CellStyle = headStyle;
                row2.GetCell(4).CellStyle = headStyle;
                row2.GetCell(5).CellStyle = headStyle;

                IRow row3 = In_sheet.CreateRow(4);
                row3.CreateCell(0).SetCellValue(OfflineOscilloscope.Export.Channel1Name);
                row3.CreateCell(1).SetCellValue(OfflineOscilloscope.Export.Channel2Name);
                row3.CreateCell(2).SetCellValue(OfflineOscilloscope.Export.Channel3Name);
                row3.CreateCell(3).SetCellValue(OfflineOscilloscope.Export.Channel4Name);
                row3.CreateCell(4).SetCellValue(GlobalCurrentInput.CurrentLoopGain);
                row3.CreateCell(5).SetCellValue(GlobalCurrentInput.CurrentLoopTimeConstant);

                IRow row4 = In_sheet.CreateRow(6);
                row4.CreateCell(0).SetCellValue("触发模式");
                row4.CreateCell(1).SetCellValue("触发通道");
                row4.CreateCell(2).SetCellValue("预触发");
                row4.CreateCell(3).SetCellValue("触发水平");
                row4.CreateCell(4).SetCellValue("位置环增益");
                row4.CreateCell(5).SetCellValue("负载惯量比");
                row4.GetCell(0).CellStyle = headStyle;
                row4.GetCell(1).CellStyle = headStyle;
                row4.GetCell(2).CellStyle = headStyle;
                row4.GetCell(3).CellStyle = headStyle;
                row4.GetCell(4).CellStyle = headStyle;
                row4.GetCell(5).CellStyle = headStyle;

                IRow row5 = In_sheet.CreateRow(7);
                row5.CreateCell(0).SetCellValue(OfflineOscilloscope.Export.TriggerMode);
                row5.CreateCell(1).SetCellValue(OfflineOscilloscope.Export.TriggerChannel);
                row5.CreateCell(2).SetCellValue(OfflineOscilloscope.Export.PreTrigger);
                row5.CreateCell(3).SetCellValue(OfflineOscilloscope.Export.TriggerLevel);
                row5.CreateCell(4).SetCellValue(GlobalCurrentInput.PositionLoopGain);
                row5.CreateCell(5).SetCellValue(GlobalCurrentInput.LoadInertiaRatio);

                IRow row6 = In_sheet.CreateRow(9);
                row6.CreateCell(0).SetCellValue("通道1倍乘");
                row6.CreateCell(1).SetCellValue("通道2倍乘");
                row6.CreateCell(2).SetCellValue("通道3倍乘");
                row6.CreateCell(3).SetCellValue("通道4倍乘");
                row6.CreateCell(4).SetCellValue("速度环增益");
                row6.CreateCell(5).SetCellValue("速度环时间积分");
                row6.GetCell(0).CellStyle = headStyle;
                row6.GetCell(1).CellStyle = headStyle;
                row6.GetCell(2).CellStyle = headStyle;
                row6.GetCell(3).CellStyle = headStyle;
                row6.GetCell(4).CellStyle = headStyle;
                row6.GetCell(5).CellStyle = headStyle;

                IRow row7 = In_sheet.CreateRow(10);
                row7.CreateCell(0).SetCellValue(OfflineOscilloscope.Export.Channel1Doubling);
                row7.CreateCell(1).SetCellValue(OfflineOscilloscope.Export.Channel2Doubling);
                row7.CreateCell(2).SetCellValue(OfflineOscilloscope.Export.Channel3Doubling);
                row7.CreateCell(3).SetCellValue(OfflineOscilloscope.Export.Channel4Doubling);
                row7.CreateCell(4).SetCellValue(GlobalCurrentInput.SpeedLoopGain);
                row7.CreateCell(5).SetCellValue(GlobalCurrentInput.SpeedLoopTimeConstant);

                IRow row8 = In_sheet.CreateRow(12);
                row8.CreateCell(0).SetCellValue("通道1单位");
                row8.CreateCell(1).SetCellValue("通道2单位");
                row8.CreateCell(2).SetCellValue("通道3单位");
                row8.CreateCell(3).SetCellValue("通道4单位");
                row8.CreateCell(4).SetCellValue("第一转矩滤波时间");
                row8.GetCell(0).CellStyle = headStyle;
                row8.GetCell(1).CellStyle = headStyle;
                row8.GetCell(2).CellStyle = headStyle;
                row8.GetCell(3).CellStyle = headStyle;
                row8.GetCell(4).CellStyle = headStyle;

                IRow row9 = In_sheet.CreateRow(13);
                for (int i = 0; i < 4; i++)
                {
                    int iCount = OfflineOscilloscope.Export.lstUnit.Count;
                    if (i < iCount)
                    {
                        row9.CreateCell(i).SetCellValue(OfflineOscilloscope.Export.lstUnit[i]);
                    }
                    else
                    {
                        row9.CreateCell(i).SetCellValue("无");
                    }                   
                }
                row9.CreateCell(4).SetCellValue(GlobalCurrentInput.FirstTrqcmdFilterTime);

                IRow row10 = In_sheet.CreateRow(15);
                row10.CreateCell(0).SetCellValue("通道1换算");
                row10.CreateCell(1).SetCellValue("通道2换算");
                row10.CreateCell(2).SetCellValue("通道3换算");
                row10.CreateCell(3).SetCellValue("通道4换算");
                row10.GetCell(0).CellStyle = headStyle;
                row10.GetCell(1).CellStyle = headStyle;
                row10.GetCell(2).CellStyle = headStyle;
                row10.GetCell(3).CellStyle = headStyle;

                IRow row11 = In_sheet.CreateRow(16);
                for (int i = 0; i < 4; i++)
                {
                    int iCount = OfflineOscilloscope.Export.lstUnit.Count;
                    if (i < iCount)
                    {
                        row11.CreateCell(i).SetCellValue(OfflineOscilloscope.Export.lstExchangeValue[i].ToString());
                    }
                    else
                    {
                        row11.CreateCell(i).SetCellValue("无");
                    }
                }

                IRow row12 = In_sheet.CreateRow(18);
                row12.CreateCell(0).SetCellValue("采样个数");
                row12.CreateCell(1).SetCellValue("采样时间");
                row12.CreateCell(2).SetCellValue("通道1数值");
                row12.CreateCell(3).SetCellValue("通道2数值");
                row12.CreateCell(4).SetCellValue("通道3数值");
                row12.CreateCell(5).SetCellValue("通道4数值");               
                row12.GetCell(0).CellStyle = headStyle;
                row12.GetCell(1).CellStyle = headStyle;
                row12.GetCell(2).CellStyle = headStyle;
                row12.GetCell(3).CellStyle = headStyle;
                row12.GetCell(4).CellStyle = headStyle;
                row12.GetCell(5).CellStyle = headStyle;

                iLength = OfflineOscilloscope.Export.lstChannel1.Count;                         
                for (int i = 19; i < 19 + iLength; i++)
                {
                    IRow row = In_sheet.CreateRow(i);
                    if (OfflineOscilloscope.Export.Channel1Name != "停用")
                    {                        
                        row.CreateCell(0).SetCellValue(Convert.ToString(i - 18));
                        row.CreateCell(1).SetCellValue(Convert.ToString(Convert.ToDouble(OfflineOscilloscope.Export.Period.Replace("μs", "").Replace("ms", "")) * (i - 18)));
                        row.CreateCell(2).SetCellValue(OfflineOscilloscope.Export.lstChannel1[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(0).SetCellValue("");
                        row.CreateCell(1).SetCellValue("");
                        row.CreateCell(2).SetCellValue("");
                    }

                    if (OfflineOscilloscope.Export.Channel2Name != "停用")
                    {
                        row.CreateCell(3).SetCellValue(OfflineOscilloscope.Export.lstChannel2[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(3).SetCellValue("");
                    }

                    if (OfflineOscilloscope.Export.Channel3Name != "停用")
                    {
                        row.CreateCell(4).SetCellValue(OfflineOscilloscope.Export.lstChannel3[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(4).SetCellValue("");
                    }

                    if (OfflineOscilloscope.Export.Channel4Name != "停用")
                    {
                        row.CreateCell(5).SetCellValue(OfflineOscilloscope.Export.lstChannel4[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(5).SetCellValue("");
                    }                  
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.EXCELHELPER_SET_EXCEL_INFO_FOR_OSCILLOSCOPE, "SetExcelInfo_For_Oscilloscope", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：SetExcelInfo_For_FaultAcquisition
        //函数功能：设置Excel信息
        //
        //输入参数：IWorkbook In_workBook
        //         ISheet In_sheet 
        //         DataTable In_clsDataTable
        //         int In_iIndex                保存文件的类别
        //
        //输出参数：1:OK
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        private static int SetExcelInfo_For_FaultAcquisition(IWorkbook In_workBook, ISheet In_sheet)
        {
            int iLength = 0;

            try
            {
                if (OfflineFaultAcquisition.Export == null)
                {
                    return RET.NO_EFFECT;
                }

                ICellStyle headStyle = SetExcelColumnStyle(In_workBook);

                //设置cell的宽度
                for (int i = 0; i < 11; i++)
                {
                    In_sheet.SetColumnWidth(i, 18 * 320);
                }


                //设置列信息
                IRow row0 = In_sheet.CreateRow(0);
                row0.CreateCell(0).SetCellValue("采样周期");
                row0.CreateCell(1).SetCellValue("采样时长");
                row0.CreateCell(2).SetCellValue("采样日期");
                row0.CreateCell(3).SetCellValue("轴地址");
                row0.GetCell(0).CellStyle = headStyle;
                row0.GetCell(1).CellStyle = headStyle;
                row0.GetCell(2).CellStyle = headStyle;
                row0.GetCell(3).CellStyle = headStyle;

                IRow row1 = In_sheet.CreateRow(1);
                row1.CreateCell(0).SetCellValue(OfflineFaultAcquisition.Export.Period);
                row1.CreateCell(1).SetCellValue(OfflineFaultAcquisition.Export.Duration);                
                row1.CreateCell(2).SetCellValue(OfflineFaultAcquisition.Export.Date);
                row1.CreateCell(3).SetCellValue(GlobalCurrentInput.SelectedAxisID);

                IRow row2 = In_sheet.CreateRow(3);
                row2.CreateCell(0).SetCellValue("通道1名称");
                row2.CreateCell(1).SetCellValue("通道2名称");
                row2.CreateCell(2).SetCellValue("通道3名称");
                row2.CreateCell(3).SetCellValue("通道4名称");
                row2.CreateCell(4).SetCellValue("通道5名称");
                row2.CreateCell(5).SetCellValue("通道6名称");
                row2.CreateCell(6).SetCellValue("通道7名称");
                row2.CreateCell(7).SetCellValue("通道8名称");
                row2.CreateCell(8).SetCellValue("电流环增益");
                row2.CreateCell(9).SetCellValue("电流环时间积分");
                row2.GetCell(0).CellStyle = headStyle;
                row2.GetCell(1).CellStyle = headStyle;
                row2.GetCell(2).CellStyle = headStyle;
                row2.GetCell(3).CellStyle = headStyle;
                row2.GetCell(4).CellStyle = headStyle;
                row2.GetCell(5).CellStyle = headStyle;
                row2.GetCell(6).CellStyle = headStyle;
                row2.GetCell(7).CellStyle = headStyle;
                row2.GetCell(8).CellStyle = headStyle;
                row2.GetCell(9).CellStyle = headStyle;

                IRow row3 = In_sheet.CreateRow(4);
                row3.CreateCell(0).SetCellValue(OfflineFaultAcquisition.Export.Channel1Name);
                row3.CreateCell(1).SetCellValue(OfflineFaultAcquisition.Export.Channel2Name);
                row3.CreateCell(2).SetCellValue(OfflineFaultAcquisition.Export.Channel3Name);
                row3.CreateCell(3).SetCellValue(OfflineFaultAcquisition.Export.Channel4Name);
                row3.CreateCell(4).SetCellValue(OfflineFaultAcquisition.Export.Channel5Name);
                row3.CreateCell(5).SetCellValue(OfflineFaultAcquisition.Export.Channel6Name);
                row3.CreateCell(6).SetCellValue(OfflineFaultAcquisition.Export.Channel7Name);
                row3.CreateCell(7).SetCellValue(OfflineFaultAcquisition.Export.Channel8Name);
                row3.CreateCell(8).SetCellValue(GlobalCurrentInput.CurrentLoopGain);
                row3.CreateCell(9).SetCellValue(GlobalCurrentInput.CurrentLoopTimeConstant);

                IRow row4 = In_sheet.CreateRow(6);
                row4.CreateCell(4).SetCellValue("位置环增益");
                row4.CreateCell(5).SetCellValue("负载惯量比");
                row4.GetCell(4).CellStyle = headStyle;
                row4.GetCell(5).CellStyle = headStyle;


                IRow row5 = In_sheet.CreateRow(7);

                row5.CreateCell(4).SetCellValue(GlobalCurrentInput.PositionLoopGain);
                row5.CreateCell(5).SetCellValue(GlobalCurrentInput.LoadInertiaRatio);

                IRow row6 = In_sheet.CreateRow(9);
                row6.CreateCell(0).SetCellValue("通道1倍乘");
                row6.CreateCell(1).SetCellValue("通道2倍乘");
                row6.CreateCell(2).SetCellValue("通道3倍乘");
                row6.CreateCell(3).SetCellValue("通道4倍乘");
                row6.CreateCell(4).SetCellValue("通道5倍乘");
                row6.CreateCell(5).SetCellValue("通道6倍乘");
                row6.CreateCell(6).SetCellValue("通道7倍乘");
                row6.CreateCell(7).SetCellValue("通道8倍乘");
                row6.CreateCell(8).SetCellValue("速度环增益");
                row6.CreateCell(9).SetCellValue("速度环时间积分");
                row6.GetCell(0).CellStyle = headStyle;
                row6.GetCell(1).CellStyle = headStyle;
                row6.GetCell(2).CellStyle = headStyle;
                row6.GetCell(3).CellStyle = headStyle;
                row6.GetCell(4).CellStyle = headStyle;
                row6.GetCell(5).CellStyle = headStyle;
                row6.GetCell(6).CellStyle = headStyle;
                row6.GetCell(7).CellStyle = headStyle;
                row6.GetCell(8).CellStyle = headStyle;
                row6.GetCell(9).CellStyle = headStyle;

                IRow row7 = In_sheet.CreateRow(10);
                row7.CreateCell(0).SetCellValue(OfflineFaultAcquisition.Export.Channel1Doubling);
                row7.CreateCell(1).SetCellValue(OfflineFaultAcquisition.Export.Channel2Doubling);
                row7.CreateCell(2).SetCellValue(OfflineFaultAcquisition.Export.Channel3Doubling);
                row7.CreateCell(3).SetCellValue(OfflineFaultAcquisition.Export.Channel4Doubling);
                row7.CreateCell(4).SetCellValue(OfflineFaultAcquisition.Export.Channel5Doubling);
                row7.CreateCell(5).SetCellValue(OfflineFaultAcquisition.Export.Channel6Doubling);
                row7.CreateCell(6).SetCellValue(OfflineFaultAcquisition.Export.Channel7Doubling);
                row7.CreateCell(7).SetCellValue(OfflineFaultAcquisition.Export.Channel8Doubling);
                row7.CreateCell(8).SetCellValue(GlobalCurrentInput.SpeedLoopGain);
                row7.CreateCell(9).SetCellValue(GlobalCurrentInput.SpeedLoopTimeConstant);

                IRow row8 = In_sheet.CreateRow(12);
                row8.CreateCell(0).SetCellValue("通道1单位");
                row8.CreateCell(1).SetCellValue("通道2单位");
                row8.CreateCell(2).SetCellValue("通道3单位");
                row8.CreateCell(3).SetCellValue("通道4单位");
                row8.CreateCell(4).SetCellValue("通道5单位");
                row8.CreateCell(5).SetCellValue("通道6单位");
                row8.CreateCell(6).SetCellValue("通道7单位");
                row8.CreateCell(7).SetCellValue("通道8单位");
                row8.CreateCell(8).SetCellValue("第一转矩滤波时间");
                row8.GetCell(0).CellStyle = headStyle;
                row8.GetCell(1).CellStyle = headStyle;
                row8.GetCell(2).CellStyle = headStyle;
                row8.GetCell(3).CellStyle = headStyle;
                row8.GetCell(4).CellStyle = headStyle;
                row8.GetCell(5).CellStyle = headStyle;
                row8.GetCell(6).CellStyle = headStyle;
                row8.GetCell(7).CellStyle = headStyle;
                row8.GetCell(8).CellStyle = headStyle;

                //单位
                IRow row9 = In_sheet.CreateRow(13);
                for (int i = 0; i < 8; i++)
                {
                    int iCount = OfflineFaultAcquisition.Export.lstUnit.Count;
                    if (i < iCount)
                    {
                        row9.CreateCell(i).SetCellValue(OfflineFaultAcquisition.Export.lstUnit[i]);
                    }
                    else
                    {
                        row9.CreateCell(i).SetCellValue("无");
                    }
                }
                row9.CreateCell(8).SetCellValue(GlobalCurrentInput.FirstTrqcmdFilterTime);

                //单位转换
                IRow row10 = In_sheet.CreateRow(15);
                row10.CreateCell(0).SetCellValue("通道1换算");
                row10.CreateCell(1).SetCellValue("通道2换算");
                row10.CreateCell(2).SetCellValue("通道3换算");
                row10.CreateCell(3).SetCellValue("通道4换算");
                row10.CreateCell(4).SetCellValue("通道5换算");
                row10.CreateCell(5).SetCellValue("通道6换算");
                row10.CreateCell(6).SetCellValue("通道7换算");
                row10.CreateCell(7).SetCellValue("通道8换算");
                row10.GetCell(0).CellStyle = headStyle;
                row10.GetCell(1).CellStyle = headStyle;
                row10.GetCell(2).CellStyle = headStyle;
                row10.GetCell(3).CellStyle = headStyle;                
                row10.GetCell(4).CellStyle = headStyle;
                row10.GetCell(5).CellStyle = headStyle;
                row10.GetCell(6).CellStyle = headStyle;
                row10.GetCell(7).CellStyle = headStyle;

                IRow row11 = In_sheet.CreateRow(16);
                for (int i = 0; i < 8; i++)
                {
                    int iCount = OfflineFaultAcquisition.Export.lstUnit.Count;
                    if (i < iCount)
                    {
                        row11.CreateCell(i).SetCellValue(OfflineFaultAcquisition.Export.lstExchangeValue[i].ToString());
                    }
                    else
                    {
                        row11.CreateCell(i).SetCellValue("无");
                    }
                }

                //采样数值
                IRow row12 = In_sheet.CreateRow(18);
                row12.CreateCell(0).SetCellValue("采样个数");
                row12.CreateCell(1).SetCellValue("采样时间");
                row12.CreateCell(2).SetCellValue("通道1数值");
                row12.CreateCell(3).SetCellValue("通道2数值");
                row12.CreateCell(4).SetCellValue("通道3数值");
                row12.CreateCell(5).SetCellValue("通道4数值");
                row12.CreateCell(6).SetCellValue("通道5数值");
                row12.CreateCell(7).SetCellValue("通道6数值");
                row12.CreateCell(8).SetCellValue("通道7数值");
                row12.CreateCell(9).SetCellValue("通道8数值");
                row12.GetCell(0).CellStyle = headStyle;
                row12.GetCell(1).CellStyle = headStyle;
                row12.GetCell(2).CellStyle = headStyle;
                row12.GetCell(3).CellStyle = headStyle;
                row12.GetCell(4).CellStyle = headStyle;
                row12.GetCell(5).CellStyle = headStyle;
                row12.GetCell(6).CellStyle = headStyle;
                row12.GetCell(7).CellStyle = headStyle;
                row12.GetCell(8).CellStyle = headStyle;
                row12.GetCell(9).CellStyle = headStyle;

                iLength = OfflineFaultAcquisition.Export.lstChannel1.Count;
                for (int i = 19; i < 19 + iLength; i++)
                {
                    IRow row = In_sheet.CreateRow(i);
                    if (OfflineFaultAcquisition.Export.Channel1Name != "停用")
                    {
                        row.CreateCell(0).SetCellValue(Convert.ToString(i - 18));
                        row.CreateCell(1).SetCellValue(Convert.ToString(Convert.ToDouble(OfflineFaultAcquisition.Export.Period.Replace("μs", "").Replace("ms", "")) * (i - 18)));
                        row.CreateCell(2).SetCellValue(OfflineFaultAcquisition.Export.lstChannel1[i - 19]);
                        //row.CreateCell(2).SetCellValue(FaultAcquisitionData.Channel1[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(0).SetCellValue("");
                        row.CreateCell(1).SetCellValue("");
                        row.CreateCell(2).SetCellValue("");
                        //row.CreateCell(3).SetCellValue("");
                        //row.CreateCell(4).SetCellValue("");
                        //row.CreateCell(5).SetCellValue("");
                        //row.CreateCell(6).SetCellValue("");
                        //row.CreateCell(7).SetCellValue("");
                        //row.CreateCell(8).SetCellValue("");

                    }

                    if (OfflineFaultAcquisition.Export.Channel2Name != "停用")
                    {
                        row.CreateCell(3).SetCellValue(OfflineFaultAcquisition.Export.lstChannel2[i - 19]);
                        //row.CreateCell(3).SetCellValue(FaultAcquisitionData.Channel2[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(3).SetCellValue("");
                    }

                    if (OfflineFaultAcquisition.Export.Channel3Name != "停用")
                    {
                        row.CreateCell(4).SetCellValue(OfflineFaultAcquisition.Export.lstChannel3[i - 19]);
                        //row.CreateCell(4).SetCellValue(FaultAcquisitionData.Channel3[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(4).SetCellValue("");
                    }

                    if (OfflineFaultAcquisition.Export.Channel4Name != "停用")
                    {
                        row.CreateCell(5).SetCellValue(OfflineFaultAcquisition.Export.lstChannel4[i - 19]);
                        //row.CreateCell(5).SetCellValue(FaultAcquisitionData.Channel4[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(5).SetCellValue("");
                    }

                    if (OfflineFaultAcquisition.Export.Channel5Name != "停用")
                    {
                        row.CreateCell(6).SetCellValue(OfflineFaultAcquisition.Export.lstChannel5[i - 19]);
                        //row.CreateCell(6).SetCellValue(FaultAcquisitionData.Channel5[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(6).SetCellValue("");
                    }

                    if (OfflineFaultAcquisition.Export.Channel6Name != "停用")
                    {
                        row.CreateCell(7).SetCellValue(OfflineFaultAcquisition.Export.lstChannel6[i - 19]);
                        //row.CreateCell(7).SetCellValue(FaultAcquisitionData.Channel6[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(7).SetCellValue("");
                    }

                    if (OfflineFaultAcquisition.Export.Channel7Name != "停用")
                    {
                        row.CreateCell(8).SetCellValue(OfflineFaultAcquisition.Export.lstChannel7[i - 19]);
                        //row.CreateCell(8).SetCellValue(FaultAcquisitionData.Channel7[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(8).SetCellValue("");
                    }

                    if (OfflineFaultAcquisition.Export.Channel8Name != "停用")
                    {
                        row.CreateCell(9).SetCellValue(OfflineFaultAcquisition.Export.lstChannel8[i - 19]);
                        //row.CreateCell(9).SetCellValue(FaultAcquisitionData.Channel8[i - 19]);
                    }
                    else
                    {
                        row.CreateCell(9).SetCellValue("");
                    }
                }

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.EXCELHELPER_SET_EXCEL_INFO_FOR_OSCILLOSCOPE, "SetExcelInfo_For_Oscilloscope", ex);
                return RET.ERROR;
                //return RET.SUCCEEDED;
            }
        }

        //*************************************************************************
        //函数名称：SetExcelColumnStyle
        //函数功能：设置Excel列Style
        //
        //输入参数：ICellStyle cellStyle
        //         IFont font       
        //
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.10.25
        //************************************************************************* 
        private static ICellStyle SetExcelColumnStyle(IWorkbook In_workBook)
        {
            try
            {
                IFont font = In_workBook.CreateFont();
                ICellStyle cellStyle = In_workBook.CreateCellStyle();

                font.FontName = "Mircosoft Yahei";
                font.FontHeightInPoints = 9;

                cellStyle.SetFont(font);
                cellStyle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellStyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Center;//水平居中
                cellStyle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellStyle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellStyle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellStyle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;

                cellStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.Coral.Index;
                cellStyle.FillBackgroundColor = NPOI.HSSF.Util.HSSFColor.Coral.Index;
                cellStyle.FillPattern = FillPattern.FineDots;
                cellStyle.WrapText = true;

                return cellStyle;
            }
            catch
            {              
                return null;
            }
        }

        //*************************************************************************
        //函数名称：SetExcelContentStyle
        //函数功能：设置Excel内容Style
        //
        //输入参数：ICellStyle cellStyle
        //         IFont font       
        //
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2019.10.25
        //************************************************************************* 
        private static ICellStyle SetExcelContentStyle(IWorkbook In_workBook, int In_iIndex)
        {
            try
            {
                IFont font = In_workBook.CreateFont();
                ICellStyle cellStyle = In_workBook.CreateCellStyle();

                font.FontName = "Mircosoft Yahei";
                font.FontHeightInPoints = 8;

                cellStyle.SetFont(font);
                cellStyle.VerticalAlignment = NPOI.SS.UserModel.VerticalAlignment.Center;
                cellStyle.Alignment = NPOI.SS.UserModel.HorizontalAlignment.Left;
                cellStyle.BorderBottom = NPOI.SS.UserModel.BorderStyle.Thin;
                cellStyle.BorderLeft = NPOI.SS.UserModel.BorderStyle.Thin;
                cellStyle.BorderRight = NPOI.SS.UserModel.BorderStyle.Thin;
                cellStyle.BorderTop = NPOI.SS.UserModel.BorderStyle.Thin;

                //if (In_iIndex % 3 == 0)
                //{
                //    cellStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.Grey25Percent.Index;
                //    cellStyle.FillBackgroundColor = NPOI.HSSF.Util.HSSFColor.Grey25Percent.Index;
                //}
                //else if (In_iIndex % 3 == 1)
                //{
                //    cellStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.White.Index;
                //    cellStyle.FillBackgroundColor = NPOI.HSSF.Util.HSSFColor.White.Index;
                //}
                //else
                //{
                //    cellStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.Aqua.Index;
                //    cellStyle.FillBackgroundColor = NPOI.HSSF.Util.HSSFColor.Aqua.Index;
                //}

                cellStyle.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.White.Index;
                cellStyle.FillBackgroundColor = NPOI.HSSF.Util.HSSFColor.White.Index;
                cellStyle.FillPattern = FillPattern.FineDots;
                cellStyle.WrapText = true;

                return cellStyle;
            }
            catch
            {
                return null;
            }
        }

        //*************************************************************************
        //函数名称：FileAttributeSet
        //函数功能：文件属性
        //
        //输入参数：string In_strFilePath    地址
        //         int iIndex               文件检索号       
        //
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2020.04.07
        //************************************************************************* 
        private static void FileAttributeSet(string In_strFilePath, int iIndex)
        {
            if (iIndex == ExcelType.SoftwareErrorLog || iIndex == ExcelType.Monitor)
            {
                FileInfo info = new FileInfo(In_strFilePath);
                if (info.Exists)
                {
                    info.Attributes = FileAttributes.Hidden;
                }
            }
        }
        #endregion
    }
}
