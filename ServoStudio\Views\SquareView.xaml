﻿<UserControl x:Class="ServoStudio.Views.SquareView"
              xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:Views="clr-namespace:ServoStudio.Views"
             xmlns:converter="clr-namespace:Converter"
           mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="30"/>
            <ColumnDefinition Width ="Auto"/>
            <ColumnDefinition Width="30"/>
            <ColumnDefinition Width ="Auto"/>
        </Grid.ColumnDefinitions>

        <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="3"  Grid.Column="0" Height="150" Width="Auto" HorizontalAlignment="Left" ShowMenu="False" Source="pack://application:,,,/ServoStudio;component/Resource/Square.png" ShowBorder="False" Opacity="0.65"/>

        <Label Grid.Row="0" Grid.Column="1"  Background="Green" Width="10" Height="10" Margin="5"/>
        <Label Grid.Row="0" Grid.Column="3"  Background="Green" Width="10" Height="10" Margin="5"/>
     
        <Label Grid.Row="1" Grid.Column="1"  Background="Blue" Width="10" Height="10" Margin="5"/>
     
        <Label Grid.Row="0" Grid.Column="2"  Margin="5" Content="Amp：指令幅值" FontSize="10pt"/>
        <Label Grid.Row="0" Grid.Column="4"  Margin="5" Content="Frq：信号频率" FontSize="10pt"/>

        <Label Grid.Row="1" Grid.Column="2"  Margin="5" Content="t/T：产生个数" FontSize="10pt"/>    
    </Grid>
</UserControl>
