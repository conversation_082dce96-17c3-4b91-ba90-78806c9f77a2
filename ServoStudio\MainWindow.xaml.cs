﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using DevExpress.Xpf.Ribbon;
using DevExpress.Xpf.Core;
using ServoStudio.ViewModels;
using System.IO;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using DevExpress.Mvvm;
using ServoStudio.Models;

namespace ServoStudio
{  
    public partial class MainWindow : DXRibbonWindow
    {
        private static Point m_Point;
        private static double m_PositionDiff;
        public event ClosingHint evtClosingHint;

        public MainWindow()
        {
            InitializeComponent();
            DXGridDataController.DisableThreadingProblemsDetection = true;
        }

        //*************************************************************************
        //函数名称：MainWindow_Loaded
        //函数功能：Loaded
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.21&2022.08.31
        //*************************************************************************
        private void DXRibbonWindow_Loaded(object sender, RoutedEventArgs e)
        {
            int iRet = -1;

            //由Lilbert改为初始化时只加载窗体，不读取相应参数
            try
            {
                //获取初始位置坐标
                m_Point = this.ServoStudioWindows.GetPosition();
                
                //赋值全局变量
                WindowSet.clsMainWindow = this;

                //Splash
                DXSplashScreen.Progress(100);
                for (int i = 0; i < 100; i++)
                {
                    DXSplashScreen.Progress(i);
                    DXSplashScreen.SetState(string.Format("{0} %", (i + 1)));
                    System.Threading.Thread.Sleep(10);
                }
                DXSplashScreen.Close();

                ////获取Ini参数配置文件地址
                //iRet = OthersHelper.GetInitialConfigInfo();
                //if (iRet != RET.SUCCEEDED)
                //{
                //    MessageBox.Show("系统初始化异常，部分功能失效!" + "\r\n" + "\r\n" + "请确认配置文件是否存在，并处于关闭状态...", "警告:", MessageBoxButton.OK);
                //}

                ////获取默认参数单位
                //OthersHelper.GetDefaultUnit();
                //OthersHelper.GetSelectDefaultUnit();

                ////获取默认示波器单位
                //OthersHelper.GetOscilloscopeParameterUnitSet();

                ////查询Excel数据，并判断是否导入正确的配置文件
                //iRet = ExcelHelper.ReadFromExcel(FilePath.Parameter, ref GlobalParameterSet.dt);
                //if (iRet == RET.ERROR)
                //{
                //    GlobalParameterSet.dt = null;
                //    GlobalParameterSet.dt_Export = null;

                //    MessageBox.Show("系统初始化异常，部分功能失效!" + "\r\n" + "\r\n" + "请确认配置文件是否存在，并处于关闭状态...", "警告:", MessageBoxButton.OK);
                //}
                //else
                //{
                //    iRet = OthersHelper.CheckTitleOfConfig();
                //    if (iRet != RET.SUCCEEDED)
                //    {
                //        GlobalParameterSet.dt = null;
                //        GlobalParameterSet.dt_Export = null;

                //        MessageBox.Show("系统初始化异常，部分功能失效!" + "\r\n" + "\r\n" + "请导入正确格式与内容的配置文件...", "警告:", MessageBoxButton.OK);
                //    }
                //}

                ////复制文件用于导出
                //iRet = OthersHelper.DataTableCopy(GlobalParameterSet.dt, ref GlobalParameterSet.dt_Export);
                //if (iRet != RET.SUCCEEDED)
                //{
                //    GlobalParameterSet.dt = null;
                //    GlobalParameterSet.dt_Export = null;
                //}

                ////更新字典信息
                //iRet = OthersHelper.RefreshDictionary(GlobalParameterSet.dt, ref CommunicationSet.CurrentPointValue_AxisA, ref CommunicationSet.CurrentPointValue_AxisB);
                //if (iRet != RET.SUCCEEDED)
                //{
                //    GlobalParameterSet.dt = null;
                //    GlobalParameterSet.dt_Export = null;
                //}

                ////导入配置参数监控文件
                //iRet = ExcelHelper.ReadFromExcel(FilePath.Monitor, ref GlobalParameterSet.dt_Monitor);
                //if (iRet != RET.SUCCEEDED)
                //{
                //    MessageBox.Show("系统初始化异常，部分功能失效!" + "\r\n" + "\r\n" + "请确认Monitor文件是否存在，并处于关闭状态...", "警告:", MessageBoxButton.OK);
                //}

                ////导入伺服报警解释文件
                //iRet = ExcelHelper.ReadFromExcel(FilePath.HardwareExplanation, ref GlobalParameterSet.dt_HardwareExplanation);
                //if (iRet == RET.SUCCEEDED)
                //{
                //    OthersHelper.RefreshDictionary(GlobalParameterSet.dt_HardwareExplanation, ref GlobalParameterSet.lstHardwareAlarm);
                //}
                //else
                //{
                //    MessageBox.Show("系统初始化异常，部分功能失效!" + "\r\n" + "\r\n" + "请确认HardwareExplanation文件是否存在，并处于关闭状态...", "警告:", MessageBoxButton.OK);
                //}
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_LOADED, "MainWindow_Loaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：DXRibbonWindow_Closing
        //函数功能：关闭系统确认
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.21
        //*************************************************************************
        private void DXRibbonWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            int iRet = -1;

            try
            {
                if (evtClosingHint != null)
                {
                    iRet = evtClosingHint();
                }

                if (iRet != RET.SUCCEEDED)
                {
                    e.Cancel = true;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.MAIN_CLOSING, "DXRibbonWindow_Closing", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetWindowStartupLocation
        //函数功能：返回到初始位置
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.24
        //*************************************************************************
        public bool GetWindowStartupLocation()
        {
            if (m_Point == null)
            {
                return false;
            }

            if (this.ServoStudioWindows.WindowState != WindowState.Maximized)
            {
                this.ServoStudioWindows.Left = m_Point.X;
                this.ServoStudioWindows.Top = m_Point.Y;
            }

            return true;
        }

        //*************************************************************************
        //函数名称：ShowNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.20
        //*************************************************************************
        public void ShowNotification(int iCode)
        {
            INotification notification = null;

            try
            {
                this.ServiceWithDefaultNotifications.Dispatcher.BeginInvoke(new Action(() =>
                {
                    switch (iCode)
                    {
                        case RET.ERROR:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "系统异常，请关闭系统重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 1001:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "伺服状态异常，请稍后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 1002:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "回送测试无响应，请稍后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 1003:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "串口通信异常，请确认伺服状态，并选择正确的串口重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 1004:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "串口通信连接成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 1005:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "回传测试成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 2002:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "保存成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 2003:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "保存失败，请确认文件是否被占用...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 2004:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "伺服使能...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 2005:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "伺服禁能...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 2006:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "电机参数辨识成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 2007:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "参数替换在退出当前界面后生效...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;    
                        case 3001:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "回送测试失败...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3002:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "数据采集下达失败...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3003:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "数据采集问询失败...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3004:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "数据采集接收失败...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;                     
                        case 3006:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "报警获取失败...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3007:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "固件升级成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3008:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "固件升级失败，请稍后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3009:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "系统异常，参数单位将设置为系统默认...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3010:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "系统异常，编码器类型不存在，参数单位将设置为系统默认...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3011:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "软硬件版本不一致，可能导致系统异常...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3012:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "单位设置成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3013:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "伺服处于使能状态，不能固件升级...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3014:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "伺服状态错误，指令无法执行，请退出后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3015:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "版本信息获取失败，请稍后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3016:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "当前波形没有绘制完成，请稍后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3017:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "串口通信异常，已断开连接，请稍后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3018:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "伺服系统当前无报警信息...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3019:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "伺服系统最多能记录十条报警信息...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3020:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "电机绝对值编码器初始位置偏置辨识成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3021:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "电机动力线相序辨识成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3022:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "电机参数自整定成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3023:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "电机负载惯量辨识成功...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3024:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "电机参数辨识失败...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3025:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "电机动力线相序辨识失败...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3026:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "电机绝对值编码器初始位置偏置辨识失败...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3027:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "电机负载惯量辨识失败...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3028:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "电机参数自整定失败...", DateTime.Now.ToString(), NotificationPicture.OK_DIAMOND);
                            break;
                        case 3029:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "故障数据采集下达失败...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3030:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "故障数据采集问询失败...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        case 3031:
                            notification = this.ServiceWithDefaultNotifications.CreatePredefinedNotification("信息提示：", "伺服状态错误，指令无法执行，请退出后重试...", DateTime.Now.ToString(), NotificationPicture.NG_BULB);
                            break;
                        default:
                            return;
                    }

                    notification.ShowAsync();
                }));
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("MainWindow.ShowNotification", ex);
            }           
        }

        //*************************************************************************
        //函数名称：ShowEdition
        //函数功能：版本提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.06.01&2022.08.05
        //*************************************************************************
        public void ShowEdition()
        {
            string strServoStudioVersion = null;
            string strServoStudioVersion_Computer = null;
            string strHardwareVersion = null;
            string strSoftwareVersion = null;
            string strInfo = null;

            try
            {
                if (SoftwareInfo.VERSION.Length < 8 || SoftwareStateParameterSet.ServoSoftwareVersion.Length < 8)
                {
                    ShowNotification(3015);
                    return;
                }

                strHardwareVersion = SoftwareStateParameterSet.ServoHardwareVersion.Substring(0, 8);
                strServoStudioVersion_Computer = SoftwareInfo.VERSION.Substring(0, 8);
                strServoStudioVersion = SoftwareInfo.VERSION.Substring(0, 8);
                //strHardwareVersion = SoftwareStateParameterSet.ServoSoftwareVersion.Substring(0, 8);
                //strSoftwareVersion = SoftwareStateParameterSet.ServoSoftwareVersion.Substring(0, 8);

                //strInfo = "系统发布 - 季华实验室机器人中心" + "\r\n";
                strInfo = "系统发布 - " + GlobalCompanyInfo.CompanyDepartment1 + "\r\n";
                strInfo += "伺服名称 - " + SoftwareStateParameterSet.ServoName + "\r\n";
                strInfo += "伺服软件版本 - " + SoftwareStateParameterSet.ServoSoftwareVersion + "\r\n";
                strInfo += "伺服硬件版本 - " + SoftwareStateParameterSet.ServoHardwareVersion + "\r\n";   //由Lilbert增加硬件版本号
                strInfo += "ServoStudio版本 - " + SoftwareInfo.VERSION;

                this.WinUIMessageBox.Dispatcher.BeginInvoke(new Action(() =>
                {
                    GetWindowStartupLocation();

                    this.WinUIMessageBox.ShowMessage(strInfo, "版本信息", MessageButton.OK, MessageIcon.Information);
                }));

                if (strServoStudioVersion != strSoftwareVersion)
                {
                    //ShowNotification(3011);
                }                       
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("MainWindow.ShowEdition", ex);
            }
        }

        //*************************************************************************
        //函数名称：ShowEdition_ForExportParas
        //函数功能：获取版本信息，但不弹窗
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.06.02
        //*************************************************************************
        public void ShowEdition_ForExportParas()
        {
            string strServoStudioVersion = null;
            string strServoStudioVersion_Computer = null;
            string strHardwareVersion = null;
            string strSoftwareVersion = null;
            string strInfo = null;

            try
            {
                if (SoftwareInfo.VERSION.Length < 8 || SoftwareStateParameterSet.ServoSoftwareVersion.Length < 8)
                {
                    ShowNotification(3015);
                    return;
                }

                strHardwareVersion = SoftwareStateParameterSet.ServoHardwareVersion.Substring(0, 8);
                strServoStudioVersion_Computer = SoftwareInfo.VERSION.Substring(0, 8);
                strServoStudioVersion = SoftwareInfo.VERSION.Substring(0, 8);
                //strHardwareVersion = SoftwareStateParameterSet.ServoSoftwareVersion.Substring(0, 8);
                //strSoftwareVersion = SoftwareStateParameterSet.ServoSoftwareVersion.Substring(0, 8);

                //strInfo = "系统发布 - 季华实验室机器人中心" + "\r\n";
                strInfo = "系统发布 - " + GlobalCompanyInfo.CompanyDepartment1 + "\r\n";
                strInfo += "伺服名称 - " + SoftwareStateParameterSet.ServoName + "\r\n";
                strInfo += "伺服软件版本 - " + SoftwareStateParameterSet.ServoSoftwareVersion + "\r\n";
                strInfo += "伺服硬件版本 - " + SoftwareStateParameterSet.ServoHardwareVersion + "\r\n";   //由Lilbert增加硬件版本号
                strInfo += "ServoStudio版本 - " + SoftwareInfo.VERSION;

                //this.WinUIMessageBox.Dispatcher.BeginInvoke(new Action(() =>
                //{
                //    GetWindowStartupLocation();

                //    this.WinUIMessageBox.ShowMessage(strInfo, "版本信息", MessageButton.OK, MessageIcon.Information);
                //}));

                if (strServoStudioVersion != strSoftwareVersion)
                {
                    //ShowNotification(3011);
                }
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("MainWindow.ShowEdition", ex);
            }
        }

        //*************************************************************************
        //函数名称：FirmUpdateAgain
        //函数功能：固件升级提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.10.13
        //*************************************************************************
        public void FirmUpdateAgain()
        {           
            try
            {               
                this.WinUIMessageBox.Dispatcher.BeginInvoke(new Action(() =>
                {
                    GetWindowStartupLocation();

                    if (this.WinUIMessageBox.ShowMessage("固件升级失败，是否重新固件升级...", "信息提示", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                    {
                        ViewModelSet.FirmwareUpdate?.FirmwareUpdateStart();
                    }
                }));
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("MainWindow.FirmUpdateAgain", ex);
            }
        }

        //*************************************************************************
        //函数名称：ControlSlideAlarmState
        //函数功能：侧边报警状态控制
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2021.01.19
        //*************************************************************************
        public void ControlSlideAlarmState()
        {
            for (int i = 0; i < 5; i++)
            {
                var slideAlarmGroupBox = this.grid_ErrorAlarm.FindName("SlideAlarm" + i.ToString()) as DevExpress.Xpf.LayoutControl.GroupBox;
                if (slideAlarmGroupBox != null)
                {
                    if (HardwareAlarmInfoSet.SlideAlarmState[i] == "Maximized")
                    {
                        slideAlarmGroupBox.State = DevExpress.Xpf.LayoutControl.GroupBoxState.Normal;
                    }
                    else
                    {
                        slideAlarmGroupBox.State = DevExpress.Xpf.LayoutControl.GroupBoxState.Minimized;
                    }
                }              
            }                 
        }

        //*************************************************************************
        //函数名称：ServoStudioWindows_SizeChanged
        //函数功能：界面缩小到一半后，停靠一边，侧边栏关闭
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.20
        //*************************************************************************
        private void ServoStudioWindows_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            double dScreenWidth = 0;
            double dScreenHeight = 0;

            try
            {
                dScreenWidth = SystemParameters.PrimaryScreenWidth;
                dScreenHeight = SystemParameters.PrimaryScreenHeight;

                if (e.NewSize.Width == dScreenWidth/2)
                {
                    ViewModelSet.Main?.SlidePanelDisplayControl(true, true);
                }               
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("ServoStudioWindows_SizeChanged", ex);
            }
        }

        //*************************************************************************
        //函数名称：ServoStudioWindows_LocationChanged
        //函数功能：界面拖放到右侧，侧边栏关闭
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.12.20
        //*************************************************************************
        private void ServoStudioWindows_LocationChanged(object sender, EventArgs e)
        {
            double dScreenWidth = 0;
            double dSoftwareWidth = 0;
            double dPositionDiff = 0;
            MainWindow clsMainWindow = null;

            try
            {
                clsMainWindow = (MainWindow)sender;
                dSoftwareWidth = clsMainWindow.WindowRect.TopRight.X;
                dScreenWidth = SystemParameters.PrimaryScreenWidth;
                dPositionDiff = dSoftwareWidth - dScreenWidth;

                if (dPositionDiff > m_PositionDiff && dPositionDiff > 0)
                {
                    ViewModelSet.Main?.SlidePanelDisplayControl(true, true);
                    m_PositionDiff = dPositionDiff;                   
                }

                m_PositionDiff = dPositionDiff;
            }
            catch (System.Exception ex)
            {
                LogHelper.ErrorLog("ServoStudioWindows_LocationChanged", ex);
            }
        }
    }
}
