﻿<UserControl
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:Views="clr-namespace:ServoStudio.Views"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars" 
             xmlns:dxwui="http://schemas.devexpress.com/winfx/2008/xaml/windowsui"
             xmlns:dxwuin="http://schemas.devexpress.com/winfx/2008/xaml/windowsui/navigation"
             xmlns:converter="clr-namespace:Converter"
             x:Class="ServoStudio.Views.CommunicationSetView"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type={x:Type ViewModels:CommunicationSetViewModel}}"
             d:DesignHeight="600" d:DesignWidth="900">

    <UserControl.Resources>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>
    </UserControl.Resources>

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding CommunicationSetLoadedCommand}"/>

        <dxwui:WinUIDialogService x:Name="AddSlaveAxisID" DialogWindowStartupLocation="CenterOwner">
            <dxwui:WinUIDialogService.ViewTemplate>
                <DataTemplate>
                    <Views:AddSlaveAxisIDView/>
                </DataTemplate>
            </dxwui:WinUIDialogService.ViewTemplate>
        </dxwui:WinUIDialogService>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
        
        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="30"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Label Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="7" Margin="3,10" Style="{StaticResource LabelStyle}" Content="通信设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="串口号" Style="{StaticResource LabelStyle}" />
            <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding SerialPortNum}" SelectedItem="{Binding SelectedSerialPortNum, Mode=TwoWay}"/>

            <Label Grid.Row="1" Grid.Column="4" Margin="10,9" Content="波特率" Style="{StaticResource LabelStyle}"/>
            <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="5" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding BaudRate}"  SelectedItem="{Binding SelectedBaudRate, Mode=TwoWay}"/>

            <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="数据位" Style="{StaticResource LabelStyle}"/>
            <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding DataBit}" SelectedItem="{Binding SelectedDataBit, Mode=TwoWay}"/>

            <Label Grid.Row="2" Grid.Column="4" Margin="10,9" Content="停止位" Style="{StaticResource LabelStyle}"/>
            <dxe:ComboBoxEdit Grid.Row="2" Grid.Column="5" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding EndBit}" SelectedItem="{Binding SelectedEndBit, Mode=TwoWay}"/>

            <Label Grid.Row="3" Grid.Column="1" Margin="10,9" Content="校验位" Style="{StaticResource LabelStyle}"/>
            <dxe:ComboBoxEdit Grid.Row="3" Grid.Column="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding CheckBit}" SelectedItem="{Binding SelectedCheckBit, Mode=TwoWay}"/>

            <Label Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="7" Margin="3,10" Style="{StaticResource LabelStyle}" Content="伺服驱动器选择" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Label Grid.Row="5" Grid.Column="1" Margin="10,9" Content="驱动器" Style="{StaticResource LabelStyle}"/>
            <dxe:ComboBoxEdit Grid.Row="5" Grid.Column="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding ServoName}" SelectedItem="{Binding SelectedServoName, Mode=TwoWay}"/>

            <Label Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="7" Margin="3,10" Style="{StaticResource LabelStyle}" Content="地址设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <!--<Label Grid.Row="7" Grid.Column="1" Margin="10,9" Content="从站地址" Style="{StaticResource LabelStyle}"/>
            <dxe:ComboBoxEdit Grid.Row="7" Grid.Column="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding SlaveID}" SelectedItem="{Binding SelectedSlaveID, Mode=TwoWay}"/>-->

            <Label Grid.Row="7" Grid.Column="1" Margin="10,9" Content="轴地址" Style="{StaticResource LabelStyle}"/>
            <dxe:ComboBoxEdit Grid.Row="7" Grid.Column="2" Margin="10,9" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding AxisID}" SelectedItem="{Binding SelectedAxisID, Mode=TwoWay}"/>

            <dx:SimpleButton Grid.Row="7" Grid.Column="3" Margin="10,9" Grid.ColumnSpan="2" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Reset_16x16.png}" Command="{Binding AxisAddressResetCommand}" Visibility="{Binding IsAxisAddressResetEnabled,Converter={StaticResource VisibilityConverter}}" >
                <Label Content="轴地址重置" Style="{StaticResource LabelStyle}" Margin="0"/>
            </dx:SimpleButton>


            <!--<dxg:GridControl  Grid.Row="7" Margin="0,0,400,5" Grid.Column="1" Grid.ColumnSpan="6" SelectionMode="Row" ItemsSource="{Binding StaionAxisIDLibrary}" AutoGenerateColumns="AddNew" >
            <dxg:GridControl.View>
                <dxg:TableView AllowEditing="False" AutoWidth="False" ShowGroupPanel="False" RowMinHeight="25" HeaderPanelMinHeight="25" ShowSearchPanelMode="Always" SearchPanelHorizontalAlignment="Stretch">
                </dxg:TableView>
            </dxg:GridControl.View>

            <dxg:GridColumn FieldName="IsChoiced" Header="选中" IsSmart="True" ReadOnly="False">
                <dxg:GridColumn.CellTemplate>
                    <DataTemplate>
                        <dxe:CheckEdit VerticalAlignment="Center" HorizontalAlignment="Center" IsChecked="{Binding RowData.Row.IsChoiced, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                            <dxmvvm:Interaction.Behaviors>
                                <dxmvvm:EventToCommand EventName="Checked" Command="{Binding Path=DataContext.CheckIsMultipleChoiceCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}"/>
                                <dxmvvm:EventToCommand EventName="Unchecked" Command="{Binding Path=DataContext.CheckIsMultipleChoiceCommand, RelativeSource={RelativeSource AncestorType={x:Type dxg:GridControl}}}"/>
                            </dxmvvm:Interaction.Behaviors>
                        </dxe:CheckEdit>
                    </DataTemplate>
                </dxg:GridColumn.CellTemplate>
            </dxg:GridColumn>

            <dxg:GridColumn FieldName="StationID" Header="从站ID" IsSmart="True" Width="*"/>
            <dxg:GridColumn FieldName="AxisID" Header="轴地址" IsSmart="True" Width="*"/>
            
        </dxg:GridControl>-->


            <StackPanel Grid.Row="8" Grid.Column="0" Grid.ColumnSpan="7" HorizontalAlignment="Right" Orientation="Horizontal">
                <!--<dx:SimpleButton Margin="0,0,0,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImage Image=AddItem_16x16.png}" IsEnabled="{Binding IsSelectedServoNameEnabled}" CommandParameter="AddSlaveAxisID" Command="{Binding AddSlaveAxisIDCommand}" >
                <Label Content="添加" Style="{StaticResource LabelStyle}" Margin="0"/>
            </dx:SimpleButton>-->

            </StackPanel>

            <Label Grid.Row="9" Grid.Column="0" Grid.ColumnSpan="7" Margin="3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <StackPanel Grid.Row="10" Grid.Column="0" Grid.ColumnSpan="7" HorizontalAlignment="Right" Orientation="Horizontal">
                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Convert_16x16.png}" Command="{Binding RefreshSerialPortNumCommand}">
                    <Label Content="刷新端口" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Merge_16x16.png}" Command="{Binding GetSerialPortConnectionCommand}">
                    <Label Content="通信连接" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/Close_16x16.png" Command="{Binding CloseSerialPortConnectionCommand}">
                    <Label Content="通信断开" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Send_16x16.png}" Command="{Binding EchoTestCommand}">
                    <Label Content="回传测试" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>
            </StackPanel>

            <Grid Grid.Row="12" Grid.Column="0" Grid.ColumnSpan="7">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="Auto"/>

                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="2" Content="2.电机参数" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="4" Content="3.电机反馈" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="6" Content="4.参数学习" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="8" Content="5.参数辨识" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="9" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="10" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="10" Content="6.JOG调试" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="11" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

            <dxe:CheckEdit Grid.Row="0" Grid.Column="12" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="12" Content="7.辨识整定" Foreground="Gray" Style="{StaticResource LabelStyle}"/>-->

                <dxe:CheckEdit Grid.Row="0" Grid.Column="0" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="{x:Null}" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="0" Content="1.通信配置" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="1" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Black"/>

                <!--<dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
            <Label Grid.Row="1" Grid.Column="2" Content="2.电机参数" Style="{StaticResource LabelStyle}"/>
            <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>-->

                <dxe:CheckEdit Grid.Row="0" Grid.Column="2" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="2" Content="2.电机反馈" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="3" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="4" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="4" Content="3.参数学习" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="5" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="6" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="6" Content="4.参数辨识" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="7" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="8" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="8" Content="5.JOG调试" Foreground="Gray" Style="{StaticResource LabelStyle}"/>
                <Label Grid.Row="1" Grid.Column="9" Margin="2,15,2,0" BorderThickness="0,1,0,0" BorderBrush="Gray"/>

                <dxe:CheckEdit Grid.Row="0" Grid.Column="10" Margin="8,0,0,-8"  HorizontalAlignment="Center" IsThreeState="True" IsChecked="False" IsReadOnly="True"/>
                <Label Grid.Row="1" Grid.Column="10" Content="6.辨识整定" Foreground="Gray" Style="{StaticResource LabelStyle}"/>



                <!--<dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="14" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Forward_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding MotorLibraryNavigationCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Column="15" FontSize="12" Content="下页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>-->

                <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="14" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Forward_32x32.png}" ShowBorder="False">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding MotorFeedbackNavigationCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                </dxe:ImageEdit>
                <Label Grid.Column="15" FontSize="12" Content="下页" Style="{StaticResource LabelStyle}" Margin="3,9,3,11" Grid.RowSpan="2"/>

            </Grid>
        </Grid>
        
    </ScrollViewer>
</UserControl>
