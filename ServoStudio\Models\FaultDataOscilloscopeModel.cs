﻿using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using ServoStudio.ViewModels;
using ServoStudio.Views;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ServoStudio.Models
{
    public static class FaultDataOscilloscopeModel
    {
        //*************************************************************************
        //函数名称：DynamicXStartPoint
        //函数功能：波形流动的X起点坐标
        //函数用途：示波器展示的波形超过界面范围以后，开始流动展示
        //
        //输入参数：List<ChannelInfo> In_lstChannelInfo  通道信息   
        //         ref double Out_dXPoint               动态X起点坐标
        //       
        //输出参数：NONE
        //        
        //编码作者：lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public static void DynamicXStartPoint(List<FaultDataChannelInfo> In_lstChannelInfo, ref double Out_dXPoint)
        {
            try
            {
                if (In_lstChannelInfo == null)
                {
                    Out_dXPoint = 0;
                }
                else if (In_lstChannelInfo.Count == 0)
                {
                    Out_dXPoint = 0;
                }
                else
                {
                    double itemp = In_lstChannelInfo[1].XValue - FaultDataOscilloscope.DynamicDisplayPoint * OthersHelper.GetAcquisitionPeriod_ForFaultData(ViewModelSet.FaultDataOscilloscope.SamplingPeriod);
                    if (itemp > 0)
                    {
                        Out_dXPoint = itemp;
                    }
                    else
                    {
                        Out_dXPoint = 0;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_DYNAMIC_X_START_POINT, "DynamicXStartPoint", ex);
            }
        }

        //*************************************************************************
        //函数名称：DynamicYMaxPoint
        //函数功能：y轴最大数据坐标
        //函数用途：示波器展示的波形超过界面范围以后，开始流动展示
        //
        //输入参数：List<ChannelInfo> In_lstChannelInfo  通道信息   
        //         ref double Out_dYPoint               动态y轴最大数据坐标
        //       
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public static void DynamicYMaxPoint(List<FaultDataChannelInfo> In_lstChannelInfo, ref double Out_dMax, ref double Out_dMin)
        {
            int iTimes = 1;
            List<double> lstMax = new List<double>();
            List<double> lstMin = new List<double>();
    
            try
            {
                if (In_lstChannelInfo == null)
                {
                    Out_dMax = 0;
                    Out_dMin = 0;
                }
                else if (In_lstChannelInfo.Count == 0)
                {
                    Out_dMax = 0;
                    Out_dMin = 0;
                }
                else
                {
                    for (int i = 1; i < In_lstChannelInfo.Count; i++)
                    {
                        if (In_lstChannelInfo[i].IsUsed == false)
                        {
                            continue;
                        }

                        //超出部分出队
                        if (In_lstChannelInfo[i].YValueQueue.Count >= FaultDataOscilloscope.QueueLength)
                        {
                            In_lstChannelInfo[i].YValueQueue.Dequeue();
                        }

                        //入队
                        In_lstChannelInfo[i].YValueQueue.Enqueue(Math.Abs(In_lstChannelInfo[i].YValue));

                        //获取最大值、最小值集合                    
                        lstMax.Add((double)In_lstChannelInfo[i].YValueQueue.ToArray().Max());
                        lstMin.Add((double)In_lstChannelInfo[i].YValueQueue.ToArray().Min());
                    }

                    //从最值集合中获取最大、最小值
                    Out_dMax = lstMax.Max();
                    Out_dMin = lstMin.Min();

                    //获取旷量倍数
                    iTimes = Convert.ToString((Int64)(Out_dMax - Out_dMin)).Length;

                    Out_dMax += Math.Pow(FaultDataOscilloscope.Vacant, iTimes);
                    Out_dMin -= Math.Pow(FaultDataOscilloscope.Vacant, iTimes);       
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_DYNAMIC_Y_MAX_POINT, "DynamicYMaxPoint", ex);
            }
        }

        //*************************************************************************
        //函数名称：CheckSamplingParameterCorrected
        //函数功能：判断采样参数设置是否正确
        //
        //输入参数：string In_strPeriod      采样周期   
        //         string In_strDuration    采样时长
        //       
        //输出参数：0：NO_EFFECT
        //         1: SUCCEED
        //        -1: WRONG
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public static int CheckSamplingParameterCorrected(string In_strPeriod, string In_strDuration, string In_strContinuousSampling)
        {
            int iRet = -1;
            int iIndex = -1;
            int iPeriod = -1;
            int iDuration = -1;

            try
            {
                if (string.IsNullOrEmpty(In_strPeriod) || string.IsNullOrEmpty(In_strDuration) || string.IsNullOrEmpty(In_strContinuousSampling))
                    return RET.SUCCEEDED;
                else if (In_strContinuousSampling == "是")
                    return RET.SUCCEEDED;

                iIndex = In_strPeriod.IndexOf("m");     
                iPeriod = Convert.ToInt32(In_strPeriod.Remove(iIndex, 2));

                iIndex = In_strDuration.IndexOf("m");
                iDuration = Convert.ToInt32(In_strDuration.Remove(iIndex, 2));
                
                if (iDuration/iPeriod == 0)
                {
                    iRet = RET.NO_EFFECT;
                }    
                else
                {
                    iRet = RET.SUCCEEDED;
                }
            }
            catch (System.Exception ex)
            {
                iRet = RET.ERROR;
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_CHECK_SAMPLING_PARAMETER_CORRECTED, "CheckSamplingParameterCorrected", ex);
            }

            return iRet;
        }

        //*************************************************************************
        //函数名称：GetSamplingPeriodAndPoints
        //函数功能：获取微秒级采样周期与采集点数
        //
        //输入参数：string In_strPeriod      采样周期   
        //         string In_strDuration    采样时长
        //         ref int Out_iPeriod      微秒级采样周期
        //         ref int Out_iPoints      采样点数
        //       
        //输出参数：0：NO_EFFECT
        //         1: SUCCEED
        //        -1: WRONG
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public static int GetSamplingPeriodAndPoints(string In_strPeriod, string In_strDuration, string In_strContinuousSampling, ref int Out_iPeriod, ref int Out_iPoints)
        {
            int iIndex = -1;
            int iPeriod = -1;
            int iDuration = -1;

            try
            {
                if (string.IsNullOrEmpty(In_strPeriod) || string.IsNullOrEmpty(In_strDuration) || string.IsNullOrEmpty(In_strContinuousSampling))
                    return RET.NO_EFFECT;
               
                iIndex = In_strPeriod.IndexOf("m");
                iPeriod = Convert.ToInt32(In_strPeriod.Remove(iIndex, 2));

                iIndex = In_strDuration.IndexOf("m");
                iDuration = Convert.ToInt32(In_strDuration.Remove(iIndex, 2));

                Out_iPeriod = iPeriod * 1000;//微秒量级

                if (In_strContinuousSampling == "是")
                    Out_iPoints = FaultDataOscilloscope.Collection; 
                else
                    Out_iPoints = iDuration / iPeriod;

                if (Out_iPoints == 0)        
                    return RET.NO_EFFECT;
                else
                    return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_GET_SAMPLING_PERIOD_AND_POINTS, "GetSamplingPeriodAndPoints", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：StaticYMaxminPoint
        //函数功能：获取Y轴最值
        //
        //输入参数：List<ChannelInfo> In_lstChannelInfo      参数集合
        //         ref int iYMax                            Y最大值
        //         ref int iYMin                            Y最小值
        //       
        //输出参数：0: NO_EFFECT
        //         1: SUCCEED
        //        -1: ERROR
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public static int StaticYMaxminPoint(List<FaultDataChannelInfo> In_lstChannelInfo, ref Int64 iYMax, ref Int64 iYMin)
        {
            Int64 iMin = 0;
            Int64 iMax = 0;
            List<Int64> lstMax = new List<Int64>();
            List<Int64> lstMin = new List<Int64>();

            try
            {
                for (int i = 0; i < In_lstChannelInfo.Count; i++)
                {
                    if (In_lstChannelInfo[i].FaultDataOscilloscope.Count == 0)
                    {
                        continue;
                    }
                    else
                    {
                        iMin = Convert.ToInt64(In_lstChannelInfo[i].FaultDataOscilloscope.Min(t => t.Value));
                        lstMin.Add(iMin);

                        iMax = Convert.ToInt64(In_lstChannelInfo[i].FaultDataOscilloscope.Max(t => t.Value));
                        lstMax.Add(iMax);
                    }
                }

                if (lstMin.Count != 0 && lstMax.Count == lstMin.Count)
                {
                    iYMin = lstMin.Min();
                    iYMax = lstMax.Max();

                }
                else
                {
                    iYMin = 0;
                    iYMax = 0;
                }

                //获取旷量倍数
                int iTimes = Convert.ToString((Int64)(iYMax - iYMin)).Length;
                iYMax += (long)Math.Pow(FaultDataOscilloscope.Vacant, iTimes);
                iYMin -= (long)Math.Pow(FaultDataOscilloscope.Vacant, iTimes);

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_STATIC_Y_MIN_POINT, "StaticYMinPoint", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：EvaluateLastWaveDataFromCurrent
        //函数功能：记录上一条数据
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public static void EvaluateLastWaveDataFromCurrent()
        {
            OfflineFaultAcquisition.Last.lstChannel1 = new List<int>();
            OfflineFaultAcquisition.Last.lstChannel2 = new List<int>();
            OfflineFaultAcquisition.Last.lstChannel3 = new List<int>();
            OfflineFaultAcquisition.Last.lstChannel4 = new List<int>();
            OfflineFaultAcquisition.Last.lstChannel5 = new List<int>();
            OfflineFaultAcquisition.Last.lstChannel6 = new List<int>();
            OfflineFaultAcquisition.Last.lstChannel7 = new List<int>();
            OfflineFaultAcquisition.Last.lstChannel8 = new List<int>();

            FaultAcquisitionData.Channel1?.ForEach(item => OfflineFaultAcquisition.Last.lstChannel1.Add(item));
            FaultAcquisitionData.Channel2?.ForEach(item => OfflineFaultAcquisition.Last.lstChannel2.Add(item));
            FaultAcquisitionData.Channel3?.ForEach(item => OfflineFaultAcquisition.Last.lstChannel3.Add(item));
            FaultAcquisitionData.Channel4?.ForEach(item => OfflineFaultAcquisition.Last.lstChannel4.Add(item));
            FaultAcquisitionData.Channel5?.ForEach(item => OfflineFaultAcquisition.Last.lstChannel5.Add(item));
            FaultAcquisitionData.Channel6?.ForEach(item => OfflineFaultAcquisition.Last.lstChannel6.Add(item));
            FaultAcquisitionData.Channel7?.ForEach(item => OfflineFaultAcquisition.Last.lstChannel7.Add(item));
            FaultAcquisitionData.Channel8?.ForEach(item => OfflineFaultAcquisition.Last.lstChannel8.Add(item));

            OfflineFaultAcquisition.Last.lstUnit = new List<string>();
            OfflineFaultAcquisition.Last.lstExchangeValue = new List<double>();

            FaultAcquisitionInfoSet.lstUnit?.ForEach(item => OfflineFaultAcquisition.Last.lstUnit.Add(item));
            FaultAcquisitionInfoSet.lstExchangeValue?.ForEach(item => OfflineFaultAcquisition.Last.lstExchangeValue.Add(item));
        }

        //*************************************************************************
        //函数名称：GetIndexOfArrayWaveData
        //函数功能：获取波形值对应的检索号
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public static string GetIndexOfArrayWaveData(double dValue)
        {
            int iRet = -1;
            double AcquisitionPeriod = 0;
            List<double> lstValue = new List<double>();

            try
            {
                if (FaultAcquisitionData.Channel1 == null)
                {
                    return null;
                }

                AcquisitionPeriod = OthersHelper.GetAcquisitionPeriod_ForFaultData(ViewModelSet.FaultDataOscilloscope?.SamplingPeriod);
                if (AcquisitionPeriod == RET.NO_EFFECT)
                {
                    return null;
                }

                for (int i = 0; i < FaultAcquisitionData.Channel1.Count; i++)
                {
                    lstValue.Add(i * AcquisitionPeriod);
                }

                iRet = lstValue.IndexOf(lstValue.Where(item => item >= dValue).FirstOrDefault());
                if (iRet == RET.ERROR)
                {
                    return null;
                }
                else
                {
                    return iRet.ToString();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_GET_INDEX_OF_ARRAY_WAVE_DATA, "GetIndexOfArrayWaveData", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetIndexOfArrayWaveData
        //函数功能：获取波形值对应的检索号
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.6
        //*************************************************************************
        public static int[] GetMaxminOfArrayWaveData(bool bMax, string strBeginIndex, string strEndIndex)
        {
            int iBeginIndex = 0;
            int iEndIndex = 0;
            int iLength = 0;
            int iIndex = 0;
            int[] arrValue = new int[4]; 

            try
            {
                if (FaultAcquisitionData.Channel1 == null)
                {
                    return null;
                }

                if (string.IsNullOrEmpty(strBeginIndex) || string.IsNullOrEmpty(strEndIndex))
                {
                    return null;
                }
                else
                {
                    iBeginIndex = Convert.ToInt32(strBeginIndex);
                    iEndIndex = Convert.ToInt32(strEndIndex);
                    iLength = Math.Abs(iBeginIndex - iEndIndex) + 1;

                    if (iBeginIndex < iEndIndex)
                    {
                        iIndex = iBeginIndex;
                    }
                    else
                    {
                        iIndex = iEndIndex;
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel1 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel1 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                {                                     
                    if (bMax)
                    {
                        arrValue[0] = FaultAcquisitionData.Channel1.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[0] = FaultAcquisitionData.Channel1.GetRange(iIndex, iLength).Min();
                    }                                     
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel2 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel2 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (bMax)
                    {
                        arrValue[1] = FaultAcquisitionData.Channel2.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[1] = FaultAcquisitionData.Channel2.GetRange(iIndex, iLength).Min();
                    }                                      
                }                

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel3 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel3 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (bMax)
                    {
                        arrValue[2] = FaultAcquisitionData.Channel3.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[2] = FaultAcquisitionData.Channel3.GetRange(iIndex, iLength).Min();
                    }                      
                }              

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel4 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel4 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (bMax)
                    {
                        arrValue[3] = FaultAcquisitionData.Channel4.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[3] = FaultAcquisitionData.Channel4.GetRange(iIndex, iLength).Min();
                    }                 
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel5 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel5 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                {
                    if (bMax)
                    {
                        arrValue[4] = FaultAcquisitionData.Channel5.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[4] = FaultAcquisitionData.Channel5.GetRange(iIndex, iLength).Min();
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel6 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel6 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                {
                    if (bMax)
                    {
                        arrValue[5] = FaultAcquisitionData.Channel6.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[5] = FaultAcquisitionData.Channel6.GetRange(iIndex, iLength).Min();
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel7 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel7 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                {
                    if (bMax)
                    {
                        arrValue[6] = FaultAcquisitionData.Channel7.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[6] = FaultAcquisitionData.Channel7.GetRange(iIndex, iLength).Min();
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel8 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel8 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                {
                    if (bMax)
                    {
                        arrValue[7] = FaultAcquisitionData.Channel8.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[7] = FaultAcquisitionData.Channel8.GetRange(iIndex, iLength).Min();
                    }
                }

                return arrValue;                                                   
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_GET_MAXMIN_OF_ARRAY_WAVE_DATA, "GetMaxminOfArrayWaveData", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetAverageOfArrayWaveData
        //函数功能：获取波形值平均值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public static double[] GetAverageOfArrayWaveData(string strBeginIndex, string strEndIndex)
        {
            int iBeginIndex = 0;
            int iEndIndex = 0;
            int iLength = 0;
            int iIndex = 0;
            double[] arrValue = new double[4];

            try
            {
                if (FaultAcquisitionData.Channel1 == null)
                {
                    return null;
                }

                if (string.IsNullOrEmpty(strBeginIndex) || string.IsNullOrEmpty(strEndIndex))
                {
                    return null;
                }
                else
                {
                    iBeginIndex = Convert.ToInt32(strBeginIndex);
                    iEndIndex = Convert.ToInt32(strEndIndex);
                    iLength = Math.Abs(iBeginIndex - iEndIndex) + 1;

                    if (iBeginIndex < iEndIndex)
                    {
                        iIndex = iBeginIndex;
                    }
                    else
                    {
                        iIndex = iEndIndex;
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel1 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel1 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    arrValue[0] = Math.Round(FaultAcquisitionData.Channel1.GetRange(iIndex, iLength).Average(), 2);
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel2 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel2 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    arrValue[1] = Math.Round(FaultAcquisitionData.Channel2.GetRange(iIndex, iLength).Average(), 2);
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel3 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel3 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    arrValue[2] = Math.Round(FaultAcquisitionData.Channel3.GetRange(iIndex, iLength).Average(), 2);
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel4 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel4 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                { 
                    arrValue[3] = Math.Round(FaultAcquisitionData.Channel4.GetRange(iIndex, iLength).Average(), 2);
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel5 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel5 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                {
                    arrValue[4] = Math.Round(FaultAcquisitionData.Channel5.GetRange(iIndex, iLength).Average(), 2);
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel6 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel6 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                {
                    arrValue[5] = Math.Round(FaultAcquisitionData.Channel6.GetRange(iIndex, iLength).Average(), 2);
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel7 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel7 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                {
                    arrValue[6] = Math.Round(FaultAcquisitionData.Channel7.GetRange(iIndex, iLength).Average(), 2);
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel8 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel8 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                {
                    arrValue[7] = Math.Round(FaultAcquisitionData.Channel8.GetRange(iIndex, iLength).Average(), 2);
                }

                return arrValue;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_GET_AVERAGE_OF_ARRAY_WAVE_DATA, "GetAverageOfArrayWaveData", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetRMSOfArrayWaveData
        //函数功能：获取波形均方根
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.06
        //*************************************************************************
        public static double[] GetRMSOfArrayWaveData(string strBeginIndex, string strEndIndex)
        {
            int iRet = -1;
            int iBeginIndex = 0;
            int iEndIndex = 0;
            int iLength = 0;
            int iIndex = 0;
            double dRMS = 0;
            double[] arrValue = new double[4];

            try
            {
                if (FaultAcquisitionData.Channel1 == null)
                {
                    return null;
                }

                if (string.IsNullOrEmpty(strBeginIndex) || string.IsNullOrEmpty(strEndIndex))
                {
                    return null;
                }
                else
                {
                    iBeginIndex = Convert.ToInt32(strBeginIndex);
                    iEndIndex = Convert.ToInt32(strEndIndex);
                    iLength = Math.Abs(iBeginIndex - iEndIndex) + 1;

                    if (iBeginIndex < iEndIndex)
                    {
                        iIndex = iBeginIndex;
                    }
                    else
                    {
                        iIndex = iEndIndex;
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel1 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel1 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    List<int> Channel1 = FaultAcquisitionData.Channel1.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel1, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[0] = dRMS;
                    }                   
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel2 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel2 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    List<int> Channel2 = FaultAcquisitionData.Channel2.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel2, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[1] = dRMS;
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel3 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel3 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    List<int> Channel3 = FaultAcquisitionData.Channel3.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel3, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[2] = dRMS;
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel4 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel4 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    List<int> Channel4 = FaultAcquisitionData.Channel4.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel4, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[3] = dRMS;
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel5 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel5 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
                {
                    List<int> Channel5 = FaultAcquisitionData.Channel5.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel5, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[4] = dRMS;
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel6 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel6 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
                {
                    List<int> Channel6 = FaultAcquisitionData.Channel6.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel6, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[5] = dRMS;
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel7 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel7 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
                {
                    List<int> Channel7 = FaultAcquisitionData.Channel7.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel7, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[6] = dRMS;
                    }
                }

                if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel8 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel8 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
                {
                    List<int> Channel8 = FaultAcquisitionData.Channel8.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel8, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[7] = dRMS;
                    }
                }

                return arrValue;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.FAULT_DATA_OSCILLOSCOPE_GET_RMS_OF_ARRAY_WAVE_DATA, "GetRMSOfArrayWaveData", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：AddCalculateSet
        //函数功能：添加计算集合
        //
        //输入参数：
        //         
        //输出参数：
        //        
        //编码作者：Lilbert
        //更新时间：2023.04.11
        //*************************************************************************
        public static FaultDataOsilloscopeCalculateSet AddCalculateSet(string strTitle, string strIndex, string strTime, double dCH1Value, double dCH2Value, double dCH3Value, double dCH4Value, double dCH5Value, double dCH6Value, double dCH7Value, double dCH8Value)
        {
            if (ViewModelSet.FaultDataOscilloscope == null)
            {
                return null;
            }

            FaultDataOsilloscopeCalculateSet clsData = new FaultDataOsilloscopeCalculateSet();
            clsData.Title = strTitle;
            clsData.Number = strIndex;
            clsData.AcquisitionTime = strTime;

            if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel1 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel1 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[0].IsHidden)
            {
                clsData.Channel1Value = Convert.ToString(dCH1Value);
            }
            else
            {
                clsData.Channel1Value = null;
            }

            if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel2 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel2 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[1].IsHidden)
            {
                clsData.Channel2Value = Convert.ToString(dCH2Value);
            }
            else
            {
                clsData.Channel2Value = null;
            }

            if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel3 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel3 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[2].IsHidden)
            {
                clsData.Channel3Value = Convert.ToString(dCH3Value);
            }
            else
            {
                clsData.Channel3Value = null;
            }

            if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel4 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel4 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[3].IsHidden)
            {
                clsData.Channel4Value = Convert.ToString(dCH4Value);
            }
            else
            {
                clsData.Channel4Value = null;
            }

            if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel5 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel5 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[4].IsHidden)
            {
                clsData.Channel5Value = Convert.ToString(dCH5Value);
            }
            else
            {
                clsData.Channel5Value = null;
            }

            if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel6 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel6 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[5].IsHidden)
            {
                clsData.Channel6Value = Convert.ToString(dCH6Value);
            }
            else
            {
                clsData.Channel6Value = null;
            }

            if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel7 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel7 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[6].IsHidden)
            {
                clsData.Channel7Value = Convert.ToString(dCH7Value);
            }
            else
            {
                clsData.Channel7Value = null;
            }

            if (ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel8 != null && ViewModelSet.FaultDataOscilloscope.SelectedSamplingChannel8 != "停用" && !ViewModelSet.FaultDataOscilloscope.OscilloscopeProperty[7].IsHidden)
            {
                clsData.Channel8Value = Convert.ToString(dCH8Value);
            }
            else
            {
                clsData.Channel8Value = null;
            }

            return clsData;
        }
    }
}