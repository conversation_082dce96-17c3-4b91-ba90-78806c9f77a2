using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ServoStudio.Models;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;

namespace ServoStudio.Services
{
    /// <summary>
    /// 参数对比服务
    /// </summary>
    public class ParameterComparisonService
    {
        /// <summary>
        /// 对比导入参数与当前设备参数
        /// </summary>
        /// <param name="importParameters">导入的参数列表</param>
        /// <param name="currentParameters">当前设备参数列表</param>
        /// <returns>对比结果</returns>
        public async Task<ComparisonResult> CompareParametersAsync(
            List<DiffParameterReadWriteSet> importParameters,
            List<DiffParameterReadWriteSet> currentParameters)
        {
            return await Task.Run(() =>
            {
                var result = new ComparisonResult
                {
                    ComparisonTime = DateTime.Now,
                    ImportParameterCount = importParameters.Count,
                    CurrentParameterCount = currentParameters.Count
                };

                try
                {
                    // 创建当前参数的字典，以Index为键
                    var currentDict = currentParameters.ToDictionary(p => p.Index, p => p);

                    foreach (var importParam in importParameters)
                    {
                        if (currentDict.TryGetValue(importParam.Index, out var currentParam))
                        {
                            // 参数存在，检查值是否不同
                            if (!AreParameterValuesEqual(importParam.Current, currentParam.Current))
                            {
                                result.Differences.Add(CreateParameterDifference(
                                    importParam, currentParam, DifferenceType.ValueDifferent));
                            }
                        }
                        else
                        {
                            // 新参数（导入文件中有，设备中没有）
                            result.Differences.Add(CreateParameterDifference(
                                importParam, null, DifferenceType.NewParameter));
                        }
                    }

                    // 检查设备中有但导入文件中没有的参数
                    var importDict = importParameters.ToDictionary(p => p.Index, p => p);
                    foreach (var currentParam in currentParameters)
                    {
                        if (!importDict.ContainsKey(currentParam.Index))
                        {
                            // 缺失参数（设备中有，导入文件中没有）
                            result.Differences.Add(CreateParameterDifference(
                                null, currentParam, DifferenceType.MissingParameter));
                        }
                    }
                }
                catch (Exception ex)
                {
                    SoftwareErrorHelper.CatchDispose(ERROR.PARAMETER_COMPARISON_SERVICE, 
                        "CompareParametersAsync", ex);
                    throw;
                }

                return result;
            });
        }

        /// <summary>
        /// 验证参数值是否有效
        /// </summary>
        /// <param name="parameter">参数信息</param>
        /// <param name="value">要验证的值</param>
        /// <returns>是否有效</returns>
        public bool ValidateParameterValue(DiffParameterReadWriteSet parameter, string value)
        {
            try
            {
                // 检查是否为只读参数
                if (parameter.RWProperty == "RO")
                {
                    return false;
                }

                // 检查是否为空值
                if (string.IsNullOrWhiteSpace(value))
                {
                    return false;
                }

                // 检查是否为16进制数，如果是则转换为10进制
                string validatedValue = value;
                if (OthersHelper.CheckIsInputHex(value))
                {
                    validatedValue = OthersHelper.TransferHexToDecimal(value, parameter.DataType);
                }

                // 检查是否为有效的数字
                if (!OthersHelper.IsInputInteger(validatedValue))
                {
                    return false;
                }

                // 检查数据范围
                return OthersHelper.IsOutOfRange(ref validatedValue, parameter.Unit, 
                    parameter.DataType, parameter.Max, parameter.Min);
            }
            catch (Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETER_VALIDATION, 
                    "ValidateParameterValue", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取参数差异列表（按类型过滤）
        /// </summary>
        /// <param name="result">对比结果</param>
        /// <param name="filterType">过滤类型，null表示不过滤</param>
        /// <returns>过滤后的差异列表</returns>
        public List<ParameterDifference> GetDifferences(ComparisonResult result, DifferenceType? filterType = null)
        {
            if (filterType.HasValue)
            {
                return result.Differences.Where(d => d.Type == filterType.Value).ToList();
            }
            return result.Differences.ToList();
        }

        /// <summary>
        /// 获取可写入的参数差异列表（排除只读参数）
        /// </summary>
        /// <param name="result">对比结果</param>
        /// <returns>可写入的参数差异列表</returns>
        public List<ParameterDifference> GetWritableParameters(ComparisonResult result)
        {
            return result.Differences.Where(d => !d.IsReadOnly && 
                d.Type != DifferenceType.MissingParameter).ToList();
        }

        /// <summary>
        /// 批量验证参数值
        /// </summary>
        /// <param name="parameters">要验证的参数列表</param>
        /// <returns>验证结果字典，键为参数Index，值为验证结果</returns>
        public Dictionary<string, bool> ValidateParameters(List<ParameterDifference> parameters)
        {
            var results = new Dictionary<string, bool>();

            foreach (var param in parameters)
            {
                var parameterSet = new DiffParameterReadWriteSet
                {
                    Index = param.Index,
                    DataType = param.DataType,
                    Unit = param.Unit,
                    Min = param.Min,
                    Max = param.Max,
                    RWProperty = param.RWProperty
                };

                results[param.Index] = ValidateParameterValue(parameterSet, param.ImportValue);
            }

            return results;
        }

        /// <summary>
        /// 创建参数差异对象
        /// </summary>
        /// <param name="importParam">导入参数</param>
        /// <param name="currentParam">当前参数</param>
        /// <param name="type">差异类型</param>
        /// <returns>参数差异对象</returns>
        private ParameterDifference CreateParameterDifference(
            DiffParameterReadWriteSet importParam,
            DiffParameterReadWriteSet currentParam,
            DifferenceType type)
        {
            var difference = new ParameterDifference
            {
                Type = type
            };

            // 根据差异类型设置参数信息
            switch (type)
            {
                case DifferenceType.ValueDifferent:
                    // 值不同：使用导入参数的信息，但显示两个值
                    SetParameterInfo(difference, importParam);
                    difference.CurrentValue = currentParam?.Current ?? "N/A";
                    difference.ImportValue = importParam?.Current ?? "N/A";
                    break;

                case DifferenceType.NewParameter:
                    // 新参数：使用导入参数的信息
                    SetParameterInfo(difference, importParam);
                    difference.CurrentValue = "N/A";
                    difference.ImportValue = importParam?.Current ?? "N/A";
                    break;

                case DifferenceType.MissingParameter:
                    // 缺失参数：使用当前参数的信息
                    SetParameterInfo(difference, currentParam);
                    difference.CurrentValue = currentParam?.Current ?? "N/A";
                    difference.ImportValue = "N/A";
                    break;
            }

            return difference;
        }

        /// <summary>
        /// 设置参数差异对象的基本信息
        /// </summary>
        /// <param name="difference">参数差异对象</param>
        /// <param name="parameter">参数信息</param>
        private void SetParameterInfo(ParameterDifference difference, DiffParameterReadWriteSet parameter)
        {
            if (parameter == null) return;

            difference.Index = parameter.Index;
            difference.Name = parameter.Name;
            difference.Description = parameter.Description;
            difference.DataType = parameter.DataType;
            difference.Unit = parameter.Unit;
            difference.Min = parameter.Min;
            difference.Max = parameter.Max;
            difference.Default = parameter.Default;
            difference.RWProperty = parameter.RWProperty;
            difference.Classification = parameter.Classification;
            difference.Comment = parameter.Comment;
        }

        /// <summary>
        /// 比较两个参数值是否相等
        /// </summary>
        /// <param name="value1">值1</param>
        /// <param name="value2">值2</param>
        /// <returns>是否相等</returns>
        private bool AreParameterValuesEqual(string value1, string value2)
        {
            // 处理空值情况
            if (string.IsNullOrWhiteSpace(value1) && string.IsNullOrWhiteSpace(value2))
                return true;

            if (string.IsNullOrWhiteSpace(value1) || string.IsNullOrWhiteSpace(value2))
                return false;

            // 去除空格后比较
            return value1.Trim().Equals(value2.Trim(), StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 获取对比统计信息
        /// </summary>
        /// <param name="result">对比结果</param>
        /// <returns>统计信息</returns>
        public ComparisonStatistics GetStatistics(ComparisonResult result)
        {
            return new ComparisonStatistics
            {
                StartTime = result.ComparisonTime,
                EndTime = DateTime.Now,
                IsSuccessful = true
            };
        }

        /// <summary>
        /// 生成对比报告摘要
        /// </summary>
        /// <param name="result">对比结果</param>
        /// <returns>报告摘要</returns>
        public string GenerateComparisonSummary(ComparisonResult result)
        {
            var summary = $"参数对比完成\n";
            summary += $"对比时间: {result.ComparisonTime:yyyy-MM-dd HH:mm:ss}\n";
            summary += $"导入参数数量: {result.ImportParameterCount}\n";
            summary += $"设备参数数量: {result.CurrentParameterCount}\n";
            summary += $"差异参数总数: {result.DifferenceCount}\n";
            summary += $"  - 值不同: {result.ValueDifferentCount}\n";
            summary += $"  - 新参数: {result.NewParameterCount}\n";
            summary += $"  - 缺失参数: {result.MissingParameterCount}\n";

            return summary;
        }
    }
}