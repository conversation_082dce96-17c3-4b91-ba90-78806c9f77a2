<?xml version="1.0"?>
<doc>
    <assembly>
        <name>GalaSoft.MvvmLight</name>
    </assembly>
    <members>
        <member name="T:GalaSoft.MvvmLight.Command.RelayCommand">
            <summary>
            A command whose sole purpose is to relay its functionality to other
            objects by invoking delegates. The default return value for the CanExecute
            method is 'true'.  This class does not allow you to accept command parameters in the
            Execute and CanExecute callback methods.
            </summary>
            <remarks>If you are using this class in WPF4.5 or above, you need to use the 
            GalaSoft.MvvmLight.CommandWpf namespace (instead of GalaSoft.MvvmLight.Command).
            This will enable (or restore) the CommandManager class which handles
            automatic enabling/disabling of controls based on the CanExecute delegate.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.RelayCommand.#ctor(System.Action,System.Boolean)">
            <summary>
            Initializes a new instance of the RelayCommand class that 
            can always execute.
            </summary>
            <param name="execute">The execution logic. IMPORTANT: If the action causes a closure,
            you must set keepTargetAlive to true to avoid side effects. </param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is causing a closure. See
            http://galasoft.ch/s/mvvmweakaction. </param>
            <exception cref="T:System.ArgumentNullException">If the execute argument is null.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.RelayCommand.#ctor(System.Action,System.Func{System.Boolean},System.Boolean)">
            <summary>
            Initializes a new instance of the RelayCommand class.
            </summary>
            <param name="execute">The execution logic. IMPORTANT: If the action causes a closure,
            you must set keepTargetAlive to true to avoid side effects. </param>
            <param name="canExecute">The execution status logic.  IMPORTANT: If the func causes a closure,
            you must set keepTargetAlive to true to avoid side effects. </param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is causing a closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
            <exception cref="T:System.ArgumentNullException">If the execute argument is null.</exception>
        </member>
        <member name="E:GalaSoft.MvvmLight.Command.RelayCommand.CanExecuteChanged">
            <summary>
            Occurs when changes occur that affect whether the command should execute.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.RelayCommand.RaiseCanExecuteChanged">
            <summary>
            Raises the <see cref="E:GalaSoft.MvvmLight.Command.RelayCommand.CanExecuteChanged" /> event.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.RelayCommand.CanExecute(System.Object)">
            <summary>
            Defines the method that determines whether the command can execute in its current state.
            </summary>
            <param name="parameter">This parameter will always be ignored.</param>
            <returns>true if this command can be executed; otherwise, false.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.RelayCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked. 
            </summary>
            <param name="parameter">This parameter will always be ignored.</param>
        </member>
        <member name="T:GalaSoft.MvvmLight.Command.RelayCommand`1">
            <summary>
            A generic command whose sole purpose is to relay its functionality to other
            objects by invoking delegates. The default return value for the CanExecute
            method is 'true'. This class allows you to accept command parameters in the
            Execute and CanExecute callback methods.
            </summary>
            <typeparam name="T">The type of the command parameter.</typeparam>
            <remarks>If you are using this class in WPF4.5 or above, you need to use the 
            GalaSoft.MvvmLight.CommandWpf namespace (instead of GalaSoft.MvvmLight.Command).
            This will enable (or restore) the CommandManager class which handles
            automatic enabling/disabling of controls based on the CanExecute delegate.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.RelayCommand`1.#ctor(System.Action{`0},System.Boolean)">
            <summary>
            Initializes a new instance of the RelayCommand class that 
            can always execute.
            </summary>
            <param name="execute">The execution logic. IMPORTANT: If the action causes a closure,
            you must set keepTargetAlive to true to avoid side effects. </param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is causing a closure. See
            http://galasoft.ch/s/mvvmweakaction. </param>
            <exception cref="T:System.ArgumentNullException">If the execute argument is null.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.RelayCommand`1.#ctor(System.Action{`0},System.Func{`0,System.Boolean},System.Boolean)">
            <summary>
            Initializes a new instance of the RelayCommand class.
            </summary>
            <param name="execute">The execution logic. IMPORTANT: If the action causes a closure,
            you must set keepTargetAlive to true to avoid side effects. </param>
            <param name="canExecute">The execution status logic.  IMPORTANT: If the func causes a closure,
            you must set keepTargetAlive to true to avoid side effects. </param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is causing a closure. See
            http://galasoft.ch/s/mvvmweakaction. </param>
            <exception cref="T:System.ArgumentNullException">If the execute argument is null.</exception>
        </member>
        <member name="E:GalaSoft.MvvmLight.Command.RelayCommand`1.CanExecuteChanged">
            <summary>
            Occurs when changes occur that affect whether the command should execute.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.RelayCommand`1.RaiseCanExecuteChanged">
            <summary>
            Raises the <see cref="E:GalaSoft.MvvmLight.Command.RelayCommand`1.CanExecuteChanged" /> event.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.RelayCommand`1.CanExecute(System.Object)">
            <summary>
            Defines the method that determines whether the command can execute in its current state.
            </summary>
            <param name="parameter">Data used by the command. If the command does not require data 
            to be passed, this object can be set to a null reference</param>
            <returns>true if this command can be executed; otherwise, false.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Command.RelayCommand`1.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked. 
            </summary>
            <param name="parameter">Data used by the command. If the command does not require data 
            to be passed, this object can be set to a null reference</param>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.DesignerLibrary">
            <summary>
            Helper class for platform detection.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.Empty">
            <summary>
            Helper class used when an async method is required,
            but the context is synchronous.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.Empty.Task">
            <summary>
            Gets the empty task.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.FeatureDetection">
            <summary>
            Helper class for platform and feature detection.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.IExecuteWithObject">
            <summary>
            This interface is meant for the <see cref="T:GalaSoft.MvvmLight.Helpers.WeakAction`1" /> class and can be 
            useful if you store multiple WeakAction{T} instances but don't know in advance
            what type T represents.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.IExecuteWithObject.Target">
            <summary>
            The target of the WeakAction.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.IExecuteWithObject.ExecuteWithObject(System.Object)">
            <summary>
            Executes an action.
            </summary>
            <param name="parameter">A parameter passed as an object,
            to be casted to the appropriate type.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.IExecuteWithObject.MarkForDeletion">
            <summary>
            Deletes all references, which notifies the cleanup method
            that this entry must be deleted.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.IExecuteWithObjectAndResult">
            <summary>
            This interface is meant for the <see cref="T:GalaSoft.MvvmLight.Helpers.WeakFunc`1" /> class and can be 
            useful if you store multiple WeakFunc{T} instances but don't know in advance
            what type T represents.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.IExecuteWithObjectAndResult.ExecuteWithObject(System.Object)">
            <summary>
            Executes a Func and returns the result.
            </summary>
            <param name="parameter">A parameter passed as an object,
            to be casted to the appropriate type.</param>
            <returns>The result of the operation.</returns>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.WeakAction">
            <summary>
            Stores an <see cref="T:System.Action" /> without causing a hard reference
            to be created to the Action's owner. The owner can be garbage collected at any time.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakAction.Method">
            <summary>
            Gets or sets the <see cref="T:System.Reflection.MethodInfo" /> corresponding to this WeakAction's
            method passed in the constructor.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakAction.MethodName">
            <summary>
            Gets the name of the method that this WeakAction represents.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakAction.ActionReference">
            <summary>
            Gets or sets a WeakReference to this WeakAction's action's target.
            This is not necessarily the same as
            <see cref="P:GalaSoft.MvvmLight.Helpers.WeakAction.Reference" />, for example if the
            method is anonymous.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakAction.LiveReference">
            <summary>
            Saves the <see cref="P:GalaSoft.MvvmLight.Helpers.WeakAction.ActionReference"/> as a hard reference. This is
            used in relation with this instance's constructor and only if
            the constructor's keepTargetAlive parameter is true.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakAction.Reference">
            <summary>
            Gets or sets a WeakReference to the target passed when constructing
            the WeakAction. This is not necessarily the same as
            <see cref="P:GalaSoft.MvvmLight.Helpers.WeakAction.ActionReference" />, for example if the
            method is anonymous.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakAction.IsStatic">
            <summary>
            Gets a value indicating whether the WeakAction is static or not.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakAction.#ctor">
            <summary>
            Initializes an empty instance of the <see cref="T:GalaSoft.MvvmLight.Helpers.WeakAction" /> class.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakAction.#ctor(System.Action,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:GalaSoft.MvvmLight.Helpers.WeakAction" /> class.
            </summary>
            <param name="action">The action that will be associated to this instance.</param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakAction.#ctor(System.Object,System.Action,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:GalaSoft.MvvmLight.Helpers.WeakAction" /> class.
            </summary>
            <param name="target">The action's owner.</param>
            <param name="action">The action that will be associated to this instance.</param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakAction.IsAlive">
            <summary>
            Gets a value indicating whether the Action's owner is still alive, or if it was collected
            by the Garbage Collector already.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakAction.Target">
            <summary>
            Gets the Action's owner. This object is stored as a 
            <see cref="T:System.WeakReference" />.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakAction.ActionTarget">
            <summary>
            The target of the weak reference.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakAction.Execute">
            <summary>
            Executes the action. This only happens if the action's owner
            is still alive.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakAction.MarkForDeletion">
            <summary>
            Sets the reference that this instance stores to null.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.WeakAction`1">
            <summary>
            Stores an Action without causing a hard reference
            to be created to the Action's owner. The owner can be garbage collected at any time.
            </summary>
            <typeparam name="T">The type of the Action's parameter.</typeparam>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakAction`1.MethodName">
            <summary>
            Gets the name of the method that this WeakAction represents.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakAction`1.IsAlive">
            <summary>
            Gets a value indicating whether the Action's owner is still alive, or if it was collected
            by the Garbage Collector already.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakAction`1.#ctor(System.Action{`0},System.Boolean)">
            <summary>
            Initializes a new instance of the WeakAction class.
            </summary>
            <param name="action">The action that will be associated to this instance.</param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakAction`1.#ctor(System.Object,System.Action{`0},System.Boolean)">
            <summary>
            Initializes a new instance of the WeakAction class.
            </summary>
            <param name="target">The action's owner.</param>
            <param name="action">The action that will be associated to this instance.</param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakAction`1.Execute">
            <summary>
            Executes the action. This only happens if the action's owner
            is still alive. The action's parameter is set to default(T).
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakAction`1.Execute(`0)">
            <summary>
            Executes the action. This only happens if the action's owner
            is still alive.
            </summary>
            <param name="parameter">A parameter to be passed to the action.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakAction`1.ExecuteWithObject(System.Object)">
            <summary>
            Executes the action with a parameter of type object. This parameter
            will be casted to T. This method implements <see cref="M:GalaSoft.MvvmLight.Helpers.IExecuteWithObject.ExecuteWithObject(System.Object)" />
            and can be useful if you store multiple WeakAction{T} instances but don't know in advance
            what type T represents.
            </summary>
            <param name="parameter">The parameter that will be passed to the action after
            being casted to T.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakAction`1.MarkForDeletion">
            <summary>
            Sets all the actions that this WeakAction contains to null,
            which is a signal for containing objects that this WeakAction
            should be deleted.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.WeakFunc`1">
            <summary>
            Stores a Func&lt;T&gt; without causing a hard reference
            to be created to the Func's owner. The owner can be garbage collected at any time.
            </summary>
            <typeparam name="TResult">The type of the result of the Func that will be stored
            by this weak reference.</typeparam>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.Method">
            <summary>
            Gets or sets the <see cref="T:System.Reflection.MethodInfo" /> corresponding to this WeakFunc's
            method passed in the constructor.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.IsStatic">
            <summary>
            Get a value indicating whether the WeakFunc is static or not.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.MethodName">
            <summary>
            Gets the name of the method that this WeakFunc represents.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.FuncReference">
            <summary>
            Gets or sets a WeakReference to this WeakFunc's action's target.
            This is not necessarily the same as
            <see cref="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.Reference" />, for example if the
            method is anonymous.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.LiveReference">
            <summary>
            Saves the <see cref="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.FuncReference"/> as a hard reference. This is
            used in relation with this instance's constructor and only if
            the constructor's keepTargetAlive parameter is true.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.Reference">
            <summary>
            Gets or sets a WeakReference to the target passed when constructing
            the WeakFunc. This is not necessarily the same as
            <see cref="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.FuncReference" />, for example if the
            method is anonymous.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakFunc`1.#ctor">
            <summary>
            Initializes an empty instance of the WeakFunc class.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakFunc`1.#ctor(System.Func{`0},System.Boolean)">
            <summary>
            Initializes a new instance of the WeakFunc class.
            </summary>
            <param name="func">The Func that will be associated to this instance.</param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakFunc`1.#ctor(System.Object,System.Func{`0},System.Boolean)">
            <summary>
            Initializes a new instance of the WeakFunc class.
            </summary>
            <param name="target">The Func's owner.</param>
            <param name="func">The Func that will be associated to this instance.</param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.IsAlive">
            <summary>
            Gets a value indicating whether the Func's owner is still alive, or if it was collected
            by the Garbage Collector already.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.Target">
            <summary>
            Gets the Func's owner. This object is stored as a 
            <see cref="T:System.WeakReference" />.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.FuncTarget">
            <summary>
            Gets the owner of the Func that was passed as parameter.
            This is not necessarily the same as
            <see cref="P:GalaSoft.MvvmLight.Helpers.WeakFunc`1.Target" />, for example if the
            method is anonymous.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakFunc`1.Execute">
            <summary>
            Executes the action. This only happens if the Func's owner
            is still alive.
            </summary>
            <returns>The result of the Func stored as reference.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakFunc`1.MarkForDeletion">
            <summary>
            Sets the reference that this instance stores to null.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Helpers.WeakFunc`2">
            <summary>
            Stores an Func without causing a hard reference
            to be created to the Func's owner. The owner can be garbage collected at any time.
            </summary>
            <typeparam name="T">The type of the Func's parameter.</typeparam>
            <typeparam name="TResult">The type of the Func's return value.</typeparam>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakFunc`2.MethodName">
            <summary>
            Gets or sets the name of the method that this WeakFunc represents.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Helpers.WeakFunc`2.IsAlive">
            <summary>
            Gets a value indicating whether the Func's owner is still alive, or if it was collected
            by the Garbage Collector already.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakFunc`2.#ctor(System.Func{`0,`1},System.Boolean)">
            <summary>
            Initializes a new instance of the WeakFunc class.
            </summary>
            <param name="func">The Func that will be associated to this instance.</param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakFunc`2.#ctor(System.Object,System.Func{`0,`1},System.Boolean)">
            <summary>
            Initializes a new instance of the WeakFunc class.
            </summary>
            <param name="target">The Func's owner.</param>
            <param name="func">The Func that will be associated to this instance.</param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakFunc`2.Execute">
            <summary>
            Executes the Func. This only happens if the Func's owner
            is still alive. The Func's parameter is set to default(T).
            </summary>
            <returns>The result of the Func stored as reference.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakFunc`2.Execute(`0)">
            <summary>
            Executes the Func. This only happens if the Func's owner
            is still alive.
            </summary>
            <param name="parameter">A parameter to be passed to the action.</param>
            <returns>The result of the Func stored as reference.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakFunc`2.ExecuteWithObject(System.Object)">
            <summary>
            Executes the Func with a parameter of type object. This parameter
            will be casted to T. This method implements <see cref="M:GalaSoft.MvvmLight.Helpers.IExecuteWithObject.ExecuteWithObject(System.Object)" />
            and can be useful if you store multiple WeakFunc{T} instances but don't know in advance
            what type T represents.
            </summary>
            <param name="parameter">The parameter that will be passed to the Func after
            being casted to T.</param>
            <returns>The result of the execution as object, to be casted to T.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Helpers.WeakFunc`2.MarkForDeletion">
            <summary>
            Sets all the funcs that this WeakFunc contains to null,
            which is a signal for containing objects that this WeakFunc
            should be deleted.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.ICleanup">
            <summary>
            Defines a common interface for classes that should be cleaned up,
            but without the implications that IDisposable presupposes. An instance
            implementing ICleanup can be cleaned up without being
            disposed and garbage collected.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.ICleanup.Cleanup">
            <summary>
            Cleans up the instance, for example by saving its state,
            removing resources, etc...
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Messaging.GenericMessage`1">
            <summary>
            Passes a generic value (Content) to a recipient.
            </summary>
            <typeparam name="T">The type of the Content property.</typeparam>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.GenericMessage`1.#ctor(`0)">
            <summary>
            Initializes a new instance of the GenericMessage class.
            </summary>
            <param name="content">The message content.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.GenericMessage`1.#ctor(System.Object,`0)">
            <summary>
            Initializes a new instance of the GenericMessage class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="content">The message content.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.GenericMessage`1.#ctor(System.Object,System.Object,`0)">
            <summary>
            Initializes a new instance of the GenericMessage class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="target">The message's intended target. This parameter can be used
            to give an indication as to whom the message was intended for. Of course
            this is only an indication, amd may be null.</param>
            <param name="content">The message content.</param>
        </member>
        <member name="P:GalaSoft.MvvmLight.Messaging.GenericMessage`1.Content">
            <summary>
            Gets or sets the message's content.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Messaging.IMessenger">
            <summary>
            The Messenger is a class allowing objects to exchange messages.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Register``1(System.Object,System.Action{``0},System.Boolean)">
            <summary>
            Registers a recipient for a type of message TMessage. The action
            parameter will be executed when a corresponding message is sent.
            <para>Registering a recipient does not create a hard reference to it,
            so if this recipient is deleted, no memory leak is caused.</para>
            </summary>
            <typeparam name="TMessage">The type of message that the recipient registers
            for.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="action">The action that will be executed when a message
            of type TMessage is sent. IMPORTANT: Note that closures are not supported at the moment
            due to the use of WeakActions (see http://stackoverflow.com/questions/25730530/). </param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Register``1(System.Object,System.Object,System.Action{``0},System.Boolean)">
            <summary>
            Registers a recipient for a type of message TMessage.
            The action parameter will be executed when a corresponding 
            message is sent. See the receiveDerivedMessagesToo parameter
            for details on how messages deriving from TMessage (or, if TMessage is an interface,
            messages implementing TMessage) can be received too.
            <para>Registering a recipient does not create a hard reference to it,
            so if this recipient is deleted, no memory leak is caused.</para>
            </summary>
            <typeparam name="TMessage">The type of message that the recipient registers
            for.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="token">A token for a messaging channel. If a recipient registers
            using a token, and a sender sends a message using the same token, then this
            message will be delivered to the recipient. Other recipients who did not
            use a token when registering (or who used a different token) will not
            get the message. Similarly, messages sent without any token, or with a different
            token, will not be delivered to that recipient.</param>
            <param name="action">The action that will be executed when a message
            of type TMessage is sent.</param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Register``1(System.Object,System.Object,System.Boolean,System.Action{``0},System.Boolean)">
            <summary>
            Registers a recipient for a type of message TMessage.
            The action parameter will be executed when a corresponding 
            message is sent. See the receiveDerivedMessagesToo parameter
            for details on how messages deriving from TMessage (or, if TMessage is an interface,
            messages implementing TMessage) can be received too.
            <para>Registering a recipient does not create a hard reference to it,
            so if this recipient is deleted, no memory leak is caused.</para>
            </summary>
            <typeparam name="TMessage">The type of message that the recipient registers
            for.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="token">A token for a messaging channel. If a recipient registers
            using a token, and a sender sends a message using the same token, then this
            message will be delivered to the recipient. Other recipients who did not
            use a token when registering (or who used a different token) will not
            get the message. Similarly, messages sent without any token, or with a different
            token, will not be delivered to that recipient.</param>
            <param name="receiveDerivedMessagesToo">If true, message types deriving from
            TMessage will also be transmitted to the recipient. For example, if a SendOrderMessage
            and an ExecuteOrderMessage derive from OrderMessage, registering for OrderMessage
            and setting receiveDerivedMessagesToo to true will send SendOrderMessage
            and ExecuteOrderMessage to the recipient that registered.
            <para>Also, if TMessage is an interface, message types implementing TMessage will also be
            transmitted to the recipient. For example, if a SendOrderMessage
            and an ExecuteOrderMessage implement IOrderMessage, registering for IOrderMessage
            and setting receiveDerivedMessagesToo to true will send SendOrderMessage
            and ExecuteOrderMessage to the recipient that registered.</para>
            </param>
            <param name="action">The action that will be executed when a message
            of type TMessage is sent.</param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Register``1(System.Object,System.Boolean,System.Action{``0},System.Boolean)">
            <summary>
            Registers a recipient for a type of message TMessage.
            The action parameter will be executed when a corresponding 
            message is sent. See the receiveDerivedMessagesToo parameter
            for details on how messages deriving from TMessage (or, if TMessage is an interface,
            messages implementing TMessage) can be received too.
            <para>Registering a recipient does not create a hard reference to it,
            so if this recipient is deleted, no memory leak is caused.</para>
            </summary>
            <typeparam name="TMessage">The type of message that the recipient registers
            for.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="receiveDerivedMessagesToo">If true, message types deriving from
            TMessage will also be transmitted to the recipient. For example, if a SendOrderMessage
            and an ExecuteOrderMessage derive from OrderMessage, registering for OrderMessage
            and setting receiveDerivedMessagesToo to true will send SendOrderMessage
            and ExecuteOrderMessage to the recipient that registered.
            <para>Also, if TMessage is an interface, message types implementing TMessage will also be
            transmitted to the recipient. For example, if a SendOrderMessage
            and an ExecuteOrderMessage implement IOrderMessage, registering for IOrderMessage
            and setting receiveDerivedMessagesToo to true will send SendOrderMessage
            and ExecuteOrderMessage to the recipient that registered.</para>
            </param>
            <param name="action">The action that will be executed when a message
            of type TMessage is sent.</param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Send``1(``0)">
            <summary>
            Sends a message to registered recipients. The message will
            reach all recipients that registered for this message type
            using one of the Register methods.
            </summary>
            <typeparam name="TMessage">The type of message that will be sent.</typeparam>
            <param name="message">The message to send to registered recipients.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Send``2(``0)">
            <summary>
            Sends a message to registered recipients. The message will
            reach only recipients that registered for this message type
            using one of the Register methods, and that are
            of the targetType.
            </summary>
            <typeparam name="TMessage">The type of message that will be sent.</typeparam>
            <typeparam name="TTarget">The type of recipients that will receive
            the message. The message won't be sent to recipients of another type.</typeparam>
            <param name="message">The message to send to registered recipients.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Send``1(``0,System.Object)">
            <summary>
            Sends a message to registered recipients. The message will
            reach only recipients that registered for this message type
            using one of the Register methods, and that are
            of the targetType.
            </summary>
            <typeparam name="TMessage">The type of message that will be sent.</typeparam>
            <param name="message">The message to send to registered recipients.</param>
            <param name="token">A token for a messaging channel. If a recipient registers
            using a token, and a sender sends a message using the same token, then this
            message will be delivered to the recipient. Other recipients who did not
            use a token when registering (or who used a different token) will not
            get the message. Similarly, messages sent without any token, or with a different
            token, will not be delivered to that recipient.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Unregister(System.Object)">
            <summary>
            Unregisters a messager recipient completely. After this method
            is executed, the recipient will not receive any messages anymore.
            </summary>
            <param name="recipient">The recipient that must be unregistered.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Unregister``1(System.Object)">
            <summary>
            Unregisters a message recipient for a given type of messages only. 
            After this method is executed, the recipient will not receive messages
            of type TMessage anymore, but will still receive other message types (if it
            registered for them previously).
            </summary>
            <typeparam name="TMessage">The type of messages that the recipient wants
            to unregister from.</typeparam>
            <param name="recipient">The recipient that must be unregistered.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Unregister``1(System.Object,System.Object)">
            <summary>
            Unregisters a message recipient for a given type of messages only and for a given token. 
            After this method is executed, the recipient will not receive messages
            of type TMessage anymore with the given token, but will still receive other message types
            or messages with other tokens (if it registered for them previously).
            </summary>
            <param name="recipient">The recipient that must be unregistered.</param>
            <param name="token">The token for which the recipient must be unregistered.</param>
            <typeparam name="TMessage">The type of messages that the recipient wants
            to unregister from.</typeparam>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Unregister``1(System.Object,System.Action{``0})">
            <summary>
            Unregisters a message recipient for a given type of messages and for
            a given action. Other message types will still be transmitted to the
            recipient (if it registered for them previously). Other actions that have
            been registered for the message type TMessage and for the given recipient (if
            available) will also remain available.
            </summary>
            <typeparam name="TMessage">The type of messages that the recipient wants
            to unregister from.</typeparam>
            <param name="recipient">The recipient that must be unregistered.</param>
            <param name="action">The action that must be unregistered for
            the recipient and for the message type TMessage.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.IMessenger.Unregister``1(System.Object,System.Object,System.Action{``0})">
            <summary>
            Unregisters a message recipient for a given type of messages, for
            a given action and a given token. Other message types will still be transmitted to the
            recipient (if it registered for them previously). Other actions that have
            been registered for the message type TMessage, for the given recipient and other tokens (if
            available) will also remain available.
            </summary>
            <typeparam name="TMessage">The type of messages that the recipient wants
            to unregister from.</typeparam>
            <param name="recipient">The recipient that must be unregistered.</param>
            <param name="token">The token for which the recipient must be unregistered.</param>
            <param name="action">The action that must be unregistered for
            the recipient and for the message type TMessage.</param>
        </member>
        <member name="T:GalaSoft.MvvmLight.Messaging.MessageBase">
            <summary>
            Base class for all messages broadcasted by the Messenger.
            You can create your own message types by extending this class.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.MessageBase.#ctor">
            <summary>
            Initializes a new instance of the MessageBase class.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.MessageBase.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the MessageBase class.
            </summary>
            <param name="sender">The message's original sender.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.MessageBase.#ctor(System.Object,System.Object)">
            <summary>
            Initializes a new instance of the MessageBase class.
            </summary>
            <param name="sender">The message's original sender.</param>
            <param name="target">The message's intended target. This parameter can be used
            to give an indication as to whom the message was intended for. Of course
            this is only an indication, amd may be null.</param>
        </member>
        <member name="P:GalaSoft.MvvmLight.Messaging.MessageBase.Sender">
            <summary>
            Gets or sets the message's sender.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Messaging.MessageBase.Target">
            <summary>
            Gets or sets the message's intended target. This property can be used
            to give an indication as to whom the message was intended for. Of course
            this is only an indication, amd may be null.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Messaging.Messenger">
            <summary>
            The Messenger is a class allowing objects to exchange messages.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Messaging.Messenger.Default">
            <summary>
            Gets the Messenger's default instance, allowing
            to register and send messages in a static manner.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Register``1(System.Object,System.Action{``0},System.Boolean)">
            <summary>
            Registers a recipient for a type of message TMessage. The action
            parameter will be executed when a corresponding message is sent.
            <para>Registering a recipient does not create a hard reference to it,
            so if this recipient is deleted, no memory leak is caused.</para>
            </summary>
            <typeparam name="TMessage">The type of message that the recipient registers
            for.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="action">The action that will be executed when a message
            of type TMessage is sent. IMPORTANT: If the action causes a closure,
            you must set keepTargetAlive to true to avoid side effects. </param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Register``1(System.Object,System.Object,System.Action{``0},System.Boolean)">
            <summary>
            Registers a recipient for a type of message TMessage.
            The action parameter will be executed when a corresponding 
            message is sent. See the receiveDerivedMessagesToo parameter
            for details on how messages deriving from TMessage (or, if TMessage is an interface,
            messages implementing TMessage) can be received too.
            <para>Registering a recipient does not create a hard reference to it,
            so if this recipient is deleted, no memory leak is caused.</para>
            <para>However if you use closures and set keepTargetAlive to true, you might
            cause a memory leak if you don't call <see cref="M:GalaSoft.MvvmLight.Messaging.Messenger.Unregister(System.Object)"/> when you are cleaning up.</para>
            </summary>
            <typeparam name="TMessage">The type of message that the recipient registers
            for.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="token">A token for a messaging channel. If a recipient registers
            using a token, and a sender sends a message using the same token, then this
            message will be delivered to the recipient. Other recipients who did not
            use a token when registering (or who used a different token) will not
            get the message. Similarly, messages sent without any token, or with a different
            token, will not be delivered to that recipient.</param>
            <param name="action">The action that will be executed when a message
            of type TMessage is sent. IMPORTANT: If the action causes a closure,
            you must set keepTargetAlive to true to avoid side effects. </param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Register``1(System.Object,System.Object,System.Boolean,System.Action{``0},System.Boolean)">
            <summary>
            Registers a recipient for a type of message TMessage.
            The action parameter will be executed when a corresponding 
            message is sent. See the receiveDerivedMessagesToo parameter
            for details on how messages deriving from TMessage (or, if TMessage is an interface,
            messages implementing TMessage) can be received too.
            <para>Registering a recipient does not create a hard reference to it,
            so if this recipient is deleted, no memory leak is caused.</para>
            </summary>
            <typeparam name="TMessage">The type of message that the recipient registers
            for.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="token">A token for a messaging channel. If a recipient registers
            using a token, and a sender sends a message using the same token, then this
            message will be delivered to the recipient. Other recipients who did not
            use a token when registering (or who used a different token) will not
            get the message. Similarly, messages sent without any token, or with a different
            token, will not be delivered to that recipient.</param>
            <param name="receiveDerivedMessagesToo">If true, message types deriving from
            TMessage will also be transmitted to the recipient. For example, if a SendOrderMessage
            and an ExecuteOrderMessage derive from OrderMessage, registering for OrderMessage
            and setting receiveDerivedMessagesToo to true will send SendOrderMessage
            and ExecuteOrderMessage to the recipient that registered.
            <para>Also, if TMessage is an interface, message types implementing TMessage will also be
            transmitted to the recipient. For example, if a SendOrderMessage
            and an ExecuteOrderMessage implement IOrderMessage, registering for IOrderMessage
            and setting receiveDerivedMessagesToo to true will send SendOrderMessage
            and ExecuteOrderMessage to the recipient that registered.</para>
            </param>
            <param name="action">The action that will be executed when a message
            of type TMessage is sent. IMPORTANT: If the action causes a closure,
            you must set keepTargetAlive to true to avoid side effects. </param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Register``1(System.Object,System.Boolean,System.Action{``0},System.Boolean)">
            <summary>
            Registers a recipient for a type of message TMessage.
            The action parameter will be executed when a corresponding 
            message is sent. See the receiveDerivedMessagesToo parameter
            for details on how messages deriving from TMessage (or, if TMessage is an interface,
            messages implementing TMessage) can be received too.
            <para>Registering a recipient does not create a hard reference to it,
            so if this recipient is deleted, no memory leak is caused.</para>
            </summary>
            <typeparam name="TMessage">The type of message that the recipient registers
            for.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="receiveDerivedMessagesToo">If true, message types deriving from
            TMessage will also be transmitted to the recipient. For example, if a SendOrderMessage
            and an ExecuteOrderMessage derive from OrderMessage, registering for OrderMessage
            and setting receiveDerivedMessagesToo to true will send SendOrderMessage
            and ExecuteOrderMessage to the recipient that registered.
            <para>Also, if TMessage is an interface, message types implementing TMessage will also be
            transmitted to the recipient. For example, if a SendOrderMessage
            and an ExecuteOrderMessage implement IOrderMessage, registering for IOrderMessage
            and setting receiveDerivedMessagesToo to true will send SendOrderMessage
            and ExecuteOrderMessage to the recipient that registered.</para>
            </param>
            <param name="action">The action that will be executed when a message
            of type TMessage is sent. IMPORTANT: If the action causes a closure,
            you must set keepTargetAlive to true to avoid side effects. </param>
            <param name="keepTargetAlive">If true, the target of the Action will
            be kept as a hard reference, which might cause a memory leak. You should only set this
            parameter to true if the action is using closures. See
            http://galasoft.ch/s/mvvmweakaction. </param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Send``1(``0)">
            <summary>
            Sends a message to registered recipients. The message will
            reach all recipients that registered for this message type
            using one of the Register methods.
            </summary>
            <typeparam name="TMessage">The type of message that will be sent.</typeparam>
            <param name="message">The message to send to registered recipients.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Send``2(``0)">
            <summary>
            Sends a message to registered recipients. The message will
            reach only recipients that registered for this message type
            using one of the Register methods, and that are
            of the targetType.
            </summary>
            <typeparam name="TMessage">The type of message that will be sent.</typeparam>
            <typeparam name="TTarget">The type of recipients that will receive
            the message. The message won't be sent to recipients of another type.</typeparam>
            <param name="message">The message to send to registered recipients.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Send``1(``0,System.Object)">
            <summary>
            Sends a message to registered recipients. The message will
            reach only recipients that registered for this message type
            using one of the Register methods, and that are
            of the targetType.
            </summary>
            <typeparam name="TMessage">The type of message that will be sent.</typeparam>
            <param name="message">The message to send to registered recipients.</param>
            <param name="token">A token for a messaging channel. If a recipient registers
            using a token, and a sender sends a message using the same token, then this
            message will be delivered to the recipient. Other recipients who did not
            use a token when registering (or who used a different token) will not
            get the message. Similarly, messages sent without any token, or with a different
            token, will not be delivered to that recipient.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Unregister(System.Object)">
            <summary>
            Unregisters a messager recipient completely. After this method
            is executed, the recipient will not receive any messages anymore.
            </summary>
            <param name="recipient">The recipient that must be unregistered.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Unregister``1(System.Object)">
            <summary>
            Unregisters a message recipient for a given type of messages only. 
            After this method is executed, the recipient will not receive messages
            of type TMessage anymore, but will still receive other message types (if it
            registered for them previously).
            </summary>
            <param name="recipient">The recipient that must be unregistered.</param>
            <typeparam name="TMessage">The type of messages that the recipient wants
            to unregister from.</typeparam>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Unregister``1(System.Object,System.Object)">
            <summary>
            Unregisters a message recipient for a given type of messages only and for a given token. 
            After this method is executed, the recipient will not receive messages
            of type TMessage anymore with the given token, but will still receive other message types
            or messages with other tokens (if it registered for them previously).
            </summary>
            <param name="recipient">The recipient that must be unregistered.</param>
            <param name="token">The token for which the recipient must be unregistered.</param>
            <typeparam name="TMessage">The type of messages that the recipient wants
            to unregister from.</typeparam>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Unregister``1(System.Object,System.Action{``0})">
            <summary>
            Unregisters a message recipient for a given type of messages and for
            a given action. Other message types will still be transmitted to the
            recipient (if it registered for them previously). Other actions that have
            been registered for the message type TMessage and for the given recipient (if
            available) will also remain available.
            </summary>
            <typeparam name="TMessage">The type of messages that the recipient wants
            to unregister from.</typeparam>
            <param name="recipient">The recipient that must be unregistered.</param>
            <param name="action">The action that must be unregistered for
            the recipient and for the message type TMessage.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Unregister``1(System.Object,System.Object,System.Action{``0})">
            <summary>
            Unregisters a message recipient for a given type of messages, for
            a given action and a given token. Other message types will still be transmitted to the
            recipient (if it registered for them previously). Other actions that have
            been registered for the message type TMessage, for the given recipient and other tokens (if
            available) will also remain available.
            </summary>
            <typeparam name="TMessage">The type of messages that the recipient wants
            to unregister from.</typeparam>
            <param name="recipient">The recipient that must be unregistered.</param>
            <param name="token">The token for which the recipient must be unregistered.</param>
            <param name="action">The action that must be unregistered for
            the recipient and for the message type TMessage.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.OverrideDefault(GalaSoft.MvvmLight.Messaging.IMessenger)">
            <summary>
            Provides a way to override the Messenger.Default instance with
            a custom instance, for example for unit testing purposes.
            </summary>
            <param name="newMessenger">The instance that will be used as Messenger.Default.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Reset">
            <summary>
            Sets the Messenger's default (static) instance to null.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.ResetAll">
            <summary>
            Provides a non-static access to the static <see cref="M:GalaSoft.MvvmLight.Messaging.Messenger.Reset"/> method.
            Sets the Messenger's default (static) instance to null.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.RequestCleanup">
            <summary>
            Notifies the Messenger that the lists of recipients should
            be scanned and cleaned up.
            Since recipients are stored as <see cref="T:System.WeakReference"/>, 
            recipients can be garbage collected even though the Messenger keeps 
            them in a list. During the cleanup operation, all "dead"
            recipients are removed from the lists. Since this operation
            can take a moment, it is only executed when the application is
            idle. For this reason, a user of the Messenger class should use
            <see cref="M:GalaSoft.MvvmLight.Messaging.Messenger.RequestCleanup"/> instead of forcing one with the 
            <see cref="M:GalaSoft.MvvmLight.Messaging.Messenger.Cleanup" /> method.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.Messenger.Cleanup">
            <summary>
            Scans the recipients' lists for "dead" instances and removes them.
            Since recipients are stored as <see cref="T:System.WeakReference"/>, 
            recipients can be garbage collected even though the Messenger keeps 
            them in a list. During the cleanup operation, all "dead"
            recipients are removed from the lists. Since this operation
            can take a moment, it is only executed when the application is
            idle. For this reason, a user of the Messenger class should use
            <see cref="M:GalaSoft.MvvmLight.Messaging.Messenger.RequestCleanup"/> instead of forcing one with the 
            <see cref="M:GalaSoft.MvvmLight.Messaging.Messenger.Cleanup" /> method.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Messaging.NotificationMessage">
            <summary>
            Passes a string message (Notification) to a recipient.
            <para>Typically, notifications are defined as unique strings in a static class. To define
            a unique string, you can use Guid.NewGuid().ToString() or any other unique
            identifier.</para>
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessage.#ctor(System.String)">
            <summary>
            Initializes a new instance of the NotificationMessage class.
            </summary>
            <param name="notification">A string containing any arbitrary message to be
            passed to recipient(s)</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessage.#ctor(System.Object,System.String)">
            <summary>
            Initializes a new instance of the NotificationMessage class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="notification">A string containing any arbitrary message to be
            passed to recipient(s)</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessage.#ctor(System.Object,System.Object,System.String)">
            <summary>
            Initializes a new instance of the NotificationMessage class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="target">The message's intended target. This parameter can be used
            to give an indication as to whom the message was intended for. Of course
            this is only an indication, amd may be null.</param>
            <param name="notification">A string containing any arbitrary message to be
            passed to recipient(s)</param>
        </member>
        <member name="P:GalaSoft.MvvmLight.Messaging.NotificationMessage.Notification">
            <summary>
            Gets a string containing any arbitrary message to be
            passed to recipient(s).
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Messaging.NotificationMessageAction">
            <summary>
            Provides a message class with a built-in callback. When the recipient
            is done processing the message, it can execute the callback to
            notify the sender that it is done. Use the <see cref="M:GalaSoft.MvvmLight.Messaging.NotificationMessageAction.Execute" />
            method to execute the callback.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageAction.#ctor(System.String,System.Action)">
            <summary>
            Initializes a new instance of the
            <see cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageAction" /> class.
            </summary>
            <param name="notification">An arbitrary string that will be
            carried by the message.</param>
            <param name="callback">The callback method that can be executed
            by the recipient to notify the sender that the message has been
            processed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageAction.#ctor(System.Object,System.String,System.Action)">
            <summary>
            Initializes a new instance of the
            <see cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageAction" /> class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="notification">An arbitrary string that will be
            carried by the message.</param>
            <param name="callback">The callback method that can be executed
            by the recipient to notify the sender that the message has been
            processed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageAction.#ctor(System.Object,System.Object,System.String,System.Action)">
            <summary>
            Initializes a new instance of the
            <see cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageAction" /> class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="target">The message's intended target. This parameter can be used
            to give an indication as to whom the message was intended for. Of course
            this is only an indication, amd may be null.</param>
            <param name="notification">An arbitrary string that will be
            carried by the message.</param>
            <param name="callback">The callback method that can be executed
            by the recipient to notify the sender that the message has been
            processed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageAction.Execute">
            <summary>
            Executes the callback that was provided with the message.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Messaging.NotificationMessageAction`1">
            <summary>
            Provides a message class with a built-in callback. When the recipient
            is done processing the message, it can execute the callback to
            notify the sender that it is done. Use the <see cref="M:GalaSoft.MvvmLight.Messaging.NotificationMessageAction`1.Execute(`0)" />
            method to execute the callback. The callback method has one parameter.
            <seealso cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageAction"/>.
            </summary>
            <typeparam name="TCallbackParameter">The type of the callback method's
            only parameter.</typeparam>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageAction`1.#ctor(System.String,System.Action{`0})">
            <summary>
            Initializes a new instance of the
            <see cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageAction`1" /> class.
            </summary>
            <param name="notification">An arbitrary string that will be
            carried by the message.</param>
            <param name="callback">The callback method that can be executed
            by the recipient to notify the sender that the message has been
            processed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageAction`1.#ctor(System.Object,System.String,System.Action{`0})">
            <summary>
            Initializes a new instance of the
            <see cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageAction`1" /> class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="notification">An arbitrary string that will be
            carried by the message.</param>
            <param name="callback">The callback method that can be executed
            by the recipient to notify the sender that the message has been
            processed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageAction`1.#ctor(System.Object,System.Object,System.String,System.Action{`0})">
            <summary>
            Initializes a new instance of the
            <see cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageAction`1" /> class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="target">The message's intended target. This parameter can be used
            to give an indication as to whom the message was intended for. Of course
            this is only an indication, amd may be null.</param>
            <param name="notification">An arbitrary string that will be
            carried by the message.</param>
            <param name="callback">The callback method that can be executed
            by the recipient to notify the sender that the message has been
            processed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageAction`1.Execute(`0)">
            <summary>
            Executes the callback that was provided with the message.
            </summary>
            <param name="parameter">A parameter requested by the message's
            sender and providing additional information on the recipient's
            state.</param>
        </member>
        <member name="T:GalaSoft.MvvmLight.Messaging.NotificationMessage`1">
            <summary>
            Passes a string message (Notification) and a generic value (Content) to a recipient.
            </summary>
            <typeparam name="T">The type of the Content property.</typeparam>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessage`1.#ctor(`0,System.String)">
            <summary>
            Initializes a new instance of the NotificationMessage class.
            </summary>
            <param name="content">A value to be passed to recipient(s).</param>
            <param name="notification">A string containing any arbitrary message to be
            passed to recipient(s)</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessage`1.#ctor(System.Object,`0,System.String)">
            <summary>
            Initializes a new instance of the NotificationMessage class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="content">A value to be passed to recipient(s).</param>
            <param name="notification">A string containing any arbitrary message to be
            passed to recipient(s)</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessage`1.#ctor(System.Object,System.Object,`0,System.String)">
            <summary>
            Initializes a new instance of the NotificationMessage class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="target">The message's intended target. This parameter can be used
            to give an indication as to whom the message was intended for. Of course
            this is only an indication, amd may be null.</param>
            <param name="content">A value to be passed to recipient(s).</param>
            <param name="notification">A string containing any arbitrary message to be
            passed to recipient(s)</param>
        </member>
        <member name="P:GalaSoft.MvvmLight.Messaging.NotificationMessage`1.Notification">
            <summary>
            Gets a string containing any arbitrary message to be
            passed to recipient(s).
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Messaging.NotificationMessageWithCallback">
            <summary>
            Provides a message class with a built-in callback. When the recipient
            is done processing the message, it can execute the callback to
            notify the sender that it is done. Use the <see cref="M:GalaSoft.MvvmLight.Messaging.NotificationMessageWithCallback.Execute(System.Object[])" />
            method to execute the callback. The callback method has one parameter.
            <seealso cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageAction"/> and
            <seealso cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageAction`1"/>.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageWithCallback.#ctor(System.String,System.Delegate)">
            <summary>
            Initializes a new instance of the <see cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageWithCallback" /> class.
            </summary>
            <param name="notification">An arbitrary string that will be
            carried by the message.</param>
            <param name="callback">The callback method that can be executed
            by the recipient to notify the sender that the message has been
            processed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageWithCallback.#ctor(System.Object,System.String,System.Delegate)">
            <summary>
            Initializes a new instance of the <see cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageWithCallback" /> class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="notification">An arbitrary string that will be
            carried by the message.</param>
            <param name="callback">The callback method that can be executed
            by the recipient to notify the sender that the message has been
            processed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageWithCallback.#ctor(System.Object,System.Object,System.String,System.Delegate)">
            <summary>
            Initializes a new instance of the <see cref="T:GalaSoft.MvvmLight.Messaging.NotificationMessageWithCallback" /> class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="target">The message's intended target. This parameter can be used
            to give an indication as to whom the message was intended for. Of course
            this is only an indication, amd may be null.</param>
            <param name="notification">An arbitrary string that will be
            carried by the message.</param>
            <param name="callback">The callback method that can be executed
            by the recipient to notify the sender that the message has been
            processed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.NotificationMessageWithCallback.Execute(System.Object[])">
            <summary>
            Executes the callback that was provided with the message with an
            arbitrary number of parameters.
            </summary>
            <param name="arguments">A  number of parameters that will
            be passed to the callback method.</param>
            <returns>The object returned by the callback method.</returns>
        </member>
        <member name="T:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1">
            <summary>
            Passes a string property name (PropertyName) and a generic value
            (<see cref="P:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1.OldValue" /> and <see cref="P:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1.NewValue" />) to a recipient.
            This message type can be used to propagate a PropertyChanged event to
            a recipient using the messenging system.
            </summary>
            <typeparam name="T">The type of the OldValue and NewValue property.</typeparam>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1.#ctor(System.Object,`0,`0,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1" /> class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="oldValue">The property's value before the change occurred.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="propertyName">The name of the property that changed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1.#ctor(`0,`0,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1" /> class.
            </summary>
            <param name="oldValue">The property's value before the change occurred.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="propertyName">The name of the property that changed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1.#ctor(System.Object,System.Object,`0,`0,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1" /> class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="target">The message's intended target. This parameter can be used
            to give an indication as to whom the message was intended for. Of course
            this is only an indication, amd may be null.</param>
            <param name="oldValue">The property's value before the change occurred.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="propertyName">The name of the property that changed.</param>
        </member>
        <member name="P:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1.NewValue">
            <summary>
            Gets the value that the property has after the change.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1.OldValue">
            <summary>
            Gets the value that the property had before the change.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.Messaging.PropertyChangedMessageBase">
            <summary>
            Basis class for the <see cref="T:GalaSoft.MvvmLight.Messaging.PropertyChangedMessage`1" /> class. This
            class allows a recipient to register for all PropertyChangedMessages without
            having to specify the type T.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.PropertyChangedMessageBase.#ctor(System.Object,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:GalaSoft.MvvmLight.Messaging.PropertyChangedMessageBase" /> class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="propertyName">The name of the property that changed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.PropertyChangedMessageBase.#ctor(System.Object,System.Object,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:GalaSoft.MvvmLight.Messaging.PropertyChangedMessageBase" /> class.
            </summary>
            <param name="sender">The message's sender.</param>
            <param name="target">The message's intended target. This parameter can be used
            to give an indication as to whom the message was intended for. Of course
            this is only an indication, amd may be null.</param>
            <param name="propertyName">The name of the property that changed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Messaging.PropertyChangedMessageBase.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:GalaSoft.MvvmLight.Messaging.PropertyChangedMessageBase" /> class.
            </summary>
            <param name="propertyName">The name of the property that changed.</param>
        </member>
        <member name="P:GalaSoft.MvvmLight.Messaging.PropertyChangedMessageBase.PropertyName">
            <summary>
            Gets or sets the name of the property that changed.
            </summary>
        </member>
        <member name="T:GalaSoft.MvvmLight.ObservableObject">
            <summary>
            A base class for objects of which the properties must be observable.
            </summary>
        </member>
        <member name="E:GalaSoft.MvvmLight.ObservableObject.PropertyChanged">
            <summary>
            Occurs after a property value changes.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.ObservableObject.PropertyChangedHandler">
            <summary>
            Provides access to the PropertyChanged event handler to derived classes.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.ObservableObject.VerifyPropertyName(System.String)">
            <summary>
            Verifies that a property name exists in this ViewModel. This method
            can be called before the property is used, for instance before
            calling RaisePropertyChanged. It avoids errors when a property name
            is changed but some places are missed.
            </summary>
            <remarks>This method is only active in DEBUG mode.</remarks>
            <param name="propertyName">The name of the property that will be
            checked.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.ObservableObject.RaisePropertyChanged(System.String)">
            <summary>
            Raises the PropertyChanged event if needed.
            </summary>
            <remarks>If the propertyName parameter
            does not correspond to an existing property on the current class, an
            exception is thrown in DEBUG configuration only.</remarks>
            <param name="propertyName">(optional) The name of the property that
            changed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.ObservableObject.RaisePropertyChanged``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Raises the PropertyChanged event if needed.
            </summary>
            <typeparam name="T">The type of the property that
            changed.</typeparam>
            <param name="propertyExpression">An expression identifying the property
            that changed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.ObservableObject.GetPropertyName``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Extracts the name of a property from an expression.
            </summary>
            <typeparam name="T">The type of the property.</typeparam>
            <param name="propertyExpression">An expression returning the property's name.</param>
            <returns>The name of the property returned by the expression.</returns>
            <exception cref="T:System.ArgumentNullException">If the expression is null.</exception>
            <exception cref="T:System.ArgumentException">If the expression does not represent a property.</exception>
        </member>
        <member name="M:GalaSoft.MvvmLight.ObservableObject.Set``1(System.Linq.Expressions.Expression{System.Func{``0}},``0@,``0)">
            <summary>
            Assigns a new value to the property. Then, raises the
            PropertyChanged event if needed. 
            </summary>
            <typeparam name="T">The type of the property that
            changed.</typeparam>
            <param name="propertyExpression">An expression identifying the property
            that changed.</param>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change
            occurred.</param>
            <returns>True if the PropertyChanged event has been raised,
            false otherwise. The event is not raised if the old
            value is equal to the new value.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.ObservableObject.Set``1(System.String,``0@,``0)">
            <summary>
            Assigns a new value to the property. Then, raises the
            PropertyChanged event if needed. 
            </summary>
            <typeparam name="T">The type of the property that
            changed.</typeparam>
            <param name="propertyName">The name of the property that
            changed.</param>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change
            occurred.</param>
            <returns>True if the PropertyChanged event has been raised,
            false otherwise. The event is not raised if the old
            value is equal to the new value.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.ObservableObject.Set``1(``0@,``0,System.String)">
            <summary>
            Assigns a new value to the property. Then, raises the
            PropertyChanged event if needed. 
            </summary>
            <typeparam name="T">The type of the property that
            changed.</typeparam>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change
            occurred.</param>
            <param name="propertyName">(optional) The name of the property that
            changed.</param>
            <returns>True if the PropertyChanged event has been raised,
            false otherwise. The event is not raised if the old
            value is equal to the new value.</returns>
        </member>
        <member name="T:GalaSoft.MvvmLight.ViewModelBase">
            <summary>
            A base class for the ViewModel classes in the MVVM pattern.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.ViewModelBase.#ctor">
            <summary>
            Initializes a new instance of the ViewModelBase class.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.ViewModelBase.#ctor(GalaSoft.MvvmLight.Messaging.IMessenger)">
            <summary>
            Initializes a new instance of the ViewModelBase class.
            </summary>
            <param name="messenger">An instance of a <see cref="T:GalaSoft.MvvmLight.Messaging.Messenger" />
            used to broadcast messages to other objects. If null, this class
            will attempt to broadcast using the Messenger's default
            instance.</param>
        </member>
        <member name="P:GalaSoft.MvvmLight.ViewModelBase.IsInDesignMode">
            <summary>
            Gets a value indicating whether the control is in design mode
            (running under Blend or Visual Studio).
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.ViewModelBase.IsInDesignModeStatic">
            <summary>
            Gets a value indicating whether the control is in design mode
            (running in Blend or Visual Studio).
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.ViewModelBase.MessengerInstance">
            <summary>
            Gets or sets an instance of a <see cref="T:GalaSoft.MvvmLight.Messaging.IMessenger" /> used to
            broadcast messages to other objects. If null, this class will
            attempt to broadcast using the Messenger's default instance.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.ViewModelBase.Cleanup">
            <summary>
            Unregisters this instance from the Messenger class.
            <para>To cleanup additional resources, override this method, clean
            up and then call base.Cleanup().</para>
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.ViewModelBase.Broadcast``1(``0,``0,System.String)">
            <summary>
            Broadcasts a PropertyChangedMessage using either the instance of
            the Messenger that was passed to this class (if available) 
            or the Messenger's default instance.
            </summary>
            <typeparam name="T">The type of the property that
            changed.</typeparam>
            <param name="oldValue">The value of the property before it
            changed.</param>
            <param name="newValue">The value of the property after it
            changed.</param>
            <param name="propertyName">The name of the property that
            changed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.ViewModelBase.RaisePropertyChanged``1(System.String,``0,``0,System.Boolean)">
            <summary>
            Raises the PropertyChanged event if needed, and broadcasts a
            PropertyChangedMessage using the Messenger instance (or the
            static default instance if no Messenger instance is available).
            </summary>
            <typeparam name="T">The type of the property that
            changed.</typeparam>
            <param name="propertyName">The name of the property that
            changed.</param>
            <param name="oldValue">The property's value before the change
            occurred.</param>
            <param name="newValue">The property's value after the change
            occurred.</param>
            <param name="broadcast">If true, a PropertyChangedMessage will
            be broadcasted. If false, only the event will be raised.</param>
            <remarks>If the propertyName parameter
            does not correspond to an existing property on the current class, an
            exception is thrown in DEBUG configuration only.</remarks>
        </member>
        <member name="M:GalaSoft.MvvmLight.ViewModelBase.RaisePropertyChanged``1(System.Linq.Expressions.Expression{System.Func{``0}},``0,``0,System.Boolean)">
            <summary>
            Raises the PropertyChanged event if needed, and broadcasts a
            PropertyChangedMessage using the Messenger instance (or the
            static default instance if no Messenger instance is available).
            </summary>
            <typeparam name="T">The type of the property that
            changed.</typeparam>
            <param name="propertyExpression">An expression identifying the property
            that changed.</param>
            <param name="oldValue">The property's value before the change
            occurred.</param>
            <param name="newValue">The property's value after the change
            occurred.</param>
            <param name="broadcast">If true, a PropertyChangedMessage will
            be broadcasted. If false, only the event will be raised.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.ViewModelBase.Set``1(System.Linq.Expressions.Expression{System.Func{``0}},``0@,``0,System.Boolean)">
            <summary>
            Assigns a new value to the property. Then, raises the
            PropertyChanged event if needed, and broadcasts a
            PropertyChangedMessage using the Messenger instance (or the
            static default instance if no Messenger instance is available). 
            </summary>
            <typeparam name="T">The type of the property that
            changed.</typeparam>
            <param name="propertyExpression">An expression identifying the property
            that changed.</param>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change
            occurred.</param>
            <param name="broadcast">If true, a PropertyChangedMessage will
            be broadcasted. If false, only the event will be raised.</param>
            <returns>True if the PropertyChanged event was raised, false otherwise.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.ViewModelBase.Set``1(System.String,``0@,``0,System.Boolean)">
            <summary>
            Assigns a new value to the property. Then, raises the
            PropertyChanged event if needed, and broadcasts a
            PropertyChangedMessage using the Messenger instance (or the
            static default instance if no Messenger instance is available). 
            </summary>
            <typeparam name="T">The type of the property that
            changed.</typeparam>
            <param name="propertyName">The name of the property that
            changed.</param>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change
            occurred.</param>
            <param name="broadcast">If true, a PropertyChangedMessage will
            be broadcasted. If false, only the event will be raised.</param>
            <returns>True if the PropertyChanged event was raised, false otherwise.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.ViewModelBase.Set``1(``0@,``0,System.Boolean,System.String)">
            <summary>
            Assigns a new value to the property. Then, raises the
            PropertyChanged event if needed, and broadcasts a
            PropertyChangedMessage using the Messenger instance (or the
            static default instance if no Messenger instance is available). 
            </summary>
            <typeparam name="T">The type of the property that
            changed.</typeparam>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change
            occurred.</param>
            <param name="broadcast">If true, a PropertyChangedMessage will
            be broadcasted. If false, only the event will be raised.</param>
            <param name="propertyName">(optional) The name of the property that
            changed.</param>
            <returns>True if the PropertyChanged event was raised, false otherwise.</returns>
        </member>
        <member name="T:GalaSoft.MvvmLight.Views.IDialogService">
            <summary>
            An interface defining how dialogs should
            be displayed in various frameworks such as Windows, 
            Windows Phone, Android, iOS etc.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.IDialogService.ShowError(System.String,System.String,System.String,System.Action)">
            <summary>
            Displays information about an error.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.IDialogService.ShowError(System.Exception,System.String,System.String,System.Action)">
            <summary>
            Displays information about an error.
            </summary>
            <param name="error">The exception of which the message must be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.IDialogService.ShowMessage(System.String,System.String)">
            <summary>
            Displays information to the user. The dialog box will have only
            one button with the text "OK".
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.IDialogService.ShowMessage(System.String,System.String,System.String,System.Action)">
            <summary>
            Displays information to the user. The dialog box will have only
            one button.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonText">The text shown in the only button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.IDialogService.ShowMessage(System.String,System.String,System.String,System.String,System.Action{System.Boolean})">
            <summary>
            Displays information to the user. The dialog box will have only
            one button.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <param name="buttonConfirmText">The text shown in the "confirm" button
            in the dialog box. If left null, the text "OK" will be used.</param>
            <param name="buttonCancelText">The text shown in the "cancel" button
            in the dialog box. If left null, the text "Cancel" will be used.</param>
            <param name="afterHideCallback">A callback that should be executed after
            the dialog box is closed by the user. The callback method will get a boolean
            parameter indicating if the "confirm" button (true) or the "cancel" button
            (false) was pressed by the user.</param>
            <returns>A Task allowing this async method to be awaited. The task will return
            true or false depending on the dialog result.</returns>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.IDialogService.ShowMessageBox(System.String,System.String)">
            <summary>
            Displays information to the user in a simple dialog box. The dialog box will have only
            one button with the text "OK". This method should be used for debugging purposes.
            </summary>
            <param name="message">The message to be shown to the user.</param>
            <param name="title">The title of the dialog box. This may be null.</param>
            <returns>A Task allowing this async method to be awaited.</returns>
        </member>
        <member name="T:GalaSoft.MvvmLight.Views.INavigationService">
            <summary>
            An interface defining how navigation between pages should
            be performed in various frameworks such as Windows, 
            Windows Phone, Android, iOS etc.
            </summary>
        </member>
        <member name="P:GalaSoft.MvvmLight.Views.INavigationService.CurrentPageKey">
            <summary>
            The key corresponding to the currently displayed page.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.INavigationService.GoBack">
            <summary>
            If possible, instructs the navigation service
            to discard the current page and display the previous page
            on the navigation stack.
            </summary>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.INavigationService.NavigateTo(System.String)">
            <summary>
            Instructs the navigation service to display a new page
            corresponding to the given key. Depending on the platforms,
            the navigation service might have to be configured with a
            key/page list.
            </summary>
            <param name="pageKey">The key corresponding to the page
            that should be displayed.</param>
        </member>
        <member name="M:GalaSoft.MvvmLight.Views.INavigationService.NavigateTo(System.String,System.Object)">
            <summary>
            Instructs the navigation service to display a new page
            corresponding to the given key, and passes a parameter
            to the new page.
            Depending on the platforms, the navigation service might 
            have to be Configure with a key/page list.
            </summary>
            <param name="pageKey">The key corresponding to the page
            that should be displayed.</param>
            <param name="parameter">The parameter that should be passed
            to the new page.</param>
        </member>
    </members>
</doc>
