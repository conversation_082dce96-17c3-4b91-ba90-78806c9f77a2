private static string Address = "C:\\Users\\<USER>\\AppData\\Local";
public static string Ini = Address + "\\Config\\InitialConfig.ini";
public static string Manual = Address + "\\Config\\Manual.pdf";
public static string Parameter = Address + "\\Config\\";
public static string Monitor = Address + "\\Config\\Monitor.xlsx";
public static string HardwareExplanation = Address + "\\Config\\HardwareExplanation.xlsx";
public static string SoftwareErrorLog = Address + "\\Config\\SoftwareErrorLog.xlsx";
public static string MotorLibraryLog = Address + "\\Config\\MotorLibraryLog.xlsx";
public static string MotorLibrary = Address + "\\MotorLibrary\\";

放到LocalAppDataFolder中

第6步 SingleImage->setup.exe-> InstallShield Prerequisites Location 属性 Extract From Setup.exe