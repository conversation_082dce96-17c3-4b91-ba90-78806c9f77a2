﻿<UserControl x:Class="ServoStudio.Views.ProgramJogView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"  
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             mc:Ignorable="d"
             xmlns:converter="clr-namespace:Converter"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:ProgramJogViewModel}">
    <UserControl.Resources>
        <converter:BackgroundConverter x:Key="ColorConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>  
    </UserControl.Resources>

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding ProgramJogLoadedCommand}" EventName="Loaded"/>
        <dxmvvm:EventToCommand Command="{Binding ProgramJogUnloadedCommand}" EventName="Unloaded"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                <Label Margin="6,0" Content="程序JOG模式" Style="{StaticResource LabelStyle}"/>
                <dxe:ComboBoxEdit Margin="0,0,6,0" Style="{StaticResource SettingComboBoxStyle}" Width="150" ItemsSource="{Binding ProgramJogSwitch}" SelectedIndex="{Binding SelectedProgramJogSwitchIndex,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>

                <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Separator_16x16.png}" Command="{Binding ProgramJogModelSwitchCommand}" Margin="0,0,6,0" Width="95">
                    <Label Content="{Binding ProgramJogSwitchHint}" Style="{StaticResource LabelStyle}" Margin="0" />
                </dx:SimpleButton>

                <dx:SimpleButton  Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Reset_16x16.png}" Margin="0,0,6,0" Width="95" IsEnabled="{Binding IsProgramJogButtonEnabled}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonDown" Command="{Binding ProgramJogRunCommand}"/>
                        <dxmvvm:EventToCommand EventName="MouseRightButtonUp" Command="{Binding ProgramJogStopCommand}"/>
                    </dxmvvm:Interaction.Behaviors>
                    <Label Content="电机转动" Style="{StaticResource LabelStyle}" Margin="0" />
                </dx:SimpleButton>
            </StackPanel>

            <Border Grid.Row="1" Margin="0" BorderThickness="0,0.7,0,0" BorderBrush="Orange"/>

            <Grid Grid.Row="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="10"/>

                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="180"/>
                    <ColumnDefinition Width="60"/>

                    <ColumnDefinition Width="50"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="180"/>
                    <ColumnDefinition Width="60"/>

                    <ColumnDefinition Width="10"/>
                </Grid.ColumnDefinitions>

                <dxe:ImageEdit Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="8"  Margin="6,10,0,0"  Height="Auto" Width="410" ShowMenu="False" Source="{Binding LegendAddress,UpdateSourceTrigger=PropertyChanged}" ShowBorder="False" Opacity="0.7" HorizontalAlignment="Left" VerticalAlignment="Center"/>

                <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="程序JOG移动距离" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding ProgramJogMovingDistance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding PositionUnit,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle_Unit}"/>

                <Label Grid.Row="1" Grid.Column="5" Margin="10,9" Content="程序JOG移动速度" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding ProgramJogMovingSpeed,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="1" Grid.Column="7" Text="rpm" Style="{StaticResource TextBoxStyle_Unit}"/>

                <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="程序JOG加减速时间" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding ProgramJogAccDecTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="2" Grid.Column="3" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"/>

                <Label Grid.Row="2" Grid.Column="5" Margin="10,9" Content="程序JOG等待时间" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding ProgramJogWaitTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="2" Grid.Column="7" Text="ms" Style="{StaticResource TextBoxStyle_Unit}"/>

                <Label Grid.Row="3" Grid.Column="1" Margin="10,9" Content="程序JOG移动次数" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Style="{StaticResource TextBoxStyle}" Margin="10,9" Text="{Binding ProgramJogMovingNumber,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                <TextBox Grid.Row="3" Grid.Column="3" Text="次" Style="{StaticResource TextBoxStyle_Unit}"/>
            </Grid>

            <Border Grid.Row="3" Margin="0" BorderThickness="0,0.7,0,0" BorderBrush="Orange"/>

            <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right">
                <Label Style="{StaticResource LabelStyle}" Content="提示：鼠标右键长按  [电机转动]  按钮，电机随即转动&#x0a;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;按键松开，电机停止" Margin="10,9" Foreground="Red" HorizontalContentAlignment="Right"/>
            </StackPanel>

        </Grid>

    </ScrollViewer>
        
</UserControl>
