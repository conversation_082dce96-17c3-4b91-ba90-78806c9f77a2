﻿<UserControl x:Class="ServoStudio.Views.FirmwareUpdateView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking" 
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:FirmwareUpdateViewModel}"
             d:DesignHeight="800" d:DesignWidth="1400">
    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding FirmwareUpdateLoadedCommand}"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Label Grid.Row="0" Grid.Column="0" Margin="3,10" Content="升级芯片选择" Style="{StaticResource LabelStyle}" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Label Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="3" Margin="10,5" Content="{Binding ARMCheckedHint}" Style="{StaticResource LabelStyle}" Foreground="Green"/>

                <Label Grid.Row="1" Grid.Column="1" Margin="10" Content="从站ID" Style="{StaticResource LabelStyle}"/>
                <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Margin="10" Width="200" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding SlaveID}" SelectedItem="{Binding SelectedSlaveID, Mode=TwoWay}"/>

                <!--<dxe:CheckEdit Grid.Row="1" Grid.Column="1" Margin="10,5" IsChecked="{Binding ARM1,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Content="升级从站1" Style="{StaticResource Jog}" IsEnabled="{Binding CheckBoxEnabled}"/>
            <dxe:CheckEdit Grid.Row="1" Grid.Column="2" Margin="10,5" IsChecked="{Binding ARM2,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Content="升级从站2" Style="{StaticResource Jog}" IsEnabled="{Binding CheckBoxEnabled}"/>
            <dxe:CheckEdit Grid.Row="1" Grid.Column="3" Margin="10,5" IsChecked="{Binding ARM3,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Content="升级从站3" Style="{StaticResource Jog}" IsEnabled="{Binding CheckBoxEnabled}"/>-->
            </Grid>

            <Label Grid.Row="2" Grid.Column="0" Margin="3,10" Content="升级状态" Style="{StaticResource LabelStyle}" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Grid Grid.Row="3">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="650"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="27"/>
                    <RowDefinition Height="222"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Label Grid.Row="0" Grid.Column="1" Margin="10,9" Content="导入文件路径" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="0" Grid.Column="2" Margin="10,9" Text="{Binding FileAddress,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource TextBoxStyle}" IsReadOnly="True"/>
                <Label Grid.Row="4" Grid.Column="1" Grid.ColumnSpan="3" Margin="10,0" Content="提示：固件升级完成后，伺服会自动重启，并请重启ServoStudio" Style="{StaticResource LabelStyle}" Foreground="Red"/>

                <Label Grid.Row="1" Grid.Column="1" Margin="10,-4,10,9" Content="当前升级信息" Style="{StaticResource LabelStyle}"/>
                <TextBox Name="TxtProgress" Grid.Row="1" Grid.RowSpan="2" Grid.Column="2" Margin="10,-4,10,9" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Visible" TextChanged="TxtProgress_TextChanged" BorderThickness="1" IsReadOnly="True"/>

                <Label Grid.Row="3" Grid.Column="1" Margin="10,-9,10,9" Content="当前升级进度" Style="{StaticResource LabelStyle}"/>
                <dxe:ProgressBarEdit Name="ProgressBar" Grid.Row="3" Grid.Column="2" Margin="10,-4,10,9" Height="25" Minimum="0" Maximum="100" ContentDisplayMode="Value" DisplayFormatString="{}{0}%"/>
            </Grid>

            <Label Grid.Row="4" Margin="3,10" Content="升级执行" Style="{StaticResource LabelStyle}" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <StackPanel Grid.Row="5" HorizontalAlignment="Right" Orientation="Horizontal">
                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Open_16x16.png}" Command="{Binding ImportFirmwareUpdateFileCommand}">
                    <Label Content="导入文件" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveUp_16x16.png}" Command="{Binding FirmwareUpdateStart_For_NameVerificationCommand}">
                    <Label Content="启动升级" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="pack://application:,,,/DevExpress.Images.v16.2;component/DevAV/Actions/Close_16x16.png" Command="{Binding FirmwareUpdateAbortCommand}">
                    <Label Content="终止升级" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>
            </StackPanel>
        </Grid>
    </ScrollViewer>
    
</UserControl>
