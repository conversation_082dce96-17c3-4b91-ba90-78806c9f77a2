﻿using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using ServoStudio.ViewModels;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Text;

namespace ServoStudio.Models
{
    public static class ParameterReadWriteModel
    {
        //*************************************************************************
        //函数名称：RetrieveDataTable
        //函数功能：参数表检索
        //
        //输入参数：string In_strCondition              
        //         ref ObservableCollection<ParameterReadWriteSet> Out_obsParameter
        //         
        //输出参数：-1：WRONG
        //          1: OK
        //          0: No_Effect
        //        
        //编码作者：Ryan
        //更新时间：2019.12.05
        //*************************************************************************
        public static int RetrieveDataTable(string In_strCondition, ref ObservableCollection<ParameterReadWriteSet> Out_obsParameter)
        {
            int iRet = -1;
            string strContent = null;
            DataTable clsDataTable = new DataTable();

            try
            {
                Out_obsParameter = new ObservableCollection<ParameterReadWriteSet>();

                strContent = "Classification = '" + In_strCondition + "'";

                iRet = OthersHelper.RetrieveDataTable(GlobalParameterSet.dt, strContent, ref clsDataTable);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                iRet = ConvertHelper.DataTableToObservableCollection(clsDataTable, ref Out_obsParameter);
            }
            catch (System.Exception ex)
            {
                iRet = RET.ERROR;
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_RETRIEVE_DATATABLE, "ParameterReadWrite.RetrieveDataTable", ex);
            }

            return iRet;
        }

        //*************************************************************************
        //函数名称：RetrieveDataTable
        //函数功能：参数表检索
        //
        //输入参数：string In_strCondition              
        //         ref ObservableCollection<ParameterReadWriteSet> Out_obsParameter
        //         
        //输出参数：-1：WRONG
        //          1: OK
        //          0: No_Effect
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public static int RetrieveDataTable_For_Compare(string In_strCondition, ref ObservableCollection<DiffParameterReadWriteSet> Out_obsParameter)
        {
            int iRet = -1;
            string strContent = null;
            DataTable clsDataTable = new DataTable();

            try
            {
                Out_obsParameter = new ObservableCollection<DiffParameterReadWriteSet>();

                strContent = "Classification = '" + In_strCondition + "'";

                iRet = OthersHelper.RetrieveDataTable_For_Compare(GlobalParameterSet.dt_Diff, strContent, ref clsDataTable);
                if (iRet != RET.SUCCEEDED)
                {
                    return iRet;
                }

                iRet = ConvertHelper.DataTableToObservableCollection_For_Compare(clsDataTable, ref Out_obsParameter);
            }
            catch (System.Exception ex)
            {
                iRet = RET.ERROR;
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_RETRIEVE_DATATABLE, "ParameterReadWrite.RetrieveDataTable", ex);
            }

            return iRet;
        }

        //*************************************************************************
        //函数名称：GetIndexAndDataType
        //函数功能：获取地址与数据类型-用于参数读取
        //
        //输入参数：ObservableCollection<ParameterReadWriteSet> Out_obsParameter 参数监视数据集
        //         ref List<TransmitingDataInfoSet> lstTransmittingDataInfo
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2019.12.18
        //*************************************************************************
        public static void GetIndexAndDataType(ObservableCollection<ParameterReadWriteSet> Out_obsParameter, ref List<TransmitingDataInfoSet> lstTransmittingDataInfo, bool IsCheckAddressDeviation)
        {
            lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            if (Out_obsParameter == null)
            {
                return;
            }

            foreach (ParameterReadWriteSet item in Out_obsParameter)
            {
                if (string.IsNullOrEmpty(item.Index))
                {
                    continue;
                }

                string strContent = null;
                string strDataType = item.DataType.ToUpper();
                string strAddress = item.Index.Replace("0x", "");

                if (IsCheckAddressDeviation && OthersHelper.SelectAxisNumber() == AxisNumber.B)
                {
                    strAddress = OthersHelper.AddressDeviation(strAddress);
                }

                if (string.IsNullOrEmpty(strAddress))
                {
                    continue;
                }

                if (strDataType == "INT32" || strDataType == "UINT32")
                {
                    strContent = strAddress + "20";
                }
                else if (strDataType == "INT16" || strDataType == "UINT16")
                {
                    strContent = strAddress + "10";
                }
                else if (strDataType == "INT8" || strDataType == "UINT8")
                {
                    strContent = strAddress + "08";
                }
                else if (strDataType == "STRING")
                {
                    strContent = strAddress + "00";
                }
                
                lstTransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = strContent, Address = strAddress, DataType = strDataType});
            }
        }

        //*************************************************************************
        //函数名称：GetIndexAndDataType_For_Compare
        //函数功能：获取地址与数据类型-用于参数读取
        //
        //输入参数：ObservableCollection<ParameterReadWriteSet> Out_obsParameter 参数监视数据集
        //         ref List<TransmitingDataInfoSet> lstTransmittingDataInfo
        //       
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2023.05.17
        //*************************************************************************
        public static void GetIndexAndDataType_For_Compare(ObservableCollection<DiffParameterReadWriteSet> Out_obsParameter, ref List<TransmitingDataInfoSet> lstTransmittingDataInfo, bool IsCheckAddressDeviation)
        {
            lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            if (Out_obsParameter == null)
            {
                return;
            }

            foreach (DiffParameterReadWriteSet item in Out_obsParameter)
            {
                if (string.IsNullOrEmpty(item.Index))
                {
                    continue;
                }

                string strContent = null;
                string strDataType = item.DataType.ToUpper();
                string strAddress = item.Index.Replace("0x", "");

                if (IsCheckAddressDeviation && OthersHelper.SelectAxisNumber() == AxisNumber.B)
                {
                    strAddress = OthersHelper.AddressDeviation(strAddress);
                }

                if (string.IsNullOrEmpty(strAddress))
                {
                    continue;
                }

                if (strDataType == "INT32" || strDataType == "UINT32")
                {
                    strContent = strAddress + "20";
                }
                else if (strDataType == "INT16" || strDataType == "UINT16")
                {
                    strContent = strAddress + "10";
                }
                else if (strDataType == "INT8" || strDataType == "UINT8")
                {
                    strContent = strAddress + "08";
                }
                else if (strDataType == "STRING")
                {
                    strContent = strAddress + "00";
                }

                lstTransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = strContent, Address = strAddress, DataType = strDataType });
            }
        }        

        public static void GetIndexAndDataType(DataTable dt, ref List<TransmitingDataInfoSet> lstTransmittingDataInfo)
        {
            if (dt == null)
            {
                return;
            }

            lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            for (int i = 0; i < dt.Rows.Count; i++)
            {
                string strContent = null;
                string strDataType = Convert.ToString(dt.Rows[i]["DataType"]);
                string strAddress = Convert.ToString(dt.Rows[i]["Index"]);

                if (string.IsNullOrEmpty(strAddress) || string.IsNullOrEmpty(strDataType))
                {
                    continue;
                }

                strDataType = strDataType.ToUpper();
                strAddress = strAddress.Replace("0x", "");

                if (OthersHelper.SelectAxisNumber() == AxisNumber.B)
                {
                    strAddress = OthersHelper.AddressDeviation(strAddress);
                }

                if (string.IsNullOrEmpty(strAddress))
                {
                    continue;
                }

                if (strDataType == "INT32" || strDataType == "UINT32")
                {
                    strContent = strAddress + "20";
                }
                else if (strDataType == "INT16" || strDataType == "UINT16")
                {
                    strContent = strAddress + "10";
                }
                else if (strDataType == "INT8" || strDataType == "UINT8")
                {
                    strContent = strAddress + "08";
                }

                lstTransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = strContent, Address = strAddress, DataType = strDataType });
            }
        }

        //*************************************************************************
        //函数名称：GetIndexAndDataType
        //函数功能：获取地址与数据类型-用于参数单个写入
        //
        //输入参数：ParameterReadWriteSet In_clsTemp
        //                ref List<TransmitingDataInfoSet> lstTransmittingDataInfo
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2019.12.18
        //*************************************************************************       
        public static void GetIndexAndDataType(List<ParameterReadWriteSet> lstParameterInfo, ref List<TransmitingDataInfoSet> lstTransmittingDataInfo, bool IsCheckAddressDeviation, bool IsEEPROM)
        {
            string strContent = null;
            string strValue = null;
            string strAddress = null;
            string strDataType = null;
            lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (lstParameterInfo == null)
                {
                    return;
                }

                if (lstParameterInfo.Count == 0)
                {
                    return;
                }

                foreach (var item in lstParameterInfo)
                {
                    if (string.IsNullOrEmpty(item.Index))
                    {
                        return;
                    }

                    strValue = item.CurrentValue;
                    strAddress = item.Index.Replace("0x", "");
                    strDataType = item.DataType.ToUpper();

                    if (IsCheckAddressDeviation && OthersHelper.SelectAxisNumber() == AxisNumber.B)
                    {
                        strAddress = OthersHelper.AddressDeviation(strAddress);
                    }

                    if (string.IsNullOrEmpty(strAddress))
                    {
                        return;
                    }

                    if (strDataType == "INT32")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "A0";
                        }
                        else
                        {
                            strContent = strAddress + "20";
                        }

                        Int32 i32temp = Convert.ToInt32(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(i32temp)).Replace("-", ""));
                    }
                    else if (strDataType == "UINT32")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "A0";
                        }
                        else
                        {
                            strContent = strAddress + "20";
                        }

                        UInt32 u32temp = Convert.ToUInt32(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(u32temp)).Replace("-", ""));
                    }
                    else if (strDataType == "INT16")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "90";
                        }
                        else
                        {
                            strContent = strAddress + "10";
                        }

                        Int16 i16temp = Convert.ToInt16(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(i16temp)).Replace("-", ""));
                    }
                    else if (strDataType == "UINT16")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "90";
                        }
                        else
                        {
                            strContent = strAddress + "10";
                        }

                        UInt16 u16temp = Convert.ToUInt16(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(u16temp)).Replace("-", ""));
                    }
                    else if (strDataType == "INT8")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "88";
                        }
                        else
                        {
                            strContent = strAddress + "08";
                        }

                        sbyte sb8temp = Convert.ToSByte(strValue);
                        strContent += BitConverter.ToString(BitConverter.GetBytes(sb8temp)).Substring(0, 2); ;
                    }
                    else if (strDataType == "UINT8")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "88";
                        }
                        else
                        {
                            strContent = strAddress + "08";
                        }

                        byte b8temp = Convert.ToByte(strValue);
                        strContent += BitConverter.ToString(BitConverter.GetBytes(b8temp)).Substring(0, 2); ;
                    }

                    lstTransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = strContent, Address = strAddress, DataType = strDataType, Value = strValue });
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_GET_INDEX_AND_DATATYPE, "GetIndexAndDataType", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetIndexAndDataType
        //函数功能：获取地址与数据类型-用于参数单个写入
        //
        //输入参数：ParameterReadWriteSet In_clsTemp
        //                ref List<TransmitingDataInfoSet> lstTransmittingDataInfo
        //       
        //输出参数：NONE
        //        
        //编码作者：Lilbert
        //更新时间：2024.05.27
        //*************************************************************************       
        public static void GetIndexAndDataType_ForAxisAddressReset(List<ParameterReadWriteSet> lstParameterInfo, ref List<TransmitingDataInfoSet> lstTransmittingDataInfo, bool IsCheckAddressDeviation, bool IsEEPROM)
        {            
            lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                lstTransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = "200711100101", Address = "200711", DataType = "UINT16", Value = "257" });
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_GET_INDEX_AND_DATATYPE, "GetIndexAndDataType_ForAxisAddressReset", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetIndexAndDataType
        //函数功能：获取地址与数据类型-用于参数单个写入
        //
        //输入参数：ParameterReadWriteSet In_clsTemp
        //                ref List<TransmitingDataInfoSet> lstTransmittingDataInfo
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2019.12.18
        //*************************************************************************       
        public static void GetIndexAndDataType_ForCompare(List<DiffParameterReadWriteSet> lstParameterInfo, ref List<TransmitingDataInfoSet> lstTransmittingDataInfo, bool IsCheckAddressDeviation, bool IsEEPROM)
        {
            string strContent = null;
            string strValue = null;
            string strAddress = null;
            string strDataType = null;
            lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (lstParameterInfo == null)
                {
                    return;
                }

                if (lstParameterInfo.Count == 0)
                {
                    return;
                }

                foreach (var item in lstParameterInfo)
                {
                    if (string.IsNullOrEmpty(item.Index))
                    {
                        return;
                    }

                    strValue = item.Current;
                    strAddress = item.Index.Replace("0x", "");
                    strDataType = item.DataType.ToUpper();

                    if (IsCheckAddressDeviation && OthersHelper.SelectAxisNumber() == AxisNumber.B)
                    {
                        strAddress = OthersHelper.AddressDeviation(strAddress);
                    }

                    if (string.IsNullOrEmpty(strAddress))
                    {
                        return;
                    }

                    if (strDataType == "INT32")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "A0";
                        }
                        else
                        {
                            strContent = strAddress + "20";
                        }

                        Int32 i32temp = Convert.ToInt32(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(i32temp)).Replace("-", ""));
                    }
                    else if (strDataType == "UINT32")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "A0";
                        }
                        else
                        {
                            strContent = strAddress + "20";
                        }

                        UInt32 u32temp = Convert.ToUInt32(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(u32temp)).Replace("-", ""));
                    }
                    else if (strDataType == "INT16")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "90";
                        }
                        else
                        {
                            strContent = strAddress + "10";
                        }

                        Int16 i16temp = Convert.ToInt16(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(i16temp)).Replace("-", ""));
                    }
                    else if (strDataType == "UINT16")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "90";
                        }
                        else
                        {
                            strContent = strAddress + "10";
                        }

                        UInt16 u16temp = Convert.ToUInt16(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(u16temp)).Replace("-", ""));
                    }
                    else if (strDataType == "INT8")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "88";
                        }
                        else
                        {
                            strContent = strAddress + "08";
                        }

                        sbyte sb8temp = Convert.ToSByte(strValue);
                        strContent += BitConverter.ToString(BitConverter.GetBytes(sb8temp)).Substring(0, 2); ;
                    }
                    else if (strDataType == "UINT8")
                    {
                        if (IsEEPROM)
                        {
                            strContent = strAddress + "88";
                        }
                        else
                        {
                            strContent = strAddress + "08";
                        }

                        byte b8temp = Convert.ToByte(strValue);
                        strContent += BitConverter.ToString(BitConverter.GetBytes(b8temp)).Substring(0, 2); ;
                    }

                    lstTransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = strContent, Address = strAddress, DataType = strDataType, Value = strValue });
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_GET_INDEX_AND_DATATYPE, "GetIndexAndDataType", ex);
            }
        }


        public static void GetIndexAndDataType(List<ParameterReadWriteSet> lstParameterInfo, ref List<TransmitingDataInfoSet> lstTransmittingDataInfo)
        {
            string strContent = null;
            string strValue = null;
            string strAddress = null;
            string strDataType = null;
            lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                if (lstParameterInfo == null)
                {
                    return;
                }

                if (lstParameterInfo.Count == 0)
                {
                    return;
                }

                foreach (var item in lstParameterInfo)
                {
                    if (string.IsNullOrEmpty(item.Index))
                    {
                        return;
                    }

                    strValue = item.CurrentValue;
                    strAddress = item.Index.Replace("0x", "");
                    strDataType = item.DataType.ToUpper();

                    if (SoftwareStateParameterSet.AxisIndex == 1 || SoftwareStateParameterSet.AxisIndex == 3 || SoftwareStateParameterSet.AxisIndex == 5)//B轴                      
                    {
                        strAddress = OthersHelper.AddressDeviation(strAddress);
                    }

                    if (string.IsNullOrEmpty(strAddress))
                    {
                        return;
                    }

                    if (strDataType == "INT32")
                    {                        
                        strContent = strAddress + "20";                   
                        Int32 i32temp = Convert.ToInt32(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(i32temp)).Replace("-", ""));
                    }
                    else if (strDataType == "UINT32")
                    {
                        strContent = strAddress + "20";
                        UInt32 u32temp = Convert.ToUInt32(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(u32temp)).Replace("-", ""));
                    }
                    else if (strDataType == "INT16")
                    {
                        strContent = strAddress + "10";                  
                        Int16 i16temp = Convert.ToInt16(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(i16temp)).Replace("-", ""));
                    }
                    else if (strDataType == "UINT16")
                    {
                        strContent = strAddress + "10";                   
                        UInt16 u16temp = Convert.ToUInt16(strValue);
                        strContent += HexHelper.ConvertHexStringEndian(BitConverter.ToString(BitConverter.GetBytes(u16temp)).Replace("-", ""));
                    }
                    else if (strDataType == "INT8")
                    {
                        strContent = strAddress + "08";                      
                        sbyte sb8temp = Convert.ToSByte(strValue);
                        strContent += BitConverter.ToString(BitConverter.GetBytes(sb8temp)).Substring(0, 2); ;
                    }
                    else if (strDataType == "UINT8")
                    {                     
                        strContent = strAddress + "08";                        
                        byte b8temp = Convert.ToByte(strValue);
                        strContent += BitConverter.ToString(BitConverter.GetBytes(b8temp)).Substring(0, 2); ;
                    }

                    lstTransmittingDataInfo.Add(new TransmitingDataInfoSet() { Content = strContent, Address = strAddress, DataType = strDataType, Value = strValue });
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.PARAMETERREADWRITE_GET_INDEX_AND_DATATYPE, "GetIndexAndDataType", ex);
            }
        }
    }
}
