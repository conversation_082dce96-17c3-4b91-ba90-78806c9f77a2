﻿using ServoStudio.GlobalConstant;
using ServoStudio.Models;
using ServoStudio.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace ServoStudio.GlobalMethod
{
    public static class ServoXmlHelper
    {
        public static XmlDocument xmlDoc = null;
        //*************************************************************************
        //函数名称：XmlServoConfigs
        //函数功能：加载伺服配置XML文件
        //
        //输入参数：
        //         
        //        
        //        
        //
        //输出参数：
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.24
        //*************************************************************************
        public static void XmlServoConfigs()
        {
            try
            {
                //FilePath.XmlPath = FilePath.XmlPath.Substring(0, FilePath.XmlPath.Substring(0, FilePath.XmlPath.Substring(0, FilePath.XmlPath.LastIndexOf("\\")).LastIndexOf("\\")).LastIndexOf("\\")) + "\\Xml\\OscilloscopePresetConfigs.xml";
                xmlDoc = new XmlDocument();
                xmlDoc.Load(FilePath.ConfigServo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_READ_OSCILLOSCOPE_PRESET_CONFIGS, "OscilloscopePresetConfigs", ex);
            }
        }
        //*************************************************************************
        //函数名称：GetOscilloscopePresetConfigsData
        //函数功能：获取示波器预配置XML文件数据
        //
        //输入参数：
        //         
        //        
        //        
        //
        //输出参数：返回示波器预配置XML文件数据集合
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.24
        //*************************************************************************
        //public static List<OscilloscopePresetModel> GetOscilloscopePresetConfigsData()
        //{
        //    List<OscilloscopePresetModel> oscilloscopePresets = new List<OscilloscopePresetModel>();
        //    string xPath = "OscilloscopePresetConfigs/config";
        //    XmlNodeList xmlNodeList = xmlDoc.SelectNodes(xPath);
        //    foreach (XmlNode xNode in xmlNodeList)
        //    {
        //        OscilloscopePresetModel oscilloscopePreset = new OscilloscopePresetModel
        //        {
        //            Id = xNode.Attributes["Id"].Value.ChangeInt(),
        //            SelectedSampleChannel1Index = xNode.Attributes["SelectedSampleChannel1Index"].Value.ChangeInt(),
        //            SelectedSampleChannel2Index = xNode.Attributes["SelectedSampleChannel2Index"].Value.ChangeInt(),
        //            SelectedSampleChannel3Index = xNode.Attributes["SelectedSampleChannel3Index"].Value.ChangeInt(),
        //            SelectedSampleChannel4Index = xNode.Attributes["SelectedSampleChannel4Index"].Value.ChangeInt(),
        //            SamplingPeriod = xNode.Attributes["SamplingPeriod"].Value,
        //            SamplingDuration = xNode.Attributes["SamplingDuration"].Value,
        //            ContinuousSampling = xNode.Attributes["ContinuousSampling"].Value,
        //            TriggerClockEdge = xNode.Attributes["TriggerClockEdge"].Value,
        //            TriggerChannel = xNode.Attributes["TriggerChannel"].Value,
        //            PreTrigger = xNode.Attributes["PreTrigger"].Value,
        //            TriggerLevel = xNode.Attributes["TriggerLevel"].Value
        //        };
        //        oscilloscopePresets.Add(oscilloscopePreset);
        //    }
        //    return oscilloscopePresets;
        //}

        //*************************************************************************
        //函数名称：GetServoConfigsData
        //函数功能：获取伺服预配置XML文件数据
        //
        //输入参数：
        //         
        //        
        //        
        //
        //输出参数：返回示波器预配置XML文件数据集合
        //        
        //编码作者：Lilbert
        //更新时间：2023.10.24
        //*************************************************************************
        public static List<ServoConfigsModel> GetServoConfigsData()
        {
            //XDocument xDoc = XDocument.Load(FilePath.ConfigServo);

            //XDocument xDoc = XDocument.Load(FilePath.ConfigServo);

            List<ServoConfigsModel> servoCofigsList = new List<ServoConfigsModel>();
            //string xPath = "OscilloscopePresetConfigs/config";

            string xPath = "ServoConfigs/config";
            XmlNodeList xmlNodeList = xmlDoc.SelectNodes(xPath);

            foreach (XmlNode xNode in xmlNodeList)
            {
                ServoConfigsModel servoConfig = new ServoConfigsModel
                {
                    Id = xNode.Attributes["Id"].Value.ChangeInt(),
                    ConfigServoName = xNode.Attributes["ConfigServoName"].Value,
                    ConfigSlaveID = xNode.Attributes["ConfigSlaveID"].Value.ChangeInt(),
                    ConfigAxisID = xNode.Attributes["ConfigAxisID"].Value.ChangeInt(),
                    ConfigParameter = xNode.Attributes["ConfigParameter"].Value
                };
                servoCofigsList.Add(servoConfig);
            }
            return servoCofigsList;
        }
        //*************************************************************************
        //函数名称：SaveToOscilloscopePresetConfigs
        //函数功能：保存示波器预配置XML文件数据
        //
        //输入参数：oscilloscopePresetModel  示波器预配置XML文件Model
        //         
        //        
        //        
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.26
        //*************************************************************************
        //public static int SaveToOscilloscopePresetConfigs(OscilloscopePresetModel oscilloscopePresetModel)
        //{
        //    int iRet = -1;
        //    try
        //    {
        //        //第一步，创建元素
        //        XmlElement configElement = xmlDoc.CreateElement("config");
        //        //第二步，创建指定节点
        //        XmlNode xmlNode = xmlDoc.SelectSingleNode("OscilloscopePresetConfigs");
        //        //第三步，给元素赋值
        //        configElement.SetAttribute("Id", oscilloscopePresetModel.Id.ToString());
        //        configElement.SetAttribute("SelectedSampleChannel1Index", oscilloscopePresetModel.SelectedSampleChannel1Index.ToString());
        //        configElement.SetAttribute("SelectedSampleChannel2Index", oscilloscopePresetModel.SelectedSampleChannel2Index.ToString());
        //        configElement.SetAttribute("SelectedSampleChannel3Index", oscilloscopePresetModel.SelectedSampleChannel3Index.ToString());
        //        configElement.SetAttribute("SelectedSampleChannel4Index", oscilloscopePresetModel.SelectedSampleChannel4Index.ToString());
        //        configElement.SetAttribute("SamplingPeriod", oscilloscopePresetModel.SamplingPeriod);
        //        configElement.SetAttribute("SamplingDuration", oscilloscopePresetModel.SamplingDuration);
        //        configElement.SetAttribute("ContinuousSampling", oscilloscopePresetModel.ContinuousSampling);
        //        configElement.SetAttribute("TriggerClockEdge", oscilloscopePresetModel.TriggerClockEdge);
        //        configElement.SetAttribute("TriggerChannel", oscilloscopePresetModel.TriggerChannel);
        //        configElement.SetAttribute("PreTrigger", oscilloscopePresetModel.PreTrigger);
        //        configElement.SetAttribute("TriggerLevel", oscilloscopePresetModel.TriggerLevel);
        //        xmlNode.AppendChild(configElement);
        //        xmlDoc.Save(FilePath.XmlPath);
        //        iRet = RET.SUCCEEDED;
        //    }
        //    catch 
        //    {
        //        iRet = RET.ERROR;
        //    }
        //    return iRet;
        //}
        //*************************************************************************
        //函数名称：EditOscilloscopePresetConfigs
        //函数功能：编辑示波器预配置XML文件数据
        //
        //输入参数：oscilloscopePresetModel  示波器预配置XML文件Model
        //         
        //        
        //        
        //
        //输出参数：1:OK
        //         0:NO_EFFECT
        //        -1:Error
        //        
        //编码作者：Lilbert
        //更新时间：2022.08.26
        //*************************************************************************
        //public static int EditOscilloscopePresetConfigs(OscilloscopePresetModel oscilloscopePresetModel)
        //{
        //    int iRet = -1;
        //    try
        //    {
        //        int oscilloscopePresetId = oscilloscopePresetModel.Id;
        //        if (oscilloscopePresetId > 0)
        //        {
        //            string xPath = string.Format("/OscilloscopePresetConfigs/config[@Id='{0}']", oscilloscopePresetId);
        //            XmlNode editXmlNode = xmlDoc.SelectSingleNode(xPath);
        //            XmlElement editXmlElement = editXmlNode as XmlElement;
        //            if (!(editXmlElement == null))
        //            {
        //                editXmlElement.Attributes["SelectedSampleChannel1Index"].Value = oscilloscopePresetModel.SelectedSampleChannel1Index.ToString();
        //                editXmlElement.Attributes["SelectedSampleChannel2Index"].Value = oscilloscopePresetModel.SelectedSampleChannel2Index.ToString();
        //                editXmlElement.Attributes["SelectedSampleChannel3Index"].Value = oscilloscopePresetModel.SelectedSampleChannel3Index.ToString();
        //                editXmlElement.Attributes["SelectedSampleChannel4Index"].Value = oscilloscopePresetModel.SelectedSampleChannel4Index.ToString();
        //                editXmlElement.Attributes["SamplingPeriod"].Value = oscilloscopePresetModel.SamplingPeriod;
        //                editXmlElement.Attributes["SamplingDuration"].Value = oscilloscopePresetModel.SamplingDuration;
        //                editXmlElement.Attributes["ContinuousSampling"].Value = oscilloscopePresetModel.ContinuousSampling;
        //                editXmlElement.Attributes["TriggerClockEdge"].Value = oscilloscopePresetModel.TriggerClockEdge;
        //                editXmlElement.Attributes["TriggerChannel"].Value = oscilloscopePresetModel.TriggerChannel;
        //                if (oscilloscopePresetModel.PreTrigger == "0")
        //                {
        //                    editXmlElement.Attributes["PreTrigger"].Value = oscilloscopePresetModel.PreTrigger;
        //                }
        //                else
        //                {
        //                    editXmlElement.Attributes["PreTrigger"].Value = oscilloscopePresetModel.PreTrigger.Substring(0, 2);
        //                }                        
        //                editXmlElement.Attributes["TriggerLevel"].Value = oscilloscopePresetModel.TriggerLevel;                       
        //                xmlDoc.Save(FilePath.XmlPath);//最后保存一下XML文件
        //                iRet = RET.SUCCEEDED;
        //            }
        //        }               
        //    }
        //    catch
        //    {
        //        iRet = RET.ERROR;
        //    }
        //    return iRet;
        //}

    }
}
