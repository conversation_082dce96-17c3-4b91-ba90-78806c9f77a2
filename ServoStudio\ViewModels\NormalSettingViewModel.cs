﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using DevExpress.Mvvm.POCO;
using ServoStudio.Models;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class NormalSettingViewModel
    {
        #region 私有字段
        private static bool IsInitialized = true;
        private static bool IsEvaluationAll = true;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 服务
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 属性
        public virtual string SelectedTabIndex { get; set; }
          
        //正反转
        public virtual bool IsCWChecked { get; set; }//以CW为正方向
        public virtual bool IsCCWChecked { get; set; }//以CCW为正方向
      
        //抱闸
        public virtual bool IsBrakeChecked { get; set; }//抱闸开关      
        public virtual string BrakeReleaseDelayTime { get; set; }//抱闸释放延时时间
        public virtual string BrakeActiveDelayTime { get; set; }//抱闸制动延时时间
        public virtual string BrakeActiveVelocity { get; set; }//抱闸制动速度门限
        public virtual string BrakeActiveAllowedDelayTime { get; set; }//抱闸制动允许延时时间
     
        //提示
        public virtual string Hint_RotateDirection { get; set; }//旋转方向设定
        public virtual string Hint_BrakeEnable { get; set; }//抱闸使能开关


        #region 停机方式
        public virtual ObservableCollection<string> OverTravelStopMode { get; set; }//超程停机方式
        public virtual string SelectedOverTravelStopMode { get; set; }//选中的超程停机方式
        public virtual ObservableCollection<string> ForceStopMode { get; set; }//强制停机方式
        public virtual string SelectedForceStopMode { get; set; }//选中的强制停机方式 
        public virtual ObservableCollection<string> TwoFaultStopMode { get; set; }//二级故障停机方式
        public virtual string SelectedTwoFaultStopMode { get; set; }//选中的二级故障停机方式
        public virtual ObservableCollection<string> OneFaultStopMode { get; set; }//一级故障停机方式
        public virtual string SelectedOneFaultStopMode { get; set; }//选中的一级故障停机方式 
        #endregion
        #endregion

        #region 构造函数
        public NormalSettingViewModel()
        {
            ViewModelSet.NormalSetting = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.NORMALSET;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：NormalSettingLoaded
        //函数功能：NormalSetting界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.03.12&2023.11.13
        //*************************************************************************
        public void NormalSettingLoaded()
        {
            int iRet = -1;

            try
            {
                //下拉列表初始化
                ComboBoxInitialize();//由Lilbert于2023.11.13添加停机方式

                //赋值
                if (IsInitialized == true)
                {
                    //标志位更新
                    IsInitialized = false;
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadNormalSettingParameter("All");

                        //停机方式
                        ReadNormalSettingShutdownMethod("All");
                    }
                    else
                    {
                        GetDefaultNormalSettingParameter("All");

                        //停机方式
                        GetDefaultNormalSettingShutdownMethod("All");
                    }
                }
                else
                {
                    //标志位更新
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadNormalSettingParameter("All");

                        //停机方式
                        ReadNormalSettingShutdownMethod("All");
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_LOADED, "NormalSettingLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：WriteNormalSettingParameter
        //函数功能：写一般设置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void WriteNormalSettingParameter(string strCategory)
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.NORMALSET, TaskName.NormalSetting, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_WRITE_PARAMETER, "WriteNormalSettingParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：WriteNormalSettingShutdownMethod
        //函数功能：写停机方式设置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.14
        //*************************************************************************
        public void WriteNormalSettingShutdownMethod(string strCategory)
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //判断串口状态
                iRet = HexHelper.CheckSerialPortStatus();
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(1000);
                    return;
                }

                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary_ForStopMode(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.NORMALSET, TaskName.NormalSetting, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_WRITE_PARAMETER, "WriteNormalSettingShutdownMethod", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadNormalSettingParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void ReadNormalSettingParameter(string strCategory)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.NORMALSET, TaskName.NormalSetting, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_READ_PARAMETER, "ReadNormalSettingParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadNormalSettingShutdownMethod
        //函数功能：读电机停机方式参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.14
        //*************************************************************************
        public void ReadNormalSettingShutdownMethod(string strCategory)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary_ForStopMode(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.NORMALSET, TaskName.NormalSetting, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_READ_PARAMETER, "ReadNormalSettingShutdownMethod", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultNormalSettingParameter
        //函数功能：获取限幅保护的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.13
        //*************************************************************************
        public void GetDefaultNormalSettingParameter(string strCategory)
        {
            string strRotateDirection = null;
            string strBrake = null;

            try
            {              
                if (strCategory == "0")
                {
                    BrakeReleaseDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Release Delay Time", "Default");
                    BrakeActiveDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Delay Time", "Default");
                    BrakeActiveVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Velocity", "Default");
                    BrakeActiveAllowedDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Allowed Delay Time", "Default");

                    strBrake = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Enable", "Default");
                    if (strBrake == "0")
                    {
                        IsBrakeChecked = false;
                        Hint_BrakeEnable = "抱闸制动功能关";
                    }
                    else if (strBrake == "1")
                    {
                        IsBrakeChecked = true;
                        Hint_BrakeEnable = "抱闸制动功能开";
                    }
                }
                else if (strCategory == "1")
                {
                    strRotateDirection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Rotate Direction", "Default");
                    if (strRotateDirection == "0")
                    {
                        IsCCWChecked = true;
                        IsCWChecked = false;
                    }
                    else if (strRotateDirection == "1")
                    {
                        IsCWChecked = true;
                        IsCCWChecked = false;
                    }
                }
                else
                {
                    BrakeReleaseDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Release Delay Time", "Default");
                    BrakeActiveDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Delay Time", "Default");
                    BrakeActiveVelocity = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Velocity", "Default");
                    BrakeActiveAllowedDelayTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Allowed Delay Time", "Default");

                    strBrake = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Enable", "Default");
                    if (strBrake == "0")
                    {
                        IsBrakeChecked = false;
                        Hint_BrakeEnable = "抱闸制动功能关";
                    }
                    else if (strBrake == "1")
                    {
                        IsBrakeChecked = true;
                        Hint_BrakeEnable = "抱闸制动功能开";
                    }

                    strRotateDirection = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Rotate Direction", "Default");
                    if (strRotateDirection == "0")
                    {
                        IsCCWChecked = true;
                        IsCWChecked = false;
                    }
                    else if (strRotateDirection == "1")
                    {
                        IsCWChecked = true;
                        IsCCWChecked = false;
                    }                    
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_GET_DEFAULT_PARAMETER, "GetDefaultNormalSettingParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultNormalSettingShutdownMethod
        //函数功能：获取停机方式的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.14
        //*************************************************************************
        public void GetDefaultNormalSettingShutdownMethod(string strCategory)
        {
            string AlarmStopMode = null;

            string strOverTravelStopMode = null;
            string strForceStopMode = null;
            string strTwoFaultStopMode = null;
            string strOneFaultStopMode = null;

            try
            {
                //停机模式
                AlarmStopMode = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Stop Mode", "Default");

                //一级故障停机方式
                //strOneFaultStopMode = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Fault No.1 Stop Mode", "Default");
                strOneFaultStopMode = (Convert.ToInt32(AlarmStopMode) & 0x0F).ToString();
                if (strOneFaultStopMode == "0")
                {
                    SelectedOneFaultStopMode = "DB停机，保持DB状态";
                }
                else if (strOneFaultStopMode == "1")
                {
                    SelectedOneFaultStopMode = "DB停机，保持自由状态";
                }
                else if (strOneFaultStopMode == "2")
                {
                    SelectedOneFaultStopMode = "自由停机，保持自由状态";
                }

                //二级故障停机方式
                //strTwoFaultStopMode = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Stop Mode", "Default");
                strTwoFaultStopMode = (Convert.ToInt32(AlarmStopMode) >> 4 & 0x0F).ToString();
                if (strTwoFaultStopMode == "0")
                {
                    SelectedTwoFaultStopMode = "使用一级故障停机方式";
                }
                else if (strTwoFaultStopMode == "1")
                {
                    SelectedTwoFaultStopMode = "零速度停机，DB状态";
                }
                else if (strTwoFaultStopMode == "2")
                {
                    SelectedTwoFaultStopMode = "零速度停机，自由状态";
                }
                else if (strTwoFaultStopMode == "3")
                {
                    SelectedTwoFaultStopMode = "减速停机，DB状态";
                }
                else if (strTwoFaultStopMode == "4")
                {
                    SelectedTwoFaultStopMode = "减速停机，自由状态";
                }

                //强制停机方式
                //strForceStopMode = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Force Stop Mode", "Default");
                strForceStopMode = (Convert.ToInt32(AlarmStopMode) >> 8 & 0x0F).ToString();
                if (strForceStopMode == "0")
                {
                    SelectedForceStopMode = "使用一级故障停机方式";
                }
                else if (strForceStopMode == "1")
                {
                    SelectedForceStopMode = "零速度停机，DB状态";
                }
                else if (strForceStopMode == "2")
                {
                    SelectedForceStopMode = "零速度停机，自由状态";
                }
                else if (strForceStopMode == "3")
                {
                    SelectedForceStopMode = "减速停机，DB状态";
                }
                else if (strForceStopMode == "4")
                {
                    SelectedForceStopMode = "减速停机，自由状态";
                }

                //超程停机方式
                //strOverTravelStopMode = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Over Travel Stop Mode", "Default");
                strOverTravelStopMode = (Convert.ToInt32(AlarmStopMode) >> 12 & 0x0F).ToString();
                if (strOverTravelStopMode == "0")
                {
                    SelectedOverTravelStopMode = "使用一级故障停机方式";
                }
                else if (strOverTravelStopMode == "1")
                {
                    SelectedOverTravelStopMode = "零速度停机，伺服锁定";
                }
                else if (strOverTravelStopMode == "2")
                {
                    SelectedOverTravelStopMode = "零速度停机，自由状态";
                }
                else if (strOverTravelStopMode == "3")
                {
                    SelectedOverTravelStopMode = "减速停机，伺服锁定";
                }
                else if (strOverTravelStopMode == "4")
                {
                    SelectedOverTravelStopMode = "减速停机，自由状态";
                }

            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_GET_DEFAULT_PARAMETER, "GetDefaultNormalSettingShutdownMethod", ex);
            }
        }

        //*************************************************************************
        //函数名称：SaveNormalSettingConfigFile
        //函数功能：保存限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void SaveNormalSettingConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                //选择配置文件存放位置
                iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.NormalSetting);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //生成配置文件
                iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetNormalSettingConfigToDataTable(), ExcelType.NormalSetting);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2002);
                }
                else
                {
                    ShowNotification(2003);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_SAVE_CONFIG_FILE, "SaveNormalSettingConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetNormalSettingConfigFile
        //函数功能：获取限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void GetNormalSettingConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            DataTable dt = null;

            try
            {
                //获取配置文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(strFilePath, ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //判断是否导入正确的配置文件
                if (dt.Columns[3].ColumnName != FileInterface.NORMALSETTING)
                {
                    ShowNotification(2001);
                    return;
                }

                //导入参数  
                iRet = GetNormalSettingConfigFromDataTable(dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //是否下载
                OthersHelper.GetWindowsStartupPosition();
                if (MessageBoxService.ShowMessage("是否下载当前导入的全部一般设定参数...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    WriteNormalSettingParameter("All");

                    //停机方式
                    WriteNormalSettingShutdownMethod("All");
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_GET_CONFIG_FILE, "GetNormalSettingConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationNormalSettingParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.15
        //*************************************************************************
        public void EvaluationNormalSettingParameter()
        {
            string strRotateDirection = null;
            string strBrake = null;

            string AlarmStopMode = null;//停机模式

            string strOverTravelStopMode = null;
            string strForceStopMode = null;
            string strTwoFaultStopMode = null;
            string strOneFaultStopMode = null;

            if (IsEvaluationAll)
            {
                //标志位更新
                IsEvaluationAll = false;

                #region 抱闸
                //抱闸
                BrakeReleaseDelayTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Release Delay Time", "Index"));//抱闸释放延时时间
                BrakeActiveDelayTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Delay Time", "Index"));//抱闸制动延时时间
                BrakeActiveVelocity = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Velocity", "Index"));//抱闸制动速度门限
                BrakeActiveAllowedDelayTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Allowed Delay Time", "Index"));//抱闸制动允许延时时间

                strBrake = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Enable", "Index"));//抱闸使能开关
                if (strBrake == "0")
                {
                    IsBrakeChecked = false;
                    Hint_BrakeEnable = "抱闸制动功能关";
                }
                else if (strBrake == "1")
                {
                    IsBrakeChecked = true;
                    Hint_BrakeEnable = "抱闸制动功能开";
                }
                #endregion

                #region 正反转
                //正反转
                strRotateDirection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Rotate Direction", "Index"));//旋转方向设定
                if (strRotateDirection == "0")
                {
                    IsCCWChecked = true;
                    IsCWChecked = false;
                }
                else if (strRotateDirection == "1")
                {
                    IsCWChecked = true;
                    IsCCWChecked = false;
                }
                #endregion

                #region 停机方式
                AlarmStopMode = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Stop Mode", "Index"));//停机模式

                //strOneFaultStopMode = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Fault No.1 Stop Mode", "Index"));//故障NO.1 停机方式
                strOneFaultStopMode = (Convert.ToInt32(AlarmStopMode) & 0x0F).ToString();
                
                if (strOneFaultStopMode == "0")
                {
                    SelectedOneFaultStopMode = "DB停机，保持DB状态";
                }
                else if (strOneFaultStopMode == "1")
                {
                    SelectedOneFaultStopMode = "DB停机，保持自由状态";
                }
                else if (strOneFaultStopMode == "2")
                {
                    SelectedOneFaultStopMode = "自由停机，保持自由状态";
                }

                //strTwoFaultStopMode = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Stop Mode", "Index"));//报警停机方式
                strTwoFaultStopMode = (Convert.ToInt32(AlarmStopMode) >> 4 & 0x0F).ToString();
                if (strTwoFaultStopMode == "0")
                {
                    SelectedTwoFaultStopMode = "使用一级故障停机方式";
                }
                else if (strTwoFaultStopMode == "1")
                {
                    SelectedTwoFaultStopMode = "零速度停机，DB状态";
                }
                else if (strTwoFaultStopMode == "2")
                {
                    SelectedTwoFaultStopMode = "零速度停机，自由状态";
                }
                else if (strTwoFaultStopMode == "3")
                {
                    SelectedTwoFaultStopMode = "减速停机，DB状态";
                }
                else if (strTwoFaultStopMode == "4")
                {
                    SelectedTwoFaultStopMode = "减速停机，自由状态";
                }

                //strForceStopMode = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Force Stop Mode", "Index"));//强制停机方式
                strForceStopMode = (Convert.ToInt32(AlarmStopMode) >> 8 & 0x0F).ToString();
                if (strForceStopMode == "0")
                {
                    SelectedForceStopMode = "使用一级故障停机方式";
                }
                else if (strForceStopMode == "1")
                {
                    SelectedForceStopMode = "零速度停机，DB状态";
                }
                else if (strForceStopMode == "2")
                {
                    SelectedForceStopMode = "零速度停机，自由状态";
                }
                else if (strForceStopMode == "3")
                {
                    SelectedForceStopMode = "减速停机，DB状态";
                }
                else if (strForceStopMode == "4")
                {
                    SelectedForceStopMode = "减速停机，自由状态";
                }

                //停机方式
                //strOverTravelStopMode = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Over Travel Stop Mode", "Index"));//超程停机方式
                strOverTravelStopMode = (Convert.ToInt32(AlarmStopMode) >> 12 & 0x0F).ToString();
                if (strOverTravelStopMode == "0")
                {
                    SelectedOverTravelStopMode = "使用一级故障停机方式";
                }
                else if (strOverTravelStopMode == "1")
                {
                    SelectedOverTravelStopMode = "零速度停机，伺服锁定";
                }
                else if (strOverTravelStopMode == "2")
                {
                    SelectedOverTravelStopMode = "零速度停机，自由状态";
                }
                else if (strOverTravelStopMode == "3")
                {
                    SelectedOverTravelStopMode = "减速停机，伺服锁定";
                }
                else if (strOverTravelStopMode == "4")
                {
                    SelectedOverTravelStopMode = "减速停机，自由状态";
                }
                
                #endregion
            }
            else
            {
                if (SelectedTabIndex == "0")
                {
                    BrakeReleaseDelayTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Release Delay Time", "Index"));//抱闸释放延时时间
                    BrakeActiveDelayTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Delay Time", "Index"));//抱闸制动延时时间
                    BrakeActiveVelocity = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Velocity", "Index"));//抱闸制动速度门限
                    BrakeActiveAllowedDelayTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Active Allowed Delay Time", "Index"));//抱闸制动允许延时时间

                    strBrake = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Brake Enable", "Index"));//抱闸使能开关
                    if (strBrake == "0")
                    {
                        IsBrakeChecked = false;
                        Hint_BrakeEnable = "抱闸制动功能关";
                    }
                    else if (strBrake == "1")
                    {
                        IsBrakeChecked = true;
                        Hint_BrakeEnable = "抱闸制动功能开";
                    }
                }
                else if (SelectedTabIndex == "1")
                {
                    strRotateDirection = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Rotate Direction", "Index"));//旋转方向设定
                    if (strRotateDirection == "0")
                    {
                        IsCCWChecked = true;
                        IsCWChecked = false;
                    }
                    else if (strRotateDirection == "1")
                    {
                        IsCWChecked = true;
                        IsCCWChecked = false;
                    }
                }
                else if (SelectedTabIndex == "2")
                {
                    //停机方式
                    AlarmStopMode = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Stop Mode", "Index"));//停机模式

                    //strOneFaultStopMode = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Fault No.1 Stop Mode", "Index"));//故障NO.1 停机方式
                    strOneFaultStopMode = (Convert.ToInt32(AlarmStopMode) & 0x0F).ToString();

                    if (strOneFaultStopMode == "0")
                    {
                        SelectedOneFaultStopMode = "DB停机，保持DB状态";
                    }
                    else if (strOneFaultStopMode == "1")
                    {
                        SelectedOneFaultStopMode = "DB停机，保持自由状态";
                    }
                    else if (strOneFaultStopMode == "2")
                    {
                        SelectedOneFaultStopMode = "自由停机，保持自由状态";
                    }

                    //strTwoFaultStopMode = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Alarm Stop Mode", "Index"));//报警停机方式
                    strTwoFaultStopMode = (Convert.ToInt32(AlarmStopMode) >> 4 & 0x0F).ToString();
                    if (strTwoFaultStopMode == "0")
                    {
                        SelectedTwoFaultStopMode = "使用一级故障停机方式";
                    }
                    else if (strTwoFaultStopMode == "1")
                    {
                        SelectedTwoFaultStopMode = "零速度停机，DB状态";
                    }
                    else if (strTwoFaultStopMode == "2")
                    {
                        SelectedTwoFaultStopMode = "零速度停机，自由状态";
                    }
                    else if (strTwoFaultStopMode == "3")
                    {
                        SelectedTwoFaultStopMode = "减速停机，DB状态";
                    }
                    else if (strTwoFaultStopMode == "4")
                    {
                        SelectedTwoFaultStopMode = "减速停机，自由状态";
                    }

                    //strForceStopMode = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Force Stop Mode", "Index"));//强制停机方式
                    strForceStopMode = (Convert.ToInt32(AlarmStopMode) >> 8 & 0x0F).ToString();
                    if (strForceStopMode == "0")
                    {
                        SelectedForceStopMode = "使用一级故障停机方式";
                    }
                    else if (strForceStopMode == "1")
                    {
                        SelectedForceStopMode = "零速度停机，DB状态";
                    }
                    else if (strForceStopMode == "2")
                    {
                        SelectedForceStopMode = "零速度停机，自由状态";
                    }
                    else if (strForceStopMode == "3")
                    {
                        SelectedForceStopMode = "减速停机，DB状态";
                    }
                    else if (strForceStopMode == "4")
                    {
                        SelectedForceStopMode = "减速停机，自由状态";
                    }

                    //停机方式
                    //strOverTravelStopMode = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Over Travel Stop Mode", "Index"));//超程停机方式
                    strOverTravelStopMode = (Convert.ToInt32(AlarmStopMode) >> 12 & 0x0F).ToString();
                    if (strOverTravelStopMode == "0")
                    {
                        SelectedOverTravelStopMode = "使用一级故障停机方式";
                    }
                    else if (strOverTravelStopMode == "1")
                    {
                        SelectedOverTravelStopMode = "零速度停机，伺服锁定";
                    }
                    else if (strOverTravelStopMode == "2")
                    {
                        SelectedOverTravelStopMode = "零速度停机，自由状态";
                    }
                    else if (strOverTravelStopMode == "3")
                    {
                        SelectedOverTravelStopMode = "减速停机，伺服锁定";
                    }
                    else if (strOverTravelStopMode == "4")
                    {
                        SelectedOverTravelStopMode = "减速停机，自由状态";
                    }
                }
            }
        }

        //*************************************************************************
        //函数名称：LimitAmplitudeNavigation
        //函数功能：限幅与保护导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void LimitAmplitudeNavigation()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.LIMITAMPLITUDE;
                NavigationService.Navigate("LimitAmplitudeView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：DigitalIONavigation
        //函数功能：IO导航
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void DigitalIONavigation()
        {
            if (ViewModelSet.Main != null)
            {
                SoftwareStateParameterSet.CurrentPageName = PageName.DIGITALIO;
                NavigationService.Navigate("DigitalIOView", null, ViewModelSet.Main);
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void OnIsBrakeCheckedChanged()//抱闸功能
        {
            GlobalCurrentInput.IsBrakeChecked = IsBrakeChecked;

            if (IsBrakeChecked)
            {
                Hint_BrakeEnable = "抱闸制动功能开";
            }
            else
            {
                Hint_BrakeEnable = "抱闸制动功能关";
            }           
        }
        public void OnBrakeReleaseDelayTimeChanged() { GlobalCurrentInput.BrakeReleaseDelayTime = BrakeReleaseDelayTime; }//抱闸释放延时时间
        public void OnBrakeActiveDelayTimeChanged() { GlobalCurrentInput.BrakeActiveDelayTime = BrakeActiveDelayTime; }//抱闸制动延时时间
        public void OnBrakeActiveVelocityChanged() { GlobalCurrentInput.BrakeActiveVelocity = BrakeActiveVelocity; }//抱闸制动速度门限
        public void OnBrakeActiveAllowedDelayTimeChanged() { GlobalCurrentInput.BrakeActiveAllowedDelayTime = BrakeActiveAllowedDelayTime; }//抱闸制动允许延时时间
        public void OnSelectedOverTravelStopModeChanged() { GlobalCurrentInput.SelectedOverTravelStopMode = SelectedOverTravelStopMode; }//超程停机方式
        public void OnSelectedForceStopModeChanged() { GlobalCurrentInput.SelectedForceStopMode = SelectedForceStopMode; }//强制停机方式
        public void OnSelectedTwoFaultStopModeChanged() { GlobalCurrentInput.SelectedTwoFaultStopMode = SelectedTwoFaultStopMode; }//故障NO.2 停机方式
        public void OnSelectedOneFaultStopModeChanged() { GlobalCurrentInput.SelectedOneFaultStopMode = SelectedOneFaultStopMode; }//故障NO.1 停机方式

        public void OnIsCWCheckedChanged()//CW方向为正
        {
            GlobalCurrentInput.IsCWChecked = IsCWChecked;

            if (IsCWChecked)
            {
                IsCCWChecked = false;
                Hint_RotateDirection = "当前转动方向：以CW方向为正转方向";
            }
            else
            {
                IsCCWChecked = true;
                Hint_RotateDirection = "当前转动方向：以CCW方向为正转方向";
            }
        }
        public void OnIsCCWCheckedChanged()//CCW方向为正  
        {
            GlobalCurrentInput.IsCCWChecked = IsCCWChecked;

            if (IsCCWChecked)
            {
                IsCWChecked = false;
                Hint_RotateDirection = "当前转动方向：以CCW方向为正转方向";
            }
            else
            {
                IsCWChecked = true;
                Hint_RotateDirection = "当前转动方向：以CW方向为正转方向";
            }
        }

        public void OnSelectedTabIndexChanged()
        {
            int iRet = -1;

            switch (SelectedTabIndex)
            {
                case "0":
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadNormalSettingParameter("0");
                    }
                    else
                    {
                        GetDefaultNormalSettingParameter("0");
                    }
                    break;
                case "1":
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadNormalSettingParameter("1");
                    }
                    else
                    {
                        GetDefaultNormalSettingParameter("1");
                    }
                    break;
                case "2":                    
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadNormalSettingShutdownMethod("All");
                    }
                    else
                    {
                        GetDefaultNormalSettingShutdownMethod("All");
                    }
                    break;                
                default:
                    break;
            }
        }
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：GetNormalSettingConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private DataTable GetNormalSettingConfigToDataTable()
        {
            string strRotateDirection = null;
            string strBrake = null;
            DataTable dt = new DataTable();

            try
            {               
                //正反转
                if (IsCCWChecked && !IsCWChecked)
                {
                    strRotateDirection = "0";
                }
                else if (!IsCCWChecked && IsCWChecked)
                {
                    strRotateDirection = "1";
                }
                else
                {
                    dicParameterInfo.Add("Rotate Direction", "0");
                }

                OthersHelper.MakeValueToDataTable(FileInterface.NORMALSETTING, "Rotate Direction", "旋转方向设定", strRotateDirection,  ref dt);

                //抱闸
                if (IsBrakeChecked)
                {
                    strBrake = "1";
                }
                else
                {
                    strBrake = "0";
                }

                OthersHelper.MakeValueToDataTable(FileInterface.NORMALSETTING,"Brake Enable", "抱闸使能开关", strBrake, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.NORMALSETTING,"Brake Release Delay Time", "抱闸释放延时时间", BrakeReleaseDelayTime, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.NORMALSETTING,"Brake Active Delay Time", "抱闸制动延时时间", BrakeActiveDelayTime, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.NORMALSETTING,"Brake Active Velocity", "抱闸制动速度门限", BrakeActiveVelocity, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.NORMALSETTING,"Brake Active Allowed Delay Time", "抱闸制动允许延时时间", BrakeActiveAllowedDelayTime, ref dt);

                //停机方式
                OthersHelper.MakeValueToDataTable(FileInterface.NORMALSETTING, "Over Travel Stop Mode", "超程停机方式", SelectedOverTravelStopMode, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.NORMALSETTING, "Force Stop Mode", "强制停机方式", SelectedForceStopMode, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.NORMALSETTING, "Alarm Stop Mode", "报警停机方式", SelectedTwoFaultStopMode, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.NORMALSETTING, "Fault No.1 Stop Mode", "故障NO.1停机方式", SelectedOneFaultStopMode, ref dt);

                return dt;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_GET_CONFIG_TO_DATATABLE, "GetNormalSettingConfigToDataTable", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetNormalSettingConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int GetNormalSettingConfigFromDataTable(DataTable dt)
        {
            string strRotateDirection = null;
            string strBrake = null;

            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                //正反转
                strRotateDirection = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Rotate Direction", "Default");
                if (strRotateDirection == "0")
                {
                    IsCCWChecked = true;
                    IsCWChecked = false;
                }
                else if (strRotateDirection == "1")
                {
                    IsCWChecked = true;
                    IsCCWChecked = false;
                }

                //抱闸
                strBrake = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Brake Enable", "Default");
                if (strBrake == "0")
                {
                    IsBrakeChecked = false;
                }
                else if (strBrake == "1")
                {
                    IsBrakeChecked = true;
                }

                BrakeReleaseDelayTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Brake Release Delay Time", "Default");
                BrakeActiveDelayTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Brake Active Delay Time", "Default");
                BrakeActiveVelocity = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Brake Active Velocity", "Default");
                BrakeActiveAllowedDelayTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Brake Active Allowed Delay Time", "Default");

                //停机方式
                SelectedOverTravelStopMode = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Over Travel Stop Mode", "Default");
                SelectedForceStopMode = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Force Stop Mode", "Default");
                SelectedTwoFaultStopMode = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Alarm Stop Mode", "Default");
                SelectedOneFaultStopMode = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Fault No.1 Stop Mode", "Default");

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_GET_CONFIG_FROM_DATATABLE, "GetNormalSettingConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ComboBoxInitialize
        //函数功能：属性初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.13
        //*************************************************************************
        private void ComboBoxInitialize()
        {
            try
            {
                OneFaultStopMode = new ObservableCollection<string>() { "DB停机，保持DB状态", "DB停机，保持自由状态", "自由停机，保持自由状态" };
                SelectedOneFaultStopMode = "DB停机，保持DB状态";

                TwoFaultStopMode = new ObservableCollection<string>() { "使用一级故障停机方式", "零速度停机，DB状态", "零速度停机，自由状态", "减速停机，DB状态", "减速停机，自由状态" };
                SelectedTwoFaultStopMode = "使用一级故障停机方式 ";

                ForceStopMode = new ObservableCollection<string>() { "使用一级故障停机方式", "零速度停机，DB状态", "零速度停机，自由状态", "减速停机，DB状态", "减速停机，自由状态" };
                SelectedForceStopMode = "使用一级故障停机方式";

                OverTravelStopMode = new ObservableCollection<string>() { "使用一级故障停机方式", "零速度停机，伺服锁定", "零速度停机，自由状态", "减速停机，伺服锁定", "减速停机，自由状态" };
                SelectedOverTravelStopMode = "使用一级故障停机方式";
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMAL_SETTING_INITIALIZE, "ComboBoxInitialize", ex);
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2019.11.18&2023.11.13
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                //正反转
                IsCWChecked = GlobalCurrentInput.IsCWChecked;
                IsCCWChecked = GlobalCurrentInput.IsCCWChecked;

                //抱闸
                IsBrakeChecked = GlobalCurrentInput.IsBrakeChecked;
                if (IsBrakeChecked)
                {
                    Hint_BrakeEnable = "抱闸制动功能开";
                }
                else
                {
                    Hint_BrakeEnable = "抱闸制动功能关";
                }

                BrakeReleaseDelayTime = GlobalCurrentInput.BrakeReleaseDelayTime;//抱闸释放延时时间
                BrakeActiveDelayTime = GlobalCurrentInput.BrakeActiveDelayTime;//抱闸制动延时时间
                BrakeActiveVelocity = GlobalCurrentInput.BrakeActiveVelocity;//抱闸制动速度门限
                BrakeActiveAllowedDelayTime = GlobalCurrentInput.BrakeActiveAllowedDelayTime;//抱闸制动允许延时时间

                //停机方式，由Lilbert于2023.11.13添加停机方式
                SelectedOverTravelStopMode = GlobalCurrentInput.SelectedOverTravelStopMode;//超程停机方式
                SelectedForceStopMode = GlobalCurrentInput.SelectedForceStopMode;//强制停机方式
                SelectedTwoFaultStopMode = GlobalCurrentInput.SelectedTwoFaultStopMode;//二级故障停机方式
                SelectedOneFaultStopMode = GlobalCurrentInput.SelectedOneFaultStopMode;//一级故障停机方式
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }
   
        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.13
        //*************************************************************************
        private int AddParameterInfoDictionary(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {           
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                if (strCategory == "0")
                {
                    if (IsBrakeChecked)
                    {
                        dicParameterInfo.Add("Brake Enable", "1");
                    }
                    else
                    {
                        dicParameterInfo.Add("Brake Enable", "0");
                    }

                    dicParameterInfo.Add("Brake Release Delay Time", BrakeReleaseDelayTime);
                    dicParameterInfo.Add("Brake Active Delay Time", BrakeActiveDelayTime);
                    dicParameterInfo.Add("Brake Active Velocity", BrakeActiveVelocity);
                    dicParameterInfo.Add("Brake Active Allowed Delay Time", BrakeActiveAllowedDelayTime);
                }
                else if (strCategory == "1")
                {
                    if (IsCCWChecked && !IsCWChecked)
                    {
                        dicParameterInfo.Add("Rotate Direction", "0");
                    }
                    else if (!IsCCWChecked && IsCWChecked)
                    {
                        dicParameterInfo.Add("Rotate Direction", "1");
                    }
                    else
                    {
                        dicParameterInfo.Add("Rotate Direction", "0");
                    }
                }                
                else
                {
                    //抱闸
                    if (IsBrakeChecked)
                    {
                        dicParameterInfo.Add("Brake Enable", "1");
                    }
                    else
                    {
                        dicParameterInfo.Add("Brake Enable", "0");
                    }

                    dicParameterInfo.Add("Brake Release Delay Time", BrakeReleaseDelayTime);
                    dicParameterInfo.Add("Brake Active Delay Time", BrakeActiveDelayTime);
                    dicParameterInfo.Add("Brake Active Velocity", BrakeActiveVelocity);
                    dicParameterInfo.Add("Brake Active Allowed Delay Time", BrakeActiveAllowedDelayTime);

                    //正反转
                    if (IsCCWChecked && !IsCWChecked)
                    {
                        dicParameterInfo.Add("Rotate Direction", "0");
                    }
                    else if (!IsCCWChecked && IsCWChecked)
                    {
                        dicParameterInfo.Add("Rotate Direction", "1");
                    }
                    else
                    {
                        dicParameterInfo.Add("Rotate Direction", "0");
                    }
                    
                }
             
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary_ForStopMode
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.11.14
        //*************************************************************************
        private int AddParameterInfoDictionary_ForStopMode(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            string AlarmStopMode = null;//停机模式

            string strOverTravelStopMode = null;
            string strForceStopMode = null;
            string strTwoFaultStopMode = null;
            string strOneFaultStopMode = null;
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                //一级故障停机方式
                switch (SelectedOneFaultStopMode)
                {
                    case "DB停机，保持DB状态":
                        strOneFaultStopMode = "0";
                        break;
                    case "DB停机，保持自由状态":
                        strOneFaultStopMode = "1";
                        break;
                    case "自由停机，保持自由状态":
                        strOneFaultStopMode = "2";
                        break;
                    default:
                        break;
                }                                

                //二级故障停机方式
                switch (SelectedTwoFaultStopMode)
                {
                    case "使用一级故障停机方式":
                        strTwoFaultStopMode = "0";
                        break;
                    case "零速度停机，DB状态":
                        strTwoFaultStopMode = "1";
                        break;
                    case "零速度停机，自由状态":
                        strTwoFaultStopMode = "2";
                        break;
                    case "减速停机，DB状态":
                        strTwoFaultStopMode = "3";
                        break;
                    case "减速停机，自由状态":
                        strTwoFaultStopMode = "4";
                        break;
                    default:
                        break;
                }                

                //强制停机方式
                switch (SelectedForceStopMode)
                {
                    case "使用一级故障停机方式":
                        strForceStopMode = "0";
                        break;
                    case "零速度停机，DB状态":
                        strForceStopMode = "1";
                        break;
                    case "零速度停机，自由状态":
                        strForceStopMode = "2";
                        break;
                    case "减速停机，DB状态":
                        strForceStopMode = "3";
                        break;
                    case "减速停机，自由状态":
                        strForceStopMode = "4";
                        break;
                    default:
                        break;
                }
                
                //超程停机方式
                switch (SelectedOverTravelStopMode)
                {
                    case "使用一级故障停机方式":
                        strOverTravelStopMode = "0";
                        break;
                    case "零速度停机，伺服锁定":
                        strOverTravelStopMode = "1";
                        break;
                    case "零速度停机，自由状态":
                        strOverTravelStopMode = "2";
                        break;
                    case "减速停机，伺服锁定":
                        strOverTravelStopMode = "3";
                        break;
                    case "减速停机，自由状态":
                        strOverTravelStopMode = "4";
                        break;
                    default:
                        break;
                }

                int Sum = (Convert.ToInt32(strOneFaultStopMode) & 0x000F)
                    + ((Convert.ToInt32(strTwoFaultStopMode) & 0x000F) << 4)
                    + ((Convert.ToInt32(strForceStopMode) & 0x000F) << 8)
                    + ((Convert.ToInt32(strOverTravelStopMode) & 0x000F) << 12);                

                AlarmStopMode = Convert.ToString(Sum);
                //停机方式,由Lilbert于2023.11.13添加停机方式
                dicParameterInfo.Add("Alarm Stop Mode", AlarmStopMode);
                //dicParameterInfo.Add("Over Travel Stop Mode", strOverTravelStopMode);
                //dicParameterInfo.Add("Force Stop Mode", strForceStopMode);
                //dicParameterInfo.Add("Alarm Stop Mode", strTwoFaultStopMode);
                //dicParameterInfo.Add("Fault No.1 Stop Mode", strOneFaultStopMode);

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.NORMALSETTING_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary_ForStopMode", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }
}