﻿<UserControl x:Class="ServoStudio.Views.PositionLoopView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"     
             xmlns:lc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"
             xmlns:Views="clr-namespace:ServoStudio.Views"
             xmlns:converter="clr-namespace:Converter"
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:PositionLoopViewModel}"
             d:DesignHeight="800" d:DesignWidth="1300">
    <UserControl.Resources>
        <converter:BackgroundConverter x:Key="BackgroudConverter"/>
        <converter:VisibilityConverter x:Key="VisibilityConverter"/>
        <converter:ContentConverter x:Key="ContentConverter"/>
    </UserControl.Resources>

    <dxmvvm:Interaction.Behaviors>
        <dxmvvm:EventToCommand Command="{Binding PositionLoopLoadedCommand}"/>
        <dxmvvm:NotificationService x:Name="ServiceWithDefaultNotifications" ApplicationId="ServoStudio" PredefinedNotificationTemplate="ShortHeaderAndTwoTextFields" UseWin8NotificationsIfAvailable="True"/>
    </dxmvvm:Interaction.Behaviors>

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="275"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Label Grid.Row="0" Margin="3" Style="{StaticResource LabelStyle}" Content="位置环图示 — 点击图例可设定相应参数" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Canvas Grid.Row="1">
                <Label Content="位置指令" Canvas.Left="12" Canvas.Top="188"/>
                <Line Canvas.Top="182" Canvas.Left="43" Style="{StaticResource Line}"/>
                <Ellipse Canvas.Top="176" Canvas.Left="31" Style="{StaticResource StartDot}"/>

                <Line Y2="100" Canvas.Top="82" Canvas.Left="177" Style="{StaticResource VerticalLine}"/>
                <Line X2="77" Canvas.Top="82" Canvas.Left="177" Style="{StaticResource Line}"/>
                <Line Canvas.Top="82" Canvas.Left="343" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="82" Canvas.Left="343" Style="{StaticResource RDArrow}"/>
                <Label Content="d/dt" Style="{StaticResource Function}" Canvas.Left="256" Canvas.Top="64" Width="60"/>

                <Line Canvas.Top="82" Canvas.Left="316" Style="{StaticResource Line}"/>
                <Line Canvas.Top="82" Canvas.Left="247" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="82" Canvas.Left="247" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="352" Canvas.Top="64" Name="labelA" Content="平滑常数" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown"  Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=labelA}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Canvas.Top="82" Canvas.Left="428" Style="{StaticResource Line}"/>
                <Line Canvas.Top="82" Canvas.Left="455" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="82" Canvas.Left="455" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="464" Canvas.Top="64" Name="labelB" Content="前馈增益" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown"  Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=labelB}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line X2="20" Canvas.Top="82" Canvas.Left="540" Style="{StaticResource Line}" />
                <Line Y2="20" Canvas.Top="61" Canvas.Left="560" Style="{StaticResource VerticalLine}"/>
                <Line X2="20" Canvas.Top="61" Canvas.Left="560" Style="{StaticResource Line}"/>
                <Line Canvas.Top="61" Canvas.Left="573" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="61" Canvas.Left="573" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="582" Canvas.Top="31" Name="labelC" Content="前馈选择" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=labelC}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Y2="20" Canvas.Top="17" Canvas.Left="559" Style="{StaticResource VerticalLine}"/>
                <Line X2="20" Canvas.Top="37" Canvas.Left="559" Style="{StaticResource Line}"/>
                <Line Canvas.Top="37" Canvas.Left="573" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="37" Canvas.Left="573" Style="{StaticResource RDArrow}"/>

                <Line X2="516" Canvas.Top="17" Canvas.Left="43" Style="{StaticResource Line}"/>
                <Ellipse Canvas.Top="12" Canvas.Left="31" Style="{StaticResource StartDot}"/>
                <Label Content="速度前馈" Canvas.Left="12" Canvas.Top="23"/>

                <Line Canvas.Top="181" Canvas.Left="397" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="181" Canvas.Left="397" Style="{StaticResource RDArrow}"/>
                <Line X2="166" Canvas.Top="181" Canvas.Left="239" Style="{StaticResource Line}"/>
                <Label Canvas.Left="406" Canvas.Top="164" Name="labelD" Content="位置环增益" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content,ElementName=labelD}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>

                <Line Canvas.Top="49" Canvas.Left="658" Style="{StaticResource Line}"/>

                <Line Canvas.Top="181" Canvas.Left="666" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="181" Canvas.Left="666" Style="{StaticResource RDArrow}"/>
                <Line X2="190" Canvas.Top="181" Canvas.Left="482" Style="{StaticResource Line}"/>

                <Line Canvas.Top="181" Canvas.Left="710" Style="{StaticResource Line}"/>
                <Line Canvas.Top="181" Canvas.Left="739" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="181" Canvas.Left="739" Style="{StaticResource RDArrow}"/>
                <Ellipse Canvas.Top="175" Canvas.Left="748" Style="{StaticResource EndDot}"/>
                <Label Content="速度指令" Canvas.Left="730" Canvas.Top="186"/>

                <Ellipse Canvas.Top="254" Canvas.Left="351" Style="{StaticResource StartDot}" />
                <Label Content="位置反馈" Canvas.Left="334" Canvas.Top="232"/>

                <Line X2="130" Canvas.Top="260" Canvas.Left="220" Style="{StaticResource Line}"/>

                <Line Y2="60" Canvas.Top="200" Canvas.Left="220" Style="{StaticResource VerticalLine}"/>
                <Line Canvas.Top="208" Canvas.Left="220" Style="{StaticResource ULArrow}"/>
                <Line Canvas.Top="208" Canvas.Left="220" Style="{StaticResource URArrow}"/>

                <Ellipse Canvas.Top="164" Canvas.Left="203" Style="{StaticResource OperatorProfile}"/>
                <Label Content="+" Canvas.Left="181" Canvas.Top="177" Style="{StaticResource Operator}"/>
                <Label Content="-" Canvas.Left="209" Canvas.Top="196" Style="{StaticResource Operator}"/>
                <Label Content="Σ" Canvas.Left="216" Canvas.Top="168" Style="{StaticResource Operator}"/>

                <Ellipse Canvas.Top="163" Canvas.Left="675" Style="{StaticResource OperatorProfile}"/>
                <Label Content="Σ" Canvas.Left="688" Canvas.Top="167" Style="{StaticResource Operator}"/>
                <Label Content="+" Canvas.Left="651" Canvas.Top="158" Style="{StaticResource Operator}"/>
                <Label Content="+" Canvas.Left="679" Canvas.Top="131" Style="{StaticResource Operator}"/>
                <Label Canvas.Left="79" Canvas.Top="165" x:Name="labelE" Content="位置指令滤波"  Style="{StaticResource BackgroundSwitch}" FontSize="10.667">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content, ElementName=labelE}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>
                <Line Canvas.Top="182" Canvas.Left="70" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="182" Canvas.Left="70" Style="{StaticResource RDArrow}"/>
                <Line X2="46" Canvas.Top="182" Canvas.Left="155" Style="{StaticResource Line}"/>
                <Line Canvas.Top="182" Canvas.Left="194" Style="{StaticResource RUArrow}"/>
                <Line Canvas.Top="182" Canvas.Left="194" Style="{StaticResource RDArrow}"/>
                <Label Canvas.Left="656" Canvas.Top="90" x:Name="labelF" Content="平均值滤波" Style="{StaticResource BackgroundSwitch}">
                    <dxmvvm:Interaction.Behaviors>
                        <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding ChangeBackgroundCommand}" CommandParameter="{Binding Content, ElementName=labelF}"/>
                    </dxmvvm:Interaction.Behaviors>
                </Label>
                <Line Y2="40" Canvas.Top="49" Canvas.Left="693" Style="{StaticResource VerticalLine}"/>
                <Line Canvas.Top="89" Canvas.Left="693" Style="{StaticResource DLArrow}"/>
                <Line Canvas.Top="89" Canvas.Left="693" Style="{StaticResource DRArrow}"/>
                <Line Y2="35" Canvas.Top="126" Canvas.Left="693" Style="{StaticResource VerticalLine}"/>
                <Line Canvas.Top="162" Canvas.Left="693" Style="{StaticResource DLArrow}"/>
                <Line Canvas.Top="162" Canvas.Left="693" Style="{StaticResource DRArrow}"/>
            </Canvas>

            <Label Grid.Row="2" Margin="3" Style="{StaticResource LabelStyle}" Content="位置环参数设置" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <Grid Grid.Row="3">
                <Grid.RowDefinitions>
                    <RowDefinition Height="9"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="170"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="40"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="170"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="40"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="170"/>
                    <ColumnDefinition Width="80"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Label Grid.Row="1" Grid.Column="1" Margin="10,9" Content="速度前馈选择" Style="{StaticResource LabelStyle}" />
                <dxe:ComboBoxEdit Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9"  Width="Auto" Style="{StaticResource SettingComboBoxStyle}" ItemsSource="{Binding SpdFFControlSelect}" SelectedIndex="{Binding SelectedSpdFFControlSelectIndex, Mode=TwoWay}" Background="{Binding SpdFFControlSelectBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="1" Grid.Column="5" Margin="10,9" Content="位置环增益" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="1" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding PositionLoopGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding PositionLoopGainBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="1" Grid.Column="7" Text="0.1Hz" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding PositionLoopGainBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="1" Grid.Column="9" Margin="10,9" Content="位置指令平均滤波时间" Style="{StaticResource LabelStyle}" />
                <TextBox Grid.Row="1" Grid.Column="10" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding PositionReferenceMaFilterTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding PositionReferenceMaFilterTimeBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="1" Grid.Column="11" Text="0.1ms" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding PositionReferenceMaFilterTimeBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="2" Grid.Column="1" Margin="10,9" Content="速度前馈滤波时间常数" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding SpdFFFilterFimeConstant,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding SpdFFFilterFimeConstantBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="2" Grid.Column="3" Text="0.01ms" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding SpeedLoopGainBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="2" Grid.Column="5" Margin="10,9" Content="速度前馈增益" Style="{StaticResource LabelStyle}" />
                <TextBox Grid.Row="2" Grid.Column="6" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding SpdFFGain,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding SpdFFGainBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="2" Grid.Column="7" Text="1%" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding SpdFFGainBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="2" Grid.Column="9" Margin="10,9" Content="位置指令平滑比" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="2" Grid.Column="10" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding PositionReferenceHighFilterRatio,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding PositionReferenceHighFilterRatioBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="2" Grid.Column="11" Text="1%" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding PositionReferenceHighFilterRatioBackground,Converter={StaticResource BackgroudConverter}}"/>

                <Label Grid.Row="3" Grid.Column="1" Margin="10,9" Content="速度前馈平均滤波时间" Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Margin="10,9" Style="{StaticResource TextBoxStyle}" Text="{Binding SpeedFeedForwardMaFilterTime,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Background="{Binding SpeedFeedForwardMaFilterTimeBackground,Converter={StaticResource BackgroudConverter}}"/>
                <TextBox Grid.Row="3" Grid.Column="3" Text="0.1ms" Style="{StaticResource TextBoxStyle_Unit}" Background="{Binding SpeedFeedForwardMaFilterTimeBackground,Converter={StaticResource BackgroudConverter}}"/>
            </Grid>

            <Label Grid.Row="4" Margin="3,3,3,10" Style="{StaticResource LabelStyle}" Content="设置执行" BorderThickness="0,0,0,1" BorderBrush="LightGray" FontWeight="Bold"/>

            <StackPanel Grid.Row="5" HorizontalAlignment="Right" Orientation="Horizontal">
                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Refresh_16x16.png}" Command="{Binding ReadPositionLoopParameterCommand}">
                    <Label Content="刷新参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0,0,10,0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=Group_16x16.png}" Command="{Binding GetDefaultPositionLoopParameterCommand}">
                    <Label Content="默认参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>

                <dx:SimpleButton Margin="0" Style="{StaticResource ButtonStyle}" Glyph="{dx:DXImageOffice2013 Image=MoveDown_16x16.png}" Command="{Binding WritePositionLoopParameterCommand}">
                    <Label Content="下载参数" Style="{StaticResource LabelStyle}" Margin="0"/>
                </dx:SimpleButton>
            </StackPanel>

            <!--<Grid Grid.Row="7">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="1" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=Previous_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding GetPositionLoopConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="2" FontSize="12" Content="加载" Style="{StaticResource LabelStyle}" />

            <dxe:ImageEdit Grid.Row="0" Grid.RowSpan="2" Grid.Column="3" Margin="5,0" ShowMenu="False" Source="{dx:DXImageOffice2013 Image=NavigateNext_32x32.png}" ShowBorder="False">
                <dxmvvm:Interaction.Behaviors>
                    <dxmvvm:EventToCommand EventName="MouseDown" Command="{Binding SavePositionLoopConfigFileCommand}"/>
                </dxmvvm:Interaction.Behaviors>
            </dxe:ImageEdit>
            <Label Grid.Row="1" Grid.Column="4" FontSize="12" Content="保存" Style="{StaticResource LabelStyle}"/>
        </Grid>-->
        </Grid>

    </ScrollViewer>
       
</UserControl>
