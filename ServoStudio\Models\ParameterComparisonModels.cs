using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace ServoStudio.Models
{
    /// <summary>
    /// 参数差异类型枚举
    /// </summary>
    public enum DifferenceType
    {
        /// <summary>
        /// 参数值不同
        /// </summary>
        ValueDifferent,
        
        /// <summary>
        /// 新参数（导入文件中有，设备中没有）
        /// </summary>
        NewParameter,
        
        /// <summary>
        /// 缺失参数（设备中有，导入文件中没有）
        /// </summary>
        MissingParameter
    }

    /// <summary>
    /// 参数写入状态枚举
    /// </summary>
    public enum WriteStatus
    {
        /// <summary>
        /// 等待写入
        /// </summary>
        Pending,
        
        /// <summary>
        /// 正在写入
        /// </summary>
        Writing,
        
        /// <summary>
        /// 写入成功
        /// </summary>
        Success,
        
        /// <summary>
        /// 写入失败
        /// </summary>
        Failed
    }

    /// <summary>
    /// 参数差异数据模型
    /// </summary>
    public class ParameterDifference : INotifyPropertyChanged
    {
        private bool _isSelected;
        private WriteStatus _writeStatus = WriteStatus.Pending;
        private string _writeMessage = string.Empty;

        /// <summary>
        /// 参数索引（地址）
        /// </summary>
        public string Index { get; set; }

        /// <summary>
        /// 参数名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 当前设备中的参数值
        /// </summary>
        public string CurrentValue { get; set; }

        /// <summary>
        /// 导入文件中的参数值
        /// </summary>
        public string ImportValue { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 参数单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 最小值
        /// </summary>
        public string Min { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public string Max { get; set; }

        /// <summary>
        /// 默认值
        /// </summary>
        public string Default { get; set; }

        /// <summary>
        /// 读写属性（RO/RW）
        /// </summary>
        public string RWProperty { get; set; }

        /// <summary>
        /// 参数分类
        /// </summary>
        public string Classification { get; set; }

        /// <summary>
        /// 参数注释
        /// </summary>
        public string Comment { get; set; }

        /// <summary>
        /// 是否选中（用于写入）
        /// </summary>
        public bool IsSelected
        {
            get { return _isSelected; }
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        /// <summary>
        /// 是否为只读参数
        /// </summary>
        public bool IsReadOnly => RWProperty == "RO";

        /// <summary>
        /// 差异类型
        /// </summary>
        public DifferenceType Type { get; set; }

        /// <summary>
        /// 写入状态
        /// </summary>
        public WriteStatus WriteStatus
        {
            get { return _writeStatus; }
            set
            {
                if (_writeStatus != value)
                {
                    _writeStatus = value;
                    OnPropertyChanged(nameof(WriteStatus));
                }
            }
        }

        /// <summary>
        /// 写入消息（成功或错误信息）
        /// </summary>
        public string WriteMessage
        {
            get { return _writeMessage; }
            set
            {
                if (_writeMessage != value)
                {
                    _writeMessage = value;
                    OnPropertyChanged(nameof(WriteMessage));
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 参数对比结果
    /// </summary>
    public class ComparisonResult
    {
        /// <summary>
        /// 参数差异列表
        /// </summary>
        public List<ParameterDifference> Differences { get; set; } = new List<ParameterDifference>();

        /// <summary>
        /// 对比时间
        /// </summary>
        public DateTime ComparisonTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 导入文件路径
        /// </summary>
        public string ImportFilePath { get; set; }

        /// <summary>
        /// 导入参数总数
        /// </summary>
        public int ImportParameterCount { get; set; }

        /// <summary>
        /// 当前设备参数总数
        /// </summary>
        public int CurrentParameterCount { get; set; }

        /// <summary>
        /// 差异参数数量
        /// </summary>
        public int DifferenceCount => Differences.Count;

        /// <summary>
        /// 值不同的参数数量
        /// </summary>
        public int ValueDifferentCount => Differences.Count(d => d.Type == DifferenceType.ValueDifferent);

        /// <summary>
        /// 新参数数量
        /// </summary>
        public int NewParameterCount => Differences.Count(d => d.Type == DifferenceType.NewParameter);

        /// <summary>
        /// 缺失参数数量
        /// </summary>
        public int MissingParameterCount => Differences.Count(d => d.Type == DifferenceType.MissingParameter);
    }

    /// <summary>
    /// 参数写入进度信息
    /// </summary>
    public class ParameterWriteProgress
    {
        /// <summary>
        /// 当前写入的参数序号
        /// </summary>
        public int Current { get; set; }

        /// <summary>
        /// 总参数数量
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 当前写入的参数索引
        /// </summary>
        public string ParameterIndex { get; set; }

        /// <summary>
        /// 当前写入的参数名称
        /// </summary>
        public string ParameterName { get; set; }

        /// <summary>
        /// 写入进度百分比
        /// </summary>
        public int Percentage { get; set; }

        /// <summary>
        /// 写入状态
        /// </summary>
        public WriteStatus Status { get; set; }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 写入开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 预计剩余时间（秒）
        /// </summary>
        public int EstimatedRemainingSeconds { get; set; }
    }

    /// <summary>
    /// 参数对比配置
    /// </summary>
    public class ComparisonConfiguration
    {
        /// <summary>
        /// 是否启用参数对比功能
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否自动选择所有差异参数
        /// </summary>
        public bool AutoSelectDifferences { get; set; } = false;

        /// <summary>
        /// 是否显示只读参数
        /// </summary>
        public bool ShowReadOnlyParameters { get; set; } = true;

        /// <summary>
        /// 是否显示相同的参数
        /// </summary>
        public bool ShowIdenticalParameters { get; set; } = false;

        /// <summary>
        /// 参数写入超时时间（毫秒）
        /// </summary>
        public int WriteTimeoutMs { get; set; } = 5000;

        /// <summary>
        /// 是否在写入前进行确认
        /// </summary>
        public bool ConfirmBeforeWrite { get; set; } = true;

        /// <summary>
        /// 是否记录详细日志
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = true;
    }

    /// <summary>
    /// 参数对比统计信息
    /// </summary>
    public class ComparisonStatistics
    {
        /// <summary>
        /// 对比开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 对比结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 对比耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds => (EndTime - StartTime).Milliseconds;

        /// <summary>
        /// 读取设备参数耗时（毫秒）
        /// </summary>
        public long ReadDeviceParametersMs { get; set; }

        /// <summary>
        /// 读取导入文件耗时（毫秒）
        /// </summary>
        public long ReadImportFileMs { get; set; }

        /// <summary>
        /// 参数对比计算耗时（毫秒）
        /// </summary>
        public long ComparisonCalculationMs { get; set; }

        /// <summary>
        /// 是否成功完成对比
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}