﻿using ServoStudio.GlobalConstant;
using ServoStudio.GlobalMethod;
using ServoStudio.ViewModels;
using ServoStudio.Views;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ServoStudio.Models
{
    public static class OscilloscopeModel
    {
        //*************************************************************************
        //函数名称：DynamicXStartPoint
        //函数功能：波形流动的X起点坐标
        //函数用途：示波器展示的波形超过界面范围以后，开始流动展示
        //
        //输入参数：List<ChannelInfo> In_lstChannelInfo  通道信息   
        //         ref double Out_dXPoint               动态X起点坐标
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2019.11.27
        //*************************************************************************
        public static void DynamicXStartPoint(List<ChannelInfo> In_lstChannelInfo, ref double Out_dXPoint)
        {
            try
            {
                if (In_lstChannelInfo == null)
                {
                    Out_dXPoint = 0;
                }
                else if (In_lstChannelInfo.Count == 0)
                {
                    Out_dXPoint = 0;
                }
                else
                {
                    double itemp = In_lstChannelInfo[1].XValue - Oscilloscope.DynamicDisplayPoint * OthersHelper.GetAcquisitionPeriod(ViewModelSet.Oscilloscope.SelectedSamplingPeriod);
                    if (itemp > 0)
                    {
                        Out_dXPoint = itemp;
                    }
                    else
                    {
                        Out_dXPoint = 0;
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_DYNAMIC_X_START_POINT, "DynamicXStartPoint", ex);
            }
        }

        //*************************************************************************
        //函数名称：DynamicYMaxPoint
        //函数功能：y轴最大数据坐标
        //函数用途：示波器展示的波形超过界面范围以后，开始流动展示
        //
        //输入参数：List<ChannelInfo> In_lstChannelInfo  通道信息   
        //         ref double Out_dYPoint               动态y轴最大数据坐标
        //       
        //输出参数：NONE
        //        
        //编码作者：Ryan
        //更新时间：2019.11.27
        //*************************************************************************
        public static void DynamicYMaxPoint(List<ChannelInfo> In_lstChannelInfo, ref double Out_dMax, ref double Out_dMin)
        {
            int iTimes = 1;
            List<double> lstMax = new List<double>();
            List<double> lstMin = new List<double>();
    
            try
            {
                if (In_lstChannelInfo == null)
                {
                    Out_dMax = 0;
                    Out_dMin = 0;
                }
                else if (In_lstChannelInfo.Count == 0)
                {
                    Out_dMax = 0;
                    Out_dMin = 0;
                }
                else
                {
                    for (int i = 1; i < In_lstChannelInfo.Count; i++)
                    {
                        if (In_lstChannelInfo[i].IsUsed == false)
                        {
                            continue;
                        }

                        //超出部分出队
                        if (In_lstChannelInfo[i].YValueQueue.Count >= Oscilloscope.QueueLength)
                        {
                            In_lstChannelInfo[i].YValueQueue.Dequeue();
                        }

                        //入队
                        In_lstChannelInfo[i].YValueQueue.Enqueue(Math.Abs(In_lstChannelInfo[i].YValue));

                        //获取最大值、最小值集合                    
                        lstMax.Add((double)In_lstChannelInfo[i].YValueQueue.ToArray().Max());
                        lstMin.Add((double)In_lstChannelInfo[i].YValueQueue.ToArray().Min());
                    }

                    //从最值集合中获取最大、最小值
                    Out_dMax = lstMax.Max();
                    Out_dMin = lstMin.Min();

                    //获取旷量倍数
                    iTimes = Convert.ToString((Int64)(Out_dMax - Out_dMin)).Length;

                    Out_dMax += Math.Pow(Oscilloscope.Vacant, iTimes);
                    Out_dMin -= Math.Pow(Oscilloscope.Vacant, iTimes);       
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_DYNAMIC_Y_MAX_POINT, "DynamicYMaxPoint", ex);
            }
        }

        //*************************************************************************
        //函数名称：CheckSamplingParameterCorrected
        //函数功能：判断采样参数设置是否正确
        //
        //输入参数：string In_strPeriod      采样周期   
        //         string In_strDuration    采样时长
        //       
        //输出参数：0：NO_EFFECT
        //         1: SUCCEED
        //        -1: WRONG
        //        
        //编码作者：Ryan
        //更新时间：2019.11.28
        //*************************************************************************
        public static int CheckSamplingParameterCorrected(string In_strPeriod, string In_strDuration, string In_strContinuousSampling)
        {
            int iRet = -1;
            int iIndex = -1;
            int iPeriod = -1;
            int iDuration = -1;

            try
            {
                if (string.IsNullOrEmpty(In_strPeriod) || string.IsNullOrEmpty(In_strDuration) || string.IsNullOrEmpty(In_strContinuousSampling))
                    return RET.SUCCEEDED;
                else if (In_strContinuousSampling == "是")
                    return RET.SUCCEEDED;

                iIndex = In_strPeriod.IndexOf("m");     
                iPeriod = Convert.ToInt32(In_strPeriod.Remove(iIndex, 2));

                iIndex = In_strDuration.IndexOf("m");
                iDuration = Convert.ToInt32(In_strDuration.Remove(iIndex, 2));
                
                if (iDuration/iPeriod == 0)
                {
                    iRet = RET.NO_EFFECT;
                }    
                else
                {
                    iRet = RET.SUCCEEDED;
                }
            }
            catch (System.Exception ex)
            {
                iRet = RET.ERROR;
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_CHECK_SAMPLING_PARAMETER_CORRECTED, "CheckSamplingParameterCorrected", ex);
            }

            return iRet;
        }

        //*************************************************************************
        //函数名称：GetSamplingPeriodAndPoints
        //函数功能：获取微秒级采样周期与采集点数
        //
        //输入参数：string In_strPeriod      采样周期   
        //         string In_strDuration    采样时长
        //         ref int Out_iPeriod      微秒级采样周期
        //         ref int Out_iPoints      采样点数
        //       
        //输出参数：0：NO_EFFECT
        //         1: SUCCEED
        //        -1: WRONG
        //        
        //编码作者：Ryan
        //更新时间：2019.11.28
        //*************************************************************************
        public static int GetSamplingPeriodAndPoints(string In_strPeriod, string In_strDuration, string In_strContinuousSampling, ref int Out_iPeriod, ref int Out_iPoints)
        {
            int iIndex = -1;
            int iPeriod = -1;
            int iDuration = -1;

            try
            {
                if (string.IsNullOrEmpty(In_strPeriod) || string.IsNullOrEmpty(In_strDuration) || string.IsNullOrEmpty(In_strContinuousSampling))
                    return RET.NO_EFFECT;
               
                iIndex = In_strPeriod.IndexOf("m");
                iPeriod = Convert.ToInt32(In_strPeriod.Remove(iIndex, 2));

                iIndex = In_strDuration.IndexOf("m");
                iDuration = Convert.ToInt32(In_strDuration.Remove(iIndex, 2));

                Out_iPeriod = iPeriod * 1000;//微秒量级

                if (In_strContinuousSampling == "是")
                    Out_iPoints = Oscilloscope.Collection; 
                else
                    Out_iPoints = iDuration / iPeriod;

                if (Out_iPoints == 0)        
                    return RET.NO_EFFECT;
                else
                    return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_GET_SAMPLING_PERIOD_AND_POINTS, "GetSamplingPeriodAndPoints", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：StaticYMaxminPoint
        //函数功能：获取Y轴最值
        //
        //输入参数：List<ChannelInfo> In_lstChannelInfo      参数集合
        //         ref int iYMax                            Y最大值
        //         ref int iYMin                            Y最小值
        //       
        //输出参数：0: NO_EFFECT
        //         1: SUCCEED
        //        -1: ERROR
        //        
        //编码作者：Ryan
        //更新时间：2020.02.27
        //*************************************************************************
        public static int StaticYMaxminPoint(List<ChannelInfo> In_lstChannelInfo, ref Int64 iYMax, ref Int64 iYMin)
        {
            Int64 iMin = 0;
            Int64 iMax = 0;
            List<Int64> lstMax = new List<Int64>();
            List<Int64> lstMin = new List<Int64>();

            try
            {
                for (int i = 0; i < In_lstChannelInfo.Count; i++)
                {
                    if (In_lstChannelInfo[i].Oscilloscope.Count == 0)
                    {
                        continue;
                    }
                    else
                    {
                        iMin = Convert.ToInt64(In_lstChannelInfo[i].Oscilloscope.Min(t => t.Value));
                        lstMin.Add(iMin);

                        iMax = Convert.ToInt64(In_lstChannelInfo[i].Oscilloscope.Max(t => t.Value));
                        lstMax.Add(iMax);
                    }
                }

                if (lstMin.Count != 0 && lstMax.Count == lstMin.Count)
                {
                    iYMin = lstMin.Min();
                    iYMax = lstMax.Max();

                }
                else
                {
                    iYMin = 0;
                    iYMax = 0;
                }

                //获取旷量倍数
                int iTimes = Convert.ToString((Int64)(iYMax - iYMin)).Length;
                iYMax += (long)Math.Pow(Oscilloscope.Vacant, iTimes);
                iYMin -= (long)Math.Pow(Oscilloscope.Vacant, iTimes);

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_STATIC_Y_MIN_POINT, "StaticYMinPoint", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：EvaluateLastWaveDataFromCurrent
        //函数功能：记录上一条数据
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.09
        //*************************************************************************
        public static void EvaluateLastWaveDataFromCurrent()
        {
            OfflineOscilloscope.Last.lstChannel1 = new List<int>();
            OfflineOscilloscope.Last.lstChannel2 = new List<int>();
            OfflineOscilloscope.Last.lstChannel3 = new List<int>();
            OfflineOscilloscope.Last.lstChannel4 = new List<int>();

            AcquisitionData.Channel1?.ForEach(item => OfflineOscilloscope.Last.lstChannel1.Add(item));
            AcquisitionData.Channel2?.ForEach(item => OfflineOscilloscope.Last.lstChannel2.Add(item));
            AcquisitionData.Channel3?.ForEach(item => OfflineOscilloscope.Last.lstChannel3.Add(item));
            AcquisitionData.Channel4?.ForEach(item => OfflineOscilloscope.Last.lstChannel4.Add(item));

            OfflineOscilloscope.Last.lstUnit = new List<string>();
            OfflineOscilloscope.Last.lstExchangeValue = new List<double>();

            AcquisitionInfoSet.lstUnit?.ForEach(item => OfflineOscilloscope.Last.lstUnit.Add(item));
            AcquisitionInfoSet.lstExchangeValue?.ForEach(item => OfflineOscilloscope.Last.lstExchangeValue.Add(item));
        }

        //*************************************************************************
        //函数名称：GetIndexOfArrayWaveData
        //函数功能：获取波形值对应的检索号
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.10
        //*************************************************************************
        public static string GetIndexOfArrayWaveData(double dValue)
        {
            int iRet = -1;
            double AcquisitionPeriod = 0;
            List<double> lstValue = new List<double>();

            try
            {
                if (AcquisitionData.Channel1 == null)
                {
                    return null;
                }

                AcquisitionPeriod = OthersHelper.GetAcquisitionPeriod(ViewModelSet.Oscilloscope?.SelectedSamplingPeriod);
                if (AcquisitionPeriod == RET.NO_EFFECT)
                {
                    return null;
                }

                for (int i = 0; i < AcquisitionData.Channel1.Count; i++)
                {
                    lstValue.Add(i * AcquisitionPeriod);
                }

                iRet = lstValue.IndexOf(lstValue.Where(item => item >= dValue).FirstOrDefault());
                if (iRet == RET.ERROR)
                {
                    return null;
                }
                else
                {
                    return iRet.ToString();
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_GET_INDEX_OF_ARRAY_WAVE_DATA, "GetIndexOfArrayWaveData", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetIndexOfArrayWaveData
        //函数功能：获取波形值对应的检索号
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.10
        //*************************************************************************
        public static int[] GetMaxminOfArrayWaveData(bool bMax, string strBeginIndex, string strEndIndex)
        {
            int iBeginIndex = 0;
            int iEndIndex = 0;
            int iLength = 0;
            int iIndex = 0;
            int[] arrValue = new int[4]; 

            try
            {
                if (AcquisitionData.Channel1 == null)
                {
                    return null;
                }

                if (string.IsNullOrEmpty(strBeginIndex) || string.IsNullOrEmpty(strEndIndex))
                {
                    return null;
                }
                else
                {
                    iBeginIndex = Convert.ToInt32(strBeginIndex);
                    iEndIndex = Convert.ToInt32(strEndIndex);
                    iLength = Math.Abs(iBeginIndex - iEndIndex) + 1;

                    if (iBeginIndex < iEndIndex)
                    {
                        iIndex = iBeginIndex;
                    }
                    else
                    {
                        iIndex = iEndIndex;
                    }
                }

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel1 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel1 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                {                                     
                    if (bMax)
                    {
                        arrValue[0] = AcquisitionData.Channel1.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[0] = AcquisitionData.Channel1.GetRange(iIndex, iLength).Min();
                    }                                     
                }

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel2 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel2 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    if (bMax)
                    {
                        arrValue[1] = AcquisitionData.Channel2.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[1] = AcquisitionData.Channel2.GetRange(iIndex, iLength).Min();
                    }                                      
                }                

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel3 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel3 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    if (bMax)
                    {
                        arrValue[2] = AcquisitionData.Channel3.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[2] = AcquisitionData.Channel3.GetRange(iIndex, iLength).Min();
                    }                      
                }              

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel4 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel4 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    if (bMax)
                    {
                        arrValue[3] = AcquisitionData.Channel4.GetRange(iIndex, iLength).Max();
                    }
                    else
                    {
                        arrValue[3] = AcquisitionData.Channel4.GetRange(iIndex, iLength).Min();
                    }                 
                }

                return arrValue;                                                   
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_GET_MAXMIN_OF_ARRAY_WAVE_DATA, "GetMaxminOfArrayWaveData", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetAverageOfArrayWaveData
        //函数功能：获取波形值平均值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.05.26
        //*************************************************************************
        public static double[] GetAverageOfArrayWaveData(string strBeginIndex, string strEndIndex)
        {
            int iBeginIndex = 0;
            int iEndIndex = 0;
            int iLength = 0;
            int iIndex = 0;
            double[] arrValue = new double[4];

            try
            {
                if (AcquisitionData.Channel1 == null)
                {
                    return null;
                }

                if (string.IsNullOrEmpty(strBeginIndex) || string.IsNullOrEmpty(strEndIndex))
                {
                    return null;
                }
                else
                {
                    iBeginIndex = Convert.ToInt32(strBeginIndex);
                    iEndIndex = Convert.ToInt32(strEndIndex);
                    iLength = Math.Abs(iBeginIndex - iEndIndex) + 1;

                    if (iBeginIndex < iEndIndex)
                    {
                        iIndex = iBeginIndex;
                    }
                    else
                    {
                        iIndex = iEndIndex;
                    }
                }

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel1 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel1 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    arrValue[0] = Math.Round(AcquisitionData.Channel1.GetRange(iIndex, iLength).Average(), 2);
                }

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel2 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel2 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    arrValue[1] = Math.Round(AcquisitionData.Channel2.GetRange(iIndex, iLength).Average(), 2);
                }

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel3 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel3 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    arrValue[2] = Math.Round(AcquisitionData.Channel3.GetRange(iIndex, iLength).Average(), 2);
                }

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel4 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel4 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                { 
                    arrValue[3] = Math.Round(AcquisitionData.Channel4.GetRange(iIndex, iLength).Average(), 2);
                }

                return arrValue;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_GET_AVERAGE_OF_ARRAY_WAVE_DATA, "GetAverageOfArrayWaveData", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetRMSOfArrayWaveData
        //函数功能：获取波形均方根
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.06.02
        //*************************************************************************
        public static double[] GetRMSOfArrayWaveData(string strBeginIndex, string strEndIndex)
        {
            int iRet = -1;
            int iBeginIndex = 0;
            int iEndIndex = 0;
            int iLength = 0;
            int iIndex = 0;
            double dRMS = 0;
            double[] arrValue = new double[4];

            try
            {
                if (AcquisitionData.Channel1 == null)
                {
                    return null;
                }

                if (string.IsNullOrEmpty(strBeginIndex) || string.IsNullOrEmpty(strEndIndex))
                {
                    return null;
                }
                else
                {
                    iBeginIndex = Convert.ToInt32(strBeginIndex);
                    iEndIndex = Convert.ToInt32(strEndIndex);
                    iLength = Math.Abs(iBeginIndex - iEndIndex) + 1;

                    if (iBeginIndex < iEndIndex)
                    {
                        iIndex = iBeginIndex;
                    }
                    else
                    {
                        iIndex = iEndIndex;
                    }
                }

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel1 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel1 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
                {
                    List<int> Channel1 = AcquisitionData.Channel1.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel1, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[0] = dRMS;
                    }                   
                }

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel2 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel2 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
                {
                    List<int> Channel2 = AcquisitionData.Channel2.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel2, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[1] = dRMS;
                    }
                }

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel3 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel3 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
                {
                    List<int> Channel3 = AcquisitionData.Channel3.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel3, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[2] = dRMS;
                    }
                }

                if (ViewModelSet.Oscilloscope.SelectedSamplingChannel4 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel4 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
                {
                    List<int> Channel4 = AcquisitionData.Channel4.GetRange(iIndex, iLength);
                    iRet = OthersHelper.GetRMSValue(Channel4, ref dRMS);
                    if (iRet == RET.SUCCEEDED)
                    {
                        arrValue[3] = dRMS;
                    }
                }

                return arrValue;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.OSCILLOSCOPE_GET_RMS_OF_ARRAY_WAVE_DATA, "GetRMSOfArrayWaveData", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：AddCalculateSet
        //函数功能：添加计算集合
        //
        //输入参数：
        //         
        //输出参数：
        //        
        //编码作者：Ryan
        //更新时间：2020.03.10
        //*************************************************************************
        public static OsilloscopeCalculateSet AddCalculateSet(string strTitle, string strIndex, string strTime, double dCH1Value, double dCH2Value, double dCH3Value, double dCH4Value)
        {
            if (ViewModelSet.Oscilloscope == null)
            {
                return null;
            }

            OsilloscopeCalculateSet clsData = new OsilloscopeCalculateSet();
            clsData.Title = strTitle;
            clsData.Number = strIndex;
            clsData.AcquisitionTime = strTime;

            if (ViewModelSet.Oscilloscope.SelectedSamplingChannel1 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel1 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[0].IsHidden)
            {
                clsData.Channel1Value = Convert.ToString(dCH1Value);
            }
            else
            {
                clsData.Channel1Value = null;
            }

            if (ViewModelSet.Oscilloscope.SelectedSamplingChannel2 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel2 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[1].IsHidden)
            {
                clsData.Channel2Value = Convert.ToString(dCH2Value);
            }
            else
            {
                clsData.Channel2Value = null;
            }

            if (ViewModelSet.Oscilloscope.SelectedSamplingChannel3 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel3 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[2].IsHidden)
            {
                clsData.Channel3Value = Convert.ToString(dCH3Value);
            }
            else
            {
                clsData.Channel3Value = null;
            }

            if (ViewModelSet.Oscilloscope.SelectedSamplingChannel4 != null && ViewModelSet.Oscilloscope.SelectedSamplingChannel4 != "停用" && !ViewModelSet.Oscilloscope.OscilloscopeProperty[3].IsHidden)
            {
                clsData.Channel4Value = Convert.ToString(dCH4Value);
            }
            else
            {
                clsData.Channel4Value = null;
            }

            return clsData;
        }
    }
}