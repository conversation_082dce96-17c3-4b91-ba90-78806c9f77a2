﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using ServoStudio.Models;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class ProgramJogViewModel
    {
        #region 字段
        public bool IsInitialized = true;
        public bool IsClosed = false;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 属性
        public virtual ObservableCollection<string> ProgramJogSwitch { get; set; }//程序PROGRAMJOG开关
        public virtual string SelectedProgramJogSwitchIndex { get; set; }//选中程序PROGRAMJOG开关
        public virtual string ProgramJogMovingDistance { get; set; }//程序PROGRAMJOG移动距离
        public virtual string ProgramJogMovingSpeed { get; set; }//程序PROGRAMJOG移动速度
        public virtual string ProgramJogAccDecTime { get; set; }//程序PROGRAMJOG加减速时间
        public virtual string ProgramJogWaitTime { get; set; }//程序PROGRAMJOG等待时间
        public virtual string ProgramJogMovingNumber { get; set; }//程序PROGRAMJOG移动次数
        public virtual string ProgramJogSwitchHint { get; set; }//程序PROGRAMJOG模块使能、禁能提示
        public virtual bool IsProgramJogButtonEnabled { get; set; }//程序PROGRAMJOG按钮属性
        public virtual string PositionUnit { get; set; }//位置单位
        public virtual string LegendAddress { get; set; }//图例地址
        #endregion

        #region 构造函数
        public ProgramJogViewModel()
        {
            ViewModelSet.ProgramJog = this;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：ProgramJogLoaded
        //函数功能：ProgramJog界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.10.21
        //*************************************************************************
        public void ProgramJogLoaded()
        {
            ControlInitialize();

            RefreshProgramJogFlag("ProgramJogInitialize");

            ReadProgramJogParameter();
        }

        //*************************************************************************
        //函数名称：ProgramJogUnloaded
        //函数功能：ProgramJog界面退出
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.10.21
        //*************************************************************************
        public void ProgramJogUnloaded()
        {
            //判断内部是否使能，若使能先禁能，再退出
            OthersHelper.GetActualEnableStatus(TaskName.ProgramJogActualEnable);
            IsClosed = true;           
        }

        //*************************************************************************
        //函数名称：ReadJogParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.10.21
        //*************************************************************************
        public void ReadProgramJogParameter()
        {
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要读取的数据字典
            AddParameterInfoDictionary(ref dicParameterInfo);

            //获取参数详细信息
            OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.PROGRAMJOG, TaskName.ProgramJog, lstTransmittingDataInfo);
        }

        //*************************************************************************
        //函数名称：EvaluationProgramJogParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.10.21
        //*************************************************************************
        public void EvaluationProgramJogParameter()
        {
            //获取当前的单位           
            PositionUnit = SelectUnit.Position;
            CurrentUnit.Position = PositionUnit;

            ProgramJogMovingDistance = OthersHelper.ExchangeUnit("Program Jog Moving Distance", DefaultUnit.PositionUnit + "-" + PositionUnit, OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Distance", "Index")));//程序PROGRAMJOG移动距离
            ProgramJogMovingSpeed = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Speed", "Index"));//程序PROGRAMJOG移动速度

            ProgramJogAccDecTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog AccDec Time", "Index"));//程序PROGRAMJOG加减速时间
            ProgramJogWaitTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Wait Time", "Index"));//程序PROGRAMJOG等待时间
            ProgramJogMovingNumber = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Program Jog Moving Number", "Index"));//程序PROGRAMJOG移动次数         
        }

        //*************************************************************************
        //函数名称：RefreshProgramJogFlag
        //函数功能：更新ProgramJog控制标志位
        //
        //输入参数：string Status    当前ProgramJog状态
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.10.21
        //*************************************************************************
        public void RefreshProgramJogFlag(string Status)
        {
            string strActualEnable = null;

            if (Status == "ProgramJogInitialize")
            {
                ProgramJogSet.Direction = null;
                ProgramJogSet.IsContinuous = false;

                ProgramJogSwitchHint = "伺服使能";
                IsProgramJogButtonEnabled = false;
            }
            else if (Status == "ProgramJogSwitch")
            {
                strActualEnable = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Actual Enable", "Index"));
                if (strActualEnable == "1")
                {                
                    if (IsClosed)
                    {
                        OthersHelper.OperatingControl("Operating Setting", "4", TaskName.ProgramJogSwitch, PageName.PROGRAMJOG);
                    }
                    else
                    {
                        ProgramJogSwitchHint = "伺服禁能";
                        IsProgramJogButtonEnabled = true;

                        ShowNotification_CrossThread(2004);
                    }
                }
                else
                {                
                    if (IsClosed)
                    {
                        OthersHelper.OperatingControl("Operating End", "1", TaskName.ProgramJogOperatingEnd, PageName.PROGRAMJOG);
                    }
                    else
                    {
                        ProgramJogSwitchHint = "伺服使能";
                        IsProgramJogButtonEnabled = false;

                        ShowNotification_CrossThread(2005);
                    }
                }
            }
            else if (Status == "ProgramJogStop")
            {
                ProgramJogSet.Direction = null;
                ProgramJogSet.IsContinuous = false;
            }
            else if (Status == "ProgramJogRun") 
            {
                if (SelectedProgramJogSwitchIndex == "0" || SelectedProgramJogSwitchIndex == "2" || SelectedProgramJogSwitchIndex == "4")
                {
                    ProgramJogSet.Direction = "2";
                }
                else
                {
                    ProgramJogSet.Direction = "3";
                }
                
                ProgramJogSet.IsContinuous = true;
            }
        }

        //*************************************************************************
        //函数名称：ProgramJogModelSwitch
        //函数功能：PROGRAMJOG模块使能禁能
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.09.23
        //*************************************************************************
        public void ProgramJogModelSwitch()
        {
            if (IsInitialized)
            {
                OthersHelper.OperatingControl("Operating Mode", "4100", TaskName.ProgramJogOperatingMode, PageName.PROGRAMJOG);
            }
            else
            {
                OthersHelper.OperatingControl("Operating Setting", "4", TaskName.ProgramJogSwitch, PageName.PROGRAMJOG);
            }
        }

        //*************************************************************************
        //函数名称：ProgramJogRun
        //函数功能：PROGRAMJOG开启
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.25
        //*************************************************************************
        public void ProgramJogRun()
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            //获取要写入的数据字典
            AddParameterInfoDictionary(ref dicParameterInfo);

            //获取参数详细信息
            OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);

            //判断输入参数是否正确
            iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
            if (iRet == RET.NO_EFFECT)
            {
                ShowNotification(2008);
                return;
            }
            else if (iRet == RET.ERROR)
            {
                return;
            }

            //获取发送任务信息
            ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: false);

            //下达任务
            ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.PROGRAMJOG, TaskName.ProgramJogContinuously, lstTransmittingDataInfo);

            //ProgramJogSet设置
            RefreshProgramJogFlag("ProgramJogRun");   
        }

        //*************************************************************************
        //函数名称：ProgramJogStop
        //函数功能：PROGRAMJOG关闭
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.25
        //*************************************************************************
        public void ProgramJogStop()
        {
            RefreshProgramJogFlag(Status: "ProgramJogStop");
        }

        //*************************************************************************
        //函数名称：OnSelectedProgramJogSwitchIndexChanged
        //函数功能：更新图例地址
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.10.28
        //*************************************************************************
        public void OnSelectedProgramJogSwitchIndexChanged()
        {
            LegendAddress = "pack://application:,,,/ServoStudio;component/Resource/ProgramJog" + SelectedProgramJogSwitchIndex + ".png";
        }
       
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：ControlInitialize
        //函数功能：控件初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.10.21
        //*************************************************************************
        private void ControlInitialize()
        {
            ProgramJogSwitch = new ObservableCollection<string>() { "正转", "反转", "正反转-模式A", "反正转-模式A", "正反转-模式B", "反正转-模式B" };
            SelectedProgramJogSwitchIndex = "0";
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.10.21
        //*************************************************************************
        private void AddParameterInfoDictionary(ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();
            
            dicParameterInfo.Add("Program Jog Moving Distance", OthersHelper.ExchangeUnit("Program Jog Moving Distance", PositionUnit + "-" + DefaultUnit.PositionUnit, ProgramJogMovingDistance));//程序JOG移动距离
            dicParameterInfo.Add("Program Jog Moving Speed", ProgramJogMovingSpeed);//程序JOG移动速度
            dicParameterInfo.Add("Program Jog AccDec Time", ProgramJogAccDecTime);//程序JOG加减速时间
            dicParameterInfo.Add("Program Jog Wait Time", ProgramJogWaitTime);//程序JOG等待时间
            dicParameterInfo.Add("Program Jog Moving Number", ProgramJogMovingNumber);//程序JOG移动次数
            dicParameterInfo.Add("Program Jog Switch", SelectedProgramJogSwitchIndex);//程序JOG运行模式

            if (SelectedProgramJogSwitchIndex == "0" || SelectedProgramJogSwitchIndex == "2" || SelectedProgramJogSwitchIndex == "4")
            {
                dicParameterInfo.Add("Operating Setting", "2");
            }
            else
            {
                dicParameterInfo.Add("Operating Setting", "3");
            }
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        private void ShowNotification_CrossThread(int In_iType)
        {
            WindowSet.clsMainWindow?.ShowNotification(In_iType);
        }
        #endregion
    }
}