﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{9DAF345E-4EA4-4994-9974-2444D0D5097C}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ServoStudio</RootNamespace>
    <AssemblyName>ServoStudio</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <TargetFrameworkProfile />
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>false</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>false</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Resource\ServoStudioICO.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CommonServiceLocator, Version=*******, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\CommonServiceLocator.2.0.2\lib\net40\CommonServiceLocator.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Xpf.Charts.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Controls.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Core.v16.2" />
    <Reference Include="DevExpress.Data.v16.2">
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Xpf.Diagram.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Docking.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Gauges.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\DevExpress.Xpf.Gauges.v16.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.Grid.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Grid.v16.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Layout.v16.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.LayoutControl.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.NavBar.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Ribbon.v16.2" />
    <Reference Include="DevExpress.Printing.v16.2.Core" />
    <Reference Include="DevExpress.Images.v16.2">
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Mvvm.v16.2" />
    <Reference Include="DevExpress.Xpf.Themes.DXStyle.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.HybridApp.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.LightGray.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.MetropolisDark.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.MetropolisLight.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.Office2007Black.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.Office2007Blue.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.Office2007Silver.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.Office2010Black.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.Office2010Blue.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.Office2010Silver.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.Office2013.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.Office2013DarkGray.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.Office2013LightGray.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.Office2016Black.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.Office2016Colorful.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="DevExpress.Xpf.Themes.Office2016White.v16.2">
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Xpf.Themes.Seven.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.TouchlineDark.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpf.Themes.VS2010.v16.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DynamicDataDisplay, Version=0.3.4703.0, Culture=neutral, PublicKeyToken=5b7d744a7263923f, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Lib\DynamicDataDisplay.dll</HintPath>
    </Reference>
    <Reference Include="GalaSoft.MvvmLight, Version=*******, Culture=neutral, PublicKeyToken=0e453835af4ee6ce, processorArchitecture=MSIL">
      <HintPath>..\packages\MvvmLightLibs.*******\lib\net40\GalaSoft.MvvmLight.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="GalaSoft.MvvmLight.Extras, Version=*******, Culture=neutral, PublicKeyToken=f46ff315b1088208, processorArchitecture=MSIL">
      <HintPath>..\packages\MvvmLightLibs.*******\lib\net40\GalaSoft.MvvmLight.Extras.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.0.86.0\lib\20\ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="log4net">
      <HintPath>Lib\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel">
      <HintPath>Lib\Microsoft.Office.Interop.Excel.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="NPOI, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net40\NPOI.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net40\NPOI.OOXML.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net40\NPOI.OpenXml4Net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net40\NPOI.OpenXmlFormats.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Windows.Interactivity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\MvvmLightLibs.*******\lib\net40\System.Windows.Interactivity.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml">
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Core">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
      <Private>True</Private>
    </Reference>
    <Reference Include="UIAutomationProvider">
      <Private>True</Private>
    </Reference>
    <Reference Include="UIAutomationTypes">
      <Private>True</Private>
    </Reference>
    <Reference Include="WindowsBase">
      <Private>True</Private>
    </Reference>
    <Reference Include="PresentationCore">
      <Private>True</Private>
    </Reference>
    <Reference Include="PresentationFramework">
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="Converter\AlarmLevelConverter.cs" />
    <Compile Include="Converter\BackgroundConverter.cs" />
    <Compile Include="Converter\ContentConverter.cs" />
    <Compile Include="Converter\VisibilityConverter.cs" />
    <Compile Include="GlobalConstant\GlobalConstant.cs" />
    <Compile Include="GlobalMethod\IniHelper.cs" />
    <Compile Include="GlobalMethod\ConvertHelper.cs" />
    <Compile Include="GlobalMethod\ExcelHelper.cs" />
    <Compile Include="GlobalMethod\HexHelper.cs" />
    <Compile Include="GlobalMethod\LogHelper.cs" />
    <Compile Include="GlobalMethod\MicrosecondHelper.cs" />
    <Compile Include="GlobalMethod\OthersHelper.cs" />
    <Compile Include="GlobalMethod\SoftwareErrorHelper.cs" />
    <Compile Include="GlobalMethod\ServoXmlHelper.cs" />
    <Compile Include="GlobalMethod\XmlHelper.cs" />
    <Compile Include="GlobalMethod\YModemHelper.cs" />
    <Compile Include="GlobalPthread\MicrosecondsOscilloscopeDrawingPthread.cs" />
    <Compile Include="GlobalPthread\SerialPortTransmittingPthread.cs" />
    <Compile Include="GlobalVariable\GlobalVariable.cs" />
    <Compile Include="Models\LocalDB.cs" />
    <Compile Include="Models\Common.cs" />
    <Compile Include="Models\FaultDataOscilloscopeModel.cs" />
    <Compile Include="Models\OscilloscopePresetModel.cs" />
    <Compile Include="Models\DigitalIOModel.cs" />
    <Compile Include="Models\OscilloscopeModel.cs" />
    <Compile Include="Models\ParameterReadWriteModel.cs" />
    <Compile Include="Models\ServoConfigsModel.cs" />
    <Compile Include="Models\SlaveAxisAddress.cs" />
    <Compile Include="ViewModels\AddSlaveAxisIDViewModel.cs" />
    <Compile Include="ViewModels\AdvancedFeedbackViewModel.cs" />
    <Compile Include="ViewModels\InfoViewModel.cs" />
    <Compile Include="MainWindowViewModel.cs" />
    <Compile Include="ViewModels\FaultDataConfigViewModel.cs" />
    <Compile Include="ViewModels\MotorDriectionJogViewModel.cs" />
    <Compile Include="ViewModels\InertiaIdentificationParameterSelfTunningViewModel.cs" />
    <Compile Include="ViewModels\MotorLineUVWSequenceAbsEncoderOffsetViewModel.cs" />
    <Compile Include="ViewModels\MotorFeedbackAutoLearnViewModel.cs" />
    <Compile Include="ViewModels\FaultDataOscilloscopeViewMode.cs" />
    <Compile Include="ViewModels\ParameterImportViewModel.cs" />
    <Compile Include="ViewModels\ProgramJogViewModel.cs" />
    <Compile Include="ViewModels\SplashScreenViewModel.cs" />
    <Compile Include="Views\AddSlaveAxisIDView.xaml.cs">
      <DependentUpon>AddSlaveAxisIDView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\AdministratorView.xaml.cs">
      <DependentUpon>AdministratorView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\AdministratorViewModel.cs" />
    <Compile Include="Views\CommunicationSetView.xaml.cs">
      <DependentUpon>CommunicationSetView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\CommunicationSetViewModel.cs" />
    <Compile Include="Views\AdvancedFeedbackView.xaml.cs">
      <DependentUpon>AdvancedFeedbackView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\CurrentLoopView.xaml.cs">
      <DependentUpon>CurrentLoopView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\CurrentLoopViewModel.cs" />
    <Compile Include="Views\DigitalIOView.xaml.cs">
      <DependentUpon>DigitalIOView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\DigitalIOViewModel.cs" />
    <Compile Include="Views\FirmwareUpdateView.xaml.cs">
      <DependentUpon>FirmwareUpdateView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\FirmwareUpdateViewModel.cs" />
    <Compile Include="Views\HardwareAlarmHistoryView.xaml.cs">
      <DependentUpon>HardwareAlarmHistoryView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\HardwareAlarmHistoryViewModel.cs" />
    <Compile Include="Views\HardwareAlarmMeasureView.xaml.cs">
      <DependentUpon>HardwareAlarmMeasureView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\HardwareAlarmMeasureViewModel.cs" />
    <Compile Include="Views\InfoView.xaml.cs">
      <DependentUpon>InfoView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\JerkFreeView.xaml.cs">
      <DependentUpon>JerkFreeView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\FaultDataConfigView.xaml.cs">
      <DependentUpon>FaultDataConfigView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\MotorDriectionJogView.xaml.cs">
      <DependentUpon>MotorDriectionJogView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\JogView.xaml.cs">
      <DependentUpon>JogView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\JogViewModel.cs" />
    <Compile Include="Views\LimitAmplitudeView.xaml.cs">
      <DependentUpon>LimitAmplitudeView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\LimitAmplitudeViewModel.cs" />
    <Compile Include="Views\ModifyPasswordView.xaml.cs">
      <DependentUpon>ModifyPasswordView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\ModifyPasswordViewModel.cs" />
    <Compile Include="Views\InertiaIdentificationParameterSelfTunningView.xaml.cs">
      <DependentUpon>InertiaIdentificationParameterSelfTunningView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\MotorLineUVWSequenceAbsEncoderOffsetView.xaml.cs">
      <DependentUpon>MotorLineUVWSequenceAbsEncoderOffsetView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\MotorFeedbackAutoLearnView.xaml.cs">
      <DependentUpon>MotorFeedbackAutoLearnView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\MotorFeedbackView.xaml.cs">
      <DependentUpon>MotorFeedbackView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\MotorFeedbackViewModel.cs" />
    <Compile Include="Views\MotorLibraryDetailsView.xaml.cs">
      <DependentUpon>MotorLibraryDetailsView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\MotorLibraryDetailsViewModel.cs" />
    <Compile Include="Views\MotorLibraryView.xaml.cs">
      <DependentUpon>MotorLibraryView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\MotorLibraryViewModel.cs" />
    <Compile Include="Views\MotorParameterIdentificationView.xaml.cs">
      <DependentUpon>MotorParameterIdentificationView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\MotorParameterIdentificationViewModel.cs" />
    <Compile Include="Views\NormalSettingView.xaml.cs">
      <DependentUpon>NormalSettingView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\NormalSettingViewModel.cs" />
    <Compile Include="Views\OfflineInertiaIdentificationView.xaml.cs">
      <DependentUpon>OfflineInertiaIdentificationView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\OfflineInertiaIdentificationViewModel.cs" />
    <Compile Include="Views\FaultDataOscilloscopeView.xaml.cs">
      <DependentUpon>FaultDataOscilloscopeView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\OscilloscopeView.xaml.cs">
      <DependentUpon>OscilloscopeView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\OscilloscopeViewModel.cs" />
    <Compile Include="Views\ParameterMonitorView.xaml.cs">
      <DependentUpon>ParameterMonitorView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\ParameterMonitorViewModel.cs" />
    <Compile Include="Views\ParameterImportView.xaml.cs">
      <DependentUpon>ParameterImportView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\ParameterReadWriteView.xaml.cs">
      <DependentUpon>ParameterReadWriteView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\ParameterReadWriteViewModel.cs" />
    <Compile Include="Views\PositionLoopView.xaml.cs">
      <DependentUpon>PositionLoopView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\PositionLoopViewModel.cs" />
    <Compile Include="Views\PositionView.xaml.cs">
      <DependentUpon>PositionView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\ProgramJogView.xaml.cs">
      <DependentUpon>ProgramJogView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\SeekZeroView.xaml.cs">
      <DependentUpon>SeekZeroView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\SeekZeroViewModel.cs" />
    <Compile Include="Views\SinView.xaml.cs">
      <DependentUpon>SinView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\SlopeView.xaml.cs">
      <DependentUpon>SlopeView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\SoftwareErrorLogView.xaml.cs">
      <DependentUpon>SoftwareErrorLogView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\SoftwareErrorLogViewModel.cs" />
    <Compile Include="Views\SpeedLoopView.xaml.cs">
      <DependentUpon>SpeedLoopView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\SpeedLoopViewModel.cs" />
    <Compile Include="Views\SpeedView.xaml.cs">
      <DependentUpon>SpeedView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\SquareView.xaml.cs">
      <DependentUpon>SquareView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\StepView.xaml.cs">
      <DependentUpon>StepView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\SwitchAxisView.xaml.cs">
      <DependentUpon>SwitchAxisView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\SwitchAxisViewModel.cs" />
    <Compile Include="Views\TorqueView.xaml.cs">
      <DependentUpon>TorqueView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\UnitView.xaml.cs">
      <DependentUpon>UnitView.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModels\UnitViewModel.cs" />
    <Page Include="DXSplashScreen\SplashScreenView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Language\en-US.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Language\zh-CN.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DXSplashScreen\SplashScreenView.xaml.cs">
      <DependentUpon>SplashScreenView.xaml</DependentUpon>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="Views\AddSlaveAxisIDView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\AdministratorView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\CommunicationSetView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\AdvancedFeedbackView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\CurrentLoopView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\DigitalIOView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\FirmwareUpdateView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\HardwareAlarmHistoryView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\HardwareAlarmMeasureView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\InfoView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\JerkFreeView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\FaultDataConfigView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\MotorDriectionJogView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\JogView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\LimitAmplitudeView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\ModifyPasswordView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\InertiaIdentificationParameterSelfTunningView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\MotorLineUVWSequenceAbsEncoderOffsetView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\MotorFeedbackAutoLearnView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\MotorFeedbackView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\MotorLibraryDetailsView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\MotorLibraryView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\MotorParameterIdentificationView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\NormalSettingView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\OfflineInertiaIdentificationView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\FaultDataOscilloscopeView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\OscilloscopeView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\ParameterMonitorView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\ParameterImportView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ParameterReadWriteView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\PositionLoopView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\PositionView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\ProgramJogView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\SeekZeroView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\SinView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\SlopeView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\SoftwareErrorLogView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\SpeedLoopView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\SpeedView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\SquareView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\StepView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\SwitchAxisView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\TorqueView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\UnitView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="DXSplashScreen\Image.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\bulb.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\diamond.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\JHL.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\ServoStudioICO.ico">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\Gear.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\Rotate.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\Position.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\Profile.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\Torque.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\JerkFree.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\1.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\2.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\10.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\11.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\12.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\13.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\14.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\17.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\18.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\19.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\20.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\21.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\22.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\23.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\24.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\25.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\26.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\27.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\28.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\29.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\3.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\30.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\33.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\34.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\4.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\5.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\6.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\7.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\8.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Resource\9.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\BO_Task_Large.png" />
    <Resource Include="Resource\Code_Central.png" />
    <Resource Include="Resource\Online_Help.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\Brake.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\Sin.png" />
    <Resource Include="Resource\Slope.png" />
    <Resource Include="Resource\Square.png" />
    <Resource Include="Resource\Step.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\三环调试.png" />
    <Resource Include="Icon\函数发生器.png" />
    <Resource Include="Icon\示波器.png" />
    <Resource Include="Icon\运动调试.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\参数识别.png" />
    <Resource Include="Icon\在线识别.png" />
    <Resource Include="Icon\离线识别.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\ROM读取.png" />
    <Resource Include="Icon\保存ROM.png" />
    <Resource Include="Icon\参数导入.png" />
    <Resource Include="Icon\参数导出.png" />
    <Resource Include="Icon\在线刷新.png" />
    <Resource Include="Icon\监控设置.png" />
    <Resource Include="Icon\监控设置保存.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\恢复出厂值.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\在线读写.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\信息提示.png" />
    <Resource Include="Icon\固件升级.png" />
    <Resource Include="Icon\实时报警.png" />
    <Resource Include="Icon\工作手册.png" />
    <Resource Include="Icon\版本信息.png" />
    <Resource Include="Icon\软件重启.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\删除日志.png" />
    <Resource Include="Icon\参数路径.png" />
    <Resource Include="Icon\日志链接.png" />
    <Resource Include="Icon\权限密码.png" />
    <Resource Include="Icon\查询日志.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\伺服使能.png" />
    <Resource Include="Icon\伺服禁能.png" />
    <Resource Include="Icon\报警展示.png" />
    <Resource Include="Icon\数据监控.png" />
    <Resource Include="Icon\轴地址切换.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\故障清除.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\系统复位.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\一般设置.png" />
    <Resource Include="Icon\位置环.png" />
    <Resource Include="Icon\单位设置.png" />
    <Resource Include="Icon\电机反馈.png" />
    <Resource Include="Icon\电流环.png" />
    <Resource Include="Icon\通信设置.png" />
    <Resource Include="Icon\速度环.png" />
    <Resource Include="Icon\限幅保护.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\一键全停.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\报警展示开.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\数据监控开.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\软件重启侧边.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\JOG.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\程序JOG.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\JOG.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\ProgramJog0.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\ProgramJog1.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resource\ProgramJog2.png" />
    <Resource Include="Resource\ProgramJog3.png" />
    <Resource Include="Resource\ProgramJog4.png" />
    <Resource Include="Resource\ProgramJog5.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\报警原因.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\历史清除.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\实时查询.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\报警方式.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\数据库.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\一键控制.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\全部初始.png" />
    <Resource Include="Icon\全部复位.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\全部使能.png" />
    <Resource Include="Icon\全部暂停.png" />
    <Resource Include="Icon\全部禁能.png" />
    <Resource Include="Icon\全部运动.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\配置急停.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\全部故障清除.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Xml\OscilloscopeOptions.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Resource Include="Xml\OscilloscopePresetConfigs.xml" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\参数自学习.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\JOGDriection.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\参数调优.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\惯量辨识参数整定.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\报警展示开 - 复制.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\禁能.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\故障.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\使能.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\语言切换.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\故障数据清除.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\故障数据查询.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\故障数据配置.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\清除故障.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\高级功能.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\清除多圈和故障.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\清除多圈.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\上位机控制.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Icon\Ecat控制.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>