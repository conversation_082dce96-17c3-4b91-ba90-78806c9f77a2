﻿<UserControl x:Class="ServoStudio.Views.AdministratorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dxmvvm="http://schemas.devexpress.com/winfx/2008/xaml/mvvm" 
             xmlns:ViewModels="clr-namespace:ServoStudio.ViewModels"             
             mc:Ignorable="d"
             DataContext="{dxmvvm:ViewModelSource Type=ViewModels:AdministratorViewModel}">

    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Label Grid.Column="0" Margin="5" Content="管理员密码" Style="{StaticResource LabelStyle}"/>
            <TextBox  Grid.Column="1" Margin="5" Width="200" Style="{StaticResource TextBoxStyle}" Text="{Binding AdministratorKey,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
        </Grid>
    </ScrollViewer>

</UserControl>
