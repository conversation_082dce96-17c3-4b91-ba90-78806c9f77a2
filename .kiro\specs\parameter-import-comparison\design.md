# 参数导入对比功能设计文档

## 概述

本设计文档详细描述了参数导入对比功能的技术实现方案，该功能将扩展现有的参数导入流程，增加参数对比和选择性写入的能力。

## 架构设计

### 1. 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    参数导入对比功能架构                        │
├─────────────────────────────────────────────────────────────┤
│  UI层                                                       │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ ParameterImport │  │ ComparisonView  │                  │
│  │     Dialog      │  │                 │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  ViewModel层                                                │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ParameterImport  │  │ParameterCompar- │                  │
│  │   ViewModel     │  │  isonViewModel  │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  Service层                                                  │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ParameterCompar- │  │ParameterWrite   │                  │
│  │  isonService    │  │    Service      │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  Data层                                                     │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   Excel文件     │  │   伺服设备      │                  │
│  │   参数数据      │  │   当前参数      │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

### 2. 核心组件设计

#### 参数对比服务 (ParameterComparisonService)
```csharp
public class ParameterComparisonService
{
    public async Task<ComparisonResult> CompareParametersAsync(
        List<Parameter> importParameters, 
        List<Parameter> currentParameters);
    
    public List<ParameterDifference> GetDifferences(ComparisonResult result);
    
    public bool ValidateParameterValue(Parameter parameter, string value);
}
```

#### 参数差异模型 (ParameterDifference)
```csharp
public class ParameterDifference
{
    public string Index { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string CurrentValue { get; set; }
    public string ImportValue { get; set; }
    public string DataType { get; set; }
    public string Unit { get; set; }
    public string Min { get; set; }
    public string Max { get; set; }
    public bool IsSelected { get; set; }
    public bool IsReadOnly { get; set; }
    public DifferenceType Type { get; set; }
}

public enum DifferenceType
{
    ValueDifferent,    // 值不同
    NewParameter,      // 新参数
    MissingParameter   // 缺失参数
}
```

## 组件详细设计

### 1. 参数对比界面 (ParameterComparisonView)

#### 界面布局
```xml
<UserControl x:Class="ServoStudio.Views.ParameterComparisonView">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>    <!-- 工具栏 -->
            <RowDefinition Height="*"/>       <!-- 对比表格 -->
            <RowDefinition Height="Auto"/>    <!-- 操作按钮 -->
        </Grid.RowDefinitions>
        
        <!-- 工具栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal">
            <CheckBox Content="全选" Command="{Binding SelectAllCommand}"/>
            <CheckBox Content="全不选" Command="{Binding UnselectAllCommand}"/>
            <TextBox PlaceholderText="搜索参数..." Text="{Binding SearchText}"/>
            <ComboBox ItemsSource="{Binding FilterOptions}" 
                      SelectedItem="{Binding SelectedFilter}"/>
        </StackPanel>
        
        <!-- 对比表格 -->
        <dxg:GridControl Grid.Row="1" ItemsSource="{Binding Differences}">
            <dxg:GridControl.Columns>
                <dxg:GridColumn FieldName="IsSelected" Header="选择"/>
                <dxg:GridColumn FieldName="Name" Header="参数名称"/>
                <dxg:GridColumn FieldName="Description" Header="描述"/>
                <dxg:GridColumn FieldName="CurrentValue" Header="当前值"/>
                <dxg:GridColumn FieldName="ImportValue" Header="导入值"/>
                <dxg:GridColumn FieldName="DataType" Header="数据类型"/>
                <dxg:GridColumn FieldName="Unit" Header="单位"/>
            </dxg:GridControl.Columns>
        </dxg:GridControl>
        
        <!-- 操作按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="写入选中参数" Command="{Binding WriteSelectedCommand}"/>
            <Button Content="取消" Command="{Binding CancelCommand}"/>
        </StackPanel>
    </Grid>
</UserControl>
```

#### 样式定义
```xml
<Style x:Key="DifferentValueStyle" TargetType="TextBlock">
    <Setter Property="Background" Value="#FFFFCC"/>
    <Setter Property="Foreground" Value="#FF8C00"/>
</Style>

<Style x:Key="NewParameterStyle" TargetType="TextBlock">
    <Setter Property="Background" Value="#E6FFE6"/>
    <Setter Property="Foreground" Value="#008000"/>
</Style>

<Style x:Key="ReadOnlyStyle" TargetType="TextBlock">
    <Setter Property="Background" Value="#F0F0F0"/>
    <Setter Property="Foreground" Value="#808080"/>
</Style>
```

### 2. 参数对比ViewModel

```csharp
public class ParameterComparisonViewModel : ViewModelBase
{
    private readonly ParameterComparisonService _comparisonService;
    private readonly ParameterWriteService _writeService;
    
    public ObservableCollection<ParameterDifference> Differences { get; set; }
    public ObservableCollection<ParameterDifference> FilteredDifferences { get; set; }
    
    public string SearchText { get; set; }
    public string SelectedFilter { get; set; }
    
    public ICommand SelectAllCommand { get; }
    public ICommand UnselectAllCommand { get; }
    public ICommand WriteSelectedCommand { get; }
    public ICommand CancelCommand { get; }
    
    public async Task LoadComparisonAsync(string importFilePath)
    {
        try
        {
            ShowProgress("正在读取导入文件...", 10);
            var importParameters = await LoadImportParametersAsync(importFilePath);
            
            ShowProgress("正在读取设备参数...", 30);
            var currentParameters = await LoadCurrentParametersAsync();
            
            ShowProgress("正在对比参数差异...", 60);
            var comparisonResult = await _comparisonService.CompareParametersAsync(
                importParameters, currentParameters);
            
            ShowProgress("正在生成差异列表...", 80);
            Differences = new ObservableCollection<ParameterDifference>(
                _comparisonService.GetDifferences(comparisonResult));
            
            ApplyFilter();
            ShowProgress("完成", 100);
        }
        catch (Exception ex)
        {
            ShowError($"参数对比失败: {ex.Message}");
        }
    }
    
    private async Task WriteSelectedParametersAsync()
    {
        var selectedParameters = Differences.Where(d => d.IsSelected).ToList();
        if (!selectedParameters.Any())
        {
            ShowWarning("请选择要写入的参数");
            return;
        }
        
        var result = await ShowConfirmation(
            $"确定要写入 {selectedParameters.Count} 个参数吗？");
        if (!result) return;
        
        await _writeService.WriteParametersAsync(selectedParameters, 
            new Progress<ParameterWriteProgress>(OnWriteProgress));
    }
    
    private void OnWriteProgress(ParameterWriteProgress progress)
    {
        ShowProgress($"正在写入参数 {progress.Current}/{progress.Total}: {progress.ParameterName}", 
            progress.Percentage);
            
        // 更新界面中对应参数的状态
        var difference = Differences.FirstOrDefault(d => d.Index == progress.ParameterIndex);
        if (difference != null)
        {
            difference.WriteStatus = progress.Status;
            difference.WriteMessage = progress.Message;
        }
    }
}
```

### 3. 参数对比服务实现

```csharp
public class ParameterComparisonService
{
    public async Task<ComparisonResult> CompareParametersAsync(
        List<Parameter> importParameters, 
        List<Parameter> currentParameters)
    {
        return await Task.Run(() =>
        {
            var result = new ComparisonResult();
            var currentDict = currentParameters.ToDictionary(p => p.Index);
            
            foreach (var importParam in importParameters)
            {
                if (currentDict.TryGetValue(importParam.Index, out var currentParam))
                {
                    if (importParam.Value != currentParam.Value)
                    {
                        result.Differences.Add(new ParameterDifference
                        {
                            Index = importParam.Index,
                            Name = importParam.Name,
                            Description = importParam.Description,
                            CurrentValue = currentParam.Value,
                            ImportValue = importParam.Value,
                            DataType = importParam.DataType,
                            Unit = importParam.Unit,
                            Min = importParam.Min,
                            Max = importParam.Max,
                            IsReadOnly = importParam.RWProperty == "RO",
                            Type = DifferenceType.ValueDifferent
                        });
                    }
                }
                else
                {
                    result.Differences.Add(new ParameterDifference
                    {
                        Index = importParam.Index,
                        Name = importParam.Name,
                        Description = importParam.Description,
                        CurrentValue = "N/A",
                        ImportValue = importParam.Value,
                        DataType = importParam.DataType,
                        Unit = importParam.Unit,
                        Min = importParam.Min,
                        Max = importParam.Max,
                        IsReadOnly = importParam.RWProperty == "RO",
                        Type = DifferenceType.NewParameter
                    });
                }
            }
            
            return result;
        });
    }
    
    public bool ValidateParameterValue(Parameter parameter, string value)
    {
        // 数据类型验证
        if (!IsValidDataType(value, parameter.DataType))
            return false;
            
        // 范围验证
        if (!IsInRange(value, parameter.Min, parameter.Max))
            return false;
            
        // 只读属性验证
        if (parameter.RWProperty == "RO")
            return false;
            
        return true;
    }
}
```

### 4. 参数写入服务

```csharp
public class ParameterWriteService
{
    private readonly ICommunicationService _communicationService;
    
    public async Task WriteParametersAsync(
        List<ParameterDifference> parameters,
        IProgress<ParameterWriteProgress> progress)
    {
        var total = parameters.Count;
        var current = 0;
        
        foreach (var param in parameters)
        {
            current++;
            progress?.Report(new ParameterWriteProgress
            {
                Current = current,
                Total = total,
                ParameterIndex = param.Index,
                ParameterName = param.Name,
                Percentage = (current * 100) / total,
                Status = WriteStatus.Writing
            });
            
            try
            {
                // 使用现有的参数写入逻辑
                await WriteParameterAsync(param);
                
                progress?.Report(new ParameterWriteProgress
                {
                    Current = current,
                    Total = total,
                    ParameterIndex = param.Index,
                    ParameterName = param.Name,
                    Percentage = (current * 100) / total,
                    Status = WriteStatus.Success,
                    Message = "写入成功"
                });
            }
            catch (Exception ex)
            {
                progress?.Report(new ParameterWriteProgress
                {
                    Current = current,
                    Total = total,
                    ParameterIndex = param.Index,
                    ParameterName = param.Name,
                    Percentage = (current * 100) / total,
                    Status = WriteStatus.Failed,
                    Message = ex.Message
                });
            }
        }
    }
    
    private async Task WriteParameterAsync(ParameterDifference param)
    {
        // 复用现有的参数写入逻辑
        var transmittingData = new List<TransmitingDataInfoSet>();
        var parameterInfo = new List<DiffParameterReadWriteSet>
        {
            new DiffParameterReadWriteSet
            {
                Index = param.Index,
                Current = param.ImportValue,
                DataType = param.DataType,
                Name = param.Name,
                Description = param.Description,
                RWProperty = param.IsReadOnly ? "RO" : "RW"
            }
        };
        
        ParameterReadWriteModel.GetIndexAndDataType_ForCompare(
            parameterInfo, ref transmittingData, 
            IsCheckAddressDeviation: true, IsEEPROM: false);
        
        await _communicationService.WriteParameterAsync(transmittingData);
    }
}

public class ParameterWriteProgress
{
    public int Current { get; set; }
    public int Total { get; set; }
    public string ParameterIndex { get; set; }
    public string ParameterName { get; set; }
    public int Percentage { get; set; }
    public WriteStatus Status { get; set; }
    public string Message { get; set; }
}

public enum WriteStatus
{
    Pending,
    Writing,
    Success,
    Failed
}
```

## 集成方案

### 1. 修改现有的参数导入流程

```csharp
// 在MainWindowViewModel中修改ImportConfigFile方法
public void ImportConfigFile(string Action)
{
    try
    {
        // 获取导入文件路径
        var filePath = GetImportFilePath();
        if (string.IsNullOrEmpty(filePath)) return;
        
        // 检查是否启用对比功能
        if (IsComparisonEnabled())
        {
            // 使用新的对比功能
            ShowParameterComparisonDialog(filePath);
        }
        else
        {
            // 使用原有的直接导入流程
            ImportConfigFile_Original(filePath);
        }
    }
    catch (Exception ex)
    {
        SoftwareErrorHelper.CatchDispose(ERROR.MAIN_IMPORT_CONFIG_FILE, 
            "ImportConfigFile", ex);
    }
}

private void ShowParameterComparisonDialog(string filePath)
{
    var viewModel = new ParameterComparisonViewModel();
    var dialog = new ParameterComparisonDialog
    {
        DataContext = viewModel
    };
    
    // 异步加载对比数据
    _ = viewModel.LoadComparisonAsync(filePath);
    
    dialog.ShowDialog();
}
```

### 2. 配置选项

```xml
<!-- 在App.config中添加配置 -->
<appSettings>
    <add key="EnableParameterComparison" value="true" />
    <add key="AutoSelectDifferences" value="false" />
    <add key="ShowReadOnlyParameters" value="true" />
</appSettings>
```

## 错误处理

### 1. 异常处理策略
- 文件读取失败：提示用户检查文件格式和路径
- 设备通信失败：提示检查设备连接
- 参数验证失败：显示具体的验证错误信息
- 写入失败：记录失败的参数并允许重试

### 2. 用户反馈
- 进度指示：显示当前操作进度
- 状态反馈：实时更新操作状态
- 错误提示：提供清晰的错误信息和解决建议

## 性能优化

### 1. 异步处理
- 所有耗时操作使用异步方法
- UI操作在主线程，数据处理在后台线程

### 2. 内存优化
- 大量数据使用虚拟化显示
- 及时释放不需要的资源

### 3. 响应性优化
- 使用进度指示器提升用户体验
- 支持操作取消功能

这个设计方案完全基于现有的代码结构，复用了现有的参数读写逻辑，确保与现有系统的兼容性。