﻿using System;
using DevExpress.Mvvm.DataAnnotations;
using DevExpress.Mvvm;
using DevExpress.Mvvm.POCO;
using System.Collections.ObjectModel;
using ServoStudio.GlobalMethod;
using ServoStudio.GlobalConstant;
using System.Collections.Generic;
using ServoStudio.Models;
using System.Data;

namespace ServoStudio.ViewModels
{
    [POCOViewModel]
    public class CurrentLoopViewModel
    {
        #region 私有字段
        private static bool IsInitialized = true;
        private static bool IsEvaluationAll = true;
        private Dictionary<string, string> dicParameterInfo = new Dictionary<string, string>();
        private List<ParameterReadWriteSet> lstParameterInfo_ForWrite = new List<ParameterReadWriteSet>();
        private ObservableCollection<ParameterReadWriteSet> obsParameterInfo_ForRead = new ObservableCollection<ParameterReadWriteSet>();
        #endregion

        #region 服务      
        protected virtual INavigationService NavigationService { get { return this.GetService<INavigationService>(); } }
        protected virtual IMessageBoxService MessageBoxService { get { return this.GetService<IMessageBoxService>(); } }
        #endregion

        #region 属性
        public virtual string SelectedTabIndex { get; set; }
        public virtual string FirstTrqcmdFilterTime { get; set; }//第一转矩指令滤波时间参数
        public virtual string SecondTrqcmdFilterFreq { get; set; }//第二转矩指令滤波器频率
        public virtual string SecondTrqcmdFilterQ { get; set; }//第二转矩指令滤波器Q值

        public virtual string NotchFilterConfig { get; set; }//陷波滤波器配置
        public virtual bool NotchFilter1Switch { get; set; }//第1段陷波滤波器开关
        public virtual string NotchFilter1Content { get; set; }//第1段陷波滤波器内容
        public virtual bool NotchFilter2Switch { get; set; }//第1段陷波滤波器开关
        public virtual string NotchFilter2Content { get; set; }//第1段陷波滤波器内容
        public virtual bool NotchFilter3Switch { get; set; }//第1段陷波滤波器开关
        public virtual string NotchFilter3Content { get; set; }//第1段陷波滤波器内容
        public virtual bool NotchFilter4Switch { get; set; }//第1段陷波滤波器开关
        public virtual string NotchFilter4Content { get; set; }//第1段陷波滤波器内容

        public virtual string NotchFilterFrequency1 { get; set; }//第1段陷波滤波器频率
        public virtual string NotchFilterQFactor1 { get; set; }//第1段陷波滤波器Q值
        public virtual string NotchFilterDepth1 { get; set; }//第1段陷波滤波器深度
        public virtual string NotchFilterFrequency2 { get; set; }//第2段陷波滤波器频率
        public virtual string NotchFilterQFactor2 { get; set; }//第2段陷波滤波器Q值
        public virtual string NotchFilterDepth2 { get; set; }//第2段陷波滤波器深度
        public virtual string NotchFilterFrequency3 { get; set; }//第3段陷波滤波器频率
        public virtual string NotchFilterQFactor3 { get; set; }//第3段陷波滤波器Q值
        public virtual string NotchFilterDepth3 { get; set; }//第3段陷波滤波器深度
        public virtual string NotchFilterFrequency4 { get; set; }//第4段陷波滤波器频率
        public virtual string NotchFilterQFactor4 { get; set; }//第4段陷波滤波器Q值
        public virtual string NotchFilterDepth4 { get; set; }//第4段陷波滤波器深度
        public virtual string ForwardInternalTorqueLimit { get; set; }//正转内部转矩限制值
        public virtual string ReverseInternalTorqueLimit { get; set; }//反转内部转矩限制值
        public virtual string ForwardExternalTorqueLimit { get; set; }//正转外部转矩限制值
        public virtual string ReverseExternalTorqueLimit { get; set; }//反转外部转矩限制值
        public virtual int FirstTrqcmdFilterTimeBackground { get; set; }//第一转矩指令滤波时间参数
        public virtual int SecondTrqcmdFilterFreqBackground { get; set; }//第二转矩指令滤波器频率
        public virtual int SecondTrqcmdFilterQBackground { get; set; }//第二转矩指令滤波器Q值
        #endregion

        #region 构造函数
        public CurrentLoopViewModel()
        {
            ViewModelSet.CurrentLoop = this;
            SoftwareStateParameterSet.CurrentPageName = PageName.CURRENTLOOP;
        }
        #endregion

        #region 公有方法
        //*************************************************************************
        //函数名称：CurrentLoopLoaded
        //函数功能：CurrentLoop界面载入
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void CurrentLoopLoaded()
        {
            int iRet = -1;

            try
            {
                //背景颜色初始化
                BackgroundInitialize();

                if (IsInitialized == true)
                {
                    //标志位更新
                    IsInitialized = false;
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadCurrentLoopParameter("All");
                    }
                    else
                    {
                        GetDefaultCurrentLoopParameter("All");
                    }
                }
                else
                {
                    //标志位更新
                    IsEvaluationAll = true;

                    //赋值
                    iRet = HexHelper.CheckSerialPortStatus();
                    if (iRet == RET.SUCCEEDED)
                    {
                        ReadCurrentLoopParameter("All");
                    }
                    else
                    {
                        InterfaceEvaluationFromGlobalVariable();
                    }
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CURRENTLOOP_LOADED, "CurrentLoopLoaded", ex);
            }
        }

        //*************************************************************************
        //函数名称：WriteCurrentLoopParameter
        //函数功能：写电流环参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void WriteCurrentLoopParameter(string strCategory)
        {
            int iRet = -1;
            bool bEEPROM = false;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要写入的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForWrite(dicParameterInfo, ref lstParameterInfo_ForWrite);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断输入参数是否正确
                iRet = OthersHelper.CheckInputParametersCorrected(ref lstParameterInfo_ForWrite);
                if (iRet == RET.NO_EFFECT)
                {
                    ShowNotification(2008);
                    return;
                }
                else if (iRet == RET.ERROR)
                {
                    return;
                }

                //返回初始位置-为了MessageBox弹窗
                if (!OthersHelper.GetWindowsStartupPosition())
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //判断是否写入EEPROM
                if (MessageBoxService.ShowMessage("是否断电记忆，参数写入到EEPROM...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    bEEPROM = true;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(lstParameterInfo_ForWrite, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true, IsEEPROM: bEEPROM);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterWrite(PageName.CURRENTLOOP, TaskName.CurrentLoop, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CURRENTLOOP_WRITE_PARAMETER, "WriteCurrentLoopParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：ReadCurrentLoopParameter
        //函数功能：读电机反馈参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.02
        //*************************************************************************
        public void ReadCurrentLoopParameter(string strCategory)
        {
            int iRet = -1;
            List<TransmitingDataInfoSet> lstTransmittingDataInfo = new List<TransmitingDataInfoSet>();

            try
            {
                //获取要读取的数据字典
                iRet = AddParameterInfoDictionary(strCategory, ref dicParameterInfo);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取参数详细信息
                iRet = OthersHelper.GetParameterForRead(dicParameterInfo, ref obsParameterInfo_ForRead);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //获取发送任务信息
                ParameterReadWriteModel.GetIndexAndDataType(obsParameterInfo_ForRead, ref lstTransmittingDataInfo, IsCheckAddressDeviation: true);

                //下达任务
                ViewModelSet.CommunicationSet?.SerialPort_DataTransmiting_For_ParameterRead(PageName.CURRENTLOOP, TaskName.CurrentLoop, lstTransmittingDataInfo);
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CURRENTLOOP_READ_PARAMETER, "ReadCurrentLoopParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetDefaultCurrentLoopParameter
        //函数功能：获取限幅保护的默认参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void GetDefaultCurrentLoopParameter(string strCategory)
        {
            try
            {
                switch (strCategory)
                {
                    case "0":
                        FirstTrqcmdFilterTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Default");
                        SecondTrqcmdFilterFreq = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Second Trqcmd Filter Freq", "Default");
                        SecondTrqcmdFilterQ = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Second Trqcmd Filter Q ", "Default");
                        break;
                    case "1":
                        NotchFilterConfig = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Config", "Default");
                        AnalyseNotchFilterConfig();

                        NotchFilterFrequency1 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 1", "Default");
                        NotchFilterQFactor1 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 1", "Default");
                        NotchFilterDepth1 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 1 ", "Default");
                        NotchFilterFrequency2 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 2", "Default");
                        NotchFilterQFactor2 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 2", "Default");
                        NotchFilterDepth2 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 2", "Default");
                        NotchFilterFrequency3 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 3", "Default");
                        NotchFilterQFactor3 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 3", "Default");
                        NotchFilterDepth3 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 3", "Default");
                        NotchFilterFrequency4 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 4", "Default");
                        NotchFilterQFactor4 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 4", "Default");
                        NotchFilterDepth4 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 4", "Default");
                        break;
                    case "2":
                        ForwardInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward Internal Torque Limit", "Default");
                        ReverseInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse Internal Torque Limit", "Default");
                        ForwardExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward External Torque Limit", "Default");
                        ReverseExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse External Torque Limit", "Default");
                        break;
                    default:
                        FirstTrqcmdFilterTime = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Default");
                        SecondTrqcmdFilterFreq = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Second Trqcmd Filter Freq", "Default");
                        SecondTrqcmdFilterQ = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Second Trqcmd Filter Q ", "Default");

                        NotchFilterConfig = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Config", "Default");
                        AnalyseNotchFilterConfig();

                        NotchFilterFrequency1 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 1", "Default");
                        NotchFilterQFactor1 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 1", "Default");
                        NotchFilterDepth1 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 1 ", "Default");
                        NotchFilterFrequency2 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 2", "Default");
                        NotchFilterQFactor2 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 2", "Default");
                        NotchFilterDepth2 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 2", "Default");
                        NotchFilterFrequency3 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 3", "Default");
                        NotchFilterQFactor3 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 3", "Default");
                        NotchFilterDepth3 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 3", "Default");
                        NotchFilterFrequency4 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 4", "Default");
                        NotchFilterQFactor4 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 4", "Default");
                        NotchFilterDepth4 = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 4", "Default");

                        ForwardInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward Internal Torque Limit", "Default");
                        ReverseInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse Internal Torque Limit", "Default");
                        ForwardExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward External Torque Limit", "Default");
                        ReverseExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse External Torque Limit", "Default");
                        break;
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CURRENTLOOP_GET_DEFAULT_PARAMETER, "GetDefaultCurrentLoopParameter", ex);
            }
        }

        //*************************************************************************
        //函数名称：SaveCurrentLoopConfigFile
        //函数功能：保存限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void SaveCurrentLoopConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;

            try
            {
                //选择配置文件存放位置
                iRet = ExcelHelper.GetWritePath(ref strFilePath, ExcelType.CurrentLoop);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //生成配置文件
                iRet = ExcelHelper.WriteIntoExcel(strFilePath, GetCurrentLoopConfigToDataTable(), ExcelType.CurrentLoop);
                if (iRet == RET.SUCCEEDED)
                {
                    ShowNotification(2002);
                }
                else
                {
                    ShowNotification(2003);
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CURRENTLOOP_SAVE_CONFIG_FILE, "SaveCurrentLoopConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：GetCurrentLoopConfigFile
        //函数功能：获取限幅配置文件
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.13
        //*************************************************************************
        public void GetCurrentLoopConfigFile()
        {
            int iRet = -1;
            string strFilePath = null;
            DataTable dt = null;

            try
            {
                //获取配置文件路径
                iRet = ExcelHelper.GetReadPath(ref strFilePath);
                if (iRet == RET.ERROR)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }
                else if (iRet == RET.NO_EFFECT)
                {
                    return;
                }

                //查询Excel数据
                iRet = ExcelHelper.ReadFromExcel(strFilePath, ref dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(2000);
                    return;
                }

                //判断是否导入正确的配置文件
                if (dt.Columns[3].ColumnName != FileInterface.CURRENTLOOP)
                {
                    ShowNotification(2001);
                    return;
                }

                //导入参数
                iRet = GetCurrentLoopConfigFromDataTable(dt);
                if (iRet != RET.SUCCEEDED)
                {
                    ShowNotification(RET.ERROR);
                    return;
                }

                //是否下载
                OthersHelper.GetWindowsStartupPosition();
                if (MessageBoxService.ShowMessage("是否下载当前导入的电流环参数...", "请确定", MessageButton.YesNo, MessageIcon.Question) == MessageResult.Yes)
                {
                    WriteCurrentLoopParameter("All");
                }
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CURRENTLOOP_GET_CONFIG_FILE, "GetCurrentLoopConfigFile", ex);
            }
        }

        //*************************************************************************
        //函数名称：EvaluationCurrentLoopParameter
        //函数功能：赋值
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.04.16
        //*************************************************************************
        public void EvaluationCurrentLoopParameter()
        {
            if (IsEvaluationAll)
            {
                //标志位更新
                IsEvaluationAll = false;

                //赋值
                FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩指令滤波时间参数
                SecondTrqcmdFilterFreq = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Second Trqcmd Filter Freq", "Index"));//第二转矩指令滤波器频率
                SecondTrqcmdFilterQ = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Second Trqcmd Filter Q ", "Index"));//第二转矩指令滤波器Q值

                NotchFilterConfig = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Config", "Index"));//陷波滤波器配置
                AnalyseNotchFilterConfig();

                NotchFilterFrequency1 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 1", "Index"));//第1段陷波滤波器频率
                NotchFilterQFactor1 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 1", "Index"));//第1段陷波滤波器Q值
                NotchFilterDepth1 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 1 ", "Index"));//第1段陷波滤波器深度
                NotchFilterFrequency2 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 2", "Index"));//第2段陷波滤波器频率
                NotchFilterQFactor2 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 2", "Index"));//第2段陷波滤波器Q值
                NotchFilterDepth2 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 2", "Index"));//第2段陷波滤波器深度
                NotchFilterFrequency3 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 3", "Index"));//第3段陷波滤波器频率
                NotchFilterQFactor3 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 3", "Index"));//第3段陷波滤波器Q值
                NotchFilterDepth3 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 3", "Index"));//第3段陷波滤波器深度
                NotchFilterFrequency4 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 4", "Index"));//第4段陷波滤波器频率
                NotchFilterQFactor4 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 4", "Index"));//第4段陷波滤波器Q值
                NotchFilterDepth4 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 4", "Index"));//第4段陷波滤波器深度

                ForwardInternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward Internal Torque Limit", "Index"));//正转内部转矩限制值
                ReverseInternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse Internal Torque Limit", "Index"));//反转内部转矩限制值
                ForwardExternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward External Torque Limit", "Index"));//正转外部转矩限制值
                ReverseExternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse External Torque Limit", "Index"));//反转外部转矩限制值
            }
            else
            {
                if (SelectedTabIndex == "0")
                {
                    FirstTrqcmdFilterTime = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "First Trqcmd Filter Time", "Index"));//第一转矩指令滤波时间参数
                    SecondTrqcmdFilterFreq = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Second Trqcmd Filter Freq", "Index"));//第二转矩指令滤波器频率
                    SecondTrqcmdFilterQ = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Second Trqcmd Filter Q ", "Index"));//第二转矩指令滤波器Q值
                }
                else if (SelectedTabIndex == "1")
                {
                    NotchFilterConfig = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Config", "Index"));//陷波滤波器配置
                    AnalyseNotchFilterConfig();

                    NotchFilterFrequency1 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 1", "Index"));//第1段陷波滤波器频率
                    NotchFilterQFactor1 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 1", "Index"));//第1段陷波滤波器Q值
                    NotchFilterDepth1 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 1 ", "Index"));//第1段陷波滤波器深度
                    NotchFilterFrequency2 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 2", "Index"));//第2段陷波滤波器频率
                    NotchFilterQFactor2 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 2", "Index"));//第2段陷波滤波器Q值
                    NotchFilterDepth2 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 2", "Index"));//第2段陷波滤波器深度
                    NotchFilterFrequency3 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 3", "Index"));//第3段陷波滤波器频率
                    NotchFilterQFactor3 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 3", "Index"));//第3段陷波滤波器Q值
                    NotchFilterDepth3 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 3", "Index"));//第3段陷波滤波器深度
                    NotchFilterFrequency4 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Frequency 4", "Index"));//第4段陷波滤波器频率
                    NotchFilterQFactor4 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Q Factor 4", "Index"));//第4段陷波滤波器Q值
                    NotchFilterDepth4 = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Notch Filter Depth 4", "Index"));//第4段陷波滤波器深度
                }
                else if (SelectedTabIndex == "2")
                {
                    ForwardInternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward Internal Torque Limit", "Index"));//正转内部转矩限制值
                    ReverseInternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse Internal Torque Limit", "Index"));//反转内部转矩限制值
                    ForwardExternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Forward External Torque Limit", "Index"));//正转外部转矩限制值
                    ReverseExternalTorqueLimit = OthersHelper.GetCurrentValueOfIndex(OthersHelper.GetCellValueFromDataTable(GlobalParameterSet.dt, "Name", "Reverse External Torque Limit", "Index"));//反转外部转矩限制值
                }
            }
        }

        //*************************************************************************
        //函数名称：ShowCurrentLoopTabItem
        //函数功能：展示TabItem
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.13
        //*************************************************************************
        public void ShowCurrentLoopTabItem(string strTabItem)
        {
            switch (strTabItem)
            {
                case "一阶低通滤波":
                    SelectedTabIndex = "0";
                    FirstTrqcmdFilterTimeBackground = BackgroundState.Selected;//第一转矩指令滤波时间参数
                    SecondTrqcmdFilterFreqBackground = BackgroundState.NotSelected;//第二转矩指令滤波器频率
                    SecondTrqcmdFilterQBackground = BackgroundState.NotSelected;//第二转矩指令滤波器Q值
                    break;
                case "二阶低通滤波":
                    SelectedTabIndex = "0";
                    FirstTrqcmdFilterTimeBackground = BackgroundState.NotSelected;//第一转矩指令滤波时间参数
                    SecondTrqcmdFilterFreqBackground = BackgroundState.Selected;//第二转矩指令滤波器频率
                    SecondTrqcmdFilterQBackground = BackgroundState.Selected;//第二转矩指令滤波器Q值
                    break;
                case "陷波滤波器":
                    SelectedTabIndex = "1";
                    break;
                default:
                    SelectedTabIndex = "2";
                    break;
            }
        }

        //*************************************************************************
        //函数名称：OnXXXChanged
        //函数功能：值变更新全局变量
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        public void OnFirstTrqcmdFilterTimeChanged() { GlobalCurrentInput.FirstTrqcmdFilterTime = FirstTrqcmdFilterTime; }//第一转矩指令滤波时间参数
        public void OnSecondTrqcmdFilterFreqChanged() { GlobalCurrentInput.SecondTrqcmdFilterFreq = SecondTrqcmdFilterFreq; }//第二转矩指令滤波器频率
        public void OnSecondTrqcmdFilterQChanged() { GlobalCurrentInput.SecondTrqcmdFilterQ = SecondTrqcmdFilterQ; }//第二转矩指令滤波器Q值
        public void OnNotchFilterConfigChanged() { GlobalCurrentInput.NotchFilterConfig = NotchFilterConfig; }//陷波滤波器配置
        public void OnNotchFilter1SwitchChanged() { AnalyseSelectedNotchFilter(); RefreshCheckBox(false); }
        public void OnNotchFilter2SwitchChanged() { AnalyseSelectedNotchFilter(); RefreshCheckBox(false); }
        public void OnNotchFilter3SwitchChanged() { AnalyseSelectedNotchFilter(); RefreshCheckBox(false); }
        public void OnNotchFilter4SwitchChanged() { AnalyseSelectedNotchFilter(); RefreshCheckBox(false); }
        public void OnNotchFilterFrequency1Changed() { GlobalCurrentInput.NotchFilterFrequency1 = NotchFilterFrequency1; }//第1段陷波滤波器频率
        public void OnNotchFilterQFactor1Changed() { GlobalCurrentInput.NotchFilterQFactor1 = NotchFilterQFactor1; }//第1段陷波滤波器Q值
        public void OnNotchFilterDepth1Changed() { GlobalCurrentInput.NotchFilterDepth1 = NotchFilterDepth1; }//第1段陷波滤波器深度
        public void OnNotchFilterFrequency2Changed() { GlobalCurrentInput.NotchFilterFrequency2 = NotchFilterFrequency2; }//第2段陷波滤波器频率
        public void OnNotchFilterQFactor2Changed() { GlobalCurrentInput.NotchFilterQFactor2 = NotchFilterQFactor2; }//第2段陷波滤波器Q值
        public void OnNotchFilterDepth2Changed() { GlobalCurrentInput.NotchFilterDepth2 = NotchFilterDepth2; }//第2段陷波滤波器深度
        public void OnNotchFilterFrequency3Changed() { GlobalCurrentInput.NotchFilterFrequency3 = NotchFilterFrequency3; }//第3段陷波滤波器频率
        public void OnNotchFilterQFactor3Changed() { GlobalCurrentInput.NotchFilterQFactor3 = NotchFilterQFactor3; }//第3段陷波滤波器Q值
        public void OnNotchFilterDepth3Changed() { GlobalCurrentInput.NotchFilterDepth3 = NotchFilterDepth3; }//第3段陷波滤波器深度
        public void OnNotchFilterFrequency4Changed() { GlobalCurrentInput.NotchFilterFrequency4 = NotchFilterFrequency4; }//第4段陷波滤波器频率
        public void OnNotchFilterQFactor4Changed() { GlobalCurrentInput.NotchFilterQFactor4 = NotchFilterQFactor4; }//第4段陷波滤波器Q值
        public void OnNotchFilterDepth4Changed() { GlobalCurrentInput.NotchFilterDepth4 = NotchFilterDepth4; }//第4段陷波滤波器深度
        public void OnForwardInternalTorqueLimitChanged() { GlobalCurrentInput.ForwardInternalTorqueLimit_CurrentLoop = ForwardInternalTorqueLimit; }//正转内部转矩限制值
        public void OnReverseInternalTorqueLimitChanged() { GlobalCurrentInput.ReverseInternalTorqueLimit_CurrentLoop = ReverseInternalTorqueLimit; }//反转内部转矩限制值
        public void OnForwardExternalTorqueLimitChanged() { GlobalCurrentInput.ForwardExternalTorqueLimit_CurrentLoop = ForwardExternalTorqueLimit; }//正转外部转矩限制值
        public void OnReverseExternalTorqueLimitChanged() { GlobalCurrentInput.ReverseExternalTorqueLimit_CurrentLoop = ReverseExternalTorqueLimit; }//反转外部转矩限制值
        #endregion

        #region 私有方法
        //*************************************************************************
        //函数名称：BackgroundInitialize
        //函数功能：背景初始化
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.16
        //*************************************************************************
        private void BackgroundInitialize()
        {
            FirstTrqcmdFilterTimeBackground = BackgroundState.Selected;//第一转矩指令滤波时间参数
            SecondTrqcmdFilterFreqBackground = BackgroundState.NotSelected;//第二转矩指令滤波器频率
            SecondTrqcmdFilterQBackground = BackgroundState.NotSelected;//第二转矩指令滤波器Q值
        }

        //*************************************************************************
        //函数名称：GetCurrentLoopConfigToDataTable
        //函数功能：获取配置参数到DataTable中
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private DataTable GetCurrentLoopConfigToDataTable()
        {
            DataTable dt = new DataTable();

            try
            {
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "First Trqcmd Filter Time", "第一转矩指令滤波时间参数", FirstTrqcmdFilterTime, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Second Trqcmd Filter Freq", "第二转矩指令滤波器频率", SecondTrqcmdFilterFreq, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Second Trqcmd Filter Q ", "第二转矩指令滤波器Q值", SecondTrqcmdFilterQ, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Config", "陷波滤波器配置", NotchFilterConfig, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Frequency 1", "第1段陷波滤波器频率", NotchFilterFrequency1, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Q Factor 1", "第1段陷波滤波器Q值", NotchFilterQFactor1, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Depth 1 ", "第1段陷波滤波器深度", NotchFilterDepth1, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Frequency 2", "第2段陷波滤波器频率", NotchFilterFrequency2, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Q Factor 2", "第2段陷波滤波器Q值", NotchFilterQFactor2, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Depth 2", "第2段陷波滤波器深度", NotchFilterDepth2, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Frequency 3", "第3段陷波滤波器频率", NotchFilterFrequency3, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Q Factor 3", "第3段陷波滤波器Q值", NotchFilterQFactor3, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Depth 3", "第3段陷波滤波器深度", NotchFilterDepth3, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Frequency 4", "第4段陷波滤波器频率", NotchFilterFrequency4, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Q Factor 4", "第4段陷波滤波器Q值", NotchFilterQFactor4, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Notch Filter Depth 4", "第4段陷波滤波器深度", NotchFilterDepth4, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Forward Internal Torque Limit", "正转内部转矩限制值", ForwardInternalTorqueLimit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Reverse Internal Torque Limit", "反转内部转矩限制值", ReverseInternalTorqueLimit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Forward External Torque Limit", "正转外部转矩限制值", ForwardExternalTorqueLimit, ref dt);
                OthersHelper.MakeValueToDataTable(FileInterface.CURRENTLOOP, "Reverse External Torque Limit", "反转外部转矩限制值", ReverseExternalTorqueLimit, ref dt);

                return dt;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CURRENTLOOP_GET_CONFIG_TO_DATATABLE, "GetCurrentLoopConfigToDataTable", ex);
                return null;
            }
        }

        //*************************************************************************
        //函数名称：GetCurrentLoopConfigFromDataTable
        //函数功能：从DataTable中获取配置参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int GetCurrentLoopConfigFromDataTable(DataTable dt)
        {
            try
            {
                if (dt == null)
                {
                    return RET.ERROR;
                }

                FirstTrqcmdFilterTime = OthersHelper.GetCellValueFromDataTable(dt, "Name", "First Trqcmd Filter Time", "Default");
                SecondTrqcmdFilterFreq = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Second Trqcmd Filter Freq", "Default");
                SecondTrqcmdFilterQ = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Second Trqcmd Filter Q ", "Default");

                NotchFilterConfig = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Config", "Default");
                AnalyseNotchFilterConfig();

                NotchFilterFrequency1 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Frequency 1", "Default");
                NotchFilterQFactor1 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Q Factor 1", "Default");
                NotchFilterDepth1 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Depth 1 ", "Default");
                NotchFilterFrequency2 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Frequency 2", "Default");
                NotchFilterQFactor2 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Q Factor 2", "Default");
                NotchFilterDepth2 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Depth 2", "Default");
                NotchFilterFrequency3 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Frequency 3", "Default");
                NotchFilterQFactor3 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Q Factor 3", "Default");
                NotchFilterDepth3 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Depth 3", "Default");
                NotchFilterFrequency4 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Frequency 4", "Default");
                NotchFilterQFactor4 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Q Factor 4", "Default");
                NotchFilterDepth4 = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Notch Filter Depth 4", "Default");
                ForwardInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Forward Internal Torque Limit", "Default");
                ReverseInternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Reverse Internal Torque Limit", "Default");
                ForwardExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Forward External Torque Limit", "Default");
                ReverseExternalTorqueLimit = OthersHelper.GetCellValueFromDataTable(dt, "Name", "Reverse External Torque Limit", "Default");

                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CURRENTLOOP_GET_CONFIG_FROM_DATATABLE, "GetCurrentLoopConfigFromDataTable", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：InterfaceEvaluationFromGlobalVariable
        //函数功能：从全局变量赋值接口
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2019.11.18
        //*************************************************************************
        private void InterfaceEvaluationFromGlobalVariable()
        {
            try
            {
                FirstTrqcmdFilterTime = GlobalCurrentInput.FirstTrqcmdFilterTime;//第一转矩指令滤波时间参数
                SecondTrqcmdFilterFreq = GlobalCurrentInput.SecondTrqcmdFilterFreq;//第二转矩指令滤波器频率
                SecondTrqcmdFilterQ = GlobalCurrentInput.SecondTrqcmdFilterQ;//第二转矩指令滤波器Q值

                NotchFilterConfig = GlobalCurrentInput.NotchFilterConfig;//陷波滤波器配置
                AnalyseNotchFilterConfig();

                NotchFilterFrequency1 = GlobalCurrentInput.NotchFilterFrequency1;//第1段陷波滤波器频率
                NotchFilterQFactor1 = GlobalCurrentInput.NotchFilterQFactor1;//第1段陷波滤波器Q值
                NotchFilterDepth1 = GlobalCurrentInput.NotchFilterDepth1;//第1段陷波滤波器深度
                NotchFilterFrequency2 = GlobalCurrentInput.NotchFilterFrequency2;//第2段陷波滤波器频率
                NotchFilterQFactor2 = GlobalCurrentInput.NotchFilterQFactor2;//第2段陷波滤波器Q值
                NotchFilterDepth2 = GlobalCurrentInput.NotchFilterDepth2;//第2段陷波滤波器深度
                NotchFilterFrequency3 = GlobalCurrentInput.NotchFilterFrequency3;//第3段陷波滤波器频率
                NotchFilterQFactor3 = GlobalCurrentInput.NotchFilterQFactor3;//第3段陷波滤波器Q值
                NotchFilterDepth3 = GlobalCurrentInput.NotchFilterDepth3;//第3段陷波滤波器深度
                NotchFilterFrequency4 = GlobalCurrentInput.NotchFilterFrequency4;//第4段陷波滤波器频率
                NotchFilterQFactor4 = GlobalCurrentInput.NotchFilterQFactor4;//第4段陷波滤波器Q值
                NotchFilterDepth4 = GlobalCurrentInput.NotchFilterDepth4;//第4段陷波滤波器深度

                ForwardInternalTorqueLimit = GlobalCurrentInput.ForwardInternalTorqueLimit_CurrentLoop;//正转内部转矩限制值
                ReverseInternalTorqueLimit = GlobalCurrentInput.ReverseInternalTorqueLimit_CurrentLoop;//反转内部转矩限制值
                ForwardExternalTorqueLimit = GlobalCurrentInput.ForwardExternalTorqueLimit_CurrentLoop;//正转外部转矩限制值
                ReverseExternalTorqueLimit = GlobalCurrentInput.ReverseExternalTorqueLimit_CurrentLoop;//反转外部转矩限制值
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CURRENTLOOP_INTERFACE_EVALUATION_FROM_VARIABLE, "EvaluationFromGlobalVariable", ex);
            }
        }

        //*************************************************************************
        //函数名称：AddParameterInfoDictionary
        //函数功能：添加要读写的字段
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.12
        //*************************************************************************
        private int AddParameterInfoDictionary(string strCategory, ref Dictionary<string, string> dicParameterInfo)
        {
            dicParameterInfo = new Dictionary<string, string>();

            try
            {
                switch (strCategory)
                {
                    case "0":
                        dicParameterInfo.Add("First Trqcmd Filter Time", FirstTrqcmdFilterTime);
                        dicParameterInfo.Add("Second Trqcmd Filter Freq", SecondTrqcmdFilterFreq);
                        dicParameterInfo.Add("Second Trqcmd Filter Q ", SecondTrqcmdFilterQ);
                        break;
                    case "1":
                        AnalyseSelectedNotchFilter();
                        dicParameterInfo.Add("Notch Filter Config", NotchFilterConfig);
                        dicParameterInfo.Add("Notch Filter Frequency 1", NotchFilterFrequency1);
                        dicParameterInfo.Add("Notch Filter Q Factor 1", NotchFilterQFactor1);
                        dicParameterInfo.Add("Notch Filter Depth 1 ", NotchFilterDepth1);
                        dicParameterInfo.Add("Notch Filter Frequency 2", NotchFilterFrequency2);
                        dicParameterInfo.Add("Notch Filter Q Factor 2", NotchFilterQFactor2);
                        dicParameterInfo.Add("Notch Filter Depth 2", NotchFilterDepth2);
                        dicParameterInfo.Add("Notch Filter Frequency 3", NotchFilterFrequency3);
                        dicParameterInfo.Add("Notch Filter Q Factor 3", NotchFilterQFactor3);
                        dicParameterInfo.Add("Notch Filter Depth 3", NotchFilterDepth3);
                        dicParameterInfo.Add("Notch Filter Frequency 4", NotchFilterFrequency4);
                        dicParameterInfo.Add("Notch Filter Q Factor 4", NotchFilterQFactor4);
                        dicParameterInfo.Add("Notch Filter Depth 4", NotchFilterDepth4);
                        break;
                    case "2":
                        dicParameterInfo.Add("Forward Internal Torque Limit", ForwardInternalTorqueLimit);
                        dicParameterInfo.Add("Reverse Internal Torque Limit", ReverseInternalTorqueLimit);
                        dicParameterInfo.Add("Forward External Torque Limit", ForwardExternalTorqueLimit);
                        dicParameterInfo.Add("Reverse External Torque Limit", ReverseExternalTorqueLimit);
                        break;
                    default:
                        dicParameterInfo.Add("First Trqcmd Filter Time", FirstTrqcmdFilterTime);
                        dicParameterInfo.Add("Second Trqcmd Filter Freq", SecondTrqcmdFilterFreq);
                        dicParameterInfo.Add("Second Trqcmd Filter Q ", SecondTrqcmdFilterQ);

                        AnalyseSelectedNotchFilter();
                        dicParameterInfo.Add("Notch Filter Config", NotchFilterConfig);
                        dicParameterInfo.Add("Notch Filter Frequency 1", NotchFilterFrequency1);
                        dicParameterInfo.Add("Notch Filter Q Factor 1", NotchFilterQFactor1);
                        dicParameterInfo.Add("Notch Filter Depth 1 ", NotchFilterDepth1);
                        dicParameterInfo.Add("Notch Filter Frequency 2", NotchFilterFrequency2);
                        dicParameterInfo.Add("Notch Filter Q Factor 2", NotchFilterQFactor2);
                        dicParameterInfo.Add("Notch Filter Depth 2", NotchFilterDepth2);
                        dicParameterInfo.Add("Notch Filter Frequency 3", NotchFilterFrequency3);
                        dicParameterInfo.Add("Notch Filter Q Factor 3", NotchFilterQFactor3);
                        dicParameterInfo.Add("Notch Filter Depth 3", NotchFilterDepth3);
                        dicParameterInfo.Add("Notch Filter Frequency 4", NotchFilterFrequency4);
                        dicParameterInfo.Add("Notch Filter Q Factor 4", NotchFilterQFactor4);
                        dicParameterInfo.Add("Notch Filter Depth 4", NotchFilterDepth4);

                        dicParameterInfo.Add("Forward Internal Torque Limit", ForwardInternalTorqueLimit);
                        dicParameterInfo.Add("Reverse Internal Torque Limit", ReverseInternalTorqueLimit);
                        dicParameterInfo.Add("Forward External Torque Limit", ForwardExternalTorqueLimit);
                        dicParameterInfo.Add("Reverse External Torque Limit", ReverseExternalTorqueLimit);
                        break;
                }
               
                return RET.SUCCEEDED;
            }
            catch (System.Exception ex)
            {
                SoftwareErrorHelper.CatchDispose(ERROR.CURRENTLOOP_ADD_PARAMETER_INFO_DICTIONARY, "AddParameterInfoDictionary", ex);
                return RET.ERROR;
            }
        }

        //*************************************************************************
        //函数名称：RefreshCheckBox_For_CNUS
        //函数功能：更新CheckBox
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Lilbert
        //更新时间：2023.01.04
        //*************************************************************************
        public void RefreshCheckBox_For_CNUS()
        {            
            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                if (NotchFilter1Switch)
                {
                    NotchFilter1Content = "第一陷波滤波器开";
                }
                else
                {
                    NotchFilter1Content = "第一陷波滤波器关";
                }

                if (NotchFilter2Switch)
                {
                    NotchFilter2Content = "第二陷波滤波器开";
                }
                else
                {
                    NotchFilter2Content = "第二陷波滤波器关";
                }

                if (NotchFilter3Switch)
                {
                    NotchFilter3Content = "第三陷波滤波器开";
                }
                else
                {
                    NotchFilter3Content = "第三陷波滤波器关";
                }

                if (NotchFilter4Switch)
                {
                    NotchFilter4Content = "第四陷波滤波器开";
                }
                else
                {
                    NotchFilter4Content = "第四陷波滤波器关";
                }

                NotchFilter1Switch = false;
                NotchFilter2Switch = false;
                NotchFilter3Switch = false;
                NotchFilter4Switch = false;
            }
            else
            {
                if (NotchFilter1Switch)
                {
                    NotchFilter1Content = "FirstNotchFilterOn";
                }
                else
                {
                    NotchFilter1Content = "FirstNotchFilterOFF";
                }

                if (NotchFilter2Switch)
                {
                    NotchFilter2Content = "SecondNotchFilterOn";
                }
                else
                {
                    NotchFilter2Content = "SecondNotchFilterOFF";
                }

                if (NotchFilter3Switch)
                {
                    NotchFilter3Content = "ThirdNotchFilterOn";
                }
                else
                {
                    NotchFilter3Content = "ThirdNotchFilterOFF";
                }

                if (NotchFilter4Switch)
                {
                    NotchFilter4Content = "FourthNotchFilterOn";
                }
                else
                {
                    NotchFilter4Content = "FourthNotchFilterOFF";
                }

                NotchFilter1Switch = false;
                NotchFilter2Switch = false;
                NotchFilter3Switch = false;
                NotchFilter4Switch = false;
            }
        }

        //*************************************************************************
        //函数名称：RefreshCheckBox
        //函数功能：更新CheckBox
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan&Lilbert
        //更新时间：2020.07.24&2023.01.04
        //*************************************************************************
        private void RefreshCheckBox(bool bDefault)
        {
            if (bDefault)
            {
                NotchFilter1Switch = false;
                NotchFilter2Switch = false;
                NotchFilter3Switch = false;
                NotchFilter4Switch = false;
            }

            if (Common.CurrentLanguageFile == @"Language\zh-CN.xaml")
            {
                if (NotchFilter1Switch)
                {
                    NotchFilter1Content = "第一陷波滤波器开";
                }
                else
                {
                    NotchFilter1Content = "第一陷波滤波器关";
                }

                if (NotchFilter2Switch)
                {
                    NotchFilter2Content = "第二陷波滤波器开";
                }
                else
                {
                    NotchFilter2Content = "第二陷波滤波器关";
                }

                if (NotchFilter3Switch)
                {
                    NotchFilter3Content = "第三陷波滤波器开";
                }
                else
                {
                    NotchFilter3Content = "第三陷波滤波器关";
                }

                if (NotchFilter4Switch)
                {
                    NotchFilter4Content = "第四陷波滤波器开";
                }
                else
                {
                    NotchFilter4Content = "第四陷波滤波器关";
                }
            }
            else
            {
                if (NotchFilter1Switch)
                {
                    NotchFilter1Content = "FirstNotchFilterOn";
                }
                else
                {
                    NotchFilter1Content = "FirstNotchFilterOFF";
                }

                if (NotchFilter2Switch)
                {
                    NotchFilter2Content = "SecondNotchFilterOn";
                }
                else
                {
                    NotchFilter2Content = "SecondNotchFilterOFF";
                }

                if (NotchFilter3Switch)
                {
                    NotchFilter3Content = "ThirdNotchFilterOn";
                }
                else
                {
                    NotchFilter3Content = "ThirdNotchFilterOFF";
                }

                if (NotchFilter4Switch)
                {
                    NotchFilter4Content = "FourthNotchFilterOn";
                }
                else
                {
                    NotchFilter4Content = "FourthNotchFilterOFF";
                }
            }
        }

        //*************************************************************************
        //函数名称：AnalyseNotchFilterConfig
        //函数功能：分析陷波Config参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.24
        //*************************************************************************
        private void AnalyseNotchFilterConfig()
        {
            string strSwitchTemp = null;
            string strSwitch = null;

            try
            {
                if (string.IsNullOrEmpty(NotchFilterConfig))
                {
                    RefreshCheckBox(true);
                    return;
                }

                strSwitch = Convert.ToString(Convert.ToUInt32(NotchFilterConfig), 16).PadLeft(8, '0');

                for (int i = 0; i < 4; i++)
                {
                    strSwitchTemp = strSwitch.Substring(6 - i * 2, 2);
                    if (strSwitchTemp == "01")
                    {
                        switch (i)
                        {
                            case 0:
                                NotchFilter1Switch = true;
                                break;
                            case 1:
                                NotchFilter2Switch = true;
                                break;
                            case 2:
                                NotchFilter3Switch = true;
                                break;
                            case 3:
                                NotchFilter4Switch = true;
                                break;
                            default:
                                break;
                        }
                    }
                    else
                    {
                        switch (i)
                        {
                            case 0:
                                NotchFilter1Switch = false;
                                break;
                            case 1:
                                NotchFilter2Switch = false;
                                break;
                            case 2:
                                NotchFilter3Switch = false;
                                break;
                            case 3:
                                NotchFilter4Switch = false;
                                break;
                            default:
                                break;
                        }
                    }
                }

                RefreshCheckBox(false);
            }
            catch (System.Exception ex)
            {
                RefreshCheckBox(true);
                SoftwareErrorHelper.CatchDispose(ERROR.CURRENTLOOP_ANALYSE_NOTCH_FILTER_CONFIG, "AnalyseNotchFilterConfig", ex);
            }           
        }

        //*************************************************************************
        //函数名称： AnalyseSelectedNotchFilter
        //函数功能：分析选中的陷波参数
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.07.24
        //*************************************************************************
        private void AnalyseSelectedNotchFilter()
        {
            string strValue = null;

            if (NotchFilter4Switch)
            {
                strValue = "01";
            }
            else
            {
                strValue = "00";
            }

            if (NotchFilter3Switch)
            {
                strValue += "01";
            }
            else
            {
                strValue += "00";
            }

            if (NotchFilter2Switch)
            {
                strValue += "01";
            }
            else
            {
                strValue += "00";
            }

            if (NotchFilter1Switch)
            {
                strValue += "01";
            }
            else
            {
                strValue += "00";
            }

            NotchFilterConfig = Convert.ToString(Convert.ToUInt32(strValue, 16));
        }

        //*************************************************************************
        //函数名称：ShowDefaultNotification
        //函数功能：信息提示
        //
        //输入参数：None
        //         
        //输出参数：None
        //        
        //编码作者：Ryan
        //更新时间：2020.03.31
        //*************************************************************************
        private void ShowNotification(int In_iType)
        {
            ViewModelSet.Main?.ShowNotification(In_iType);
        }
        #endregion
    }
}